@extends('reports.layout')

@php
    $available_locales = App\Helpers\ConfigHelper::getAvailableLocales();
@endphp
@section('content')
    <div style="text-align: center; margin-bottom: 20px;">
        <h3>{{ __('attendance.class_taking_attendance_report') }}</h3>
        <h4 style="margin: 1px;"> {{ __('general.class') }} :
            @foreach ($available_locales as $locale)
                {{ optional($data['class'])->getTranslation('name', $locale) ?? '' }}
            @endforeach
        </h4>
        <h4 style="margin: 1px;">
            {{ __('general.date') }} : {{ \Carbon\Carbon::parse($date)->format('F j, Y') }}
        </h4>

        <div style="font-size: 16px; padding: 5px; border: 1px solid black; margin-top: 20px;">
            {{ __('attendance.class_attendance_taking_info') }}
        </div>
    </div>

    <table class="small-font">
        <thead>
        <tr>
            <th rowspan="3">{{ __('general.class') }}</th>
            <th rowspan="3">{{ __('general.student') }}</th>
            <th rowspan="3">{{ __('general.student_no') }}</th>
            @foreach ($data['headers'] as $header)
                <th>
                    <div style="display: flex; flex-direction: column; justify-content: center; align-items: center;">
                        <span>{{ $header['name'][app()->getLocale()] }}</span><br/>
                        <span>{{ \Carbon\Carbon::parse($header['from_time'])->format('H:i') }} - {{ \Carbon\Carbon::parse($header['to_time'])->format('H:i') }}</span>
                    </div>
                </th>
            @endforeach
        </tr>

        <tr>
            @php
                $merged_periods = [];
            @endphp
            @foreach ($data['headers'] as $index => $header)
                @php
                    $sub_header = $data['sub_headers'][$header['period']] ?? [];
                @endphp

                @if(!empty($sub_header['updated_by_employee']))
                    @php
                        $colspan = collect($data['headers'])
                            ->slice($index)
                            ->takeWhile(function ($next_header) use ($sub_header, $data) {
                                $next_sub_header = $data['sub_headers'][$next_header['period']] ?? [];
                                return !empty($next_sub_header['updated_by_employee']) && $sub_header['updated_by_employee'] == $next_sub_header['updated_by_employee'];
                            })
                            ->count();

                        if($colspan > 1) {
                            $merged_periods = array_merge($merged_periods, collect($data['headers'])
                                ->slice($index + 1, $colspan - 1)
                                ->pluck('period')
                                ->toArray());
                        }
                    @endphp

                    @if(in_array($header['period'], $merged_periods))
                        @continue
                    @endif

                    <th style="text-align: center; font-size: 10px;" colspan="{{ $colspan }}">{{ $sub_header['updated_by_employee'] }}</th>
                @else
                    <th></th>
                @endif
            @endforeach
        </tr>
        <tr>
            @foreach ($data['headers'] as $header)
                @php
                    $sub_header = $data['sub_headers'][$header['period']] ?? [];
                @endphp

                @if(!empty($sub_header['updated_by_employee']))
                    <th style="text-align: center; font-size: 10px;">{{ $sub_header['attendance_taken_at'] ?? '' }}</th>
                @else
                    <th></th>
                @endif
            @endforeach
        </tr>

        </thead>
        <tbody>
        @foreach ($data['attendance_data'] as $student)
            <tr class="narrow-rows">
                <td style="width:50px">{{ $student['student_primary_class'] }}</td>
                <td style="width: 150px;">
                    <div style="display: flex; flex-direction: column; justify-content: start;">
                        @foreach ($available_locales as $locale)
                            <span>{{ $student['student_name'][$locale] ?? '' }}</span><br/>
                        @endforeach
                    </div>
                </td>
                <td>{{ $student['student_number'] }}</td>
                @foreach ($student['period_attendances'] as $period => $status)
                    <td style="text-align: center; font-size: 14px;">{{ \App\Enums\PeriodAttendanceStatus::getStatusForReport($status, $student['school_attendance_status']) }}</td>
                @endforeach
            </tr>
        @endforeach
        </tbody>
    </table>
@endsection
