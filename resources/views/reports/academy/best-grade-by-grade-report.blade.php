@extends('reports.layout')
@section('content')
    <table>
        <thead>
        <tr>
            @foreach ( $headers as $header)
                <th>{{ $header }}</th>
            @endforeach
        </tr>
        </thead>

        <tbody>
        @foreach ($data as $grade => $students )
            @php $counter = $loop->iteration @endphp
            @foreach ($students as $index => $student)
                <tr>

                    @if($index == 0)
                        <td rowspan="{{ count($students) }}"> {{ $counter }}</td>
                        <td rowspan="{{ count($students) }}">{{ $grade }}</td>
                    @endif

                    @foreach ($student as $student_data)
                        @if($index == 0)
                            <td style="border-bottom:none">  {{ $student_data }}  </td>
                        @elseif($index == count($students) - 1)
                            <td style="border-top:none"> {{$student_data}}</td>
                        @else
                            <td style="border-top:none; border-bottom:none;"> {{ $student_data }}</td>
                        @endif
                    @endforeach
                </tr>
            @endforeach
        @endforeach

        </tbody>

    </table>
@endsection


@push('styles')
    <style>
        th {
            text-align: center;
        }

        td {
            text-align: center;
        }
    </style>
@endpush
