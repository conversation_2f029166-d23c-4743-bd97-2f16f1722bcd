@php use Carbon\Carbon; @endphp
@extends('reports.layout')
@section('content')
    @foreach($students as $student)
        <div style="padding-top: 170px; padding-left: 15px; padding-right: 15px;">
            <table class="no-border no-padding">
                <tr class="bg-white">
                    <td>{{ __('general.name', [], 'zh')}}</td>
                    <td>{{$student['name']['zh']}}</td>
                    <td>{{ __('general.class', [], 'zh')}}</td>
                    <td>{{$student_class['class_model']['name']['zh']}}</td>
                    <td>{{ __('general.student_no', [], 'zh')}}</td>
                    <td>{{$student['student_number']}}</td>
                    <td>{{ __('general.date_issued', [], 'zh')}}</td>
                    <td>{{ App\Helpers\ReportHelper::getPrintDate('Y-m-d') }}</td>
                </tr>
                <tr class="bg-white">
                    <td>{{ __('general.name', [], 'en')}}</td>
                    <td>{{$student['name']['en']}}</td>
                    <td>{{ __('general.class', [], 'en')}}</td>
                    <td>{{$student_class['class_model']['name']['en']}}</td>
                    <td>{{ __('general.student_no', [], 'en')}}</td>
                    <td></td>
                    <td>{{ __('general.date_issued', [], 'en')}}</td>
                    <td></td>
                </tr>
            </table>
            <table style="margin-top: 15px;">
                <thead>
                <tr>
                    <th style="width: 5%; font-weight: bold" class="text-center">{{__('general.no')}}</th>
                    <th style="width: 10%; font-weight: bold" class="text-center">{{__('general.year')}}</th>
                    <th style="width: 85%; font-weight: bold"
                        class="text-center">{{ __('general.merit_record', [], 'zh')}} {{ __('general.merit_record', [], 'en')}}</th>
                </tr>
                @forelse($student['reward_punishment_records'] as $record)
                    <tr>
                        <td class="text-center">{{$loop->iteration}}</td>
                        <td class="text-center">{{Carbon::parse($record['date'])->format('Y')}}</td>
                        <td>{{$record['reward_punishment']['name'][App::getLocale()]}}</td>
                    </tr>
                @empty
                    <tr>
                        <td class="text-center" colspan="3">{{ __('general.no_data_available') }}</td>
                    </tr>
                @endforelse

                </thead>
            </table>

            <table style="margin-top: 15px;">
                <thead>
                <tr>
                    <th style="width: 5%; font-weight: bold" class="text-center">{{__('general.no')}}</th>
                    <th style="width: 10%; font-weight: bold" class="text-center">{{__('general.year')}}</th>
                    <th style="width: 85%; font-weight: bold"
                        class="text-center">{{ __('general.exceptional_performance', [], 'zh')}} {{ __('general.exceptional_performance', [], 'en')}}</th>
                </tr>
                @forelse($student['competition_records'] as $record)
                    <tr>
                        <td class="text-center">{{$loop->iteration}}</td>
                        <td class="text-center">{{Carbon::parse($record['competition']['date'])->format('Y')}}</td>
                        <td>{{$record['competition']['name']}} ({{$record['award']['name'][App::getLocale()]}})</td>
                    </tr>
                @empty
                    <tr>
                        <td class="text-center" colspan="3">{{ __('general.no_data_available') }}</td>
                    </tr>
                @endforelse

                </thead>
            </table>
        </div>
        @if(!$loop->last)
            <div style="page-break-after: always"></div>
        @endif
    @endforeach
@endsection

@push('styles')
    <style>
        table.no-border,
        table.no-border th,
        table.no-border td {
            border: none;
        }

        table.no-padding,
        table.no-padding th,
        table.no-padding td {
            padding: 0;
        }

        .text-center {
            text-align: center;
        }
    </style>
@endpush
