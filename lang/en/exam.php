<?php

return [
    'class_rank' => 'Position In Class',
    'subject' => 'Subject',
    'marks' => 'Marks',
    'examination_result_report_title' => ':semester_setting_name Examination Result By Exam Report',
    'net_average_passing_rate_report_title' => ':report_card_output_name :grade_name Net Average Passing Rate Report',
    'subject_passing_rate_report_title' => ':exam_name :grade_name :subject_name Passing Rate Report',
    'subject_average_mark_report_title' => ':exam_name :grade_name :subject_name Average Mark Report',
    'subject_score_analysis_report_title' => ':exam_name Analysis of Examination Results for :class_name',
    'all_passed_student_report_title' => ':school_name All Passed Report :semester_year',
    'best_grade_by_class_report_title' => ':school_name Best Academic Performance By Class :semester_setting_name',
    'best_grade_by_subject_report_title' => ':school_name :semester_year Best Academic Performance By Subject',
    'best_grade_by_grade_report_title' => ':school_name :semester_setting_name Best Academic Performance by Grade',
];
