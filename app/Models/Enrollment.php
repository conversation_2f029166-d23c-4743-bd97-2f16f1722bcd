<?php

namespace App\Models;

use App\Enums\CardStatus;
use App\Enums\EnrollmentPaymentStatus;
use App\Enums\EnrollmentStatus;
use App\Enums\Gender;
use App\Enums\StudentAdmissionType;
use App\Enums\StudentLeaveStatus;
use App\Helpers\ErrorCodeHelper;
use App\Interfaces\Billable;
use App\Interfaces\BillableItem;
use App\Interfaces\Deletable;
use App\Interfaces\TransactionLoggable;
use App\Interfaces\Userable;
use App\Repositories\ProductRepository;
use App\Repositories\StudentRepository;
use App\Traits\Models\HasMediaInteractions;
use App\Traits\Models\HasTranslations;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Eloquent\Relations\MorphOne;
use Illuminate\Support\Collection;
use Spatie\MediaLibrary\HasMedia;

class Enrollment extends Model implements HasMedia, TransactionLoggable, Userable, Billable, BillableItem, Deletable
{
    use HasFactory, HasTranslations, HasMediaInteractions;

    public $translatable = ['name'];

    public $guarded = ['id'];

    protected $casts = [
        'custom_field' => 'json',
        'leave_status' => StudentLeaveStatus::class,
        'gender' => Gender::class,
        'admission_type' => StudentAdmissionType::class,
        'payment_status' => EnrollmentPaymentStatus::class,
        'sibling_student_ids' => 'array',
    ];

    /**
     * Get the enrollment's enrollmentSession.
     */
    public function enrollmentSession(): BelongsTo
    {
        return $this->belongsTo(EnrollmentSession::class, 'enrollment_session_id');
    }

    /**
     * Get the enrollment's grade.
     */
    public function admissionGrade(): BelongsTo
    {
        return $this->belongsTo(Grade::class, 'admission_grade_id');
    }

    /**
     * Get the enrollment's nationality.
     */
    public function nationality(): BelongsTo
    {
        return $this->belongsTo(Country::class, 'nationality_id');
    }

    /**
     * Get the enrollment's race.
     */
    public function race(): BelongsTo
    {
        return $this->belongsTo(Race::class, 'race_id');
    }

    /**
     * Get the enrollment's religion.
     */
    public function religion(): BelongsTo
    {
        return $this->belongsTo(Religion::class, 'religion_id');
    }

    public function enrollmentUser(): BelongsTo
    {
        return $this->belongsTo(EnrollmentUser::class);
    }

    /**
     * Get the enrollment's state.
     */
    public function state(): BelongsTo
    {
        return $this->belongsTo(State::class, 'state_id');
    }

    // This relationship is to get the main payment for the enrollment
    // Does not include the extra fees assigned by the finance user
    public function billingDocument(): BelongsTo
    {
        return $this->belongsTo(BillingDocument::class, 'billing_document_id');
    }

    /**
     * Get the enrollment's billing documents
     */
    public function billingDocuments(): MorphMany
    {
        return $this->morphMany(BillingDocument::class, 'bill_to');
    }

    /**
     * Get the enrollment's country.
     */
    public function country(): BelongsTo
    {
        return $this->belongsTo(Country::class, 'country_id');
    }

    public function healthConcern(): BelongsTo
    {
        return $this->belongsTo(HealthConcern::class, 'health_concern_id');
    }

    public function primarySchool(): BelongsTo
    {
        return $this->belongsTo(School::class, 'primary_school_id');
    }

    public function enrollmentExams(): HasMany
    {
        return $this->hasMany(EnrollmentExam::class);
    }

    public function guardians(): HasMany
    {
        return $this->hasMany(EnrollmentGuardian::class);
    }

    public function user(): ?BelongsTo
    {
        return null;
    }

    public function getCurrency(): Currency
    {
        // TODO: Set the currency into config
        return Currency::where('code', 'MYR')->firstOrFail();
    }

    public function canMakePayment(): bool
    {
        return in_array($this->status, [EnrollmentStatus::PENDING_PAYMENT, EnrollmentStatus::PAYMENT_FAILED]);
    }

    public function getUserNumberColumnName(): ?string
    {
        return null;
    }

    public function getUserNumber(): ?string
    {
        return 'ENROLLMENT-' . $this->id;
    }

    public function getUserTypeDescription(): string
    {
        return Userable::USER_LABELS[Enrollment::class];
    }

    public function getProfilePicture(): ?string
    {
        return null;
    }

    public function getBillToType(): string
    {
        return get_class($this);
    }

    public function getBillToId(): int
    {
        return $this->id;
    }

    public function getBillToName(): string
    {
        return $this->getUserName() ?? '';
    }

    public function getUserName(): ?string
    {
        return $this->getTranslation('name', 'en');
    }

    public function firstActiveCard(): MorphOne
    {
        return $this->morphOne(Card::class, 'userable')->latest('id')->where('status', CardStatus::ACTIVE->value);
    }

    public function getBillToAddress(): string
    {
        return $this->address ?? '';
    }

    public function getBillToEmail(): ?string
    {
        return $this->email;
    }

    public function getBillToReferenceNumber(): ?string
    {
        return $this->nric ?? $this->passport_no ?? $this->getUserName();
    }

    public function markAsPaid($payments)
    {
        $this->payment_status = EnrollmentPaymentStatus::PAID;
        $this->payment_date = now()->tz(config('school.timezone'))->toDateString();
        $this->save();
    }

    public function canBeUpdated(): bool
    {
        // if enrollment already passed expiry_date, it cannot be updated
        if ($this->expiry_date && Carbon::now()->greaterThanOrEqualTo($this->expiry_date)) {
            return false;
        }

        return true;
    }

    public function canBeDeleted(): bool
    {
        // block from delete if there is PAID and CONFIRMED billing document
        if (
            $this->billingDocuments()
                ->exists()
        ) {
            ErrorCodeHelper::throwError(ErrorCodeHelper::ENROLLMENT_ERROR, 5028);
        }

        return true;
    }

    public function billingDocumentVoidedActionCallback()
    {
        if (in_array($this->enrollment_status, [EnrollmentStatus::PENDING_PAYMENT, EnrollmentStatus::PAYMENT_FAILED])) {
            $this->enrollment_status = EnrollmentStatus::PENDING_PAYMENT;
            $this->save();
        }
    }

    public function getDiscountApplicableDate()
    {
        return null;
    }

    /**
     * Determine the fees for the enrollment based on the fee_assignment_settings.
     *
     * @return array
     */
    public function determineFees(): array
    {
        $enrollmentSession = $this->enrollmentSession;

        if (!$enrollmentSession || !$enrollmentSession->fee_assignment_settings) {
            return [];
        }

        $product_ids = [];
        $fees = [];
        $settings = $enrollmentSession->fee_assignment_settings;

        foreach ($settings as $setting) {
            $conditions = $setting['conditions'];

            // If conditions are null, apply the fee to all students
            if ($conditions === null) {
                $fees[] = $setting['outcome'];
                $product_ids[] = $setting['outcome']['product_id'];
                continue;
            }

            $is_applicable = true;

            // Check if all conditions are satisfied
            foreach ($conditions as $condition) {
                $field = $condition['field'];
                $operator = $condition['operator'];
                $value = $condition['value'];

                if (!isset($this->{$field})) {
                    $is_applicable = false;
                    break;
                }

                // Get the field value from the enrollment model
                // e.g if the field is 'nationality_id', it will check $this->nationality_id, if the field is 'is_hostel', it will check $this->is_hostel
                $field_value = $this->{$field};

                switch ($operator) {
                    case '=':
                        if ($field_value != $value) {
                            $is_applicable = false;
                        }
                        break;
                    case '!=':
                        if ($field_value == $value) {
                            $is_applicable = false;
                        }
                        break;
                    default:
                        $is_applicable = false;
                }

                if (!$is_applicable) {
                    break;
                }
            }

            if ($is_applicable) {
                $fees[] = $setting['outcome'];
                $product_ids[] = $setting['outcome']['product_id'];
            }
        }

        $products = (new ProductRepository())->find($product_ids)->keyBy('id');

        foreach ($fees as &$fee) {
            $fee['product'] = $products[$fee['product_id']];
        }

        return $fees;
    }

    public function getSiblings(): Collection
    {
        if (empty($this->sibling_student_ids)) {
            return collect();
        }

        return (new StudentRepository())->getAll([
            'id' => $this->sibling_student_ids,
            'order_by' => ['id' => 'asc'],
        ]);
    }

    public function canBeAccessedByUser(EnrollmentUser $enrollment_user): bool
    {
        return $enrollment_user->id === $this->enrollment_user_id;
    }
}
