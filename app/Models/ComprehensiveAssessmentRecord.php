<?php

namespace App\Models;

use App\Enums\ComprehensiveAssessmentResult;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use OwenIt\Auditing\Contracts\Auditable;

class ComprehensiveAssessmentRecord extends Model implements Auditable
{
    use HasFactory, \OwenIt\Auditing\Auditable;

    protected $guarded = ['id'];

    protected $casts = [
        'result' => ComprehensiveAssessmentResult::class
    ];

    public function student(): BelongsTo
    {
        return $this->belongsTo(Student::class);
    }

    public function semesterSetting(): BelongsTo
    {
        return $this->belongsTo(SemesterSetting::class);
    }

    public function semesterClass(): BelongsTo
    {
        return $this->belongsTo(SemesterClass::class);
    }

    public function employee(): BelongsTo
    {
        return $this->belongsTo(Employee::class, 'created_by_id');
    }

    public function comprehensiveAssessmentQuestion(): BelongsTo
    {
        return $this->belongsTo(ComprehensiveAssessmentQuestion::class);
    }
}
