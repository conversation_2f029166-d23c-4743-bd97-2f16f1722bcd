<?php

namespace App\Models;

use App\Interfaces\Deletable;
use App\Interfaces\Userable;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasMany;
use OwenIt\Auditing\Contracts\Auditable;
use Spatie\Permission\Models\Role as SpatieRole;

class Role extends SpatieRole implements Deletable, Auditable
{
    use \OwenIt\Auditing\Auditable;

    const SUPERADMIN = 'Super Admin';
    const GUARDIAN = 'Guardian';
    const EMPLOYEE = 'Employee';
    const MERCHANT = 'Merchant';
    const STUDENT = 'Student';
    const STUDENT_DEACTIVATED = 'Student (Deactivated)';
    const CONTRACTOR = 'Contractor';
    const EMPLOYEE_ADMIN = 'Employee Admin';
    const STUDENT_ADMIN = 'Student Admin';
    const SUPPORT = 'Support';

    use HasFactory;

    public function models(): HasMany
    {
        return $this->hasMany(ModelHasDefaultRole::class);
    }

    public function defaultModels(): Attribute
    {
        return Attribute::make(
            get: function () {
                $default_models = $this->models->pluck('model')->toArray();

                $data = [];

                if (count($default_models) > 0) {
                    foreach ($default_models as $default_model) {
                        if (isset(Userable::USER_TYPE_MAPPING[$default_model])) {
                            $data[] = Userable::USER_TYPE_MAPPING[$default_model];
                        }
                    }
                }

                return $data;
            }
        );
    }

    public function canBeDeleted(): bool
    {
        return $this->users()->count() === 0 && !$this->is_system;
    }
}
