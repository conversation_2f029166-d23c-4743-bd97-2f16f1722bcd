<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use App\Enums\CardStatus;
use App\Enums\PushNotificationPlatform;
use App\Interfaces\Userable;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Illuminate\Support\Collection;
use Laravel\Sanctum\HasApiTokens;
use OwenIt\Auditing\Contracts\Auditable;
use Spatie\Permission\Traits\HasRoles;

/**
 * @property Userable $userable
 */
class User extends Authenticatable implements Auditable
{
    use HasApiTokens, HasFactory, Notifiable, HasRoles, \OwenIt\Auditing\Auditable;

    // Moved to userables
//    public $translatable = ['name'];

    protected $guard_name = 'api';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'email',
        'phone_number',
        'password',
        'userable_id',
        'userable_type',
        'is_active',
        'push_notification_token',
        'push_notification_platform',
        'last_login_at',
        'is_password_reset_required'
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'last_login_at' => 'datetime',
        'email_verified_at' => 'datetime',
        'password' => 'hashed',
        'is_password_reset_required' => 'boolean',
        'push_notification_platform' => PushNotificationPlatform::class,
    ];

    public function cards(): HasMany
    {
        return $this->hasMany(Card::class);
    }

    public function activeCard(): HasOne
    {
        return $this->hasOne(Card::class)->where('status', CardStatus::ACTIVE);
    }

    public function employee(): HasOne
    {
        return $this->hasOne(Employee::class);
    }

    public function merchants(): HasMany
    {
        return $this->hasMany(Merchant::class);
    }

    public function guardian(): HasOne
    {
        return $this->hasOne(Guardian::class);
    }

    public function student(): HasOne
    {
        return $this->hasOne(Student::class);
    }

    public function userables(): HasMany
    {
        return $this->hasMany(UserableView::class);
    }

    public function otps(): HasMany
    {
        return $this->hasMany(UserLoginOtp::class);
    }

    public function walletBelongsToUser($wallet_id): bool
    {
        return $this->wallets()->where('id', $wallet_id)->exists();
    }

    public function wallets(): HasMany
    {
        return $this->hasMany(Wallet::class);
    }

    /**
     * Check if user is a Student.
     * @return bool
     */
    public function isStudent(): bool
    {
        return $this->student !== null;
    }

    /**
     * Check if user is an Employee.
     * @return bool
     */
    public function isEmployee(): bool
    {
        return $this->employee !== null;
    }

    public function isGuardian(): bool
    {
        return $this->guardian !== null;
    }

    public function isInactive(): bool
    {
        return !$this->is_active;
    }

    public function canAccessBalanceForWallet($wallet_id): bool
    {
        $userable_user_ids = $this->getAllUserables()->pluck('user_id')->all();

        return Wallet::whereIn('user_id', $userable_user_ids)->where('id', $wallet_id)->exists();
    }

    public function getAllUserables(): Collection
    {
        $student_userables = $this->getStudentUserables();
        $userables = collect($this->userables);

        return $userables->merge($student_userables);
    }

    public function getStudentUserables(): Collection
    {
        $student_userables = $this->guardian?->directDependants?->pluck('userable');

        return $student_userables ?? collect();
    }

    public function canTopupWallet($wallet_id): bool
    {
        $userable_user_ids = $this->getAllUserables()->pluck('user_id')->all();

        return Wallet::whereIn('user_id', $userable_user_ids)->where('id', $wallet_id)->exists();
    }

    public function canReceivePushNotification()
    {
        return $this->push_notification_token !== null && $this->push_notification_platform !== null;
    }

    public function userInboxes(): HasMany
    {
        return $this->hasMany(UserInbox::class);
    }

    public function hasStudentRole(): bool
    {
        return $this->hasRole(Role::STUDENT);
    }

    public function hasStudentDeactivatedRole(): bool
    {
        return $this->hasRole(Role::STUDENT_DEACTIVATED);
    }

    public function canAccessUserable($userable_type, $userable_id): bool
    {
        $userables = $this->getAllUserables();

        foreach ($userables as $userable) {
            if ($userable->userable_type === $userable_type && $userable->userable_id == $userable_id) {
                return true;
            }
        }

        return false;

    }
}
