<?php

namespace App\Models;

use App\Enums\CardStatus;
use App\Enums\EmployeeStatus;
use App\Enums\Gender;
use App\Enums\JobType;
use App\Enums\MarriedStatus;
use App\Interfaces\AttendanceRecordable;
use App\Interfaces\Billable;
use App\Interfaces\HostelRoomBedAssignable;
use App\Interfaces\LeaveApplicable;
use App\Interfaces\StatusChangeable;
use App\Interfaces\Userable;
use App\Traits\Models\HasMediaInteractions;
use App\Traits\Models\HasTranslations;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Eloquent\Relations\MorphOne;
use OwenIt\Auditing\Contracts\Auditable;
use Spatie\MediaLibrary\HasMedia;

class Employee extends Model implements Userable, HasMedia, Billable, HostelRoomBedAssignable, StatusChangeable, LeaveApplicable, Auditable, AttendanceRecordable
{
    use HasFactory, HasMediaInteractions, HasTranslations, \OwenIt\Auditing\Auditable;

    const SYSTEM_ID = 'SYSTEM';        // default system ID used by automated tasks

    public $translatable = ['name'];

    protected $guarded = ['id'];

    protected $casts = [
        'gender' => Gender::class,
        'status' => EmployeeStatus::class,
        'employment_type' => JobType::class,
        'marriage_status' => MarriedStatus::class,
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function orders(): MorphMany
    {
        return $this->morphMany(EcommerceOrder::class, 'buyer_userable');
    }

    /**
     * Get the employee's jobTitle.
     */
    public function jobTitle(): BelongsTo
    {
        return $this->belongsTo(EmployeeJobTitle::class, 'job_title_id');
    }

    /**
     * Get the employee's religion.
     */
    public function religion(): BelongsTo
    {
        return $this->belongsTo(Religion::class, 'religion_id');
    }

    /**
     * Get the employee's race.
     */
    public function race(): BelongsTo
    {
        return $this->belongsTo(Race::class, 'race_id');
    }

    /**
     * Get the employee's state.
     */
    public function state(): BelongsTo
    {
        return $this->belongsTo(State::class, 'state_id');
    }

    /**
     * Get the employee's country.
     */
    public function country(): BelongsTo
    {
        return $this->belongsTo(Country::class, 'country_id');
    }

    /**
     * Get the employee's session.
     */
    public function employeeSession(): BelongsTo
    {
        return $this->belongsTo(EmployeeSession::class, 'employee_session_id');
    }

    public function beds(): MorphMany
    {
        return $this->morphMany(HostelBedAssignment::class, 'assignable');
    }

    public function cards(): MorphMany
    {
        return $this->morphMany(Card::class, 'userable');
    }

    public function firstActiveCard(): MorphOne
    {
        return $this->morphOne(Card::class, 'userable')->latest('id')->where('status', CardStatus::ACTIVE->value);
    }

    public function highestEducationCountry(): BelongsTo
    {
        return $this->belongsTo(Country::class, 'highest_education_country_id');
    }

    public function employeeCategory(): BelongsTo
    {
        return $this->belongsTo(EmployeeCategory::class, 'employee_category_id');
    }

    public function employmentHistories()
    {
        return $this->hasMany(EmploymentHistory::class);
    }

    public function employmentHistoriesLatestFirst()
    {
        return $this->employmentHistories->sortByDesc('employment_start_date');
    }

    public function hasExistingBedAssignment(): bool
    {
        return $this->activeHostelBedAssignments()->exists();
    }

    public function activeHostelBedAssignments(): MorphMany
    {
        return $this->morphMany(HostelBedAssignment::class, 'assignable')->whereNull('end_date');
    }

    /**
     * Only get latest first active bed assignment
     */
    public function firstActiveHostelBedAssignment(): MorphOne
    {
        return $this->morphOne(HostelBedAssignment::class, 'assignable')->latest('id')->whereNull('end_date');
    }

    public function getPhotoAttribute()
    {
        $media = $this->getMedia('photo')->first();

        if ($media) {
            return $media->getTemporaryUrl(now()->addSeconds(config('filesystems.s3_expires_seconds')));
        }

        return null;
    }

    public function getUserName(): ?string
    {
        return $this->name;
    }

    public function getUserNumberColumnName(): ?string
    {
        return 'employee_number';
    }

    public function getUserNumber(): ?string
    {
        return $this->employee_number;
    }

    public function getProfilePicture(): ?string
    {
        return $this->photo;
    }

    public function getUserTypeDescription(): string
    {
        return Userable::USER_LABELS[Employee::class];
    }

    public function getBillToType(): string
    {
        return get_class($this);
    }

    public function getBillToId(): int
    {
        return $this->id;
    }

    public function getBillToName(): string
    {
        return $this->getUserName() ?? '';
    }

    public function getBillToAddress(): string
    {
        return $this->address ?? '';
    }

    public function getBillToEmail(): ?string
    {
        return $this->email;
    }

    public function getBillToReferenceNumber(): ?string
    {
        return $this->employee_number;
    }

    public function pendingStatusChanges(): MorphMany
    {
        return $this->morphMany(PendingStudentEmployeeStatusChange::class, name: 'status_changeable');
    }

    public function leaves(): MorphMany
    {
        return $this->morphMany(LeaveApplication::class, 'leave_applicable');
    }

    public function getLeaveApplicableClass(): string
    {
        return self::class;
    }

    public function getLeaveApplicableId()
    {
        return $this->id;
    }

    public function timeslots()
    {
        return $this->hasMany(TimeslotTeacher::class);
    }

    public function attendances(): MorphMany
    {
        return $this->morphMany(Attendance::class, 'attendance_recordable');
    }

    public function attendancePeriodOverrides(): MorphMany
    {
        return $this->morphMany(AttendancePeriodOverride::class, 'attendance_recordable');
    }

    public function getId()
    {
        return $this->id;
    }

    public function getClass()
    {
        return self::class;
    }
}
