<?php

namespace App\Models;

use App\Casts\ConfigCast;
use App\Enums\Day;
use App\Helpers\ConfigHelper;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use OwenIt\Auditing\Contracts\Auditable;

class Config extends Model implements Auditable
{
    use HasFactory, \OwenIt\Auditing\Auditable;

    const DATA_TYPE_ARRAY = 'array';
    const DATA_TYPE_OBJECT = 'object';
    const DATA_TYPE_STRING = 'string';
    const DATA_TYPE_DATE = 'date';
    const DATA_TYPE_TIME = 'time';
    const DATA_TYPE_INTEGER = 'integer';
    const DATA_TYPE_DECIMAL = 'decimal';
    const DATA_TYPE_BOOLEAN = 'boolean';

    const CATEGORY_GENERAL = 'GENERAL';
    const CATEGORY_LIBRARY = 'LIBRARY';
    const CATEGORY_HOSTEL = 'HOSTEL';
    const CATEGORY_CANTEEN = 'CANTEEN';
    const CATEGORY_BANK_ACCOUNT = 'BANK_ACCOUNT';
    const CATEGORY_SYSTEM = 'SYSTEM';

    const ENROLLMENT_DOCUMENT_TYPE = 'ENROLLMENT_DOCUMENT_TYPE';
    const WALLET_TRANSFERABLE_MODEL = 'WALLET_TRANSFERABLE_MODEL';
    const WALLET_DEPOSIT_MIN_AMOUNT = 'WALLET_DEPOSIT_MIN_AMOUNT';
    const WALLET_DEPOSIT_MAX_AMOUNT = 'WALLET_DEPOSIT_MAX_AMOUNT';
    const PAYEX_MERCHANT_EMAIL = 'PAYEX_MERCHANT_EMAIL';
    const PAYEX_MERCHANT_SECRET = 'PAYEX_MERCHANT_SECRET';
    const DATA_COMPULSORY_LOCALE = 'DATA_COMPULSORY_LOCALE';
    const ENROLLMENT_FEES = 'ENROLLMENT_FEES';
    const string ENROLLMENT_REPLY_TO_EMAIL = 'ENROLLMENT_REPLY_TO_EMAIL';

    const LIBRARY_BORROW_LIMIT_STUDENT = 'LIBRARY_BORROW_LIMIT_STUDENT';
    const LIBRARY_BORROW_LIMIT_EMPLOYEE = 'LIBRARY_BORROW_LIMIT_EMPLOYEE';
    const LIBRARY_BORROW_LIMIT_LIBRARIAN = 'LIBRARY_BORROW_LIMIT_LIBRARIAN';
    const LIBRARY_BORROW_LIMIT_OTHER = 'LIBRARY_BORROW_LIMIT_OTHER';
    const LOAN_PERIOD_DAY_STUDENT = 'LOAN_PERIOD_DAY_STUDENT';
    const LOAN_PERIOD_DAY_EMPLOYEE = 'LOAN_PERIOD_DAY_EMPLOYEE';
    const LOAN_PERIOD_DAY_LIBRARIAN = 'LOAN_PERIOD_DAY_LIBRARIAN';
    const LOAN_PERIOD_DAY_OTHER = 'LOAN_PERIOD_DAY_OTHER';
    const BORROW_WITH_UNRETURNED_BOOK_STUDENT = 'BORROW_WITH_UNRETURNED_BOOK_STUDENT';
    const BORROW_WITH_UNRETURNED_BOOK_EMPLOYEE = 'BORROW_WITH_UNRETURNED_BOOK_EMPLOYEE';
    const BORROW_WITH_UNRETURNED_BOOK_LIBRARIAN = 'BORROW_WITH_UNRETURNED_BOOK_LIBRARIAN';
    const BORROW_WITH_UNRETURNED_BOOK_OTHER = 'BORROW_WITH_UNRETURNED_BOOK_OTHER';
    const LIBRARY_FINE_PER_DAY_STUDENT = 'LIBRARY_FINE_PER_DAY_STUDENT';
    const LIBRARY_FINE_PER_DAY_EMPLOYEE = 'LIBRARY_FINE_PER_DAY_EMPLOYEE';
    const LIBRARY_FINE_PER_DAY_LIBRARIAN = 'LIBRARY_FINE_PER_DAY_LIBRARIAN';
    const LIBRARY_FINE_PER_DAY_OTHER = 'LIBRARY_FINE_PER_DAY_OTHER';

    // Hostel
    const HOSTEL_REWARD_PUNISHMENT_DEDUCTABLE_MARKS = 'HOSTEL_REWARD_PUNISHMENT_DEDUCTABLE_MARKS';

    // Canteen
    const CANTEEN_OPEN_STATUS = 'CANTEEN_OPEN_STATUS';
    const CANTEEN_OPENING_DAY = 'CANTEEN_OPENING_DAY';
    const CANTEEN_OPENING_TIME = 'CANTEEN_OPENING_TIME';
    const CANTEEN_CUTOFF_DAY = 'CANTEEN_CUTOFF_DAY';
    const CANTEEN_CUTOFF_TIME = 'CANTEEN_CUTOFF_TIME';
    const CANTEEN_NOTIFICATION_DAY = 'CANTEEN_NOTIFICATION_DAY';
    const CANTEEN_NOTIFICATION_TIME = 'CANTEEN_NOTIFICATION_TIME';

    const CANTEEN_NOTIFICATION_EMAIL = 'CANTEEN_NOTIFICATION_EMAIL';

    // Bank accounts
    const BANK_ACCOUNT_DEFAULT_HOSTEL_SAVINGS_ACCOUNT = 'BANK_ACCOUNT_DEFAULT_HOSTEL_SAVINGS_ACCOUNT';
    const BANK_ACCOUNT_DEFAULT_SCHOOL_FEES = 'BANK_ACCOUNT_DEFAULT_SCHOOL_FEES';

    // System
    const MAINTENANCE_MODE = "MAINTENANCE_MODE";
    const IOS_VERSION = "IOS_VERSION";
    const ANDROID_VERSION = "ANDROID_VERSION";
    const HUAWEI_VERSION = "HUAWEI_VERSION";

    // Attendance
    const ATTENDANCE_TAP_CARD_INTERVAL_SECOND = 'ATTENDANCE_TAP_CARD_INTERVAL_SECOND';

    const AVAILABLE_CONFIG_CATEGORIES = [
        self::CATEGORY_GENERAL,
        self::CATEGORY_LIBRARY,
        self::CATEGORY_HOSTEL,
        self::CATEGORY_CANTEEN,
        self::CATEGORY_BANK_ACCOUNT,
    ];

    const AVAILABLE_CONFIGS = [
        self::ENROLLMENT_DOCUMENT_TYPE => [],
        self::WALLET_TRANSFERABLE_MODEL => [
            'guardian' => ['student'],
            'employee' => ['student'],
            'student' => ['student']
        ],
        self::PAYEX_MERCHANT_EMAIL => null,
        self::PAYEX_MERCHANT_SECRET => null,
        self::DATA_COMPULSORY_LOCALE => ['en'],
        self::ENROLLMENT_FEES => 0,
        self::ENROLLMENT_REPLY_TO_EMAIL => null,

        self::LIBRARY_BORROW_LIMIT_STUDENT => 3,
        self::LIBRARY_BORROW_LIMIT_EMPLOYEE => 6,
        self::LIBRARY_BORROW_LIMIT_LIBRARIAN => 5,
        self::LIBRARY_BORROW_LIMIT_OTHER => 2,

        self::LOAN_PERIOD_DAY_STUDENT => 14,
        self::LOAN_PERIOD_DAY_EMPLOYEE => 30,
        self::LOAN_PERIOD_DAY_LIBRARIAN => 21,
        self::LOAN_PERIOD_DAY_OTHER => 21,

        self::BORROW_WITH_UNRETURNED_BOOK_STUDENT => true,
        self::BORROW_WITH_UNRETURNED_BOOK_EMPLOYEE => true,
        self::BORROW_WITH_UNRETURNED_BOOK_LIBRARIAN => true,
        self::BORROW_WITH_UNRETURNED_BOOK_OTHER => true,

        self::LIBRARY_FINE_PER_DAY_STUDENT => 0.2,
        self::LIBRARY_FINE_PER_DAY_EMPLOYEE => 0,
        self::LIBRARY_FINE_PER_DAY_LIBRARIAN => 0,
        self::LIBRARY_FINE_PER_DAY_OTHER => 0,

        // Hostel
        self::HOSTEL_REWARD_PUNISHMENT_DEDUCTABLE_MARKS => 30,
        self::WALLET_DEPOSIT_MIN_AMOUNT => 10,
        self::WALLET_DEPOSIT_MAX_AMOUNT => 300,

        // Canteen
        self::CANTEEN_OPEN_STATUS => true,
        self::CANTEEN_OPENING_DAY => null,
        self::CANTEEN_OPENING_TIME => null,
        self::CANTEEN_CUTOFF_DAY => null,
        self::CANTEEN_CUTOFF_TIME => null,
        self::CANTEEN_NOTIFICATION_DAY => null,
        self::CANTEEN_NOTIFICATION_TIME => null,
        self::CANTEEN_NOTIFICATION_EMAIL => null,

        // Bank accounts
        self::BANK_ACCOUNT_DEFAULT_HOSTEL_SAVINGS_ACCOUNT => null,
        self::BANK_ACCOUNT_DEFAULT_SCHOOL_FEES => null,

        // Attendance
        self::ATTENDANCE_TAP_CARD_INTERVAL_SECOND => 300,

    ];

    const AVAILABLE_USER_CONFIGS = [
        // Canteen
        self::CANTEEN_CUTOFF_DAY => null,
        self::CANTEEN_CUTOFF_TIME => null,

        // Hostel
        self::WALLET_DEPOSIT_MIN_AMOUNT => 10,
        self::WALLET_DEPOSIT_MAX_AMOUNT => 300,
        self::IOS_VERSION => '',
        self::ANDROID_VERSION => '',
        self::HUAWEI_VERSION => ''
    ];

    const CONFIG_DATA_TYPE = [
        self::ENROLLMENT_DOCUMENT_TYPE => self::DATA_TYPE_ARRAY,
        self::WALLET_TRANSFERABLE_MODEL => self::DATA_TYPE_OBJECT,
        self::PAYEX_MERCHANT_EMAIL => self::DATA_TYPE_STRING,
        self::PAYEX_MERCHANT_SECRET => self::DATA_TYPE_STRING,
        self::DATA_COMPULSORY_LOCALE => self::DATA_TYPE_ARRAY,
        self::ENROLLMENT_FEES => self::DATA_TYPE_DECIMAL,
        self::ENROLLMENT_REPLY_TO_EMAIL => self::DATA_TYPE_STRING,

        // Library
        self::LIBRARY_BORROW_LIMIT_STUDENT => self::DATA_TYPE_INTEGER,
        self::LIBRARY_BORROW_LIMIT_EMPLOYEE => self::DATA_TYPE_INTEGER,
        self::LIBRARY_BORROW_LIMIT_LIBRARIAN => self::DATA_TYPE_INTEGER,
        self::LIBRARY_BORROW_LIMIT_OTHER => self::DATA_TYPE_INTEGER,

        self::LOAN_PERIOD_DAY_STUDENT => self::DATA_TYPE_INTEGER,
        self::LOAN_PERIOD_DAY_EMPLOYEE => self::DATA_TYPE_INTEGER,
        self::LOAN_PERIOD_DAY_LIBRARIAN => self::DATA_TYPE_INTEGER,
        self::LOAN_PERIOD_DAY_OTHER => self::DATA_TYPE_INTEGER,

        self::BORROW_WITH_UNRETURNED_BOOK_STUDENT => self::DATA_TYPE_BOOLEAN,
        self::BORROW_WITH_UNRETURNED_BOOK_EMPLOYEE => self::DATA_TYPE_BOOLEAN,
        self::BORROW_WITH_UNRETURNED_BOOK_LIBRARIAN => self::DATA_TYPE_BOOLEAN,
        self::BORROW_WITH_UNRETURNED_BOOK_OTHER => self::DATA_TYPE_BOOLEAN,

        self::LIBRARY_FINE_PER_DAY_STUDENT => self::DATA_TYPE_DECIMAL,
        self::LIBRARY_FINE_PER_DAY_EMPLOYEE => self::DATA_TYPE_DECIMAL,
        self::LIBRARY_FINE_PER_DAY_LIBRARIAN => self::DATA_TYPE_DECIMAL,
        self::LIBRARY_FINE_PER_DAY_OTHER => self::DATA_TYPE_DECIMAL,

        // Hostel
        self::HOSTEL_REWARD_PUNISHMENT_DEDUCTABLE_MARKS => self::DATA_TYPE_INTEGER,
        self::WALLET_DEPOSIT_MIN_AMOUNT => self::DATA_TYPE_DECIMAL,
        self::WALLET_DEPOSIT_MAX_AMOUNT => self::DATA_TYPE_DECIMAL,

        // Canteen
        self::CANTEEN_OPEN_STATUS => self::DATA_TYPE_BOOLEAN,
        self::CANTEEN_OPENING_DAY => self::DATA_TYPE_STRING,
        self::CANTEEN_OPENING_TIME => self::DATA_TYPE_TIME,
        self::CANTEEN_CUTOFF_DAY => self::DATA_TYPE_STRING,
        self::CANTEEN_CUTOFF_TIME => self::DATA_TYPE_TIME,
        self::CANTEEN_NOTIFICATION_DAY => self::DATA_TYPE_STRING,
        self::CANTEEN_NOTIFICATION_TIME => self::DATA_TYPE_TIME,
        self::CANTEEN_NOTIFICATION_EMAIL => self::DATA_TYPE_STRING,

        // Bank accounts
        self::BANK_ACCOUNT_DEFAULT_HOSTEL_SAVINGS_ACCOUNT => self::DATA_TYPE_INTEGER,
        self::BANK_ACCOUNT_DEFAULT_SCHOOL_FEES => self::DATA_TYPE_INTEGER,

        // System
        self::MAINTENANCE_MODE => self::DATA_TYPE_BOOLEAN,
        self::IOS_VERSION => self::DATA_TYPE_STRING,
        self::ANDROID_VERSION => self::DATA_TYPE_STRING,
        self::HUAWEI_VERSION => self::DATA_TYPE_STRING,

        // Attendance
        self::ATTENDANCE_TAP_CARD_INTERVAL_SECOND => self::DATA_TYPE_INTEGER,
    ];

    const CONFIG_CATEGORY = [
        self::CATEGORY_GENERAL => [
            self::ENROLLMENT_DOCUMENT_TYPE,
            self::WALLET_TRANSFERABLE_MODEL,
            self::PAYEX_MERCHANT_EMAIL,
            self::PAYEX_MERCHANT_SECRET,
            self::DATA_COMPULSORY_LOCALE,
            self::ENROLLMENT_FEES,
            self::ENROLLMENT_REPLY_TO_EMAIL,
            self::WALLET_DEPOSIT_MIN_AMOUNT,
            self::WALLET_DEPOSIT_MAX_AMOUNT,
            self::ATTENDANCE_TAP_CARD_INTERVAL_SECOND,
        ],

        self::CATEGORY_LIBRARY => [
            self::LIBRARY_BORROW_LIMIT_STUDENT,
            self::LIBRARY_BORROW_LIMIT_EMPLOYEE,
            self::LIBRARY_BORROW_LIMIT_LIBRARIAN,
            self::LIBRARY_BORROW_LIMIT_OTHER,

            self::LOAN_PERIOD_DAY_STUDENT,
            self::LOAN_PERIOD_DAY_EMPLOYEE,
            self::LOAN_PERIOD_DAY_LIBRARIAN,
            self::LOAN_PERIOD_DAY_OTHER,

            self::BORROW_WITH_UNRETURNED_BOOK_STUDENT,
            self::BORROW_WITH_UNRETURNED_BOOK_EMPLOYEE,
            self::BORROW_WITH_UNRETURNED_BOOK_LIBRARIAN,
            self::BORROW_WITH_UNRETURNED_BOOK_OTHER,

            self::LIBRARY_FINE_PER_DAY_STUDENT,
            self::LIBRARY_FINE_PER_DAY_EMPLOYEE,
            self::LIBRARY_FINE_PER_DAY_LIBRARIAN,
            self::LIBRARY_FINE_PER_DAY_OTHER,
        ],

        self::CATEGORY_HOSTEL => [
            self::HOSTEL_REWARD_PUNISHMENT_DEDUCTABLE_MARKS,
        ],

        self::CATEGORY_CANTEEN => [
            self::CANTEEN_OPEN_STATUS, // true, false
            self::CANTEEN_OPENING_DAY, // TUESDAY
            self::CANTEEN_OPENING_TIME, // 10:00
            self::CANTEEN_CUTOFF_DAY, // FRIDAY
            self::CANTEEN_CUTOFF_TIME, // 23:00
            self::CANTEEN_NOTIFICATION_DAY, // SATURDAY
            self::CANTEEN_NOTIFICATION_TIME, // 11:00
            self::CANTEEN_NOTIFICATION_EMAIL, //<EMAIL>,<EMAIL>
        ],

        self::CATEGORY_BANK_ACCOUNT => [
            self::BANK_ACCOUNT_DEFAULT_HOSTEL_SAVINGS_ACCOUNT,
            self::BANK_ACCOUNT_DEFAULT_SCHOOL_FEES,
        ],

        self::CATEGORY_SYSTEM => [
            self::MAINTENANCE_MODE,
            self::IOS_VERSION,
            self::ANDROID_VERSION,
            self::HUAWEI_VERSION
        ],
    ];

    protected $table = 'master_configs';

    protected $fillable = [
        'category',
        'key',
        'value',
    ];

    protected $casts = [
        'value' => ConfigCast::class,
    ];

    public static function getAvailableConfigOptions($key): array
    {
        return match ($key) {
            self::ENROLLMENT_DOCUMENT_TYPE => [
                ['value' => 'file_ic_front', 'label' => 'IC Front'],
                ['value' => 'file_ic_back', 'label' => 'IC Back'],
                ['value' => 'file_upsr_result', 'label' => 'UPSR Result'],
            ],
            self::WALLET_TRANSFERABLE_MODEL => [
                ['value' => 'guardian', 'label' => 'Guardian', 'sub_value' => [['value' => 'student', 'label' => 'Student']]],
                ['value' => 'employee', 'label' => 'Employee', 'sub_value' => [['value' => 'student', 'label' => 'Student']]],
                ['value' => 'student', 'label' => 'Student', 'sub_value' => [['value' => 'student', 'label' => 'Student']]],
            ],
            self::DATA_COMPULSORY_LOCALE => ConfigHelper::getAvailableLocales(),
            self::CANTEEN_OPENING_DAY, self::CANTEEN_CUTOFF_DAY, self::CANTEEN_NOTIFICATION_DAY => [
                ['value' => Day::MONDAY->value, 'label' => Day::getLabel(Day::MONDAY)],
                ['value' => Day::TUESDAY->value, 'label' => Day::getLabel(Day::TUESDAY)],
                ['value' => Day::WEDNESDAY->value, 'label' => Day::getLabel(Day::WEDNESDAY)],
                ['value' => Day::THURSDAY->value, 'label' => Day::getLabel(Day::THURSDAY)],
                ['value' => Day::FRIDAY->value, 'label' => Day::getLabel(Day::FRIDAY)],
                ['value' => Day::SATURDAY->value, 'label' => Day::getLabel(Day::SATURDAY)],
                ['value' => Day::SUNDAY->value, 'label' => Day::getLabel(Day::SUNDAY)],
            ],
            default => []
        };
    }

    public static function getConfigCategoryList($config = null): array|string
    {
        $flipped_categories = [];
        foreach (self::CONFIG_CATEGORY as $category => $values) {
            foreach ($values as $value) {
                if ($config && $config == $value) {
                    return $category;
                }
                $flipped_categories[$value] = $category;
            }
        }
        return $flipped_categories;
    }
}
