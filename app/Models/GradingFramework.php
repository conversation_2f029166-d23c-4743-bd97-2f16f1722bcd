<?php

namespace App\Models;

use App\Traits\Models\HasTranslations;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use OwenIt\Auditing\Contracts\Auditable;

class GradingFramework extends Model implements Auditable
{
    use HasFactory, HasTranslations, \OwenIt\Auditing\Auditable;

    protected $table = 'master_grading_frameworks';

    protected $guarded = ['id'];
    public $translatable = ['name'];

    protected $casts = [
        'configuration' => 'array'
    ];

    public function studentGradingFrameworks()
    {
        return $this->hasMany(StudentGradingFramework::class, 'grading_framework_id')->where('is_active', true);
    }

    public function activeStudentGradingFrameworks()
    {
        return $this->hasMany(StudentGradingFramework::class, 'grading_framework_id')
            ->select(['id', 'student_id', 'grading_framework_id', 'effective_from', 'effective_to', 'is_active', 'academic_year'])
            ->where('is_active', true);
    }

}
