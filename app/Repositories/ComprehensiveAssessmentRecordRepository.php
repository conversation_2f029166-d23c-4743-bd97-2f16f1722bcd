<?php

namespace App\Repositories;

use App\Models\ComprehensiveAssessmentRecord;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;

class ComprehensiveAssessmentRecordRepository extends BaseRepository
{
    public function getModelClass(): string
    {
        return ComprehensiveAssessmentRecord::class;
    }

    public function getQuery($filters = []): Builder
    {
        return parent::getQuery($filters)
            ->when(isset($filters['comprehensive_assessment_category_id']), function (Builder $query) use ($filters) {
                $query->whereRelation('comprehensiveAssessmentQuestion.comprehensiveAssessmentCategory', 'id', $filters['comprehensive_assessment_category_id']);
            })
            ->when(isset($filters['semester_class_id']), function (Builder $query) use ($filters) {
                $query->where('semester_class_id', $filters['semester_class_id']);
            })
            ->when(isset($filters['student_id']), function (Builder $query) use ($filters) {
                $query->where('student_id', $filters['student_id']);
            })
            ->when(isset($filters['semester_setting_id']), function (Builder $query) use ($filters) {
                $query->where('semester_setting_id', $filters['semester_setting_id']);
            });
    }

    public function getAll(array $filters = []): Collection
    {
        return $this->getQuery($filters)->get();
    }

    public function getAllPaginated(array $filters = []): LengthAwarePaginator
    {
        return $this
            ->getQuery($filters)
            ->paginate($this->per_page);
    }

}
