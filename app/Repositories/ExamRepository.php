<?php

namespace App\Repositories;

use App\Helpers\ConfigHelper;
use App\Models\Exam;
use App\Models\ReportCardOutputComponent;
use App\Models\ResultSourceExam;
use App\Models\ResultsPostingHeader;
use App\Models\ResultsPostingLineItem;
use App\Models\SchoolProfile;
use App\Models\SemesterClass;
use App\Models\SemesterSetting;
use App\Models\Student;
use App\Models\StudentClass;
use App\Models\Subject;
use App\Services\Exam\ExamResultsPostingService;
use Carbon\Carbon;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;

class ExamRepository extends BaseRepository
{
    public function __construct(
        protected StudentRepository $studentRepository,
        protected ExamResultsPostingService $examResultsPostingService,
    ) {

    }

    public function getModelClass(): string
    {
        return Exam::class;
    }

    public function getQuery($filters = []): Builder
    {
        $query = parent::getQuery($filters);
        $query
            ->when(isset($filters['code']), function (Builder $query) use ($filters) {
                $query->where('code', $filters['code']);
            })
            ->when(isset($filters['name']), function (Builder $query) use ($filters) {
                $query->whereTranslations('name', $filters['name'], 'ILIKE', true);
            })
            ->when(isset($filters['is_current_year']) && $filters['is_current_year'], function (Builder $query) {
                $query->whereBetween('results_entry_period_from', [Carbon::now()->startOfYear(), Carbon::now()->endOfYear()])
                    ->where('results_entry_period_from', '<=', now('UTC')->toDateTimeString());
            })
            ->when(isset($filters['results_entry_period_open']) && $filters['results_entry_period_open'], function (Builder $query) {
                $query->where('results_entry_period_from', '<=', now('UTC')->toDateTimeString())
                    ->where('results_entry_period_to', '>=', now('UTC')->toDateTimeString());
            });

        return $query;
    }

    public function getAll(array $filters = []): Collection
    {
        return $this->getQuery($filters)->get();
    }

    public function getAllPaginated(array $filters = []): LengthAwarePaginator
    {
        return $this
            ->getQuery($filters)
            ->paginate($this->per_page);
    }

    public function examinationResultByExamReportData(SemesterSetting $semester_setting, Exam $exam, $semester_class_ids = [], $grade_ids = []): array
    {
        $data = [];
        // get grading framework from semester setting
        $semester_setting_year = $semester_setting->semesterYearSetting?->year;

        if ($semester_setting_year == null) {
            return $data;
        }

        // get students filter by semester_class_ids or grade_ids
        $filters = [];
        if (count($semester_class_ids) > 0) {
            $filters['semester_class_ids'] = $semester_class_ids;
        }
        if (count($grade_ids) > 0) {
            $filters['grade_ids'] = $grade_ids;
        }

        // If both semester_class_ids and grade_ids are empty, return empty data
        if ($filters == []) {
            return $data;
        }

        $filters['semester_setting_id'] = $semester_setting->id;

        $filters['includes'] = [
            'latestPrimaryClass' => function ($query) use ($semester_setting) {
                $query->where('semester_setting_id', $semester_setting->id)
                    ->where('is_latest_class_in_semester', true);
            },
        ];
        $students = $this->studentRepository->getAll($filters)
            ->keyBy('id')
            ->all();

        // get all ResultSourceExam by exam
        $result_source_exams = ResultSourceExam::query()
            ->with(['resultSource.subjects.components'])
            ->whereHas('resultSource.studentGradingFramework', function ($q) use ($students, $semester_setting_year) {
                $q->where('academic_year', $semester_setting_year)
                    ->whereIn('student_id', array_keys($students))
                    ->where('is_active', true);
            })
            ->where('exam_id', $exam->id)
            ->get();

        if (count($result_source_exams) == 0) {
            return $data;
        }

        // pluck all subject_id (ResultSourceExam -> ResultSource -> subjects)
        $subject_ids = $result_source_exams
            ->pluck('resultSource.subjects')
            ->flatten()
            ->pluck('subject_id')
            ->unique();

        $subjects = Subject::query()
            ->select('id', 'name')
            ->whereIn('id', $subject_ids)
            ->get();

        // table header
        $header = [
            'seat_no' => __('general.seat_number'),
            'student_no' => __('general.student_no'),
            'student_name' => __('general.student_name'),
        ];
        foreach ($subjects as $subject) {
            $header['subject_' . $subject->id] = $subject->getTranslation('name', app()->getLocale());
        }

        // table content
        $body = [];
        $available_subjects_by_semester_class = [];
        foreach ($result_source_exams as $result_source_exam) {
            $student = $students[$result_source_exam->resultSource->studentGradingFramework->student_id];

            $d = [
                'seat_no' => $student->latestPrimaryClass->seat_no,
                'student_no' => $student->student_number,
                'student_name' => $student->getFormattedTranslations('name'),
            ];
            foreach ($subjects as $subject) {
                $d['subject_' . $subject->id] = null;
            }

            $result_source_subjects = $result_source_exam->resultSource->subjects;
            foreach ($result_source_subjects as $result_source_subject) {
                $d['subject_' . $result_source_subject->subject_id] = (int) $this->examResultsPostingService->calculateResultSourceSubjectActualScore($result_source_subject);

                $available_subjects_by_semester_class[$student->latestPrimaryClass->semester_class_id][$result_source_subject->subject_id] ??= true;
            }
            $body[$student->latestPrimaryClass->semester_class_id][] = $d;
        }

        // append class name
        $semester_classes = SemesterClass::with('classModel')
            ->whereIn('id', array_keys($body))
            ->get()
            ->keyBy('id')
            ->all();

        foreach ($body as $semester_class_id => &$students_scores) {
            $class_name = $semester_classes[$semester_class_id]->classModel->getTranslation('name', app()->getLocale());

            $available_subject_ids = $available_subjects_by_semester_class[$semester_class_id] ?? [];
            $available_subject_ids = array_keys($available_subject_ids);
            $available_subject_keys = array_map(fn($subject_id) => 'subject_' . $subject_id, $available_subject_ids);

            $semester_class_header = $header;

            // loop thru each student, if score not null, append to not_null array, if all students score are null, won't exist in not_null array
            foreach ($students_scores as &$student_scores) {
                foreach ($student_scores as $key => $student_score) {
                    if (str_starts_with($key, 'subject_') && !in_array($key, $available_subject_keys)) {
                        unset($student_scores[$key]);
                        unset($semester_class_header[$key]);
                    }
                }
            }

            $students_scores = [
                'class_name' => $class_name,
                'header' => $semester_class_header,
                'data' => collect($students_scores)->sortBy('seat_no')->values()->toArray(),
            ];
        }

        $data = [
            'data_by_classes' => collect($body)->sortBy('class_name')->values()->toArray(),
        ];

        return $data;
    }

    public function examinationResultByStudentReportData(SemesterSetting $semester_setting, Exam $exam, $semester_class_ids = [], $grade_ids = []): array
    {
        $data = [];

        // get grading framework from semester setting
        $semester_setting_year = $semester_setting->semesterYearSetting?->year;
        if ($semester_setting_year == null) {
            return $data;
        }

        // get students filter by semester_class_ids or grade_ids
        $filters = [];
        if (count($semester_class_ids) > 0) {
            $filters['semester_class_ids'] = $semester_class_ids;
        }
        if (count($grade_ids) > 0) {
            $filters['grade_ids'] = $grade_ids;
        }

        // If both semester_class_ids and grade_ids are empty, return empty data
        if ($filters == []) {
            return $data;
        }

        $filters['semester_setting_id'] = $semester_setting->id;

        $filters['includes'] = [
            'latestPrimaryClass' => function ($query) use ($semester_setting) {
                $query->where('semester_setting_id', $semester_setting->id)
                    ->where('is_latest_class_in_semester', true);

                $query->with([
                    'semesterClass' => function ($query) {
                        $query->with([
                            'classModel' => function ($query) {
                                $query->select('id', 'name');
                            },
                            'homeroomTeacher' => function ($query) {
                                $query->select('id', 'name');
                            },
                        ]);
                    },
                ]);
            },
        ];
        $students = $this->studentRepository->getAll($filters)
            ->keyBy('id')
            ->all();

        // get all ResultSourceExam by exam
        $result_source_exams = ResultSourceExam::query()
            ->with(['resultSource.subjects.components', 'resultSource.subjects.subject'])
            ->whereHas('resultSource.studentGradingFramework', function ($q) use ($students, $semester_setting_year) {
                $q->where('academic_year', $semester_setting_year)
                    ->whereIn('student_id', array_keys($students))
                    ->where('is_active', true);
            })
            ->where('exam_id', $exam->id)
            ->get();

        if (count($result_source_exams) == 0) {
            return $data;
        }

        $principal_name = SchoolProfile::first()?->getTranslation('principal_name', 'zh');

        $data = [];
        foreach ($result_source_exams as $result_source_exam) {
            $student = $students[$result_source_exam->resultSource->studentGradingFramework->student_id];
            // student details
            $d = [
                'exam' => $exam->getFormattedTranslations('name'),
                'seat_no' => $student->latestPrimaryClass->seat_no,
                'student_no' => $student->student_number,
                'student_name_zh' => $student->getTranslation('name', 'zh'),
                'student_name_en' => $student->getTranslation('name', 'en'),
                'class_name' => $student->latestPrimaryClass->semesterClass->classModel->getTranslation('name', 'en'),
                'homeroom_teacher' => $student->latestPrimaryClass->semesterClass->homeroomTeacher?->getTranslation('name', 'zh'),
                'principal_name' => $principal_name,
            ];

            // student scores
            $result_source_subjects = $result_source_exam->resultSource->subjects;
            $result_source_subjects = $result_source_subjects->sortByDesc('subject.sequence');

            foreach ($result_source_subjects as $result_source_subject) {
                $d['scores'][] = [
                    'subject_name_zh' => $result_source_subject->subject->getTranslation('name', 'zh'),
                    'subject_name_en' => $result_source_subject->subject->getTranslation('name', 'en'),
                    'score' => (int) $this->examResultsPostingService->calculateResultSourceSubjectActualScore($result_source_subject),
                ];
            }
            $data[] = $d;
        }

        // sort by class_name, then each class sort by seat_no
        $data = collect($data)->sortBy('seat_no')->sortBy('class_name')->values()->toArray();

        return $data;
    }

    public function examinationResultBySemesterClassData(ResultsPostingHeader $results_posting_header, SemesterClass $semester_class, bool $with_position_in_standard)
    {
        // perhaps can filter at frontend?
        if (!in_array($results_posting_header->status, [ResultsPostingHeader::STATUS_COMPLETED, ResultsPostingHeader::STATUS_IN_PROGRESS])) {
            return [];
        }

        $semester_setting_year = $semester_class->semesterSetting->semesterYearSetting?->year;

        if ($semester_setting_year == null) {
            return [];
        }

        $student_classes = StudentClass::query()
            ->with([
                'student' => function ($query) use ($semester_setting_year) {
                    $query->with([
                        'gradingFrameworks' => function ($query) use ($semester_setting_year) {
                            $query->where('academic_year', $semester_setting_year);
                            $query->with([
                                'resultSources' => function ($query) {
                                    $query->select(['id', 'student_grading_framework_id', 'code']);
                                    $query->with([
                                        'subjects' => function ($query) {
                                            $query->select(['id', 'result_source_id', 'subject_id', 'is_exempted']);
                                        },
                                    ]);
                                },
                            ]);
                        },
                    ]);
                },
            ])
            ->where('semester_setting_id', $semester_class->semester_setting_id)
            ->where('semester_class_id', $semester_class->id)
            ->where('is_latest_class_in_semester', true)
            ->get()
            ->keyBy('student_id')
            ->all();

        $line_items = ResultsPostingLineItem::query()
            ->with([
                'subject',
                'reportCardOutputComponent' => function ($query) {
                    $query->select(['id', 'total_formula']);
                },
            ])
            ->where('header_id', $results_posting_header->id)
            ->where('semester_class_id', $semester_class->id)
            ->whereIn('student_id', array_keys($student_classes)) // student with class
            ->get();

        if (count($line_items) == 0) {
            return [];
        }

        // pluck all subject_id (ResultsPostingLineItem -> subjects)
        $subject_ids = $line_items
            ->whereNotNull('subject_id')
            ->pluck('subject_id')
            ->unique();

        $subjects = Subject::query()
            ->select('id', 'name')
            ->whereIn('id', $subject_ids)
            ->orderBy('sequence', 'DESC')
            ->get();

        // table header
        $available_locales = ConfigHelper::getAvailableLocales();
        $header = [
            'seat_no' => __('general.seat_number'),
            'student_no' => __('general.student_no'),
        ];

        rsort($available_locales); 
        foreach ($available_locales as $locale) {
            $header['student_name_' . $locale] = __('general.student_name');
        }
        $header += [
            'gross_total' => __('general.gross_total'),
            'gross_average' => __('general.gross_average'),
            'mark_subtracted' => __('general.mark_subtracted'),
            'mark_added' => __('general.mark_added'),
            'net_average' => __('general.net_average'),
            'grade_exam' => __('general.grade_exam'),
            'position_in_class' => __('general.position_in_class'),
            'position_in_standard' => __('general.position_in_standard'),
            'conduct' => __('general.conduct'),
        ];

        foreach ($subjects as $subject) {
            $header['subject_' . $subject->id] = $subject->getTranslation('name', app()->getLocale());
        }

        $line_items_group_by_student_id = $line_items
            ->groupBy('student_id')
            ->all();

        $data = [];
        foreach ($line_items_group_by_student_id as $student_id => $line_items) {
            $student_class = $student_classes[$student_id];
            $student = $student_class->student;

            $position_in_class = $this->getLineItemValueByReportCardOutputComponentCode($line_items, 'SYS_NET_AVG', 'class_rank', false);
            $position_in_class_and_population = '';
            if ($position_in_class != '' && $position_in_class != null) {
                $position_in_class_and_population = $position_in_class . ' / ' . $this->getLineItemValueByReportCardOutputComponentCode($line_items, 'SYS_NET_AVG', 'class_population', false);
            }

            $position_in_standard = $this->getLineItemValueByReportCardOutputComponentCode($line_items, 'SYS_NET_AVG', 'grade_rank', false);
            if ($position_in_standard != '' && $position_in_standard != null) {
                $position_in_standard .= ' / ' . $this->getLineItemValueByReportCardOutputComponentCode($line_items, 'SYS_NET_AVG', 'grade_population', false);
            } else {
                $position_in_standard = '';
            }

            $body = [
                'seat_no' => $student_class->seat_no,
                'student_no' => $student->student_number,
            ];
            foreach ($available_locales as $locale) {
                $body['student_name_' . $locale] = $student->getTranslation('name', $locale);
            }
            $body += [
                'gross_total' => $this->getLineItemValueByReportCardOutputComponentCode($line_items, 'GT', 'total'),
                'gross_average' => $this->getLineItemValueByReportCardOutputComponentCode($line_items, 'GA', 'total'),
                'mark_subtracted' => $this->getLineItemValueByReportCardOutputComponentCode($line_items, 'MS', 'total'),
                'mark_added' => $this->getLineItemValueByReportCardOutputComponentCode($line_items, 'MA', 'total'),
                'net_average' => $this->getLineItemValueByReportCardOutputComponentCode($line_items, 'SYS_NET_AVG', 'total'),
                'grade_exam' => $this->getLineItemValueByReportCardOutputComponentCode($line_items, 'SYS_NET_AVG', 'label', false),
                'position_in_class' => $position_in_class_and_population,
                'position_in_standard' => $position_in_standard,
                'conduct' => $this->getLineItemValueByReportCardOutputComponentCode($line_items, 'CONDUCT', 'label', false),
            ];
            if ($with_position_in_standard == false) {
                unset($header['position_in_standard']);
                unset($body['position_in_standard']);
            }
            foreach ($subjects as $subject) {
                $line_item = $line_items->where('subject_id', $subject->id)->first();

                if ($line_item) {
                    $is_exempted = $this->determineStudentIsExempted($subject->id, $line_item->reportCardOutputComponent, $student);

                    if ($line_item->output_type == ReportCardOutputComponent::OUTPUT_TYPE_GRADE) { // if output type = GRADE
                        $value = $line_item->label;
                    } else { // if output type = SCORE / null
                        $value = $line_item->total != null ?
                            intval($line_item->total) == $line_item->total ? (string) intval($line_item->total) : number_format($line_item->total, 2) // 1 == 1.00 = true, return 1 | 1 == 1.11 = false, return 1.11
                            : '-';
                    }
                    $body['subject_' . $subject->id] = $is_exempted == true ? '-' : $value;
                } else {
                    $body['subject_' . $subject->id] = '';
                }
            }

            $data[] = [
                'position_in_class' => $position_in_class,
                'data' => $body,
            ];
        }

        // students without position_in_class ("-"), will appear before students with position_in_class
        $data = array_column(
            collect($data)->sortBy('position_in_class')->values()->toArray(),
            'data'
        );

        return [
            'class' => $semester_class->classModel->getFormattedTranslations('name'),
            'header' => $header,
            'body' => $data,
        ];
    }

    public function getLineItemValueByReportCardOutputComponentCode($line_items, $report_card_output_component_code, $column, $number_format = true)
    {
        $line_item = $line_items->where('report_card_output_component_code', $report_card_output_component_code)->first();

        if ($line_item && $line_item->$column != null) {
            return $number_format == true ? number_format($line_item->$column, 2, '.', '') : $line_item->$column;
        } else {
            return '';
        }
    }

    public function determineStudentIsExempted($subject_id, ReportCardOutputComponent $report_card_output_component, Student $student)
    {
        if ($report_card_output_component->total_formula == null) {
            return false;
        }

        $student_grading_frameworks = $student->gradingFrameworks;

        if (count($student_grading_frameworks) == 0) {
            return false;
        }

        foreach ($student_grading_frameworks as $grading_framework) {
            foreach ($grading_framework->resultSources as $result_source) {
                if (str_contains($report_card_output_component->total_formula, $result_source->code)) {
                    $result_source_subject = $result_source->subjects->where('subject_id', $subject_id)->first();

                    if ($result_source_subject?->is_exempted) {
                        return true;
                    }
                }
            }
        }

        return false;
    }
}
