<?php

namespace App\Repositories;

use App\Models\Competition;
use App\Models\Department;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Carbon;

class CompetitionRepository extends BaseRepository
{
    public function getModelClass(): string
    {
        return Competition::class;
    }

    public function getQuery($filters = []): Builder
    {
        $order_by = isset($filters['order_by']) ? $filters['order_by'] : null;

        return parent::getQuery($filters)
            ->select('competitions.*')
            ->when(isset($filters['name']) , function (Builder $query) use ($filters) {
                $query->where('name', 'ILIKE', "%". $filters['name']."%");
            })
            ->when(isset($filters['department_id']) , function (Builder $query) use ($filters) {
                $query->where('department_id', $filters['department_id']);
            })
            ->when(isset($filters['date']) , function (Builder $query) use ($filters) {
                $query->where('date', $filters['date']);
            })
            ->when(isset($filters['student_name']) || isset($filters['student_number']) , function (Builder $query) use ($filters) {
                $query->whereHas('records', function (Builder $query) use ($filters) {
                    $query->whereHas('student', function (Builder $query) use ($filters) {
                        if(isset($filters['student_number'])){
                            $query->where('student_number', $filters['student_number']);
                        }
                        if(isset($filters['student_name'])){
                            $query->whereTranslations('name', $filters['student_name'], 'ILIKE', true);
                        }
                    });
                });
            })
            ->when(isset($order_by['department']), function (Builder $query) use ($order_by) {
                $query->join('master_departments', 'master_departments.id', '=', 'competitions.department_id');
                $this->setupOrderBy($query, $order_by['department'], Department::class, 'master_departments.');
            });
    }

    public function getAll(array $filters = []): Collection
    {
        return $this->getQuery($filters)->get();
    }

    public function getAllPaginated(array $filters = []): LengthAwarePaginator
    {
        return $this->getQuery($filters)->paginate($this->per_page);
    }

    public function studentPerformanceReportByDateRangeData($from_date, $to_date)
    {
        $competitions_with_record = Competition::query()
            ->with([
                'records' => function ($query) {
                    $query->with([
                        'award' => function ($query) {
                            $query->select(['id', 'name', 'sequence']);
                        },
                        'student' => function ($query) {
                            $query->select(['id', 'name', 'student_number']);
                        },
                        'semesterClass' => function ($query) {
                            $query->select(['id', 'class_id']);
                            $query->with([
                                'classModel' => function ($query) {
                                    $query->select(['id', 'name']);
                                },
                            ]);
                        },
                    ]);
                },
                'department' => function ($query) {
                    $query->select(['id', 'name']);
                },
            ])
            ->whereHas('records')
            ->whereBetween('date', [$from_date, $to_date])
            ->get();

        $data['header'] = [
            __('general.no'),
            __('general.competition_name'),
            __('general.award'),
            __('general.date'),
            __('general.department'),
            __('general.student_number'),
            __('general.student_name'),
            __('general.class'),
            __('general.year'),
            __('general.type_of_bonus'),
            __('general.mark_of_bonus'),
        ];

        $no = 1;
        $locale = app()->getLocale();
        foreach($competitions_with_record as $competition_with_record){
            $records = $competition_with_record->records->sortBy('award.sequence'); // sort by award's sequence ascending
            foreach($records as $record){
                $data['body'][$record->id] = [
                    'no' => $no,
                    'competition_name' => $competition_with_record->name,
                    'award_name' => $record->award->getTranslation('name', $locale),
                    'competition_date' => $competition_with_record->date,
                    'department_name' => $competition_with_record->department->getTranslation('name', $locale),
                    'student_number' => $record->student->student_number,
                    'student_name' => $record->student->getTranslation('name', $locale),
                    'class_name' => $record->semesterClass->classModel->getTranslation('name', $locale),
                    'competition_year' => Carbon::parse($competition_with_record->date)->year,
                    'type_of_bonus' => $record->type_of_bonus->value,
                    'mark_of_bonus' => $record->mark,
                ];
                $no++;
            }
        }
        
        return $data;
    }
}
