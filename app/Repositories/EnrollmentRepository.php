<?php

namespace App\Repositories;

use App\Models\Enrollment;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;

class EnrollmentRepository extends BaseRepository
{
    public function getModelClass(): string
    {
        return Enrollment::class;
    }

    public function getQuery($filters = []): Builder
    {
        $query = parent::getQuery($filters);

        return $query
            ->when(isset($filters['admission_year']), function (Builder $query) use ($filters) {
                $query->where('admission_year', $filters['admission_year']);
            })
            ->when(isset($filters['admission_grade_id']), function (Builder $query) use ($filters) {
                $query->where('admission_grade_id', $filters['admission_grade_id']);
            })
            ->when(isset($filters['name']), function (Builder $query) use ($filters) {
                $query->whereTranslations('name', $filters['name'], 'ILIKE', true);
            })
            ->when(isset($filters['nric']), function (Builder $query) use ($filters) {
                if (is_array($filters['nric'])) {
                    $query->whereIn('nric', $filters['nric']);
                } else {
                    $query->where('nric', $filters['nric']);
                }
            })
            ->when(isset($filters['passport_number']), function (Builder $query) use ($filters) {
                if (is_array($filters['passport_number'])) {
                    $query->whereIn('passport_number', $filters['passport_number']);
                } else {
                    $query->where('passport_number', $filters['passport_number']);
                }
            })
            ->when(isset($filters['nationality_id']), function (Builder $query) use ($filters) {
                $query->where('nationality_id', $filters['nationality_id']);
            })
            ->when(isset($filters['gender']), function (Builder $query) use ($filters) {
                $query->where('gender', $filters['gender']);
            })
            ->when(isset($filters['birth_cert_number']), function (Builder $query) use ($filters) {
                $query->where('birth_cert_number', $filters['birth_cert_number']);
            })
            ->when(isset($filters['race_id']), function (Builder $query) use ($filters) {
                $query->where('race_id', $filters['race_id']);
            })
            ->when(isset($filters['religion_id']), function (Builder $query) use ($filters) {
                $query->where('religion_id', $filters['religion_id']);
            })
            ->when(isset($filters['state_id']), function (Builder $query) use ($filters) {
                $query->where('state_id', $filters['state_id']);
            })
            ->when(isset($filters['country_id']), function (Builder $query) use ($filters) {
                $query->where('country_id', $filters['country_id']);
            })
            ->when(isset($filters['enrollment_status']), function (Builder $query) use ($filters) {
                $query->where('enrollment_status', $filters['enrollment_status']);
            })
            ->when(isset($filters['payment_status']), function (Builder $query) use ($filters) {
                $query->where('payment_status', $filters['payment_status']);
            })
            ->when(isset($filters['enrollment_user_id']), function (Builder $query) use ($filters) {
                $query->where('enrollment_user_id', $filters['enrollment_user_id']);
            })
            ->when(isset($filters['common_search']), function (Builder $query) use ($filters) {
                $query->where(function ($query) use ($filters) {
                    $query->whereTranslations('name', $filters['common_search'], 'ILIKE', true)
                        ->when(!is_array($filters['common_search']), function ($query) use ($filters) {
                            $query->orWhere('nric', "ILIKE", "%" . $filters['common_search'] . "%");
                        });
                });
            })
            ->when(isset($filters['enrollment_session_id']), function (Builder $query) use ($filters) {
                $query->where('enrollment_session_id', $filters['enrollment_session_id']);
            })
            ->when(isset($filters['registration_date']), function (Builder $query) use ($filters) {
                $query->where('registration_date', $filters['registration_date']);
            })
            ->when(isset($filters['expiry_date']), function (Builder $query) use ($filters) {
                $query->where('expiry_date', $filters['expiry_date']);
            })
            ->when(isset($filters['guardian_name']) || isset($filters['guardian_phone_number']), function (Builder $query) use ($filters) {
                $query->whereHas('guardians', function ($query) use ($filters) {
                    $query
                        ->when(isset($filters['guardian_name']), function (Builder $query) use ($filters) {
                            $query->whereTranslations('name', $filters['guardian_name'], 'ILIKE', true);
                        })
                        ->when(isset($filters['guardian_phone_number']), function (Builder $query) use ($filters) {
                            $query->where('phone_number', 'ILIKE', '%' . $filters['guardian_phone_number'] . '%');
                        });
                });
            })
            ->when(isset($filters['is_hostel']), function (Builder $query) use ($filters) {
                $query->where('is_hostel', $filters['is_hostel']);
            })
            ->when(isset($filters['is_foreigner']), function (Builder $query) use ($filters) {
                $query->where('is_foreigner', $filters['is_foreigner']);
            })
            ->when(!(isset($filters['order_by']['id']) || (isset($filters['order_by']) && is_array($filters['order_by']) && in_array('id', $filters['order_by']))), function (Builder $query) {
                // Always do this to prevent pagination issue
                $query->orderByDesc('id');
            })
        ;
    }

    public function getAll(array $filters = []): Collection
    {
        return $this->getQuery($filters)->get();
    }

    public function getAllPaginated(array $filters = []): LengthAwarePaginator
    {
        return $this
            ->getQuery($filters)
            ->paginate($this->per_page);
    }

    public function getPartialEnrollments(array $filters = [])
    {
        return $this
            ->getQuery($filters)
            ->select([
                'id',
                'nric',
                'passport_number',
            ])
            ->get();
    }
}
