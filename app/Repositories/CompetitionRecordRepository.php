<?php

namespace App\Repositories;

use App\Models\CompetitionRecord;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;

class CompetitionRecordRepository extends BaseRepository
{
    public function getModelClass(): string
    {
        return CompetitionRecord::class;
    }

    public function getQuery($filters = []): Builder
    {

        return parent::getQuery($filters)
            ->when(isset($filters['competition_id']) , function (Builder $query) use ($filters) {
                $query->where('competition_id', $filters['competition_id']);
            })
            ->when(isset($filters['student_id']), function (Builder $query) use ($filters) {
                $query->where('student_id', $filters['student_id']);
            })
            ->when(isset($filters['award_id']), function (Builder $query) use ($filters) {
                $query->where('award_id', $filters['award_id']);
            });
    }

    public function getAll(array $filters = []): Collection
    {
        return $this->getQuery($filters)->get();
    }

    public function getAllPaginated(array $filters = []): LengthAwarePaginator
    {
        return $this->getQuery($filters)->paginate($this->per_page);
    }
}
