<?php

namespace App\Jobs;

use App\Mail\EnrollmentStatusMail;
use App\Models\Enrollment;
use App\Services\ISmsService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Mail;

class NotifyGuardianOfEnrollmentStatusJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $tries = 1;
    public mixed $enrollmentId;
    public string $guardianEmail;
    public string $guardianPhoneNumber;

    public function __construct($enrollment_id, $guardian_email, $guardian_phone_number)
    {
        $this->onQueue('enrollment')->onConnection('enrollment');
        $this->enrollmentId = $enrollment_id;
        $this->guardianEmail = $guardian_email;
        $this->guardianPhoneNumber = $guardian_phone_number;
    }

    public function handle(): void
    {
        $enrollment = Enrollment::find($this->enrollmentId);

        if (!$enrollment) {
            return;
        }

        Mail::to($this->guardianEmail)->send(new EnrollmentStatusMail($enrollment));

        $sms_message = "滨华中学：您好，恭喜贵子女已被录取为本校2026年初一新生。请留意电邮，我们将与您联系安排注册事宜。\n
Congratulations! Your child has been admitted to Pin Hwa High School for 2026 Junior 1. We will send you an email with registration details.";
        app()->make(ISmsService::class)->sendSms($this->guardianPhoneNumber, $sms_message);
    }
}
