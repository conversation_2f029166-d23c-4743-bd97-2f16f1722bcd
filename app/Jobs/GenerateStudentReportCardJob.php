<?php

namespace App\Jobs;

use App\Models\ResultsPostingHeader;
use App\Models\Student;
use Illuminate\Bus\Batchable;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class GenerateStudentReportCardJob implements ShouldQueue
{
    use Batchable, Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $tries = 1;

    public ResultsPostingHeader $postingHeader;
    public Student $student;
    public int $studentId;

    public function __construct(ResultsPostingHeader $posting_header, $student_id)
    {
        $this->postingHeader = $posting_header;
        $this->studentId = $student_id;
        $this->student = Student::findOrFail($student_id);
    }

    public function handle(): void
    {
        try{

            $this->postingHeader->addLog("[{$this->student->student_number}] Generating report card PDF for student {$this->student->student_number}");

            $template_service = $this->postingHeader->getReportCardTemplateService();
            $template_service
                ->setResultsPostingHeader($this->postingHeader)
                ->setStudent($this->student)
                ->generate();

            $this->postingHeader->addLog("[{$this->student->student_number}] Report card PDF generated for student {$this->student->student_number}");

        }catch(\Throwable $e){
            \Log::error($e->getMessage());
            \Log::error($e->getTraceAsString());
            $this->postingHeader->addError($this->studentId, $e->getMessage());
        }
    }

}
