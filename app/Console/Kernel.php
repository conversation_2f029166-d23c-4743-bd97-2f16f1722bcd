<?php

namespace App\Console;

use App\Enums\Day;
use App\Helpers\ConfigHelper;
use App\Models\Config;
use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel
{
    /**
     * Define the application's command schedule.
     */
    protected function schedule(Schedule $schedule): void
    {
        $schedule->command('book:loan-overdue-amount')
            ->dailyAt('00:01')
            ->withoutOverlapping(10)
            ->onOneServer()
            ->runInBackground();

        $schedule->command('announcement:send')
            ->everyFifteenMinutes()
            ->withoutOverlapping(10)
            ->onOneServer()
            ->runInBackground();

        $schedule->command('ecommerce:check-order-status')
            ->everyMinute()
            ->withoutOverlapping(10)
            ->onOneServer()
            ->runInBackground();

        $canteen_notification_day = ConfigHelper::get(Config::CANTEEN_NOTIFICATION_DAY);
        $canteen_notification_time = ConfigHelper::get(Config::CANTEEN_NOTIFICATION_TIME);

        if ($canteen_notification_day && $canteen_notification_time) {
            $schedule->command('canteen-report-by-class-weekly:send')
                ->days(Day::mapToCarbonWeekday(Day::from($canteen_notification_day)))
                ->at($canteen_notification_time)
                ->withoutOverlapping(10)
                ->onOneServer()
                ->runInBackground();
        }

        $schedule->command('execute:pending-status-change')
            ->dailyAt('00:00')
            ->withoutOverlapping(10)
            ->onOneServer()
            ->runInBackground();

        $schedule->command('results-posting:process', ['--actual'])
            ->everyMinute()
            ->withoutOverlapping(120)
            ->onOneServer()
            ->runInBackground();

        $schedule->command('unpaid-item-assignment:process')
            ->everyTenMinutes()
            ->withoutOverlapping(120)
            ->onOneServer()
            ->runInBackground();

        $schedule->command('payment:update-status')
            ->everyFiveMinutes()
            ->withoutOverlapping(120)
            ->onOneServer()
            ->runInBackground();

        $schedule->command('results-source-subject:process')
            ->dailyAt('01:00')
            ->withoutOverlapping(10)
            ->onOneServer()
            ->runInBackground();

        $schedule->command('exam:expire-student-grading-framework')
            ->dailyAt('01:00')
            ->withoutOverlapping(10)
            ->onOneServer()
            ->runInBackground();

        $schedule->command('publish-student-report-card:process')
            ->dailyAt('01:00')
            ->withoutOverlapping(10)
            ->onOneServer()
            ->runInBackground();

        $schedule->command('mat-view:refresh')
            ->dailyAt('00:30')
            ->withoutOverlapping(120)
            ->onOneServer()
            ->runInBackground();

        $schedule->command('init:student-attendance')
            ->dailyAt('03:00')
            ->onOneServer()
            ->withoutOverlapping(10)
            ->runInBackground();

        // Don't need this as now post when attendance is created
//        $schedule->command('posting:student-attendance-input')
//            ->dailyAt('07:31')
//            ->withoutOverlapping(10)
//            ->onOneServer()
//            ->runInBackground();

        $schedule->command('posting:student-attendance-input', ['--process-check-out'])
            ->dailyAt('20:00')
            ->withoutOverlapping(10)
            ->onOneServer()
            ->runInBackground();

        $schedule->command('posting:employee-contractor-attendance-input --is-all-contractors')
            ->dailyAt('23:30')
            ->onOneServer()
            ->withoutOverlapping(10)
            ->runInBackground();

        $this->cacheStudentAttendancePeriod($schedule);

        $schedule->command('mat-view:refresh', ['--view-model' => 'App\Models\ExamPostingPreCheck'])
            ->everyFiveMinutes()
            ->between('06:00', '02:00')
            ->withoutOverlapping(10)
            ->onOneServer();
    }

    /**
     * Register the commands for the application.
     */
    protected function commands(): void
    {
        $this->load(__DIR__ . '/Commands');

        require base_path('routes/console.php');
    }

    public function cacheStudentAttendancePeriod(Schedule &$schedule)
    {

        // mori provided timings 5am, 6am, 6:45am, 8am and 12pm
        // we run 7 minutes before
        $timings = [
            '04:53', '05:53', '06:38', '07:53', '11:53',
        ];

        foreach ($timings as $timing) {
            $schedule->command('cache:student-attendance-periods')
                ->dailyAt($timing)
                ->withoutOverlapping(10)
                ->onOneServer()
                ->runInBackground();
        }

    }
}
