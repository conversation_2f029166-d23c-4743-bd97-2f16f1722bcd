<?php

namespace App\Console\Commands;

use App\Exceptions\RepositoryException;
use App\Factories\StatusChangeableFactory;
use App\Interfaces\StatusChangeable;
use App\Interfaces\StatusChangeableInterface;
use App\Models\EmployeeJobTitle;
use App\Models\LeaveReason;
use App\Models\PendingStudentEmployeeStatusChange;
use App\Models\SemesterClass;
use App\Models\SemesterSetting;
use App\Models\Student;
use App\Repositories\PendingStudentEmployeeStatusChangeRepository;
use App\Services\StudentService;
use Illuminate\Console\Command;

class ExecutePendingStudentEmployeeStatusChange extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'execute:pending-status-change   { --execution_date= : eg: 2024-12-31 }
                                                            { --type= : eg: student-leave or student-graduate }';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'to execute scheduled tasks daily';
    public $executionDate;
    public $status;
    public ?string $type;

    public StatusChangeableInterface $service;
    public StatusChangeable $statusChangeable;
    public PendingStudentEmployeeStatusChangeRepository $pendingStudentEmployeeStatusChangeRepository;

    public function __construct(PendingStudentEmployeeStatusChangeRepository $pendingStudentEmployeeStatusChangeRepository) {
        parent::__construct();
        $this->pendingStudentEmployeeStatusChangeRepository = $pendingStudentEmployeeStatusChangeRepository;
    }

    /**
     * Execute the console command.
     * @throws RepositoryException
     */
    public function handle()
    {
        $this->executionDate = $this->option('execution_date') ?? now()->toDateString();
        $this->status = PendingStudentEmployeeStatusChange::STATUS_PENDING;

        $query = PendingStudentEmployeeStatusChange::with('statusChangeable')
            ->where('execution_date', '<=', $this->executionDate)
            ->where('status', $this->status);

        if($this->type = $this->option('type')){
            $query->where('type', $this->type);
        }

        $pending_student_employee_status_changes = $query->get();

        $success_pending_student_employee_status_change_ids = [];

        foreach($pending_student_employee_status_changes as $pending_student_employee_status_change){

            try {
                $this->statusChangeable = $pending_student_employee_status_change->statusChangeable;
                $this->service = StatusChangeableFactory::getStatusChangeableService($this->statusChangeable);

                match ($pending_student_employee_status_change->type) {
                    PendingStudentEmployeeStatusChange::TYPE_LEAVE => $this->leaveOrResign($pending_student_employee_status_change),
                    PendingStudentEmployeeStatusChange::TYPE_RETURN => $this->returnOrReinstate($pending_student_employee_status_change),
                    PendingStudentEmployeeStatusChange::TYPE_TRANSFER => $this->transfer($pending_student_employee_status_change),
                    default => throw new \Exception("Unsupported type. ID: {$pending_student_employee_status_change->id} - type: {$pending_student_employee_status_change->type}"),
                };

                $success_pending_student_employee_status_change_ids[] = $pending_student_employee_status_change->id;

            } catch(\Throwable $e) {
                $this->pendingStudentEmployeeStatusChangeRepository->update($pending_student_employee_status_change, ['status' => PendingStudentEmployeeStatusChange::STATUS_ERROR, 'error_message' => $e->getMessage()]);
            }
        }

        if ( count($success_pending_student_employee_status_change_ids) > 0 ) {
            $this->pendingStudentEmployeeStatusChangeRepository->batchUpdateByIds($success_pending_student_employee_status_change_ids, ['status' => PendingStudentEmployeeStatusChange::STATUS_SUCCESS]);
        }

        return true;

    }

    public function leaveOrResign(PendingStudentEmployeeStatusChange $pending_student_employee_status_change){

        $effective_date_string = $pending_student_employee_status_change->execution_date;
        $data = $pending_student_employee_status_change->data;

        $this->service->setStatusChangeable($this->statusChangeable)
            ->validateLeaveOrResign($effective_date_string);

        if ( isset($data['leave_reason_id']) ) {
            $leave_reason = LeaveReason::findOrFail($data['leave_reason_id']);
            $this->service->setLeaveReason($leave_reason);
        }
        if ( isset($data['remarks']) ) {
            $this->service->setRemarks($data['remarks']);
        }

        $this->service
            ->leaveOrResign($effective_date_string);

    }

    public function returnOrReinstate(PendingStudentEmployeeStatusChange $pending_student_employee_status_change)
    {
        $effective_date_string = $pending_student_employee_status_change->execution_date;
        $data = $pending_student_employee_status_change->data;

        if(isset($data['semester_setting_id'])){
            $semester_setting = SemesterSetting::findOrFail($data['semester_setting_id']);
            $this->service->setSemesterSetting($semester_setting);
        }
        if(isset($data['semester_class_id'])){
            $semester_class = SemesterClass::findOrFail($data['semester_class_id']);
            $this->service->setSemesterClass($semester_class);
        }

        return $this->service->setStatusChangeable($this->statusChangeable)
            ->validateReturnOrReinstate()
            ->returnOrReinstate($effective_date_string);
    }

    public function transfer(PendingStudentEmployeeStatusChange $pending_student_employee_status_change)
    {
        $effective_date_string = $pending_student_employee_status_change->execution_date;
        $data = $pending_student_employee_status_change->data;

        if(isset($data['job_title_id'])){
            $new_job_title = EmployeeJobTitle::findOrFail($data['job_title_id']);
            $this->service->setNewJobTitle($new_job_title);
        }

        $this->service->setStatusChangeable($this->statusChangeable)
            ->validateTransfer($effective_date_string)
            ->transfer($effective_date_string);
    }
}
