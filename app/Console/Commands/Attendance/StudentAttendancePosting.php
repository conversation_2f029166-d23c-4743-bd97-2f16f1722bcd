<?php

namespace App\Console\Commands\Attendance;

use App\Enums\AttendanceCheckInStatus;
use App\Enums\AttendanceCheckOutStatus;
use App\Enums\AttendanceStatus;
use App\Enums\LeaveApplicationStatus;
use App\Enums\PeriodAttendanceStatus;
use App\Helpers\ConfigHelper;
use App\Helpers\LogHelper;
use App\Models\Attendance;
use App\Models\AttendanceInput;
use App\Models\Config;
use App\Models\Employee;
use App\Models\LeaveApplication;
use App\Models\Student;
use App\Repositories\AttendanceArchiveRepository;
use App\Repositories\AttendancePeriodOverrideRepository;
use App\Repositories\AttendanceRepository;
use App\Repositories\CalendarTargetRepository;
use App\Repositories\LeaveApplicationPeriodRepository;
use App\Repositories\LeaveApplicationTypeRepository;
use App\Repositories\PeriodAttendanceRepository;
use App\Services\Timetable\AttendancePeriodOverrideService;
use App\Services\Timetable\StudentTimetableService;
use Illuminate\Console\Command;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Psr\Log\LogLevel;

class StudentAttendancePosting extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'posting:student-attendance-input { --date= : eg: 2024-12-31 } { --student_ids= : eg: 1,2,3 } { --process-check-out }';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Attendance input posting';

    protected $date = null;
    protected Collection $students;
    protected array $studentIdsThatRequiredAttendance;
    public int $interval;
    protected bool $isProcessCheckOut;
    protected Employee $systemEmployee;

    const int FIRST_CLASS_AFFAIR_PERIOD = 1; // period 1 is first 班务 of the day
    const int SECOND_CLASS_AFFAIR_PERIOD = 15; // period 15 is second 班务 of the day
    const string LEAVE_TYPE_FORGOT_TAP_CARD_ON_CHECK_OUT = '12'; // code 12 is 早退/无签离

    public function __construct(
        protected AttendanceRepository $attendanceRepository,
        protected AttendanceArchiveRepository $attendanceArchiveRepository,
        protected CalendarTargetRepository $calendarTargetRepository,
        protected StudentTimetableService $studentTimetableService,
        protected PeriodAttendanceRepository $periodAttendanceRepository,
        protected LeaveApplicationPeriodRepository $leaveApplicationPeriodRepository,
        protected LeaveApplicationTypeRepository $leaveApplicationTypeRepository,
        protected AttendancePeriodOverrideRepository $attendancePeriodOverrideRepository,
        protected AttendancePeriodOverrideService $attendancePeriodOverrideService,
    ) {
        parent::__construct();
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->setInterval();
        $start_time = microtime(true);
        try {
            $this->date = $this->option('date') ?? Carbon::now(config('school.timezone'))->toDateString();
            $this->isProcessCheckOut = (bool) $this->option('process-check-out') ?? false;
            $this->systemEmployee = Employee::where('employee_number', Employee::SYSTEM_ID)->firstOrFail();

            $student_ids = [];
            if ($this->option('student_ids')) {
                $student_ids = explode(',', $this->option('student_ids'));
                $student_ids = array_map('trim', $student_ids);
            }

            $this->students = Student::getActiveStudents($student_ids, [], ['id'])->get();

            if (count($this->students) == 0) {
                return true;
            }

            $students_school_is_attendance_required = $this->calendarTargetRepository->getStudentsSchoolDayOrNonSchoolDayByDate($this->date, $this->students->pluck('id')->toArray());
            $this->studentIdsThatRequiredAttendance = [];
            foreach ($this->students as $student) {
                // only post for students with calendar + is_attendance_required = true
                if (isset($students_school_is_attendance_required[$student->id]) && $students_school_is_attendance_required[$student->id] == true) {
                    $this->studentIdsThatRequiredAttendance[] = $student->id;
                }
            }

            $student_approved_leave_application_second_class_affairs_period = $this->getStudentApprovedLeaveApplicationByPeriod();
            $leave_application_type_check_out_forgot_tap_card = $this->getLeaveApplicationTypeForCheckOutForgotTapCard();

            $student_attendances = $this->getStudentAttendances();

            $student_attendance_inputs = $this->getStudentAttendanceInputs();

            $existing_period_attendances = $this->periodAttendanceRepository->getAll([
                'date' => $this->date,
                'student_id' => $this->studentIdsThatRequiredAttendance,
            ])
                ->groupBy(['student_id', 'period'])
                ->all();

            $version = Carbon::now()->toDateTimeString();

            foreach (array_chunk($this->studentIdsThatRequiredAttendance, 100) as $student_ids) {
                $attendances_to_be_deleted = [];
                $attendances_to_be_archived = [];
                $new_attendances_to_be_created = [];
                $period_attendances_to_be_updated_by_id = [];
                $period_attendances_to_be_created = [];
                $leave_application_periods_to_be_created = [];

                $student_attendance_periods = $this->studentTimetableService
                    ->setStudentIds($student_ids)
                    ->setSimpleOutput(false)
                    ->setIsPosting(true)
                    ->getAttendancePeriods($this->date, $this->date)
                    ->where('userable_type', Student::class)
                    ->whereNotNull('attendance_from')
                    ->groupBy('userable_id')
                    ->all();
                $student_timetable_list_by_date_and_student = $this->studentTimetableService->getTimetableListByDateAndStudent();

                foreach ($student_ids as $student_id) {
                    $check_in_datetime = null;
                    $check_in_status = null;
                    $check_in_remarks = null;
                    $check_in_overridable_type = null;
                    $check_in_overridable_id = null;
                    $attendance_from = null;
                    $attendance_from_datetime = null;
                    $check_out_datetime = null;
                    $check_out_status = null;
                    $check_out_remarks = null;
                    $check_out_overridable_type = null;
                    $check_out_overridable_id = null;
                    $attendance_to = null;
                    $attendance_to_datetime = null;
                    $status = AttendanceStatus::ABSENT->value;
                    $card_id = null;
                    $leave_application_id = null;
                    $is_error = false;
                    $error_message = null;

                    // student attendance periods
                    $student_attendance_period = null;
                    if (isset($student_attendance_periods[$student_id])) {
                        $student_attendance_period = $student_attendance_periods[$student_id]->first();
                    } else {
                        $is_error = true;
                        $error_message = 'Student timetable not found.';
                    }

                    // student attendance inputs
                    $attendance_inputs = $student_attendance_inputs[$student_id] ?? [];

                    $earliest_record = null;
                    $latest_record = null;

                    // get earliest and latest attendance inputs
                    if (count($attendance_inputs) > 0) {
                        $card_id = $attendance_inputs->firstWhere('card_id', '!=', null)?->card_id;
                        $status = AttendanceStatus::PRESENT->value; // if got tap card, mark as present

                        $earliest_latest_record = $this->getEarliestAndLatestRecord($attendance_inputs);

                        $earliest_record = $earliest_latest_record['earliest'];
                        $latest_record = $earliest_latest_record['latest'];

                        if (isset($student_attendance_period)) {

                            $check_in_overridable = $student_attendance_period['_attendance_from_overwritten_by'] ?? null;
                            $check_in_overridable_type = isset($check_in_overridable) ? get_class($check_in_overridable) : null;
                            $check_in_overridable_id = $check_in_overridable->id ?? null;

                            $check_out_overridable = $student_attendance_period['_attendance_to_overwritten_by'] ?? null;
                            $check_out_overridable_type = isset($check_out_overridable) ? get_class($check_out_overridable) : null;
                            $check_out_overridable_id = $check_out_overridable->id ?? null;

                            $attendance_from = $student_attendance_period['attendance_from'];
                            $attendance_to = $student_attendance_period['attendance_to'];

                            if ($earliest_record) {
                                $check_in_datetime = $earliest_record->record_datetime;
                                $check_in_status = $this->determineAttendanceCheckInStatus($attendance_from, $earliest_record->record_datetime);
                                $check_in_remarks = $earliest_record->remarks;
                            }

                            if ($latest_record) {
                                $check_out_datetime = $latest_record->record_datetime;
                                $check_out_status = $this->determineAttendanceCheckOutStatus($attendance_to, $latest_record->record_datetime);
                                $check_out_remarks = $latest_record->remarks;
                            }
                        }
                    }


                    // student attendance
                    $student_attendance = $student_attendances[$student_id] ?? null; // it won't be null, will create in getStudentAttendances() if attendance missing

                    if ($student_attendance !== null) {
                        $attendances_to_be_deleted[] = $student_attendance->id;
                        $student_attendance = $student_attendance->toArray();
                        unset($student_attendance['id']);
                        $student_attendance['version'] = $version;
                        $attendances_to_be_archived[] = $student_attendance;
                    }

                    if ($attendance_from !== null) {
                        $attendance_from_datetime = Carbon::parse($this->date . " " . $attendance_from, config('school.timezone'))->tz('UTC');
                    }
                    if ($attendance_to !== null) {
                        $attendance_to_datetime = Carbon::parse($this->date . " " . $attendance_to, config('school.timezone'))->tz('UTC');
                    }

                    $new_attendances_to_be_created[] = [
                        'attendance_recordable_type' => Student::class,
                        'attendance_recordable_id' => $student_id,
                        'date' => $this->date,
                        'check_in_datetime' => $check_in_datetime,
                        'check_in_status' => $check_in_status,
                        'check_in_remarks' => $check_in_remarks,
                        'check_in_overridable_type' => $check_in_overridable_type,
                        'check_in_overridable_id' => $check_in_overridable_id,
                        'attendance_from' => $attendance_from_datetime,
                        'check_out_datetime' => $check_out_datetime,
                        'check_out_status' => $check_out_status,
                        'check_out_remarks' => $check_out_remarks,
                        'check_out_overridable_type' => $check_out_overridable_type,
                        'check_out_overridable_id' => $check_out_overridable_id,
                        'attendance_to' => $attendance_to_datetime,
                        'status' => $status,
                        'card_id' => $card_id,
                        'leave_application_id' => $leave_application_id,
                        'is_error' => $is_error,
                        'error_message' => $error_message,
                        'created_at' => now(),
                        'updated_at' => now(),
                    ];

                    $student_timetable_timeslots = $student_timetable_list_by_date_and_student[$this->date][$student_id] ?? null;

                    // if school attendance is present and student has timetable
                    if ($status == AttendanceStatus::PRESENT->value && $student_timetable_timeslots) {
                        // TODO: !!!Change this to use event handler, as only PINHWA uses this logic!!!
                        // run this first before looping through each timetable timeslots
                        if (
                            !$this->isProcessCheckOut &&
                            isset($existing_period_attendances[$student_id][self::FIRST_CLASS_AFFAIR_PERIOD]) &&
                            in_array($check_in_status, [
                                AttendanceCheckInStatus::LATE->value,
                                AttendanceCheckInStatus::ON_TIME->value,
                            ])
                        ) {
                            $period_attendance = $existing_period_attendances[$student_id][self::FIRST_CLASS_AFFAIR_PERIOD]->first();

                            $status = match ($check_in_status) {
                                AttendanceCheckInStatus::LATE->value => PeriodAttendanceStatus::LATE->value,
                                AttendanceCheckInStatus::ON_TIME->value => PeriodAttendanceStatus::PRESENT->value,
                            };

                            if ($period_attendance->status !== $status) {
                                $period_attendance->update([
                                    'status' => $status,
                                    'updated_by_employee_id' => $this->systemEmployee->id,
                                    'attendance_taken_at' => now(),
                                ]);
                            }
                        }

                        // period attendance
                        foreach ($student_timetable_timeslots as $data) {
                            $existing_period_attendance = isset($existing_period_attendances[$student_id][$data['period']]) ? $existing_period_attendances[$student_id][$data['period']]->first() : null;

                            if ($data['inherit_from_school_attendance'] == true && $data['is_empty'] == false && $data['class_attendance_required'] == true) {

                                if (isset($existing_period_attendance)) {
                                    // skip if class attendance already taken
                                    if ($existing_period_attendance->updated_by_employee_id !== null) {
                                        continue;
                                    }

                                    $period_attendances_to_be_updated_by_id[$existing_period_attendance->id] = [
                                        'student_id' => $student_id,
                                        'date' => $this->date,
                                        'timeslot_type' => $data['timeslot_type'],
                                        'timeslot_id' => $data['timeslot_id'],
                                        'updated_by_employee_id' => null,
                                        'attendance_taken_at' => null,
                                        'status' => PeriodAttendanceStatus::PRESENT->value,
                                        'leave_application_id' => $existing_period_attendance->leave_application_id,
                                        'period' => $data['period'],
                                        'created_at' => $existing_period_attendance->created_at,
                                        'updated_at' => now(),
                                        'has_mark_deduction' => $data['has_mark_deduction'],
                                    ];
                                } else {
                                    $period_attendances_to_be_created[] = [
                                        'student_id' => $student_id,
                                        'date' => $this->date,
                                        'timeslot_type' => $data['timeslot_type'],
                                        'timeslot_id' => $data['timeslot_id'],
                                        'updated_by_employee_id' => null,
                                        'attendance_taken_at' => null,
                                        'status' => PeriodAttendanceStatus::PRESENT->value,
                                        'leave_application_id' => null,
                                        'period' => $data['period'],
                                        'created_at' => now(),
                                        'updated_at' => now(),
                                        'has_mark_deduction' => $data['has_mark_deduction'],
                                    ];
                                }
                            }
                        }

                        // TODO: !!!Change this to use event handler, as only PINHWA uses this logic!!!
                        // create leave application for student that forgot to check out
                        // run when
                        // 1. is second attendance posting of the day
                        // 2. check_out_status = null (only 1 attendance input)
                        // 3. no approved leave in period 15 (second 班务)
                        // 4. 早退/无签离 leave application type exists
                        // Alvin : put inside DB::transaction after attendance-module-enhancements branch is merged into master
                        if (
                            $this->isProcessCheckOut &&
                            $check_out_status == null &&
                            !isset($student_approved_leave_application_second_class_affairs_period[$student_id]) &&
                            isset($leave_application_type_check_out_forgot_tap_card)
                        ) {
                            $leave_application = LeaveApplication::create([
                                'leave_applicable_type' => Student::class,
                                'leave_applicable_id' => $student_id,
                                'status' => LeaveApplicationStatus::APPROVED,
                                'leave_application_type_id' => $leave_application_type_check_out_forgot_tap_card->id,
                                'reason' => $leave_application_type_check_out_forgot_tap_card->getTranslation('name', 'en'),
                                'remarks' => null,
                                'is_present' => $leave_application_type_check_out_forgot_tap_card->is_present,
                                'is_full_day' => false,
                                'average_point_deduction' => $leave_application_type_check_out_forgot_tap_card->average_point_deduction,
                                'conduct_point_deduction' => $leave_application_type_check_out_forgot_tap_card->conduct_point_deduction,
                            ]);
                            $leave_application_periods_to_be_created[] = [
                                'leave_application_id' => $leave_application->id,
                                'date' => $this->date,
                                'period' => self::SECOND_CLASS_AFFAIR_PERIOD,
                                'created_at' => now(),
                                'updated_at' => now(),
                            ];
                        }
                    }
                }

                DB::transaction(function () use (
                    $attendances_to_be_deleted,
                    $new_attendances_to_be_created,
                    $attendances_to_be_archived,
                    $period_attendances_to_be_updated_by_id,
                    $period_attendances_to_be_created,
                    $leave_application_periods_to_be_created
                ) {
                    $this->attendanceRepository->batchDeleteByIds($attendances_to_be_deleted);

                    foreach (array_chunk($new_attendances_to_be_created, 200) as $data) {
                        $this->attendanceRepository->insert($data);
                    }
                    foreach (array_chunk($attendances_to_be_archived, 200) as $data) {
                        $this->attendanceArchiveRepository->insert($data);
                    }
                    foreach ($period_attendances_to_be_updated_by_id as $id => $period_attendance_data) {
                        $this->periodAttendanceRepository->updateById($id, $period_attendance_data);
                    }
                    foreach (array_chunk($period_attendances_to_be_created, 200) as $data) {
                        $this->periodAttendanceRepository->insert($data);
                    }
                    foreach (array_chunk($leave_application_periods_to_be_created, 200) as $data) {
                        $this->leaveApplicationPeriodRepository->insert($data);
                    }
                });
            }
            $this->info('Time taken: ' . number_format(microtime(true) - $start_time, 3) . 's');
            $this->info('memory usage: ' . number_format(memory_get_peak_usage() / 1024 / 1024, 2) . 'MB');

            $this->info("Completed!");
            return true;

        } catch (\Exception $e) {
            LogHelper::write('Error posting attendance: ' . $e->getMessage() . $e->getTraceAsString(), [], LogLevel::ERROR, LogHelper::LOG_NAME_ATTENDANCE);
            $this->error("ERROR! " . $e->getMessage());
            throw $e;
        }

    }

    public function getEarliestAndLatestRecord($attendance_inputs)
    {

        $sorted = $attendance_inputs->sortBy('record_datetime');

        if (count($attendance_inputs) == 1) {
            $earliest_record = $sorted->first(); // check in
            $latest_record = null;
        } else {
            $earliest_record = $sorted->first(); // check in
            $latest_record = $sorted
                ->where('record_datetime', '>=', Carbon::parse($earliest_record['record_datetime'])->addSeconds($this->interval))
                ->last(); // check-out
        }

        return [
            'earliest' => $earliest_record,
            'latest' => $latest_record,
        ];
    }

    public function getLeaveApplicationTypeForCheckOutForgotTapCard()
    {
        return $this->leaveApplicationTypeRepository->first([
            'code' => self::LEAVE_TYPE_FORGOT_TAP_CARD_ON_CHECK_OUT,
        ], false);
    }

    public function getStudentApprovedLeaveApplicationByPeriod()
    {
        return $this->leaveApplicationPeriodRepository->getAll([
            'period' => self::SECOND_CLASS_AFFAIR_PERIOD,
            'leave_applicable_type' => Student::class,
            'leave_applicable_id' => $this->studentIdsThatRequiredAttendance,
            'leave_application_status' => LeaveApplicationStatus::APPROVED->value,
            'leave_application_date' => $this->date,
        ])
            ->keyBy('leaveApplication.leave_applicable_id')
            ->all();
    }

    public function getStudentAttendances()
    {
        $attendances = Attendance::where('attendance_recordable_type', Student::class)
            ->whereIn('attendance_recordable_id', $this->studentIdsThatRequiredAttendance)
            ->where('date', $this->date)
            ->get()
            ->keyBy('attendance_recordable_id')
            ->all();

        $student_ids_with_attendance = array_keys($attendances);

        $students_without_attendance = array_diff($this->studentIdsThatRequiredAttendance, $student_ids_with_attendance);

        // patch missing attendances
        if (count($students_without_attendance) > 0) {
            $attendance_data = collect($students_without_attendance)->map(function ($student_id) {
                return [
                    'attendance_recordable_type' => Student::class,
                    'attendance_recordable_id' => $student_id,
                    'date' => $this->date,
                    'check_in_datetime' => null,
                    'check_in_status' => null,
                    'check_in_remarks' => null,
                    'check_out_datetime' => null,
                    'check_out_status' => null,
                    'check_out_remarks' => null,
                    'status' => AttendanceStatus::ABSENT,
                    'created_at' => now(),
                    'updated_at' => now(),
                ];
            })->toArray();

            Attendance::insert($attendance_data);

            $missing_attendances = Attendance::where('attendance_recordable_type', Student::class)
                ->whereIn('attendance_recordable_id', $students_without_attendance)
                ->where('date', $this->date)
                ->get()
                ->keyBy('attendance_recordable_id')
                ->all();

            $attendances = array_replace($attendances, $missing_attendances);
        }

        return $attendances;
    }

    public function getStudentAttendanceInputs()
    {
        return AttendanceInput::where('date', $this->date)
            ->where('attendance_recordable_type', Student::class)
            ->whereIn('attendance_recordable_id', $this->studentIdsThatRequiredAttendance)
            ->get()
            ->groupBy('attendance_recordable_id')
            ->all();
    }

    public function determineAttendanceCheckInStatus($student_attendance_from_time, $student_check_in_utc_datetime)
    {
        // convert both to school's timezone
        $student_attendance_from_datetime = Carbon::parse($this->date . " " . $student_attendance_from_time, config('school.timezone'));
        $student_check_in_datetime = Carbon::parse($student_check_in_utc_datetime, 'UTC')->tz(config('school.timezone'));

        if ($student_check_in_datetime->gt($student_attendance_from_datetime)) { // LATE = checkin time after late time
            return AttendanceCheckInStatus::LATE->value;
        } else {
            return AttendanceCheckInStatus::ON_TIME->value;
        }
    }

    public function determineAttendanceCheckOutStatus($student_attendance_to_time, $student_checkout_utc_datetime)
    {
        // convert both to school's timezone
        $student_attendance_to_datetime = Carbon::parse($this->date . " " . $student_attendance_to_time, config('school.timezone'));
        $student_checkout_datetime = Carbon::parse($student_checkout_utc_datetime, 'UTC')->tz(config('school.timezone'));

        if ($student_checkout_datetime->lt($student_attendance_to_datetime)) { // LEFT_EARLY = checkout time before on time
            return AttendanceCheckOutStatus::LEFT_EARLY->value;
        } else {
            return AttendanceCheckOutStatus::ON_TIME->value;
        }
    }

    public function setDate($date)
    {
        $this->date = $date;
        return $this;
    }

    public function setInterval()
    {
        $this->interval = ConfigHelper::get(Config::ATTENDANCE_TAP_CARD_INTERVAL_SECOND);
        return $this;
    }
}
