<?php

namespace App\Console\Commands;

use App\Models\Competition;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class PatchCompetitionDepartments extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'patch:competition-departments';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $competitions = Competition::query()
            ->where('created_at', '<=', '2025-04-20')
            ->get();

        $duplicated_competitions = Competition::query()
            ->where('created_at', '<=', '2025-04-20')
            ->select('name')
            ->groupBy('name')
            ->havingRaw('COUNT(*) > 1')
            ->get();

        // Remove duplicated departments
        $competitions = $competitions->whereNotIn('name', $duplicated_competitions->pluck('name'));

        $v1_competitions = $from_data = DB::connection('smsv1')->table('student_performance')
            ->select(
                DB::raw('item_m.item_id as item_id'),
                DB::raw('item_m.item_name as item_name'),
                DB::raw('item_m.item_code as item_code'),
                DB::raw('student_performance.date as date')
            )
            ->join('item_m', 'item_m.item_id', 'student_performance.item_id')
            ->where('student_performance.date', '>=', '2024-01-01')
            ->whereIn('item_m.item_name', $competitions->pluck('name'))
            ->groupBy('date', 'item_m.item_id', 'item_m.item_name', 'item_m.item_code')
            ->get()
            ->keyBy('item_name');

        foreach ($competitions as $competition) {
            if (isset($v1_competitions[$competition->name])) {
                // Check first character of item_code to determine department
                $department = match (substr($v1_competitions[$competition->name]->item_code, 0, 1)) {
                    '5' => 2,
                    '6' => 4,
                    '8' => 3,
                    default => 1,
                };

                $competition->update([
                    'department_id' => $department,
                ]);

                $this->info('Updated ' . $competition->name . ' to department ' . $department);
            }

        }
    }
}
