<?php

namespace App\Services;

use App\Interfaces\StatusChangeable;
use App\Interfaces\StatusChangeableInterface;
use App\Models\EmployeeJobTitle;
use App\Models\SemesterClass;
use App\Models\SemesterSetting;
use Carbon\Carbon;

abstract class StatusChangeableBaseService implements StatusChangeableInterface
{
    protected ?string $remarks;

    public function __construct(
    ) {
    }

    public abstract function leaveOrResign($effective_date): self;

    public abstract function validateLeaveOrResign($effective_date_string): self;

    public abstract function returnOrReinstate($return_date): self;

    public abstract function validateReturnOrReinstate(): self;

    public abstract function setStatusChangeable(StatusChangeable $status_changeable): self;

    public function transfer($effective_date_string): self
    {
        return $this;
    }

    public function validateTransfer($effective_date_string): self
    {
        return $this;
    }

    public function setNewJobTitle(EmployeeJobTitle $new_job_title): self
    {
        return $this;
    }

    public function setSemesterSetting(SemesterSetting $semester_setting): self
    {
        return $this;
    }

    public function setSemesterClass(SemesterClass $semester_class): self
    {
        return $this;
    }

    public function isFutureDate($date): bool
    {
        $school_timezone = config('school.timezone');
        return Carbon::parse($date, $school_timezone)->startOfDay()->gt(now()->setTimezone($school_timezone)->startOfDay());
    }

    public function getRemarks(): ?string
    {
        return $this->remarks;
    }

    public function setRemarks(?string $remarks): StatusChangeableBaseService
    {
        $this->remarks = $remarks;
        return $this;
    }

}
