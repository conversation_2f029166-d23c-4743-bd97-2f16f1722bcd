<?php

namespace App\Services;

use App\Enums\Day;
use App\Enums\LeaveApplicationStatus;
use App\Enums\PeriodAttendanceStatus;
use App\Helpers\ErrorCodeHelper;
use App\Models\PeriodAttendance;
use App\Models\Student;
use App\Repositories\PeriodAttendanceRepository;
use App\Services\Timetable\StudentTimetableService;
use Carbon\Carbon;
use Illuminate\Support\Collection;

class PeriodAttendanceService
{
    protected Collection $leaveApplications;
    protected Collection $studentLeaveApplications;
    protected string $leaveApplicationNewStatus;

    public function __construct(
        protected StudentTimetableService $studentTimetableService,
        protected PeriodAttendanceRepository $periodAttendanceRepository,
        protected StudentService $studentService,
    ) {
    }

    public function setLeaveapplications(Collection $leave_applications)
    {
        $this->leaveApplications = $leave_applications;
        return $this;
    }

    public function setLeaveApplicationNewStatus($status)
    {
        $this->leaveApplicationNewStatus = $status;
        return $this;
    }

    public function updatePeriodAttendanceByLeaveApplications()
    {
        $this->studentLeaveApplications = $this->leaveApplications->where('leave_applicable_type', Student::class);

        if (count($this->studentLeaveApplications) == 0) {
            return $this;
        }

        // dates
        $leave_application_dates = $this->leaveApplications->pluck('leaveApplicationPeriods')->flatten()->pluck('date')->unique();
        // days
        $days = array_unique($leave_application_dates->map(function ($date) {
            return strtoupper(Carbon::parse($date)->format('l'));
        })->toArray());

        // student ids
        $student_ids = $this->studentLeaveApplications->pluck('leave_applicable_id')->unique()->toArray();

        // student timetables
        $student_timetable_list_by_date_and_student = collect($this->studentTimetableService
            ->setDaysFilter($days)
            ->setStudentIds($student_ids)
            ->getTimetableWithTimeslotOverrideByDate($leave_application_dates->toArray()));

        // existing period attendances
        $student_existing_period_attendances = $this->periodAttendanceRepository->getAll([
            'student_id' => $student_ids,
            'date' => $leave_application_dates->toArray(),
        ])
            ->groupBy(['student_id', 'date', 'period'])
            ->all();

        $existing_period_attendance_to_be_deleted = [];
        $period_attendance_to_be_created = [];
        foreach ($this->studentLeaveApplications as $leave_application) {
            $leave_application_periods_group_by_date = $leave_application->leaveApplicationPeriods->groupBy('date');
            $student_id = $leave_application->leave_applicable_id;

            foreach ($leave_application_periods_group_by_date as $date => $leave_application_periods) {
                $existing_period_attendances = $student_existing_period_attendances[$student_id][$date] ?? collect([]);
                $student_timetable = $student_timetable_list_by_date_and_student[$date][$student_id] ?? null;
                // no timetable on that day, skip
                if ($student_timetable == null) {
                    continue;
                }
                $student_timetable = collect($student_timetable);

                foreach ($leave_application_periods as $leave_application_period) {
                    $period = $leave_application_period->period;
                    $timeslot = $student_timetable->where('period', $period)->first();
                    if ($timeslot == null) {
                        continue;
                    }
                    $timeslot_type = $timeslot['timeslot_type'];
                    $timeslot_id = $timeslot['timeslot_id'];
                    // has existing period attendance
                    if (isset($existing_period_attendances[$period])) {
                        $existing_period_attendance = $existing_period_attendances[$period]->first();
                        $existing_period_attendance_to_be_deleted[] = $existing_period_attendance->id;
                        $period_attendance_to_be_created[] = [
                            'id' => $existing_period_attendance->id,
                            'student_id' => $student_id,
                            'date' => $date,
                            'timeslot_type' => $timeslot_type,
                            'timeslot_id' => $timeslot_id,
                            'period' => $period,
                            'updated_by_employee_id' => $existing_period_attendance->updated_by_employee_id,
                            'attendance_taken_at' => $existing_period_attendance->attendance_taken_at,
                            'status' => $existing_period_attendance->status,
                            'leave_application_id' => $this->leaveApplicationNewStatus === LeaveApplicationStatus::APPROVED->value ? $leave_application->id : null,
                            'created_at' => $existing_period_attendance->created_at,
                            'updated_at' => now(),
                            'has_mark_deduction' => $existing_period_attendance->has_mark_deduction,
                        ];
                    }
                }
            }
        }
        $this->periodAttendanceRepository->batchDeleteByIds($existing_period_attendance_to_be_deleted);
        $this->periodAttendanceRepository->insert($period_attendance_to_be_created);
    }
}
