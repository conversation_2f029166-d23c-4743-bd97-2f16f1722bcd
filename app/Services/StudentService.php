<?php

namespace App\Services;

use App\Enums\ClassType;
use App\Enums\LibraryMemberType;
use App\Enums\StudentLeaveStatus;
use App\Helpers\ErrorCodeHelper;
use App\Interfaces\StatusChangeable;
use App\Interfaces\StatusChangeableInterface;
use App\Models\Config;
use App\Models\Guardian;
use App\Models\LeaveReason;
use App\Models\PendingStudentEmployeeStatusChange;
use App\Models\Role;
use App\Models\SemesterClass;
use App\Models\SemesterSetting;
use App\Models\Student;
use App\Repositories\GuardianStudentRepository;
use App\Repositories\PendingStudentEmployeeStatusChangeRepository;
use App\Repositories\StudentClassRepository;
use App\Repositories\StudentHistoryRepository;
use App\Repositories\StudentRepository;
use Carbon\Carbon;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;

class StudentService extends StatusChangeableBaseService implements StatusChangeableInterface
{
    const STUDENT_NUMBER_HOSTEL_PREFIX = 'H';
    private mixed $student;
    private array $api_request = [];
    private LeaveReason $leaveReason;
    private SemesterSetting $semesterSetting;
    private SemesterClass $semesterClass;
    private $schoolStudentService;

    protected $admissionGradeId;
    protected $admissionType;
    protected $admissionYear;
    protected $isHostel;

    public function __construct(
        protected StudentRepository $studentRepository,
        protected UserService $userService,
        protected StudentClassRepository $studentClassRepository,
        // Don't inject DocumentRunningNumberService directly, worry to have circular dependency
//        protected DocumentRunningNumberService $documentRunningNumberService,
        protected LibraryMemberService $libraryMemberService,
        protected ConfigService $configService,
        protected GuardianStudentRepository $guardianStudentRepository,
        protected GuardianService $guardianService,
        protected SemesterSettingService $semesterSettingService,
        protected StudentHistoryRepository $studentHistoryRepository,
        protected PendingStudentEmployeeStatusChangeRepository $pendingStudentEmployeeStatusChangeRepository,
    ) {
        // TODO: change to use factory if more than 1 school
        $this->schoolStudentService = app()->make(PinHwaStudentService::class);
    }

    public function getAllStudents($filters = [])
    {
        return $this->studentRepository->getAll($filters);
    }

    public function getAllPaginatedStudents($filters = []): LengthAwarePaginator
    {
        return $this->studentRepository->getAllPaginated($filters);
    }

    public function createStudent($data): ?Model
    {
        return DB::transaction(function () use ($data, &$created_student) {
            $this->setAdmissionYear($data['admission_year']);
            $this->setIsHostel($data['is_hostel']);
            $this->setAdmissionGradeId($data['admission_grade_id']);
            $this->setAdmissionType($data['admission_type']);

            $data['student_number'] = $this->generateStudentNumber();
            $data['email'] = $this->schoolStudentService
                ->setStudentNumber($data['student_number'])
                ->generateStudentEmail();

            $user = $this->userService->createUser($data, Student::class);

            $data['user_id'] = $user->id;

            $student = $this->studentRepository->create($data);

            $this->setStudent($student);

            $library_member = $this->libraryMemberService->createLibraryMember([
                'type' => LibraryMemberType::STUDENT->value,
                'student_id' => $student->id,
                'member_number' => $student->student_number,
                'borrow_limit' => $this->configService->getConfigValue(Config::LIBRARY_BORROW_LIMIT_STUDENT),
                'is_librarian' => false,
                'register_date' => now(),
                'valid_from' => now(),
                'valid_to' => now()->addYear(),
                'is_active' => true,
                'name' => $student->translations['name'],
                'gender' => $student->gender,
                'nric' => $student->nric,
                'passport_number' => $student->passport_number,
                'date_of_birth' => $student->date_of_birth,
                'race_id' => $student->race_id,
                'religion_id' => $student->religion_id,
                'address' => $student->address,
                'postcode' => $student->postal_code,
                'city' => $student->city,
                'state_id' => $student->state_id,
                'country_id' => $student->country_id,
                'phone_number' => $student->phone_number,
                'email' => $student->email,
            ]);

            if (!empty($data['photo']) && is_file($data['photo'])) {
                $student->replaceMedia('photo', $data['photo']);
                // copy over student photo to library member
                $media = $student->getFirstMedia('photo');
                $media->copy($library_member, 'photo');
            }

            //update guardian
            $this->saveGuardians($data['guardians']);

            return $student;
        });
    }

    public function setAdmissionYear($admission_year): self
    {
        $this->admissionYear = $admission_year;

        return $this;
    }

    public function setIsHostel($is_hostel): self
    {
        $this->isHostel = $is_hostel;

        return $this;
    }

    public function setAdmissionGradeId($admission_grade_id): self
    {
        $this->admissionGradeId = $admission_grade_id;
        return $this;
    }

    public function setAdmissionType($admission_type): self
    {
        $this->admissionType = $admission_type;
        return $this;
    }

    public function getAdmissionYear(): int
    {
        return $this->admissionYear;
    }

    public function getIsHostel(): bool
    {
        return $this->isHostel;
    }

    public function getAdmissionGradeId(): int
    {
        return $this->admissionGradeId;
    }

    public function getAdmissionType(): string
    {
        return $this->admissionType;
    }

    public function generateStudentNumber(): string
    {
        $student_number = $this->schoolStudentService
            ->setAdmissionYear($this->getAdmissionYear())
            ->setIsHostel($this->getIsHostel())
            ->setGradeId($this->getAdmissionGradeId())
            ->setAdmissionType($this->getAdmissionType())
            ->generateStudentNumber();

        return $student_number;
    }

    public function setStudent($student): self
    {
        $this->student = $student;

        return $this;
    }

    public function saveGuardians($guardians): void
    {
        foreach ($guardians as $guardian_data) {
            $guardian_user = null;

            $with_user_account = Arr::get($guardian_data, 'with_user_account');

            $guardian_id = Arr::get($guardian_data, 'id');

            $guardian_user = $with_user_account ? $this->userService->firstUserByPhoneNumber($guardian_data['phone_number']) : null;

            if ($guardian_id) {
                $guardian = $this->guardianService->findGuardianById($guardian_id);

                if ($with_user_account && !$guardian->user_id) {
                    if (is_null($guardian_user)) {
                        $guardian_user = $this->userService->createUser($guardian_data, Guardian::class);
                    }
                    $guardian_data['user_id'] = $guardian_user?->id;
                }

                $guardian = $this->guardianService->updateGuardian($guardian, $guardian_data);

                if (!$with_user_account) {
                    $this->guardianService->unlinkUser($guardian);
                }
            } else {
                if ($with_user_account && is_null($guardian_user)) {
                    $guardian_user = $this->userService->createUser($guardian_data, Guardian::class);
                }
                $guardian_data['user_id'] = $guardian_user?->id;

                $guardian = $this->guardianService->createGuardian($guardian_data);
            }

            $this->linkGuardian($guardian, $guardian_data);
        }
    }

    public function linkGuardian(Guardian|Model $guardian, array $guardian_data): void
    {
        $this->guardianStudentRepository->create([
            'guardian_id' => $guardian->id,
            'type' => $guardian_data['type'],
            'studenable_type' => Student::class,
            'studenable_id' => $this->student->id,
            'is_primary' => $guardian_data['is_primary'] ?? false,
            'relation_to_student' => $guardian_data['relation_to_student'] ?? null,
            'is_direct_dependant' => $guardian_data['is_direct_dependant'] ?? true,
        ]);
    }

    public function updateStudent(Student $student, $data): ?Model
    {
        $this->setStudent($student);

        return DB::transaction(function () use ($student, $data) {

            $data['student_number'] = $this->schoolStudentService
                ->setIsHostel($data['is_hostel'])
                ->setStudentNumber($this->student->student_number)
                ->reformatStudentNumberByStudentHostel();

            $student = $this->studentRepository->update($this->student, $data);

            $this->setStudent($student);

            if (!empty($data['photo']) && is_file($data['photo'])) {
                $student->replaceMedia('photo', $data['photo']);
            }

            //Update guardian
            $this->removeLinkGuardians()
                ->saveGuardians($data['guardians']);

            return $student;
        });
    }

    public function updateStudentNonDirectGuardian($guardians): Student
    {
        // Only can update
        foreach ($guardians as $index => $guardian) {
            $guardians[$index]['is_direct_dependant'] = false;
            $guardians[$index]['is_primary'] = false;
        }

        $this->removeNonDirectGuardians()
            ->saveGuardians($guardians);

        return $this->student->refresh();
    }

    public function deleteStudent(Student $student): bool
    {
        $this->setStudent($student)
            ->checkStudentCanBeDeleted()
            ->removeLinkGuardians();

        return $this->studentRepository->delete($student);
    }

    // TODO: To refactor to use $model->canBeDeleted() instead of this method
    public function checkStudentCanBeDeleted(): static
    {
        if ($this->student->guardians->count() > 0) {
            ErrorCodeHelper::throwError(ErrorCodeHelper::GUARDIAN_ERROR, 7004);
        }

        if (isset($this->student->firstActiveHostelBedAssignment)) {
            ErrorCodeHelper::throwError(ErrorCodeHelper::STUDENT_ERROR, 29001);
        }

        return $this;
    }

    public function regenerateStudentNumber(Student $student): string
    {
        return DB::transaction(function () use ($student) {
            $student_number = $this->setAdmissionYear($student->admission_year)
                ->setIsHostel($student->is_hostel)
                ->setAdmissionGradeId($student->admission_grade_id)
                ->setAdmissionType($student->admission_type->value)
                ->generateStudentNumber();

            $student->update(['student_number' => $student_number]);

            return $student_number;
        });
    }

    public function getFirstByStudentNumberOrCardNumber($number): ?Student
    {
        $student = $this->studentRepository->getFirstByStudentNumberOrCardNumber($number);

        if (!$student) {
            throw new ModelNotFoundException();
        }

        return $student;
    }

    public function getCurrentSemesterStudentPrimaryClassId($id)
    {
        $semester_settings = $this->semesterSettingService->getCurrentSemesterSettings();

        $semester_setting_ids = $semester_settings->pluck('id')->all();

        $student_class = $this->studentClassRepository->first([
            'semester_setting_ids' => $semester_setting_ids,
            'student_id' => $id,
            'class_type' => ClassType::PRIMARY->value,
            'is_active' => true
        ], false);

        return $student_class?->id;
    }

    protected function removeLinkGuardians(): static
    {
        $this->guardianStudentRepository->deleteByStudenable($this->student);

        return $this;
    }

    protected function removeNonDirectGuardians(): static
    {
        $this->guardianStudentRepository->deleteByStudenable($this->student, true);

        return $this;
    }

    public function getApiRequest(): array
    {
        return $this->api_request;
    }

    public function setApiRequest($api_request): self
    {
        $this->api_request = $api_request;
        return $this;
    }

    public function markStudentLeftAt($effective_date_string)
    {
        $this->validateLeaveOrResign($effective_date_string);

        $effective_date_string = Carbon::parse($effective_date_string)->toDateString();

        if ($this->isFutureDate($effective_date_string)) {
            // create or update pending student employee status change, and cronjob will execute it on the effective date
            $this->pendingStudentEmployeeStatusChangeRepository->updateOrCreate(
                [
                    'type' => PendingStudentEmployeeStatusChange::TYPE_LEAVE,
                    'status' => PendingStudentEmployeeStatusChange::STATUS_PENDING,
                    'status_changeable_type' => Student::class,
                    'status_changeable_id' => $this->student->id,
                ],
                [
                    'execution_date' => $effective_date_string,
                    'data' => $this->getApiRequest(),
                ]
            );
        } else {
            $this->leaveOrResign($effective_date_string);
        }
    }

    public function transferRemainingBalanceToPrimaryGuardian()
    {
        $user = $this->student->user;
        $student_wallets = $user->wallets;

        $student_guardian = $this->student->guardians()
            ->whereHas('user.wallets') // only fetch guardian with wallet
            ->orderByDesc('guardian_student.is_primary') // prioritize primary guardian
            ->first();

        if (!isset($student_guardian) && $student_wallets->sum('balance') > 0) {
            ErrorCodeHelper::throwError(ErrorCodeHelper::STUDENT_ERROR, 29006);
        }

        $guardian_wallet = $student_guardian->user->wallets->first();

        if ($guardian_wallet === null) {
            ErrorCodeHelper::throwError(ErrorCodeHelper::WALLET_ERROR, 1008);
        }

        foreach ($student_wallets as $wallet) {
            if ($wallet->balance > 0) {
                $walletTransactionService = app()->make(WalletTransactionService::class);
                $payload = [
                    'receivers' => [
                        [
                            'userable_id' => $student_guardian->id,
                            'userable_type' => Guardian::class,
                            'wallet_id' => $guardian_wallet->id,
                            'amount' => $wallet->balance,
                            'description' => 'Transfer Student Remaining Balance to Guardian',
                        ]
                    ]
                ];
                $walletTransactionService
                    ->setUser($user)
                    ->setUserableByTypeAndId(Student::class, $this->student->id)
                    ->setWalletById($wallet->id)
                    ->transferBalance($payload);

            }
        }
    }

    public function leaveOrResign($effective_date): self
    {
        if ($this->leaveReason === null) {
            throw new \Exception('Please specify a leave reason.');
        }

        DB::transaction(function () use ($effective_date) {

            // create or update student history table
            $this->studentHistoryRepository->create(
                [
                    'student_id' => $this->student->id,
                    'start_date' => $this->student->join_date,
                    'event_type' => Student::LEAVE,
                    'end_date' => $effective_date,
                    'remarks' => $this->getRemarks(),
                    'leave_reason_id' => $this->leaveReason->id,
                ]
            );

            // update student class
            $this->markStudentExistingClassToInactive();

            // update student's leave_date
            $this->studentRepository->update($this->student, [
                'leave_date' => $effective_date,
                'is_active' => false,
                'leave_status' => StudentLeaveStatus::LEFT,
            ]);

            // update student role
            $user = $this->student->user;

            if ($user->hasStudentRole()) {
                $user->removeRole(Role::STUDENT);
            }
            $user->assignRole(Role::STUDENT_DEACTIVATED);

            // 25/2 update - student's remaining balance will no longer be transferred to primary guardian
            // $this->transferRemainingBalanceToPrimaryGuardian();

        });

        return $this;
    }

    public function markStudentReturnedAt($effective_date_string)
    {
        $this->validateReturnOrReinstate();

        $effective_date_string = Carbon::parse($effective_date_string)->toDateString();

        if ($this->isFutureDate($effective_date_string)) {
            // create or update pending student employee status change, and cronjob will execute it on the effective date
            $this->pendingStudentEmployeeStatusChangeRepository->updateOrCreate(
                [
                    'type' => PendingStudentEmployeeStatusChange::TYPE_RETURN,
                    'status' => PendingStudentEmployeeStatusChange::STATUS_PENDING,
                    'status_changeable_type' => Student::class,
                    'status_changeable_id' => $this->student->id,
                ],
                [
                    'execution_date' => $effective_date_string,
                    'data' => $this->getApiRequest(),
                ]
            );
        } else {
            $this->returnOrReinstate($effective_date_string);
        }
    }

    public function getSemesterSetting(): SemesterSetting
    {
        return $this->semesterSetting;
    }

    public function setSemesterSetting(SemesterSetting $semester_setting): self
    {
        $this->semesterSetting = $semester_setting;
        return $this;
    }

    public function getSemesterClass(): SemesterClass
    {
        return $this->semesterClass;
    }

    public function setSemesterClass(SemesterClass $semester_class): self
    {
        $this->semesterClass = $semester_class;
        return $this;
    }

    public function returnOrReinstate($return_date): self
    {
        DB::transaction(function () use ($return_date) {

            $this->studentRepository->update($this->student, [
                'is_active' => true,
                'leave_date' => null,
                'leave_status' => null,
                'join_date' => $return_date,
            ]);

            // update student role
            $user = $this->student->user;
            if ($user->hasStudentDeactivatedRole()) {
                $user->removeRole(Role::STUDENT_DEACTIVATED);
            }
            $user->assignRole(Role::STUDENT);

            // assign class to student
            $classService = app()->make(ClassService::class);
            $input = [
                'semester_setting_id' => $this->semesterSetting->id,
                'semester_class_id' => $this->semesterClass->id,
                'students' => [
                    [
                        'id' => $this->student->id,
                        'is_active' => true,
                        'class_enter_date' => $return_date,
                    ],
                ],
            ];
            $classService->assignClassToStudent($input);
        });

        return $this;
    }

    public function validateReturnOrReinstate(): self
    {
        if ($this->student->leave_status !== StudentLeaveStatus::LEFT) {
            ErrorCodeHelper::throwError(ErrorCodeHelper::STUDENT_ERROR, 29005);
        }

        if (!isset($this->semesterSetting)) {
            ErrorCodeHelper::throwError(ErrorCodeHelper::STUDENT_ERROR, 29007);
        }

        if (!isset($this->semesterClass)) {
            ErrorCodeHelper::throwError(ErrorCodeHelper::STUDENT_ERROR, 29008);
        }

        return $this;
    }


    public function validateLeaveOrResign($effective_date_string): self
    {
        if (!isset($this->student)) {
            ErrorCodeHelper::throwError(ErrorCodeHelper::STUDENT_ERROR, 29009);
        }
        if (!$this->student->is_active) {
            ErrorCodeHelper::throwError(ErrorCodeHelper::STUDENT_ERROR, 29002);
        }
        // Leave Date should be after Join Date (e.g. 1/10 - Join Date, 2/10 - Leave Date)
        if (Carbon::parse($effective_date_string)->isBefore(Carbon::parse($this->student->join_date))) {
            ErrorCodeHelper::throwError(ErrorCodeHelper::STUDENT_ERROR, 29003);
        }
        if ($this->student->hasExistingBedAssignment()) {
            ErrorCodeHelper::throwError(ErrorCodeHelper::STUDENT_ERROR, 29004);
        }
        return $this;
    }

    public function markStudentExistingClassToInactive(): self
    {
        $student_classes = $this->student->classes?->where('is_active', true) ?: [];

        foreach ($student_classes as $student_class) {
            $this->studentClassRepository->update($student_class->id, [
                'is_active' => false,
                'class_leave_date' => now(),
            ]);
        }

        return $this;
    }

    public function setStatusChangeable(StatusChangeable $student): self
    {
        $this->student = $student;
        return $this;
    }

    public function getLeaveReason(): LeaveReason
    {
        return $this->leaveReason;
    }

    public function setLeaveReason(LeaveReason $leaveReason): StudentService
    {
        $this->leaveReason = $leaveReason;
        return $this;
    }


}
