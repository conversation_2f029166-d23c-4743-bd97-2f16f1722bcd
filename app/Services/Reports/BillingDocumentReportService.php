<?php

namespace App\Services\Reports;

use App\Exports\BillingDocumentsByDailyCollectionExport;
use App\Factories\ExportAdapterFactory;
use App\Models\PaymentMethod;
use App\Repositories\BillingDocumentRepository;
use App\Services\BaseReportService;
use App\Services\ReportPrintService;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;

class BillingDocumentReportService extends BaseReportService
{
    const string ADVANCE = 'Advance';

    public function __construct(
        protected BillingDocumentRepository $billingDocumentRepository,
        protected ReportPrintService $reportPrintService,
    ) {
    }

    public function getDailyCollectionReportData(array $filters = []): mixed
    {
        $data = $this->billingDocumentRepository->getPaidInvoiceReportData($filters);

        $mapped_data = $this->mapForDailyCollectionReport($data, $filters['product_ids'] ?? []);

        $report_data = [
            'data' => $mapped_data['data'],
            'products' => $mapped_data['products'],
            'payment_methods' => $mapped_data['payment_methods'],
            'total_for_products' => $mapped_data['total_for_products'],
            'summary_amount_for_products' => $mapped_data['summary_amount_for_products'],
            'summary_amount_for_bank_charges' => $mapped_data['summary_amount_for_bank_charges'],
            'payment_date_from' => $filters['payment_date_from'],
            'payment_date_to' => $filters['payment_date_to'],
        ];

        // !!!IMPORTANT, Currently only support excel, please change this if got PDF format!!!
        $adapter = ExportAdapterFactory::getAdapterFor($this->getExportType());
        $adapter->setReportBuilder(new BillingDocumentsByDailyCollectionExport($report_data))
            ->setReportViewName($this->getReportViewName());

        $url = $this->reportPrintService->setExportFileAdapter($adapter)
            ->setFileName($this->getFileName())
            ->generate()
            ->upload()
            ->getFileUrl();

        return ['url' => $url];
    }

    public function mapForDailyCollectionReport(Collection $data, array $filtered_product_ids): array
    {
        $products = [];
        $payment_methods = [];

        $summary_amount_for_products = 0;
        $summary_amount_for_bank_charges = 0;
        $total_for_products = 0;

        $final_data = [];

        foreach ($data as $invoice) {
            $mapped_line_item = [
                'payment_date' => Carbon::parse($invoice->paid_at)->tz(config('school.timezone'))->format('Y-m-d'),
                'document_date' => $invoice->document_date,
                'reference_no' => $invoice->reference_no,
                'bill_to_name' => join(' ', array_values($invoice->billTo->getTranslations('name'))), // all names in all locales
                'bill_to_reference_number' => $invoice->bill_to_reference_number,
                'class_name' => $invoice->class_name,
                'grade_name' => $invoice->grade_name,
            ];


            /**
             * group each line items with its respective discount line items then sum up the amount
             */
            $line_items = $this->recalculateLineItems($invoice);


            /**
             * group each line_items by product, if there is same product id but different month, group it separately
             */
            $line_items_grouped = $this->groupLineItemsByProduct($line_items, $filtered_product_ids, $products);

            $total_amount_for_products = $line_items_grouped['total_amount_for_products'];

            /**
             * map payments
             */
            $is_fpx = false; // only fpx will have bank charges

            $payments = $this->mapPayments($invoice, $payment_methods, $is_fpx);

            /**
             * bank charges calculation
             */
            if ($is_fpx) {
                // TODO: Use helper function for this
                $bank_charges = $total_amount_for_products <= 250 ? bcadd('0.6', bcmul('0.001', $total_amount_for_products, 2), 2) : 0.85;
            } else {
                $bank_charges = 0;
            }

            /**
             * calculate total for products, summary amount for products and bank charges
             */
            $total_for_products = bcadd($total_for_products, $total_amount_for_products, 2);
            $summary_amount_for_bank_charges = bcadd($summary_amount_for_bank_charges, $bank_charges, 2);
            $summary_amount_for_products = bcadd($summary_amount_for_products, $invoice->amount_after_tax, 2);

            $mapped_line_item['products'] = $line_items_grouped['grouped'];
            $mapped_line_item['total_amount'] = $total_amount_for_products;
            $mapped_line_item['payments'] = $payments;
            $mapped_line_item['bank_charges'] = $bank_charges;

            $final_data[] = $mapped_line_item;
        }

        $products = $this->reorderProducts($products);

        return [
            'data' => $final_data,
            'products' => $products,
            'payment_methods' => $payment_methods,
            'total_for_products' => $total_for_products,
            'summary_amount_for_products' => $summary_amount_for_products,
            'summary_amount_for_bank_charges' => $summary_amount_for_bank_charges,
        ];
    }

    public function recalculateLineItems($invoice)
    {
        $line_items = [];
        $discount_line_items = []; // each line item can have multiple discount line items and need to be adjusted

        // Calculate discount
        foreach ($invoice->lineItems as $item) {
            $item_id = $item->id;

            // If no discount, add in to $line_items
            if (!$item->is_discount) {
                $line_items[$item_id] = $item->toArray();
            } elseif (!empty($item->discount_original_line_item_id)) { // add up all discount for each line item
                $original_item_id = $item->discount_original_line_item_id;

                // (Multiple discount scenario) If discount existed, we recalculate based on calculated discount
                if (isset($discount_line_items[$original_item_id])) {
                    $discount_line_items[$original_item_id]['amount_before_tax'] = bcadd($discount_line_items[$original_item_id]['amount_before_tax'], $item->amount_before_tax, 2);
                } else { // If discount doesn't exist, we put in the discount value
                    $discount_line_items[$original_item_id]['amount_before_tax'] = $item->amount_before_tax;
                }
            }
        }

        // Apply discount to original invoice line item, to get the NETT value
        foreach ($line_items as $item_id => &$item) {
            if (isset($discount_line_items[$item_id])) {
                $item['amount_before_tax'] = bcadd($item['amount_before_tax'], $discount_line_items[$item_id]['amount_before_tax'], 2);
            }
        }

        return $line_items;
    }

    public function groupLineItemsByProduct($line_items, $filtered_product_ids, &$products)
    {
        $locale = app()->getLocale();
        $has_product_filter = count($filtered_product_ids);

        $grouped = [];

        $total_amount_for_products = 0;

        foreach ($line_items as &$item) {

            // if filtered, skip advance and product not in filter
            if ($has_product_filter && !in_array($item['product_id'], $filtered_product_ids)) {
                continue;
            }

            // if item is advance
            if (isset($item['offset_billing_document_id'])) {
                $product_key = self::ADVANCE;

                // only 1 advance line item per invoice
                $grouped[$product_key] = $item['amount_before_tax'];
            } else {

                // get current line item month
                $billable_month = Carbon::parse($item['billable_item']['period']);

                // create unique product + month, this key will be used to sort the product
                $product_key = "{$billable_month->timestamp}///{$item['product']['name'][$locale]}";

                // this key will be used to display the product name
                $translated_key = "{$item['product']['name'][$locale]} ({$billable_month->format('M Y')})";

                // if 2 or more line items has same product and same month, add up the amount, else just take amount_before_tax
                if (isset($grouped[$translated_key])) {
                    $grouped[$translated_key] = bcadd($grouped[$translated_key], $item['amount_before_tax'], 2);
                } else {
                    $grouped[$translated_key] = $item['amount_before_tax'];
                }
            }

            // add up the amount for each line item
            $total_amount_for_products = bcadd($total_amount_for_products, $item['amount_before_tax'], 2);

            // keep track of all products total in all billing documents
            if (isset($products[$product_key])) {
                $products[$product_key] = bcadd($products[$product_key], $item['amount_before_tax'], 2);
            } else {
                $products[$product_key] = $item['amount_before_tax'];
            }
        }

        return [
            'grouped' => $grouped,
            'total_amount_for_products' => $total_amount_for_products,
        ];
    }

    public function mapPayments($invoice, &$payment_methods, &$is_fpx)
    {
        $payments = [];

        foreach ($invoice->payments as $payment) {
            $payment_code = $payment->paymentMethod->code;

            $payments[] = [
                'payment_method' => $payment_code,
                'payment_reference_no' => $payment->payment_reference_no,
                'bank' => $payment?->paymentSource?->bank?->name,
                'bank_swift_code' => $payment?->paymentSource?->bank?->swift_code,
            ];

            if ($payment_code === PaymentMethod::CODE_FPX) {
                $is_fpx = true;
            }

            // keep track of all payment methods total in all billing documents
            if (isset($payment_methods[$payment_code])) {
                $payment_methods[$payment_code] = bcadd($payment_methods[$payment_code], $payment?->paymentSource?->amount, 2);
            } else {
                $payment_methods[$payment_code] = $payment?->paymentSource?->amount;
            }
        }

        return $payments;
    }

    private function reorderProducts($products)
    {
        ksort($products);

        $formatted_products = [];

        foreach ($products as $key => $value) {
            if (str_contains($key, '///')) {
                [$timestamp, $product] = explode('///', $key);
                $month = Carbon::createFromTimestamp($timestamp)->format('M Y');
                $formatted_products["$product ($month)"] = $value;
            } else {
                $formatted_products[$key] = $value;
            }
        }

        // If Advance exists, move it to the end
        if (isset($formatted_products[self::ADVANCE])) {
            $advance = $formatted_products[self::ADVANCE];
            unset($formatted_products[self::ADVANCE]);
            $formatted_products[self::ADVANCE] = $advance;
        }

        return $formatted_products;
    }
}
