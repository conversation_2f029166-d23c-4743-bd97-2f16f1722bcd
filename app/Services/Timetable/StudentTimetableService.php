<?php

namespace App\Services\Timetable;

use App\Enums\CardStatus;
use App\Enums\CardType;
use App\Enums\Day;
use App\Enums\PeriodAttendanceStatus;
use App\Models\Contractor;
use App\Models\Employee;
use App\Models\Student;
use App\Models\StudentTimetable;
use App\Models\Timeslot;
use App\Models\TimeslotOverride;
use App\Repositories\AttendancePeriodOverrideRepository;
use App\Repositories\SchoolAttendancePeriodOverrideRepository;
use App\Repositories\TimeslotOverrideRepository;
use Carbon\Carbon;
use Carbon\CarbonPeriod;
use Illuminate\Support\Collection;

/**
 * This service allows you to get a timetable from student's POV.
 * Can be used to render a particular student's timetable.
 * Also lets you know what's the valid attendance period (from-to) for a student
 */
class StudentTimetableService
{
    protected Student $student;
    protected ?Day $dayFilter;
    protected array $daysFilter = [];
    protected array $studentIds = [];
    protected Collection $timetableListByDateAndStudent;
    protected bool $simpleOutput = false;
    protected bool $isPosting = false;

    public function getDefaultTimetable()
    {
        $query = StudentTimetable::query()
            ->orderBy('day', 'ASC')
            ->orderBy('attendance_from', 'ASC');

        if ($this->simpleOutput) {
            $query->selectRaw('student_id, timeslot_id, day, period, attendance_from, attendance_to, has_mark_deduction');
        }

        if (count($this->studentIds) > 0) {
            $query->whereIn('student_id', $this->studentIds);
        }
        if (isset($this->dayFilter)) {
            $query->where('day', $this->dayFilter);
        }
        if (count($this->daysFilter) > 0) {
            $query->whereIn('day', $this->daysFilter);
        }
        $data = $query->get();

        // employee query
        $employees_data = Employee::selectRaw('timeslot_teacher.timeslot_id AS timeslot_id, employees.name AS employee_name,
                employees.employee_number AS employee_number')
            ->join('timeslot_teacher', 'timeslot_teacher.employee_id', '=', 'employees.id')
            ->whereIn('timeslot_teacher.timeslot_id', $data->pluck('timeslot_id')->unique()->values())
            ->get();

        $employees_timeslot_mapping = $employees_data->mapToGroups(function ($item) {
            return [$item->timeslot_id => $item];
        });

        // remove timeslot without teacher
        $data = $data->filter(function ($d) use ($employees_timeslot_mapping) {
            return isset($employees_timeslot_mapping[$d->timeslot_id]) && count($employees_timeslot_mapping[$d->timeslot_id]) > 0;
        });

        if (!$this->simpleOutput && !$this->isPosting) {

            // merge timetable slots and employee data
            foreach ($data as &$d) {

                $employees = $employees_timeslot_mapping[$d->timeslot_id]->toArray();

                foreach ($employees as &$e) {
                    $e['employee_name'] = json_decode($e['employee_name'], true);
                }

                $d->employees = $employees;
                $d->class_name = json_decode($d->class_name, true);
                $d->subject_name = json_decode($d->subject_name, true);
                $d->period_group_name = json_decode($d->period_group_name, true);
            }

        }

        return $data->values()->toArray();
    }

    public function getTimetableWithTimeslotOverrideByDate(array $dates = []): Collection
    {
        $timetable_list_by_day_and_student = collect($this->getDefaultTimetable())->groupBy(['day', 'student_id']);
        $filters = [
            'date' => $dates,
        ];

        if (count($this->studentIds) > 0) {
            $filters['student_id'] = $this->studentIds;
        }

        // group by period
        $timeslot_overrides = app()->make(TimeslotOverrideRepository::class)
            ->getAll($filters)
            ->groupBy(['date', 'student_id'])
            ->all();

        $timetable_list_by_date_and_student = [];

        foreach ($dates as $date) {
            $day = strtoupper(Carbon::parse($date)->format('l'));
            foreach ($this->studentIds as $student_id) {
                $student_timetable_periods = $timetable_list_by_day_and_student[$day][$student_id] ?? [];
                $student_timeslot_overrides = $timeslot_overrides[$date][$student_id] ?? [];

                $timeslot_override_periods = [];
                $timeslots = collect([]);
                // no timeslot override
                if (count($student_timeslot_overrides) > 0) {
                    $timeslot_override_periods = $student_timeslot_overrides->pluck('period')->toArray();
                    foreach ($student_timeslot_overrides as $student_timeslot_override) {

                        if ($this->simpleOutput) {
                            $timeslots->push([
                                'student_id' => $student_id,
                                'day' => $day,
                                'date' => $date,
                                'period' => $student_timeslot_override->period,
                                'attendance_from' => $student_timeslot_override->attendance_from,
                                'attendance_to' => $student_timeslot_override->attendance_to,
                                'is_empty' => $student_timeslot_override->is_empty,
                                'has_mark_deduction' => true,
                            ]);
                        } else {
                            $timeslots->push([
                                'student_id' => $student_id,
                                'timetable_name' => null,
                                'period_group_name' => null,
                                'timeslot_type' => TimeslotOverride::class,
                                'timeslot_id' => $student_timeslot_override->id,
                                'day' => $day,
                                'date' => $date,
                                'from_time' => null,
                                'to_time' => null,
                                'period' => $student_timeslot_override->period,
                                'attendance_from' => $student_timeslot_override->attendance_from,
                                'attendance_to' => $student_timeslot_override->attendance_to,
                                'placeholder' => $student_timeslot_override->placeholder,
                                'inherit_from_school_attendance' => $student_timeslot_override->inherit_from_school_attendance,
                                'class_attendance_required' => $student_timeslot_override->class_attendance_required,
                                'is_empty' => $student_timeslot_override->is_empty,
                                'default_init_status' => PeriodAttendanceStatus::ABSENT->value,
                                'period_label_id' => null,
                                'has_mark_deduction' => true,
                            ]);
                        }

                    }
                }

                if (count($student_timetable_periods) > 0) {
                    foreach ($student_timetable_periods as $student_timetable_period) {

                        // skip if already have timeslot override
                        if (in_array($student_timetable_period['period'], $timeslot_override_periods)) {
                            continue;
                        }

                        if ($this->simpleOutput) {
                            $timeslots->push([
                                'student_id' => $student_id,
                                'day' => $day,
                                'date' => $date,
                                'period' => $student_timetable_period['period'],
                                'attendance_from' => $student_timetable_period['attendance_from'],
                                'attendance_to' => $student_timetable_period['attendance_to'],
                                'is_empty' => false, // student_timetable will only retrieve timeslot with period_labels.is_attendance_required = true + getDefaultTimetable() will remove timeslot without teacher
                                'has_mark_deduction' => $student_timetable_period['has_mark_deduction'],
                            ]);
                        } else {
                            $timeslots->push([
                                'student_id' => $student_id,
                                'timetable_name' => $student_timetable_period['timetable_name'],
                                'period_group_name' => $student_timetable_period['period_group_name'],
                                'timeslot_type' => Timeslot::class,
                                'timeslot_id' => $student_timetable_period['timeslot_id'],
                                'day' => $day,
                                'date' => $date,
                                'from_time' => $student_timetable_period['from_time'],
                                'to_time' => $student_timetable_period['to_time'],
                                'period' => $student_timetable_period['period'],
                                'attendance_from' => $student_timetable_period['attendance_from'],
                                'attendance_to' => $student_timetable_period['attendance_to'],
                                'placeholder' => $student_timetable_period['placeholder'],
                                'inherit_from_school_attendance' => true,
                                'class_attendance_required' => true,
                                'is_empty' => false, // student_timetable will only retrieve timeslot with period_labels.is_attendance_required = true + getDefaultTimetable() will remove timeslot without teacher
                                'default_init_status' => $student_timetable_period['default_init_status'],
                                'period_label_id' => $student_timetable_period['period_label_id'],
                                'has_mark_deduction' => $student_timetable_period['has_mark_deduction'],
                            ]);
                        }

                    }
                }
                if (count($timeslots) > 0) {
                    // sort by period asc
                    $timetable_list_by_date_and_student[$date][$student_id] = $timeslots->sortBy('period')->values()->toArray();
                }
            }
        }

        $this->timetableListByDateAndStudent = collect($timetable_list_by_date_and_student);
        return $this->timetableListByDateAndStudent;
    }

    public function getOverride($from_date, $to_date)
    {

        // attendance override
        $filters = [
            'period_from' => $from_date,
            'period_to' => $to_date,
            'attendance_recordable_type' => Student::class,
        ];

        if (count($this->studentIds) > 0) {
            $filters['attendance_recordable_id'] = $this->studentIds;
        }

        // group by period
        $data = app()->make(AttendancePeriodOverrideRepository::class)
            ->getAll($filters)
            ->groupBy(['period']);

        // group by student id
        foreach ($data as $index => &$d) {
            $data[$index] = $d->mapWithKeys(function ($value) {
                return [$value->attendance_recordable_id => $value];
            });
        }

        return $data;
    }

    public function hasIndividualOverrideOn($student, $date)
    {
        // attendance override
        $filters = [
            'period' => $date,
            'attendance_recordable_type' => get_class($student),
            'attendance_recordable_id' => $student->id,
        ];

        // group by period
        return app()->make(AttendancePeriodOverrideRepository::class)
            ->getQuery($filters)
            ->exists();

    }

    /**
     * @param $from_date
     * @param $to_date
     * @return Collection
     * @throws \Illuminate\Contracts\Container\BindingResolutionException
     * Simple Output is there to reduce memory required when processing student timetable.
     * If you only need the attendance_from/attendance_to for each date for each student, can use simple_output = true
     * If you need the detailed breakdown like each timeslot got what subject, what teacher, can use simple_output = false
     */
    public function getAttendancePeriods($from_date, $to_date)
    {

        $active_students_query = Student::query()
            ->with([
                'media' => function ($query) {
                    $query
                        ->select(['id', 'model_type', 'model_id', 'file_name', 'disk'])
                        ->where('collection_name', 'photo');
                }
            ])
            ->select(['students.id', 'students.student_number', 'students.name', 'cards.card_number'])
            ->leftJoin('cards', function ($join) {
                $join->on('cards.userable_id', '=', 'students.id')
                    ->where('cards.userable_type', Student::class)
                    ->where('cards.status', CardStatus::ACTIVE)
                    ->where('cards.card_type', CardType::PROXIMITY);
            })
            ->where('is_active', true);

        if (count($this->studentIds) > 0) {
            $active_students_query->whereIn('students.id', $this->studentIds);
        }

        $active_students = $active_students_query->lazy(500);
        $this->studentIds = $active_students->pluck('id')->toArray();

        $dates = CarbonPeriod::create($from_date, $to_date);

        // get individual override
        $override_list = $this->getOverride($from_date, $to_date);       // grouped by date and student ID

        $schoolAttendancePeriodOverrideRepository = app()->make(SchoolAttendancePeriodOverrideRepository::class);
        $final = collect([]);

        foreach ($dates as $date) {
            $school_attendance_period_override = $schoolAttendancePeriodOverrideRepository->getFirstPrioritySchoolAttendancePeriodOverride($date);

            $date_ymd = $date->toDateString();
            $day = Day::carbonWeekdayToDay($date->dayOfWeek);

            // get default student timetable merged with timeslot override
            $this->setDayFilter($day);
            $this->getTimetableWithTimeslotOverrideByDate([$date_ymd]);

            foreach ($active_students as $active_student) {
                $student_timetable_periods = collect($this->timetableListByDateAndStudent[$date_ymd][$active_student->id] ?? [])->where('is_empty', false)->where('has_mark_deduction', true);

                $attendance = [
                    'period' => $date_ymd,
                    'day' => $day->value,
                    'userable_id' => $active_student->id,
                    'userable_type' => Student::class,
                    'userable_name' => $active_student->getFormattedTranslations('name'),
                    'userable_number' => $active_student->student_number,
                    'card_number' => $active_student->card_number,
                    '_default_attendance_from' => null,
                    '_default_attendance_to' => null,
                    'attendance_from' => null,
                    'attendance_to' => null,
                    '_attendance_from_overwritten_by' => null,
                    '_attendance_to_overwritten_by' => null,
                ];

                if (!$this->isPosting) {
                    $attendance['img_url'] = $active_student->media->first()?->getUrl();
                }

                // student timetable
                if (count($student_timetable_periods) > 0) {
                    $attendance_period = $this->getAttendancePeriodForStudentAndDay($student_timetable_periods);

                    $attendance['_default_attendance_from'] = $attendance_period['from'];
                    $attendance['_default_attendance_to'] = $attendance_period['to'];
                    $attendance['attendance_from'] = $attendance_period['from'];
                    $attendance['attendance_to'] = $attendance_period['to'];
                }

                // school attendance period override
                if ($school_attendance_period_override) {
                    if ($school_attendance_period_override->attendance_from !== null) {
                        $attendance['attendance_from'] = $school_attendance_period_override->attendance_from;
                        $attendance['_attendance_from_overwritten_by'] = $school_attendance_period_override;
                    }
                    if ($school_attendance_period_override->attendance_to !== null) {
                        $attendance['attendance_to'] = $school_attendance_period_override->attendance_to;
                        $attendance['_attendance_to_overwritten_by'] = $school_attendance_period_override;
                    }
                }

                // individual override as highest priority
                $individual_override = $override_list[$date_ymd][$active_student->id] ?? null;
                if ($individual_override !== null) {
                    $attendance['attendance_from'] = $individual_override->attendance_from;
                    $attendance['_attendance_from_overwritten_by'] = $individual_override;
                    $attendance['attendance_to'] = $individual_override->attendance_to;
                    $attendance['_attendance_to_overwritten_by'] = $individual_override;
                }

                $final->push($attendance);
            }
        }
        return $final;

    }

    /**
     * @param $timetable_data
     * @return array
     * Please pass in timetable data for 1 student for 1 day only (e.g. student1 monday)
     */
    public function getAttendancePeriodForStudentAndDay($timetable_data)
    {
        return [
            'from' => collect($timetable_data)->sortBy('attendance_from')->first()['attendance_from'],
            'to' => collect($timetable_data)->sortByDesc('attendance_to')->first()['attendance_to'],
        ];

    }

    public function getStudent(): Student
    {
        return $this->student;
    }

    public function setStudent(Student $student): StudentTimetableService
    {
        $this->studentIds = [$student->id];
        $this->student = $student;
        return $this;
    }

    public function getDayFilter(): Day
    {
        return $this->dayFilter;
    }

    public function setDayFilter(Day $dayFilter): StudentTimetableService
    {
        $this->dayFilter = $dayFilter;
        return $this;
    }

    public function getDaysFilter(): array
    {
        return $this->daysFilter;
    }

    public function setDaysFilter(array $daysFilter): StudentTimetableService
    {
        $this->daysFilter = $daysFilter;
        return $this;
    }

    public function getStudentIds(): array
    {
        return $this->studentIds;
    }

    public function setStudentIds(array $studentIds): StudentTimetableService
    {
        $this->studentIds = $studentIds;
        return $this;
    }

    public function appendContractorsData($students_attendance_periods)
    {
        $dates = array_unique(array_column($students_attendance_periods->toArray(), 'period'));

        $contractors = Contractor::with([
            'media' => function ($query) {
                $query->where('collection_name', 'photo');
            }
        ])
            ->select(['contractors.id', 'contractors.contractor_number', 'contractors.name', 'cards.card_number'])
            ->join('cards', function ($join) {
                $join->on('cards.userable_id', '=', 'contractors.id')
                    ->where('cards.userable_type', Contractor::class)
                    ->where('cards.status', CardStatus::ACTIVE)
                    ->where('cards.card_type', CardType::PROXIMITY);
            })
            ->get();

        foreach ($dates as $date) {
            $date = Carbon::parse($date);
            foreach ($contractors as $contractor) {
                $contractor_attendance = [
                    'period' => $date->toDateString(),
                    'day' => Day::carbonWeekdayToDay($date->dayOfWeek),
                    'userable_id' => $contractor->id,
                    'userable_type' => Contractor::class,
                    'userable_name' => $contractor->getFormattedTranslations('name'),
                    'userable_number' => $contractor->contractor_number,
                    'card_number' => $contractor->card_number,
                    'img_url' => $contractor->media->first()?->getUrl(),
                    '_default_attendance_from' => null,
                    '_default_attendance_to' => null,
                    'attendance_from' => null,
                    'attendance_to' => null,
                    '_attendance_from_overwritten_by' => null,
                    '_attendance_to_overwritten_by' => null,
                ];

                $students_attendance_periods->push($contractor_attendance);
            }
        }
        return $students_attendance_periods;
    }

    public function getTimetableListByDateAndStudent(): Collection
    {
        return $this->timetableListByDateAndStudent;
    }

    public function isSimpleOutput(): bool
    {
        return $this->simpleOutput;
    }

    public function setSimpleOutput(bool $simpleOutput): StudentTimetableService
    {
        $this->simpleOutput = $simpleOutput;
        return $this;
    }

    public function setIsPosting(bool $is_posting): StudentTimetableService
    {
        $this->isPosting = $is_posting;
        return $this;
    }
}
