<?php

namespace App\Services;

use App\Exceptions\RunningNumberException;
use App\Models\RunningNumber;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Cache;

class DocumentRunningNumberService
{
    const ZERO_STR_PAD_LEFT = 5;
    const PLACEHOLDER_RUNNING_NUMBER = '[[RUNNING_NUMBER]]';
    const PLACEHOLDER_YEAR = '[[YEAR]]';
    const PLACEHOLDER_MONTH = '[[MONTH]]';

    protected ?string $documentType;
    protected ?string $year;
    protected ?string $month;
    protected ?string $identifier1;
    protected ?string $identifier2;

    protected ?RunningNumber $runningNumber;
    protected bool $runningNumberAddedToComponent;

    protected Collection $components;
    private int $padding;
    private int $initNextNumber;

    public function __construct()
    {
        $this->init();
    }

    public function init()
    {
        $this->components = new Collection([]);
        $this->documentType = null;
        $this->year = null;
        $this->month = null;
        $this->runningNumber = null;
        $this->runningNumberAddedToComponent = false;

        return $this;
    }

    public function generate()
    {

        if (!isset($this->documentType)) {
            throw new \Exception('Document type not set');
        }

        if (!$this->runningNumberAddedToComponent) {
            throw new RunningNumberException('Document number must have a running number component.');
        }


        return Cache::lock('running-number-lock-' . $this->documentType . '-' . ($this->year ?? '#NA#') . '-' . ($this->month ?? '#NA#') . '-' . ($this->identifier1 ?? '#NA#') . '-' . ($this->identifier2 ?? '#NA#'),
            15)->block(3, function () {

            $this->getRunningNumberFromDatabase();
            $output = $this->components->join('');

            $next_number = $this->runningNumber->next_number;
            $max_number = $this->runningNumber->max_number;

            if ($max_number !== null && $next_number > $max_number) {
                throw new RunningNumberException('Unable to generate more running number for this series.');
            }

            $next_number_with_padding = str_pad($next_number, $this->getPadding(), '0', STR_PAD_LEFT);

            $month_with_padding = isset($this->month) ? str_pad($this->month, 2, '0', STR_PAD_LEFT) : '';

            $output = str_replace(self::PLACEHOLDER_RUNNING_NUMBER, $next_number_with_padding, $output);
            $output = str_replace(self::PLACEHOLDER_YEAR, $this->year ?? '', $output);
            $output = str_replace(self::PLACEHOLDER_MONTH, $month_with_padding, $output);

            $this->incrementRunningNumber();
            return $output;

        });

    }

    public function getRunningNumberFromDatabase()
    {

        if (!isset($this->documentType)) {
            throw new \Exception('Document type not set');
        }

        $data = RunningNumber::lockForUpdate()->firstOrCreate([
            'document_type' => $this->documentType,
            'year' => $this->year ?? null,
            'month' => $this->month ?? null,
            'identifier1' => $this->identifier1 ?? null,
            'identifier2' => $this->identifier2 ?? null,
        ], [
            'next_number' => $this->initNextNumber ?? 1
        ]);

        $this->runningNumber = $data;
        return $this;

    }

    public function initRunningNumber()
    {
        if (!isset($this->documentType)) {
            throw new \Exception('Document type not set');
        }

        return RunningNumber::firstOrCreate([
            'document_type' => $this->documentType,
            'year' => $this->year ?? null,
            'month' => $this->month ?? null,
            'identifier1' => $this->identifier1 ?? null,
            'identifier2' => $this->identifier2 ?? null,
        ], [
            'next_number' => $this->initNextNumber ?? 1
        ]);
    }

    public function getPadding(): int
    {
        if (isset($this->padding)) {
            return $this->padding;
        } else {
            return static::ZERO_STR_PAD_LEFT;
        }
    }

    public function incrementRunningNumber()
    {

        if (!isset($this->runningNumber)) {
            throw new \Exception('Running number not set');
        }

        RunningNumber::where('id', $this->runningNumber->id)->increment('next_number', 1);
        return $this;

    }

    public function addCustomComponent(string $value)
    {

        if (preg_match("/\[\[.+\]\]/", $value) === 1) {
            throw new \Exception('Custom component cannot contain pattern [[ ]]');
        }

        $this->components->push($value);
        return $this;
    }

    public function addPresetComponent(string $placeholder)
    {

        $this->components->push($placeholder);

        if ($placeholder === self::PLACEHOLDER_RUNNING_NUMBER) {
            $this->runningNumberAddedToComponent = true;
        }

        return $this;

    }

    public function setCustomPadding(int $padding): DocumentRunningNumberService
    {
        $this->padding = $padding;

        return $this;
    }

    public function getDocumentType(): string
    {
        return $this->documentType;
    }

    public function setDocumentType(string $documentType): DocumentRunningNumberService
    {
        $this->documentType = $documentType;
        return $this;
    }

    public function getYear(): string
    {
        return $this->year;
    }

    public function setYear(string $year): DocumentRunningNumberService
    {
        $this->year = $year;
        return $this;
    }

    public function getMonth(): string
    {
        return $this->month;
    }

    public function setMonth(string $month): DocumentRunningNumberService
    {
        $this->month = $month;
        return $this;
    }

    public function getRunningNumber(): RunningNumber
    {
        return $this->runningNumber;
    }

    public function setRunningNumber(RunningNumber $runningNumber): DocumentRunningNumberService
    {
        $this->runningNumber = $runningNumber;
        return $this;
    }

    public function getIdentifier1(): ?string
    {
        return $this->identifier1;
    }

    public function setIdentifier1(?string $identifier1): DocumentRunningNumberService
    {
        $this->identifier1 = $identifier1;
        return $this;
    }

    public function getIdentifier2(): ?string
    {
        return $this->identifier2;
    }

    public function setIdentifier2(?string $identifier2): DocumentRunningNumberService
    {
        $this->identifier2 = $identifier2;
        return $this;
    }

    public function setInitNextNumber(int $init_next_number): DocumentRunningNumberService
    {
        $this->initNextNumber = $init_next_number;
        return $this;
    }
}
