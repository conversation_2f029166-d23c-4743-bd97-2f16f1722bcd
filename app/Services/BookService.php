<?php

namespace App\Services;

use App\Enums\BookCondition;
use App\Enums\BookStatus;
use App\Enums\LibraryMemberType;
use App\Exceptions\CannotDeleteModelException;
use App\Helpers\ErrorCodeHelper;
use App\Models\Book;
use App\Repositories\BookRepository;
use App\Repositories\LibraryBookLoanRepository;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;

class BookService
{
    public function __construct(
        protected BookRepository $bookRepository,
        protected AuthorService $authorService
    ) {
    }

    public function getAllPaginatedBooks($filters = []): LengthAwarePaginator
    {
        return $this->bookRepository->getAllPaginated($filters);
    }

    public function createBook($data): ?Model
    {
        return DB::transaction(function () use ($data) {
            // Automatically set status based on condition
            if (isset($data['condition'])) {
                $condition = BookCondition::from($data['condition']);
                $data['status'] = $this->determineStatusFromCondition($condition);
            }

            $model = $this->bookRepository->create($data);
            $authors = $this->authorService->createAndGetAuthors($data['authors'] ?? []);
            $author_ids = $authors->pluck('id')->toArray();

            $this->bookRepository->syncAuthors($model, $author_ids);
            $this->bookRepository->updateLoanSettings($model, $data['loan_settings']);

            return $model->refresh();
        });
    }

    public function updateBook($id, $data): ?Model
    {
        return DB::transaction(function () use ($id, $data) {
            $book = $this->bookRepository->find($id);

            if (isset($data['condition']) && $book->condition->value != $data['condition']) {
                $condition = BookCondition::from($data['condition']);
                $data['status'] = $this->determineStatusFromCondition($condition);
            }

            $model = $this->bookRepository->update($book, $data);
            $authors = $this->authorService->createAndGetAuthors($data['authors'] ?? []);
            $author_ids = $authors->pluck('id')->toArray();

            $this->bookRepository->syncAuthors($model, $author_ids);
            $this->bookRepository->updateLoanSettings($model, $data['loan_settings']);

            return $model->refresh();
        });
    }

    public function markBookAsBorrowed($id): ?Model
    {
        return $this->bookRepository->update($id, ['status' => BookStatus::BORROWED]);
    }

    public function markBookAsAvailable($id): ?Model
    {
        return $this->bookRepository->update($id, ['status' => BookStatus::AVAILABLE]);
    }

    public function markBookAsLost($id): ?Model
    {
        return $this->bookRepository->update($id, ['status' => BookStatus::LOST]);
    }

    public function deleteBook($id): bool
    {
        $is_used = app()->make(LibraryBookLoanRepository::class)->hasBookLoanByBookId($id);

        if ($is_used) {
            throw new CannotDeleteModelException('Unable to delete book already used in existing loans.');
        }

        return $this->bookRepository->delete($id);
    }

    public function findLoanSettingVia(int $id, LibraryMemberType $type): ?Model
    {
        return $this->bookRepository->findLoanSettingVia($id, $type);
    }

    public function findBook(int $id): ?Model
    {
        return $this->bookRepository->find($id);
    }

    public function checkBookIsAllowedToBorrow(?Model $member, ?Model $book)
    {
        if ($book->status != BookStatus::AVAILABLE) {
            throw new \Exception("Book [No:$book->book_no] cannot be borrowed.");
        }

        $book_loan_setting = $this->findLoanSettingVia($book->id, $member->type);

        if (!$book_loan_setting?->can_borrow) {
            throw new \Exception("Book [No:$book->book_no] cannot be borrowed.");
        }

        return $this;
    }

    public function recoverLostBook(Book $book): ?Model
    {
        if (!$book->isLost()) {
            ErrorCodeHelper::throwError(ErrorCodeHelper::LIBRARY_ERROR, 23001);
        }

        return $this->markBookAsAvailable($book);
    }

    private function determineStatusFromCondition(BookCondition $condition): BookStatus
    {
        return match ($condition) {
            BookCondition::WRITE_OFF => BookStatus::NOT_AVAILABLE,
            BookCondition::MISSING => BookStatus::LOST,
            default => BookStatus::AVAILABLE,
        };
    }
}
