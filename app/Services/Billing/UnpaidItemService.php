<?php

namespace App\Services\Billing;

use App\Interfaces\Billable;
use App\Models\BillingDocument;
use App\Models\BillingDocumentLineItem;
use App\Models\DiscountSetting;
use App\Models\Employee;
use App\Models\Product;
use App\Models\ScholarshipAward;
use App\Models\UnpaidItem;
use App\Repositories\UnpaidItemRepository;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Pagination\LengthAwarePaginator;

class UnpaidItemService
{

    protected UnpaidItem $unpaidItem;
    protected Carbon $paidAt;
    protected Employee $createdByEmployee;
    protected string $description;
    protected Billable $billToParty;
    protected Product $product;
    protected string $period;
    protected string $currency;
    protected float $unitPrice;
    protected float $quantity;
    protected Collection|\Illuminate\Support\Collection $unpaidItems;

    protected UnpaidItemRepository $unpaidItemRepository;

    public function __construct(UnpaidItemRepository $unpaidItemRepository)
    {
        $this->unpaidItemRepository = $unpaidItemRepository;
    }

    public function getAllPaginatedUnpaidItems($filters = []): LengthAwarePaginator
    {
        return $this->unpaidItemRepository->getAllPaginated($filters);
    }

    public function getAllUnpaidItems($filters = []): Collection
    {
        return $this->unpaidItemRepository->getAll($filters);
    }

    public function getAllUnpaidItemsWithDiscount($filters = []): Collection
    {
        // Period must be set to asc in order for the discount to be applied correctly
        $filters['order_by']['period'] = 'ASC';
        $filters['includes'] = array_merge($filters['includes'] ?? [], [
            'billingDocument' => function ($query) {
                $query->with([
                    'lineItems' => function ($query) {
                        $query->with([
                            'discount' => function ($query) {
                                $query->with([
                                    'source' => function (MorphTo $morphTo) {
                                        $morphTo->morphWith([
                                            // only load scholarship if it's a ScholarshipAward
                                            ScholarshipAward::class => ['scholarship'],
                                        ]);
                                    },
                                ]);
                            },
                        ]);
                    },
                    'paymentGatewayLogs',
                ]);
            },
        ]);

        $unpaid_items = $this->unpaidItemRepository->getAll($filters);

        $this->setDiscountForUnpaidItems($unpaid_items);
        $this->setDiscountForNonUnpaidItems($unpaid_items);

        return $unpaid_items;
    }

    public function createNewUnpaidItem()
    {

        $this->unpaidItem = $this->unpaidItemRepository->create([
            'bill_to_type' => $this->billToParty->getBillToType(),
            'bill_to_id' => $this->billToParty->getBillToId(),
            'status' => UnpaidItem::STATUS_UNPAID,
            'description' => $this->description ?? null,
            'product_id' => $this->product->id,
            'gl_account_code' => $this->product->gl_account_code,
            'period' => $this->period,
            'currency_code' => $this->currency,
            'unit_price' => $this->unitPrice,
            'quantity' => $this->quantity,
            'amount_before_tax' => bcmul($this->unitPrice, $this->quantity, 2),
            'created_by_employee_id' => $this->createdByEmployee->id,
            'paid_at' => null,
            'billing_document_id' => null,
        ]);

        return $this;

    }

    public function assignBillingDocument(BillingDocument $billingDocument)
    {

        if (isset($this->unpaidItems) && $this->unpaidItems->count() > 0) {

            UnpaidItem::whereIn('id', $this->unpaidItems->pluck('id')->toArray())->update([
                'status' => UnpaidItem::STATUS_PENDING,
                'billing_document_id' => $billingDocument->id,
            ]);

        } else {
            if (isset($this->unpaidItem)) {
                $this->unpaidItem->billing_document_id = $billingDocument->id;
                $this->updateStatus(UnpaidItem::STATUS_PENDING);
            }
        }

        return $this;
    }

    public function updateStatus($new_status)
    {

        if (isset($this->unpaidItem)) {
            $this->unpaidItem->status = $new_status;
        } else {
            if (isset($this->unpaidItems) && $this->unpaidItems->count() > 0) {
                UnpaidItem::whereIn('id', $this->unpaidItems->pluck('id')->toArray())->update([
                    'status' => $new_status,
                ]);
            }
        }

        return $this;
    }

    public function save()
    {
        if (isset($this->unpaidItem)) {
            $this->unpaidItem->save();
        }
    }

    public function getUnpaidItem(): UnpaidItem
    {
        return $this->unpaidItem;
    }

    public function setUnpaidItem(UnpaidItem &$unpaidItem): UnpaidItemService
    {
        $this->unpaidItem = $unpaidItem;
        return $this;
    }

    public function getPaidAt(): Carbon
    {
        return $this->paidAt;
    }

    public function setPaidAt(Carbon $paidAt): UnpaidItemService
    {
        $this->paidAt = $paidAt;
        return $this;
    }

    public function getCreatedByEmployee(): Employee
    {
        return $this->createdByEmployee;
    }

    public function setCreatedByEmployee(Employee $createdByEmployee): UnpaidItemService
    {
        $this->createdByEmployee = $createdByEmployee;
        return $this;
    }

    public function getDescription(): string
    {
        return $this->description;
    }

    public function setDescription(string $description): UnpaidItemService
    {
        $this->description = $description;
        return $this;
    }

    public function getBillToParty(): Billable
    {
        return $this->billToParty;
    }

    public function setBillToParty(Billable $billToParty): UnpaidItemService
    {
        $this->billToParty = $billToParty;
        return $this;
    }

    public function getProduct(): Product
    {
        return $this->product;
    }

    public function setProduct(Product $product): UnpaidItemService
    {
        $this->product = $product;
        return $this;
    }

    public function getPeriod(): string
    {
        return $this->period;
    }

    public function setPeriod(string $period): UnpaidItemService
    {
        $this->period = Carbon::parse($period)->startOfMonth()->toDateString();
        return $this;
    }

    public function getCurrency(): string
    {
        return $this->currency;
    }

    public function setCurrency(string $currency): UnpaidItemService
    {
        $this->currency = $currency;
        return $this;
    }

    public function getUnitPrice(): float
    {
        return $this->unitPrice;
    }

    public function setUnitPrice(float $unitPrice): UnpaidItemService
    {
        $this->unitPrice = $unitPrice;
        return $this;
    }

    public function getQuantity(): float
    {
        return $this->quantity;
    }

    public function setQuantity(float $quantity): UnpaidItemService
    {
        $this->quantity = $quantity;
        return $this;
    }

    public function getUnpaidItems(): Collection|\Illuminate\Support\Collection
    {
        return $this->unpaidItems;
    }

    public function setUnpaidItems(Collection|\Illuminate\Support\Collection $unpaidItems): UnpaidItemService
    {
        $this->unpaidItems = $unpaidItems;
        return $this;
    }

    private function setDiscountForUnpaidItems(&$unpaid_items): void
    {
        $discounts = collect();

        if (count($unpaid_items) > 0) {
            // simulate unpaid item with discounts to get amount_before_tax_after_discount
            $billing_document_service = app()->make(BillingDocumentService::class)
                ->setBillToParty($this->getBillToParty())
                ->setDocumentDate(now()->startOfDay());

            foreach ($unpaid_items as $unpaid_item) {
                if ($unpaid_item->status !== UnpaidItem::STATUS_UNPAID) {
                    continue;
                }

                $line_item = new BillingDocumentLineItem([
                    'is_discount' => false,
                    'amount_before_tax' => $unpaid_item->amount_before_tax,
                    'billable_item_id' => $unpaid_item->id,
                    'billable_item_type' => get_class($unpaid_item),
                    'gl_account_code' => $unpaid_item->gl_account_code,
                ]);

                $line_item->setRelation('billableItem', $unpaid_item);

                $billing_document_service->addLineItem($line_item);
            }

            $discounts = $billing_document_service
                ->calculateEligibleDiscounts()
                ->getEligibleDiscounts();

            // there can be multiple discounts for a billable item
            $discounts = collect($discounts)->groupBy('original_line_item.billable_item_id');
        }

        foreach ($unpaid_items as &$unpaid_item) {
            if (isset($discounts[$unpaid_item->id])) {
                $discount_group = $discounts[$unpaid_item->id];

                $temp_discount_names = [];

                foreach ($discount_group->pluck('discount_setting') as $discount) {
                    $temp_discount_names[] = $this->getDiscountName($discount);
                }

                // sum all discounts for this billable item
                $total_discounts = $discount_group->sum('amount_before_tax');

                $unpaid_item->amount_before_tax_after_discount = bcsub($unpaid_item->amount_before_tax, $total_discounts, 2);
                $unpaid_item->discounts = $temp_discount_names;
            } else {
                $unpaid_item->amount_before_tax_after_discount = $unpaid_item->amount_before_tax;
            }
        }
    }

    private function setDiscountForNonUnpaidItems(&$unpaid_items): void
    {
        foreach ($unpaid_items as &$unpaid_item) {
            if ($unpaid_item->status === UnpaidItem::STATUS_UNPAID) {
                continue;
            }

            $current_unpaid_item_id = $unpaid_item->id;
            $current_unpaid_item_type = get_class($unpaid_item);

            $billing_document = $unpaid_item->billingDocument;

            if ($billing_document) {
                $current_line_item = $billing_document->lineItems
                    ->where('billable_item_id', $current_unpaid_item_id)
                    ->where('billable_item_type', $current_unpaid_item_type)
                    ->first();

                // total discounts for this line item
                $discount_line_items = $billing_document->lineItems
                    ->where('discount_original_line_item_id', $current_line_item->id)
                    ->where('is_discount', true);

                $temp_discount_names = [];

                foreach ($discount_line_items as $line_item) {
                    $temp_discount_names[] = $this->getDiscountName($line_item->discount);
                } 

                $total_discounts = $discount_line_items->sum('amount_before_tax');

                $unpaid_item->amount_before_tax_after_discount = bcadd($unpaid_item->amount_before_tax, $total_discounts, 2);
                $unpaid_item->discounts = $temp_discount_names;
            }
        }
    }

    private function getDiscountName(DiscountSetting $discount): string
    {
        if (isset($discount->source)) {
            $discount_name = $discount->source?->scholarship?->name;
        } else {
            $discount_name = 'Discount (' . $discount->basis . '): ' . number_format($discount->basis_amount, 2);
        }

        return $discount_name;
    }
}
