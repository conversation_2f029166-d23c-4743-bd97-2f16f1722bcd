<?php

namespace App\Services;

use App\Helpers\ErrorCodeHelper;
use Carbon\Carbon;
use Illuminate\Http\Client\PendingRequest;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Str;

class IsbnService
{
    protected ?string $bookTitle = null;
    protected array $authors = [];
    protected ?int $bookPage = null;
    protected ?string $bookPublisher = null;
    protected ?string $bookPublishedDate = null;
    protected ?string $bookSize = null;
    protected string $apiKey;
    protected array $item = [];
    protected Http $client;

    public function __construct()
    {
        $this->apiKey = $this->getConfigValue('key');
    }

    public function getBookTitle(): ?string
    {
        return $this->bookTitle;
    }

    public function setBookTitle(string $bookTitle)
    {
        $this->bookTitle = $bookTitle;
    }

    public function getAuthors(): array
    {
        return $this->authors;
    }

    public function setAuthors(array $authors)
    {
        $this->authors = $authors;
    }

    public function setBookPage(int $bookPage)
    {
        $this->bookPage = $bookPage;
    }

    public function getBookPage(): ?int
    {
        return $this->bookPage;
    }

    public function setBookPublisher(string $bookPublisher)
    {
        $this->bookPublisher = $bookPublisher;
    }

    public function getBookPublisher(): ?string
    {
        return $this->bookPublisher;
    }

    public function setBookPublishedDate(string $bookPublishedDate)
    {
        $this->bookPublishedDate = $bookPublishedDate;
    }

    public function getBookPublishedDate(): ?string
    {
        return $this->bookPublishedDate;
    }

    public function setBookSize(string $bookSize)
    {
        $this->bookSize = $bookSize;
    }

    public function getBookSize(): ?string
    {
        return $this->bookSize;
    }

    public function setItem(array $item)
    {
        $this->item = $item;
    }

    public function getItem(): array
    {
        return $this->item;
    }

    public function request($isbn): IsbnService
    {
        $isbn = str_replace('-', '', $isbn); // remove dash
        $isbn = Str::startsWith($isbn, 'isbn:') ? $isbn : 'isbn:'.$isbn; // add prefix isbn:

        $response = $this->client()->get($this->getConfigValue('book_url').'/volumes',
            ['key' => $this->apiKey, 'q' => $isbn]
        );

        $response->throw();

        $response_data = $response->json();

        // if invalid, totalItems will be 0 and won't have items
        if (isset($response_data['totalItems']) && $response_data['totalItems'] == 0 && !isset($response_data['items'])) {
            ErrorCodeHelper::throwError(ErrorCodeHelper::ISBN_ERROR, 22001);
        }
        $this->setItem($response->json()['items'][0]); // should have volumeInfo, saleInfo, accessInfo

        // if item has selfLink, use data from selfLink since it is more comprehensive
        if (isset($this->getItem()['selfLink'])) {
            $response = $this->client()->get($this->getItem()['selfLink'],
                ['key' => $this->apiKey]
            );
            $response->throw();
            $this->setItem($response->json());
        }

        return $this;
    }

    public function populateBookSize(array $dimensions): string
    {
        $book_size_string = '';
        foreach ($dimensions as $key => $value) {
            if ($book_size_string !== '') {
                $book_size_string .= ', ';
            }
            $book_size_string .= "{$key}: {$value}";
        }
        return $book_size_string;
    }

    public function mapItem(): IsbnService
    {
        if (isset($this->getItem()['volumeInfo'])) {
            $volume_info = $this->getItem()['volumeInfo'];
            // title
            if (isset($volume_info['title']) && is_string($volume_info['title'])) {
                $this->setBookTitle($volume_info['title']);
            }
            // authors
            if (isset($volume_info['authors']) && is_array($volume_info['authors'])) {
                $this->setAuthors($volume_info['authors']);
            }
            // book page
            if (isset($volume_info['pageCount']) && is_integer($volume_info['pageCount'])) {
                $this->setBookPage($volume_info['pageCount']);
            }
            // publisher
            if (isset($volume_info['publisher']) && is_string($volume_info['publisher'])) {
                $this->setBookPublisher($volume_info['publisher']);
            }
            // published date (sometimes the date will be just a year)
            if (isset($volume_info['publishedDate']) && is_string($volume_info['publishedDate']) && strlen($volume_info['publishedDate']) !== 4) {
                try {
                    $formatted_published_date = Carbon::parse($volume_info['publishedDate'])->format('Y-m-d');
                } catch (\Exception $e) {
                    $formatted_published_date = null;
                }
                
                if ($formatted_published_date != null) {
                    $this->setBookPublishedDate($formatted_published_date);
                }
            }
            // book size
            if (isset($volume_info['dimensions']) && is_array($volume_info['dimensions'])) {
                $book_size_string = $this->populateBookSize($volume_info['dimensions']);
                $this->setBookSize($book_size_string);
            }
        }

        return $this;
    }

    protected function client(): PendingRequest
    {
        return Http::timeout(15)
            ->connectTimeout(5)
            ->acceptJson()
            ->contentType('application/json');
    }

    protected function getConfigValue($configName)
    {
        return config('services.isbn.'.$configName);
    }
}
