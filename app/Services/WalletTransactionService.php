<?php

namespace App\Services;

use App\Enums\PaymentProvider;
use App\Enums\PaymentStatus;
use App\Enums\TaxType;
use App\Enums\WalletTransactionStatus;
use App\Enums\WalletTransactionType;
use App\Factories\BillingDocumentFactory;
use App\Factories\UserableFactory;
use App\Helpers\ErrorCodeHelper;
use App\Helpers\SystemHelper;
use App\Interfaces\Userable;
use App\Interfaces\WalletTransactable;
use App\Models\BillingDocument;
use App\Models\GlAccount;
use App\Models\PaymentGatewayLog;
use App\Models\PaymentMethod;
use App\Models\Student;
use App\Models\User;
use App\Models\Wallet;
use App\Models\WalletTransaction;
use App\Repositories\CardRepository;
use App\Repositories\TerminalRepository;
use App\Repositories\WalletRepository;
use App\Repositories\WalletTransactionRepository;
use App\Services\Billing\BillingDocumentLineItemService;
use App\Services\Billing\BillingDocumentService;
use Illuminate\Contracts\Auth\Authenticatable;
use Illuminate\Contracts\Cache\Lock;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;

class WalletTransactionService
{
    const WALLET_LOCK_RELEASE_IN_SECONDS = 60;
    protected array $data = [];
    protected Model $wallet;
    protected WalletTransactable $walletTransactable;
    protected Model $receiver_wallet;
    protected Model $model;
    protected float $amount;
    private WalletTransactionRepository $walletTransactionRepository;
    private WalletRepository $walletRepository;
    private TerminalRepository $terminalRepository;
    private CardRepository $cardRepository;
    private WalletService $walletService;
    private PaymentGatewayLogService $paymentGatewayLogService;
    private PaymentGatewayService $paymentGatewayService;
    private Authenticatable|User|Model $user;
    private Userable $userable;
    private WalletTransaction $walletTransaction;

    private WalletTransaction $transaction;


    public function __construct(
        WalletTransactionRepository $wallet_transaction_repository,
        WalletRepository $wallet_repository,
        TerminalRepository $terminal_repository,
        WalletService $wallet_service,
        CardRepository $card_repository,
        PaymentGatewayLogService $payment_gateway_log_service,
        PaymentGatewayService $payment_gateway_service,
    ) {
        $this->walletTransactionRepository = $wallet_transaction_repository;
        $this->walletRepository = $wallet_repository;
        $this->terminalRepository = $terminal_repository;
        $this->walletService = $wallet_service;
        $this->cardRepository = $card_repository;
        $this->paymentGatewayLogService = $payment_gateway_log_service;
        $this->paymentGatewayService = $payment_gateway_service;
    }

    public function setWalletTransaction(WalletTransaction $wallet_transaction): self
    {
        $this->walletTransaction = $wallet_transaction;
        return $this;
    }

    public function getWalletTransaction(): WalletTransaction
    {
        return $this->walletTransaction;
    }

    public function setType(WalletTransactionType $transaction_type): static
    {
        $this->data['type'] = $transaction_type;

        return $this;
    }

    public function setRemark(string|null $remark): static
    {
        $this->data['remark'] = $remark;

        return $this;
    }

    public function setDescription(string $description): static
    {
        $this->data['description'] = $description;

        return $this;
    }

    public function setTransaction(WalletTransaction|Model $transaction): static
    {
        $this->transaction = $transaction;

        return $this;
    }

    public function getTransaction(): WalletTransaction
    {
        return $this->transaction;
    }

    public function setUserableByTypeAndId(string $userable_type, int $userable_id): self
    {
        $this->userable = (new UserableFactory($userable_type))->find($userable_id);

        return $this;
    }

    public function setWalletByCurrencyCode(string $currency): self
    {
        $this->wallet = $this->walletRepository->getWalletByUserAndCurrencyCode($this->user, $currency);
        return $this;
    }

    public function getWallet(): Model
    {
        return $this->wallet;
    }

    public function setWalletTransactable(WalletTransactable $wallet_transactable): self
    {
        $this->walletTransactable = $wallet_transactable;

        return $this;
    }

    public function getWalletTransactable(): WalletTransactable
    {
        return $this->walletTransactable;
    }

    public function setWalletTransactableByQubeRefNo(string $ref_no): static
    {
        $this->walletTransactable = $this->getTerminalFromQubeRefNo($ref_no);

        return $this;
    }

    /**
     * @param $input
     * @param  Wallet  $source_wallet  The wallet of the user initiating the transaction.
     * @param  Wallet  $target_wallet  The wallet receiving the transaction.
     * @return Model|null Returns the created transaction model on success, null on failure (e.g., insufficient funds, currency mismatch).
     */
    public function createWalletTransaction($input, Wallet $source_wallet, WalletTransactable $wallet_transactable): ?Model
    {

        $balance_before = $source_wallet->balance;
        $balance_after = bcadd($balance_before, $input['amount_after_tax'], 2);

        $payload = [
            'card_id' => $input['card_id'] ?? null,
            'wallet_id' => $source_wallet->id,
            'reference_no' => $input['reference_no'] ?? null,
            'type' => $input['type'],
            'status' => $input['status'],
            'wallet_transactable_type' => $wallet_transactable->getMorphClass(),
            'wallet_transactable_id' => $wallet_transactable->getKey(),
            'total_amount' => $input['total_amount'],
            'balance_before' => $balance_before,
            'balance_after' => $balance_after,
            'description' => $input['description'],
            'remark' => $input['remark'] ?? null,
            'amount_before_tax' => $input['amount_before_tax'],
            'amount_after_tax' => $input['amount_after_tax'],
            'tax_type' => $input['tax_type'],
            'tax_value' => $input['tax_value'],
        ];

        if (isset($this->userable)) {
            $payload['userable_type'] = get_class($this->userable);
            $payload['userable_id'] = $this->userable->id;
        }

        $this->model = $this->walletTransactionRepository->create($payload);

        return $this->model;
    }

    public function chargeWalletTransaction($input): WalletTransaction
    {
        $user = Arr::get($input, 'user');
        $card = null;

        if (!$user) {
            $card = $this->cardRepository->getCardByCardNumber($input['card_number']);
            $user = $card->userable->user;
        }

        if ( $card !== null ) {
            $this->userable = $card->userable;
        }

        $input['amount_after_tax'] = bcmul(abs($input['amount_after_tax']), -1, 2);
        $input['amount_before_tax'] = bcmul(abs($input['amount_before_tax']), -1, 2);
        $input['total_amount'] = $input['amount_after_tax'];

        $this->setUser($user)
            ->setWalletByCurrencyCode($input['currency']);

        $wallet = $this->getWallet();

        if (!$wallet) {
            throw new \Exception(ErrorCodeHelper::getTranslatedErrorMessage(ErrorCodeHelper::WALLET_ERROR, 1008), 1008);
        }

        $lock = $this->getWalletLock();

        try {
            if (!$lock->get()) {
                throw new \Exception(ErrorCodeHelper::getTranslatedErrorMessage(ErrorCodeHelper::WALLET_ERROR, 1005), 1005);
            }

            $this->walletService->setWallet($wallet);

            if (!$this->walletService->hasEnoughBalance($input['total_amount'])) {
                throw new \Exception(ErrorCodeHelper::getTranslatedErrorMessage(ErrorCodeHelper::WALLET_ERROR, 1002), 1002);
            }

            $wallet_transaction = DB::transaction(function () use (&$card, $input) {
                $input['card_id'] = $card?->id;
                $input['reference_no'] = $input['order_reference_no'];
                $input['type'] = WalletTransactionType::TRANSACTION->value;
                $input['status'] = WalletTransactionStatus::SUCCESS->value;
                $input['tax_type'] = TaxType::FLAT_AMOUNT->value;
                $input['tax_value'] = 0;

                $wallet_transaction = $this->setType(WalletTransactionType::TRANSACTION)
                    ->createWalletTransaction($input, $this->getWallet(), $this->getWalletTransactable());

                $this->walletService->addBalance($input['amount_after_tax']);

                return $wallet_transaction;
            });

            if (isset($this->userable)) {

                // todo: Refactor this to use wallet transaction currency instead of school currency for accuracy
                if ($wallet_transaction->walletTransactable) {
                    $message = 'Payment of ' . config('school.currency_code') . ' ' . number_format(abs($wallet_transaction->total_amount), 2) . ' for ' . $wallet_transaction->walletTransactable->getDescription() . ' is successful';
                } else {
                    $message = 'Payment of ' . config('school.currency_code') . ' ' . number_format(abs($wallet_transaction->total_amount), 2) . ' is successful';
                }

                app()->make(AdHocNotificationService::class)
                    ->setUserable($this->userable)
                    ->setTitle('Wallet Charge Transaction')
                    ->setMessage($message)
                    ->determineRecipients()
                    ->send();

            }

            return $wallet_transaction;

        } finally {
            optional($lock)->release();
        }

    }

    public function getWalletTransactionByReferenceNo($reference_no): Model
    {
        $wallet_transaction = $this->walletTransactionRepository->getWalletTransactionByReferenceNo($reference_no);

        if (!$wallet_transaction) {
            throw new \Exception(ErrorCodeHelper::getTranslatedErrorMessage(ErrorCodeHelper::WALLET_ERROR, 1004), 1004);
        }

        return $wallet_transaction;
    }

    // This method is customized for Qube(POS Vendor) purpose only
    public function getTerminalFromQubeRefNo(string $ref_no): ?Model
    {
        $exploded_ref_no = explode('-', $ref_no);

        if (count($exploded_ref_no) !== 3) {
            return null;
        }

        $terminal_code = $exploded_ref_no[1];

        return $this->terminalRepository->first(['code' => $terminal_code], false);
    }

    public function updateWalletTransactionStatusByPaymentGateway(
        PaymentGatewayLog $payment_gateway_log,
        WalletTransactionStatus $status
    ): ?WalletTransaction {
        $wallet_transaction = $payment_gateway_log->transactionLoggable;

        if (!$wallet_transaction) {
            throw new \Exception(ErrorCodeHelper::getTranslatedErrorMessage(ErrorCodeHelper::PAYMENT_GATEWAY_ERROR,
                2004), 2004);
        }

        if ($wallet_transaction->getMorphClass() != WalletTransaction::class) {
            throw new \Exception(ErrorCodeHelper::getTranslatedErrorMessage(ErrorCodeHelper::PAYMENT_GATEWAY_ERROR,
                2004), 2004);
        }

        // do nothing if updating to same status
        if ($wallet_transaction->status == $status) {
            return $wallet_transaction;
        }

        $this->setWalletById($wallet_transaction->wallet->id);

        $lock = $this->getWalletLock();

        try {
            if (!$lock->get()) {
                throw new \Exception(ErrorCodeHelper::getTranslatedErrorMessage(ErrorCodeHelper::WALLET_ERROR, 1005), 1005);
            }

            if ($wallet_transaction->status !== WalletTransactionStatus::PENDING) {
                throw new \Exception(ErrorCodeHelper::getTranslatedErrorMessage(ErrorCodeHelper::PAYMENT_GATEWAY_ERROR, 2003), 2003);
            }

            $wallet = $this->walletService
                ->setWallet($wallet_transaction->wallet)
                ->getWallet();

            $balance_before = $wallet->balance;
            $balance_after = $wallet->balance;

            if ($payment_gateway_log->status == PaymentStatus::SUCCESS) {
                $this->walletService->addBalance($payment_gateway_log->amount);
                $balance_after = bcadd($balance_before, $payment_gateway_log->amount, 2);
            }

            $this->walletTransactionRepository->update($wallet_transaction, [
                'status' => $status,
                'balance_before' => $balance_before,
                'balance_after' => $balance_after
            ]);

            return $wallet_transaction->refresh();

        } finally {
            optional($lock)->release();
        }
    }

    public function transferBalance($input): array
    {
        $sender_wallet = $this->getWallet()->load(['currency', 'user']);
        $sender_balance_before_transfer = $sender_wallet->balance;

        $receiver_wallet_ids = Arr::pluck($input['receivers'], 'wallet_id');
        $receiver_wallets = $this->walletRepository->getWalletByIds($receiver_wallet_ids)->load('user')->keyBy('id');

        $total_amount_to_be_transferred = abs(array_sum(Arr::pluck($input['receivers'], 'amount')));

        if (!$this->walletService->setWallet($sender_wallet)->hasEnoughBalance($total_amount_to_be_transferred)) {
            throw new \Exception(ErrorCodeHelper::getTranslatedErrorMessage(ErrorCodeHelper::WALLET_ERROR, 1002), 1002);
        }

        $receiver_wallet_transactions = [];

        foreach ($input['receivers'] as $receiver) {
            $receiver_userable = (new UserableFactory($receiver['userable_type']))->findOrFail($receiver['userable_id']);

            // TODO: Add this in after guardian and employee module merged

//        if (!$this->walletService->isAllowToTransfer($receiver)) {
//            throw new \Exception('You are not able to make transfer to this user.');
//        }

            $receiver_wallet = $receiver_wallets[$receiver['wallet_id']];
            $amount_to_transfer = $receiver['amount'];

            $payload = [
                'type' => WalletTransactionType::TRANSFER,
                'status' => WalletTransactionStatus::PENDING,
                'total_amount' => bcmul($amount_to_transfer, -1, 2), // Sender will have negative amount
                'description' => optional($receiver)['description'],
                'tax_type' => TaxType::FLAT_AMOUNT,
                'tax_value' => 0,
            ];

            // pre-generate running number outside DB transaction, else running number will clash.
            $prefix = WalletTransactionType::getReferenceNoPrefix(WalletTransactionType::TRANSFER);
            $payload['sender_running_number'] = $this->generateReferenceNumber($prefix, WalletTransactionType::TRANSFER->value);
            $payload['receiver_running_number'] = $this->generateReferenceNumber($prefix, WalletTransactionType::TRANSFER->value);

            DB::transaction(function () use (
                $sender_wallet,
                $receiver_wallet,
                $payload,
                $amount_to_transfer,
                &$receiver_wallet_transactions,
                $receiver_userable
            ) {

                $payload['remark'] = 'To ' . $receiver_userable->name;
                $payload['reference_no'] = $payload['sender_running_number'];
                $sender_wallet_transaction = $this->createWalletTransferTransaction($payload, $sender_wallet, $receiver_wallet);

                $payload['remark'] = 'From ' . $this->getUserable()->name;
                $payload['total_amount'] = $amount_to_transfer; // Receiver will have a positive amount
                $payload['reference_no'] = $payload['receiver_running_number'];
                $receiver_wallet_transaction = $this->createWalletTransferTransaction($payload, $receiver_wallet, $sender_wallet);

                $this->walletService
                    ->setWallet($sender_wallet)
                    ->addBalance($sender_wallet_transaction->total_amount);

                $this->walletService
                    ->setWallet($receiver_wallet)
                    ->addBalance($receiver_wallet_transaction->total_amount);

                $this->walletTransactionRepository->update($sender_wallet_transaction, [
                    'status' => WalletTransactionStatus::SUCCESS
                ]);

                $this->walletTransactionRepository->update($receiver_wallet_transaction, [
                    'status' => WalletTransactionStatus::SUCCESS
                ]);

                $receiver_wallet_transactions[] = [
                    'name' => $receiver_userable->name,
                    'wallet_id' => $receiver_wallet->id,
                    'balance_before' => $receiver_wallet_transaction->balance_before,
                    'balance_after' => $receiver_wallet_transaction->balance_after,
                    'total_amount' => $receiver_wallet_transaction->total_amount,
                    'currency' => $receiver_wallet->currency,
                ];
            });
        }

        $sender_balance_after_transfer = $sender_wallet->refresh()->balance;

        return [
            'sender_wallet_transaction' => [
                'name' => $this->getUserable()->name,
                'wallet_id' => $sender_wallet->id,
                'balance_before' => $sender_balance_before_transfer,
                'balance_after' => $sender_balance_after_transfer,
                'total_amount' => bcsub($sender_balance_after_transfer, $sender_balance_before_transfer, 2),
                'currency' => $sender_wallet->currency,
            ],
            'receiver_wallet_transactions' => $receiver_wallet_transactions,
        ];
    }

    /**
     * @param $input
     * @param  Wallet  $source_wallet  The wallet of the user initiating the transaction.
     * @param  Wallet  $target_wallet  The wallet receiving the transaction.
     * @return Model|null Returns the created transaction model on success, null on failure (e.g., insufficient funds, currency mismatch).
     */
    public function createWalletTransferTransaction($input, Wallet $source_wallet, Wallet $target_wallet): ?Model
    {
        $payload = [
            'reference_no' => $input['reference_no'],
            'type' => WalletTransactionType::TRANSFER,
            'status' => WalletTransactionStatus::PENDING,
            'total_amount' => $input['total_amount'],
            'description' => $input['description'],
            'remark' => $input['remark'],
            'amount_before_tax' => $input['total_amount'],
            'amount_after_tax' => $input['total_amount'],
            'tax_type' => TaxType::FLAT_AMOUNT,
            'tax_value' => 0,
        ];

        return $this->createWalletTransaction($payload, $source_wallet, $target_wallet);
    }

    public function depositRequest($input): Model
    {
        $lock = $this->getWalletLock();

        try {
            if (!$lock->get()) {
                throw new \Exception(ErrorCodeHelper::getTranslatedErrorMessage(ErrorCodeHelper::WALLET_ERROR, 1005),
                    1005);
            }

            // generate ref number outside to lock in the reference number, prevent race condition
            $reference_number = $this->generateReferenceNumber(WalletTransactionType::getReferenceNoPrefix(WalletTransactionType::DEPOSIT), WalletTransactionType::DEPOSIT->value);

            [$payment_gateway_log, $wallet_transaction, $billing_document] = DB::transaction(function () use ($input, &$reference_number) {
                $wallet_transaction = $this->createWalletDepositTransaction($input['amount'], $reference_number);

                // create billing document
                $line_item = app()->make(BillingDocumentLineItemService::class)
                    ->setBillableItem($wallet_transaction)
                    ->setGlAccountCode(GlAccount::CODE_EWALLET)
                    ->setDescription('E-Wallet Topup')
                    ->setAmountBeforeTax($input['amount'])
                    ->setProduct(SystemHelper::getEWalletTopupProduct())
                    ->setCurrency(config('school.currency_code'))
                    ->make();

                $invoice_service = app()->make(BillingDocumentService::class);
                $billing_document = $invoice_service->init()
                    ->setType(BillingDocument::TYPE_INVOICE)
                    ->setSubType(BillingDocument::SUB_TYPE_WALLET_TOPUP)
                    ->setDefaultValuesForWalletTopup()
                    ->setCurrency(config('school.currency_code'))
                    ->setStatus(BillingDocument::STATUS_CONFIRMED)
                    ->setBillToParty($this->getUserable())
                    ->addLineItem($line_item)
                    ->calculateAmountBeforeTax()
                    ->applyTax(SystemHelper::getNotApplicableTax())
                    ->calculatePaymentDueDate()
                    ->generateReferenceNumber()
                    ->create()
                    ->getBillingDocument();

                $payment_gateway_log = $this->paymentGatewayLogService
                    ->setProvider(PaymentProvider::PAYEX) // TODO: To add other provider in the future if any
                    ->setUser($this->getUser())
                    ->setTransactionLoggable($wallet_transaction)
                    ->setBillingDocument($billing_document)
                    ->setPaymentMethod(PaymentMethod::where('code', $input['payment_type'])->firstOrFail())
                    ->createWalletDepositTransaction($input['amount']);

                return [$payment_gateway_log, $wallet_transaction, $billing_document];

            });

            // TODO: Lucas need to fix this, might have error if payex fail coz order id is returned from here
            $payment_gateway_response = $this->paymentGatewayService
                ->setProvider(PaymentProvider::PAYEX) // TODO: To add other provider in the future if any
                ->setPaymentTypes([$input['payment_type']]) // TODO: To add in other provider in the future if any
                ->setCustomerEmail($input['customer_email'] ?? null)
                ->setCustomerName($input['customer_name'])
                ->setPaymentGatewayLog($payment_gateway_log)
                ->setReturnUrl($input['return_url'] ?? null)
                ->request();

            $this->paymentGatewayLogService
                ->setRequestData($payment_gateway_response['request_data'])
                ->setResponseData($payment_gateway_response['response_data'])
                ->setOrderId($payment_gateway_response['order_id']);

            if (Arr::get($payment_gateway_response, 'status') == PaymentStatus::FAILED) {

                $this->walletTransactionRepository->update($wallet_transaction, [
                    'status' => WalletTransactionStatus::FAILED
                ]);

                $this->paymentGatewayLogService
                    ->setRemark($payment_gateway_response['remark'])
                    ->setStatus(PaymentStatus::FAILED)
                    ->updatePaymentGatewayLog();

                // void invoice if payment gateway failed for wallet transaction, no retry
                $billing_document_service = BillingDocumentFactory::getInstance($billing_document->type);

                $billing_document_service->setBillingDocument($billing_document)
                    ->changeStatusTo(BillingDocument::STATUS_VOIDED);

                throw new \Exception($payment_gateway_response['remark']);
            }

            $payment_gateway_log = $this->paymentGatewayLogService
                ->setPaymentUrl($payment_gateway_response['payment_url'])
                ->updatePaymentGatewayLog();

            return $payment_gateway_log;

        } finally {
            optional($lock)->release();
        }

    }

    public function createWalletDepositTransaction($amount, $reference_number): ?Model
    {
        $payload = [
            'reference_no' => $reference_number,
            'type' => WalletTransactionType::DEPOSIT,
            'status' => WalletTransactionStatus::PENDING,
            'total_amount' => $amount,
            'description' => "Deposit from payment gateway",
            'amount_before_tax' => $amount,
            'amount_after_tax' => $amount,
            'tax_type' => TaxType::FLAT_AMOUNT,
            'tax_value' => 0,
        ];

        return $this->createWalletTransaction($payload, $this->getWallet(), $this->getWallet());
    }

    /**
     * @throws \Exception
     */
    public function getAllPaginatedWalletTransactionsByWallet(
        User $user,
        Wallet $wallet,
        array $filters = []
    ): LengthAwarePaginator {
        if (!$user->canAccessBalanceForWallet($wallet->id)) {
            ErrorCodeHelper::throwError(ErrorCodeHelper::WALLET_ERROR, 1006);
        }

        $filters = array_merge($filters, ['wallet_id' => $wallet->id]);
        $filters['with'] = ['wallet.currency'];

        return $this->walletTransactionRepository->getAllPaginated($filters);
    }

    public function getAllPaginatedWalletTransactions(array $filters = []): LengthAwarePaginator
    {
        return $this->walletTransactionRepository->getAllPaginated($filters);
    }

    public function getAllWalletTransactions(array $filters = []): Collection
    {
        return $this->walletTransactionRepository->getAll($filters);
    }

    public function walletAdjustment(): static
    {
        $wallet = $this->getWallet()->load(['currency', 'user', 'userables']);
        $wallet_service = $this->walletService->setWallet($wallet);

        if ($this->getAmount() < 0 && !$wallet_service->hasEnoughBalance($this->getAmount())) {
            ErrorCodeHelper::throwError(ErrorCodeHelper::WALLET_ERROR, 1002);
        }

        $lock = $this->getWalletLock();

        try {
            if (!$lock->get()) {
                ErrorCodeHelper::throwError(ErrorCodeHelper::WALLET_ERROR, 1005);
            }

            DB::transaction(function () use ($wallet) {

                $type = $this->data['type'];
                $reference_no = $this->generateReferenceNumber(WalletTransactionType::getReferenceNoPrefix($type), $type->value);

                $payload = [
                    'reference_no' => $reference_no,
                    'type' => $type,
                    'status' => WalletTransactionStatus::PENDING,
                    'total_amount' => $this->getAmount(),
                    'description' => Arr::get($this->data, 'description'),
                    'remark' => Arr::get($this->data, 'remark'),
                    'amount_before_tax' => $this->getAmount(),
                    'amount_after_tax' => $this->getAmount(),
                    'tax_type' => TaxType::FLAT_AMOUNT,
                    'tax_value' => 0,
                ];

                $wallet_transaction = $this->createWalletTransaction($payload, $wallet, $wallet);

                $this->walletService
                    ->addBalance($wallet_transaction->total_amount);

                $this->walletTransactionRepository->update($wallet_transaction, [
                    'status' => WalletTransactionStatus::SUCCESS
                ]);

                $this->setTransaction($wallet_transaction);
            });

            return $this;

        } finally {
            optional($lock)->release();
        }
    }

    public function sendWithdrawBalanceNotification(): static
    {
        $amount = number_format(abs($this->getAmount()), 2);
        $title = 'Wallet Balance Withdraw';
        $message = "RM{$amount} from your wallet has been withdrawn successfully.";

        return $this->sendWalletNotification($title, $message);
    }

    public function sendAdjustmentBalanceNotification(): static
    {
        $amount = ($this->getAmount() < 0 ? '-' : '') . 'RM' . number_format(abs($this->getAmount()), 2);
        $title = 'Wallet Balance Adjustment';
        $message = "$amount from your wallet has been adjusted successfully.";

        return $this->sendWalletNotification($title, $message);
    }

    public function sendWalletNotification(string $title, string $message): self
    {
        $wallet = $this->getWallet()->load(['userables']);

        // Prioritize student userable
        $receiver = $wallet->userables->where('userable_type', Student::class)->first();

        // If no userable found, use first userable
        if (!$receiver) {
            $receiver = $wallet->userables->first();
        }

        $userable = $receiver->userable;

        app()->make(AdHocNotificationService::class)
            ->setUserable($userable)
            ->setTitle($title)
            ->setMessage($message)
            ->determineRecipients()
            ->send();

        return $this;
    }

    public function refundWalletTransaction(array $input = []): WalletTransaction
    {
        $wallet_transaction = $this->getWalletTransaction();
        $this->setWalletById($wallet_transaction->wallet_id);
        $this->walletService->setWallet($wallet_transaction->wallet);
        $lock = $this->getWalletLock();

        try {
            if (!$lock->get()) {
                throw new \Exception(ErrorCodeHelper::getTranslatedErrorMessage(ErrorCodeHelper::WALLET_ERROR, 1005), 4005);
            }
            return DB::transaction(function () use ($wallet_transaction, $input) {
                $input['amount_after_tax'] = abs($wallet_transaction->amount_after_tax);
                $input['amount_before_tax'] = abs($wallet_transaction->amount_before_tax);
                $input['total_amount'] = $input['amount_after_tax'];
                $input['card_id'] = $wallet_transaction->card_id;
                $input['description'] = 'Transaction Refunded';
                $input['remark'] = $input['remark'] ?? 'Transaction Refunded';
                $input['reference_no'] = $wallet_transaction->reference_no . '-REFUND';
                $input['type'] = WalletTransactionType::REFUND->value;
                $input['status'] = WalletTransactionStatus::SUCCESS->value;
                $input['tax_type'] = TaxType::FLAT_AMOUNT->value;
                $input['tax_value'] = 0;

                $wallet_transaction = $this->setType(WalletTransactionType::REFUND)
                    ->setUserable($wallet_transaction->userable)
                    ->createWalletTransaction($input, $this->getWallet(), $this->getWalletTransactable());

                $this->walletService->addBalance($input['amount_after_tax']);
                return $wallet_transaction;
            });
        } finally {
            optional($lock)->release();
        }
    }

    public function generateReferenceNumber($prefix, $type)
    {
        $service = app()->make(DocumentRunningNumberService::class);

        return $service
            ->setDocumentType(WalletTransaction::class)
            ->setYear(now()->year)
            ->setCustomPadding(7)
            ->addCustomComponent($prefix)
            ->setIdentifier1($type)
            ->addPresetComponent(DocumentRunningNumberService::PLACEHOLDER_YEAR)
            ->addPresetComponent(DocumentRunningNumberService::PLACEHOLDER_RUNNING_NUMBER)
            ->generate();
    }

    public function setWalletById(int $wallet_id): self
    {
        $this->wallet = $this->walletRepository->find($wallet_id);

        return $this;
    }

    public function getUserable(): Userable
    {
        return $this->userable;
    }

    public function setUserable(Userable $userable): WalletTransactionService
    {
        $this->userable = $userable;
        return $this;
    }

    public function getUser(): User|Authenticatable|Model
    {
        return $this->user;
    }

    public function setUser(User|Authenticatable|Model $user): self
    {
        $this->user = $user;

        return $this;
    }

    public function setUserableByWallet()
    {
        $userable = $this->wallet->userables->first();

        if (!$this->wallet || !$userable) {
            return $this;
        }

        $this->setUserableByTypeAndId($userable->userable_type, $userable->userable_id);

        return $this;
    }

    public function validateCanManualRefund(): self
    {
        $wallet_transaction = $this->getWalletTransaction();

        if ($wallet_transaction->canRefund()) {
            return $this;
        }

        ErrorCodeHelper::throwError(ErrorCodeHelper::WALLET_ERROR, 1012);
    }

    public function setAmount(float $amount): self
    {
        $this->amount = $amount;
        return $this;
    }

    public function getAmount(): float
    {
        return $this->amount;
    }

    private function getWalletLock(): Lock
    {
        return Cache::lock('wallet-lock-' . get_class($this->getWallet()) . '-' . $this->getWallet()->getKey(),
            self::WALLET_LOCK_RELEASE_IN_SECONDS);
    }
}
