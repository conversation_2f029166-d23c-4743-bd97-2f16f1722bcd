<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class SimpleEmployeeResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'employee_number' => (string) $this->employee_number ?: null,
            'userable_reference_number' => $this->getUserNumber(),
            'badge_no' => (string) $this->badge_no ?: null,
            'phone_number' => $this->phone_number,
            'email' => $this->email,
            'nric' => $this->nric,
            'status' => $this->status?->value,
            'job_title' => new EmployeeJobTitleResource($this->whenLoaded('jobTitle')),
            'gender' => $this->gender?->value,
            'photo' => $this->relationLoaded('media') ? ((string) $this->photo ?: null) : null,
            'translations' => $this->translations,
            'active_hostel_bed_assignment' => $this->whenLoaded('activeHostelBedAssignments', function () {
                return new HostelBedAssignmentResource($this->activeHostelBedAssignments->first());
            }),
        ];
    }
}
