<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class LeaveApplicationTypeResource extends JsonResource
{
    public function toArray(Request $request)
    {
        return [
            'id' => $this->id,
            'translations' => $this->translations,
            'is_present' => (bool) $this->is_present,
            'average_point_deduction' => (float) $this->average_point_deduction,
            'conduct_point_deduction' => (float) $this->conduct_point_deduction,
            'display_in_report_card' => $this->display_in_report_card
        ];
    }
}