<?php

namespace App\Http\Resources;

use App\Models\Contractor;
use App\Models\Employee;
use App\Models\Guardian;
use App\Models\Merchant;
use App\Models\Student;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class UnpaidItemResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        if ($this->relationLoaded('billTo')) {
            switch (get_class($this->billTo)) {
                case Student::class:
                    $bill_to = new SimpleStudentResource($this->billTo);
                    break;

                case Guardian::class:
                    $bill_to = new SimpleGuardianResource($this->billTo);
                    break;

                case Employee::class:
                    $bill_to = new SimpleEmployeeResource($this->billTo);
                    break;

                case Contractor::class:
                    $bill_to = new SimpleContractorResource($this->billTo);
                    break;

                case Merchant::class:
                    $bill_to = new SimpleMerchantResource($this->billTo);
                    break;

            }
        }

        return [
            'id' => $this->id,
            'bill_to' => $bill_to ?? null,
            'bill_to_type' => $this->bill_to_type,
            'bill_to_id' => $this->bill_to_id,
            'status' => $this->status,
            'description' => $this->description,
            'product' => new ProductResource($this->whenLoaded('product')),
            'gl_account_code' => $this->gl_account_code,
            'period' => $this->period,
            'currency_code' => $this->currency_code,
            'unit_price' => (float) $this->unit_price,
            'quantity' => (int) $this->quantity,
            'amount_before_tax' => (float) $this->amount_before_tax,
            'amount_before_tax_after_discount' => isset($this->amount_before_tax_after_discount) ? (float) $this->amount_before_tax_after_discount : null,
            'created_by_employee' => new SimpleEmployeeResource($this->whenLoaded('createdByEmployee')),
            'billing_document' => new BillingDocumentResource($this->whenLoaded('billingDocument')),
            'paid_at' => $this->paid_at,
            'discounts' => !empty($this->discounts) ? $this->discounts : null,
        ];
    }
}
