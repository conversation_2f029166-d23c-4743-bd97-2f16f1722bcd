<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class SimpleEnrollmentResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'email' => $this->email,
            'phone_number' => $this->phone_number,
            'nric' => $this->nric,
            'passport_number' => $this->passport_number,
            'admission_year' => $this->admission_year,
            'student_number' => $this->student_number,
            'birthplace' => $this->birthplace,
            'gender' => $this->gender->value,
            'is_hostel' => $this->is_hostel,
            'is_active' => $this->is_active,
            'enrollment_status' => $this->enrollment_status,
            'payment_status' => $this->payment_status,
            'translations' => $this->translations,
        ];
    }
}
