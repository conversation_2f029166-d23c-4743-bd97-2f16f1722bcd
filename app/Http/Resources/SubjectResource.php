<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class SubjectResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'type' => $this->type,
            'club' => $this->club ? new ClubResource($this->club) : null,
            'code' => $this->code,
            'name' => $this->name,
            'translations' => $this->translations,
            'semester_class_ids' =>  $this->whenLoaded('classSubjects', function () {
                return $this->classSubjects->pluck('semester_class_id');
            }),
        ];
    }
}
