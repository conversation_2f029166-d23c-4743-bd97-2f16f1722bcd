<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ResultsPostingHeaderResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'report_card_output_code' => $this->report_card_output_code,
            'report_card_template_service' => $this->report_card_template_service,
            'grade' => new GradeResource($this->whenLoaded('grade')),
            'status' => $this->status,
            'errors' => $this->errors,
            'posted_at' => $this->posted_at?->toISOString(),
            'processing_start' => $this->processing_start?->toISOString(),
            'processing_end' => $this->processing_end?->toISOString(),
            'semester_setting' => new SemesterSettingResource($this->whenLoaded('semesterSetting')),
            'posted_by_employee' => new EmployeeResource($this->whenLoaded('postedByEmployee'))
        ];
    }
}
