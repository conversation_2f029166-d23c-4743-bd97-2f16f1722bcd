<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class UserProfileResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => (integer) $this->id,
            'email' => (string) $this->email,
            'phone_number' => (string) $this->phone_number,
            'is_active' => (boolean) $this->is_active,
            'last_login_at' => (string) $this->last_login_at,
            'direct_userables' => $this->whenLoaded('userables',
                fn() => UserableViewResource::collection($this->userables)),
            'accessible_student_userables' => $this->whenLoaded('userables',
                fn() => UserableViewResource::collection($this->getStudentUserables())),
            'userables' => $this->whenLoaded('userables',
                fn() => UserableViewResource::collection($this->getAllUserables())),
            'roles' => RoleResource::collection($this->whenLoaded('roles')),
            'permissions' => $this->whenLoaded('permissions',
                fn() => PermissionResource::collection($this->getAllPermissions())),
            'is_password_reset_required' => $this->is_password_reset_required,
            'user_inboxes_unread_count' => $this->whenHas('user_inboxes_unread_count')
        ];
    }
}
