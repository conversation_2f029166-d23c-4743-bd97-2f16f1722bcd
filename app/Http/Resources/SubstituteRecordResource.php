<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class SubstituteRecordResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'timeslot_id' => $this->timeslot_id,
            'substitute_date' => $this->substitute_date,
            'day' => $this->day,
            'period' => $this->period->period,
            'period_label' => $this->when(
                $this->relationLoaded('period') && $this->period->relationLoaded('periodGroup') && $this->period->periodGroup->relationLoaded('periodLabels'),
                $this->period->periodGroup->periodLabels->where('period', $this->period->period)->first()?->name
            ),
            'from_time' => $this->period->from_time,
            'to_time' => $this->period->to_time,
            'class_name' => $this->classSubject->semesterClass->classModel->getTranslations('name'),
            'subject_name' => $this->classSubject->subject->getTranslations('name'),
            'remarks' => $this->remarks,
            'substitute_teacher' => new SimpleEmployeeResource($this->whenLoaded('substituteTeacher')),
            'requestor' => new SimpleEmployeeResource($this->whenLoaded('requestor')),
            'allowance' => (float) $this->allowance,
        ];
    }
}
