<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class SimpleStudentResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'student_number' => $this->student_number,
            'userable_reference_number' => $this->getUserNumber(),
            'name' => $this->name,
            'gender' => $this->gender,
            'translations' => $this->translations,
            'leave_status' => $this->leave_status,
            'active_hostel_bed_assignment' => $this->whenLoaded('activeHostelBedAssignments', function () {
                return new HostelBedAssignmentResource($this->activeHostelBedAssignments->first());
            }),
            'society_positions' => $this->whenLoaded('societyPositions', function () use ($request) {
                $request = $request->all();

                if (isset($request['semester_class_id'])) {
                    $positions = $this->societyPositions->where('semester_class_id', $request['semester_class_id']);
                } else {
                    $positions = $this->societyPositions;
                }

                return StudentSocietyPositionResource::collection($positions);
            }),
            'is_active' => $this->is_active,
            'attendances' => $this->whenLoaded('attendances', function () {
                return $this->attendances ? new AttendanceResource($this->attendances->first()) : [];
            }),
            'primary_class' => $this->whenLoaded('primaryClassAndGradeBySemesterSetting', function() {
                return ClassAndGradeResource::make($this->primaryClassAndGradeBySemesterSetting->first());
            })
        ];
    }
}
