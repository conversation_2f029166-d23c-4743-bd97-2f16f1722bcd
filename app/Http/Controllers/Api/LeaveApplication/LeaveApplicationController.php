<?php

namespace App\Http\Controllers\Api\LeaveApplication;

use App\Enums\LeaveApplicationStatus;
use App\Helpers\ErrorCodeHelper;
use App\Http\Controllers\Controller;
use App\Http\Requests\Api\LeaveApplication\LeaveApplicationBatchUpdateStatusRequest;
use App\Http\Requests\Api\LeaveApplication\LeaveApplicationCreateRequest;
use App\Http\Requests\Api\LeaveApplication\LeaveApplicationHistoryRequest;
use App\Http\Requests\Api\LeaveApplication\LeaveApplicationIndexRequest;
use App\Http\Requests\Api\LeaveApplication\LeaveApplicationPreviewStatusChangeRequest;
use App\Http\Requests\Api\LeaveApplication\LeaveApplicationUpdateRequest;
use App\Http\Requests\Api\LeaveApplication\LeaveApplicationUpdateStatusRequest;
use App\Http\Resources\ApiResponse;
use App\Http\Resources\AttendancePeriodOverrideResource;
use App\Http\Resources\LeaveApplicationResource;
use App\Models\LeaveApplication;
use App\Models\PeriodGroup;
use App\Repositories\LeaveApplicationTypeRepository;
use App\Repositories\StudentRepository;
use App\Services\LeaveApplicationService;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class LeaveApplicationController extends Controller
{
    protected LeaveApplicationService $leaveApplicationService;
    protected LeaveApplicationTypeRepository $leaveApplicationTypeRepository;
    protected StudentRepository $studentRepository;

    public function __construct(
        LeaveApplicationService $leaveApplicationService,
        LeaveApplicationTypeRepository $leaveApplicationTypeRepository,
        StudentRepository $studentRepository,
    ) {
        $this->leaveApplicationService = $leaveApplicationService;
        $this->leaveApplicationTypeRepository = $leaveApplicationTypeRepository;
        $this->studentRepository = $studentRepository;
    }

    public function index(LeaveApplicationIndexRequest $request): JsonResponse
    {
        $input = $request->validated();

        $input['includes'] = array_merge($input['includes'] ?? [], ['leaveApplicable', 'leaveApplicationType']);
        $input['includes'] = array_unique($input['includes']);

        $data = $this->leaveApplicationService->getAllPaginatedLeaveApplications($input);

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->setData(LeaveApplicationResource::collection($data))
            ->setPagination($data)
            ->getResponse();
    }

    public function show(LeaveApplication $leave_application): JsonResponse
    {
        $leave_application->loadMissing(['leaveApplicable', 'leaveApplicationPeriods', 'leaveApplicationType']);

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->setData(new LeaveApplicationResource($leave_application))
            ->getResponse();
    }

    public function create(LeaveApplicationCreateRequest $request): JsonResponse
    {
        $input = $request->validated();

        $student_list_with_period_label_ids = [];

        $period_groups = PeriodGroup::all();

        foreach ($input['period_group_students'] as $period_group_student) {
            $period_group = $period_groups->where('id', $period_group_student['period_group_id'])->firstOrFail();
            $unique_period_label_ids = array_unique($period_group_student['period_label_ids']);

            $this->leaveApplicationService->validatePeriodLabelsByPeriodGroup($unique_period_label_ids, $period_group);
            $is_full_day = $this->leaveApplicationService->determineIsFullDay($period_group, $unique_period_label_ids);

            $students = $this->studentRepository->getByIds($period_group_student['student_ids']);
            foreach ($students as $student) {
                $student_list_with_period_label_ids[] = [
                    'student' => $student,
                    'period_label_ids' => $period_group_student['period_label_ids'],
                    'is_full_day' => $is_full_day,
                ];
            }
        }
        $type = $this->leaveApplicationTypeRepository->findOrFail($input['leave_application_type_id']);

        $leave_applications = [];

        if (isset($input['proof'])) {
            $this->leaveApplicationService->setProof($input['proof']);
        }

        DB::transaction(function () use ($student_list_with_period_label_ids, $type, &$leave_applications, $input) {
            foreach ($student_list_with_period_label_ids as $student_with_period_label_ids) {
                $leave_applications[] = $this->leaveApplicationService
                    ->setLeaveApplicable($student_with_period_label_ids['student'])
                    ->setLeaveApplicationType($type)
                    ->setReason($input['reason'])
                    ->setAveragePointDeduction($input['average_point_deduction'])
                    ->setConductPointDeduction($input['conduct_point_deduction'])
                    ->setRemarks(isset($input['remarks']) ? $input['remarks'] : null)
                    ->setFromDate($input['from_date'])
                    ->setToDate($input['to_date'])
                    ->setIsPresent($input['is_present'])
                    ->setPeriodLabelIds($student_with_period_label_ids['period_label_ids'])
                    ->setIsFullDay($student_with_period_label_ids['is_full_day'])
                    ->validateLeaveApplicationPeriodExistence(true)
                    ->createLeaveApplication();
            }

            if (Auth::user()->can('leave-application-approve') &&
                Auth::user()->isEmployee() &&
                count($leave_applications) > 0
            ) {
                $leave_application_ids = collect($leave_applications)->pluck('id')->toArray();
                $this->leaveApplicationService
                    ->setEmployee(Auth::user()->employee)
                    ->updateStatus($leave_application_ids, LeaveApplicationStatus::APPROVED->value);
            }
        });

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->getResponse();
    }

    public function update(LeaveApplicationUpdateRequest $request, LeaveApplication $leave_application): JsonResponse
    {
        $input = $request->validated();

        $type = $this->leaveApplicationTypeRepository->findOrFail($input['leave_application_type_id']);
        $reason = $input['reason'];
        $remarks = isset($input['remarks']) ? $input['remarks'] : $leave_application->remarks;
        $is_present = $input['is_present'];
        $this->leaveApplicationService->validateLeaveApplicationIsEditable($leave_application);

        if (isset($input['proof'])) {
            $this->leaveApplicationService->setProof($input['proof']);
        }

        $this->leaveApplicationService->setUpdateProof(in_array('proof', array_keys($input)));

        $period_group = PeriodGroup::findOrFail($input['period_group_id']);
        $unique_period_label_ids = array_unique($input['period_label_ids']);
        $this->leaveApplicationService->validatePeriodLabelsByPeriodGroup($unique_period_label_ids, $period_group);
        $is_full_day = $this->leaveApplicationService->determineIsFullDay($period_group, $unique_period_label_ids);

        $this->leaveApplicationService
            ->setLeaveApplication($leave_application)
            ->setLeaveApplicationType($type)
            ->setLeaveApplicable($leave_application->leaveApplicable)
            ->setReason($reason)
            ->setRemarks($remarks)
            ->setIsPresent($is_present)
            ->setAveragePointDeduction($input['average_point_deduction'])
            ->setConductPointDeduction($input['conduct_point_deduction'])
            ->setFromDate($input['from_date'])
            ->setToDate($input['to_date'])
            ->setPeriodLabelIds($unique_period_label_ids)
            ->setIsFullDay($is_full_day)
            ->validateLeaveApplicationPeriodExistence(false)
            ->updateLeaveApplication();

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->getResponse();
    }


    public function updateStatus(LeaveApplicationUpdateStatusRequest $request, LeaveApplication $leave_application): JsonResponse
    {
        $input = $request->validated();

        $employee = Auth::user()->employee;
        if (!$employee) {
            ErrorCodeHelper::throwError(ErrorCodeHelper::ATTENDANCE_ERROR, 42004);
        }

        $this->leaveApplicationService
            ->setEmployee($employee)
            ->updateStatus([$leave_application->id], $input['status']);

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->getResponse();
    }


    public function updateStatusBatch(LeaveApplicationBatchUpdateStatusRequest $request)
    {

        $input = $request->validated();

        $employee = Auth::user()->employee;
        if (!$employee) {
            ErrorCodeHelper::throwError(ErrorCodeHelper::ATTENDANCE_ERROR, 42004);
        }

        $this->leaveApplicationService
            ->setEmployee($employee)
            ->updateStatus($input['leave_application_ids'], $input['status']);

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->getResponse();
    }

    public function destroy(LeaveApplication $leave_application): JsonResponse
    {
        DB::transaction(function () use ($leave_application) {
            if ($leave_application->status != LeaveApplicationStatus::PENDING) {
                $employee = Auth::user()->employee;
                if (!$employee) {
                    ErrorCodeHelper::throwError(ErrorCodeHelper::ATTENDANCE_ERROR, 42004);
                }

                $this->leaveApplicationService
                    ->setEmployee($employee)
                    ->updateStatus([$leave_application->id], LeaveApplicationStatus::PENDING->value);

                $leave_application->refresh();
            }

            $this->leaveApplicationService->setLeaveApplication($leave_application)->deleteLeaveApplication();
        });

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->getResponse();
    }

    public function previewLeaveApplicationStatusChange(LeaveApplicationPreviewStatusChangeRequest $request): JsonResponse
    {
        $input = $request->validated();
        $data = $this->leaveApplicationService->validateStatusChange($input['leave_application_ids'], $input['status']);

        $output = collect([]);

        foreach ($data as $d) {
            $temp = $d;

            // massage data using resource
            if (isset($d['leave_application'])) {
                $temp['leave_application'] = LeaveApplicationResource::make($d['leave_application']);
            }
            if (isset($d['attendance_period_overrides'])) {
                $temp['attendance_period_overrides'] = AttendancePeriodOverrideResource::collection($d['attendance_period_overrides']);
            }

            $output->push($temp);
        }

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->setData($output)
            ->getResponse();
    }

    public function getLeaveApplicationHistory(LeaveApplicationHistoryRequest $request): JsonResponse
    {
        $input = $request->validated();

        $data = $this->leaveApplicationService
            ->setLeaveApplicationStatus($input['status'] ?? null)
            ->setFromDate($input['date_from'] ?? null)
            ->setToDate($input['date_to'] ?? null)
            ->getLeaveApplicationHistory($input['leave_applicable_type'], $input['leave_applicable_id']);

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->setData($data)
            ->getResponse();
    }
}

