<?php

namespace App\Http\Controllers\Api\LeaveApplication;

use App\Http\Controllers\Controller;
use App\Http\Requests\Api\LeaveApplication\LeaveApplicationTypeCreateRequest;
use App\Http\Requests\Api\LeaveApplication\LeaveApplicationTypeIndexRequest;
use App\Http\Requests\Api\LeaveApplication\LeaveApplicationTypeUpdateRequest;
use App\Http\Resources\ApiResponse;
use App\Http\Resources\LeaveApplicationTypeResource;
use App\Models\LeaveApplicationType;
use App\Services\LeaveApplicationTypeService;
use Illuminate\Http\JsonResponse;

class LeaveApplicationTypeController extends Controller
{
    protected LeaveApplicationTypeService $leaveApplicationTypeService;

    public function __construct(
        LeaveApplicationTypeService $leaveApplicationTypeService,
    ) {
        $this->leaveApplicationTypeService = $leaveApplicationTypeService;
    }

    public function index(LeaveApplicationTypeIndexRequest $request): JsonResponse
    {
        $input = $request->validated();

        $response = (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200);

        if (isset($input['per_page']) && $input['per_page'] == -1) {
            $data = $this->leaveApplicationTypeService->getAllLeaveApplicationTypes($input);
        } else {
            $data = $this->leaveApplicationTypeService->getAllPaginatedLeaveApplicationTypes($input);
            $response->setPagination($data);
        }

        return $response
            ->setData(LeaveApplicationTypeResource::collection($data))
            ->getResponse();
    }

    public function show(LeaveApplicationType $leave_application_type): JsonResponse
    {
        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->setData(new LeaveApplicationTypeResource($leave_application_type))
            ->getResponse();
    }

    public function create(LeaveApplicationTypeCreateRequest $request): JsonResponse
    {
        $input = $request->validated();

        $leave_application_type = $this->leaveApplicationTypeService
            ->setName($input['name'])
            ->setIsPresent($input['is_present'])
            ->setAveragePointDeduction($input['average_point_deduction'])
            ->setConductPointDeduction($input['conduct_point_deduction'])
            ->setDisplayInReportCard($input['display_in_report_card'])
            ->createLeaveApplicationType();

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->setData(new LeaveApplicationTypeResource($leave_application_type))
            ->getResponse();
    }

    public function update(LeaveApplicationType $leave_application_type, LeaveApplicationTypeUpdateRequest $request): JsonResponse
    {
        $input = $request->validated();

        $leave_application_type = $this->leaveApplicationTypeService
            ->setLeaveApplicationType($leave_application_type)
            ->setName($input['name'])
            ->setIsPresent($input['is_present'])
            ->setAveragePointDeduction($input['average_point_deduction'])
            ->setConductPointDeduction($input['conduct_point_deduction'])
            ->setDisplayInReportCard($input['display_in_report_card'])
            ->updateLeaveApplicationType();

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->setData(new LeaveApplicationTypeResource($leave_application_type))
            ->getResponse();
    }

    public function destroy(LeaveApplicationType $leave_application_type): JsonResponse
    {
        $this->leaveApplicationTypeService
            ->setLeaveApplicationType($leave_application_type)
            ->deleteLeaveApplicationType();

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->getResponse();
    }

}

