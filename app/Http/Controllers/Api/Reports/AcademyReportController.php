<?php

namespace App\Http\Controllers\Api\Reports;

use App\Enums\ExportType;
use App\Exports\GenericExcelExportViaView;
use App\Factories\ExportAdapterFactory;
use App\Helpers\FileHelper;
use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Reports\Academy\AllPassedStudentReportRequest;
use App\Http\Requests\Api\Reports\Academy\BestGradeByClassReportRequest;
use App\Http\Requests\Api\Reports\Academy\BestGradeByGradeReportRequest;
use App\Http\Requests\Api\Reports\Academy\BestGradeBySubjectReportRequest;
use App\Http\Requests\Api\Reports\Academy\ExaminationResultByExamReportRequest;
use App\Http\Requests\Api\Reports\Academy\ExaminationResultBySemesterClassRequest;
use App\Http\Requests\Api\Reports\Academy\ExaminationResultByStudentReportRequest;
use App\Http\Requests\Api\Reports\Academy\NetAverageReportDataRequest;
use App\Http\Requests\Api\Reports\Academy\StudentAnalysisReportBySemesterRequest;
use App\Http\Requests\Api\Reports\Academy\SubjectAnalysisReportDataRequest;
use App\Http\Requests\Api\Reports\Academy\SubjectAverageMarkReportDataRequest;
use App\Http\Requests\Api\Reports\Academy\SubjectPassingRateReportDataRequest;
use App\Http\Requests\Api\Reports\Academy\StudentMeritAndExceptionalPerformanceReportRequest;
use App\Http\Requests\Api\Reports\Academy\TransferredStudentListByAdmissionYearRequest;
use App\Http\Resources\ApiResponse;
use App\Models\Exam;
use App\Models\ResultsPostingHeader;
use App\Models\SemesterClass;
use App\Models\SemesterSetting;
use App\Repositories\ExamRepository;
use App\Repositories\SemesterClassRepository;
use App\Repositories\StudentRepository;
use App\Services\DocumentPrintService;
use App\Services\ReportPrintService;
use App\Services\Reports\AcademyReportService;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Arr;

class AcademyReportController extends Controller
{
    public function __construct(
        protected StudentRepository       $studentRepository,
        protected SemesterClassRepository $semesterClassRepository,
        protected ReportPrintService      $reportPrintService,
    protected ExamRepository $examRepository,
        protected AcademyReportService $academyReportService
    ) {
    }

    public function transferredStudentListByAdmissionYear(TransferredStudentListByAdmissionYearRequest $request): JsonResponse
    {
        $filters = $request->validated();

        app()->setLocale($filters['report_language']);

        $data = $this->studentRepository->transferredStudentListByAdmissionYearData($filters['admission_year']);

        $export_type = Arr::get($filters, 'export_type');

        if (!$export_type) {
            return (new ApiResponse())
                ->setMessage(__('api.common.success'))
                ->setCode(200)
                ->setData($data)
                ->getResponse();
        }

        $report_data = [
            'data' => $data,
        ];

        $report_view_name = 'reports.academy.transferred-student-list';
        $file_name = 'academy-transferred-student-list';

        $export_type = ExportType::from($export_type);
        $report_view = view($report_view_name, $report_data);

        $adapter = ExportAdapterFactory::getAdapterFor($export_type);
        $adapter->setReportBuilder(new GenericExcelExportViaView($report_view))
            ->setReportViewName($report_view_name)
            ->setReportData($report_data);

        $url = $this->reportPrintService->setExportFileAdapter($adapter)
            ->setFileName(FileHelper::generateFileName($file_name))
            ->setPaperOrientation(ReportPrintService::PAPER_ORIENTATION_LANDSCAPE)
            ->generate()
            ->upload()
            ->getFileUrl();

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->setData(['url' => $url])
            ->getResponse();
    }

    public function studentAnalysisReportBySemesterGroupByGrade(StudentAnalysisReportBySemesterRequest $request): JsonResponse
    {
        $filters = $request->validated();

        app()->setLocale($filters['report_language']);

        $semester_setting = SemesterSetting::findOrFail($filters['semester_setting_id']);
        $data = $this->studentRepository->studentAnalysisReportBySemesterGroupByGradeData($semester_setting);

        $export_type = Arr::get($filters, 'export_type');

        if (!$export_type) {
            return (new ApiResponse())
                ->setMessage(__('api.common.success'))
                ->setCode(200)
                ->setData($data)
                ->getResponse();
        }

        $report_data = [
            'data' => $data,
        ];

        $report_view_name = 'reports.academy.student-analysis-report-by-semester';
        $file_name = 'academy-student-analysis-report-by-semester';

        $export_type = ExportType::from($export_type);
        $report_view = view($report_view_name, $report_data);

        $adapter = ExportAdapterFactory::getAdapterFor($export_type);
        $adapter->setReportBuilder(new GenericExcelExportViaView($report_view))
            ->setReportViewName($report_view_name)
            ->setReportData($report_data);

        $url = $this->reportPrintService->setExportFileAdapter($adapter)
            ->setFileName(FileHelper::generateFileName($file_name))
            ->setPaperOrientation(ReportPrintService::PAPER_ORIENTATION_LANDSCAPE)
            ->generate()
            ->upload()
            ->getFileUrl();

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->setData(['url' => $url])
            ->getResponse();
    }

    public function studentMeritAndExceptionalPerformanceReport(StudentMeritAndExceptionalPerformanceReportRequest $request): JsonResponse
    {
        $filters = $request->validated();

        app()->setLocale($filters['report_language']);

        $data = $this->studentRepository->studentMeritAndExceptionalPerformanceData($filters['student_ids']);

        $export_type = Arr::get($filters, 'export_type');

        $report_data = [
            'students' => $data->toArray(),
            'student_class' => $this->semesterClassRepository->first([
                'id' => $filters['semester_class_id'],
                'includes' => ['classModel']
            ])->toArray(),
        ];

        $report_view_name = 'reports.academy.student-merit-exceptional-performance';
        $file_name = 'student-merit-exceptional-performance';

        $export_type = ExportType::from($export_type);
        $report_view = view($report_view_name, $report_data);

        $adapter = ExportAdapterFactory::getAdapterFor($export_type);
        $adapter->setReportBuilder(new GenericExcelExportViaView($report_view))
            ->setReportViewName($report_view_name)
            ->setReportData($report_data);

        $url = $this->reportPrintService->setExportFileAdapter($adapter)
            ->setFileName(FileHelper::generateFileName($file_name))
            ->generate()
            ->upload()
            ->getFileUrl();

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->setData(['url' => $url])
            ->getResponse();
    }

    public function examinationResultBySemesterClass(ExaminationResultBySemesterClassRequest $request): JsonResponse
    {
        $filters = $request->validated();
        $filters['with_position_in_standard'] ??= false;

        app()->setLocale($filters['report_language']);

        $results_posting_header = ResultsPostingHeader::findOrFail($filters['results_posting_header_id']);
        $semester_class = SemesterClass::with(['classModel', 'semesterSetting.semesterYearSetting'])->findOrFail($filters['semester_class_id']);
        $data = $this->examRepository->examinationResultBySemesterClassData($results_posting_header, $semester_class, $filters['with_position_in_standard']);

        $export_type = Arr::get($filters, 'export_type');

        if (!$export_type) {
            return (new ApiResponse())
                ->setMessage(__('api.common.success'))
                ->setCode(200)
                ->setData($data)
                ->getResponse();
        }

        switch ($results_posting_header->report_card_output_code) {
            case 'SEM1RESULT':
                $title = __('general.examination_result_sem1_report_title', ['semester_year' => $semester_class->semesterSetting->semesterYearSetting?->year]);
                break;
            case 'SEM2RESULT':
                $title = __('general.examination_result_sem2_report_title', ['semester_year' => $semester_class->semesterSetting->semesterYearSetting?->year]);
                break;
            default:
                $title = __('general.examination_result_final_report_title', ['semester_year' => $semester_class->semesterSetting->semesterYearSetting?->year]);
                break;
        }

        $report_data = [
            'title' => $title,
            'data' => $data,
        ];

        $report_view_name = 'reports.academy.examination-result-by-semester-class';
        $file_name = 'academy-examination-result-by-semester-class';

        $export_type = ExportType::from($export_type);
        $report_view = view($report_view_name, $report_data);

        $adapter = ExportAdapterFactory::getAdapterFor($export_type);
        $adapter->setReportBuilder(new GenericExcelExportViaView($report_view))
            ->setReportViewName($report_view_name)
            ->setReportData($report_data);

        $url = $this->reportPrintService->setExportFileAdapter($adapter)
            ->setFileName(FileHelper::generateFileName($file_name))
            ->setPaperOrientation(ReportPrintService::PAPER_ORIENTATION_LANDSCAPE)
            ->generate()
            ->upload()
            ->getFileUrl();

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->setData(['url' => $url])
            ->getResponse();
    }

    public function examinationResultByExamReport(ExaminationResultByExamReportRequest $request): JsonResponse
    {
        $filters = $request->validated();

        app()->setLocale($filters['report_language']);

        $semester_setting = SemesterSetting::with(['semesterYearSetting'])->findOrFail($filters['semester_setting_id']);
        $exam = Exam::findOrFail($filters['exam_id']);
        $data = $this->examRepository->examinationResultByExamReportData(
            $semester_setting,
            $exam,
            $filters['semester_class_ids'] ?? [],
            $filters['grade_ids'] ?? [],
        );

        $export_type = Arr::get($filters, 'export_type');

        if (!$export_type) {
            return (new ApiResponse())
                ->setMessage(__('api.common.success'))
                ->setCode(200)
                ->setData($data)
                ->getResponse();
        }

        $report_data = [
            'title' => __('exam.examination_result_report_title', ['semester_setting_name' => $semester_setting->name]),
            'data' => $data,
        ];

        $report_view_name = 'reports.academy.examination-result-by-exam';
        $file_name = 'examination-result-by-exam';

        $export_type = ExportType::from($export_type);
        $report_view = view($report_view_name, $report_data);

        $adapter = ExportAdapterFactory::getAdapterFor($export_type);
        $adapter->setReportBuilder(new GenericExcelExportViaView($report_view))
            ->setReportViewName($report_view_name)
            ->setReportData($report_data);

        $url = $this->reportPrintService->setExportFileAdapter($adapter)
            ->setFileName(FileHelper::generateFileName($file_name))
            ->setPaperOrientation(ReportPrintService::PAPER_ORIENTATION_LANDSCAPE)
            ->generate()
            ->upload()
            ->getFileUrl();

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->setData(['url' => $url])
            ->getResponse();
    }

    public function examinationResultByStudentReport(ExaminationResultByStudentReportRequest $request): JsonResponse
    {
        $filters = $request->validated();

        $semester_setting = SemesterSetting::with(['semesterYearSetting'])->findOrFail($filters['semester_setting_id']);

        $exam = Exam::findOrFail($filters['exam_id']);

        $data_by_student = $this->examRepository->examinationResultByStudentReportData(
            $semester_setting,
            $exam,
            $filters['semester_class_ids'] ?? [],
            $filters['grade_ids'] ?? [],
        );

        $export_type = Arr::get($filters, 'export_type');
        if (!$export_type) {
            return (new ApiResponse())
                ->setMessage(__('api.common.success'))
                ->setCode(200)
                ->setData($data_by_student)
                ->getResponse();
        }

        $report_data = [
            'data_by_student' => $data_by_student,
        ];

        $report_view_name = 'reports.academy.examination-result-by-student';
        $file_name = 'examination-result-by-student';

        $export_type = ExportType::from($export_type);
        $report_view = view($report_view_name, $report_data);

        $adapter = ExportAdapterFactory::getAdapterFor($export_type);
        $adapter->setReportBuilder(new GenericExcelExportViaView($report_view))
            ->setReportViewName($report_view_name)
            ->setReportData($report_data);

        $url = $this->reportPrintService->setExportFileAdapter($adapter)
            ->setFileName(FileHelper::generateFileName($file_name))
            ->setPaperOrientation(ReportPrintService::PAPER_ORIENTATION_PORTRAIT)
            ->generate()
            ->upload()
            ->getFileUrl();

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->setData(['url' => $url])
            ->getResponse();
    }

    public function netAveragePassingRateReportData(NetAverageReportDataRequest $request)
    {
        $input = $request->validated();
        app()->setLocale($input['report_language']);

        $data = $this->academyReportService->getNetAveragePassingRateReportData($input);

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->setData($data)
            ->getResponse();

    }

    public function subjectPassingRateReportData(SubjectPassingRateReportDataRequest $request)
    {
        $input = $request->validated();
        app()->setLocale($input['report_language']);

        $data = $this->academyReportService->getSubjectPassingRateReportData($input);

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->setData($data)
            ->getResponse();
    }

    public function subjectAverageMarkReportData(SubjectAverageMarkReportDataRequest $request)
    {
        $input = $request->validated();
        app()->setLocale($input['report_language']);

        $data = $this->academyReportService->getSubjectAverageMarkReportData($input);

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->setData($data)
            ->getResponse();
    }

    public function subjectAnalysisReportData(SubjectAnalysisReportDataRequest $request)
    {
        $input = $request->validated();
        app()->setLocale($input['report_language']);

        $data = $this->academyReportService->getSubjectScoreAnalysisReportData($input);

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->setData($data)
            ->getResponse();
    }

    public function allPassedStudentReport(AllPassedStudentReportRequest $request)
    {
        $input = $request->validated();
        app()->setLocale($input['report_language']);

        $report_data = $this->academyReportService->getAllPassedStudentReportData($input);

        $export_type = ExportType::from($input['export_type']);

        $report_view_name = 'reports.academy.all-passed-student-report';
        $report_view = view($report_view_name, $report_data);

        $file_name = 'all-passed-student-report';

        $adapter = ExportAdapterFactory::getAdapterFor($export_type);
        $adapter->setReportBuilder(new GenericExcelExportViaView($report_view))
            ->setReportViewName($report_view_name)
            ->setReportData($report_data);

        $url = $this->reportPrintService->setExportFileAdapter($adapter)
            ->setFileName(FileHelper::generateFileName($file_name))
            ->setPaperOrientation(DocumentPrintService::PAPER_ORIENTATION_LANDSCAPE)
            ->generate()
            ->upload()
            ->getFileUrl();


        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->setData(['url' => $url])
            ->getResponse();
    }

    public function bestGradeByClassReport(BestGradeByClassReportRequest $request)
    {
        $input = $request->validated();
        app()->setLocale($input['report_language']);

        $report_data = $this->academyReportService->getBestGradeByClassReportData($input);

        $export_type = ExportType::from($input['export_type']);

        $report_view_name = 'reports.academy.best-grade-by-class-report';
        $report_view = view($report_view_name, $report_data);

        $file_name = 'best-grade-by-class-report';

        $adapter = ExportAdapterFactory::getAdapterFor($export_type);
        $adapter->setReportBuilder(new GenericExcelExportViaView($report_view))
            ->setReportViewName($report_view_name)
            ->setReportData($report_data);

        $url = $this->reportPrintService->setExportFileAdapter($adapter)
            ->setFileName(FileHelper::generateFileName($file_name))
            ->setPaperOrientation(DocumentPrintService::PAPER_ORIENTATION_LANDSCAPE)
            ->generate()
            ->upload()
            ->getFileUrl();


        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->setData(['url' => $url])
            ->getResponse();
    }

    public function bestGradeBySubjectReport(BestGradeBySubjectReportRequest $request)
    {
        $input = $request->validated();
        app()->setLocale($input['report_language']);

        $report_data = $this->academyReportService->getBestGradeBySubjectReportData($input);

        $export_type = ExportType::from($input['export_type']);

        $report_view_name = 'reports.academy.best-grade-by-subject-report';
        $report_view = view($report_view_name, $report_data);

        $file_name = 'best-grade-by-subject-report';

        $adapter = ExportAdapterFactory::getAdapterFor($export_type);
        $adapter->setReportBuilder(new GenericExcelExportViaView($report_view))
            ->setReportViewName($report_view_name)
            ->setReportData($report_data);

        $url = $this->reportPrintService->setExportFileAdapter($adapter)
            ->setFileName(FileHelper::generateFileName($file_name))
            ->setPaperOrientation(DocumentPrintService::PAPER_ORIENTATION_LANDSCAPE)
            ->generate()
            ->upload()
            ->getFileUrl();


        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->setData(['url' => $url])
            ->getResponse();
    }

    public function bestGradeByGradeReport(BestGradeByGradeReportRequest $request)
    {
        $input = $request->validated();
        app()->setLocale($input['report_language']);

        $report_data = $this->academyReportService->getBestGradeByGradeReportData($input);
        $export_type = ExportType::from($input['export_type']);

        $report_view_name = 'reports.academy.best-grade-by-grade-report';
        $report_view = view($report_view_name, $report_data);

        $file_name = 'best-grade-by-grade-report';

        $adapter = ExportAdapterFactory::getAdapterFor($export_type);
        $adapter->setReportBuilder(new GenericExcelExportViaView($report_view))
            ->setReportViewName($report_view_name)
            ->setReportData($report_data);

        $url = $this->reportPrintService->setExportFileAdapter($adapter)
            ->setFileName(FileHelper::generateFileName($file_name))
            ->setPaperOrientation(DocumentPrintService::PAPER_ORIENTATION_LANDSCAPE)
            ->generate()
            ->upload()
            ->getFileUrl();

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->setData(['url' => $url])
            ->getResponse();
    }
}
