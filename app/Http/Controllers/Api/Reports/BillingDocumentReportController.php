<?php

namespace App\Http\Controllers\Api\Reports;

use App\Enums\ExportType;
use App\Helpers\FileHelper;
use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Reports\BillingDocument\ReportByDailyCollectionRequest;
use App\Http\Resources\ApiResponse;
use App\Services\Reports\BillingDocumentReportService;
use Illuminate\Http\JsonResponse;


class BillingDocumentReportController extends Controller
{
    public function __construct(
        protected BillingDocumentReportService $billingDocumentReportService,
    ) {}

    public function reportByDailyCollection(ReportByDailyCollectionRequest $request): JsonResponse
    {
        $filters = $request->validated();

        app()->setLocale($filters['report_language']);

        $file_name = FileHelper::generateFileName('billing-document-report-by-daily-collection');

        $response = $this->billingDocumentReportService
            ->setExportType(ExportType::EXCEL->value)
            ->setReportViewName('reports.billing-documents.by-daily-collection')
            ->setFileName($file_name)
            ->getDailyCollectionReportData($filters);

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->setData($response)
            ->getResponse();
    }
}
