<?php

namespace App\Http\Controllers\Api\Reports;

use App\Enums\ExportType;
use App\Exports\GenericExcelExportViaView;
use App\Factories\ExportAdapterFactory;
use App\Helpers\FileHelper;
use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Reports\StudentAffairs\StudentPerformanceReportByDateRangeRequest;
use App\Http\Resources\ApiResponse;
use App\Repositories\CompetitionRepository;
use App\Services\ReportPrintService;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Arr;

class StudentAffairsReportController extends Controller
{
    public function __construct(
        protected CompetitionRepository $competitionRepository,
        protected ReportPrintService    $reportPrintService,
    )
    {
    }

    public function studentPerformanceReportByDateRange(StudentPerformanceReportByDateRangeRequest $request): JsonResponse
    {
        $filters = $request->validated();

        app()->setLocale($filters['report_language']);

        $data = $this->competitionRepository->studentPerformanceReportByDateRangeData($filters['from_date'], $filters['to_date']);

        $export_type = Arr::get($filters, 'export_type');
        if (!$export_type) {
            return (new ApiResponse())
                ->setMessage(__('api.common.success'))
                ->setCode(200)
                ->setData($data)
                ->getResponse();
        }

        $report_data = [
            'data' => $data,
        ];

        $report_view_name = 'reports.student-affairs.student-performance-report-by-date-range';
        $file_name = 'student-affairs-student-performance-report-by-date-range';
        
        $export_type = ExportType::from($export_type);
        $report_view = view($report_view_name, $report_data);
        $adapter = ExportAdapterFactory::getAdapterFor($export_type);
        $adapter->setReportBuilder(new GenericExcelExportViaView($report_view))
            ->setReportViewName($report_view_name)
            ->setReportData($report_data);

        $url = $this->reportPrintService->setExportFileAdapter($adapter)
            ->setFileName(FileHelper::generateFileName($file_name))
            ->setPaperOrientation(ReportPrintService::PAPER_ORIENTATION_LANDSCAPE)
            ->generate()
            ->upload()
            ->getFileUrl();

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->setData(['url' => $url])
            ->getResponse();
    }
}