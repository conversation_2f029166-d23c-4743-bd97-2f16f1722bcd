<?php

namespace App\Http\Controllers\Api\Timetable;

use App\Helpers\ErrorCodeHelper;
use App\Http\Controllers\Controller;
use App\Http\Requests\Api\SubstituteRecord\SubstituteRecordBulkCreateOrUpdateSubmitRequest;
use App\Http\Requests\Api\SubstituteRecord\SubstituteRecordBulkCreateOrUpdateViewRequest;
use App\Http\Requests\Api\SubstituteRecord\SubstituteRecordIndexRequest;
use App\Http\Requests\Api\SubstituteRecord\SubstituteRecordUpdateRequest;
use App\Http\Resources\ApiResponse;
use App\Http\Resources\SubstituteRecordResource;
use App\Models\Employee;
use App\Models\SubstituteRecord;
use App\Repositories\EmployeeRepository;
use App\Repositories\TimeslotRepository;
use App\Services\SubstituteRecordService;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;

class SubstituteRecordController extends Controller
{
    protected SubstituteRecordService $substituteRecordService;
    protected EmployeeRepository $employeeRepository;
    protected TimeslotRepository $timeslotRepository;

    public function __construct(
        SubstituteRecordService $substituteRecordService,
        EmployeeRepository $employeeRepository,
        TimeslotRepository $timeslotRepository
    ) {
        $this->substituteRecordService = $substituteRecordService;
        $this->employeeRepository = $employeeRepository;
        $this->timeslotRepository = $timeslotRepository;
    }

    public function index(SubstituteRecordIndexRequest $request): JsonResponse
    {
        $input = $request->validated();

        $input['includes'] = array_unique(array_merge($input['includes'] ?? [], ['period.periodGroup.periodLabels', 'classSubject.semesterClass.classModel', 'classSubject.subject']));
        $substitute_records = $this->substituteRecordService->getAllPaginatedSubstituteRecords($input);

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->setData(SubstituteRecordResource::collection($substitute_records))
            ->setPagination($substitute_records)
            ->getResponse();
    }

    public function getRequestorSubstituteTimeslotsByDateRange(Employee $requestor, SubstituteRecordBulkCreateOrUpdateViewRequest $request): JsonResponse
    {
        $input = $request->validated();

        $substitute_timeslots = $this->substituteRecordService->getRequestorSubstituteTimeslotsByDateRange($requestor, $input['substitute_date_from'], $input['substitute_date_to']);

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->setData($substitute_timeslots)
            ->getResponse();
    }

    public function bulkCreateOrUpdate(Employee $requestor, SubstituteRecordBulkCreateOrUpdateSubmitRequest $request): JsonResponse
    {
        $input = $request->validated();

        $substitute_records = $input['substitute_records'];

        $substitute_teacher_ids = array_unique(array_column($substitute_records, 'substitute_teacher_id'));
        if (in_array($requestor->id, $substitute_teacher_ids)) {
            ErrorCodeHelper::throwError(ErrorCodeHelper::ATTENDANCE_ERROR, 42010);
        }

        $substitute_teachers = $this->employeeRepository->getAll([
            'id' => $substitute_teacher_ids,
        ])->keyBy('id');

        $timeslots = $this->timeslotRepository->getAll([
            'id' => array_column($substitute_records, 'timeslot_id'),
        ])->keyBy('id');

        $substitute_records_created_or_updated = [];

        DB::transaction(function () use ($requestor, $substitute_records, $timeslots, $substitute_teachers, &$substitute_records_created_or_updated) {
            foreach ($substitute_records as $substitute_record) {
                $substitute_records_created_or_updated[] = $this->substituteRecordService
                    ->setTimeslot($timeslots[$substitute_record['timeslot_id']])
                    ->setSubstituteTeacher($substitute_teachers[$substitute_record['substitute_teacher_id']])
                    ->setRequestor($requestor)
                    ->setSubstituteDate($substitute_record['substitute_date'])
                    ->setRemarks($substitute_record['remarks'] ?? null)
                    ->setAllowance($substitute_record['allowance'])
                    ->createOrUpdate();
            }
        });

        $substitute_records_created_or_updated = $this->substituteRecordService->getAllSubstituteRecords([
            'id' => collect($substitute_records_created_or_updated)->pluck('id')->toArray(),
            'includes' => ['period.periodGroup.periodLabels', 'classSubject.semesterClass.classModel', 'classSubject.subject', 'substituteTeacher', 'requestor'],
        ]);

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->setData(SubstituteRecordResource::collection($substitute_records_created_or_updated))
            ->getResponse();
    }

    public function update(SubstituteRecord $substitute_record, SubstituteRecordUpdateRequest $request)
    {
        $input = $request->validated();

        if ($substitute_record->requestor_id == $input['substitute_teacher_id']) {
            ErrorCodeHelper::throwError(ErrorCodeHelper::ATTENDANCE_ERROR, 42010);
        }

        $substitute_teacher = Employee::findOrFail($input['substitute_teacher_id']);
        $remarks = $input['remarks'] ?? null;
        $allowance = $input['allowance'];

        $substitute_record = $this->substituteRecordService
            ->setSubstituteRecord($substitute_record)
            ->setSubstituteTeacher($substitute_teacher)
            ->setRemarks($remarks)
            ->setAllowance($allowance)
            ->update();

        $substitute_record->loadMissing(['substituteTeacher', 'requestor']);

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->setData(new SubstituteRecordResource($substitute_record))
            ->getResponse();
    }

    public function destroy(SubstituteRecord $substitute_record): JsonResponse
    {
        $this->substituteRecordService->setSubstituteRecord($substitute_record)->delete();

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->getResponse();
    }
}
