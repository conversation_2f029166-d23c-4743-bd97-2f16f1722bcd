<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\Api\User\UserChangePasswordRequest;
use App\Http\Requests\Api\User\UserGetProfileRequest;
use App\Http\Requests\Api\User\UserIndexRequest;
use App\Http\Requests\Api\User\UserUnlinkGuardianRequest;
use App\Http\Requests\Api\User\UserUpdatePushNotificationTokenRequest;
use App\Http\Requests\Api\User\UserUpdateRequest;
use App\Http\Resources\ApiResponse;
use App\Http\Resources\UserProfileResource;
use App\Http\Resources\UserResource;
use App\Interfaces\Userable;
use App\Models\User;
use App\Services\UserService;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\JsonResponse;

class UserController extends Controller
{
    public function __construct(
        protected UserService $userService,
    ) {
    }

    public function index(UserIndexRequest $request): JsonResponse
    {
        $input = $request->validated();

        if (isset($input['user_type'])) {
            $input['user_type'] = Userable::USERABLE_MAPPING[$input['user_type']];
        }

        $input['includes'] = array_merge($input['includes'] ?? [], [
            'userables',
            'guardian.directDependants.userable',
        ]);
        $input['includes'] = array_unique($input['includes']);

        $users = $this->userService->getAllPaginatedUsers($input);

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->setData(UserResource::collection($users))
            ->setPagination($users)
            ->getResponse();
    }

    public function show(User $user): JsonResponse
    {
        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->setData(new UserResource($user->loadMissing(['userables', 'guardian.directDependants.userable', 'roles', 'permissions'])))
            ->getResponse();
    }

    public function update(User $user, UserUpdateRequest $request): JsonResponse
    {
        $input = $request->validated();

        if (empty($input['password'])) {
            unset($input['password']);
        }

        $user = $this->userService->updateUser($user, $input);

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->setData(new UserResource($user->loadMissing(['userables', 'guardian.directDependants.userable', 'roles', 'permissions'])))
            ->getResponse();
    }

    public function unlinkGuardian(User $user, UserUnlinkGuardianRequest $request): JsonResponse
    {
        $input = $request->validated();
        $this->userService->unlinkGuardian($user, $input['guardian_id']);

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->setData(new UserResource($user->loadMissing(['userables', 'guardian.directDependants.userable', 'roles', 'permissions'])))
            ->getResponse();
    }

    public function getProfile(UserGetProfileRequest $request): JsonResponse
    {
        $input = $request->validated();

        /** @var User */
        $user = auth()->user();

        $includes = array_merge($input['includes'] ?? [], [
            'userables.wallets.currency',
        ]);
        $includes = array_unique($includes);
        $includes = array_merge($includes, [
            'guardian.directDependants.userable' => function ($query) {
                $query->with([
                    'wallets.currency',
                    'userable.currentSemesterPrimaryClass.semesterSetting',
                    'userable.currentSemesterPrimaryClass.semesterClass.classModel.grade',
                    'userable.media',
                ]);
            },
        ]);

        $user->loadMissing($includes);

        $user->loadCount([
            'userInboxes as user_inboxes_unread_count' => function (Builder $query) {
                $query->whereNull('read_at');
            }
        ]);

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->setData(new UserProfileResource($user))
            ->getResponse();
    }

    public function changePassword(UserChangePasswordRequest $request): JsonResponse
    {
        $this->userService->changePassword(auth()->user(), $request->password);

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->getResponse();
    }

    public function updatePushNotificationToken(UserUpdatePushNotificationTokenRequest $request)
    {

        $input = $request->validated();
        $user = auth()->user();

        $this->userService->updateUserPushNotificationSettings($user, $input['platform'], $input['token']);

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->getResponse();

    }

    public function clearPushNotificationToken()
    {
        $user = auth()->user();

        $this->userService->clearUserPushNotificationSettings($user);

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->getResponse();

    }
}
