<?php

namespace App\Http\Controllers\Api;

use App\Enums\ResponseType;
use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Employee\EmployeeCreateRequest;
use App\Http\Requests\Api\Employee\EmployeeIndexRequest;
use App\Http\Requests\Api\Employee\EmployeeReinstateRequest;
use App\Http\Requests\Api\Employee\EmployeeResignRequest;
use App\Http\Requests\Api\Employee\EmployeeTransferRequest;
use App\Http\Requests\Api\Employee\EmployeeUpdateRequest;
use App\Http\Resources\ApiResponse;
use App\Http\Resources\EmployeeResource;
use App\Http\Resources\SimpleEmployeeResource;
use App\Models\Employee;
use App\Models\EmployeeJobTitle;
use App\Services\EmployeeService;
use Illuminate\Http\JsonResponse;

class EmployeeController extends Controller
{
    protected EmployeeService $employeeService;

    public function __construct(EmployeeService $employee_service)
    {
        $this->employeeService = $employee_service;
    }

    public function index(EmployeeIndexRequest $request): JsonResponse
    {
        $input = $request->validated();

        if (!isset($input['response'])) {
            $input['response'] = ResponseType::SIMPLE->value;
        }

        $input['includes'] = array_merge($input['includes'] ?? [], ['media', 'jobTitle']);
        $input['includes'] = array_unique($input['includes']);

        $data = $this->employeeService->getAllPaginatedEmployees($input);

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->setData($input['response'] === ResponseType::SIMPLE->value ? SimpleEmployeeResource::collection($data) : EmployeeResource::collection($data))
            ->setPagination($data)
            ->getResponse();
    }

    public function show(Employee $employee): JsonResponse
    {
        $employee->load(['employmentHistories', 'employeeSession']);

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->setData(new EmployeeResource($employee))
            ->getResponse();
    }

    public function create(EmployeeCreateRequest $request): JsonResponse
    {
        $input = $request->validated();

        $employee = $this->employeeService
            ->setUserData($input)
            ->setEmployeeData($input)
            ->createEmployee();

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->setData(new EmployeeResource($employee))
            ->getResponse();
    }

    public function update(Employee $employee, EmployeeUpdateRequest $request): JsonResponse
    {
        $input = $request->validated();

        $employee = $this->employeeService
            ->setEmployeeData($input)
            ->updateEmployee($employee);

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->setData(new EmployeeResource($employee))
            ->getResponse();
    }

    public function destroy(Employee $employee): JsonResponse
    {
        $this->employeeService->deleteEmployee($employee);

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->getResponse();
    }


    public function resign(Employee $employee, EmployeeResignRequest $request): JsonResponse
    {
        $input = $request->validated();

        $this->employeeService
            ->setEmployee($employee)
            ->setApiRequest($input)
            ->markEmployeeResignedAt($input['effective_date']);

        $employee->refresh();

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->setData(new EmployeeResource($employee))
            ->getResponse();
    }

    public function reinstate(Employee $employee, EmployeeReinstateRequest $request): JsonResponse
    {
        $input = $request->validated();

        $this->employeeService
            ->setEmployee($employee)
            ->setApiRequest($input)
            ->markEmployeeReinstatedAt($input['effective_date']);

        $employee->refresh();

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->setData(new EmployeeResource($employee))
            ->getResponse();
    }

    public function transfer(Employee $employee, EmployeeTransferRequest $request): JsonResponse
    {
        $input = $request->validated();

        $new_job_title = EmployeeJobTitle::where('id', $input['job_title_id'])->firstOrFail();

        $this->employeeService
            ->setEmployee($employee)
            ->setApiRequest($input)
            ->setNewJobTitle($new_job_title)
            ->transferEmployee($input['effective_date']);

        $employee->refresh();

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->setData(new EmployeeResource($employee))
            ->getResponse();
    }
}
