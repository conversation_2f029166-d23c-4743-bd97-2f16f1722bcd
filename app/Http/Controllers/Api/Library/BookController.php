<?php

namespace App\Http\Controllers\Api\Library;

use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Book\BookCreateRequest;
use App\Http\Requests\Api\Book\BookIndexRequest;
use App\Http\Requests\Api\Book\BookUpdateRequest;
use App\Http\Resources\ApiResponse;
use App\Http\Resources\BookResource;
use App\Models\Book;
use App\Services\BookService;
use Illuminate\Http\JsonResponse;

class BookController extends Controller
{
    protected BookService $bookService;

    public function __construct(BookService $book_service)
    {
        $this->bookService = $book_service;
    }

    public function index(BookIndexRequest $request): JsonResponse
    {
        $input = $request->validated();

        $data = $this->bookService->getAllPaginatedBooks($input);

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->setData(BookResource::collection($data))
            ->setPagination($data)
            ->getResponse();
    }

    public function showViaBookNo(Book $book): JsonResponse
    {
        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->setData(new BookResource($book))
            ->getResponse();
    }

    public function show(Book $book): JsonResponse
    {
        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->setData(new BookResource($book))
            ->getResponse();
    }

    public function create(BookCreateRequest $request): JsonResponse
    {
        $input = $request->validated();

        $book = $this->bookService->createBook($input);

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->setData(new BookResource($book))
            ->getResponse();
    }

    public function update(Book $book, BookUpdateRequest $request): JsonResponse
    {
        $input = $request->validated();

        $book = $this->bookService->updateBook($book->id, $input);

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->setData(new BookResource($book))
            ->getResponse();
    }

    public function destroy(Book $book): JsonResponse
    {
        $this->bookService->deleteBook($book->id);

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->getResponse();
    }

    public function recoverLostBook(Book $book): JsonResponse
    {
        $book = $this->bookService->recoverLostBook($book);

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->setData(new BookResource($book))
            ->getResponse();
    }
}
