<?php

namespace App\Http\Controllers\Api\Student;

use App\Enums\ResponseType;
use App\Helpers\ErrorCodeHelper;
use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Hostel\HostelStudentUpdateRequest;
use App\Http\Requests\Api\Student\StudentCreateRequest;
use App\Http\Requests\Api\Student\StudentIndexRequest;
use App\Http\Requests\Api\Student\StudentLeaveRequest;
use App\Http\Requests\Api\Student\StudentReturnRequest;
use App\Http\Requests\Api\Student\StudentUpdateRequest;
use App\Http\Resources\ApiResponse;
use App\Http\Resources\SimpleStudentResource;
use App\Http\Resources\StudentResource;
use App\Models\LeaveReason;
use App\Models\SemesterClass;
use App\Models\SemesterSetting;
use App\Models\Student;
use App\Services\StudentService;
use App\Traits\HandlesPagination;
use Illuminate\Http\JsonResponse;

class StudentController extends Controller
{
    use HandlesPagination;

    private StudentService $studentService;

    public function __construct(StudentService $student_service)
    {
        $this->studentService = $student_service;
    }

    public function index(StudentIndexRequest $request): JsonResponse
    {
        $input = $request->validated();

        $api_response = (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200);

        if (!isset($input['response'])) {
            $input['response'] = ResponseType::SIMPLE->value;
        } else {
            $input['includes'] = array_merge($input['includes'] ?? [], [
                'latestPrimaryClassBySemesterSettings.semesterClass.classModel.grade',
                'latestPrimaryClassBySemesterSettings.semesterClass.semesterSetting',
                'media',
            ]);
            $input['includes'] = array_unique($input['includes']);
        }

        $data = $this->fetchData($input, $this->studentService, 'getAllStudents', 'getAllPaginatedStudents');
        $this->determinePagination($api_response, $input, $data);

        return $api_response->setData($input['response'] == ResponseType::SIMPLE->value ? SimpleStudentResource::collection($data) : StudentResource::collection($data))
            ->getResponse();
    }

    public function show(Student $student): JsonResponse
    {
        $student->load(request()->input('includes', []));

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->setData(new StudentResource($student))
            ->getResponse();
    }

    public function create(StudentCreateRequest $request): JsonResponse
    {
        $input = $request->validated();

        $student = $this->studentService->createStudent($input);

        $student->load('currentSemesterPrimaryClass');

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->setData(new StudentResource($student))
            ->getResponse();
    }

    public function update(Student $student, StudentUpdateRequest $request): JsonResponse
    {
        $input = $request->validated();

        $student = $this->studentService->updateStudent($student, $input);

        $student->load('currentSemesterPrimaryClass');

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->setData(new StudentResource($student))
            ->getResponse();
    }

    public function updateHostelStudent(Student $student, HostelStudentUpdateRequest $request): JsonResponse
    {
        $input = $request->validated();

        $current_direct_guardian_ids = $student->guardians()->where('is_direct_dependant', true)->pluck('id')->toArray();
        $new_non_direct_guardian_ids = collect($input['guardians'])->pluck('id')->filter()->toArray();

        // If non direct guardian id exist in current direct guardian ids, throw error
        $existing_guardian_ids = array_intersect($current_direct_guardian_ids, $new_non_direct_guardian_ids);

        if (count($existing_guardian_ids) > 0) {
            $existing_guardian_name = null;
            foreach ($input['guardians'] as $guardian) {
                if (isset($guardian['id']) && in_array($guardian['id'], $existing_guardian_ids)) {
                    if ($existing_guardian_name !== null) {
                        $existing_guardian_name .= ', ';
                    }
                    $existing_guardian_name .= implode(' - ', $guardian['name']);
                }
            }
            ErrorCodeHelper::throwError(ErrorCodeHelper::GUARDIAN_ERROR, 7005, [
                'guardian_name' => $existing_guardian_name
            ]);
        }

        $student = $this->studentService
            ->setStudent($student)
            ->updateStudentNonDirectGuardian($input['guardians']);

        $student->load('currentSemesterPrimaryClass');

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->setData(new StudentResource($student))
            ->getResponse();
    }

    public function destroy(Student $student): JsonResponse
    {
        $this->studentService->deleteStudent($student);

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->getResponse();
    }

    public function regenerateStudentNumber(Student $student): JsonResponse
    {
        $new_student_number = $this->studentService->regenerateStudentNumber($student);

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->setData(['student_number' => $new_student_number])
            ->getResponse();
    }

    public function getFirstByStudentNumberOrCardNumber($number): JsonResponse
    {
        $student = $this->studentService->getFirstByStudentNumberOrCardNumber($number);

        $student->load('currentSemesterPrimaryClass');

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->setData(new StudentResource($student))
            ->getResponse();
    }

    public function leave(Student $student, StudentLeaveRequest $request): JsonResponse
    {
        $validated_api_request = $request->validated();
        $remarks = isset($validated_api_request['remarks']) ? $validated_api_request['remarks'] : null;
        $leave_reason = LeaveReason::findOrFail($validated_api_request['leave_reason_id']);

        $this->studentService
            ->setStudent($student)
            ->setRemarks($remarks)
            ->setApiRequest($validated_api_request)
            ->setLeaveReason($leave_reason)
            ->markStudentLeftAt($validated_api_request['effective_date']);

        $student->refresh();

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->setData(new StudentResource($student))
            ->getResponse();
    }

    public function return(Student $student, StudentReturnRequest $request): JsonResponse
    {
        $validated_api_request = $request->validated();
        $semester_setting = SemesterSetting::findOrFail($validated_api_request['semester_setting_id']);
        $semester_class = SemesterClass::findOrFail($validated_api_request['semester_class_id']);

        $this->studentService
            ->setStudent($student)
            ->setApiRequest($validated_api_request)
            ->setSemesterSetting($semester_setting)
            ->setSemesterClass($semester_class)
            ->markStudentReturnedAt($validated_api_request['effective_date']);

        $student->refresh();

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->setData(new StudentResource($student))
            ->getResponse();
    }
}


