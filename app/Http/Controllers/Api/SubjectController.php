<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Subject\SubjectCreateRequest;
use App\Http\Requests\Api\Subject\SubjectIndexRequest;
use App\Http\Requests\Api\Subject\SubjectUpdateRequest;
use App\Http\Resources\ApiResponse;
use App\Http\Resources\SubjectResource;
use App\Models\Subject;
use App\Services\SubjectService;
use App\Traits\HandlesPagination;
use Illuminate\Http\JsonResponse;

class SubjectController extends Controller
{
    use HandlesPagination;

    protected SubjectService $subjectService;

    public function __construct(SubjectService $subject_service)
    {
        $this->subjectService = $subject_service;
    }

    public function index(SubjectIndexRequest $request): JsonResponse
    {
        $input = $request->validated();

        $api_response = (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200);

        $data = $this->fetchData($input, $this->subjectService, 'getAllSubjects', 'getAllPaginatedSubjects');
        $this->determinePagination($api_response, $input, $data);

        return $api_response->setData(SubjectResource::collection($data))->getResponse();
    }

    public function show(Subject $subject): JsonResponse
    {
        $subject = $subject->load('classSubjects');

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->setData(new SubjectResource($subject))
            ->getResponse();
    }

    public function create(SubjectCreateRequest $request): JsonResponse
    {
        $input = $request->validated();

        $subject = $this->subjectService->createSubject($input);

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->setData(new SubjectResource($subject))
            ->getResponse();
    }

    public function update(Subject $subject, SubjectUpdateRequest $request): JsonResponse
    {
        $input = $request->validated();

        $subject = $this->subjectService->updateSubject($subject, $input);

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->setData(new SubjectResource($subject))
            ->getResponse();
    }

    public function destroy(Subject $subject): JsonResponse
    {
        $this->subjectService->deleteSubject($subject);

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->getResponse();
    }
}
