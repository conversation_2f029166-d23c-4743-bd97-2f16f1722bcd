<?php

namespace App\Http\Controllers\Api;

use App\Enums\EnrollmentPaymentStatus;
use App\Enums\EnrollmentStatus;
use App\Enums\ExportType;
use App\Exports\GenericExcelExportViaView;
use App\Factories\ExportAdapterFactory;
use App\Helpers\ErrorCodeHelper;
use App\Helpers\FileHelper;
use App\Helpers\SystemHelper;
use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Enrollment\EnrollmentAdminIndexRequest;
use App\Http\Requests\Api\Enrollment\EnrollmentAdminUpdateRequest;
use App\Http\Requests\Api\Enrollment\EnrollmentBulkSaveImportedDataRequest;
use App\Http\Requests\Api\Enrollment\EnrollmentBulkSavePostPaymentImportedDataRequest;
use App\Http\Requests\Api\Enrollment\EnrollmentDownloadTemplateRequest;
use App\Http\Requests\Api\Enrollment\EnrollmentExtendExpiryRequest;
use App\Http\Requests\Api\Enrollment\EnrollmentImportTemplateValidationRequest;
use App\Http\Requests\Api\Enrollment\EnrollmentIndexRequest;
use App\Http\Requests\Api\Enrollment\EnrollmentMakePaymentRequest;
use App\Http\Requests\Api\Enrollment\EnrollmentPrintDetailRequest;
use App\Http\Requests\Api\Enrollment\EnrollmentRetryPaymentRequest;
use App\Http\Requests\Api\Enrollment\EnrollmentStatusUpdateRequest;
use App\Http\Requests\Api\Enrollment\EnrollmentUpdateRequest;
use App\Http\Requests\Api\PostEnrollmentsToAutocountRequest;
use App\Http\Resources\ApiResponse;
use App\Http\Resources\EnrollmentResource;
use App\Http\Resources\PaymentGatewayLogResource;
use App\Models\BillingDocument;
use App\Models\Enrollment;
use App\Models\PaymentGatewayLog;
use App\Repositories\EnrollmentSessionRepository;
use App\Services\Billing\BillingDocumentLineItemService;
use App\Services\Billing\BillingDocumentService;
use App\Services\Billing\UnpaidItemService;
use App\Services\EnrollmentPostingService;
use App\Services\EnrollmentService;
use App\Services\ReportPrintService;
use App\Traits\HandlesPagination;
use Carbon\Carbon;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class EnrollmentController extends Controller
{
    use HandlesPagination;

    public function __construct(
        protected EnrollmentService $enrollmentService,
        protected EnrollmentPostingService $enrollmentPostingService,
        protected ReportPrintService $reportPrintService,
        protected EnrollmentSessionRepository $enrollmentSessionRepository
    ) {
    }

    public function adminIndex(EnrollmentAdminIndexRequest $request): JsonResponse
    {
        $input = $request->validated();

        $api_response = (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200);

        $data = $this->fetchData(
            $input,
            $this->enrollmentService,
            'getAllEnrollments',
            'getAllPaginatedEnrollments'
        );

        $this->determinePagination($api_response, $input, $data);

        return $api_response->setData(EnrollmentResource::collection($data))->getResponse();
    }

    public function index(EnrollmentIndexRequest $request): JsonResponse
    {
        $user = auth()->user();

        $input = $request->validated();
        $input['enrollment_user_id'] = $user->id;

        $api_response = (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200);

        $data = $this->fetchData(
            $input,
            $this->enrollmentService,
            'getAllEnrollments',
            'getAllPaginatedEnrollments'
        );

        $this->determinePagination($api_response, $input, $data);

        return $api_response->setData(EnrollmentResource::collection($data))->getResponse();
    }

    public function adminShow(Enrollment $enrollment): JsonResponse
    {
        $enrollment->load([
            'admissionGrade',
            'nationality',
            'race',
            'religion',
            'state',
            'country',
            'enrollmentSession',
            'healthConcern',
            'primarySchool',
            'billingDocuments',
            'guardians.nationality',
            'guardians.race',
            'guardians.religion',
            'guardians.education',
            'enrollmentExams.examMarks.subject',
        ]);

        // show the enrollment's fees
        $enrollment->show_fee = true;

        // show the enrollment's siblings
        $enrollment->show_siblings = true;

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->setData(new EnrollmentResource($enrollment))
            ->getResponse();
    }

    public function show(Enrollment $enrollment): JsonResponse
    {
        if (!$enrollment->canBeAccessedByUser(auth()->user())) {
            return (new ApiResponse())
                ->setStatus(ApiResponse::STATUS_ERROR)
                ->setError(__('api.common.unauthorized'))
                ->setCode(403)
                ->getResponse();
        }

        $enrollment->load([
            'admissionGrade',
            'nationality',
            'race',
            'religion',
            'state',
            'country',
            'enrollmentSession',
            'healthConcern',
            'primarySchool',
            'billingDocuments',
            'guardians.nationality',
            'guardians.race',
            'guardians.religion',
            'guardians.education',
            'enrollmentExams.examMarks.subject',
        ]);

        // show the enrollment's fees
        $enrollment->show_fee = true;

        // show the enrollment's siblings
        $enrollment->show_siblings = true;

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->setData(new EnrollmentResource($enrollment))
            ->getResponse();
    }

    public function destroy(Enrollment $enrollment): JsonResponse
    {
        $this->enrollmentService->delete($enrollment);

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->getResponse();
    }

    /**
     * BO - Update enrollment details.
     */
    public function adminUpdate(Enrollment $enrollment, EnrollmentAdminUpdateRequest $request): JsonResponse
    {
        $input = $request->validated();

        $enrollment = $this->enrollmentService->update($enrollment, $input, true);

        $enrollment->load([
            'admissionGrade',
            'nationality',
            'race',
            'religion',
            'state',
            'country',
            'enrollmentSession',
            'healthConcern',
            'primarySchool',
            'enrollmentUser',
            'billingDocuments',
            'guardians.nationality',
            'guardians.race',
            'guardians.religion',
            'guardians.education',
            'enrollmentExams.examMarks.subject',
        ]);

        // show the enrollment's fees
        $enrollment->show_fee = true;

        // show the enrollment's siblings
        $enrollment->show_siblings = true;

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->setData(new EnrollmentResource($enrollment))
            ->getResponse();
    }

    /**
     * Guardian Portal - Update enrollment details.
     */
    public function update(Enrollment $enrollment, EnrollmentUpdateRequest $request): JsonResponse
    {
        $input = $request->validated();

        if (!$enrollment->canBeUpdated()) {
            ErrorCodeHelper::throwError(ErrorCodeHelper::ENROLLMENT_ERROR, 5029);
        }

        $enrollment = $this->enrollmentService->update($enrollment, $input, false);

        $enrollment->load([
            'admissionGrade',
            'nationality',
            'race',
            'religion',
            'state',
            'country',
            'enrollmentSession',
            'healthConcern',
            'primarySchool',
            'enrollmentUser',
            'billingDocuments',
            'guardians',
        ]);

        // show the enrollment's fees
        $enrollment->show_fee = true;

        // show the enrollment's siblings
        $enrollment->show_siblings = true;

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->setData(new EnrollmentResource($enrollment))
            ->getResponse();
    }

    public function updateStatus(Enrollment $enrollment, EnrollmentStatusUpdateRequest $request): JsonResponse
    {
        $input = $request->validated();
        $status = EnrollmentStatus::from($input['status']);

        $enrollment = $this->enrollmentService->setEnrollment($enrollment)
            ->updateEnrollmentStatus($status);

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->setData(new EnrollmentResource($enrollment))
            ->getResponse();
    }

    /**
     * BO - admin to update enrollment expiry.
     */
    public function extendExpiry(Enrollment $enrollment, EnrollmentExtendExpiryRequest $request): JsonResponse
    {
        $input = $request->validated();

        $enrollment = $this->enrollmentService->extendExpiry($enrollment, Carbon::parse($input['expiry_date']));

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->setData(new EnrollmentResource($enrollment))
            ->getResponse();
    }

    public function printDetails(Enrollment $enrollment, EnrollmentPrintDetailRequest $request): JsonResponse
    {
        $input = $request->validated();

        app()->setLocale($input['report_language']);

        $enrollment->load([
            'admissionGrade',
            'nationality',
            'race',
            'religion',
            'state',
            'country',
            'enrollmentSession',
            'healthConcern',
            'primarySchool',
            'guardians.nationality',
            'guardians.race',
            'guardians.religion',
            'guardians.education',
            'enrollmentExams',
        ]);

        $report_view_name = 'reports.enrollment.enrollment-details';
        $file_name = 'enrollment-details';

        $report_data = [
            'enrollment' => $enrollment,
        ];

        $report_view = view($report_view_name, $report_data);

        $adapter = ExportAdapterFactory::getAdapterFor(ExportType::PDF); // always PDF
        $adapter->setReportBuilder(new GenericExcelExportViaView($report_view))
            ->setReportViewName($report_view_name)
            ->setReportData($report_data);

        $url = $this->reportPrintService->setExportFileAdapter($adapter)
            ->setFileName(FileHelper::generateFileName($file_name))
            ->generate()
            ->upload()
            ->getFileUrl();

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->setData(['url' => $url])
            ->getResponse();
    }

    public function downloadPrePaymentTemplate(EnrollmentDownloadTemplateRequest $request)
    {
        $input = $request->validated();

        $report_data = $this->enrollmentService->getTemplateData($input['enrollment_session_id'], false);

        $report_view_name = 'templates.enrollment-template';
        $file_name = 'enrollment-template';

        $export_type = ExportType::EXCEL;
        $report_view = view($report_view_name, $report_data);

        $adapter = ExportAdapterFactory::getAdapterFor($export_type);
        $adapter->setReportBuilder(new GenericExcelExportViaView($report_view))
            ->setReportViewName($report_view_name)
            ->setReportData($report_data);

        $url = $this->reportPrintService->setExportFileAdapter($adapter)
            ->setFileName(FileHelper::generateFileName($file_name))
            ->generate()
            ->upload()
            ->getFileUrl();

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->setData(['url' => $url])
            ->getResponse();
    }

    public function downloadPostPaymentTemplate(EnrollmentDownloadTemplateRequest $request)
    {
        $input = $request->validated();

        $report_data = $this->enrollmentService->getTemplateData($input['enrollment_session_id'], true);

        $report_view_name = 'templates.enrollment-template-post-payment';
        $file_name = 'enrollment-template';

        $export_type = ExportType::EXCEL;
        $report_view = view($report_view_name, $report_data);

        $adapter = ExportAdapterFactory::getAdapterFor($export_type);
        $adapter->setReportBuilder(new GenericExcelExportViaView($report_view))
            ->setReportViewName($report_view_name)
            ->setReportData($report_data);

        $url = $this->reportPrintService->setExportFileAdapter($adapter)
            ->setFileName(FileHelper::generateFileName($file_name))
            ->generate()
            ->upload()
            ->getFileUrl();

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->setData(['url' => $url])
            ->getResponse();
    }

    /**
     * Validate the enrollment import template (FULL).
     */
    public function importTemplateValidation(EnrollmentImportTemplateValidationRequest $request): JsonResponse
    {
        $input = $request->validated();

        $enrollment_session = $this->enrollmentSessionRepository->find($input['enrollment_session_id']);

        $response = $this->enrollmentService
            ->setImportFile($input['file'])
            ->setEnrollmentSession($enrollment_session)
            ->transformExcelToCollection()
            ->validatePrePaymentImport()
            ->getValidatedData();

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->setData($response)
            ->getResponse();
    }

    /**
     * save the enrollment import template (FULL).
     */
    public function bulkSaveImportedData(EnrollmentBulkSaveImportedDataRequest $request): JsonResponse
    {
        $input = $request->all();

        $enrollment_session = $this->enrollmentSessionRepository->find($input['enrollment_session_id']);

        try {
            $response = $this->enrollmentService
                ->setData($input['enrollments'])
                ->setEnrollmentSession($enrollment_session)
                ->validatePrepaymentImport();

            if ($response->getValidatedData()['error_count'] > 0) {
                return (new ApiResponse())
                    ->setStatus(ApiResponse::STATUS_ERROR)
                    ->setError(__('api.common.error'))
                    ->setCode(422)
                    ->setData($response->getValidatedData())
                    ->getResponse();
            }

            $response->savePrePaymentImportedData();

            return (new ApiResponse())
                ->setMessage(__('api.common.success'))
                ->setCode(200)
                ->getResponse();
        } catch (\Exception $e) {
            Log::error('Enrollment bulkSaveImportedData function error: ' . $e->getMessage(), [
                'exception' => $e,
            ]);

            return (new ApiResponse())
                ->setError($e->getMessage())
                ->setCode($e->getCode())
                ->getResponse();
        }
    }

    /**
     * Validate the enrollment import template (POST PAYMENT).
     */
    public function importPostPaymentTemplateValidation(EnrollmentImportTemplateValidationRequest $request): JsonResponse
    {
        $input = $request->validated();

        $enrollment_session = $this->enrollmentSessionRepository->find($input['enrollment_session_id']);

        $response = $this->enrollmentService
            ->setImportFile($input['file'])
            ->setEnrollmentSession($enrollment_session)
            ->transformPostPaymentExcelToCollection()
            ->validatePostPaymentImport()
            ->getValidatedData();

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->setData($response)
            ->getResponse();
    }

    /**
     * save the enrollment import template (POST PAYMENT).
     */
    public function bulkSavePostPaymentImportedData(EnrollmentBulkSavePostPaymentImportedDataRequest $request): JsonResponse
    {
        $input = $request->all();

        $enrollment_session = $this->enrollmentSessionRepository->find($input['enrollment_session_id']);

        try {
            $response = $this->enrollmentService
                ->setData($input['enrollments'])
                ->setEnrollmentSession($enrollment_session)
                ->validatePostPaymentImport();

            if ($response->getValidatedData()['error_count'] > 0) {
                return (new ApiResponse())
                    ->setMessage(__('api.common.error'))
                    ->setCode(422)
                    ->setData($response->getValidatedData())
                    ->getResponse();
            }

            $response->savePostPaymentImportedData();

            return (new ApiResponse())
                ->setMessage(__('api.common.success'))
                ->setCode(200)
                ->getResponse();
        } catch (\Exception $e) {
            Log::error('Enrollment bulkSavePostPaymentImportedData function error: ' . $e->getMessage(), [
                'exception' => $e,
            ]);

            return (new ApiResponse())
                ->setError($e->getMessage())
                ->setCode($e->getCode())
                ->getResponse();
        }
    }

    /**
     * For FE only
     */
    public function makePayment(Enrollment $enrollment, EnrollmentMakePaymentRequest $request): JsonResponse
    {
        $user = auth()->user();

        $input = $request->validated();
        $input['is_manual_payment_requested'] = $input['is_manual_payment_requested'] ?? false;

        if (!$enrollment->canBeAccessedByUser($user)) {
            return (new ApiResponse())
                ->setStatus(ApiResponse::STATUS_ERROR)
                ->setError(__('api.common.unauthorized'))
                ->setCode(403)
                ->getResponse();
        }

        if (!$enrollment->canBeUpdated()) {
            ErrorCodeHelper::throwError(ErrorCodeHelper::ENROLLMENT_ERROR, 5029);
        }

        try {
            if ($enrollment->payment_status !== EnrollmentPaymentStatus::UNPAID) {
                ErrorCodeHelper::throwError(ErrorCodeHelper::ENROLLMENT_ERROR, 5021);
            }

            $fees = $enrollment->determineFees();

            // If no payment, just set payment status to NOT_REQUIRED
            if (empty($fees)) {
                $this->enrollmentService->setEnrollment($enrollment)
                    ->updatePaymentStatus(EnrollmentPaymentStatus::NOT_REQUIRED);

                return (new ApiResponse())
                    ->setMessage(__('api.common.success'))
                    ->setData(['payment_required' => false])
                    ->setCode(200)
                    ->getResponse();
            }

            $payment_gateway_log = DB::transaction(function () use ($enrollment, $user, $fees, $input) {
                // only ( enrollment->payment_status = UNPAID ) able to create billing document to do payment
                $system = SystemHelper::getSystemEmployee();

                $unpaid_items = collect();

                foreach ($fees as $fee) {
                    /** @var UnpaidItemService */
                    $unpaid_item_service = app()->make(UnpaidItemService::class);

                    $unpaid_item_service
                        ->setCurrency(config('school.currency_code'))
                        ->setCreatedByEmployee($system)
                        ->setProduct($fee['product'])
                        ->setUnitPrice($fee['amount'])
                        ->setQuantity(1)
                        ->setBillToParty($enrollment)
                        ->setDescription($fee['product']->name . " ({$fee['period']})")
                        ->setPeriod($fee['period'])
                        ->createNewUnpaidItem();

                    $unpaid_items->push($unpaid_item_service->getUnpaidItem());
                }

                /** @var BillingDocumentService $invoice_service */
                $invoice_service = app()->make(BillingDocumentService::class);

                $invoice_service
                    ->init()
                    ->setType(BillingDocument::TYPE_INVOICE)
                    ->setSubType(BillingDocument::SUB_TYPE_ENROLLMENT_FEES)
                    ->setDefaultValuesForPayingUnpaidItems()
                    ->setCurrency(config('school.currency_code'))
                    ->setStatus(BillingDocument::STATUS_CONFIRMED)
                    ->setBillToParty($enrollment);

                foreach ($unpaid_items as $unpaid_item) {
                    /** @var BillingDocumentLineItemService $line_item_service */
                    $line_item_service = app()->make(BillingDocumentLineItemService::class);

                    $line_item = $line_item_service
                        ->setBillableItem($unpaid_item)
                        ->setQuantity($unpaid_item->quantity)
                        ->setUnitPrice($unpaid_item->unit_price)
                        ->setGlAccountCode($unpaid_item->gl_account_code)
                        ->setDescription($unpaid_item->description)
                        ->setAmountBeforeTax($unpaid_item->amount_before_tax)
                        ->setProduct($unpaid_item->product)
                        ->setCurrency($unpaid_item->currency_code)
                        ->make();

                    $invoice_service->addLineItem($line_item);
                }

                $billing_document = $invoice_service
                    ->calculateAmountBeforeTax()
                    ->applyTax(SystemHelper::getNotApplicableTax())
                    ->calculatePaymentDueDate()
                    ->generateReferenceNumber()
                    ->create()
                    ->calculateEligibleDiscounts()
                    ->addDiscountLineItems()
                    ->applyAdvanceOffset()      // deduct advance
                    ->createAdvanceOffsetTransactions()
                    ->recalculateAndUpdateAfterDiscount()
                    ->getBillingDocument();

                // assign billing document to unpaid items, marking unpaid items status as PENDING
                app()->make(UnpaidItemService::class)
                    ->setUnpaidItems($unpaid_items)
                    ->assignBillingDocument($billing_document)
                    ->save();

                $enrollment->update([
                    'payment_status' => EnrollmentPaymentStatus::PENDING,
                    'billing_document_id' => $billing_document->id,
                ]);

                if ($input['is_manual_payment_requested']) {
                    Log::info('Manual payment requested: ' . $enrollment->id, [
                        'billing_document_id' => $billing_document->id,
                        'user_id' => $user->id,
                    ]);

                    // create mock payment gateway log
                    $payment_gateway_log = new PaymentGatewayLog();
                    $payment_gateway_log->billing_document_id = $billing_document->id;
                    $payment_gateway_log->amount = $billing_document->amount_after_tax;
                    $payment_gateway_log->currency_code = $billing_document->currency_code;
                    $payment_gateway_log->created_by = $user->id;
                    $payment_gateway_log->payment_required = false;
                    $payment_gateway_log->is_manual_payment = true;

                    return $payment_gateway_log;
                } else {
                    Log::info('Generating payment gateway log for enrollment: ' . $enrollment->id, [
                        'billing_document_id' => $billing_document->id,
                        'user_id' => $user->id,
                    ]);

                    return $this->enrollmentService
                        ->setEnrollmentUser($user)
                        ->setBillingDocument($billing_document)
                        ->setEnrollment($enrollment)
                        ->setReturnUrl($input['return_url'] ?? null)
                        ->generatePaymentGatewayLogWithPaymentLink();
                }
            });

            return (new ApiResponse())
                ->setMessage(__('api.common.success'))
                ->setData(new PaymentGatewayLogResource($payment_gateway_log))
                ->setCode(200)
                ->getResponse();
        } catch (\Exception $exception) {
            return (new ApiResponse())
                ->setError($exception->getMessage())
                ->setCode($exception->getCode() ?: 500)
                ->getResponse();
        }
    }

    /**
     * For FE only
     */
    public function retryPayment(BillingDocument $billing_document, EnrollmentRetryPaymentRequest $request): JsonResponse
    {
        $user = auth()->user();

        $input = $request->validated();

        /** @var Enrollment */
        $enrollment = $billing_document->billTo;

        if (!$enrollment->canBeUpdated()) {
            ErrorCodeHelper::throwError(ErrorCodeHelper::ENROLLMENT_ERROR, 5029);
        }

        if (!$enrollment instanceof Enrollment || !$enrollment->canBeAccessedByUser($user)) {
            return (new ApiResponse())
                ->setStatus(ApiResponse::STATUS_ERROR)
                ->setError(__('api.common.unauthorized'))
                ->setCode(403)
                ->getResponse();
        }

        try {
            $payment_gateway_log = $this->enrollmentService
                ->setEnrollmentUser($user)
                ->setBillingDocument($billing_document)
                ->setEnrollment($enrollment)
                ->setReturnUrl($input['return_url'] ?? null)
                ->requestPaymentLink();

            return (new ApiResponse())
                ->setMessage(__('api.common.success'))
                ->setData(new PaymentGatewayLogResource($payment_gateway_log))
                ->setCode(200)
                ->getResponse();
        } catch (\Exception $exception) {
            return (new ApiResponse())
                ->setError($exception->getMessage())
                ->setCode($exception->getCode() ?: 500)
                ->getResponse();
        }
    }

    public function postToAutoCount(PostEnrollmentsToAutocountRequest $request)
    {
        $input = $request->validated();

        $service = $this->enrollmentPostingService
            ->setPaymentDateFrom($input['payment_date_from'])
            ->setPaymentDateTo($input['payment_date_to']);

        if (isset($input['type'])) {
            $service->setType($input['type']);
        }

        if (isset($input['status'])) {
            $service->setStatus($input['status']);
        }

        if (isset($input['reference_no'])) {
            $service->setReferenceNo($input['reference_no']);
        }

        $url = $service
            ->query()
            ->generateExcelAndGetFileUrl();

        if ($url !== null) {
            return (new ApiResponse())
                ->setMessage(__('api.common.success'))
                ->setCode(200)
                ->setData([
                    'url' => $url
                ])
                ->getResponse();
        } else {
            return (new ApiResponse())
                ->setStatus(ApiResponse::STATUS_OK)
                ->setError(__('api.common.not_found'))
                ->setHttpCode(400)
                ->setCode(404)
                ->getResponse();
        }
    }
}
