<?php

namespace App\Http\Controllers\Debug;

use App\Http\Controllers\Controller;
use App\Models\BillingDocument;
use App\Models\RunningNumber;
use App\Services\DocumentRunningNumberService;
use Illuminate\Http\Request;

class TestController extends Controller
{
    public function ip(Request $request) {

        $headers = collect($request->header())->transform(function ($item) {
            return $item[0];
        });

        return [
            'ip' => $request->ip(),
            'ips' => $request->ips(),
            'headers' => $headers,
            'trusted' => $request->isFromTrustedProxy(),
        ];

    }

    public function test() {

        return \DB::transaction(function () {

            // test race condition for running number
            /*$number1 = app(DocumentRunningNumberService::class)
                ->setDocumentType(BillingDocument::class)
                ->setYear('2030')
                ->setIdentifier1('INV')
                ->addCustomComponent('INV')
                ->addPresetComponent(DocumentRunningNumberService::PLACEHOLDER_YEAR)
                ->addPresetComponent(DocumentRunningNumberService::PLACEHOLDER_RUNNING_NUMBER)
                ->generate();

            $number2 = app(DocumentRunningNumberService::class)
                ->setDocumentType(BillingDocument::class)
                ->setYear('2030')
                ->setIdentifier1('INV')
                ->addCustomComponent('INV')
                ->addPresetComponent(DocumentRunningNumberService::PLACEHOLDER_YEAR)
                ->addPresetComponent(DocumentRunningNumberService::PLACEHOLDER_RUNNING_NUMBER)
                ->generate();

            $number3 = app(DocumentRunningNumberService::class)
                ->setDocumentType(BillingDocument::class)
                ->setYear('2030')
                ->setIdentifier1('INV')
                ->addCustomComponent('INV')
                ->addPresetComponent(DocumentRunningNumberService::PLACEHOLDER_YEAR)
                ->addPresetComponent(DocumentRunningNumberService::PLACEHOLDER_RUNNING_NUMBER)
                ->generate();

            sleep(3);

            $rows = RunningNumber::where('document_type', BillingDocument::class)
                ->where('year', '2030')
                ->count();

            return response()->json([
                'data' => $number1 . ', ' . $number2 . ', ' . $number3,
                'num_rows' => $rows,
            ]);*/

        });

    }
}
