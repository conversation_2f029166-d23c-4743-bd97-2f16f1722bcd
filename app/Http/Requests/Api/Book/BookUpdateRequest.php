<?php

namespace App\Http\Requests\Api\Book;

use App\Enums\BookBinding;
use App\Enums\BookCondition;
use App\Enums\BookLoanSettingType;
use App\Enums\BookStatus;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class BookUpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $rules = [
            'book_no' => ['required', 'string', Rule::unique('books', 'book_no')->ignore($this->book->id)],
            'call_no' => ['required', 'string'],
            'book_category_id' => ['required', 'integer', 'exists:master_book_categories,id'],
            'book_classification_id' => ['required', 'integer', 'exists:master_book_classifications,id'],
            'book_sub_classification_id' => ['required', 'integer', 'exists:master_book_sub_classifications,id'],
            'title' => ['required', 'string'],
            'series' => ['nullable', 'string'],
            'edition' => ['nullable', 'string'],
            'remark' => ['nullable', 'string'],
            'topic' => ['nullable', 'string'],
            'location_1' => ['nullable', 'string'],
            'location_2' => ['nullable', 'string'],
            'isbn' => ['nullable', 'string'],
            'book_source_id' => ['required', 'integer', 'exists:master_book_sources,id'],
            'binding' => ['required', Rule::in(BookBinding::values())],
            'publisher' => ['nullable', 'string'],
            'publisher_place' => ['nullable', 'string'],
            'published_date' => ['nullable', 'date'],
            'book_page' => ['nullable', 'integer'],
            'words' => ['nullable', 'integer'],
            'book_size' => ['nullable', 'string'],
            'cdrom' => ['required', 'boolean'],
            'purchase_value' => ['required', 'numeric', 'min:0'],
            'lost_penalty_value' => ['required', 'numeric', 'min:0'],
            'authors' => ['required', 'array'],
            'authors.*' => ['nullable', 'string'],
            'entry_date' => ['nullable', 'date'],
            'condition' => [
                'nullable',
                Rule::in(BookCondition::values()),
                function ($attribute, $value, $fail) {
                    if ($value != $this->book->condition->value && $this->book->status === BookStatus::BORROWED) {
                        $fail('Cannot update book condition when book is borrowed.');
                    }
                }
            ],
            'book_language_id' => ['nullable', 'integer', 'exists:master_book_languages,id'],
        ];

        foreach (BookLoanSettingType::all() as $type) {
            $rules = array_merge($rules, [
                'loan_settings.' . $type . '.loan_period_day' => ['required', 'integer'],
                'loan_settings.' . $type . '.can_borrow' => ['required', 'boolean'],
            ]);
        }

        return $rules;
    }

    public function attributes()
    {
        $attributes = [];

        foreach (BookLoanSettingType::all() as $type) {
            $attributes['loan_settings.' . $type . '.loan_period_day'] = 'loan settings.' . $type . '.loan period day';
            $attributes['loan_settings.' . $type . '.can_borrow'] = 'loan settings.' . $type . '.can borrow';
        }

        return $attributes;
    }
}
