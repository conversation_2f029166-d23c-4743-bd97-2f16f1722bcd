<?php

namespace App\Http\Requests\Api\Reports\Academy;

use App\Enums\ExportType;
use App\Models\SemesterClass;
use App\Repositories\StudentRepository;
use App\Rules\CheckExistFromRepositoryRule;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class StudentMeritAndExceptionalPerformanceReportRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'report_language' => ['required', 'exists:master_internationalization,code'],
            'semester_class_id' => ['required', Rule::exists(SemesterClass::class, 'id')],
            'export_type' => ['required', Rule::in([ExportType::PDF])],
            'student_ids' => ['required', 'array', new CheckExistFromRepositoryRule(StudentRepository::class)],
        ];
    }
}
