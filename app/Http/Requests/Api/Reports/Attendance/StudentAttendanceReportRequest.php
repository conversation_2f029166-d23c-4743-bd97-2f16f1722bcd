<?php

namespace App\Http\Requests\Api\Reports\Attendance;

use App\Enums\AttendanceCheckInStatus;
use App\Enums\AttendanceStatus;
use App\Enums\ClassType;
use App\Enums\ExportType;
use App\Models\SemesterClass;
use App\Models\SemesterSetting;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class StudentAttendanceReportRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'report_language' => ['required', 'exists:master_internationalization,code'],
            'date_from' => ['required', 'date_format:Y-m-d'],
            'date_to' => ['required', 'date_format:Y-m-d', 'after_or_equal:date_from'],
            'semester_setting_id' => ['required', Rule::exists(SemesterSetting::class, 'id')],
            'semester_class_id' => ['nullable', Rule::exists(SemesterClass::class, 'id')],
            'class_type' => ['required', Rule::enum(ClassType::class)],
            'status' => [
                'nullable',
                Rule::in([
                    AttendanceStatus::PRESENT->value,
                    AttendanceStatus::ABSENT->value,
                    AttendanceCheckInStatus::LATE->value,
                ])
            ],
            'export_type' => ['nullable', Rule::in([ExportType::PDF->value])],
        ];
    }
}
