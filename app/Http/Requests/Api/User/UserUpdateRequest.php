<?php

namespace App\Http\Requests\Api\User;

use App\Repositories\RoleRepository;
use App\Rules\CheckExistFromRepositoryRule;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UserUpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'is_active' => ['required', 'boolean'],
            'password' => ['nullable', 'required_with:password_confirmation', 'confirmed'],
            'role_ids' => ['nullable', 'array', new CheckExistFromRepositoryRule(RoleRepository::class)],
            'email' => ['nullable', 'email', 'max:100', Rule::unique('users', 'email')->ignore($this->user->id)],
            'phone_number' => ['nullable', 'phone', 'max:20', Rule::unique('users', 'phone_number')->ignore($this->user->id)],
        ];
    }
}
