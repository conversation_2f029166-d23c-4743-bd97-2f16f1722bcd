<?php

namespace App\Http\Requests\Api\Student;

use App\Enums\ClassStream;
use App\Enums\Gender;
use App\Enums\StudentAdmissionType;
use App\Http\Requests\Api\CommonApiValidationRequest;
use Illuminate\Validation\Rule;

class StudentIndexRequest extends CommonApiValidationRequest
{

    protected function prepareForValidation(): void
    {
        $input = $this->all();

        $keys = [
            'grade_id' => 'grade_ids',
            'semester_class_id' => 'semester_class_ids'
        ];

        foreach ($keys as $key => $key_array) {
            if (isset($input[$key]) && is_array($input[$key])) {
                $input[$key_array] = $input[$key];

                unset($input[$key]);
            }
        }

        $this->replace($input);
    }

    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return array_merge(parent::rules(), [
            'name' => ['nullable'],
            'admission_grade_id' => ['nullable'],
            'admission_type' => ['nullable', Rule::in(StudentAdmissionType::values())],
            'gender' => ['nullable', Rule::in(Gender::values())],
            'guardian_name' => ['nullable', 'string'],
            'guardian_phone_number' => ['nullable', 'string', 'max:20'],
            'guardian_email' => ['nullable', 'string', 'max:100'],
            'race_id' => ['nullable'],
            'religion_id' => ['nullable'],
            'state_id' => ['nullable'],
            'country_id' => ['nullable'],
            'is_hostel' => ['nullable', 'boolean'],
            'has_active_bed' => ['nullable', 'boolean'],
            'student_number' => ['nullable'],
            'student_number_wildcard' => ['nullable'],
            'block_id' => ['nullable', 'exists:hostel_blocks,id'],
            'room_id' => ['nullable', 'exists:hostel_rooms,id'],
            'bed_id' => ['nullable', 'exists:hostel_room_beds,id'],
            'is_active' => ['nullable', 'boolean'],
            'semester_setting_id' => ['nullable', 'exists:master_semester_settings,id'],
            'grade_ids' => ['nullable', 'array'],
            'grade_ids.*' => ['nullable', 'exists:master_grades,id'],
            'grade_id' => ['nullable', 'exists:master_grades,id'],
            'semester_class_ids' => ['nullable', 'array'],
            'semester_class_ids.*' => ['nullable', 'exists:semester_classes,id'],
            'semester_class_id' => ['nullable', 'exists:semester_classes,id'],
            'class_stream' => ['nullable', Rule::in(ClassStream::values())],
            'is_checked_out' => ['nullable', 'boolean'],
            'common_search' => ['nullable'],
            'only_active_class' => ['nullable', 'boolean'],
            'attendance_date' => ['nullable', 'date'],
            'is_latest_class_in_semester' => ['nullable', 'boolean'],
        ]);
    }
}
