<?php

namespace App\Http\Requests\Api\Subject;

use App\Enums\SubjectType;
use App\Rules\InternationalizationValidation;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class SubjectUpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'type' => ['required', Rule::in(SubjectType::values())],
            'club_id' => ['nullable', 'required_if:type,' . SubjectType::COCURRICULUM->value, 'exists:clubs,id'],
            'code' => ['required', 'string', Rule::unique('subjects', 'code')->ignore($this->subject->id)],
            'name' => ['required', 'array'],
            'name.*' => ['nullable', 'string', new InternationalizationValidation()],
        ];
    }

    protected function prepareForValidation(): void
    {
        $this->merge([
            'code' => isset($this->code) ? strtoupper($this->code) : null,
        ]);
    }
}
