<?php

namespace App\Http\Requests\Api\Subject;

use App\Enums\SubjectType;
use App\Http\Requests\Api\CommonApiValidationRequest;
use Illuminate\Validation\Rule;

class SubjectIndexRequest extends CommonApiValidationRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return array_merge(parent::rules(), [
            'types' => ['nullable', 'array', Rule::in(SubjectType::values())],
            'club_id' => ['nullable', 'exists:clubs,id'],
            'code' => ['nullable', 'string'],
            'name' => ['nullable'],
        ]);
    }

    protected function prepareForValidation(): void
    {
        $this->merge([
            'code' => isset($this->code) ? strtoupper($this->code) : null,
        ]);
    }
}
