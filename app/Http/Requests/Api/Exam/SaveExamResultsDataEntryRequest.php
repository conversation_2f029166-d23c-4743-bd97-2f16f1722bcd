<?php

namespace App\Http\Requests\Api\Exam;

use App\Http\Requests\Api\CommonApiValidationRequest;

class SaveExamResultsDataEntryRequest extends CommonApiValidationRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'result_source_subject_id' => 'required|integer|exists:result_source_subjects,id',
            'result_source_subject_component_id' => 'nullable|integer|exists:result_source_subject_components,id',
            'score' => 'nullable|integer|min:0|max:100',
        ];
    }
}
