<?php

namespace App\Http\Requests\Api\EcommerceProduct;

use App\Enums\MerchantType;
use App\Http\Requests\Api\CommonApiValidationRequest;
use App\Interfaces\Userable;
use Illuminate\Validation\Rule;

class EcommerceProductIndexRequest extends CommonApiValidationRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return array_merge(parent::rules(), [
            'userable_id' => ['nullable', Rule::exists('userable_views', 'userable_id')],
            'userable_type' => ['nullable', Rule::in(Userable::USERABLE_TYPES)],
            'name' => ['nullable', 'string'],
            'code' => ['nullable', 'string'],
            'delivery_date_from' => ['nullable', 'date'],
            'delivery_date_to' => ['nullable', 'date', 'after:delivery_date_from'],
            'merchant_id' => ['nullable', 'integer'],
            'category_id' => ['nullable', 'integer'],
            'sub_category_id' => ['nullable', 'integer'],
            'tag_id' => ['nullable', 'integer'],
            'group_id' => ['nullable', 'integer'],
            'is_active' => ['nullable', 'boolean'],
            'merchant_type' => ['nullable', Rule::in(MerchantType::values())],
        ]);
    }

    protected function prepareForValidation(): void
    {
        $this->merge([
            'code' => isset($this->code) ? strtoupper($this->code) : null,
        ]);
    }
}
