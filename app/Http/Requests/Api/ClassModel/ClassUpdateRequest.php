<?php

namespace App\Http\Requests\Api\ClassModel;

use App\Enums\ClassStream;
use App\Enums\ClassType;
use App\Enums\EnglishLevel;
use App\Models\ClassModel;
use App\Rules\InternationalizationValidation;
use App\Rules\RequiredLocaleValidation;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class ClassUpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name' => ['required', 'array', new RequiredLocaleValidation()],
            'name.*' => [new InternationalizationValidation(), 'string', 'max:100'],
            'code' => ['required', 'string', Rule::unique(ClassModel::class, 'code')->ignore($this->class->id)],
            'type' => ['required', Rule::in(ClassType::values())],
            'stream' => ['required', Rule::in(ClassStream::values())],
            'english_level' => ['required_if:type,' . ClassType::ENGLISH->value, Rule::in(EnglishLevel::values())],
            'grade_id' => ['nullable', 'required_if:type,PRIMARY', 'exists:master_grades,id'],
            'is_active' => ['required', 'boolean'],
        ];
    }

    protected function prepareForValidation(): void
    {
        $this->merge([
            'code' => isset($this->code) ? strtoupper($this->code) : null,
        ]);
    }
}
