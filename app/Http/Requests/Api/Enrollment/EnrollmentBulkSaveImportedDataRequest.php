<?php

namespace App\Http\Requests\Api\Enrollment;

use Illuminate\Foundation\Http\FormRequest;

class EnrollmentBulkSaveImportedDataRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'enrollment_session_id' => ['required', 'exists:enrollment_sessions,id'],
            'enrollments' => ['required', 'array'],
            'enrollments.*.exam_slip_number' => ['nullable', 'string'],
            'enrollments.*.student_name_en' => ['nullable', 'string'],
            'enrollments.*.student_name_zh' => ['nullable', 'string'],
            'enrollments.*.nric' => ['nullable', 'string'],
            'enrollments.*.passport_number' => ['nullable', 'string'],
            'enrollments.*.religion' => ['nullable', 'string'],
            'enrollments.*.gender' => ['nullable', 'string'],
            'enrollments.*.guardian_phone_number' => ['nullable', 'string'],
            'enrollments.*.guardian_email' => ['nullable', 'string'],
            'enrollments.*.guardian_name' => ['nullable', 'string'],
            'enrollments.*.guardian_type' => ['nullable', 'string'],
            'enrollments.*.total_average' => ['nullable', 'string'],
            'enrollments.*.status' => ['nullable', 'string'],
            'enrollments.*.address' => ['nullable', 'string'],
            'enrollments.*.primary_school' => ['nullable', 'string'],
            'enrollments.*.hostel' => ['nullable', 'boolean'],
            'enrollments.*.hostel_reason' => ['nullable'],
            'enrollments.*.have_siblings' => ['nullable', 'boolean'],
            'enrollments.*.dietary_restriction' => ['nullable', 'string'],
            'enrollments.*.health_concern' => ['nullable', 'string'],
            'enrollments.*.foreigner' => ['nullable', 'boolean'],
            'enrollments.*.nationality' => ['nullable', 'string'],
            'enrollments.*.conduct' => ['nullable', 'string'],
            'enrollments.*.remarks' => ['nullable', 'string'],
            'enrollments.*.register_date' => ['nullable', 'date'],
            'enrollments.*.expiry_date' => ['nullable', 'date'],
        ];
    }

    public function attributes(): array
    {
        return [
            'enrollments.*.exam_slip_number' => 'exam slip number',
            'enrollments.*.student_name_en' => 'student name (en)',
            'enrollments.*.student_name_zh' => 'student name (zh)',
            'enrollments.*.nric' => 'nric',
            'enrollments.*.passport_number' => 'passport number',
            'enrollments.*.religion' => 'religion',
            'enrollments.*.gender' => 'gender',
            'enrollments.*.guardian_phone_number' => 'guardian phone number',
            'enrollments.*.guardian_email' => 'guardian email',
            'enrollments.*.guardian_name' => 'guardian name',
            'enrollments.*.guardian_type' => 'guardian type',
            'enrollments.*.total_average' => 'total average',
            'enrollments.*.status' => 'status',
            'enrollments.*.address' => 'address',
            'enrollments.*.primary_school' => 'primary school',
            'enrollments.*.hostel' => 'hostel',
            'enrollments.*.hostel_reason' => 'hostel reason',
            'enrollments.*.have_siblings' => 'have siblings',
            'enrollments.*.dietary_restriction' => 'dietary restriction',
            'enrollments.*.health_concern' => 'health concern',
            'enrollments.*.foreigner' => 'foreigner',
            'enrollments.*.nationality' => 'nationality',
            'enrollments.*.conduct' => 'conduct',
            'enrollments.*.remarks' => 'remarks',
            'enrollments.*.register_date' => 'register date',
            'enrollments.*.expiry_date' => 'expiry date',
        ];
    }
}
