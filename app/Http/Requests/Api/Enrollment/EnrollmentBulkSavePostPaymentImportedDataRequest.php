<?php

namespace App\Http\Requests\Api\Enrollment;

use Illuminate\Foundation\Http\FormRequest;

class EnrollmentBulkSavePostPaymentImportedDataRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'enrollment_session_id' => ['required', 'exists:enrollment_sessions,id'],
            'enrollments' => ['required', 'array'],
            'enrollments.*.exam_slip_number' => ['nullable', 'string'],
            'enrollments.*.student_name_en' => ['nullable', 'string'],
            'enrollments.*.nric' => ['nullable', 'string'],
            'enrollments.*.passport_number' => ['nullable', 'string'],
            'enrollments.*.hostel' => ['nullable'],
            'enrollments.*.total_average' => ['nullable', 'string'],
            'enrollments.*.status' => ['nullable', 'string'],
        ];
    }

    public function attributes(): array
    {
        return [
            'enrollments.*.student_name_en' => 'student name (en)',
            'enrollments.*.nric' => 'nric',
            'enrollments.*.passport_number' => 'passport number',
            'enrollments.*.hostel' => 'hostel',
            'enrollments.*.total_average' => 'total average',
            'enrollments.*.status' => 'status',
        ];
    }
}
