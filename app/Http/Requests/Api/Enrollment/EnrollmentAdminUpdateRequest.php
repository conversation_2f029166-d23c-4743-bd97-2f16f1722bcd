<?php

namespace App\Http\Requests\Api\Enrollment;

use App\Enums\DietaryRestriction;
use App\Enums\EnrollmentStatus;
use App\Enums\Gender;
use App\Enums\GuardianType;
use App\Enums\LiveStatus;
use App\Enums\MarriedStatus;
use App\Enums\SchoolLevel;
use App\Enums\StudentAdmissionType;
use App\Enums\StudentLeaveStatus;
use App\Repositories\CountryRepository;
use App\Repositories\EducationRepository;
use App\Repositories\RaceRepository;
use App\Repositories\ReligionRepository;
use App\Repositories\StudentRepository;
use App\Rules\InternationalizationValidation;
use App\Rules\RequiredLocaleValidation;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class EnrollmentAdminUpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     * @throws \Exception
     */
    public function rules(): array
    {
        $enrollment = $this->route('enrollment');
        $enrollment->loadMissing(['enrollmentExams.examMarks']);

        $valid_exam_ids = $enrollment->enrollmentExams->pluck('id')->filter()->unique()->toArray();

        $valid_exam_marks_ids = $enrollment->enrollmentExams->flatMap(function ($exam) {
            return $exam->examMarks->pluck('id');
        })->filter()->unique()->toArray();

        $guardians_payload = collect($this->input('guardians', []));

        // NATIONALITY_ID
        $nationality_ids_payload = $guardians_payload->pluck('nationality_id')->filter()->unique()->toArray();
        $existing_nationality_ids = (new CountryRepository())->find($nationality_ids_payload)->pluck('id')->toArray();

        // RACE_ID
        $race_ids_payload = $guardians_payload->pluck('race_id')->filter()->unique()->toArray();
        $existing_race_ids = (new RaceRepository())->find($race_ids_payload)->pluck('id')->toArray();

        // RELIGION_ID
        $religion_ids_payload = $guardians_payload->pluck('religion_id')->filter()->unique()->toArray();
        $existing_religion_ids = (new ReligionRepository())->find($religion_ids_payload)->pluck('id')->toArray();

        // EDUCATION_ID
        $education_ids_payload = $guardians_payload->pluck('education_id')->filter()->unique()->toArray();
        $existing_education_ids = (new EducationRepository())->find($education_ids_payload)->pluck('id')->toArray();

        // STUDENT SIBLINGS
        $sibling_student_numbers = $this->input('sibling_student_numbers', []);
        $sibling_student_numbers = collect($sibling_student_numbers)->filter()->unique()->toArray();
        $existing_sibling_student_numbers = (new StudentRepository())->getAll(['student_number' => $sibling_student_numbers])->pluck('student_number')->toArray();

        return [
            'name' => ['required', 'array', new RequiredLocaleValidation()],
            'name.*' => [new InternationalizationValidation(), 'string', 'max:100'],
            'email' => ['nullable', 'email'],
            'phone_number' => ['nullable', 'phone', 'max:20'],
            'phone_number_2' => ['nullable', 'phone', 'max:20'],
            'nric' => ['nullable', 'numeric', 'digits:12', 'required_without:passport_number', Rule::unique('students', 'nric')],
            'passport_number' => ['nullable', 'string', 'max:50', 'required_without:nric', Rule::unique('students', 'passport_number')],
            'admission_year' => ['nullable', 'numeric', 'digits:4'],
            'admission_grade_id' => ['nullable', 'exists:master_grades,id'],
            'join_date' => ['nullable', 'date'],
            'leave_date' => ['nullable', 'date'],
            'leave_status' => ['nullable', 'string', Rule::in(StudentLeaveStatus::values())],
            'birthplace' => ['required', 'string', 'max:100'],
            'nationality_id' => ['required', 'exists:master_countries,id'],
            'date_of_birth' => ['required', 'date', 'before:today'],
            'gender' => ['required', Rule::in(Gender::values())],
            'birth_cert_number' => ['required', 'string', 'max:50', Rule::unique('students', 'birth_cert_number')],
            'race_id' => ['required', 'exists:master_races,id'],
            'religion_id' => ['required', 'exists:master_religions,id'],
            'address' => ['required', 'string', 'max:255'],
            'postal_code' => ['required', 'string', 'max:20'],
            'city' => ['required', 'string', 'max:100'],
            'state_id' => ['required', 'exists:master_states,id'],
            'country_id' => ['required', 'exists:master_countries,id'],
            'address_2' => ['nullable', 'string', 'max:255'],
            'is_hostel' => ['nullable', 'boolean'],
            'is_active' => ['nullable', 'boolean'],
            'remarks' => ['nullable', 'string'],
            'custom_field' => ['nullable', 'json'],
            'dietary_restriction' => ['nullable', Rule::in(DietaryRestriction::values())],
            'health_concern_id' => ['nullable', 'exists:master_health_concerns,id'],
            'primary_school_id' => ['nullable', 'exists:master_schools,id,level,' . SchoolLevel::PRIMARY->value],
            'admission_type' => ['nullable', Rule::in(StudentAdmissionType::values())],
            'enrollment_status' => ['required', Rule::in(EnrollmentStatus::values())],
            'have_siblings' => ['nullable', 'boolean'],
            'is_foreigner' => ['nullable', 'boolean'],
            'conduct' => ['nullable', 'string'],
            'sibling_student_numbers' => [
                'nullable',
                'array',
                Rule::in($existing_sibling_student_numbers),
                function ($attribute, $value, $fail) {
                    if (count($value) !== collect($value)->uniqueStrict()->count()) {
                        $fail(__('validation.distinct', ['attribute' => 'sibling student numbers']));
                    }
                },
            ],
            'guardians' => ['required', 'array', 'min:1'],
            'guardians.*.name' => ['required', 'array', new RequiredLocaleValidation()],
            'guardians.*.name.*' => [new InternationalizationValidation(), 'string', 'max:100'],
            'guardians.*.email' => ['nullable', 'email', 'max:100', 'distinct'],
            'guardians.*.phone_number' => ['required_unless:guardians.*.live_status,' . LiveStatus::PASSED_AWAY->value, 'nullable', 'phone', 'string', 'max:20', 'distinct'],
            'guardians.*.nric' => ['required_without:guardians.*.passport_number', 'nullable', 'distinct', 'max:12'],
            'guardians.*.passport_number' => ['required_without:guardians.*.nric', 'nullable', 'distinct', 'max:50'],
            'guardians.*.nationality_id' => ['nullable', Rule::in($existing_nationality_ids)],
            'guardians.*.race_id' => ['nullable', Rule::in($existing_race_ids)],
            'guardians.*.religion_id' => ['nullable', Rule::in($existing_religion_ids)],
            'guardians.*.education_id' => ['nullable', Rule::in($existing_education_ids)],
            'guardians.*.married_status' => ['nullable', Rule::in(MarriedStatus::values())],
            'guardians.*.live_status' => ['nullable', Rule::in(LiveStatus::values())],
            'guardians.*.occupation' => ['nullable', 'string', 'max:100'],
            'guardians.*.occupation_description' => ['nullable', 'string', 'max:200'],
            'guardians.*.remarks' => ['nullable', 'string'],
            'guardians.*.is_primary' => ['nullable', 'boolean'],
            'guardians.*.guardian_type' => ['nullable', Rule::in(GuardianType::values())],
            'enrollment_exams' => ['nullable', 'array'],
            'enrollment_exams.*.exam_id' => ['required', Rule::in($valid_exam_ids)],
            // 'enrollment_exams.*.exam_slip_number' => ['required', 'string'], // will not be updated
            'enrollment_exams.*.total_average' => ['required', 'numeric'],
            'enrollment_exams.*.marks' => ['required', 'array'],
            'enrollment_exams.*.marks.*.exam_mark_id' => ['required', Rule::in($valid_exam_marks_ids)],
            'enrollment_exams.*.marks.*.mark' => ['required', 'numeric'],
        ];
    }
}
