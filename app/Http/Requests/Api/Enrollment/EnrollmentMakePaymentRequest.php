<?php

namespace App\Http\Requests\Api\Enrollment;

use Illuminate\Foundation\Http\FormRequest;

class EnrollmentMakePaymentRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     * @throws \Exception
     */
    public function rules(): array
    {
        return [
            'return_url' => ['nullable', 'url:http,https'],
            'is_manual_payment_requested' => ['nullable', 'boolean'],
        ];
    }
}
