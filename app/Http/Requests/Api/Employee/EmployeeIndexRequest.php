<?php

namespace App\Http\Requests\Api\Employee;

use App\Enums\EmployeeStatus;
use App\Enums\Gender;
use App\Http\Requests\Api\CommonApiValidationRequest;
use App\Models\HostelBlock;
use App\Models\HostelRoom;
use App\Models\HostelRoomBed;
use Illuminate\Validation\Rule;

class EmployeeIndexRequest extends CommonApiValidationRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return array_merge(parent::rules(), [
            'name' => ['nullable'],
            'email' => ['nullable', 'string'],
            'phone_number' => ['nullable', 'string'],
            'job_title_id' => ['nullable', 'exists:master_employee_job_titles,id'],
            'employee_number' => ['nullable', 'string'],
            'badge_no' => ['nullable', 'string'],
            'nric' => ['nullable', 'string'],
            'gender' => ['nullable', 'string', Rule::in(Gender::values())],
            'status' => ['nullable', 'string', Rule::in(EmployeeStatus::values())],
            'religion_id' => ['nullable', 'exists:master_religions,id'],
            'race_id' => ['nullable', 'exists:master_races,id'],
            'state_id' => ['nullable', 'exists:master_states,id'],
            'country_id' => ['nullable', 'exists:master_countries,id'],
            'is_hostel' => ['nullable', 'boolean'],
            'has_active_bed' => ['nullable', 'boolean'],
            'is_teacher' => ['nullable', 'boolean'],
            'block_id' => ['nullable', Rule::exists(HostelBlock::class, 'id')],
            'room_id' => ['nullable', Rule::exists(HostelRoom::class, 'id')],
            'bed_id' => ['nullable', Rule::exists(HostelRoomBed::class, 'id')],
        ]);
    }
}
