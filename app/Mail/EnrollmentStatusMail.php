<?php

namespace App\Mail;

use App\Helpers\ConfigHelper;
use App\Models\Config;
use App\Models\Enrollment;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

// TODO: Make this fking thing more dynamic coz <PERSON> dun have time to do it now
class EnrollmentStatusMail extends Mailable
{
    use Queueable, SerializesModels;

    /**
     * Create a new message instance.
     */
    public function __construct(
        private Enrollment $enrollment,
    ) {
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        $reply_to = ConfigHelper::get(Config::ENROLLMENT_REPLY_TO_EMAIL) ?? [];

        if ($reply_to) {
            $reply_to = explode(',', $reply_to);
            $reply_to = array_map('trim', $reply_to);
        }

        return new Envelope(
            replyTo: $reply_to,
            subject: "滨华中学 2026年新生报名：录取通知",
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        return new Content(
            view: 'emails.pinhwa-enrollment'
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [];
    }
}
