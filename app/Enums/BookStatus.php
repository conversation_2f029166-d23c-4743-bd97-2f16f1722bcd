<?php

namespace App\Enums;

use App\Interfaces\IEnum;
use App\Traits\EnumOption;

enum BookStatus: string implements IEnum
{
    use EnumOption;

    case AVAILABLE = 'AVAILABLE';
    case BORROWED = 'BORROWED';
    case LOST = 'LOST';
    case NOT_AVAILABLE = 'NOT_AVAILABLE';

    public static function getLabel($value): string
    {
        return match ($value) {
            self::AVAILABLE => 'Available',
            self::BORROWED => 'Borrowed',
            self::LOST => 'Lost',
            self::NOT_AVAILABLE => 'Not Available',
        };
    }
}
