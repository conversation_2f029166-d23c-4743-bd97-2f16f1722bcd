<?php

namespace App\Helpers;

use App\Repositories\SchoolProfileRepository;

class ReportHelper
{
    public static function getPrintDate($format = null): string
    {
        $format ??= config('school.print_date_format');
        return now()->timezone(config('school.timezone'))->format($format);
    }

    public static function getSchoolName($locale = null): string
    {
        $locale = $locale ?: app()->getLocale();

        return resolve(SchoolProfileRepository::class)
            ->first()
            ->getTranslation('name', $locale);
    }
}
