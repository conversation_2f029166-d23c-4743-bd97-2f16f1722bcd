<?php

namespace App\Helpers;

use App\Enums\OperatingSystem;
use App\Models\Employee;
use App\Models\LegalEntity;
use App\Models\PaymentMethod;
use App\Models\PaymentTerm;
use App\Models\Product;
use App\Models\Tax;

class SystemHelper
{
    public static function getSystemEmployee(): Employee
    {
        return Employee::where('employee_number', Employee::SYSTEM_ID)->firstOrFail();
    }

    public static function getNotApplicableTax(): Tax
    {
        return Tax::where('code', Tax::TAX_CODE_NOT_APPLICABLE)->firstOrFail();
    }

    public static function getEWalletTopupProduct(): Product
    {
        return Product::where('code', Product::CODE_EWALLET_TOPUP)->firstOrFail();
    }

    public static function getEnrollmentExamProduct(): Product
    {
        return Product::where('code', Product::CODE_ENROLLMENT_EXAM)->firstOrFail();
    }

    public static function getHostelSavingsAccountProduct(): Product
    {
        return Product::where('code', Product::CODE_HOSTEL_SAVINGS)->firstOrFail();
    }


    public static function getDefaultLegalEntity(): LegalEntity
    {
        return LegalEntity::where('code', LegalEntity::CODE_PINHWA)->firstOrFail();
    }

    public static function getDefaultPaymentTerm(): PaymentTerm
    {
        return PaymentTerm::where('code', PaymentTerm::CODE_DUE_NOW)->firstOrFail();
    }

    public static function getSystemPaymentMethod(): PaymentMethod
    {
        return PaymentMethod::where('code', PaymentMethod::CODE_SYSTEM)->firstOrFail();
    }

    public static function getFpxPaymentMethod(): PaymentMethod
    {
        return PaymentMethod::where('code', PaymentMethod::CODE_FPX)->firstOrFail();
    }

    public static function checkIfAppVersionRequiresUpdate(OperatingSystem $operating_system, string $app_version): bool
    {
        $min_required_version = ConfigHelper::get($operating_system->value . '_VERSION');

        if (is_null($min_required_version)) {
            return false;
        }

        if (version_compare($app_version, $min_required_version) < 0) {
            return true;
        }

        return false;
    }

    public static function authUserCanViewAllMerchants(): bool
    {
        return auth()->check() && auth()->user()->can('view-all-merchant');
    }
}
