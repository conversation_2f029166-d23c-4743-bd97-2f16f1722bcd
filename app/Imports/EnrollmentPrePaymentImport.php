<?php

namespace App\Imports;

use App\Models\EnrollmentSession;
use Carbon\Carbon;
use Maatwebsite\Excel\Concerns\SkipsEmptyRows;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStartRow;
use PhpOffice\PhpSpreadsheet\Shared\Date;

class EnrollmentPrePaymentImport implements WithMapping, WithStartRow, SkipsEmptyRows
{
    public function __construct(
        protected EnrollmentSession $enrollmentSession,
    ) {
    }

    public function map($row): array
    {
        // trim whitespaces from all cells
        $row = array_map('trim', $row);

        $current_index = 0;

        $adjusted = [];

        $adjusted['number'] = (string) mb_strtoupper($row[$current_index++]);
        $adjusted['exam_slip_number'] = (string) mb_strtoupper($row[$current_index++]);
        $adjusted['status'] = (string) mb_strtoupper($row[$current_index++]);
        $adjusted['expiry_date'] = is_numeric($row[$current_index]) ? Carbon::parse(Date::excelToDateTimeObject((int) $row[$current_index]))->toDateString() : null;
        $current_index++;
        $adjusted['register_date'] = is_numeric($row[$current_index]) ? Carbon::parse(Date::excelToDateTimeObject((int) $row[$current_index]))->toDateString() : null;
        $current_index++;
        $adjusted['student_name_zh'] = (string) mb_strtoupper($row[$current_index++]);
        $adjusted['student_name_en'] = (string) mb_strtoupper($row[$current_index++]);
        $adjusted['gender'] = (string) mb_strtoupper($row[$current_index++]);
        $adjusted['nric'] = (string) mb_strtoupper($row[$current_index++]);
        $adjusted['passport_number'] = (string) mb_strtoupper($row[$current_index++]);
        $adjusted['foreigner'] = $this->convertToBoolean($row[$current_index++]);
        $adjusted['nationality'] = (string) $row[$current_index++];
        $adjusted['religion'] = (string) $row[$current_index++];
        $adjusted['address'] = (string) mb_strtoupper($row[$current_index++]);
        $adjusted['hostel'] = $this->convertToBoolean($row[$current_index++]);
        $adjusted['hostel_reason'] = (string) $row[$current_index++];
        $adjusted['dietary_restriction'] = (string) $row[$current_index++];
        $adjusted['health_concern'] = (string) $row[$current_index++];
        $adjusted['have_siblings'] = $this->convertToBoolean($row[$current_index++]);
        $adjusted['primary_school'] = (string) $row[$current_index++];
        $adjusted['total_average'] = (string) $row[$current_index++];


        $enrollment_session = $this->enrollmentSession->loadMissing(['examSubjects']);
        // Add subject fields
        foreach ($enrollment_session->getExamSubjectsSortedBySubjectCode() as $subject) {
            $adjusted[$subject->code] = (string) $row[$current_index++];
        }

        $adjusted['conduct'] = (string) $row[$current_index++];
        $adjusted['guardian_type'] = (string) mb_strtoupper($row[$current_index++]);
        $adjusted['guardian_name'] = (string) mb_strtoupper($row[$current_index++]);
        $adjusted['guardian_phone_number'] = (string) $row[$current_index++];
        $adjusted['guardian_email'] = (string) $row[$current_index++];
        $adjusted['remarks'] = (string) $row[$current_index++];

        return $adjusted;
    }

    public function startRow(): int
    {
        return 2;
    }

    /**
     * Convert Excel value to boolean.
     */
    private function convertToBoolean($value): mixed
    {
        $truthy['1'] = true;
        $truthy['true'] = true;
        $truthy['TRUE'] = true;
        $truthy['yes'] = true;
        $truthy['on'] = true;

        $falsy['0'] = true;
        $falsy['false'] = true;
        $falsy['FALSE'] = true;
        $falsy['no'] = true;
        $falsy['off'] = true;
        $falsy[''] = true;

        $value = strtolower(trim((string) $value));

        if (isset($truthy[$value])) {
            return true;
        } elseif (isset($falsy[$value])) {
            return false;
        } else {
            return null;
        }
    }
}
