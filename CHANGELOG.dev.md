# [dev:v2.3.3.4] 1 Aug 2025

1. [SKLEARN-603](https://skribblelab-team.atlassian.net/browse/SKLEARN-603) Issue 123: Add Logic Validation For Employees Resignation
2. [SKLEARN-555](https://skribblelab-team.atlassian.net/browse/SKLEARN-555) Issue 203: Student Absent Report for Society

---

# [dev:v2.3.3.3] 31 July 2025

1. [SKLEARN-510](https://skribblelab-team.atlassian.net/browse/SKLEARN-510) Resend Email/Sms Notification When Extend Enrollment Expiry
2. [SKLEARN-597](https://skribblelab-team.atlassian.net/browse/SKLEARN-597) [API] ISSUE-206 1, 0, and ɸ, ∞ should only be displayed after the teacher has taken class attendance
3. [SKLEARN-583](https://skribblelab-team.atlassian.net/browse/SKLEARN-583) [API] ISSUE-95: Library Fixes

---

# [dev:v2.3.3.2] 30 July 2025

1. [SKLEARN-460](https://skribblelab-team.atlassian.net/browse/SKLEARN-460) academy report student exemption report
2. [SKLEARN-501](https://skribblelab-team.atlassian.net/browse/SKLEARN-501) [Student Affairs][Report] Student Comprehensive Quality Assessment

---

# [dev:v2.3.3.1] 29 July 2025

1. [SKLEARN-593](https://skribblelab-team.atlassian.net/browse/SKLEARN-593) Only display available teacher on substitute teacher request page

---

# [dev:v2.3.3.0] 28 July 2025

1. [SKLEARN-429](https://skribblelab-team.atlassian.net/browse/SKLEARN-429) [Academy Report] Position Ranking by Grade Report

---

# [dev:v2.3.2.8] 28 July 2025

1. [SKLEARN-582](https://skribblelab-team.atlassian.net/browse/SKLEARN-582) [API] Implement Monthly Limit for AI Question Submissions with Quota Response for Mobile App

---

# [dev:v2.3.2.7] 28 July 2025

1. Fixed isbn parse time error
    1. Just have to test ISBN retrieve, make sure still working as usual
2. Added eager loading checking on classSubjects to prevent n+1
    1. Glance thru pages that has class/subject to see if any unexpected issues
3. Fix : Attendance Mark Deduction Report Exclude leave application with no mark deduction
4. Added support account

TODO:

```bash
php artisan db:seed --class=PermissionSeeder
php artisan db:seed --class=SupportAccountSeeder
```

---

# [dev:v2.3.2.6] 25 July 2025

1. [SKLEARN-559](https://skribblelab-team.atlassian.net/browse/SKLEARN-559) Attendance period override enhancements

---

# [dev:v2.3.2.5] 23 July 2025

1. [SKLEARN-504](https://skribblelab-team.atlassian.net/browse/SKLEARN-504) Teacher Attendance Report - Show Teacher, Class, and Subject with Green Indicator Before Attendance
2. Fix employee reinstate create user account issue

---

# [dev:v2.3.2.4] 23 July 2025

1. [SKLEARN-546](https://skribblelab-team.atlassian.net/browse/SKLEARN-546) Homeroom teachers can only view student details for their assigned semester class.

---

# [dev:v2.3.2.3] 23 July 2025

1. [SKLEARN-480](https://skribblelab-team.atlassian.net/browse/SKLEARN-480) Enhancement for Class Attendance Report for Society

---

# [dev:v2.3.2.2] 22 July 2025

1. [SKLEARN-505](https://skribblelab-team.atlassian.net/browse/SKLEARN-505) Issue 69 - Finance report

---

# [dev:v2.3.2.1] 21 July 2025

1. [SKLEARN-193](https://skribblelab-team.atlassian.net/browse/SKLEARN-193) [Academy Report] Best Grade by Subject
2. [SKLEARN-428](https://skribblelab-team.atlassian.net/browse/SKLEARN-428) [Academy Report] Best Grade by Grade Report

---

# [dev:v2.3.2.0] 21 July 2025

1. [SKLEARN-426](https://skribblelab-team.atlassian.net/browse/SKLEARN-426) [Academy Report] All Passed Report
2. [SKLEARN-427](https://skribblelab-team.atlassian.net/browse/SKLEARN-427) [Academy Report] Best Grade by Class Report

---

# [dev:v2.3.1.9] 21 July 2025

1. [SKLEARN-445](https://skribblelab-team.atlassian.net/browse/SKLEARN-445) Enrollment Generate Student Number
2. [SKLEARN-212](https://skribblelab-team.atlassian.net/browse/SKLEARN-212) [Student Affairs Reports] Student Performance Reports

Pending FE Implementation for item 1

---

# [dev:v2.3.1.8] 17 July 2025

1. [SKLEARN-450](https://skribblelab-team.atlassian.net/browse/SKLEARN-450) [Academy Report][Exam] Merit and Exceptional Performance
2. [SKLEARN-497](https://skribblelab-team.atlassian.net/browse/SKLEARN-497) Reopen posted exam result by exam and grade

---

# [dev:v2.3.1.7] 17 July 2025

1. [SKLEARN-533](https://skribblelab-team.atlassian.net/browse/SKLEARN-533) Issue 117: Discount Page Enhancements
2. [SKLEARN-532](https://skribblelab-team.atlassian.net/browse/SKLEARN-532) Email Template for Exam Students

---

# [dev:v2.3.1.6] 15 July 2025

1. [SKLEARN-544](https://skribblelab-team.atlassian.net/browse/SKLEARN-544) Autocount Enhancements

---

# [dev:v2.3.1.5] 14 July 2025

1. [SKLEARN-509](https://skribblelab-team.atlassian.net/browse/SKLEARN-509) Redeployed with feedback: Enrollment Summary Page Fixes

---

# [dev:v2.3.1.4] 11 July 2025

1. [SKLEARN-490](https://skribblelab-team.atlassian.net/browse/SKLEARN-490) Add Translatable to Title in Net Average Report
2. [SKLEARN-495](https://skribblelab-team.atlassian.net/browse/SKLEARN-495) Exam Mark Entry Enhancements
3. [SKLEARN-496](https://skribblelab-team.atlassian.net/browse/SKLEARN-496) Examination Result by Class Report Enhancements
4. [SKLEARN-538](https://skribblelab-team.atlassian.net/browse/SKLEARN-538) Issue 179: Include the Outing students in the Hostel Report

Note: Yinwen need to update 2 to show the employee name

---

# [dev:v2.3.1.3] 10 July 2025

1. [SKLEARN-507](https://skribblelab-team.atlassian.net/browse/SKLEARN-507) Issue 147 - Add column for scholarship / discounted items

---

# [dev:v2.3.1.2] 10 July 2025

1. [SKLEARN-509](https://skribblelab-team.atlassian.net/browse/SKLEARN-509) Enrollment Summary Page Fixes
2. [SKLEARN-483](https://skribblelab-team.atlassian.net/browse/SKLEARN-483) First Excel Import Enhancement
3. [SKLEARN-484](https://skribblelab-team.atlassian.net/browse/SKLEARN-484) Second Excel Import

---

# [dev:v2.3.1.1] 9 July 2025

1. Fix billing document duplicated reference number due to race condition

---

# [dev:v2.3.1.0] 9 July 2025

Redeployed for sorting class name -> student name

1. [SKLEARN-536](https://skribblelab-team.atlassian.net/browse/SKLEARN-536) Issue 160: Add new column to absent report
2. [SKLEARN-537](https://skribblelab-team.atlassian.net/browse/SKLEARN-537) Issue 172: Add more student details to the Student Attendance Report
3. [SKLEARN-464](https://skribblelab-team.atlassian.net/browse/SKLEARN-464) Issue 185 Student daily arrival report enhancements

---

# [dev:v2.3.0.12] 8 July 2025

1. [SKLEARN-536](https://skribblelab-team.atlassian.net/browse/SKLEARN-536) Issue 160: Add new column to absent report
2. [SKLEARN-537](https://skribblelab-team.atlassian.net/browse/SKLEARN-537) Issue 172: Add more student details to the Student Attendance Report

---

# [dev:v2.3.0.11] 8 July 2025

1. [SKLEARN-464](https://skribblelab-team.atlassian.net/browse/SKLEARN-464) Issue 185 Student daily arrival report enhancements
2. [SKLEARN-489](https://skribblelab-team.atlassian.net/browse/SKLEARN-489) Issue 199 Added permissions bookstore-view and canteen-view for FE to hide/show side menu
3. [SKLEARN-493](https://skribblelab-team.atlassian.net/browse/SKLEARN-493) Autocount Report Enhancement
4. Redeploy: [SKLEARN-452](https://skribblelab-team.atlassian.net/browse/SKLEARN-452) Enrollment Autocount Report

---

# [dev:v2.3.0.10] 4 July 2025

1. [SKLEARN-473](https://skribblelab-team.atlassian.net/browse/SKLEARN-473) Newly Registered Student Page: Enrollment Enhancement
    - Added common_search for enrollment admin index route

---

# [dev:v2.3.0.9] 3 July 2025

1. [SKLEARN-491](https://skribblelab-team.atlassian.net/browse/SKLEARN-491) Daily Collection Reports To Product Based columns

---

# [dev:v2.3.0.8] 2 July 2025

1. [SKLEARN-515](https://skribblelab-team.atlassian.net/browse/SKLEARN-515) Implement Configurable Reply-To Address for Enrolment Emails
2. [SKLEARN-452](https://skribblelab-team.atlassian.net/browse/SKLEARN-452) Enrollment Autocount Report

---

# [dev:v2.3.0.7] 1 July 2025

1. Issue 200: update period 1 class attendance status by school attendance check in status
2. (Fix redeploy) Issue 187: fix reward punishment reports showing wrong room / bed number

---

# [dev:v2.3.0.6] 1 July 2025

1. [SKLEARN-498](https://skribblelab-team.atlassian.net/browse/SKLEARN-498) Issue 166: Outstanding Balance Report
2. Issue 187: fix reward punishment reports showing wrong room / bed number
3. Issue 188: cocu student statistic report exclude students that already left the society class

---

# [dev:v2.3.0.5] 30 June 2025

1. [SKLEARN-502](https://skribblelab-team.atlassian.net/browse/SKLEARN-502) Newly Registered Student Report Enhancements

---

# [dev:v2.3.0.4] 24 June 2025

1. [Issue 192] Fix library report not showing up past semester student correctly.

---

# [dev:v2.3.0.3] 23 June 2025

1. Update enrollment student report to show only paid students

---

# [dev:v2.3.0.2] 20 June 2025

1. Added AI RND chat feature

---

# [dev:v2.3.0.1] 19 June 2025

1. Enrollment guardian nric/passport either one required fix
2. Enrollment users now will return enrollments data

---

# [dev:v2.3.0.0] 19 June 2025

1. [Upgrade] Upgrade to laravel 11.0.0

---

# [dev:v2.2.6.13] 18 June 2025

1. SKLEARN-474 Added translations to guest info

---

# [dev:v2.2.6.12] 18 June 2025

1. Update student grading framework job to just load id instead of whole config

---

# [dev:v2.2.6.11] 18 June 2025

1. Exam report now supports diff language, including
    1. Net average report
    2. Subject analysis report
    3. Subject average mark report
    4. Subject passing rate report
2. Examination report by class now sorts subject by sequence (like report card)

@Yin Wen for 1 there’s data structure changes, rmb to update

---

# [dev:v2.2.6.10] 18 June 2025

1. Added `admission_grade_id` to enrollment session crud api
    1. FE → Need to add `admission_grade_id` during create/update, and display admission grade on index
    2. BE → Will now auto populate `admission_grade_id` and `admission_type` when import

---

# [dev:v2.2.6.9] 16 June 2025

1. Added Net Average Report
2. Added subject analysis report
3. Added subject average mark report
4. Added subject Passing rate report

---

# [dev:v2.2.6.8] 16 June 2025

1. Assign student to class fix

---

# [dev:v2.2.6.7] 16 June 2025

1. Enhanced examination result by class report to check is_exempted (is_exempted now shows empty)

---

# [dev:v2.2.6.6] 13 June 2025

1. [Issue 180] Update Semester Class (Primary) - By Student in Class report title and remove leave school legend
2. [Issue 191] Sort non primary report by semester class with primary class ascending

@Wilson to test n get back

---

# [dev:v2.2.6.5] 13 June 2025

1. Examination result by student added principal and homeroom teacher name
    1. @Lynnette to check n get back
2. Examination result by exam report fixed column when generate for multiple grades
    1. @yinwen to enable back multiple grade_ids filter
    2. @Lynnette to test and get back after Yinwen update

---

# [dev:v2.2.6.4] 13 June 2025

1. Return `student_id` and `result_source_subject_id` on mark entry even if error

---

# [dev:v2.2.6.3] 13 June 2025

1. Added `sibling_student_numbers` for enrollment admin/non admin update
2. nric/passport is required for parent and cannot be duplicated
3. Added `siblings` key for enrollment show api
4. Added `pending` status in enrollment session summary page

---

# [dev:v2.2.6.2] 12 June 2025

1. Added `summary` key in enrollment show api

---

# [dev:v2.2.6.1] 10 June 2025

1. Deployed exam result/average report (By Class) `POSTMAN: Examination Result/Average Report (By Class)`

---

# [dev:v2.2.6.0] 10 June 2025

1. Report Card Formatting Changes (should be the final one)
2. Add display_in_report_card to LeaveApplicationType CRUD
3. removed exam posting dependency on is_current_semester column from Semester Setting
4. Fixed mergeReportCardJob error handling
5. Fixed getEligibleMasterGradingFramework API - now only returns framework related to specific grade

---

# [dev:v2.2.5.13] 9 June 2025

1. Enrollment update status `POSTMAN: Update Status (ADMIN)`

---

# [dev:v2.2.5.12] 9 June 2025Update Status (ADMIN)

1. Added duplicate semester setting `POSTMAN: SemesterSetting → Copy`

---

# [dev:v2.2.5.11] 7 June 2025

1. All enrollment enhancement update

---

# [dev:v2.2.5.10] 6 June 2025

1. Added delete enrollment API `POSTMAN: Enrollment -> DELETE(ADMIN)`

---

# [dev:v2.2.5.9] 6 June 2025

1. Enrollment admin update now able to update exam marks

---

# [dev:v2.2.5.8] 6 June 2025

1. [JIRA 183] Added Exam Result 2nd report card format `POSTMAN: Examination Result By Student`

---

# [dev:v2.2.5.7] 6 June 2025

1. [JIRA 181] Added Examination Result By Exam

---

# [dev:v2.2.5.6] 6 June 2025

1. Added email login `POSTMAN: Enrollment User (GUARDIAN FACING)`

---

# [dev:v2.2.5.5] 5 June 2025

1. Report card formatting update
2. Generate merged report card → By grade or class

---

# [dev:v2.2.5.4] 5 June 2025

1. [Issue 182] allow teacher to take class attendance 5 mins earlier
2. [Issue 173] Fee payment bypass month fix
3. [Issue 172] Add student chinese and english name in attendance report

---

# [dev:v2.2.5.3] 4 June 2025

1. Updated autocount report format (export enrollment details from current autocount report oso)
2. Added queue to send email to enrollment guardian

---

# [dev:v2.2.5.2] 4 June 2025

1. Added enrollment retry payment API
2. Added Daily collection report for enrollment

---

# [dev:v2.2.5.1] 4 June 2025

1. Added enrollment student register report
2. Added `register_date` and `expiry_date` to the excel

---

# [dev:v2.2.5.0] 4 June 2025

1. Added coco/english report `POSTMAN:Report By Non Primary Student In Class`

---

# [dev:v2.2.4.5] 3 June 2025

1. Billing document by daily collection export timeout fix

---

# [dev:v2.2.4.4] 30 May 2025

1. Added `admission_year` to enrollment_sessions crud

---

# [dev:v2.2.4.3] 29 May 2025

1. Added teacher name/principal name to student report card

---

# [dev:v2.2.4.2] 29 May 2025

1. Enrollment guardian facing
    1. Added index/show/update API to update enrollment
    2. Added make payment for enrollment API

---

# [dev:v2.2.4.1] 28 May 2025

1. Updated mark subtracted formula

---

# [dev:v2.2.4.0] 27 May 2025

1. Added enrollment fee setting
2. Updated import to create and link enrollment user

---

# [dev:v2.2.3.6] 26 May 2025

1. Added student conduct report
2. Added exam semester settings

---

# [dev:v2.2.3.5] 26 May 2025

1. Added enrollment login `POSTMAN: Enrollment (Guardian Facing)`
2. Added enrollment import excel to BO `POSTMAN: Bulk Insert Imported Excel (FULL)`

```bash
php artisan patch:reward-punishment-records-date-of-sign --actual
```

---

# [dev:v2.2.3.4] 23 May 2025

1. Added import validation `POSTMAN:Import Enrollment By Excel (FULL)`

---

# [dev:v2.2.3.3] 21 May 2025

1. Added Enrollment Session CRUD
2. [Issue 14] Added 2 reports
    1. Report By Boarders Saving Account Records
    2. Report By Boarders Saving Account

---

# [dev:v2.2.3.2] 20 May 2025

1. Added `academic_year` to student grading framework

FE TODOs

1. Added `academic_year` field in apply student grading framework page
    1. Need to filter semester in selected academic_year only
    2. with 1a means only display class in selected year

---

# [dev:v2.2.3.1] 19 May 2025

1. Deploy all exam changes in JIRA-436

---

# [dev:v2.2.3.0] 15 May 2025

1. Exam result posting pre check added `is_exempted` fields

```bash
# Rerun migration for exam_posting_pre_checks
delete from migrations where migration = '2025_04_07_142125_create_materialized_view_exam_posting_pre_checks';
php artisan migrate
```

---

# [dev:v2.2.2.3] 14 May 2025

1. Added permission for academic admin to view all exam result data entry
2. Added seat number to mark entry page
3. Added primary class code to mark entry page

---

# [dev:v2.2.2.2] 13 May 2025

1. Exam module enhancements
2. Promotion mark API deploy

---

# [dev:v2.2.2.1] 13 May 2025

1. When change class → update column `is_latest_class_in_semester` → only have to test n make sure change student class/remove student class functions the same as before
2. Repository fix → Need to run thru all pages, make sure no error

---

# [dev:v2.2.2.0] 9 May 2025

1. Added `has_mark_deduction` flag for timeslots/period_attendances table, this will
    1. Determine if the period requires mark deduction (Like second 班务）
    2. If period doesn’t requires mark deduction, leave application history will not display ✅
2. When updating timetable timeslots, need to pass in 2 new param
    1. `timeslots[]['default_init_status']` → Required, Accepts value `PRESENT, ABSENT, LATE`
    2. `timeslots[]['has_mark_defuction']` → Required, accepts boolean

```sql
php
artisan migrate
// Rerun migration for student_timetable

php artisan patch:period-attendance --exclude-patch-period-attendance-status --actual
php artisan patch:period-attendance --exclude-patch-has-mark-deduction --actual
php artisan patch:period-attendance --exclude-remove-timeslot-teacher --actual
php artisan patch:period-attendance --exclude-patch-leave-application --actual
```

---

# [dev:v2.2.1.2] 8 May 2025

1. [Issue 156] Co-curricular statistic report add Code Column 👍🏻
2. [Issue 126] Student Mark Deduction Report added `leave_application_type_ids` field

---

# [dev:v2.2.1.1] 8 May 2025

1. [Issue 149] Add max wallet topup value 👍🏻
2. [Issue 96] Added `call_no_wildcard` for book search

---

# [dev:v2.2.1.0] 8 May 2025

1. [Issue 83] Newly created reward punishment record auto set to POSTED 👍🏻
2. [Issue 159] Canteen product page too long to load 👍🏻

---

# [dev:v2.2.0.7] 7 May 2025

1. [Issue 24] Teacher attendance taking report permission fix

---

# [dev:v2.2.0.6] 7 May 2025

1. Attendance changes

Things to test:

1. Leave application can now directly delete from `APPROVED` or `REJECTED` status

1. When creating substitute teacher, will throw error if requestor and substitute teacher is the same person
2. First 班务 is now NOT Editable under any scenario
3. If student late to school, First 班务 will now be set to `LATE`
4. If student left early,
    1. If second 班务 have no leave application, system will auto create a leave (早退/无签离）
    2. If second 班务 have leave application, system will not auto create leave

---

# [dev:v2.2.0.4] 2 May 2025

1. [Issue 135] Prevent attendance card tapping if user card has been inactivated
2. [Issue 136] Substitute management index change to show period label instead

FE TODO

1. Need to change to use `period_label` instead of `period`

---

# [dev:v2.2.0.3] 2 May 2025

1. [Issue 24] Updated teacher attendance report → RENAMED this to Class Attendance Taking Status Report

---

# [dev:v2.2.0.2] 2 May 2025

1. [Issue 85] Update formatting, update text translation
2. [Issue 50] Now support filtering by different class type (Filter by `class_type`, check postman) for Student Attendance and Student Absent report
3. [Issue 104] Discount page pagination fix
4. [Issue 134] Rearrange column in Daily Collection Report
5. [Issue 116] Student Class search displaying old class

---

# [dev:v2.2.0.1] 30 Apr 2025

1. [Issue 86] Permission dependency fix
2. Attendance changes
    1. Enable to directly delete leave application from APPROVED/REJECTED status
    2. Remove period label from leave application (Only uses period now)
    3. 8pm cronjob will create approved leave application for student that left early
3. Substitute teacher changes
    1. Validation to prevent create substitute teacher as same person as requestor
    2. If teacher are being substituted, original teacher cannot see the period during class attendance taking

---

# [dev:v2.1.4] 29 Apr 2025

1. [Issue 24] Teacher attendance taking report
2. [Issue 50] Student Absent Report added `class_type` filter

---

# [dev:v2.1.3] 29 Apr 2025

1. [Issue 103,114] Return `updated_by_employee` and `photo` to class attendance taking

---

# [dev:v2.1.2] 29 Apr 2025

1. [Issue 106] Remove compulsory for signing guardian

---

# [dev:v2.1.1] 29 Apr 2025

1. [Issue 105] Updated bank charges formula

---

# [dev:v2.1.0] 29 Apr 2025

1. [Issue 96] Added `book_no_wildcard` for book index API
2. [Issue 90/91] Library page show student class
3. [Issue 36] Fix description disappear when discount is confirmed

---

# [dev:v2.0.12] 25 Apr 2025

1. [Issue 34] Allow updating book status from Lost to Available

FE TODO:

1. Implement the API `Recover Lost Book` on book index page, if book status = `LOST` , show a button `Recover Lost Book` that call the above API

---

# [dev:v2.0.11] 25 Apr 2025

1. [Issue 80] Return scholarship details in Discount index/show API

FE

@yinwen need to add `source` into `includes`

---

# [dev:v2.0.10] 25 Apr 2025

1. [Issue 40] Validate don’t allow void billing doc if got active payment gateway log

---

# [dev:v2.0.9] 25 Apr 2025

1. [Issue 71] Parent to pay all fees in the same month, admin doesn’t have this limitation

---

# [dev:v2.0.8] 25 Apr 2025

1. [Issue 36] Added `description` column in discount setting

FE: Need to add in Description column, → Check Create Discount Bulk (BO) in postman on how to pass in the data

---

# [dev:v2.0.7] 24 Apr 2025

1. School attendance now post whenever student tap card

   → Applies to tap card online api and bulk submit api

---

# [dev:v2.0.6] 23 Apr 2025

1. Added Student Absent Report `POSTMAN: Reports → Attendance → Student Absent`, permission `student-absent-report`

---

# [dev:v2.0.5] 23 Apr 2025

1. Billing document index add `payment_reference_no` filter
2. Added Mark deduction report `POSTMAN: Reports → Attendance → Report by Student Attendance Mark Deduction` , permission `attendance-by-student-attendance-mark-deduction-report`

---

# [dev:v2.0.4] 22 Apr 2025

1. Issue on calculating invoice with multiple discount
2. Patched 早退/无签离 and 放学后无拍卡免扣 to second 班务
3. Updated init to generate second 班务 as PRESENT
4. Updated student attendance posting to auto create 早退/无签离 leave application if student forgot to tap card when leave school

---

# [dev:v2.0.3] 22 Apr 2025

1. [Issue 40] Prevent to void paid invoice
2. [Issue 16] Add total into daily collection report
3. [Issue 42] Contractor Daily Attendance Report

FE Things to deploy

1. [Issue 15] Add type `DEPOSIT` and `TRANSFER` on wallet transaction report
2. [Issue 20] Student search engine change to `student_number_wildcard` for search
3. [Issue 29] When student borrow book, due date should populate from book info, not hardcoded to 7 days later
4. [Issue 30] After borrow book, reset form

---

# [dev:v2.0.2] 21 Apr 2025

1. If student attendance = absent, is_editable = false
2. Eager load `/semester-classes` api
3. Updated period label name

FE for 2 → Need to include `latestPrimaryClassBySemesterSettings` and `defaultGradingFramework`

---

# [dev:v2.0.1] 21 Apr 2025

1. Class attendance data school date time pass as ISO string
2. Added patch for computer study teacher

---

# [dev:1.2.17] 20 Apr 2025

1. Added student analysis report `POSTMAN: Reports → Academy → Student Analysis By Semester`

---

# [dev:1.2.16] 20 Apr 2025

1. `billing-documents/:billing-document-id` route now return has pending payment true/false flag
2. Added Student attendance report `POSTMAN: Reports → Attendance → Student Attendance`

---

# [dev:1.2.15] 20 Apr 2025

1. Added Class Attendance Report `POSTMAN: Reports → Attendance → Class Attendance Report`

---

# [dev:1.2.14] 20 Apr 2025

1. Enhance student attendance posting
2. Added Attendance report by attendance summary `POSTMAN: Reports → Attendance → Report By Attendance Summary`

---

# [dev:1.2.13] 19 Apr 2025

1. [Ryan:Issue 42] Timetable → Period → Sort by period group not working
    1. Added `period_group_name` for sorting
2. [Ryan/LX:Issue 41/28] Substitute management sorting doesn’t work
    1. Added `requestor_name`, `substitute_teacher_name`, `subject_name`, `class_name` for sorting
3. [Farah:Issue 52] Period label changes

# [dev:1.2.12] 19 Apr 2025

1. [Issue 45] School Rate of Borrow bug fix
2. [Issue 11] Comprehensive Assessment Question sorting
    1. Need to sort by `question` not name
    2. Added new field `comprehensive_assessment_category_name` to be used for sorting
3. [Issue 27] Class subject sorting not working
    1. Added `class_name` for sorting
    2. Added `subject_name` for sorting
4. [Issue 50] Add books timeout

---

# [dev:1.2.11] 18 Apr 2025

1. Added Permissions:
    1. `counselling-case-record-view-all-teacher` → If only have `counselling-case-record-view` role, only will return own records
        1. Also cannot perform show,update,delete action for record not created by them
    2. `student-tab-fee-records-view` → to be used in student profile tab
    3. `student-update-non-direct-guardian-only` → For hostel user to update guardian only
    4. `hostel-student-view` → FE should use this to define whether can see Hostel → student management
    5. `hostel-student-update` → To define whether can update non direct guardians (NOTE: hostel update only update non direct guardians, direct guardians and student data will not be affected)

---

# [dev:1.2.10] 18 Apr 2025

1. [Issue 35] Added validation if passed deadline, display error msg instead
2. [Issue 15] Fix unable to select current month to perform billing

---

# [dev:1.2.9] 18 Apr 2025

1. [Issue 36] Fix download trainer report not working
2. [Issue 16] Change `attendance_recordable_id` to array for attendance index
3. [Issue 33] Hide student that has left school from period attendance
4. [FE CHANGES REQUIRED][Issue 39] Added `repost_school_attendance` (boolean) to attendance input create api

---

# [dev:v1.2.8] 18 Apr 2025

1. Return active hostel bed for SimpleEmployeeResource
2. Fix society semester class went missing when sort by class

---

# [dev:v1.2.7] 17 Apr 2025

1. Added `has_pending_payments` flag to `BillingDocumentResource`
2. Fixed english class timeslot migrations
3. Fixed returning all class attendance for teacher selection
4. Class subject show API return student primary class
5. Semester class show API return student primary class
6. Added route for mobile app to view billing document → `POSTMAN: Billing Document -> Show FE`
7. Refresh view table using job instead of sync

---

# [dev:v1.2.6] 17 Apr 2025

1. Fix calendar priority issue
2. Fix substitute teacher only display timeslot with active timetable
3. Fix seat setting issue
4. (BREAKING Changes) Added `allowance` (required) to substitute record bulk create

---

# [v1.2.5-dev] 16 Apr 2025

1. Attendance module related fix as of 15 Apr
2. Daily Attendance Report
3. Autocount Report
    1. document_date_from, document_date_to parameter change to payment_date_from, payment_date_to
    2. remove filter for payment_status, will always default to PAID in the backend since user wants to filter using payment date.

---

# [v1.2.3-dev] 14 Apr 2025

1. Added migrations
    1. English timetable
    2. Substitute teacher
    3. Coco timetable
2. Fix exempted student returning encoded json issue

---

# [v1.2.2-dev] 8 Apr 2025

1. Leave application migration

---

# [v1.2.1-dev] 7 Apr 2025

1. Existing timetable API return period labels
2. Employee timetable - Return period time for employee timetable
3. Employee timetable - Return employee detail
4. Class subject → Return period info

## BREAKING CHANGES - NEED TO UPDATE ASAP

1. Add `number_of_period_per_week` field when calling `PUT /class-subjects/{id}`

---

# [v1.1.78-dev] 7 Apr 2025

1. Fix migration for timetable:
    1. Cocurriculum Requirement not showing correctly issue
    2. Junior and Senior have different lunch break and 7th period issue
    3. 班务 not linked to teacher issue
2. Fix migration for employee not showing in hostel bed
3. Fix migration for cocurriculum contractor
4. Added migration for cocurriculum instructor
5. Added employee timetable view table and API

Migration already re-ran on QAS

FE to fix:

1. `/hostels/employee-management/hostel-employees` when calling `/employees` need to pass in `response = FULL` to get full data instead of simple

---

# [v1.1.77-dev] 4 Apr 2025

1. Added daily collection report, `POSTMAN: Billing Document → By Daily Collection`
2. Added new permission for conduct to view all teacher `conduct-setting-view-all-teacher`
3. Added API to get available teacher for conduct mark entry `POSTMAN: ConductSetting → Get Conduct Teacher List`
4. Added API to get available classes for selected teacher `POSTMAN: ConductSetting → Get Semester Classes By Teacher`

---

# [v1.1.76-dev] 3 Apr 2025

1. Added leadership position migration (MIGRATED to DEV)
2. Allow `/leadership-position-records` to return all records using `per_page = -1`

FE Bug to be fixed

1. `/student-affairs/class-leadership-list/class-leaderships` after filter, api returns data, but BO shows all empty

---

# [v1.1.75-dev] 3 Apr 2025

1. `guardian_id` is mandatory for departure records
2. Sort all report by ascending bed number
3. Added sorting for hostel disciplinary records
4. Added hostel PIC, check `POSTMAN: Hostel PIC`
5. Added new field for student index api `student_number_wildcard`, use for student advance search

Note for FE: When create disciplinary record, load from `{{url}}/admin/hostels/person-in-charge` instead of `{{url}}/employees`

---

# [v1.1.74-dev] 3 Apr 2025

1. Added finance autocount report `POSTMAN: Post to autocount`

---

# [v1.1.73-dev] 2 Apr 2025

1. During 3am cronjob, set all student’s period_attendances to ABSENT, if period_attendances already exist, do not overwrite
2. When calling class-attendance-taking/get-class-attendance-by-timeslot,
    1. If period_attendances.updated_by_employee_id == null, use default value
    2. If period_attendances.updated_by_employee_id ≠ null, use period_attendances value
    3. Add in a flag is_default (bool), FE will put a diff color if is_default = true
3. Add terminal_id (nullable) into attendance_input table
4. Add terminal_id (nullable) into attendance_input_error_logs table
5. Fix bug which only apply advance payment can be applied multiple times when confirmed multiple times
6. Fix bug whereby discount are not being applied to only unpaid items

---

# [v1.1.71-dev] 25 Mar 2025

1. If selected unpaid items sum up to RM0.01-RM0.99, will prevent from being paid, will throw error instead
2. If select unpaid items sum up to RM0, no need to make payment, status should be directly updated to CONFIRMED + PAID.

---

# [v1.1.70-dev] 25 Mar 2025

1. Deployed exam module custom functions

---

# [v1.1.69-dev] 20 Mar 2025

1. Add book `loan_statuses` filter for `POSTMAN: Report By Book Loan`
2. Add book loan status at book loan report
3. Add grade sequence to canteen report
4. Fix substitute record bug

---

# [v1.1.68-dev] 17 Mar 2025

1. Added permission `leave-application-approve`
2. Remove leave application manual change status
3. Fixed bug where student without timeslot return all timeslot

---

# [v1.1.67-dev] 17 Mar 2025

1. Renamed `attendance-submit` → `attendance-offline-bulk-submit`
2. Added `attendance-online-submit` for operator app online attendance taking

Misc

1. Added interval at config to configure attendance taking interval

---

# [v1.1.66-dev] 14 Mar 2025

1. Added permission for student, employee, contractor attendance input

---

# [v1.1.65-dev] 11 Mar 2025

1. Fix leave application photo upload
2. Optimize announcement sending codes

---

# [v1.1.64-dev] 10 Mar 2025

1. Added `class_type` to class_subjects index API
2. Added `sub_type` to billing-documents API

---

# [v1.1.63-dev] 10 Mar 2025

1. Added void billing document API for hostel saving account voiding `POSTMAN: Void Billing Document`
2. Fix Canteen Order completed not sending out notification issue
3. Added `average_point_deduction` and `conduct_point_deduction` to leave application type

## To Test

1. Canteen order completed should receive a notification

---

# [v1.1.62-dev] 07 Mar 2025

1. Changes for `/students` index API:
    1. Added `admission_type` filter which support `NEW/TRANSFERRED`
    2. Added `grade_ids` filter which support multiple grade_ids for filtering
    3. Added `semester_class_ids` filter which support multiple semester_class_ids for filtering
    4. Added `class_stream` filter which support `NOT_APPLICABLE/SCIENCE/ART`

   > NOTE: Changes above need to be updated to student advance search

>

2. Changes for `class` CRUD
    1. Added new required field `stream` into classes table `NOT_APPLICABLE/SCIENCE/ART`

   > NOTE: This is a breaking changes so it will break current create/update, FE need to implement this first

>

---

# [v1.1.61-dev] 07 Mar 2025

1. Added outstanding fee payment report

---

# [v1.1.60-dev] 07 Mar 2025

1. Change get attendance period’s student_id to userable_id and userable_type

---

# [v1.1.59-dev] 07 Mar 2025

1. Change discount confirmation to confirm multiple discount at once
2. Added discount to print unpaid items API

---

# [v1.1.58-dev] 05 Mar 2025

1. Attendance related fixes

---

> PRD v1.1.12
>

# [v1.1.57-dev] 04 Mar 2025

1. Added wallet adjustment feature

---

# [v1.1.56-dev] 03 Mar 2025

1. Deployed attendance module

---

# [v1.1.55-dev] 28 Feb 2025

1. Added Grading framework formula

---

# [v1.1.54-dev] 28 Feb 2025

1. Grading framework error group together

---

# [v1.1.53-dev] 28 Feb 2025

1. Add terminal names at report
2. Add wallet withdraw API
3. Add wallet refund API
4. Improve `/admin/wallets/transactions` to accept `user_name`, `user_email` , `user_phone_number` for filtering
5. Added wallet transaction report: `POSTMAN: By All Wallet Transaction`
6. Add adhoc notification to be sent out when ecommerce order completes

## FE to implement

1. Wallet withdraw function
2. Wallet refund function
3. Create an index page that shows all the wallet transaction
4. Add new wallet transaction report

## To test

1. Test when canteen order is completed, receives a notification → when press on notification, jump to order page

---

# [v1.1.52-dev] 27 Feb 2025

1. Remove logic for student to transfer money to primary guardian once they left school
2. Added employee number patching script
3. Added wallet refund function

---

> PRD v1.1.11
>

# [v1.1.51-dev] 27 Feb 2025

1. Enhance announcement image to store in s3 and chunk records to be processed

---

# [v1.1.50-dev] 25 Feb 2025

1. Added route for POS to retrieve charge status

---

# [v1.1.49-dev] 24 Feb 2025

1. Added Grading Framework CRUD

---

# [v1.1.48-dev] 21 Feb 2025

1. Added discount preview for unpaid items

---

# [v1.1.47-dev] 21 Feb 2025

1. Permission dependency update

---

# [v1.1.46-dev] 21 Feb 2025

1. Added `guardian_name`, `guardian_phone_number`, and `guardian_email` student index api
2. Student wallet will now be returned when searching for guardian’s email/phone/name
3. Change invoice bill to number to dynamic (Previously always shows as Student No.)

---

# [v1.1.45-dev] 20 Feb 2025

1. Added `is_direct_dependant` flag to student create/update under guardian

## FE TODO:

1. Add `is_direct_dependant` checkbox under student create/update under guardian, by default checked
2. Default check `with_user_account` for guardian

---

# [v1.1.44-dev] 20 Feb 2025

1. Added Hostel Reports:
    1. Reward punishment by block
    2. Reward punishment by room
    3. Reward punishment by student

---

# [v1.1.43-dev] 20 Feb 2025

1. Added admin manual payment API for billing document

---

# [v1.1.42-dev] 19 Feb 2025

1. Added Discount CRUD
2. Added logic to apply discount on generating billing document

---

# [v1.1.41-dev] 17 Feb 2025

1. Added scholarship CRUD
2. Set unpaid item index page period from and to to nullable

---

# [v1.1.40-dev] 17 Feb 2025

1. Changed route `{{url}}/accounting/fees/unpaid-items/pay` to `{{url}}/accounting/fees/unpaid-items/create-billing-document`
2. Route `{{url}}/accounting/fees/unpaid-items/create-billing-document` now returns billing document instead of payment gateway url

---

# [v1.1.39-dev] 17 Feb 2025

1. Added unpaid item assignment index/show/delete
    1. Note: Only can be deleted when unpaid item assign status = NEW
2. Group wallet transactions by school timezone

---

# [v1.1.38-dev] 14 Feb 2025

1. Added `email` and `phone_number` for user update
2. Fix Announcements to only send message to direct dependants
3. `/employees` route now uses Simple Resource

---

# [v1.1.37-dev] 14 Feb 2025

1. Fix guardian - when updating guardian phone number, it should not create a new user

---

# [v1.1.36-dev] 14 Feb 2025

1. Added billing document APIs
    1. Admin route (BO)
        1. index admin `admin/billing-document`
        2. show billing document `admin/billing-document/{billing_document}`
        3. Change billing document status `admin/billing-document/status`
    2. FE Route
        1. index `billing-document`
        2. Void billing document `billing-document/{id}/void`
        3. Make/retry fpx payment `billing-document/{id}/make-fpx-payment`

---

# [v1.1.35-dev] 14 Feb 2025

1. Separate Payex router and callback url
2. Create credit note for cancelled invoice
3. Patch invoice number running number

---

# [v1.1.34-dev] 12 Feb 2025

- Change card import to deactive old card and add new card for students/employee/contractor that already have an active card

# [v1.1.33-dev] 12 Feb 2025

- Optimize queries with eager loading

## To be tested/potentially affected places

- User CRUD
- Semester class CRUD
- Semester setting CRUD
- Student class subject assign Update page
- User Profile CRUD

To test this efficiently, can set the perpage records → 50, and navigate few pages, see if encounter any slow queries, normally the speed should be less than 300ms, 100-200ms is the normal range

# [v1.1.32-dev] 10 Feb 2025

1. Updated ecommerce product delivery/available date assignment and display format

---

# [v1.1.31-dev] 10 Feb 2025

1. Remove part where BO API will call Payex side again to get latest transaction data, just process using received payload
2. User SimpleUserableViewResource on UserResource

## To be tested:

1. Test payment Success, Pending, Failure, all still working as per normal
2. Check payment_gateway_logs, wallet_transactions, wallets. Make sure transaction are going thru as per normal, topup happens as per how it is now

---

# [v1.1.30-dev] 9 Feb 2025

1. Add `is_direct_dependant` to guardian_student_table

---

# [v1.1.29-dev] 6 Feb 2025

1. Change WalletResource to use SimpleUserableViewResource to optimize
2. Added includes currency to wallet api to prevent n+1

# [v1.1.28-dev] 5 Feb 2025

1. Added inbound log for payment gateway callback
2. Update APIs to use SimpleEmployeeResource:
    1. Conduct setting
    2. Counselling Case Record
    3. Hostel Reward Punishment
    4. Semester Class
3. Add `getTransactionByReferenceNumber` for cronjob payex status update
4. Added trait `HandlesPagination` as a helper to determine return all/paginated
5. Increased invoice label width to 200px
6. Set guardian email to nullable
7. Set payex callback time to UTC

---

# [v1.1.27-dev] 5 Feb 2025

- Added `delivery_date_from`, `delivery_date_to` and `per_page = -1` filter for `products` route

---

# [v1.1.26-dev] 4 Feb 2025

- `conduct-settings/get-teachers-by-semester-class/{semester_class}` now only return primary teachers
- Contractor now cannot be deleted if he/she is linked to any card/class subject
- Optimize product tag and announcement crud page
  @Mori needs to merge this https://bitbucket.org/skribblelab/skribble-learn-backoffice-frontend/pull-requests/5

---

# [v1.1.25-dev] 4 Feb 2025

- Added `leave_status` to student API
- When student left school, set all active student class to active = false
- Set employee `job_title_id, address, address2` to compulsory (Need to add `*` on FE)
- Added `category_id, sub_category_id, tag_id, group_id` filter on routes below:
    - `products/merchant-type/{merchant_type}`
    - `products/by-userable`
    - `products`

---

# [v1.1.24-dev] 3 Feb 2025

- Fixed migration data for employee session (This fixes employee session cannot be deleted once first created issue)
- Added `selected_semester_class` on student index API (URGENT for FE to implement)

## TODO

- Run `php artisan v1:migrate --step=migrateEmployeeSession --actual --truncate`

---

# [v1.1.23-dev] 27 Jan 2025

- Userable access fix for routes:
    - Ecommerce checkout
    - Ecommerce view bookshop/canteen
    - Wallet deposit
    - Wallet transfer

---

# [v1.1.22-dev] 27 Jan 2025

- Payment gateway cancel fix

---

# [v1.1.21-dev] 27 Jan 2025

- Added userable in wallet_transactions table
- Places to be tested
    - Places should store in the userable id/type
        - Library book loan penalty payment
        - Ecommerce order payment
        - Inter-Wallet transfer (will save userable as the sender in both transactions)
        - POS Terminal wallet charge payment
        - Deposit money to wallet
    - Places should not store the userable id/type
        - Ecommerce order cancel (refund) - should be null for userable.

---

# [v1.1.20-dev] 24 Jan 2025

## Urgent changes required from FE

- Add in `admission_type` required for student create/update → Accepts `NEW, TRANSFERRED`

- Merchant cannot be deleted if there’s product attached to them
- Added transferred student list report
- Removed `email` from student create/update
- Auto generate `email` from student number
- BE: Updated to use service to generate student number @Wilson can help to do quick test on create student/generate number (hostel and non hostel) student and check if got issue?
- Updated guardian `nric` validation to contains only 12 digits

---

# [v1.1.19-dev] 24 Jan 2025

- Added `/logout` route
- When assigning ARN token, it will remove everyone with that ARN token
- Removed `CustomUserData` for creating ARN so that it wont cause issues

---

# [v1.1.18-dev] 23 Jan 2025

- Ecommerce product description set to nullable
- Added `merchant_id` (nullable) to terminal CRUD
- Added stricter access control to terminal
    - If user dun have `view-all-merchants` permission, they can only view/edit/delete their own terminal, else will throw error
- Added userable to library loan wallet deduction
- Leave application crud

Lucas TODO:

- Logout end point -clear user token and api

---

# [v1.1.17-dev] 23 Jan 2025

- Migration
    - Migrated employee email
    - Migrated contractor email & phone number
- Added Guest CRUD
- Added Society Student statistic report `Postman: Student Statistic By Semester`
- Lee Xin Notification changes: https://skribblegroup.slack.com/archives/C07DQ1L7YQZ/p1737557612329879

## Todo:

@Wilson to help to test out:

- Following actions will trigger a notification:
    - Library member pay penalty
    - Ecommerce place order
    - Student pay on pos terminal

---

# [v1.1.16-dev] 22 Jan 2025

- Added `return_url` for payment gateway

---

# [v1.1.15-dev] 20 Jan 2025

- Added `user_type_description` in wallet balance API
- Add wallet to contractor when create via CRUD page
- When scanning card to make POS payment, will throw invalid use error if userable is inactive
- Added `contractor` able to perform wallet related transactions

---

# [v1.1.14-dev] 20 Jan 2025

- Migration
    - Primary School ✅
    - Link Trainer to subject/class ✅
- Middleware updates
    - Exempted POS Terminal route to check for platform/version
    - Update to check min available version instead of setting force update version

---

# [v1.1.13-dev] 17 Jan 2025

- Migration
    - Club Category ✅
    - Club class ✅
    - Club ✅
    - Class subject ✅
    - Class subject student ✅
    - Club class student ✅
    - Trainer and trainer card ✅
- Fix SOCIETY class not showing student
- Added Middleware for Maintenance Mode and Force update app version
    - Error code:
        - 34001 → Maintenance Mode
        - 34002 → Force update app
- Added `MAINTENANCE_MODE, IOS_FORCE_UPDATE_VERSION, ANDROID_FORCE_UPDATE_VERSION, HUAWEI_FORCE_UPDATE_VERSION` configs. This will not be available on FE as is configured manually backend

  BEEP BEEP @channel, IMPORTANT UPDATES, I’ve added few headers that’s gonna be COMPULSORY for all routes moving forward, I’ll brief everyone on what it does on monday

    ```php
    X-SKLearn-Platform
    X-SKLearn-Operating-System
    X-SKLearn-App-Version
    ```

---

# [v1.1.12-dev] 16 Jan 2025

- Added Department CRUD
- Added `department` into Competition Index
- Added `department_id` into Competition Create/Update

- Things to test
    - End to end flow for wallet deposit/transfer
        - Success/Pending/Failed scenario
        - To check Billing Document status changed to VOID if transaction failed
        - To check Billing Document payment status to be changed to PAID
        - To check a row in `payments` table is created after successful payment (pending/failed payment will not create this record)
        - Changed `customer_email` field to `present` instead of `required`, App still need pass in this field even if email is null when doing topup, Wilson/Mori to help to test this too
    - End to end flow for hostel saving account
    - End to end flow for accounting module (if FE is ready) Create fee → pay fee (test with success/failed scenarios)

---

# [v1.1.11-dev] 15 Jan 2025

- Added Migration
    - Book Loan
    - Hostel Block
- Fix bug for removing optional field in Employee
