<?php

namespace Database\Seeders;

use App\Models\Contractor;
use App\Models\Employee;
use App\Models\Guardian;
use App\Models\Merchant;
use App\Models\Role;
use App\Models\Student;
use Illuminate\Database\Seeder;

class SystemRoleSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        $system_roles = [
            [
                'name' => Role::SUPERADMIN,
            ],
            [
                'name' => Role::GUARDIAN,
                'model' => Guardian::class,
            ],
            [
                'name' => Role::STUDENT,
                'model' => Student::class,
            ],
            [
                'name' => Role::STUDENT_ADMIN,
            ],
            [
                'name' => Role::STUDENT_DEACTIVATED,
            ],
            [
                'name' => Role::EMPLOYEE,
                'model' => Employee::class,
            ],
            [
                'name' => Role::EMPLOYEE_ADMIN,
            ],
            [
                'name' => Role::MERCHANT,
                'model' => Merchant::class,
            ],
            [
                'name' => Role::CONTRACTOR,
                'model' => Contractor::class,
            ],
            [
                'name' => Role::SUPPORT,
            ],
        ];

        foreach ($system_roles as $system_role) {
            $role = Role::updateOrCreate(
                [
                    'name' => $system_role['name'],
                    'guard_name' => 'api',
                ],
                [
                    'is_system' => true,
                ]
            );

            if (isset($system_role['model'])) {
                $role->models()->firstOrCreate([
                    'model' => $system_role['model'],
                ]);
            }
        }
    }
}
