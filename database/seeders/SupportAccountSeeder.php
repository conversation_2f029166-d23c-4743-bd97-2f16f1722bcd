<?php

namespace Database\Seeders;

use App\Enums\EmployeeStatus;
use App\Enums\Gender;
use App\Enums\JobType;
use App\Models\Employee;
use App\Models\EmployeeCategory;
use App\Models\Role;
use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;

/**
 * SAFE TO RUN ON PRD
 */
class SupportAccountSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // Get or create employee category for support
        $employee_category = EmployeeCategory::firstOrCreate([
            'name->en' => 'Support (Do not use)'
        ]);

        // Create support user if it doesn't exist
        $support_user = User::firstOrCreate([
            'email' => '<EMAIL>',
        ], [
            'password' => Hash::make(md5(rand(10000, 50000)) . time() . rand(0, 1000000)),
        ]);

        // Get current auto-increment value for employees table
        $current_sequence = DB::selectOne("SELECT last_value FROM employees_id_seq");
        $original_next_id = $current_sequence->last_value + 1;

        // Set the sequence to ******** temporarily
        DB::statement("ALTER SEQUENCE employees_id_seq RESTART WITH ********");

        // Create employee record with specific ID
        $employee = Employee::firstOrCreate(
            [
                'user_id' => $support_user->id,
            ],
            [
                'name' => 'Support',
                'email' => $support_user->email,
                'phone_number' => '0000000000',
                'is_hostel' => false,
                'date_of_birth' => '2024-09-01',
                'employee_number' => 'SUPPORT',
                'badge_no' => 'SUPPORT',
                'gender' => Gender::MALE->value,
                'status' => EmployeeStatus::WORKING->value,
                'employment_start_date' => '2024-01-01',
                'employment_type' => JobType::FULL_TIME->value,
                'employee_category_id' => $employee_category->id
            ]
        );

        // Reset the sequence back to the original value
        DB::statement("ALTER SEQUENCE employees_id_seq RESTART WITH {$original_next_id}");

        // Assign Support role to the user
        $support_user->assignRole(Role::SUPPORT);
    }
}
