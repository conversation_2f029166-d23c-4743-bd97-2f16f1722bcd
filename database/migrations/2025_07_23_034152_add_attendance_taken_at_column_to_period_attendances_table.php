<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('period_attendances', function (Blueprint $table) {
            $table->timestamp('attendance_taken_at')
                ->nullable()
                ->comment('The latest time the attendance was taken for this period');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('period_attendances', function (Blueprint $table) {
            $table->dropColumn('attendance_taken_at');
        });
    }
};
