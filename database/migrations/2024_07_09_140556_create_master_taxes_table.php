<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('master_taxes', function (Blueprint $table) {
            $table->id();
            $table->string('code', 8);
            $table->string('name', 64);
            $table->decimal('percentage', 6, 2)->unsigned();
            $table->dateTime('applicable_from');
            $table->dateTime('applicable_to');
            $table->string('billing_document_classification', 6)->comment('AR / AP');
            $table->timestamps();

            $table->index(['code']);
            $table->index(['applicable_from', 'applicable_to', 'billing_document_classification']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('master_taxes');
    }
};
