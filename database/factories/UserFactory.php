<?php

namespace Database\Factories;

use App\Models\Employee;
use App\Models\Guardian;
use App\Models\Student;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\User>
 */
class UserFactory extends Factory
{
    /**
     * The current password being used by the factory.
     */
    protected static ?string $password;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'email' => fake()->unique()->safeEmail(),
            'phone_number' => fake()->e164PhoneNumber(),
            'is_active' => true,
            'last_login_at' => now(),
            'email_verified_at' => now(),
            'password' => static::$password ??= Hash::make('password'),
            'remember_token' => Str::random(10),
            'is_password_reset_required' => false,
            'push_notification_token' => null,
            'push_notification_platform' => null,
        ];
    }

    /**
     * Indicate that the model's email address should be unverified.
     */
    public function unverified(): static
    {
        return $this->state(fn(array $attributes) => [
            'email_verified_at' => null,
        ]);
    }

    public function withStudent(): static
    {
        return $this->afterCreating(function (User $user) {
            $name = str_replace("'", "", fake()->name());

            Student::factory()->create([
                'user_id' => $user->id,
                'email' => $user->email,
                'phone_number' => $user->phone_number,
                'name->en' => $name,
            ]);
        });
    }

    public function withEmployee(): static
    {
        return $this->afterCreating(function (User $user) {
            $name = str_replace("'", "", fake()->name());

            Employee::factory()->create([
                'user_id' => $user->id,
                'email' => $user->email,
                'phone_number' => $user->phone_number,
                'name->en' => $name,
            ]);
        });
    }

    public function withGuardian(): static
    {
        return $this->afterCreating(function (User $user) {
            $name = str_replace("'", "", fake()->name());

            Guardian::factory()->create([
                'user_id' => $user->id,
                'email' => $user->email,
                'phone_number' => $user->phone_number,
                'name->en' => $name,
            ]);
        });
    }
}
