<?php

namespace Database\Factories;

use App\Models\DiscountSetting;
use App\Models\Employee;
use App\Models\GlAccount;
use App\Models\Student;
use Illuminate\Database\Eloquent\Factories\Factory;

class DiscountSettingFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'basis' => DiscountSetting::BASIS_PERCENT,
            'basis_amount' => 100,
            'max_amount' => 2000,
            'used_amount' => 0,
            'effective_from' => '2024-01-01',
            'effective_to' => '2024-12-31',
            'description' => null,
            'gl_account_codes' => json_encode([GlAccount::CODE_OTHERS]),
            'source_type' => null,
            'source_id' => null,
            'userable_type' => Student::class,
            'userable_id' => 1,
            'is_active' => true,
            'created_by_employee_id' => Employee::factory(),
        ];
    }

    /**
     * Indicate that the model's userable is Student.
     */
    public function studentUserable(): static
    {
        return $this->state(fn(array $attributes) => [
            'userable_type' => Student::class,
            'userable_id' => Student::factory(),
        ]);
    }
}
