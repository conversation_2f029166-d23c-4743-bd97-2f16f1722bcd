<?php

namespace Database\Factories;

use App\Enums\PeriodAttendanceStatus;
use App\Models\Student;
use App\Models\Timeslot;
use Illuminate\Database\Eloquent\Factories\Factory;

class PeriodAttendanceFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'student_id' => Student::factory(),
            'date' => now()->toDateString(),
            'timeslot_type' => Timeslot::class,
            'timeslot_id' => Timeslot::factory(),
            'updated_by_employee_id' => null,
            'status' => PeriodAttendanceStatus::ABSENT->value,
            'leave_application_id' => null,
            'period' => 1,
            'has_mark_deduction' => true,
            'attendance_taken_at' => null,
        ];
    }
}
