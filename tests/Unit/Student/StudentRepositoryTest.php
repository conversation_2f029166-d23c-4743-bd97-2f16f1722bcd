<?php

use App\Enums\CardStatus;
use App\Enums\ClassStream;
use App\Enums\ClassType;
use App\Enums\Gender;
use App\Enums\GuardianType;
use App\Enums\HostelInOutType;
use App\Enums\LibraryMemberType;
use App\Enums\MeritDemeritType;
use App\Enums\RewardPunishmentRecordStatus;
use App\Enums\StudentAdmissionType;
use App\Enums\StudentLeaveStatus;
use App\Models\Book;
use App\Models\Card;
use App\Models\ClassModel;
use App\Models\CompetitionRecord;
use App\Models\Country;
use App\Models\Course;
use App\Models\Employee;
use App\Models\Grade;
use App\Models\Guardian;
use App\Models\GuardianStudent;
use App\Models\HostelBedAssignment;
use App\Models\HostelBlock;
use App\Models\HostelInOutRecord;
use App\Models\HostelRoom;
use App\Models\HostelRoomBed;
use App\Models\LatestPrimaryClassBySemesterSettingView;
use App\Models\LibraryBookLoan;
use App\Models\LibraryMember;
use App\Models\MeritDemeritSetting;
use App\Models\Race;
use App\Models\Religion;
use App\Models\RewardPunishment;
use App\Models\RewardPunishmentRecord;
use App\Models\SemesterClass;
use App\Models\SemesterSetting;
use App\Models\SemesterYearSetting;
use App\Models\State;
use App\Models\Student;
use App\Models\StudentClass;
use App\Models\User;
use App\Repositories\StudentRepository;
use Carbon\Carbon;
use Database\Seeders\InternationalizationSeeder;
use Illuminate\Database\Eloquent\Factories\Sequence;
use Illuminate\Support\Facades\DB;

beforeEach(function () {

    $this->seed([InternationalizationSeeder::class]);
    $this->masterStudentRepository = resolve(StudentRepository::class);

    app()->setLocale('en');

    $this->testLocale = app()->getLocale();

    $this->malaysia = Country::factory()->create([
        'name->en' => 'Malaysia'
    ]);

    $this->first_state = State::factory()->create([
        'name->en' => 'Kedah',
        'country_id' => $this->malaysia->id,
    ]);

    $this->second_state = State::factory()->create([
        'name->en' => 'Kelantan',
        'country_id' => $this->malaysia->id,
    ]);

    $this->first_grade = Grade::factory()->create([
        'name->en' => 'Student 1',
        'name->zh' => '初一',
        'sequence' => 1,
    ]);

    $this->second_grade = Grade::factory()->create([
        'name->en' => 'Student 2',
        'name->zh' => '初二',
        'sequence' => 2,
    ]);

    $this->first_religion = Religion::factory()->create([
        'name' => 'Chinese'
    ]);

    $this->second_religion = Religion::factory()->create([
        'name' => 'Chinese (China)'
    ]);

    $this->first_race = Race::factory()->create([
        'name' => 'malay'
    ]);

    $this->second_race = Race::factory()->create([
        'name' => 'chinese'
    ]);
});

test('getModelClass()', function () {
    $response = $this->masterStudentRepository->getModelClass();

    expect($response)->toEqual(Student::class);
});
// Not in use anymore
//test('getAll() filter by book loan exist', function () {
//    $students = Student::factory(2)->state(new Sequence(
//        [
//            'name->en' => 'Student 1'
//        ],
//        [
//            'name->en' => 'Student 2'
//        ],
//    ))->create();
//
//    $members = LibraryMember::factory(3)->state(new Sequence(
//        [
//            'name' => 'member 1',
//            'type' => LibraryMemberType::STUDENT,
//            'userable_id' => $students[0]->id,
//            'userable_type' => Student::class,
//        ],
//        [
//            'name' => 'member 2',
//            'type' => LibraryMemberType::STUDENT,
//            'userable_id' => $students[1]->id,
//            'userable_type' => Student::class,
//        ],
//        [
//            'name' => 'member 3',
//            'type' => LibraryMemberType::GUARDIAN
//        ],
//    ))->create();
//
//    $books = Book::factory(3)->state(new Sequence(
//        [
//            'book_no' => '123',
//            'title' => 'Book 1',
//        ],
//        [
//            'book_no' => '124',
//            'title' => 'Book 2',
//        ],
//        [
//            'book_no' => '125',
//            'title' => 'Book 3',
//        ],
//    ))->create();
//
//    $bookLoans = LibraryBookLoan::factory(4)->state(new Sequence(
//        [
//            'book_id' => $books[0]->id,
//            'member_id' => $members[0]->id,
//            'loan_date' => '2024-01-01'
//        ],
//        [
//            'book_id' => $books[1]->id,
//            'member_id' => $members[0]->id,
//            'loan_date' => '2024-01-02'
//        ],
//        [
//            'book_id' => $books[2]->id,
//            'member_id' => $members[1]->id,
//            'loan_date' => '2024-01-03'
//        ]
//    ))->create();
//
//    $filters = [
//        'book_loan_exist' => true,
//        'includes' => ['libraryMember', 'libraryMember.bookLoans']
//    ];
//
//    $response = $this->masterStudentRepository->getAll($filters)->toArray();
//
//    foreach ($response as $student) {
//        expect($student['library_member']['book_loans'])->not->toBeEmpty();
//    }
//});

test('getAll() filter by book period loan date', function () {
    $students = Student::factory(2)->state(new Sequence(
        [
            'name->en' => 'Student 1'
        ],
        [
            'name->en' => 'Student 2'
        ],
    ))->create();

    $members = LibraryMember::factory(3)->state(new Sequence(
        [
            'name' => 'member 1',
            'type' => LibraryMemberType::STUDENT,
            'userable_id' => $students[0]->id,
            'userable_type' => Student::class,
        ],
        [
            'name' => 'member 2',
            'type' => LibraryMemberType::STUDENT,
            'userable_id' => $students[1]->id,
            'userable_type' => Student::class,
        ],
        [
            'name' => 'member 3',
            'type' => LibraryMemberType::GUARDIAN
        ],
    ))->create();

    $books = Book::factory(3)->state(new Sequence(
        [
            'book_no' => '123',
            'title' => 'Book 1',
        ],
        [
            'book_no' => '124',
            'title' => 'Book 2',
        ],
        [
            'book_no' => '125',
            'title' => 'Book 3',
        ],
    ))->create();

    $bookLoans = LibraryBookLoan::factory(4)->state(new Sequence(
        [
            'book_id' => $books[0]->id,
            'member_id' => $members[0]->id,
            'loan_date' => '2024-01-01'
        ],
        [
            'book_id' => $books[1]->id,
            'member_id' => $members[0]->id,
            'loan_date' => '2024-01-02'
        ],
        [
            'book_id' => $books[2]->id,
            'member_id' => $members[1]->id,
            'loan_date' => '2024-01-03'
        ]
    ))->create();

    $start_loan_date = '2024-01-01';
    $end_loan_date = '2024-01-02';
    $filters = [
        'library_book_loan_date_from' => $start_loan_date,
        'library_book_loan_date_to' => $end_loan_date,
        'includes' => ['libraryMember', 'libraryMember.bookLoans']
    ];

    $response = $this->masterStudentRepository->getAll($filters)->toArray();

    foreach ($response as $student) {
        foreach ($student['library_member']['book_loans'] as $book_loan) {
            $loan_date = Carbon::parse($book_loan['loan_date'])->toDateString();

            expect($loan_date)->toBeGreaterThanOrEqual($start_loan_date)
                ->and($loan_date)->toBeLessThanOrEqual($end_loan_date);
        }
    }
});

test('getAll()', function () {
    $first_student = Student::factory()->create([
        'admission_year' => 2024,
        'admission_grade_id' => $this->first_grade->id,
        'join_date' => '2024-01-01',
        'student_number' => '001',
        // name is now stored in users table
        //        'name->en' => 'Student 1',
        //        'name->zh' => '学生 1',
        'birthplace' => $this->malaysia->name,
        'nationality_id' => $this->malaysia->id,
        'date_of_birth' => '2001-01-01',
        'gender' => 'MALE',
        'birth_cert_number' => '001',
        'nric' => '001',
        'passport_number' => '001',
        'race_id' => $this->first_race->id,
        'religion_id' => $this->first_religion->id,
        'address' => '123 Main Street',
        'postal_code' => '12345',
        'city' => 'Petaling Jaya',
        'state_id' => $this->first_state->id,
        'country_id' => $this->malaysia->id,
        'remarks' => 'This is a student remark for student 1',
        'custom_field' => [
            'key' => 'custom_key',
            'value' => 'custom_value'
        ],
    ]);

    $second_student = Student::factory()->create([
        'admission_year' => 2024,
        'admission_grade_id' => $this->first_grade->id,
        'student_number' => '002',
        // name is now stored in users table
        //        'name->en' => 'Student 2',
        //        'name->zh' => '学生 2',
        'birthplace' => $this->malaysia->name,
        'nationality_id' => $this->malaysia->id,
        'date_of_birth' => '2002-02-02',
        'gender' => 'MALE',
        'birth_cert_number' => '002',
        'nric' => '002',
        'passport_number' => '002',
        'race_id' => $this->first_race->id,
        'religion_id' => $this->first_religion->id,
        'address' => '123 Main Street',
        'postal_code' => '12345',
        'city' => 'Damansara',
        'state_id' => $this->first_state->id,
        'country_id' => $this->malaysia->id,
        'remarks' => 'This is a student remark for student 2',
        'custom_field' => [
            'key' => 'custom_key',
            'value' => 'custom_value'
        ],
    ]);

    $third_student = Student::factory()->create([
        'admission_year' => 2024,
        'admission_grade_id' => $this->second_grade->id,
        'join_date' => '2024-01-01',
        'student_number' => '003',
        // name is now stored in users table
        //        'name->en' => 'Student 3',
        //        'name->zh' => '学生 3',
        'birthplace' => $this->malaysia->name,
        'nationality_id' => $this->malaysia->id,
        'date_of_birth' => '2013-11-01',
        'gender' => 'FEMALE',
        'birth_cert_number' => '003',
        'nric' => '003',
        'passport_number' => '003',
        'race_id' => $this->second_race->id,
        'religion_id' => $this->second_religion->id,
        'address' => '123 Main Street',
        'postal_code' => '12345',
        'city' => 'Damansara',
        'state_id' => $this->second_state->id,
        'country_id' => $this->malaysia->id,
        'remarks' => 'This is a student remark for student 3',
        'custom_field' => [
            'key' => 'custom_key',
            'value' => 'custom_value'
        ],
    ]);

    $response = $this->masterStudentRepository->getAll(['order_by' => ['id' => 'asc']])->toArray();

    expect($response)->toEqual([
        $first_student->toArray(),
        $second_student->toArray(),
        $third_student->toArray(),
    ]);
});

test('getAll() filter by latest_semester_class_id', function () {
    //prepare data
    $students = Student::factory(5)->state(new Sequence(
        [
            'name->en' => 'Student 1',
        ],
        [
            'name->en' => 'Student 2',
        ],
        [
            'name->en' => 'Student 3',
        ],
        [
            'name->en' => 'Student 4',
        ],
        [
            'name->en' => 'Student 5',
        ],
    ))->create();

    $classes = ClassModel::factory(3)->state(new Sequence(
        [
            'name' => 'J111',
        ],
        [
            'name' => 'J112',
        ],
        [
            'name' => 'J222',
        ],
    ))->create();

    $semester_year_setting = SemesterYearSetting::factory()->create(['year' => 2024]);

    $semester_settings = SemesterSetting::factory(2)
        ->state(new Sequence(
            [
                'name' => 'Semester 1',
                'semester_year_setting_id' => $semester_year_setting->id,
                'is_current_semester' => true
            ],
            [
                'name' => 'Semester 2',
                'semester_year_setting_id' => $semester_year_setting->id,
                'is_current_semester' => true
            ],
        ))
        ->create();

    $semester_classes = SemesterClass::factory(3)
        ->state(new Sequence(
            [
                'class_id' => $classes[0]->id,
                'semester_setting_id' => $semester_settings[0]->id,
            ],
            [
                'class_id' => $classes[1]->id,
                'semester_setting_id' => $semester_settings[0]->id,
            ],
            [
                'class_id' => $classes[2]->id,
                'semester_setting_id' => $semester_settings[1]->id,
            ],
        ))
        ->create();

    $student_classes = StudentClass::factory(6)->state(new Sequence(
        [
            'student_id' => $students[0]->id,
            'seat_no' => 1,
            'semester_setting_id' => $semester_settings[0]->id,
            'semester_class_id' => $semester_classes[0]->id,
            'class_enter_date' => '2024-08-08',
        ],
        [
            'student_id' => $students[0]->id,
            'seat_no' => 1,
            'semester_setting_id' => $semester_settings[0]->id,
            'semester_class_id' => $semester_classes[1]->id,
            'class_enter_date' => '2024-08-09',
        ],
        [
            'student_id' => $students[1]->id,
            'seat_no' => 2,
            'semester_setting_id' => $semester_settings[0]->id,
            'semester_class_id' => $semester_classes[0]->id,
        ],
        [
            'student_id' => $students[2]->id,
            'seat_no' => 3,
            'semester_setting_id' => $semester_settings[0]->id,
            'semester_class_id' => $semester_classes[1]->id,
        ],
        [
            'student_id' => $students[3]->id,
            'seat_no' => 4,
            'semester_setting_id' => $semester_settings[0]->id,
            'semester_class_id' => $semester_classes[1]->id,
        ],
        [
            'student_id' => $students[4]->id,
            'seat_no' => 5,
            'semester_setting_id' => $semester_settings[1]->id,
            'semester_class_id' => $semester_classes[2]->id,
        ],
    ))->create();

    DB::statement('REFRESH MATERIALIZED VIEW latest_primary_class_by_semester_setting_views');

    // Only have 1 student, because another student moved to $semester_classes[1]
    $response = $this->masterStudentRepository->getAll([
        'latest_semester_class_id' => $semester_classes[0]->id,
        'includes' => [
            'latestPrimaryClassBySemesterSettings' => function ($query) use ($semester_classes) {
                $query->where('semester_class_id', $semester_classes[0]->id);
            }
        ]
    ])->toArray();

    expect($response)->toHaveCount(1)
        ->and($response[0]['latest_primary_class_by_semester_settings'][0])->toMatchArray([
            'semester_class_id' => $semester_classes[0]->id,
            'semester_setting_id' => $semester_settings[0]->id,
            'student_id' => $students[1]->id,
        ]);

    $response = $this->masterStudentRepository->getAll([
        'latest_semester_class_id' => $semester_classes[1]->id,
        'includes' => [
            'latestPrimaryClassBySemesterSettings' => function ($query) use ($semester_classes) {
                $query->where('semester_class_id', $semester_classes[1]->id);
            }
        ],
        'order_by' => [
            'name' => [
                'en' => 'asc'
            ]
        ]
    ])->toArray();

    expect($response)->toHaveCount(3)
        ->sequence(
            fn($student) => $student->toHaveKey('latest_primary_class_by_semester_settings.0.semester_class_id', $semester_classes[1]->id)
                ->toHaveKey('latest_primary_class_by_semester_settings.0.student_id', $students[0]->id),
            fn($student) => $student->toHaveKey('latest_primary_class_by_semester_settings.0.semester_class_id', $semester_classes[1]->id)
                ->toHaveKey('latest_primary_class_by_semester_settings.0.student_id', $students[2]->id),
            fn($student) => $student->toHaveKey('latest_primary_class_by_semester_settings.0.semester_class_id', $semester_classes[1]->id)
                ->toHaveKey('latest_primary_class_by_semester_settings.0.student_id', $students[3]->id)
        );
});

test('getAll() filter by latest_semester_setting_id', function () {
    //prepare data
    $students = Student::factory(5)->state(new Sequence(
        [
            'name->en' => 'Student 1',
        ],
        [
            'name->en' => 'Student 2',
        ],
        [
            'name->en' => 'Student 3',
        ],
        [
            'name->en' => 'Student 4',
        ],
        [
            'name->en' => 'Student 5',
        ],
    ))->create();

    $classes = ClassModel::factory(4)->state(new Sequence(
        [
            'name' => 'J111',
        ],
        [
            'name' => 'J112',
        ],
        [
            'name' => 'J222',
        ],
        [
            'name' => 'J223'
        ]
    ))->create();

    $semester_year_setting = SemesterYearSetting::factory()->create(['year' => 2024]);

    $semester_settings = SemesterSetting::factory(2)
        ->state(new Sequence(
            [
                'name' => 'Semester 1',
                'semester_year_setting_id' => $semester_year_setting->id,
                'is_current_semester' => true
            ],
            [
                'name' => 'Semester 2',
                'semester_year_setting_id' => $semester_year_setting->id,
                'is_current_semester' => true
            ],
        ))
        ->create();

    $semester_classes = SemesterClass::factory(4)
        ->state(new Sequence(
            [
                'class_id' => $classes[0]->id,
                'semester_setting_id' => $semester_settings[0]->id,
            ],
            [
                'class_id' => $classes[1]->id,
                'semester_setting_id' => $semester_settings[0]->id,
            ],
            [
                'class_id' => $classes[2]->id,
                'semester_setting_id' => $semester_settings[1]->id,
            ], [
                'class_id' => $classes[3]->id,
                'semester_setting_id' => $semester_settings[1]->id,
            ]
        ))
        ->create();

    $student_classes = StudentClass::factory(7)->state(new Sequence(
        [
            'student_id' => $students[0]->id,
            'seat_no' => 1,
            'semester_setting_id' => $semester_settings[0]->id,
            'semester_class_id' => $semester_classes[0]->id,
        ],
        [
            'student_id' => $students[1]->id,
            'seat_no' => 2,
            'semester_setting_id' => $semester_settings[0]->id,
            'semester_class_id' => $semester_classes[0]->id,
        ],
        [
            'student_id' => $students[2]->id,
            'seat_no' => 3,
            'semester_setting_id' => $semester_settings[0]->id,
            'semester_class_id' => $semester_classes[1]->id,
        ],
        [
            'student_id' => $students[3]->id,
            'seat_no' => 4,
            'semester_setting_id' => $semester_settings[0]->id,
            'semester_class_id' => $semester_classes[1]->id,
        ],
        [
            'student_id' => $students[2]->id,
            'seat_no' => 5,
            'semester_setting_id' => $semester_settings[1]->id,
            'semester_class_id' => $semester_classes[2]->id,
        ],
        [
            'student_id' => $students[4]->id,
            'seat_no' => 5,
            'semester_setting_id' => $semester_settings[1]->id,
            'semester_class_id' => $semester_classes[2]->id,
            'class_enter_date' => '2024-08-08',
        ],
        [
            'student_id' => $students[4]->id,
            'seat_no' => 5,
            'semester_setting_id' => $semester_settings[1]->id,
            'semester_class_id' => $semester_classes[3]->id,
            'class_enter_date' => '2024-08-09',
        ],

    ))->create();

    DB::statement('REFRESH MATERIALIZED VIEW latest_primary_class_by_semester_setting_views');

    $response = $this->masterStudentRepository->getAll([
        'latest_class_semester_setting_id' => $semester_settings[1]->id,
        'includes' => [
            'latestPrimaryClassBySemesterSettings' => function ($query) use ($semester_settings) {
                $query->where('semester_setting_id', $semester_settings[1]->id);
            }
        ],
        'order_by' => [
            'name' => [
                'en' => 'asc',
            ]
        ]
    ])->toArray();

    expect($response)->toHaveCount(2)
        ->sequence(
            fn($item) => $item->toHaveKey('latest_primary_class_by_semester_settings.0.semester_class_id', $semester_classes[2]->id)
                ->toHaveKey('id', $students[2]->id),
            fn($item) => $item->toHaveKey('latest_primary_class_by_semester_settings.0.semester_class_id', $semester_classes[3]->id)
                ->toHaveKey('id', $students[4]->id),
        );
});

test('getAll() filter by left_school', function () {
    Student::factory(3)->state(new Sequence(
        [
            'name->en' => 'Student 1',
            'leave_status' => null,
        ],
        [
            'name->en' => 'Student 2',
            'leave_status' => StudentLeaveStatus::LEFT,
        ],
        [
            'name->en' => 'Student 3',
            'leave_status' => StudentLeaveStatus::GRADUATED,
        ],
    ))->create();

    $response = $this->masterStudentRepository->getAll([
        'left_school' => true,
    ])->toArray();

    expect($response)->toHaveCount(1)
        ->and($response[0]['name']['en'])->toBe('Student 2');

    $response = $this->masterStudentRepository->getAll([
        'left_school' => false,
    ])->toArray();

    expect($response)->toHaveCount(2)
        ->and($response[0]['name']['en'])->toBe('Student 3')
        ->and($response[1]['name']['en'])->toBe('Student 1');
});

test('getAllPaginated()', function () {
    $first_student = Student::factory()
        ->create([
            'admission_year' => 2024,
            'admission_grade_id' => $this->first_grade->id,
            'admission_type' => StudentAdmissionType::NEW,
            'join_date' => '2024-01-01',
            'student_number' => '2025001',
            // name is now stored in users table
            'name->en' => 'Student 1',
            'name->zh' => '学生 1',
            'phone_number' => '*********',
            'email' => '<EMAIL>',
            'birthplace' => $this->malaysia->name,
            'nationality_id' => $this->malaysia->id,
            'date_of_birth' => '2001-01-01',
            'gender' => 'MALE',
            'birth_cert_number' => '001',
            'nric' => '001',
            'passport_number' => '001',
            'race_id' => $this->first_race->id,
            'religion_id' => $this->first_religion->id,
            'address' => '123 Main Street',
            'postal_code' => '12345',
            'city' => 'Petaling Jaya',
            'state_id' => $this->first_state->id,
            'country_id' => $this->malaysia->id,
            'remarks' => 'This is a student remark for student 1',
            'is_active' => true,
            'custom_field' => [
                'key' => 'custom_key',
                'value' => 'custom_value'
            ],
        ]);

    $second_student = Student::factory()
        ->create([
            'admission_year' => 2024,
            'admission_grade_id' => $this->first_grade->id,
            'admission_type' => StudentAdmissionType::TRANSFERRED,
            'student_number' => '2025002',
            // name is now stored in users table
            'name->en' => 'Student 2',
            'name->zh' => '学生 2',
            'phone_number' => '*********',
            'email' => '<EMAIL>',
            'birthplace' => $this->malaysia->name,
            'nationality_id' => $this->malaysia->id,
            'date_of_birth' => '2002-02-02',
            'gender' => 'MALE',
            'birth_cert_number' => '002',
            'nric' => '002',
            'passport_number' => '002',
            'race_id' => $this->first_race->id,
            'religion_id' => $this->first_religion->id,
            'address' => '123 Main Street',
            'postal_code' => '12345',
            'city' => 'Damansara',
            'state_id' => $this->first_state->id,
            'country_id' => $this->malaysia->id,
            'remarks' => 'This is a student remark for student 2',
            'is_active' => true,
            'custom_field' => [
                'key' => 'custom_key',
                'value' => 'custom_value'
            ],
        ]);

    $third_student = Student::factory()
        ->create([
            'admission_year' => 2024,
            'admission_grade_id' => $this->second_grade->id,
            'admission_type' => StudentAdmissionType::TRANSFERRED,
            'join_date' => '2024-01-01',
            'student_number' => '2025003',
            // name is now stored in users table
            'name->en' => 'Thomas 3',
            'name->zh' => '学生 3',
            'phone_number' => '*********',
            'email' => '<EMAIL>',
            'birthplace' => $this->malaysia->name,
            'nationality_id' => $this->malaysia->id,
            'date_of_birth' => '2013-11-01',
            'gender' => 'FEMALE',
            'birth_cert_number' => '003',
            'nric' => '003',
            'passport_number' => '003',
            'race_id' => $this->second_race->id,
            'religion_id' => $this->second_religion->id,
            'address' => '123 Main Street',
            'postal_code' => '12345',
            'city' => 'Damansara',
            'state_id' => $this->second_state->id,
            'country_id' => $this->malaysia->id,
            'remarks' => 'This is a student remark for student 3',
            'is_hostel' => true,
            'is_active' => false,
            'custom_field' => [
                'key' => 'custom_key',
                'value' => 'custom_value'
            ],
        ]);

    // Filter by admission_type = NEW
    $response = $this->masterStudentRepository->getAllPaginated([
        'admission_type' => StudentAdmissionType::NEW
    ])->toArray();

    expect($response['data'])->toEqual([$first_student->toArray()]);

    // Filter by name = Student 1
    $response = $this->masterStudentRepository->getAllPaginated([
        'name' => ['en' => 'Student 1']
    ])->toArray();

    expect($response['data'])->toEqual([$first_student->toArray()]);

    // Filter by name = Student 2
    $response = $this->masterStudentRepository->getAllPaginated([
        'name' => ['en' => 'Student 2']
    ])->toArray();

    expect($response['data'])->toEqual([$second_student->toArray()]);

    // Filter by partial name = student
    $response = $this->masterStudentRepository->getAllPaginated([
        'name' => ['en' => 'Student'],
        'order_by' => 'id'
    ])->toArray();

    expect($response['data'])->toEqual([
        $first_student->toArray(),
        $second_student->toArray(),
    ]);

    // Filter by non-existing name = Student 4
    $response = $this->masterStudentRepository->getAllPaginated([
        'name' => ['en' => 'Student 4']
    ])->toArray();

    expect($response['data'])->toBeEmpty();

    // Filter by phone number = *********
    $response = $this->masterStudentRepository->getAllPaginated([
        'phone_number' => '*********'
    ])->toArray();

    expect($response['data'])->toEqual([$first_student->toArray()]);

    // Filter by email = <EMAIL>
    $response = $this->masterStudentRepository->getAllPaginated([
        'email' => '<EMAIL>'
    ])->toArray();

    expect($response['data'])->toEqual([$second_student->toArray()]);

    // Filter by gender = male
    $response = $this->masterStudentRepository->getAllPaginated(['gender' => 'MALE', 'order_by' => 'id'])->toArray();

    expect($response['data'])->toEqual([
        $first_student->toArray(),
        $second_student->toArray()
    ]);

    // Filter by gender = female
    $response = $this->masterStudentRepository->getAllPaginated(['gender' => 'FEMALE'])->toArray();

    expect($response['data'])->toEqual([$third_student->toArray()]);

    // Filter by grade = first grade
    $response = $this->masterStudentRepository->getAllPaginated([
        'admission_grade_id' => $this->first_grade->id,
        'order_by' => 'id'
    ])->toArray();

    expect($response['data'])->toEqual([
        $first_student->toArray(),
        $second_student->toArray()
    ]);

    // Filter by grade = second grade
    $response = $this->masterStudentRepository->getAllPaginated(['admission_grade_id' => $this->second_grade->id])->toArray();

    expect($response['data'])->toEqual([$third_student->toArray()]);

    // Filter by country = malaysia
    $response = $this->masterStudentRepository->getAllPaginated([
        'country_id' => $this->malaysia->id,
        'order_by' => ['id' => 'asc']
    ])->toArray();

    expect($response['data'])->toEqual([
        $first_student->toArray(),
        $second_student->toArray(),
        $third_student->toArray()
    ]);

    // Filter by is_hostel = true
    $response = $this->masterStudentRepository->getAllPaginated(['is_hostel' => true])->toArray();

    expect($response['data'])->toEqual([$third_student->toArray()]);

    // Filter by student_number = '2025' // test partial student_number
    $response = $this->masterStudentRepository->getAllPaginated([
        'student_number_wildcard' => '2025',
        'order_by' => ['id' => 'asc']
    ])->toArray();

    expect($response['data'])->toEqual([
        $first_student->toArray(),
        $second_student->toArray(),
        $third_student->toArray()
    ]);

    // Filter by student_number = '003' // test partial student_number
    $response = $this->masterStudentRepository->getAllPaginated(['student_number_wildcard' => '003'])->toArray();

    expect($response['data'])->toEqual([
        $third_student->toArray()
    ]);

    // Filter by student_number = '002' // test partial student_number
    $response = $this->masterStudentRepository->getAllPaginated(['student_number_wildcard' => '002'])->toArray();

    expect($response['data'])->toEqual([
        $second_student->toArray()
    ]);

    // Filter by student_number = '2025002' // test student_number
    $response = $this->masterStudentRepository->getAllPaginated(['student_number' => '2025002'])->toArray();

    expect($response['data'])->toEqual([
        $second_student->toArray()
    ]);

    // Filter by student_name_or_student_number = '002' // student_number
    $response = $this->masterStudentRepository->getAllPaginated(['common_search' => '002'])->toArray();

    expect($response['data'])->toEqual([$second_student->toArray()]);

    // Filter by student_name_or_student_number = '003' // student_number
    $response = $this->masterStudentRepository->getAllPaginated(['common_search' => '003'])->toArray();

    expect($response['data'])->toEqual([$third_student->toArray()]);

    // Filter by student_name_or_student_number = 'Thomas 3' // student_name->en
    $response = $this->masterStudentRepository->getAllPaginated(['common_search' => 'Thomas 3'])->toArray();

    expect($response['data'])->toEqual([$third_student->toArray()]);

    // Filter by student_name_or_student_number = '学生 3' // student_name->zh
    $response = $this->masterStudentRepository->getAllPaginated(['common_search' => '学生 3'])->toArray();

    expect($response['data'])->toEqual([$third_student->toArray()]);

    // Sort by name asc
    $response = $this->masterStudentRepository->getAllPaginated([
        'order_by' => ['name' => 'asc'],
    ])->toArray();

    expect($response['data'][0]['id'])->toEqual($first_student->id)
        ->and($response['data'][1]['id'])->toEqual($second_student->id)
        ->and($response['data'][2]['id'])->toEqual($third_student->id);

    // Sort by name desc
    $response = $this->masterStudentRepository->getAllPaginated([
        'order_by' => ['name' => 'desc'],
    ])->toArray();

    expect($response['data'][0]['id'])->toEqual($third_student->id)
        ->and($response['data'][1]['id'])->toEqual($second_student->id)
        ->and($response['data'][2]['id'])->toEqual($first_student->id);

    // Sort by id asc
    $response = $this->masterStudentRepository->getAllPaginated([
        'order_by' => ['id' => 'asc'],
    ])->toArray();

    expect($response['data'][0]['id'])->toEqual($first_student->id)
        ->and($response['data'][1]['id'])->toEqual($second_student->id)
        ->and($response['data'][2]['id'])->toEqual($third_student->id);

    // Sort by id desc
    $response = $this->masterStudentRepository->getAllPaginated([
        'order_by' => ['id' => 'desc'],
    ])->toArray();

    expect($response['data'][0]['id'])->toEqual($third_student->id)
        ->and($response['data'][1]['id'])->toEqual($second_student->id)
        ->and($response['data'][2]['id'])->toEqual($first_student->id);

    $employee_user = Employee::factory()->create();
    HostelBedAssignment::factory()->create([
        'assignable_type' => Student::class,
        'assignable_id' => $third_student->id,
        'assigned_by' => $employee_user->user->id
    ]);

    // Filter by has_active_bed = true
    $response = $this->masterStudentRepository->getAllPaginated([
        'has_active_bed' => true,
    ])->toArray();

    expect($response['data'])->toEqual([$third_student->toArray()]);


    // Filter by has_active_bed = false
    $response = $this->masterStudentRepository->getAllPaginated([
        'has_active_bed' => false,
        'order_by' => ['id' => 'asc'],
    ])->toArray();

    expect($response['data'])->sequence(
        fn($item) => $item->toHaveKey('id', $first_student->id),
        fn($item) => $item->toHaveKey('id', $second_student->id),
    );

    // Filter by is_active = false
    $response = $this->masterStudentRepository->getAllPaginated([
        'is_active' => false,
        'order_by' => ['id' => 'asc'],
    ])->toArray();

    expect($response['data'])->toEqual([$third_student->toArray()]);
});

test('getAllPaginated() : filter by block_id, room_id, bed_id', function () {
    /**
     * student_solo in block_id = 1
     * student_3 n student_4 in block_id = 2, room_id 1, 2 respectively
     * student_5 n student_6 in block_id = 3, room_id 4, , bed_id 5, 6 respectively
     */
    $student_solo = Student::factory()->create();
    $student_2 = Student::factory()->create();
    $student_3 = Student::factory()->create();
    $student_4 = Student::factory()->create();
    $student_5 = Student::factory()->create();

    // only in 1 block
    $block_1 = HostelBlock::factory()->create();
    $block_1_room_1 = HostelRoom::factory()->create(['hostel_block_id' => $block_1->id]);
    $block_1_room_1_bed_1 = HostelRoomBed::factory()->create(['hostel_room_id' => $block_1_room_1->id]);

    HostelBedAssignment::factory()->create([
        'hostel_room_bed_id' => $block_1_room_1_bed_1->id,
        'assignable_type' => Student::class,
        'assignable_id' => $student_solo->id,
    ]);

    // same block but different room
    $block_2 = HostelBlock::factory()->create();
    $block_2_room_1 = HostelRoom::factory()->create(['hostel_block_id' => $block_2->id]);
    $block_2_room_2 = HostelRoom::factory()->create(['hostel_block_id' => $block_2->id]);
    $block_2_room_1_bed_1 = HostelRoomBed::factory()->create(['hostel_room_id' => $block_2_room_1->id]);
    $block_2_room_2_bed_1 = HostelRoomBed::factory()->create(['hostel_room_id' => $block_2_room_2->id]);

    HostelBedAssignment::factory()->create([
        'hostel_room_bed_id' => $block_2_room_1_bed_1->id,
        'assignable_type' => Student::class,
        'assignable_id' => $student_2->id,
    ]);

    HostelBedAssignment::factory()->create([
        'hostel_room_bed_id' => $block_2_room_2_bed_1->id,
        'assignable_type' => Student::class,
        'assignable_id' => $student_3->id,
    ]);

    // same block, same room but different bed
    $block_3 = HostelBlock::factory()->create();
    $block_3_room_1 = HostelRoom::factory()->create(['hostel_block_id' => $block_3->id]);
    $block_3_room_1_bed_1 = HostelRoomBed::factory()->create(['hostel_room_id' => $block_3_room_1->id]);
    $block_3_room_1_bed_2 = HostelRoomBed::factory()->create(['hostel_room_id' => $block_3_room_1->id]);

    HostelBedAssignment::factory()->create([
        'hostel_room_bed_id' => $block_3_room_1_bed_1->id,
        'assignable_type' => Student::class,
        'assignable_id' => $student_4->id,
    ]);

    HostelBedAssignment::factory()->create([
        'hostel_room_bed_id' => $block_3_room_1_bed_2->id,
        'assignable_type' => Student::class,
        'assignable_id' => $student_5->id,
    ]);

    // ===================================================================================================
    // filter by block_id => student_solo
    $response = $this->masterStudentRepository->getAllPaginated([
        'has_active_bed' => true,
        'block_id' => $block_1->id,
        'order_by' => ['id' => 'asc'],
    ])->toArray();

    expect($response['data'])->toHaveCount(1)->toEqual([$student_solo->toArray()]);

    // ===================================================================================================

    // filter by same_block but different_room

    $response = $this->masterStudentRepository->getAllPaginated([
        'has_active_bed' => true,
        'block_id' => $block_2->id,
        'room_id' => $block_2_room_1->id,
        'order_by' => ['id' => 'asc'],
    ])->toArray();

    expect($response['data'])->toHaveCount(1)->toEqual([$student_2->toArray()]);

    $response = $this->masterStudentRepository->getAllPaginated([
        'has_active_bed' => true,
        'block_id' => $block_2->id,
        'room_id' => $block_2_room_2->id,
        'order_by' => ['id' => 'asc'],
    ])->toArray();

    expect($response['data'])->toHaveCount(1)->toEqual([$student_3->toArray()]);

    // ===================================================================================================

    // filter by same_room but different_bed
    $response = $this->masterStudentRepository->getAllPaginated([
        'has_active_bed' => true,
        'block_id' => $block_3->id,
        'room_id' => $block_3_room_1->id,
        'bed_id' => $block_3_room_1_bed_1->id,
        'order_by' => ['id' => 'asc'],
    ])->toArray();

    expect($response['data'])->toHaveCount(1)->toEqual([$student_4->toArray()]);

    $response = $this->masterStudentRepository->getAllPaginated([
        'has_active_bed' => true,
        'block_id' => $block_3->id,
        'room_id' => $block_3_room_1->id,
        'bed_id' => $block_3_room_1_bed_2->id,
        'order_by' => ['id' => 'asc'],
    ])->toArray();

    expect($response['data'])->toHaveCount(1)->toEqual([$student_5->toArray()]);
});

test('getAllPaginated() : filter by semester_setting_id, grade_id, grade_ids, semester_class_id', function () {
    $teacher = Employee::factory()->create();

    $first_class = ClassModel::factory()->create([
        'grade_id' => $this->first_grade->id,
        'stream' => ClassStream::COMMERCE
    ]);
    $second_class = ClassModel::factory()->create([
        'grade_id' => $this->second_grade->id,
        'stream' => ClassStream::SCIENCE
    ]);

    $first_semester = SemesterSetting::factory()->create();
    $second_semester = SemesterSetting::factory()->create();

    $first_semester_class = SemesterClass::factory()->create([
        'semester_setting_id' => $first_semester->id,
        'class_id' => $first_class->id,
        'homeroom_teacher_id' => $teacher->id,
    ]);

    $second_semester_class = SemesterClass::factory()->create([
        'semester_setting_id' => $second_semester->id,
        'class_id' => $second_class->id,
        'homeroom_teacher_id' => $teacher->id,
    ]);

    $first_student_user = User::factory()->create();

    $first_student = Student::factory()->create([
        'user_id' => $first_student_user->id,
        'admission_grade_id' => $this->first_grade->id,
        'birthplace' => $teacher->country->name,
        'nationality_id' => $teacher->country_id,
        'race_id' => $teacher->race_id,
        'religion_id' => $teacher->religion_id,
        'state_id' => $teacher->state_id,
        'country_id' => $teacher->country_id,
    ]);

    StudentClass::factory()->create([
        'semester_setting_id' => $first_semester->id,
        'semester_class_id' => $first_semester_class->id,
        'student_id' => $first_student->id,
    ]);

    $second_student_user = User::factory()->create();

    $second_student = Student::factory()->create([
        'user_id' => $second_student_user->id,
        'admission_grade_id' => $this->second_grade->id,
        'birthplace' => $teacher->country->name,
        'nationality_id' => $teacher->country_id,
        'race_id' => $teacher->race_id,
        'religion_id' => $teacher->religion_id,
        'state_id' => $teacher->state_id,
        'country_id' => $teacher->country_id,
    ]);

    StudentClass::factory()->create([
        'semester_setting_id' => $second_semester->id,
        'semester_class_id' => $second_semester_class->id,
        'student_id' => $second_student->id,
    ]);

    // Filter by semester_setting_id = first_semester
    $response = $this->masterStudentRepository->getAllPaginated([
        'semester_setting_id' => $first_semester->id,
    ])->toArray();

    expect($response['data'])->toHaveCount(1)->toEqual([$first_student->toArray()]);

    // Filter by semester_setting_id = second_semester
    $response = $this->masterStudentRepository->getAllPaginated([
        'semester_setting_id' => $second_semester->id,
    ])->toArray();

    expect($response['data'])->toHaveCount(1)->toEqual([$second_student->toArray()]);

    // Filter by class_stream = COMMERCE
    $response = $this->masterStudentRepository->getAllPaginated([
        'class_stream' => ClassStream::COMMERCE->value,
    ])->toArray();

    expect($response['data'])->toHaveCount(1)->toEqual([$first_student->toArray()]);

    // Filter by class_stream = SCIENCE
    $response = $this->masterStudentRepository->getAllPaginated([
        'class_stream' => ClassStream::SCIENCE->value,
    ])->toArray();

    expect($response['data'])->toHaveCount(1)->toEqual([$second_student->toArray()]);

    // Filter by grade_id = first_grade
    $response = $this->masterStudentRepository->getAllPaginated([
        'grade_id' => $this->first_grade->id,
    ])->toArray();

    expect($response['data'])->toHaveCount(1)->toEqual([$first_student->toArray()]);

    // Filter by grade_id = second_grade
    $response = $this->masterStudentRepository->getAllPaginated([
        'grade_id' => $this->second_grade->id,
    ])->toArray();

    expect($response['data'])->toHaveCount(1)->toEqual([$second_student->toArray()]);

    // Filter by grade_ids = first_grade, second_grade
    $response = $this->masterStudentRepository->getAllPaginated([
        'grade_ids' => [$this->first_grade->id, $this->second_grade->id],
        'order_by' => ['id' => 'asc'],
    ])->toArray();

    expect($response['data'])->toHaveCount(2)->toEqual([$first_student->toArray(), $second_student->toArray()]);

    // Filter by semester_class_id = first_semester_class
    $response = $this->masterStudentRepository->getAllPaginated([
        'semester_class_id' => $first_semester_class->id,
    ])->toArray();

    expect($response['data'])->toHaveCount(1)->toEqual([$first_student->toArray()]);

    // Filter by semester_class_ids = [first_semester_class, second_semester_class]
    $response = $this->masterStudentRepository->getAllPaginated([
        'semester_class_ids' => [$first_semester_class->id, $second_semester_class->id],
        'order_by' => ['id' => 'asc']
    ])->toArray();

    expect($response['data'])->toHaveCount(2)->toEqual([$first_student->toArray(), $second_student->toArray()]);

    // Filter by semester_class_id = second_semester_class
    $response = $this->masterStudentRepository->getAllPaginated([
        'semester_class_id' => $second_semester_class->id,
    ])->toArray();

    expect($response['data'])->toHaveCount(1)->toEqual([$second_student->toArray()]);

    $response = $this->masterStudentRepository->getAllPaginated([
        'is_latest_class_in_semester' => true,
    ])->toArray();

    expect($response['data'])->toHaveCount(2);

    $response = $this->masterStudentRepository->getAllPaginated([
        'is_latest_class_in_semester' => false,
    ])->toArray();

    expect($response['data'])->toHaveCount(0);
});

test('getAllPaginated() : filter students by currently out of hostel or currently in the hostel', function () {
    /**
     * 3 student is outing, 2 student is in the hostel
     */
    $student_out_1 = Student::factory()->create(['is_hostel' => true]);
    $student_out_2 = Student::factory()->create(['is_hostel' => true]);
    $student_out_3 = Student::factory()->create(['is_hostel' => true]);

    HostelBedAssignment::factory()->create(['assignable_id' => $student_out_1->id]); // assign bed
    HostelBedAssignment::factory()->create(['assignable_id' => $student_out_2->id]);
    HostelBedAssignment::factory()->create(['assignable_id' => $student_out_3->id]);

    HostelInOutRecord::factory()->create(['student_id' => $student_out_1->id]); // old completed record
    HostelInOutRecord::factory()->create(['student_id' => $student_out_2->id]);

    HostelInOutRecord::factory()->onlyCheckout()->create(['student_id' => $student_out_1->id]); // only checkout not yet check in
    HostelInOutRecord::factory()->onlyCheckout()->create(['student_id' => $student_out_2->id]);
    HostelInOutRecord::factory()->onlyCheckout()->create(['student_id' => $student_out_3->id]);

    $student_in_1 = Student::factory()->create(['is_hostel' => true]);
    $student_in_2 = Student::factory()->create(['is_hostel' => true]);

    HostelBedAssignment::factory()->create(['assignable_id' => $student_in_1->id]); // assign bed
    HostelBedAssignment::factory()->create(['assignable_id' => $student_in_2->id]);

    HostelInOutRecord::factory()->create(['student_id' => $student_in_1->id]); // completed record
    HostelInOutRecord::factory()->create(['student_id' => $student_in_2->id]);

    // Filter by is_checked_out = true
    $response = $this->masterStudentRepository->getAllPaginated([
        'is_checked_out' => true,
        'order_by' => ['id' => 'asc'],
    ])->toArray();

    expect($response['data'])->toHaveCount(3)->toEqual([
        $student_out_1->toArray(),
        $student_out_2->toArray(),
        $student_out_3->toArray(),
    ]);

    // Filter by is_checked_out = false
    $response = $this->masterStudentRepository->getAllPaginated([
        'is_checked_out' => false,
        'order_by' => ['id' => 'asc'],
    ])->toArray();

    expect($response['data'])->toHaveCount(2)->toEqual([
        $student_in_1->toArray(),
        $student_in_2->toArray(),
    ]);
});

test('getAllPaginated() : filter by guardian properties', function () {
    $student_A1 = Student::factory()->create([
        'name->en' => 'Student A1',
        'email' => '<EMAIL>',
        'phone_number' => '+60128889999',
        'student_number' => 'STUDENT-A001',
    ]);

    $student_A2 = Student::factory()->create([
        'name->en' => 'Student A2',
        'email' => '<EMAIL>',
        'phone_number' => '+60123334444',
        'student_number' => 'STUDENT-A002',
    ]);

    $guardian_A = Guardian::factory()->create([
        'name->en' => 'Father A',
        'email' => '<EMAIL>',
        'phone_number' => '+60175558888',
    ]);

    $student_B = Student::factory()->create([
        'name->en' => 'Student B2',
        'email' => '<EMAIL>',
        'phone_number' => '+60134569875',
        'student_number' => 'STUDENT-B001',
    ]);

    $guardian_B = Guardian::factory()->create([
        'name->en' => 'Father B',
        'email' => '<EMAIL>',
        'phone_number' => '+60185550000',
    ]);

    GuardianStudent::factory(4)->state(new Sequence(
        [
            'guardian_id' => $guardian_A->id,
            'studenable_id' => $student_A1->id,
            'studenable_type' => Student::class,
            'is_direct_dependant' => true
        ],
        [
            'guardian_id' => $guardian_A->id,
            'studenable_id' => $student_A2->id,
            'studenable_type' => Student::class,
            'is_direct_dependant' => true
        ],
        [
            'guardian_id' => $guardian_B->id,
            'studenable_id' => $student_B->id,
            'studenable_type' => Student::class,
            'is_direct_dependant' => true
        ],
        [
            'guardian_id' => $guardian_B->id,
            'studenable_id' => $student_A2->id,
            'studenable_type' => Student::class,
            'is_direct_dependant' => false
        ]

    ))->create();

    // TESTING GUARDIAN_NAME FILTER
    // Return student A1 and student A2
    $response = $this->masterStudentRepository->getAllPaginated([
        'guardian_name' => $guardian_A->name,
        'order_by' => ['id' => 'asc'],
    ])->toArray();

    expect($response['data'])->toHaveCount(2)
        ->and($response['data'][0])->toMatchArray($student_A1->toArray())
        ->and($response['data'][1])->toMatchArray($student_A2->toArray());

    // Return student B
    $response = $this->masterStudentRepository->getAllPaginated([
        'guardian_name' => $guardian_B->name,
        'order_by' => ['id' => 'asc'],
    ])->toArray();

    expect($response['data'])->toHaveCount(2)
        ->and($response['data'][0])->toMatchArray($student_A2->toArray())
        ->and($response['data'][1])->toMatchArray($student_B->toArray());

    // Return student A1, student A2, student B (testing wildcard filter)
    $response = $this->masterStudentRepository->getAllPaginated([
        'guardian_name' => 'Father',
        'order_by' => ['id' => 'asc'],
    ])->toArray();

    expect($response['data'])->toHaveCount(3)
        ->and($response['data'][0])->toMatchArray($student_A1->toArray())
        ->and($response['data'][1])->toMatchArray($student_A2->toArray())
        ->and($response['data'][2])->toMatchArray($student_B->toArray());

    // TESTING GUARDIAN_PHONE_NUMBER FILTER
    // Return student A1 and student A2
    $response = $this->masterStudentRepository->getAllPaginated([
        'guardian_phone_number' => $guardian_A->phone_number,
        'order_by' => ['id' => 'asc'],
    ])->toArray();

    expect($response['data'])->toHaveCount(2)
        ->and($response['data'][0])->toMatchArray($student_A1->toArray())
        ->and($response['data'][1])->toMatchArray($student_A2->toArray());

    // Return student B
    $response = $this->masterStudentRepository->getAllPaginated([
        'guardian_phone_number' => $guardian_B->phone_number,
        'order_by' => ['id' => 'asc'],
    ])->toArray();

    expect($response['data'])->toHaveCount(2)
        ->and($response['data'][0])->toMatchArray($student_A2->toArray())
        ->and($response['data'][1])->toMatchArray($student_B->toArray());

    // Return student A1, student A2, student B (testing wildcard filter)
    $response = $this->masterStudentRepository->getAllPaginated([
        'guardian_phone_number' => +60,
        'order_by' => ['id' => 'asc'],
    ])->toArray();

    expect($response['data'])->toHaveCount(3)
        ->and($response['data'][0])->toMatchArray($student_A1->toArray())
        ->and($response['data'][1])->toMatchArray($student_A2->toArray())
        ->and($response['data'][2])->toMatchArray($student_B->toArray());

    // TESTING GUARDIAN_EMAIL FILTER
    // Return student A1 and student A2
    $response = $this->masterStudentRepository->getAllPaginated([
        'guardian_email' => $guardian_A->email,
        'order_by' => ['id' => 'asc'],
    ])->toArray();

    expect($response['data'])->toHaveCount(2)
        ->and($response['data'][0])->toMatchArray($student_A1->toArray())
        ->and($response['data'][1])->toMatchArray($student_A2->toArray());

    // Return student B
    $response = $this->masterStudentRepository->getAllPaginated([
        'guardian_email' => $guardian_B->email,
        'order_by' => ['id' => 'asc'],
    ])->toArray();

    expect($response['data'])->toHaveCount(2)
        ->and($response['data'][0])->toMatchArray($student_A2->toArray())
        ->and($response['data'][1])->toMatchArray($student_B->toArray());

    // Return student A1, student A2, student B (testing wildcard filter)
    $response = $this->masterStudentRepository->getAllPaginated([
        'guardian_email' => 'father',
        'order_by' => ['id' => 'asc'],
    ])->toArray();

    expect($response['data'])->toHaveCount(3)
        ->and($response['data'][0])->toMatchArray($student_A1->toArray())
        ->and($response['data'][1])->toMatchArray($student_A2->toArray())
        ->and($response['data'][2])->toMatchArray($student_B->toArray());

});

test('getFirstByStudentNumberOrCardNumber()', function () {
    /**
     * Student AA, BB has active card
     * Student CC has inactive card
     */
    $studentAA = Student::factory()->create([
        'student_number' => 'AA-11'
    ]);

    Card::factory()->create([
        'card_number' => 'AA1',
        'card_number2' => 'AA2',
        'card_number3' => 'AA3',
        'userable_type' => Student::class,
        'userable_id' => $studentAA->id,
        'status' => CardStatus::ACTIVE->value,
    ]);

    // ================================================ filter by student_number
    $response = $this->masterStudentRepository->getFirstByStudentNumberOrCardNumber('AA-11');

    expect($response)->toBeInstanceOf(Student::class)
        ->and($response->id)->toEqual($studentAA->id);

    // ================================================ filter by card_number
    $response = $this->masterStudentRepository->getFirstByStudentNumberOrCardNumber('AA1');

    expect($response)->toBeInstanceOf(Student::class)
        ->and($response->id)->toEqual($studentAA->id);

    // ======================================================================================

    $studentBB = Student::factory()->create([
        'student_number' => 'BB-22'
    ]);

    Card::factory()->create([
        'card_number' => 'BB1',
        'card_number2' => 'BB2',
        'card_number3' => 'BB3',
        'userable_type' => Student::class,
        'userable_id' => $studentBB->id,
        'status' => CardStatus::ACTIVE->value,
    ]);

    // ================================================ filter by student_number
    $response = $this->masterStudentRepository->getFirstByStudentNumberOrCardNumber('BB-22');

    expect($response)->toBeInstanceOf(Student::class)
        ->and($response->id)->toEqual($studentBB->id);

    // ================================================ filter by card_number
    $response = $this->masterStudentRepository->getFirstByStudentNumberOrCardNumber('BB1');

    expect($response)->toBeInstanceOf(Student::class)
        ->and($response->id)->toEqual($studentBB->id);

    // ======================================================================================

    $studentCC = Student::factory()->create([
        'student_number' => 'CC-33'
    ]);

    Card::factory()->create([ // inactive
        'card_number' => 'CC1',
        'card_number2' => 'CC2',
        'card_number3' => 'CC3',
        'userable_type' => Student::class,
        'userable_id' => $studentCC->id,
        'status' => CardStatus::INACTIVE->value,
    ]);

    // ================================================ filter by student_number - even tho card inactive, using student number is still legit
    $response = $this->masterStudentRepository->getFirstByStudentNumberOrCardNumber('CC-33');

    expect($response)->toBeInstanceOf(Student::class)
        ->and($response->id)->toEqual($studentCC->id);

    // ================================================ filter by card_number - card inactive
    $response = $this->masterStudentRepository->getFirstByStudentNumberOrCardNumber('CC1');

    expect($response)->toBeNull();
});

test('getHostelBoarderListData()', function () {
    $sem1 = SemesterSetting::factory()->create([
        'name' => 'Semester 1'
    ]);

    $sem2 = SemesterSetting::factory()->create([
        'name' => 'Semester 2'
    ]);

    $grade1 = Grade::factory()->create([
        'name->en' => 'Junior 1'
    ]);

    $grade2 = Grade::factory()->create([
        'name->en' => 'Junior 2'
    ]);

    $classes = ClassModel::factory(2)->create(new Sequence(
        [
            'name->en' => 'J111',
            'type' => ClassType::PRIMARY,
            'grade_id' => $grade1->id
        ],
        [
            'name->en' => 'J211',
            'type' => ClassType::PRIMARY,
            'grade_id' => $grade2->id
        ],
    ));

    $semester_classes = SemesterClass::factory(4)->create(new Sequence(
        [
            'semester_setting_id' => $sem1->id,
            'class_id' => $classes[0]->id // Grade 1
        ],
        [
            'semester_setting_id' => $sem1->id,
            'class_id' => $classes[1]->id // Grade 2
        ],
        [
            'semester_setting_id' => $sem2->id,
            'class_id' => $classes[0]->id // Grade 1
        ],
        [
            'semester_setting_id' => $sem2->id,
            'class_id' => $classes[1]->id // Grade 2
        ],
    ));

    $students[0] = Student::factory()->create([
        'is_hostel' => true,
        'student_number' => 'A120',
        'name->en' => 'Aaron'
    ]);
    $students[1] = Student::factory()->create([
        'is_hostel' => true,
        'student_number' => 'A121',
        'name->en' => 'Baby'
    ]);

    StudentClass::factory()->create([
        'semester_setting_id' => $sem1->id,
        'semester_class_id' => $semester_classes[0]->id, // Grade 1
        'student_id' => $students[0]->id,
        'class_enter_date' => now()->toDateString(),
    ]);

    StudentClass::factory()->create([
        'semester_setting_id' => $sem2->id,
        'semester_class_id' => $semester_classes[3]->id, // Grade 2
        'student_id' => $students[0]->id,
        'class_enter_date' => now()->subDays(5)->toDateString()
    ]);

    StudentClass::factory()->create([
        'semester_setting_id' => $sem1->id,
        'semester_class_id' => $semester_classes[1]->id, // Grade 2
        'student_id' => $students[1]->id,
        'class_enter_date' => now()->subDays(10)->toDateString()
    ]);

    $hostel_room1 = HostelRoom::factory()->create([
        'name' => 'Room 1'
    ]);

    $hostel_room2 = HostelRoom::factory()->create([
        'name' => 'Room 2'
    ]);

    $hostel_bed1 = HostelRoomBed::factory()->create([
        'name' => 'Bed 1',
        'hostel_room_id' => $hostel_room1->id,
    ]);

    $hostel_bed2 = HostelRoomBed::factory()->create([
        'name' => 'Bed 2',
        'hostel_room_id' => $hostel_room2->id,
    ]);

    HostelBedAssignment::factory()->create([
        'hostel_room_bed_id' => $hostel_bed1->id,
        'assignable_id' => $students[0]->id,
        'assignable_type' => Student::class,
        'start_date' => now()->subYear()->toDateString(),
        'end_date' => now()->toDateString()
    ]);

    HostelBedAssignment::factory()->create([
        'hostel_room_bed_id' => $hostel_bed2->id,
        'assignable_id' => $students[0]->id,
        'assignable_type' => Student::class,
        'end_date' => null
    ]);

    HostelBedAssignment::factory()->create([
        'hostel_room_bed_id' => $hostel_bed2->id,
        'assignable_id' => $students[1]->id,
        'assignable_type' => Student::class,
        'start_date' => now()->subYear()->toDateString(),
        'end_date' => now()->toDateString()
    ]);

    // Filter by semester_setting_id = sem1
    $response = $this->masterStudentRepository->getHostelBoarderListData([
        'semester_setting_id' => $sem1->id,
    ]);

    expect($response)->toHaveCount(2)->sequence(
        fn($item) => $item->toHaveKey('latest_primary_class.semester_setting_id', $students[1]->latestPrimaryClass->semester_setting_id)->toHaveKey('latest_primary_class.semester_class_id', $students[1]->latestPrimaryClass->semester_class_id),
        fn($item) => $item->toHaveKey('latest_primary_class.semester_setting_id', $students[0]->latestPrimaryClass->semester_setting_id)->toHaveKey('latest_primary_class.semester_class_id', $students[0]->latestPrimaryClass->semester_class_id),
    )
        // Test eager loading
        ->and($response[0])->toHaveKeys([
            'id', 'student_number', 'name'
        ])
        ->and($response[0]->relationLoaded('activeHostelBedAssignments'))->toBeTrue()
        ->and($response[0]->relationLoaded('latestPrimaryClass'))->toBeTrue();

    // Filter by semester_setting_id = sem2
    $response = $this->masterStudentRepository->getHostelBoarderListData([
        'semester_setting_id' => $sem2->id,
    ]);

    expect($response)->toHaveCount(1)->sequence(
        fn($item) => $item->toHaveKey('latest_primary_class.semester_setting_id', $sem2->id)->toHaveKey('latest_primary_class.semester_class.id', $semester_classes[3]->id),
    );

    // Filter by semester_setting_id = sem1, grade_id = grade1
    $response = $this->masterStudentRepository->getHostelBoarderListData([
        'semester_setting_id' => $sem1->id,
        'grade_id' => $grade1->id,
    ]);

    expect($response->toArray())->toHaveCount(1)
        ->and($response[0])
        ->toHaveKey('latest_primary_class.semester_class.id', $semester_classes[0]->id)
        ->toHaveKey('name.en', $students[0]->name);

    // Filter by semester_setting_id = sem1, grade_id = grade2
    $response = $this->masterStudentRepository->getHostelBoarderListData([
        'semester_setting_id' => $sem1->id,
        'grade_id' => $grade2->id,
    ]);

    expect($response)->toHaveCount(1)
        ->and($response[0])
        ->toHaveKey('latest_primary_class.semester_class.id', $semester_classes[1]->id)
        ->toHaveKey('name.en', $students[1]->name);

    // Filter by semester_setting_id = sem2, semester_class_id = semester_classes[3]
    $response = $this->masterStudentRepository->getHostelBoarderListData([
        'semester_setting_id' => $sem2->id,
        'semester_class_id' => $semester_classes[3]->id,
    ]);

    expect($response)->toHaveCount(1)
        ->and($response[0])
        ->toHaveKey('latest_primary_class.semester_class.id', $semester_classes[3]->id)
        ->toHaveKey('name.en', $students[0]->name);
});

test('getHostelBoardersData()', function () {
    $sem1 = SemesterSetting::factory()->create([
        'name' => 'Semester 1'
    ]);

    $sem2 = SemesterSetting::factory()->create([
        'name' => 'Semester 2'
    ]);

    $grade1 = Grade::factory()->create([
        'name->en' => 'Junior 1'
    ]);

    $grade2 = Grade::factory()->create([
        'name->en' => 'Junior 2'
    ]);

    $classes = ClassModel::factory(2)->create(new Sequence(
        [
            'name->en' => 'J111',
            'type' => ClassType::PRIMARY,
            'grade_id' => $grade1->id
        ],
        [
            'name->en' => 'J211',
            'type' => ClassType::PRIMARY,
            'grade_id' => $grade2->id
        ],
    ));

    $semester_classes = SemesterClass::factory(4)->create(new Sequence(
        [
            'semester_setting_id' => $sem1->id,
            'class_id' => $classes[0]->id // Grade 1
        ],
        [
            'semester_setting_id' => $sem1->id,
            'class_id' => $classes[1]->id // Grade 2
        ],
        [
            'semester_setting_id' => $sem2->id,
            'class_id' => $classes[0]->id // Grade 1
        ],
        [
            'semester_setting_id' => $sem2->id,
            'class_id' => $classes[1]->id // Grade 2
        ],
    ));

    $students = Student::factory()->count(4)->create([
        'is_hostel' => true
    ]);

    $user_guardians = User::factory(2)->withGuardian()->create(); // 2 guardians for $students[0]

    $gs1 = GuardianStudent::factory()->create([
        'guardian_id' => $user_guardians[0]->guardian->id,
        'type' => GuardianType::FATHER->value,
        'studenable_type' => Student::class,
        'studenable_id' => $students[0]->id,
        'is_direct_dependant' => true,
    ]);

    $gs2 = GuardianStudent::factory()->create([
        'guardian_id' => $user_guardians[1]->guardian->id,
        'type' => GuardianType::MOTHER->value,
        'studenable_type' => Student::class,
        'studenable_id' => $students[0]->id,
        'is_direct_dependant' => true,
    ]);

    $user_guardians2 = User::factory(2)->withGuardian()->create(); // 2 guardians for $students[1]

    $gs3 = GuardianStudent::factory()->create([
        'guardian_id' => $user_guardians2[0]->guardian->id,
        'type' => GuardianType::FATHER->value,
        'studenable_type' => Student::class,
        'studenable_id' => $students[1]->id,
        'is_direct_dependant' => true,
    ]);

    $gs4 = GuardianStudent::factory()->create([
        'guardian_id' => $user_guardians2[1]->guardian->id,
        'type' => GuardianType::MOTHER->value,
        'studenable_type' => Student::class,
        'studenable_id' => $students[1]->id,
        'is_direct_dependant' => true,
    ]);

    StudentClass::factory()->create([
        'semester_setting_id' => $sem1->id,
        'semester_class_id' => $semester_classes[0]->id, // Grade 1
        'student_id' => $students[0]->id,
        'class_enter_date' => now()->toDateString()
    ]);

    StudentClass::factory()->create([
        'semester_setting_id' => $sem1->id,
        'semester_class_id' => $semester_classes[0]->id, // Grade 1
        'student_id' => $students[1]->id,
        'class_enter_date' => now()->toDateString()
    ]);

    StudentClass::factory()->create([
        'semester_setting_id' => $sem1->id,
        'semester_class_id' => $semester_classes[0]->id, // Grade 1
        'student_id' => $students[2]->id,
        'class_enter_date' => now()->toDateString()
    ]);

    StudentClass::factory()->create([
        'semester_setting_id' => $sem1->id,
        'semester_class_id' => $semester_classes[0]->id, // Grade 1
        'student_id' => $students[3]->id,
        'class_enter_date' => now()->toDateString()
    ]);

    $hostel_block = HostelBlock::factory()->create([
        'name->en' => 'Block A',
    ]);

    $hostel_room1 = HostelRoom::factory()->create([
        'name' => 'Room 1',
        'hostel_block_id' => $hostel_block->id,
    ]);

    $hostel_bed2 = HostelRoomBed::factory()->create([
        'name' => 'Bed 2',
        'hostel_room_id' => $hostel_room1->id,
    ]);

    $hostel_bed1 = HostelRoomBed::factory()->create([
        'name' => 'Bed 1',
        'hostel_room_id' => $hostel_room1->id,
    ]);


    $bed_assignment1 = HostelBedAssignment::factory()->create([
        'hostel_room_bed_id' => $hostel_bed1->id,
        'assignable_id' => $students[0]->id,
        'assignable_type' => Student::class,
        'start_date' => now()->toDateString()
    ]);

    $bed_assignment2 = HostelBedAssignment::factory()->create([
        'hostel_room_bed_id' => $hostel_bed2->id,
        'assignable_id' => $students[1]->id,
        'assignable_type' => Student::class,
        'start_date' => now()->toDateString()
    ]);

    // Filter by semester_setting_id = sem1, with all guardians
    $response = $this->masterStudentRepository->getHostelBoardersData([
        'semester_setting_id' => $sem1->id,
        'all_guardians' => true,
    ])->keyBy('student_id');

    // student[0] has 2 guardians, bed assignment in bed1
    expect($response[$students[0]->id])->toEqual([
        'student_id' => $students[0]->id,
        'student_number' => $students[0]->student_number,
        'student_name' => $students[0]->getTranslations('name'),
        'student_nric' => $students[0]->nric,
        'student_address' => $students[0]->address,
        'student_phone_number' => $students[0]->phone_number,
        'student_date_of_birth' => $students[0]->date_of_birth,
        'start_date' => $bed_assignment1->start_date->toDateString(),
        'end_date' => $bed_assignment1->end_date?->toDateString(),
        'block_name' => $hostel_block->getTranslation('name', app()->getLocale()),
        'bed_name' => $hostel_bed1->name,
        'room_name' => $hostel_room1->name,
        'class_name' => $classes[0]->getTranslations('name'),
        'grade_name' => $grade1->getTranslations('name'),
        'guardians' => [
            [
                'id' => $user_guardians[0]->guardian->id,
                'name' => $user_guardians[0]->guardian->getTranslations('name'),
                'nric' => $user_guardians[0]->guardian->nric,
                'phone_number' => $user_guardians[0]->guardian->phone_number,
                'pivot' => $gs1->toArray(),
            ],
            [
                'id' => $user_guardians[1]->guardian->id,
                'name' => $user_guardians[1]->guardian->getTranslations('name'),
                'nric' => $user_guardians[1]->guardian->nric,
                'phone_number' => $user_guardians[1]->guardian->phone_number,
                'pivot' => $gs2->toArray(),
            ]
        ]
    ]);

    // student[1] has 2 guardians, bed assignment in bed2
    expect($response[$students[1]->id])->toEqual([
        'student_id' => $students[1]->id,
        'student_number' => $students[1]->student_number,
        'student_name' => $students[1]->getTranslations('name'),
        'student_nric' => $students[1]->nric,
        'student_address' => $students[1]->address,
        'student_phone_number' => $students[1]->phone_number,
        'student_date_of_birth' => $students[1]->date_of_birth,
        'start_date' => $bed_assignment2->start_date->toDateString(),
        'end_date' => $bed_assignment2->end_date?->toDateString(),
        'block_name' => $hostel_block->getTranslation('name', app()->getLocale()),
        'bed_name' => $hostel_bed2->name,
        'room_name' => $hostel_room1->name,
        'class_name' => $classes[0]->getTranslations('name'),
        'grade_name' => $grade1->getTranslations('name'),
        'guardians' => [
            [
                'id' => $user_guardians2[0]->guardian->id,
                'name' => $user_guardians2[0]->guardian->getTranslations('name'),
                'nric' => $user_guardians2[0]->guardian->nric,
                'phone_number' => $user_guardians2[0]->guardian->phone_number,
                'pivot' => $gs3->toArray(),
            ],
            [
                'id' => $user_guardians2[1]->guardian->id,
                'name' => $user_guardians2[1]->guardian->getTranslations('name'),
                'nric' => $user_guardians2[1]->guardian->nric,
                'phone_number' => $user_guardians2[1]->guardian->phone_number,
                'pivot' => $gs4->toArray(),
            ]
        ]
    ]);

    // student[2] has no guardians, has no bed assignment
    expect($response[$students[2]->id])->toEqual([
        'student_id' => $students[2]->id,
        'student_number' => $students[2]->student_number,
        'student_name' => $students[2]->getTranslations('name'),
        'student_nric' => $students[2]->nric,
        'student_address' => $students[2]->address,
        'student_phone_number' => $students[2]->phone_number,
        'student_date_of_birth' => $students[2]->date_of_birth,
        'start_date' => null,
        'end_date' => null,
        'block_name' => null,
        'bed_name' => null,
        'room_name' => null,
        'class_name' => $classes[0]->getTranslations('name'),
        'grade_name' => $grade1->getTranslations('name'),
        'guardians' => []
    ]);

    // student[3] has no guardians, has no bed assignment
    expect($response[$students[3]->id])->toEqual([
        'student_id' => $students[3]->id,
        'student_number' => $students[3]->student_number,
        'student_name' => $students[3]->getTranslations('name'),
        'student_nric' => $students[3]->nric,
        'student_address' => $students[3]->address,
        'student_phone_number' => $students[3]->phone_number,
        'student_date_of_birth' => $students[3]->date_of_birth,
        'start_date' => null,
        'end_date' => null,
        'block_name' => null,
        'bed_name' => null,
        'room_name' => null,
        'class_name' => $classes[0]->getTranslations('name'),
        'grade_name' => $grade1->getTranslations('name'),
        'guardians' => []
    ]);


    // Filter by semester_setting_id = sem1, with all guardians == false, only 1 guardian
    $response = $this->masterStudentRepository->getHostelBoardersData([
        'semester_setting_id' => $sem1->id,
        'all_guardians' => false,
    ])->keyBy('student_id');

    expect($response)->toHaveCount(4);

    // student[0] has only 2 guardians but only see 1 guardian, bed assignment in bed1
    expect($response[$students[0]->id])->toEqual([
        'student_id' => $students[0]->id,
        'student_number' => $students[0]->student_number,
        'student_name' => $students[0]->getTranslations('name'),
        'student_nric' => $students[0]->nric,
        'student_address' => $students[0]->address,
        'student_phone_number' => $students[0]->phone_number,
        'student_date_of_birth' => $students[0]->date_of_birth,
        'start_date' => $bed_assignment1->start_date->toDateString(),
        'end_date' => $bed_assignment1->end_date?->toDateString(),
        'block_name' => $hostel_block->getTranslation('name', app()->getLocale()),
        'bed_name' => $hostel_bed1->name,
        'room_name' => $hostel_room1->name,
        'class_name' => $classes[0]->getTranslations('name'),
        'grade_name' => $grade1->getTranslations('name'),
        'guardians' => [
            [
                'id' => $user_guardians[0]->guardian->id,
                'name' => $user_guardians[0]->guardian->getTranslations('name'),
                'nric' => $user_guardians[0]->guardian->nric,
                'phone_number' => $user_guardians[0]->guardian->phone_number,
                'pivot' => $gs1->toArray(),
            ],
        ]
    ]);

    // student[1] has only 2 guardians but only see 1 guardian, bed assignment in bed1
    expect($response[$students[1]->id])->toEqual([
        'student_id' => $students[1]->id,
        'student_number' => $students[1]->student_number,
        'student_name' => $students[1]->getTranslations('name'),
        'student_nric' => $students[1]->nric,
        'student_address' => $students[1]->address,
        'student_phone_number' => $students[1]->phone_number,
        'student_date_of_birth' => $students[1]->date_of_birth,
        'start_date' => $bed_assignment2->start_date->toDateString(),
        'end_date' => $bed_assignment2->end_date?->toDateString(),
        'block_name' => $hostel_block->getTranslation('name', app()->getLocale()),
        'bed_name' => $hostel_bed2->name,
        'room_name' => $hostel_room1->name,
        'class_name' => $classes[0]->getTranslations('name'),
        'grade_name' => $grade1->getTranslations('name'),
        'guardians' => [
            [
                'id' => $user_guardians2[0]->guardian->id,
                'name' => $user_guardians2[0]->guardian->getTranslations('name'),
                'nric' => $user_guardians2[0]->guardian->nric,
                'phone_number' => $user_guardians2[0]->guardian->phone_number,
                'pivot' => $gs3->toArray(),
            ],
        ]
    ]);

    /**
     *
     * all 4 students reassigned to new class in sem1, expect to see these class
     */
    StudentClass::factory()->create([
        'semester_setting_id' => $sem1->id,
        'semester_class_id' => $semester_classes[1]->id, // Grade 2
        'student_id' => $students[0]->id,
        'class_enter_date' => now()->addDay()->toDateString()
    ]);

    StudentClass::factory()->create([
        'semester_setting_id' => $sem1->id,
        'semester_class_id' => $semester_classes[1]->id, // Grade 2
        'student_id' => $students[1]->id,
        'class_enter_date' => now()->addDay()->toDateString()
    ]);

    StudentClass::factory()->create([
        'semester_setting_id' => $sem1->id,
        'semester_class_id' => $semester_classes[1]->id, // Grade 2
        'student_id' => $students[2]->id,
        'class_enter_date' => now()->addDay()->toDateString()
    ]);

    StudentClass::factory()->create([
        'semester_setting_id' => $sem1->id,
        'semester_class_id' => $semester_classes[1]->id, // Grade 2
        'student_id' => $students[3]->id,
        'class_enter_date' => now()->addDay()->toDateString()
    ]);

    // Filter by semester_setting_id = sem1
    $response = $this->masterStudentRepository->getHostelBoardersData([
        'semester_setting_id' => $sem1->id,
        'all_guardians' => false,
    ])->keyBy('student_id');

    // check all students have same class
    foreach ($response as $response_data) {
        expect($response_data['class_name'])->toEqual($classes[1]->getTranslations('name'));
        expect($response_data['grade_name'])->toEqual($grade2->getTranslations('name'));
    }


    /**
     *
     * only 1 student assigned to new class in sem2, all other 3 students class data wont appear
     */
    StudentClass::factory()->create([
        'semester_setting_id' => $sem2->id,
        'semester_class_id' => $semester_classes[2]->id, // Grade 2
        'student_id' => $students[0]->id,
        'class_enter_date' => now()->addDay()->toDateString()
    ]);


    // Filter by semester_setting_id = sem2, only 1 guardian
    $response = $this->masterStudentRepository->getHostelBoardersData([
        'semester_setting_id' => $sem2->id,
        'all_guardians' => false,
    ])->keyBy('student_id');


    expect($response)->toHaveCount(4);

    expect($response[$students[0]->id])->toEqual([
        'student_id' => $students[0]->id,
        'student_number' => $students[0]->student_number,
        'student_name' => $students[0]->getTranslations('name'),
        'student_nric' => $students[0]->nric,
        'student_address' => $students[0]->address,
        'student_phone_number' => $students[0]->phone_number,
        'student_date_of_birth' => $students[0]->date_of_birth,
        'start_date' => $bed_assignment1->start_date->toDateString(),
        'end_date' => $bed_assignment1->end_date?->toDateString(),
        'block_name' => $hostel_block->getTranslation('name', app()->getLocale()),
        'bed_name' => $hostel_bed1->name,
        'room_name' => $hostel_room1->name,
        'class_name' => $classes[0]->getTranslations('name'),
        'grade_name' => $grade1->getTranslations('name'),
        'guardians' => [
            [
                'id' => $user_guardians[0]->guardian->id,
                'name' => $user_guardians[0]->guardian->getTranslations('name'),
                'nric' => $user_guardians[0]->guardian->nric,
                'phone_number' => $user_guardians[0]->guardian->phone_number,
                'pivot' => $gs1->toArray(),
            ],
        ]
    ]);

    expect($response[$students[1]->id])->toEqual([
        'student_id' => $students[1]->id,
        'student_number' => $students[1]->student_number,
        'student_name' => $students[1]->getTranslations('name'),
        'student_nric' => $students[1]->nric,
        'student_address' => $students[1]->address,
        'student_phone_number' => $students[1]->phone_number,
        'student_date_of_birth' => $students[1]->date_of_birth,
        'start_date' => $bed_assignment2->start_date->toDateString(),
        'end_date' => $bed_assignment2->end_date?->toDateString(),
        'block_name' => $hostel_block->getTranslation('name', app()->getLocale()),
        'bed_name' => $hostel_bed2->name,
        'room_name' => $hostel_room1->name,
        'class_name' => null,
        'grade_name' => null,
        'guardians' => [
            [
                'id' => $user_guardians2[0]->guardian->id,
                'name' => $user_guardians2[0]->guardian->getTranslations('name'),
                'nric' => $user_guardians2[0]->guardian->nric,
                'phone_number' => $user_guardians2[0]->guardian->phone_number,
                'pivot' => $gs3->toArray(),
            ],
        ]
    ]);

    expect($response[$students[2]->id])->toEqual([
        'student_id' => $students[2]->id,
        'student_number' => $students[2]->student_number,
        'student_name' => $students[2]->getTranslations('name'),
        'student_nric' => $students[2]->nric,
        'student_address' => $students[2]->address,
        'student_phone_number' => $students[2]->phone_number,
        'student_date_of_birth' => $students[2]->date_of_birth,
        'start_date' => null,
        'end_date' => null,
        'block_name' => null,
        'bed_name' => null,
        'room_name' => null,
        'class_name' => null,
        'grade_name' => null,
        'guardians' => []
    ]);

    expect($response[$students[3]->id])->toEqual([
        'student_id' => $students[3]->id,
        'student_number' => $students[3]->student_number,
        'student_name' => $students[3]->getTranslations('name'),
        'student_nric' => $students[3]->nric,
        'student_address' => $students[3]->address,
        'student_phone_number' => $students[3]->phone_number,
        'student_date_of_birth' => $students[3]->date_of_birth,
        'start_date' => null,
        'end_date' => null,
        'block_name' => null,
        'bed_name' => null,
        'room_name' => null,
        'class_name' => null,
        'grade_name' => null,
        'guardians' => []
    ]);

    /**
     *
     * NO GUARDIANs
     *
     */
    // Filter by semester_setting_id = sem1, NO GUARDIAN
    $response = $this->masterStudentRepository->getHostelBoardersData([
        'semester_setting_id' => $sem1->id,
        'no_guardians' => true,
    ])->keyBy('student_id');


    // student[0] has 2 guardians, bed assignment in bed1
    expect($response[$students[0]->id])->toEqual([
        'student_id' => $students[0]->id,
        'student_number' => $students[0]->student_number,
        'student_name' => $students[0]->getTranslations('name'),
        'student_nric' => $students[0]->nric,
        'student_address' => $students[0]->address,
        'student_phone_number' => $students[0]->phone_number,
        'student_date_of_birth' => $students[0]->date_of_birth,
        'start_date' => $bed_assignment1->start_date->toDateString(),
        'end_date' => $bed_assignment1->end_date?->toDateString(),
        'block_name' => $hostel_block->getTranslation('name', app()->getLocale()),
        'bed_name' => $hostel_bed1->name,
        'room_name' => $hostel_room1->name,
        'class_name' => $classes[1]->getTranslations('name'), // new class
        'grade_name' => $grade2->getTranslations('name'), // new grade
        'guardians' => [],
    ]);

    // student[1] has no guardians, bed assignment in bed2
    expect($response[$students[1]->id])->toEqual([
        'student_id' => $students[1]->id,
        'student_number' => $students[1]->student_number,
        'student_name' => $students[1]->getTranslations('name'),
        'student_nric' => $students[1]->nric,
        'student_address' => $students[1]->address,
        'student_phone_number' => $students[1]->phone_number,
        'student_date_of_birth' => $students[1]->date_of_birth,
        'start_date' => $bed_assignment2->start_date->toDateString(),
        'end_date' => $bed_assignment2->end_date?->toDateString(),
        'block_name' => $hostel_block->getTranslation('name', app()->getLocale()),
        'bed_name' => $hostel_bed2->name,
        'room_name' => $hostel_room1->name,
        'class_name' => $classes[1]->getTranslations('name'), // new class
        'grade_name' => $grade2->getTranslations('name'), // new grade
        'guardians' => [],
    ]);

    // student[2] has no guardians, has no bed assignment
    expect($response[$students[2]->id])->toEqual([
        'student_id' => $students[2]->id,
        'student_number' => $students[2]->student_number,
        'student_name' => $students[2]->getTranslations('name'),
        'student_nric' => $students[2]->nric,
        'student_address' => $students[2]->address,
        'student_phone_number' => $students[2]->phone_number,
        'student_date_of_birth' => $students[2]->date_of_birth,
        'start_date' => null,
        'end_date' => null,
        'block_name' => null,
        'bed_name' => null,
        'room_name' => null,
        'class_name' => $classes[1]->getTranslations('name'), // new class
        'grade_name' => $grade2->getTranslations('name'), // new grade
        'guardians' => [],
    ]);

    // student[3] has no guardians, has no bed assignment
    expect($response[$students[3]->id])->toEqual([
        'student_id' => $students[3]->id,
        'student_number' => $students[3]->student_number,
        'student_name' => $students[3]->getTranslations('name'),
        'student_nric' => $students[3]->nric,
        'student_address' => $students[3]->address,
        'student_phone_number' => $students[3]->phone_number,
        'student_date_of_birth' => $students[3]->date_of_birth,
        'start_date' => null,
        'end_date' => null,
        'block_name' => null,
        'bed_name' => null,
        'room_name' => null,
        'class_name' => $classes[1]->getTranslations('name'), // new class
        'grade_name' => $grade2->getTranslations('name'), // new grade
        'guardians' => [],
    ]);
});

test('getHostelBoardersStayBackData()', function () {
    $sem1 = SemesterSetting::factory()->create([
        'name' => '2025 Semester 1'
    ]);

    $grades = Grade::factory(2)->create(new Sequence(
        [
            'name->en' => 'Junior 1'
        ],
        [
            'name->en' => 'Junior 2'
        ],
    ));

    $classes = ClassModel::factory(2)->create(new Sequence(
        [
            'name->en' => 'J111',
            'type' => ClassType::PRIMARY,
            'grade_id' => $grades[0]->id
        ],
        [
            'name->en' => 'J211',
            'type' => ClassType::PRIMARY,
            'grade_id' => $grades[1]->id
        ],
    ));

    $semester_classes = SemesterClass::factory(4)->create(new Sequence(
        [
            'semester_setting_id' => $sem1->id,
            'class_id' => $classes[0]->id // Grade 1
        ],
        [
            'semester_setting_id' => $sem1->id,
            'class_id' => $classes[1]->id // Grade 2
        ],
    ));


    $students = Student::factory(4)->create(new Sequence(
        [
            'name->en' => 'John Jones',
            'gender' => Gender::MALE->value,
            'is_hostel' => true,

        ],
        [
            'name->en' => 'Dwayne',
            'gender' => Gender::MALE->value,
            'is_hostel' => true,

        ],
        [
            'name->en' => 'Karen',
            'gender' => Gender::FEMALE->value,
            'is_hostel' => true,

        ],
        [
            'name->en' => 'Kaley',
            'gender' => Gender::FEMALE->value,
            'is_hostel' => true,

        ],
    ));

    StudentClass::factory()->create([
        'semester_setting_id' => $sem1->id,
        'semester_class_id' => $semester_classes[0]->id, // Grade 1
        'student_id' => $students[0]->id,
        'class_enter_date' => now()->toDateString()
    ]);

    StudentClass::factory()->create([
        'semester_setting_id' => $sem1->id,
        'semester_class_id' => $semester_classes[0]->id, // Grade 1
        'student_id' => $students[1]->id,
        'class_enter_date' => now()->toDateString()
    ]);

    StudentClass::factory()->create([
        'semester_setting_id' => $sem1->id,
        'semester_class_id' => $semester_classes[1]->id, // Grade 2
        'student_id' => $students[2]->id,
        'class_enter_date' => now()->toDateString()
    ]);

    StudentClass::factory()->create([
        'semester_setting_id' => $sem1->id,
        'semester_class_id' => $semester_classes[1]->id, // Grade 2
        'student_id' => $students[3]->id,
        'class_enter_date' => now()->toDateString()
    ]);

    $hostel_block = HostelBlock::factory()->create([
        'name->en' => 'Block A',
    ]);

    $hostel_rooms = HostelRoom::factory(2)->create(new Sequence(
        [
            'name' => 'Room 1',
            'hostel_block_id' => $hostel_block->id,
        ],
        [
            'name' => 'Room 2',
            'hostel_block_id' => $hostel_block->id,
        ],
    ));


    $hostel_beds = HostelRoomBed::factory(4)->create(new Sequence(
        [
            'name' => 'Bed 1',
            'hostel_room_id' => $hostel_rooms[1]->id,
        ],
        [
            'name' => 'Bed 2',
            'hostel_room_id' => $hostel_rooms[1]->id,
        ],
        [
            'name' => 'Bed 3',
            'hostel_room_id' => $hostel_rooms[0]->id,
        ],
        [
            'name' => 'Bed 4',
            'hostel_room_id' => $hostel_rooms[0]->id,
        ],
    ));


    $hostel_bed_assignments = HostelBedAssignment::factory(4)->create(new Sequence(
        [
            'hostel_room_bed_id' => $hostel_beds[0]->id,
            'assignable_id' => $students[0]->id,
            'assignable_type' => Student::class,
            'start_date' => now()->toDateString(),
        ],
        [
            'hostel_room_bed_id' => $hostel_beds[1]->id,
            'assignable_id' => $students[1]->id,
            'assignable_type' => Student::class,
            'start_date' => now()->toDateString(),
        ],
        [
            'hostel_room_bed_id' => $hostel_beds[2]->id,
            'assignable_id' => $students[2]->id,
            'assignable_type' => Student::class,
            'start_date' => now()->toDateString(),
        ],
        [
            'hostel_room_bed_id' => $hostel_beds[3]->id,
            'assignable_id' => $students[3]->id,
            'assignable_type' => Student::class,
            'start_date' => now()->toDateString(),
        ],
    ));


    $response = $this->masterStudentRepository->getHostelBoardersStayBackData()->toArray();

    expect($response)->toEqual([
        [
            'block_name' => $hostel_block->getTranslation('name', app()->getLocale()),
            'room_name' => $hostel_rooms[0]->name,
            'bed_name' => $hostel_beds[2]->name,
            'student_number' => $students[2]->student_number,
            'student_name' => $students[2]->getTranslations('name'),
            'class_name' => $classes[1]->getTranslations('name'),
            'grade_name' => $grades[1]->getTranslations('name'),
        ],
        [
            'block_name' => $hostel_block->getTranslation('name', app()->getLocale()),
            'room_name' => $hostel_rooms[0]->name,
            'bed_name' => $hostel_beds[3]->name,
            'student_number' => $students[3]->student_number,
            'student_name' => $students[3]->getTranslations('name'),
            'class_name' => $classes[1]->getTranslations('name'),
            'grade_name' => $grades[1]->getTranslations('name'),
        ],
        [
            'block_name' => $hostel_block->getTranslation('name', app()->getLocale()),
            'room_name' => $hostel_rooms[1]->name,
            'bed_name' => $hostel_beds[0]->name,
            'student_number' => $students[0]->student_number,
            'student_name' => $students[0]->getTranslations('name'),
            'class_name' => $classes[0]->getTranslations('name'),
            'grade_name' => $grades[0]->getTranslations('name'),
        ],
        [
            'block_name' => $hostel_block->getTranslation('name', app()->getLocale()),
            'room_name' => $hostel_rooms[1]->name,
            'bed_name' => $hostel_beds[1]->name,
            'student_number' => $students[1]->student_number,
            'student_name' => $students[1]->getTranslations('name'),
            'class_name' => $classes[0]->getTranslations('name'),
            'grade_name' => $grades[0]->getTranslations('name'),
        ],
    ]);


    /**
     *  $students[3] is currently outing
     */
    $student_3_outing = HostelInOutRecord::factory()->onlyCheckout()->create([
        'type' => HostelInOutType::OUTING->value,
        'student_id' => $students[3]->id,
        'hostel_room_bed_id' => $hostel_beds[3]->id,
        'reason' => 'go out',
    ]);

    $student_2_home = HostelInOutRecord::factory()->onlyCheckout()->create([
        'type' => HostelInOutType::HOME->value,
        'student_id' => $students[2]->id,
        'hostel_room_bed_id' => $hostel_beds[2]->id,
        'reason' => 'go home',
    ]);

    // $students[3] is currently outing
    // 25/6/2025 update: include outing students in hostel stay back report
    $response = $this->masterStudentRepository->getHostelBoardersStayBackData()->toArray();

    expect($response)->toEqual([
        // $student[2] is home, should be excluded in hostel stay back report
//        [
//            'block_name' => $hostel_block->getTranslation('name', app()->getLocale()),
//            'room_name' => $hostel_rooms[0]->name,
//            'bed_name' => $hostel_beds[2]->name,
//            'student_number' => $students[2]->student_number,
//            'student_name' => $students[2]->getTranslations('name'),
//            'class_name' => $classes[1]->getTranslations('name'),
//            'grade_name' => $grades[1]->getTranslations('name'),
//        ],
        [
            'block_name' => $hostel_block->getTranslation('name', app()->getLocale()),
            'room_name' => $hostel_rooms[0]->name,
            'bed_name' => $hostel_beds[3]->name,
            'student_number' => $students[3]->student_number,
            'student_name' => $students[3]->getTranslations('name'),
            'class_name' => $classes[1]->getTranslations('name'),
            'grade_name' => $grades[1]->getTranslations('name'),
        ],
        [
            'block_name' => $hostel_block->getTranslation('name', app()->getLocale()),
            'room_name' => $hostel_rooms[1]->name,
            'bed_name' => $hostel_beds[0]->name,
            'student_number' => $students[0]->student_number,
            'student_name' => $students[0]->getTranslations('name'),
            'class_name' => $classes[0]->getTranslations('name'),
            'grade_name' => $grades[0]->getTranslations('name'),
        ],
        [
            'block_name' => $hostel_block->getTranslation('name', app()->getLocale()),
            'room_name' => $hostel_rooms[1]->name,
            'bed_name' => $hostel_beds[1]->name,
            'student_number' => $students[1]->student_number,
            'student_name' => $students[1]->getTranslations('name'),
            'class_name' => $classes[0]->getTranslations('name'),
            'grade_name' => $grades[0]->getTranslations('name'),
        ],
    ]);


    /**
     *  $students[3] is back in hostel
     */
    $student_3_outing->update(['check_in_datetime' => now()]);

    /**W
     *  filter by MALE students only
     */
    $payload = [
        'gender' => Gender::MALE->value,
    ];

    $response = $this->masterStudentRepository->getHostelBoardersStayBackData($payload);

    expect($response)
        ->toHaveKey('0.student_name.en', 'John Jones')
        ->toHaveKey('1.student_name.en', 'Dwayne');

    /**
     *  filter by FEMALE students only
     */
    $payload = [
        'gender' => Gender::FEMALE->value,
    ];

    $response = $this->masterStudentRepository->getHostelBoardersStayBackData($payload);

    expect($response)
        // This student went home, should not be included
//        ->toHaveKey('0.student_name.en', 'Karen')
        ->toHaveKey('0.student_name.en', 'Kaley');
});

test('getStudentsInGradeAndSemester', function () {

    $grade1 = Grade::factory()->create();
    $grade2 = Grade::factory()->create();

    $student1 = Student::factory()->create([
        'student_number' => 'A',
    ]);
    $student2 = Student::factory()->create([
        'student_number' => 'B',
    ]);
    $student3 = Student::factory()->create([
        'student_number' => 'C',
    ]);

    $course = Course::factory()->uec()->create();

    $semester_setting1 = SemesterSetting::factory()->create([
        'course_id' => $course->id,
        'name' => '2025 Semester 1',
    ]);
    $semester_setting2 = SemesterSetting::factory()->create([
        'course_id' => $course->id,
        'name' => '2025 Semester 2',
    ]);

    $class1 = ClassModel::factory()->create([
        'grade_id' => $grade1->id,
    ]);
    $class2 = ClassModel::factory()->create([
        'grade_id' => $grade2->id,
    ]);

    $semester_class1 = SemesterClass::factory()->create([
        'semester_setting_id' => $semester_setting1->id,
        'class_id' => $class1->id,
    ]);
    $semester_class2 = SemesterClass::factory()->create([
        'semester_setting_id' => $semester_setting1->id,
        'class_id' => $class2->id,
    ]);
    $semester_class3 = SemesterClass::factory()->create([
        'semester_setting_id' => $semester_setting2->id,
        'class_id' => $class1->id,
    ]);
    $semester_class4 = SemesterClass::factory()->create([
        'semester_setting_id' => $semester_setting2->id,
        'class_id' => $class2->id,
    ]);

    // class 1 is grade 1, class 2 is grade 2
    // in sem 1, student 1 & 2 in class 1, student 3 in class 2
    StudentClass::factory()->create([
        'semester_setting_id' => $semester_setting1->id,
        'semester_class_id' => $semester_class1->id,
        'student_id' => $student1->id,
        'class_type' => ClassType::PRIMARY
    ]);
    StudentClass::factory()->create([
        'semester_setting_id' => $semester_setting1->id,
        'semester_class_id' => $semester_class1->id,
        'student_id' => $student2->id,
        'class_type' => ClassType::PRIMARY
    ]);
    StudentClass::factory()->create([
        'semester_setting_id' => $semester_setting1->id,
        'semester_class_id' => $semester_class2->id,
        'student_id' => $student3->id,
        'class_type' => ClassType::PRIMARY
    ]);

    // in sem 2, student 1 & 2 & 3 in class 2, no one in class 1
    StudentClass::factory()->create([
        'semester_setting_id' => $semester_setting2->id,
        'semester_class_id' => $semester_class4->id,
        'student_id' => $student1->id,
        'class_type' => ClassType::PRIMARY
    ]);
    StudentClass::factory()->create([
        'semester_setting_id' => $semester_setting2->id,
        'semester_class_id' => $semester_class4->id,
        'student_id' => $student2->id,
        'class_type' => ClassType::PRIMARY
    ]);
    StudentClass::factory()->create([
        'semester_setting_id' => $semester_setting2->id,
        'semester_class_id' => $semester_class4->id,
        'student_id' => $student3->id,
        'class_type' => ClassType::PRIMARY
    ]);

    // sem 1, grade 1
    $students = app()->make(StudentRepository::class)->getStudentsInGradeAndSemester($grade1, $semester_setting1);

    expect($students)->toHaveCount(2)
        ->and($students->pluck('id'))->toContain($student1->id, $student2->id)->not()->toContain($student3->id);

    // sem 1, grade 2
    $students = app()->make(StudentRepository::class)->getStudentsInGradeAndSemester($grade2, $semester_setting1);

    expect($students)->toHaveCount(1)
        ->and($students->pluck('id'))->toContain($student3->id)->not()->toContain($student1->id, $student2->id);

    // sem 2, grade 1
    $students = app()->make(StudentRepository::class)->getStudentsInGradeAndSemester($grade1, $semester_setting2);

    expect($students)->toHaveCount(0);

    // sem 2, grade 2
    $students = app()->make(StudentRepository::class)->getStudentsInGradeAndSemester($grade2, $semester_setting2);

    expect($students)->toHaveCount(3)
        ->and($students->pluck('id'))->toContain($student1->id, $student2->id, $student3->id);
});


test('student getPrimaryClassOfSemester', function () {

    $student = Student::factory()->create([]);

    $course = Course::factory()->uec()->create();

    $grade1 = Grade::factory()->create([
        'name->en' => 'J1',
    ]);
    $grade2 = Grade::factory()->create([
        'name->en' => 'J2',
    ]);

    $sem12024 = SemesterSetting::factory()->create([
        'course_id' => $course->id,
        'name' => '2024 Semester 1',
        'is_current_semester' => false,
    ]);
    $sem22024 = SemesterSetting::factory()->create([
        'course_id' => $course->id,
        'name' => '2024 Semester 2',
        'is_current_semester' => false,
    ]);
    $sem12025 = SemesterSetting::factory()->create([
        'course_id' => $course->id,
        'name' => '2025 Semester 1',
        'is_current_semester' => false,
    ]);

    $class1 = ClassModel::factory()->create([
        'grade_id' => $grade1->id,
    ]);
    $class2 = ClassModel::factory()->create([
        'grade_id' => $grade1->id,
    ]);
    $class3 = ClassModel::factory()->create([
        'grade_id' => $grade2->id,
    ]);

    $semester_class_1 = SemesterClass::factory()->create([
        'semester_setting_id' => $sem12024->id,
        'class_id' => $class1->id,
        'is_active' => true,
    ]);
    $semester_class_2 = SemesterClass::factory()->create([
        'semester_setting_id' => $sem12024->id,
        'class_id' => $class2->id,
        'is_active' => true,
    ]);
    $semester_class_3 = SemesterClass::factory()->create([
        'semester_setting_id' => $sem12025->id,
        'class_id' => $class3->id,
        'is_active' => true,
    ]);

    $student_class_1 = StudentClass::factory()->create([
        'semester_setting_id' => $sem12024->id,
        'semester_class_id' => $semester_class_1->id,
        'student_id' => $student->id,
        'class_enter_date' => '2024-01-01',
        'class_leave_date' => '2024-05-31',
        'is_active' => false,
    ]);

    // sem 2 2024 change class
    $student_class_2 = StudentClass::factory()->create([
        'semester_setting_id' => $sem22024->id,
        'semester_class_id' => $semester_class_2->id,
        'student_id' => $student->id,
        'class_enter_date' => '2024-06-01',
        'class_leave_date' => '2024-08-31',
        'is_active' => false,
    ]);
    // sem 2 2024 change class
    $student_class_3 = StudentClass::factory()->create([
        'semester_setting_id' => $sem22024->id,
        'semester_class_id' => $semester_class_2->id,
        'student_id' => $student->id,
        'class_enter_date' => '2024-09-01',
        'class_leave_date' => '2024-12-31',
        'is_active' => false,
    ]);

    $student_class_4 = StudentClass::factory()->create([
        'semester_setting_id' => $sem12025->id,
        'semester_class_id' => $semester_class_3->id,
        'student_id' => $student->id,
        'class_enter_date' => '2025-01-01',
        'is_active' => true,
    ]);

    expect($student->getPrimaryClassOfSemester($sem12024->id)->id)->toBe($student_class_1->id)
        ->and($student->getPrimaryClassOfSemester($sem22024->id)->id)->toBe($student_class_3->id)
        ->and($student->getPrimaryClassOfSemester($sem12025->id)->id)->toBe($student_class_4->id);

});

test('studentStatisticReportBySemesterData()', function () {
    Grade::truncate();
    $semester_setting = SemesterSetting::factory()->create([
        'name' => '2025 Semester 1'
    ]);

    $semester_setting2 = SemesterSetting::factory()->create([
        'name' => '2025 Semester 2'
    ]);

    $grades = Grade::factory(3)->create(new Sequence(
        [
            'name->en' => 'Junior 1',
            'name->zh' => '初一',
            'sequence' => 2,
        ],
        [
            'name->en' => 'Junior 2',
            'name->zh' => '初二',
            'sequence' => 1,
        ],
        [
            'name->en' => 'Junior 3',
            'name->zh' => '初三',
            'sequence' => 0,
        ],
    ));

    $active_society_class = ClassModel::factory()->create([
        'type' => ClassType::SOCIETY,
        'is_active' => true,
        'grade_id' => $grades[0]->id,
        'name->en' => 'Drama Club',
        'name->zh' => '戏剧',
        'code' => '002',
    ]);

    $active_society_class2 = ClassModel::factory()->create([
        'type' => ClassType::SOCIETY,
        'is_active' => true,
        'grade_id' => $grades[0]->id,
        'name->en' => 'Art Club',
        'name->zh' => '美术',
        'code' => '001',
    ]);

    $inactive_society_class = ClassModel::factory()->create([
        'type' => ClassType::SOCIETY,
        'is_active' => false,
        'grade_id' => $grades[0]->id,
    ]);

    $semester_classes_society = SemesterClass::factory(4)->create(new Sequence(
        [
            'semester_setting_id' => $semester_setting->id,
            'class_id' => $active_society_class->id
        ],
        [
            'semester_setting_id' => $semester_setting->id,
            'class_id' => $active_society_class2->id
        ],
        [
            'semester_setting_id' => $semester_setting->id,
            'class_id' => $inactive_society_class->id
        ],
        [
            'semester_setting_id' => $semester_setting2->id,
            'class_id' => $active_society_class->id
        ],
    ));

    $primary_class = ClassModel::factory()->create([
        'type' => ClassType::PRIMARY,
        'grade_id' => $grades[1]->id,
    ]);

    $primary_class2 = ClassModel::factory()->create([
        'type' => ClassType::PRIMARY,
        'grade_id' => $grades[2]->id,
    ]);

    $semester_classes_primary = SemesterClass::factory(2)->create(new Sequence(
        [
            'semester_setting_id' => $semester_setting->id,
            'class_id' => $primary_class->id
        ],
        [
            'semester_setting_id' => $semester_setting->id,
            'class_id' => $primary_class2->id
        ],
    ));

    // first society semester class (4 Male, 3 Female, 3 Junior two, 4 Junior three) + 1 Male that already left school + 1 Male that already left the society class
    for ($i = 1; $i <= 4; $i++) {
        $student = Student::factory()->create([
            'gender' => Gender::MALE->value,
            'admission_grade_id' => $semester_classes_primary[0]->classModel->grade_id,
        ]);
        StudentClass::factory()->create([
            'semester_setting_id' => $semester_setting->id,
            'semester_class_id' => $semester_classes_society[0]->id,
            'student_id' => $student->id,
            'class_enter_date' => now()->toDateString()
        ]);
        // 2 Junior 2, 2 Junior 3
        if ($i <= 2) {
            StudentClass::factory()->create([
                'semester_setting_id' => $semester_setting->id,
                'semester_class_id' => $semester_classes_primary[0]->id, // Junior 2
                'student_id' => $student->id,
                'class_enter_date' => now()->toDateString()
            ]);
        } else {
            StudentClass::factory()->create([
                'semester_setting_id' => $semester_setting->id,
                'semester_class_id' => $semester_classes_primary[1]->id, // Junior 3
                'student_id' => $student->id,
                'class_enter_date' => now()->toDateString()
            ]);
        }
    }
    for ($i = 1; $i <= 3; $i++) {
        $student = Student::factory()->create([
            'gender' => Gender::FEMALE->value,
            'admission_grade_id' => $semester_classes_primary[0]->classModel->grade_id,
        ]);
        StudentClass::factory()->create([
            'semester_setting_id' => $semester_setting->id,
            'semester_class_id' => $semester_classes_society[0]->id,
            'student_id' => $student->id,
            'class_enter_date' => now()->toDateString()
        ]);
        // 1 Junior 2, 2 Junior 3
        if ($i <= 1) {
            StudentClass::factory()->create([
                'semester_setting_id' => $semester_setting->id,
                'semester_class_id' => $semester_classes_primary[0]->id, // Junior 2
                'student_id' => $student->id,
                'class_enter_date' => now()->toDateString()
            ]);
        } else {
            StudentClass::factory()->create([
                'semester_setting_id' => $semester_setting->id,
                'semester_class_id' => $semester_classes_primary[1]->id, // Junior 3
                'student_id' => $student->id,
                'class_enter_date' => now()->toDateString()
            ]);
        }
    }
    // student that already left school
    $student = Student::factory()->create([
        'gender' => Gender::MALE->value,
        'admission_grade_id' => $semester_classes_primary[0]->classModel->grade_id,
    ]);
    StudentClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'semester_class_id' => $semester_classes_society[0]->id,
        'student_id' => $student->id,
        'class_enter_date' => now()->toDateString()
    ]);
    StudentClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'semester_class_id' => $semester_classes_primary[0]->id, // Junior 2
        'student_id' => $student->id,
        'class_leave_date' => now()->toDateString() // class_leave_date not null
    ]);

    // student that already left society class
    $student = Student::factory()->create([
        'gender' => Gender::MALE->value,
        'admission_grade_id' => $semester_classes_primary[0]->classModel->grade_id,
    ]);
    StudentClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'semester_class_id' => $semester_classes_society[0]->id,
        'student_id' => $student->id,
        'class_enter_date' => now()->toDateString(),
        'is_active' => false,
        'is_latest_class_in_semester' => false,
    ]);
    StudentClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'semester_class_id' => $semester_classes_primary[0]->id, // Junior 2
        'student_id' => $student->id,
        'class_leave_date' => null,
        'is_latest_class_in_semester' => true,
    ]);

    // second society semester class (2 Male, 4 Female, 4 Junior 2, 2 Junior 3)
    for ($i = 1; $i <= 2; $i++) {
        $student = Student::factory()->create([
            'gender' => Gender::MALE->value,
            'admission_grade_id' => $semester_classes_primary[1]->classModel->grade_id,
        ]);
        StudentClass::factory()->create([
            'semester_setting_id' => $semester_setting->id,
            'semester_class_id' => $semester_classes_society[1]->id,
            'student_id' => $student->id,
            'class_enter_date' => now()->toDateString()
        ]);
        // 1 Junior 2, 1 Junior 3
        if ($i <= 1) {
            StudentClass::factory()->create([
                'semester_setting_id' => $semester_setting->id,
                'semester_class_id' => $semester_classes_primary[0]->id, // Junior 2
                'student_id' => $student->id,
                'class_enter_date' => now()->toDateString()
            ]);
        } else {
            StudentClass::factory()->create([
                'semester_setting_id' => $semester_setting->id,
                'semester_class_id' => $semester_classes_primary[1]->id, // Junior 3
                'student_id' => $student->id,
                'class_enter_date' => now()->toDateString()
            ]);
        }
    }
    for ($i = 1; $i <= 4; $i++) {
        $student = Student::factory()->create([
            'gender' => Gender::FEMALE->value,
            'admission_grade_id' => $semester_classes_primary[1]->classModel->grade_id,
        ]);
        StudentClass::factory()->create([
            'semester_setting_id' => $semester_setting->id,
            'semester_class_id' => $semester_classes_society[1]->id,
            'student_id' => $student->id,
            'class_enter_date' => now()->toDateString(),

        ]);
        // 3 Junior 2, 1 Junior 3
        if ($i <= 3) {
            StudentClass::factory()->create([
                'semester_setting_id' => $semester_setting->id,
                'semester_class_id' => $semester_classes_primary[0]->id, // Junior 2
                'student_id' => $student->id,
                'class_enter_date' => now()->toDateString()
            ]);
        } else {
            StudentClass::factory()->create([
                'semester_setting_id' => $semester_setting->id,
                'semester_class_id' => $semester_classes_primary[1]->id, // Junior 3
                'student_id' => $student->id,
                'class_enter_date' => now()->toDateString()
            ]);
        }
    }

    // third society semester class (inactive)
    $student = Student::factory()->create([
        'gender' => Gender::MALE->value,
        'admission_grade_id' => $semester_classes_primary[0]->classModel->grade_id,
    ]);
    StudentClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'semester_class_id' => $semester_classes_society[2]->id,
        'student_id' => $student->id,
        'class_enter_date' => now()->toDateString()
    ]);
    StudentClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'semester_class_id' => $semester_classes_primary[0]->id, // Junior 2
        'student_id' => $student->id,
        'class_enter_date' => now()->toDateString()
    ]);

    // fourth society semester class (other semester setting)
    $student = Student::factory()->create([
        'gender' => Gender::MALE->value,
        'admission_grade_id' => $semester_classes_primary[0]->classModel->grade_id,
    ]);
    StudentClass::factory()->create([
        'semester_setting_id' => $semester_setting2->id,
        'semester_class_id' => $semester_classes_society[3]->id,
        'student_id' => $student->id,
        'class_enter_date' => now()->toDateString()
    ]);
    StudentClass::factory()->create([
        'semester_setting_id' => $semester_setting2->id,
        'semester_class_id' => $semester_classes_primary[0]->id, // Junior 2
        'student_id' => $student->id,
        'class_enter_date' => now()->toDateString()
    ]);

    LatestPrimaryClassBySemesterSettingView::refreshViewTable(false);

    app()->setLocale('zh');
    $response = $this->masterStudentRepository->studentStatisticReportBySemesterData($semester_setting);

    // dd($response);

    expect($response)->toMatchArray([
        "header" => [
            "班级编号",
            "学会",
            "男",
            "女",
            "总数",
            "初一",
            "初二",
            "初三",
        ],
        "body" => [
            $semester_classes_society[1]->id => [
                "001",
                "美术",
                2,
                4,
                6,
                0,
                4,
                2,
            ],
            $semester_classes_society[0]->id => [
                "002",
                "戏剧",
                4,
                3,
                7,
                0,
                3,
                4,
            ],
        ],
    ]);

    app()->setLocale('en');
    $response = $this->masterStudentRepository->studentStatisticReportBySemesterData($semester_setting);

    expect($response)->toMatchArray([
        "header" => [
            "Class Code",
            "Society",
            "Male",
            "Female",
            "Total Count",
            "Junior 1",
            "Junior 2",
            "Junior 3",
        ],
        "body" => [
            $semester_classes_society[1]->id => [
                "001",
                "Art Club",
                2,
                4,
                6,
                0,
                4,
                2,
            ],
            $semester_classes_society[0]->id => [
                "002",
                "Drama Club",
                4,
                3,
                7,
                0,
                3,
                4,
            ],
        ],
    ]);

    $semester_setting_with_empty_data = SemesterSetting::factory()->create();
    $response = $this->masterStudentRepository->studentStatisticReportBySemesterData($semester_setting_with_empty_data);

    expect($response)->toMatchArray([
        "header" => [
            "Class Code",
            "Society",
            "Male",
            "Female",
            "Total Count",
            "Junior 1",
            "Junior 2",
            "Junior 3",
        ],
        "body" => [],
    ]);
});

test('transferredStudentListByAdmissionYearData()', function () {
    // create transferred student with multiple classes
    $semester_setting = SemesterSetting::factory()->create();
    $primary_class_junior = ClassModel::factory()->create([
        'type' => ClassType::PRIMARY,
        'name->en' => 'J11',
        'name->zh' => '一年1班',
    ]);
    $semester_class_junior_1 = SemesterClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'class_id' => $primary_class_junior->id
    ]);
    $semester_setting2 = SemesterSetting::factory()->create();
    $primary_class_junior_2 = ClassModel::factory()->create([
        'type' => ClassType::PRIMARY,
        'name->en' => 'J21',
        'name->zh' => '二年1班',
    ]);
    $semester_class_junior_2 = SemesterClass::factory()->create([
        'semester_setting_id' => $semester_setting2->id,
        'class_id' => $primary_class_junior_2->id
    ]);
    $student = Student::factory()->create([
        'admission_year' => '2024',
        'admission_type' => StudentAdmissionType::TRANSFERRED->value,
        'name->en' => 'student A',
        'name->zh' => '学生 A',
        'student_number' => '0001',
    ]);

    StudentClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'semester_class_id' => $semester_class_junior_1->id,
        'student_id' => $student->id,
        'class_enter_date' => '2025-01-05',
    ]);
    StudentClass::factory()->create([
        'semester_setting_id' => $semester_setting2->id,
        'semester_class_id' => $semester_class_junior_2->id,
        'student_id' => $student->id,
        'class_enter_date' => '2025-08-05',
    ]);

    // create non transferred student
    $student2 = Student::factory()->create([
        'admission_year' => '2024',
        'admission_type' => StudentAdmissionType::NEW->value,
        'name->en' => 'student B',
        'name->zh' => '学生 B',
        'student_number' => '0002',
    ]);
    StudentClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'semester_class_id' => $semester_class_junior_1->id,
        'student_id' => $student2->id,
        'class_enter_date' => '2025-01-05',
    ]);

    // create transferred student but admission year not 2024
    $student3 = Student::factory()->create([
        'admission_year' => '2023',
        'admission_type' => StudentAdmissionType::TRANSFERRED->value,
        'name->en' => 'student C',
        'name->zh' => '学生 C',
        'student_number' => '0003',
    ]);
    StudentClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'semester_class_id' => $semester_class_junior_1->id,
        'student_id' => $student3->id,
        'class_enter_date' => '2025-01-05',
    ]);

    // create transferred student without class
    $student3 = Student::factory()->create([
        'admission_year' => '2024',
        'admission_type' => StudentAdmissionType::TRANSFERRED->value,
        'name->en' => 'student D',
        'name->zh' => '学生 D',
        'student_number' => '0004',
    ]);

    LatestPrimaryClassBySemesterSettingView::refreshViewTable(false);
    app()->setLocale('zh');
    $data = $this->masterStudentRepository->transferredStudentListByAdmissionYearData('2024');
    expect($data)->toEqual([
        [
            "student_name" => [
                "en" => "student A",
                "zh" => "学生 A",
            ],
            "student_number" => "0001",
            "class_name" => "J11 - 一年1班",
        ],
        [
            "student_name" => [
                "en" => "student D",
                "zh" => "学生 D",
            ],
            "student_number" => "0004",
            "class_name" => "-",
        ],
    ]);
});

test('studentAnalysisReportBySemesterGroupByGradeData()', function () {
    Grade::truncate();
    $semester_setting = SemesterSetting::factory()->create([
        'name' => '2025 Semester 1'
    ]);
    $semester_setting2 = SemesterSetting::factory()->create([
        'name' => '2025 Semester 1'
    ]);

    $grades = Grade::factory(4)->create(new Sequence(
        [
            'name->en' => 'Junior 1',
            'name->zh' => '初一',
            'sequence' => 3,
        ],
        [
            'name->en' => 'Junior 2',
            'name->zh' => '初二',
            'sequence' => 2,
        ],
        [
            'name->en' => 'Junior 3',
            'name->zh' => '初三',
            'sequence' => 1,
        ],
        // Grade without student
        [
            'name->en' => 'Senior 1',
            'name->zh' => '高一',
            'sequence' => 0,
        ],
    ));
    $primary_class_junior_1 = ClassModel::factory()->create([
        'type' => ClassType::PRIMARY,
        'grade_id' => $grades[0]->id,
        'name->en' => 'J12',
        'name->zh' => '一年2班',
    ]);
    $second_primary_class_junior_1 = ClassModel::factory()->create([
        'type' => ClassType::PRIMARY,
        'grade_id' => $grades[0]->id,
        'name->en' => 'J11',
        'name->zh' => '一年1班',
    ]);
    $primary_class_junior_2 = ClassModel::factory()->create([
        'type' => ClassType::PRIMARY,
        'grade_id' => $grades[1]->id,
        'name->en' => 'J21',
        'name->zh' => '二年1班',
    ]);
    // class without student
    $second_primary_class_junior_2 = ClassModel::factory()->create([
        'type' => ClassType::PRIMARY,
        'grade_id' => $grades[1]->id,
        'name->en' => 'J22',
        'name->zh' => '二年2班',
    ]);
    $primary_class_junior_3 = ClassModel::factory()->create([
        'type' => ClassType::PRIMARY,
        'grade_id' => $grades[2]->id,
        'name->en' => 'J31',
        'name->zh' => '三年1班',
    ]);
    $semester_class_junior_1 = SemesterClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'class_id' => $primary_class_junior_1->id
    ]);
    $second_semester_class_junior_1 = SemesterClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'class_id' => $second_primary_class_junior_1->id
    ]);
    $semester_class_junior_2 = SemesterClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'class_id' => $primary_class_junior_2->id
    ]);
    // class without student
    $second_semester_class_junior_2 = SemesterClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'class_id' => $second_primary_class_junior_2->id
    ]);
    $semester_class_junior_3 = SemesterClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'class_id' => $primary_class_junior_3->id
    ]);
    $semester_class_junior_3_other_semester_setting = SemesterClass::factory()->create([
        'semester_setting_id' => $semester_setting2->id,
        'class_id' => $primary_class_junior_3->id
    ]);
    // Junior 1
    // total = 63
    // total male -> 15 + 15 = 30
    // total female -> 16 + 17 = 33
    // total hostel -> 5 + 4 + 6 + 7 = 22
    // total non hostel -> 10 + 12 + 9 + 10 = 41

    // Junior 2
    // total = 3
    // total male -> 1
    // total female -> 2
    // total hostel -> 2
    // total non hostel -> 1

    // Junior 3
    // total = 15
    // total male -> 5
    // total female -> 10
    // total hostel -> 5
    // total non hostel -> 10

    // sum
    // total = 63 + 3 + 15 = 81
    // total male -> 30 + 1 + 5  = 36
    // total female -> 33 + 2 + 10 = 45
    // total hostel -> 22 + 2 + 5 = 29
    // total non hostel -> 41 + 1 + 10 = 52
    $data_to_be_created = [
        [
            'semester_setting' => $semester_setting,
            'semester_class' => $semester_class_junior_1,
            // 15
            'male_students_hostel' => 5,
            'male_students_non_hostel' => 10,
            // 16
            'female_students_hostel' => 4,
            'female_students_non_hostel' => 12,
        ],
        [
            'semester_setting' => $semester_setting,
            'semester_class' => $second_semester_class_junior_1,
            // 15
            'male_students_hostel' => 6,
            'male_students_non_hostel' => 9,
            // 17
            'female_students_hostel' => 7,
            'female_students_non_hostel' => 10,
        ],
        [
            'semester_setting' => $semester_setting,
            'semester_class' => $semester_class_junior_2,
            'male_students_hostel' => 0,
            'male_students_non_hostel' => 1,
            'female_students_hostel' => 2,
            'female_students_non_hostel' => 0,
        ],
        [
            'semester_setting' => $semester_setting,
            'semester_class' => $semester_class_junior_3,
            'male_students_hostel' => 5,
            'male_students_non_hostel' => 0,
            'female_students_hostel' => 0,
            'female_students_non_hostel' => 10,
        ],
        [
            'semester_setting' => $semester_setting2,
            'semester_class' => $semester_class_junior_3_other_semester_setting,
            'male_students_hostel' => 2,
            'male_students_non_hostel' => 2,
            'female_students_hostel' => 2,
            'female_students_non_hostel' => 2,
        ],
    ];

    foreach ($data_to_be_created as $data) {
        for ($i = 1; $i <= $data['male_students_hostel']; $i++) {
            StudentClass::factory()->create([
                'semester_setting_id' => $data['semester_setting']->id,
                'semester_class_id' => $data['semester_class']->id,
                'student_id' => Student::factory()->create([
                    'gender' => Gender::MALE->value,
                    'is_hostel' => true,
                    'admission_grade_id' => $data['semester_class']->classModel->grade_id,
                ])->id,
            ]);
        }
        for ($i = 1; $i <= $data['male_students_non_hostel']; $i++) {
            StudentClass::factory()->create([
                'semester_setting_id' => $data['semester_setting']->id,
                'semester_class_id' => $data['semester_class']->id,
                'student_id' => Student::factory()->create([
                    'gender' => Gender::MALE->value,
                    'is_hostel' => false,
                    'admission_grade_id' => $data['semester_class']->classModel->grade_id,
                ])->id,
            ]);
        }
        for ($i = 1; $i <= $data['female_students_hostel']; $i++) {
            StudentClass::factory()->create([
                'semester_setting_id' => $data['semester_setting']->id,
                'semester_class_id' => $data['semester_class']->id,
                'student_id' => Student::factory()->create([
                    'gender' => Gender::FEMALE->value,
                    'is_hostel' => true,
                    'admission_grade_id' => $data['semester_class']->classModel->grade_id,
                ])->id,
            ]);
        }
        for ($i = 1; $i <= $data['female_students_non_hostel']; $i++) {
            StudentClass::factory()->create([
                'semester_setting_id' => $data['semester_setting']->id,
                'semester_class_id' => $data['semester_class']->id,
                'student_id' => Student::factory()->create([
                    'gender' => Gender::FEMALE->value,
                    'is_hostel' => false,
                    'admission_grade_id' => $data['semester_class']->classModel->grade_id,
                ])->id,
            ]);
        }
    }

    LatestPrimaryClassBySemesterSettingView::refreshViewTable(false);

    app()->setLocale('zh');
    $response = $this->masterStudentRepository->studentAnalysisReportBySemesterGroupByGradeData($semester_setting);
    expect($response)->toEqual([
        "summary" => [
            "total_students" => 81,
            "total_male_students" => 36,
            "total_female_students" => 45,
            "total_hostel_students" => 29,
            "total_non_hostel_students" => 52,
        ],
        "summary_by_grade" => [
            $grades[0]->id => [
                "grade_name" => "初一",
                "total_students" => 63,
                "total_male_students" => 30,
                "total_female_students" => 33,
                "total_hostel_students" => 22,
                "total_non_hostel_students" => 41,
                "classes_data" => [
                    [
                        "class_name" => "一年1班",
                        "total_students" => 32,
                        "total_male_students" => 15,
                        "total_female_students" => 17,
                        "total_hostel_students" => 13,
                        "total_non_hostel_students" => 19,
                    ],
                    [
                        "class_name" => "一年2班",
                        "total_students" => 31,
                        "total_male_students" => 15,
                        "total_female_students" => 16,
                        "total_hostel_students" => 9,
                        "total_non_hostel_students" => 22,
                    ]
                ],
            ],
            $grades[1]->id => [
                "grade_name" => "初二",
                "total_students" => 3,
                "total_male_students" => 1,
                "total_female_students" => 2,
                "total_hostel_students" => 2,
                "total_non_hostel_students" => 1,
                "classes_data" => [
                    [
                        "class_name" => "二年1班",
                        "total_students" => 3,
                        "total_male_students" => 1,
                        "total_female_students" => 2,
                        "total_hostel_students" => 2,
                        "total_non_hostel_students" => 1,
                    ],
                    [
                        "class_name" => "二年2班",
                        "total_students" => 0,
                        "total_male_students" => 0,
                        "total_female_students" => 0,
                        "total_hostel_students" => 0,
                        "total_non_hostel_students" => 0,
                    ]
                ],
            ],
            $grades[2]->id => [
                "grade_name" => "初三",
                "total_students" => 15,
                "total_male_students" => 5,
                "total_female_students" => 10,
                "total_hostel_students" => 5,
                "total_non_hostel_students" => 10,
                "classes_data" => [
                    [
                        "class_name" => "三年1班",
                        "total_students" => 15,
                        "total_male_students" => 5,
                        "total_female_students" => 10,
                        "total_hostel_students" => 5,
                        "total_non_hostel_students" => 10,
                    ],
                ],
            ],
            $grades[3]->id => [
                "grade_name" => "高一",
                "total_students" => 0,
                "total_male_students" => 0,
                "total_female_students" => 0,
                "total_hostel_students" => 0,
                "total_non_hostel_students" => 0,
                "classes_data" => [],
            ],
        ],
    ]);

    app()->setLocale('en');
    $response = $this->masterStudentRepository->studentAnalysisReportBySemesterGroupByGradeData($semester_setting);
    expect($response)->toEqual([
        "summary" => [
            "total_students" => 81,
            "total_male_students" => 36,
            "total_female_students" => 45,
            "total_hostel_students" => 29,
            "total_non_hostel_students" => 52,
        ],
        "summary_by_grade" => [
            $grades[0]->id => [
                "grade_name" => "Junior 1",
                "total_students" => 63,
                "total_male_students" => 30,
                "total_female_students" => 33,
                "total_hostel_students" => 22,
                "total_non_hostel_students" => 41,
                "classes_data" => [
                    [
                        "class_name" => "J11",
                        "total_students" => 32,
                        "total_male_students" => 15,
                        "total_female_students" => 17,
                        "total_hostel_students" => 13,
                        "total_non_hostel_students" => 19,
                    ],
                    [
                        "class_name" => "J12",
                        "total_students" => 31,
                        "total_male_students" => 15,
                        "total_female_students" => 16,
                        "total_hostel_students" => 9,
                        "total_non_hostel_students" => 22,
                    ]
                ],
            ],
            $grades[1]->id => [
                "grade_name" => "Junior 2",
                "total_students" => 3,
                "total_male_students" => 1,
                "total_female_students" => 2,
                "total_hostel_students" => 2,
                "total_non_hostel_students" => 1,
                "classes_data" => [
                    [
                        "class_name" => "J21",
                        "total_students" => 3,
                        "total_male_students" => 1,
                        "total_female_students" => 2,
                        "total_hostel_students" => 2,
                        "total_non_hostel_students" => 1,
                    ],
                    [
                        "class_name" => "J22",
                        "total_students" => 0,
                        "total_male_students" => 0,
                        "total_female_students" => 0,
                        "total_hostel_students" => 0,
                        "total_non_hostel_students" => 0,
                    ]
                ],
            ],
            $grades[2]->id => [
                "grade_name" => "Junior 3",
                "total_students" => 15,
                "total_male_students" => 5,
                "total_female_students" => 10,
                "total_hostel_students" => 5,
                "total_non_hostel_students" => 10,
                "classes_data" => [
                    [
                        "class_name" => "J31",
                        "total_students" => 15,
                        "total_male_students" => 5,
                        "total_female_students" => 10,
                        "total_hostel_students" => 5,
                        "total_non_hostel_students" => 10,
                    ],
                ],
            ],
            $grades[3]->id => [
                "grade_name" => "Senior 1",
                "total_students" => 0,
                "total_male_students" => 0,
                "total_female_students" => 0,
                "total_hostel_students" => 0,
                "total_non_hostel_students" => 0,
                "classes_data" => [],
            ],
        ],
    ]);
});

test('studentMeritAndExceptionalPerformanceData()', function () {
    //Student 1
    $student1 = Student::factory()->create([
        'name->en' => 'Student 1'
    ]);

    $merit_setting = MeritDemeritSetting::factory()->create([
        'name' => 'Merit Setting',
        'type' => MeritDemeritType::MERIT->value,
    ]);

    $demerit_setting = MeritDemeritSetting::factory()->create([
        'name' => 'Demerit Setting',
        'type' => MeritDemeritType::DEMERIT->value,
    ]);

    $merit_reward_punishment = RewardPunishment::factory()->create([
        'name' => 'Merit Record',
        'conduct_marks' => 1,
    ]);

    $demerit_reward_punishment = RewardPunishment::factory()->create([
        'name' => 'Demerit Record',
        'conduct_marks' => -1,
    ]);

    $merit_reward_punishment->meritDemeritSettings()->attach($merit_setting->id);

    $demerit_reward_punishment->meritDemeritSettings()->attach($demerit_setting->id);

    $student1_reward_punishment_records = RewardPunishmentRecord::factory(3)
        ->state(new Sequence(
            [
                'student_id' => $student1->id,
                'reward_punishment_id' => $merit_reward_punishment->id,
                'status' => RewardPunishmentRecordStatus::POSTED,
                'conduct_marks' => 1,
                'display_in_report_card' => true,
                'date' => '2024-01-01'
            ],
            [
                'student_id' => $student1->id,
                'reward_punishment_id' => $merit_reward_punishment->id,
                'status' => RewardPunishmentRecordStatus::POSTED,
                'conduct_marks' => 1,
                'display_in_report_card' => true,
                'date' => '2024-01-03'
            ],
            [
                'student_id' => $student1->id,
                'reward_punishment_id' => $merit_reward_punishment->id,
                'status' => RewardPunishmentRecordStatus::POSTED,
                'conduct_marks' => 1,
                'display_in_report_card' => true,
                'date' => '2024-01-02'
            ],
        ))->create();

    $demerit_records = RewardPunishmentRecord::factory(3)
        ->create([
            'student_id' => $student1->id,
            'reward_punishment_id' => $demerit_reward_punishment->id,
            'status' => RewardPunishmentRecordStatus::POSTED,
            'conduct_marks' => -1,
            'display_in_report_card' => true
        ]);

    $merit_records = RewardPunishmentRecord::factory(3)->create([
        'student_id' => $student1->id,
        'reward_punishment_id' => $merit_reward_punishment->id,
        'status' => RewardPunishmentRecordStatus::POSTED,
        'conduct_marks' => 1,
        'display_in_report_card' => false
    ]);

    RewardPunishmentRecord::factory(3)->create([
        'student_id' => $student1->id,
        'reward_punishment_id' => $merit_reward_punishment->id,
        'status' => RewardPunishmentRecordStatus::DRAFT,
        'conduct_marks' => 1,
        'display_in_report_card' => true
    ]);

    CompetitionRecord::factory(2)->create([
        'student_id' => $student1->id,
    ]);


    //Student 2
    $student2 = Student::factory()->create([
        'name->en' => 'Student 2'
    ]);

    RewardPunishmentRecord::factory(3)->create([
        'student_id' => $student2->id,
        'reward_punishment_id' => $merit_reward_punishment->id,
        'conduct_marks' => 1,
        'display_in_report_card' => true
    ]);

    CompetitionRecord::factory(2)->create([
        'student_id' => $student2->id,
    ]);

    $response = $this->masterStudentRepository->studentMeritAndExceptionalPerformanceData([$student1->id])->toArray();

    $student1_response = $response[0];

    expect($response)->toHaveCount(1)
        ->and($student1_response['name']['en'])->toBe('Student 1')
        ->and($student1_response['id'])->toBe($student1->id)
        ->and($student1_response['reward_punishment_records'])
        ->toHaveCount(3)
        ->sequence(
            function ($item) use ($student1_reward_punishment_records) {
                $item->toMatchArray([
                    'id' => $student1_reward_punishment_records[1]->id,
                    'date' => '2024-01-03'
                ]);
            },
            function ($item) use ($student1_reward_punishment_records) {
                $item->toMatchArray([
                    'id' => $student1_reward_punishment_records[2]->id,
                    'date' => '2024-01-02'
                ]);
            },
            function ($item) use ($student1_reward_punishment_records) {
                $item->toMatchArray([
                    'id' => $student1_reward_punishment_records[0]->id,
                    'date' => '2024-01-01'
                ]);
            },
        );
});
