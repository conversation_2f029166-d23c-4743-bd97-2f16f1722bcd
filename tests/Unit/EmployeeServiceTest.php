<?php

use App\Enums\EmployeeStatus;
use App\Enums\Gender;
use App\Enums\JobType;
use App\Enums\LibraryMemberType;
use App\Enums\MarriedStatus;
use App\Models\Config;
use App\Models\Currency;
use App\Models\EcommerceProductTag;
use App\Models\EcommerceProductTagTarget;
use App\Models\Employee;
use App\Models\EmployeeCategory;
use App\Models\EmployeeJobTitle;
use App\Models\EmployeeSession;
use App\Models\EmploymentHistory;
use App\Models\LibraryMember;
use App\Models\PendingStudentEmployeeStatusChange;
use App\Models\Race;
use App\Models\Religion;
use App\Models\State;
use App\Services\EmployeeService;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Arr;

beforeEach(function () {
    $this->employeeService = app(EmployeeService::class);

    app()->setLocale('en');

    $this->testLocale = app()->getLocale();

    $this->employeeTableName = resolve(Employee::class)->getTable();
    $this->libraryMemberTableName = resolve(LibraryMember::class)->getTable();
    $this->productTagTargetTableName = resolve(EcommerceProductTagTarget::class)->getTable();

    $this->currency = Currency::factory()->malaysiaCurrency()->create();

    $config = [
        Config::LIBRARY_BORROW_LIMIT_EMPLOYEE => 3,
        Config::LIBRARY_BORROW_LIMIT_STUDENT => 2,
        Config::LIBRARY_BORROW_LIMIT_LIBRARIAN => 5,
        Config::LIBRARY_BORROW_LIMIT_OTHER => 4,
    ];

    foreach ($config as $key => $value) {
        Config::factory()->create([
            'key' => $key,
            'value' => $value
        ]);
    }
});

test('getAllPaginatedEmployees()', function () {
    $first_employee = Employee::factory()
        ->create([
            'name->en' => 'Jane Doe',
            'name->zh' => '初一',
            'gender' => Gender::FEMALE,
            'status' => EmployeeStatus::WORKING,
        ]);

    $second_employee = Employee::factory()
        ->create([
            'name->en' => 'John Doe',
            'name->zh' => '初二',
            'gender' => Gender::MALE,
            'status' => EmployeeStatus::WORKING,
        ]);

    $third_employee = Employee::factory()
        ->create([
            'name->en' => 'Lollo',
            'name->zh' => '初三',
            'gender' => Gender::FEMALE,
            'status' => EmployeeStatus::WORKING,
        ]);

    // Filter by name = Jane Doe
    $response = $this->employeeService->getAllPaginatedEmployees([
        'name' => ['en' => 'Jane Doe']
    ])->toArray();

    expect($response['data'])->toEqual([$first_employee->toArray()]);

    // Filter by name = John Doe
    $response = $this->employeeService->getAllPaginatedEmployees([
        'name' => ['en' => 'John Doe']
    ])->toArray();

    expect($response['data'])->toEqual([$second_employee->toArray()]);

    // Filter by non-existing name = No Name
    $response = $this->employeeService->getAllPaginatedEmployees([
        'name' => ['en' => 'No Name']
    ])->toArray();

    expect($response['data'])->toBeEmpty();

    // Filter by gender = female
    $response = $this->employeeService->getAllPaginatedEmployees(['gender' => 'FEMALE', 'order_by' => 'id'])->toArray();

    expect($response['data'])->sequence(
        fn($item) => $item->toEqual($first_employee->toArray()),
        fn($item) => $item->toEqual($third_employee->toArray()),
    );

    // Filter by gender = male
    $response = $this->employeeService->getAllPaginatedEmployees(['gender' => 'MALE'])->toArray();

    expect($response['data'])->toEqual([$second_employee->toArray()]);

    // Sort by name asc
    $response = $this->employeeService->getAllPaginatedEmployees([
        'order_by' => ['name' => 'asc'],
    ])->toArray();

    expect($response['data'])->sequence(
        fn($item) => $item->toEqual($first_employee->toArray()),
        fn($item) => $item->toEqual($second_employee->toArray()),
        fn($item) => $item->toEqual($third_employee->toArray()),
    );

    // Sort by name desc
    $response = $this->employeeService->getAllPaginatedEmployees([
        'order_by' => ['name' => 'desc'],
    ])->toArray();

    expect($response['data'])->sequence(
        fn($item) => $item->toEqual($third_employee->toArray()),
        fn($item) => $item->toEqual($second_employee->toArray()),
        fn($item) => $item->toEqual($first_employee->toArray()),
    );

    // Sort by id asc
    $response = $this->employeeService->getAllPaginatedEmployees([
        'order_by' => ['id' => 'asc'],
    ])->toArray();

    expect($response['data'])->sequence(
        fn($item) => $item->toHaveKey('id', $first_employee->id),
        fn($item) => $item->toHaveKey('id', $second_employee->id),
        fn($item) => $item->toHaveKey('id', $third_employee->id),
    );

    // Sort by id desc
    $response = $this->employeeService->getAllPaginatedEmployees([
        'order_by' => ['id' => 'desc'],
    ])->toArray();

    expect($response['data'])->sequence(
        fn($item) => $item->toHaveKey('id', $third_employee->id),
        fn($item) => $item->toHaveKey('id', $second_employee->id),
        fn($item) => $item->toHaveKey('id', $first_employee->id),
    );
});

test('createEmployee()', function () {
    //store success
    $this->assertDatabaseCount($this->libraryMemberTableName, 0);
    $this->assertDatabaseCount($this->employeeTableName, 0);
    $this->assertDatabaseCount('users', 0);

    $employee_category = EmployeeCategory::factory()->create();
    $job_title = EmployeeJobTitle::factory()->create();
    $religion = Religion::factory()->create();
    $race = Race::factory()->create();
    $state = State::factory()->create();
    $country = $state->country;
    $employee_session = EmployeeSession::factory()->create();

    $payload = [
        'name' => [
            'en' => fake()->name,
            'zh' => fake('zh_CN')->name,
        ],
        'badge_no' => 'dasdas34',
        'email' => '<EMAIL>',
        'personal_email' => '<EMAIL>',
        'phone_number' => '1234567890',
        'job_title_id' => $job_title->id,
        'nric' => '999000290190',
        'passport_number' => '999000290190',
        'date_of_birth' => '1990-07-03',
        'gender' => Gender::MALE->value,
        'religion_id' => $religion->id,
        'race_id' => $race->id,
        'address' => 'Long Address',
        'address_2' => 'Long Address 2',
        'postal_code' => '202200',
        'city' => 'Ipoh',
        'state_id' => $state->id,
        'country_id' => $country->id,
        'epf_number' => '1234567890',
        'employment_start_date' => '1990-07-03',
        'employment_end_date' => null,
        'highest_education' => 'Bachelor of Science',
        'highest_education_country_id' => $country->id,
        'employment_type' => JobType::FULL_TIME->value,
        'employee_category_id' => $employee_category->id,
        'marriage_status' => MarriedStatus::SINGLE->value,
        'is_hostel' => true,
        'photo' => UploadedFile::fake()->create('first_file.png', 500),
        'employee_session_id' => $employee_session->id,
    ];

    $response = $this->employeeService
        ->setUserData($payload)
        ->setEmployeeData($payload)
        ->createEmployee()
        ->toArray();

    $user_keys = ['email', 'phone_number'];

    $user_data = Arr::only($payload, $user_keys);

    unset($payload['photo']); // photo will not store inside employee table
    expect($response)->toMatchArray($payload);

    $this->assertDatabaseCount($this->employeeTableName, 1);
    $this->assertDatabaseCount('users', 1);

    $this->assertDatabaseHas($this->employeeTableName, [
        'name->en' => $payload['name']['en'],
        'name->zh' => $payload['name']['zh'],
        'email' => $payload['email'],
        'personal_email' => $payload['personal_email'],
        'phone_number' => $payload['phone_number'],
        'employee_number' => "00001", // Generated automatically
        'badge_no' => $payload['badge_no'],
        'job_title_id' => $payload['job_title_id'],
        'nric' => $payload['nric'],
        'passport_number' => $payload['passport_number'],
        'date_of_birth' => $payload['date_of_birth'],
        'gender' => $payload['gender'],
        'status' => EmployeeStatus::WORKING,
        'religion_id' => $payload['religion_id'],
        'race_id' => $payload['race_id'],
        'address' => $payload['address'],
        'address_2' => $payload['address_2'],
        'postal_code' => $payload['postal_code'],
        'city' => $payload['city'],
        'state_id' => $payload['state_id'],
        'country_id' => $payload['country_id'],
        'is_hostel' => $payload['is_hostel'],
        'id' => $response['id'],
        'epf_number' => $payload['epf_number'],
        'employment_start_date' => $payload['employment_start_date'],
        'employment_end_date' => $payload['employment_end_date'],
        'highest_education' => $payload['highest_education'],
        'highest_education_country_id' => $payload['highest_education_country_id'],
        'employment_type' => $payload['employment_type'],
        'employee_category_id' => $payload['employee_category_id'],
        'marriage_status' => $payload['marriage_status'],
        'employee_session_id' => $payload['employee_session_id'],
    ]);

    $this->assertDatabaseHas('users', [
        'email' => $user_data['email'],
        'phone_number' => $user_data['phone_number'],
    ]);

    $this->assertDatabaseHas('wallets', [
        'user_id' => $response['user_id'],
        'currency_id' => $this->currency->id,
        'balance' => 0,
    ]);

    $this->assertDatabaseCount($this->libraryMemberTableName, 1);
    $this->assertDatabaseHas($this->libraryMemberTableName, [
        'name->en' => $payload['name']['en'],
        'name->zh' => $payload['name']['zh'],
        'phone_number' => $payload['phone_number'],
        'email' => $payload['email'],
        'member_number' => "00001",
        'date_of_birth' => $payload['date_of_birth'],
        'gender' => $payload['gender'],
        'nric' => $payload['nric'],
        'passport_number' => $payload['passport_number'],
        'race_id' => $payload['race_id'],
        'religion_id' => $payload['religion_id'],
        'address' => $payload['address'],
        'postcode' => $payload['postal_code'],
        'city' => $payload['city'],
        'state_id' => $payload['state_id'],
        'country_id' => $payload['country_id'],
        'borrow_limit' => 3,
        'is_librarian' => false,
        'type' => LibraryMemberType::EMPLOYEE->value,
        'userable_type' => Employee::class,
        'userable_id' => $response['id'],
        'is_active' => true
    ]);

    $library_member = LibraryMember::where('userable_type', Employee::class)->where('userable_id', $response['id'])->first();

    $employee = Employee::find($response['id']);

    $this->assertDatabaseHas('media', [
        'model_type' => Employee::class,
        'model_id' => $employee->id,
        'file_name' => 'first_file.png',
        'collection_name' => 'photo'
    ]);

    $this->assertDatabaseHas('media', [
        'model_type' => LibraryMember::class,
        'model_id' => $library_member->id,
        'file_name' => 'first_file.png',
        'collection_name' => 'photo'
    ]);
});

test('createEmployee() without nullables', function () {
    //store success
    $this->assertDatabaseCount($this->libraryMemberTableName, 0);
    $this->assertDatabaseCount($this->employeeTableName, 0);
    $this->assertDatabaseCount('users', 0);

    $employee_category = EmployeeCategory::factory()->create();
    $job_title = EmployeeJobTitle::factory()->create();

    $payload = [
        'name' => [
            'en' => 'Godzilla',
            'zh' => '初二',
        ],
        'email' => '<EMAIL>',
        'phone_number' => '1234567890',
        'date_of_birth' => '1990-07-03',
        'gender' => Gender::MALE->value,
        'is_hostel' => false,
        'job_title_id' => $job_title->id,
        'address' => 'Some Address',
        'address_2' => 'Second part of address',
        'employment_start_date' => '1990-07-03',
        'employment_type' => JobType::FULL_TIME->value,
        'employee_category_id' => $employee_category->id,
    ];

    $response = $this->employeeService
        ->setUserData($payload)
        ->setEmployeeData($payload)
        ->createEmployee()
        ->toArray();

    $user_keys = ['email', 'phone_number'];

    $user_data = Arr::only($payload, $user_keys);

    expect($response)->toMatchArray($payload);

    $this->assertDatabaseCount($this->employeeTableName, 1);
    $this->assertDatabaseCount('users', 1);

    $this->assertDatabaseHas($this->employeeTableName, [
        'name->en' => $payload['name']['en'],
        'name->zh' => $payload['name']['zh'],
        'email' => $payload['email'],
        'personal_email' => null,
        'phone_number' => $payload['phone_number'],
        'badge_no' => null,
        'job_title_id' => $payload['job_title_id'],
        'nric' => null,
        'date_of_birth' => $payload['date_of_birth'],
        'gender' => $payload['gender'],
        'status' => EmployeeStatus::WORKING,
        'race_id' => null,
        'address' => $payload['address'],
        'address_2' => $payload['address_2'],
        'postal_code' => null,
        'city' => null,
        'state_id' => null,
        'country_id' => null,
        'is_hostel' => $payload['is_hostel'],
        'epf_number' => null,
        'employment_start_date' => $payload['employment_start_date'],
        'employment_end_date' => null,
        'highest_education' => null,
        'highest_education_country_id' => null,
        'employment_type' => $payload['employment_type'],
        'employee_category_id' => $payload['employee_category_id'],
        'marriage_status' => null,
        'employee_session_id' => null,
    ]);

    $this->assertDatabaseHas('users', [
        'email' => $user_data['email'],
        'phone_number' => $user_data['phone_number'],
    ]);

    $this->assertDatabaseHas('wallets', [
        'user_id' => $response['user_id'],
        'currency_id' => $this->currency->id,
        'balance' => 0,
    ]);

    $this->assertDatabaseCount($this->libraryMemberTableName, 1);
    $this->assertDatabaseHas($this->libraryMemberTableName, [
        'name->en' => $payload['name']['en'],
        'name->zh' => $payload['name']['zh'],
        'phone_number' => $payload['phone_number'],
        'email' => $payload['email'],
        'member_number' => "00001",
        'date_of_birth' => $payload['date_of_birth'],
        'gender' => $payload['gender'],
        'nric' => null,
        'passport_number' => null,
        'race_id' => null,
        'religion_id' =>null,
        'address' => $payload['address'],
        'postcode' => null,
        'city' =>null,
        'state_id' => null,
        'country_id' => null,
        'borrow_limit' => 3,
        'is_librarian' => false,
        'type' => LibraryMemberType::EMPLOYEE->value,
        'userable_type' => Employee::class,
        'userable_id' => $response['id'],
        'is_active' => true
    ]);
});

test('updateEmployee()', function () {
    $employee = Employee::factory()->create(['is_hostel' => true]);

    //update with id exist
    $this->assertDatabaseCount($this->employeeTableName, 1);
    $this->assertDatabaseCount('users', 1);

    $employee_category = EmployeeCategory::factory()->create();
    $job_title = EmployeeJobTitle::factory()->create();
    $religion = Religion::factory()->create();
    $race = Race::factory()->create();
    $state = State::factory()->create();
    $employee_session = EmployeeSession::factory()->create();
    $country = $state->country;

    $payload = [
        'name' => [
            'en' => 'John Jones',
            'zh' => '初二',
        ],
        'email' => '<EMAIL>',
        'personal_email' => '<EMAIL>',
        'phone_number' => '1234567890',
        'badge_no' => 'dasdas34',
        'job_title_id' => $job_title->id,
        'nric' => '999000290190',
        'date_of_birth' => '1990-07-03',
        'gender' => Gender::MALE->value,
        'religion_id' => $religion->id,
        'race_id' => $race->id,
        'address' => 'Long Address',
        'address_2' => 'Long Address 2',
        'postal_code' => '202200',
        'city' => 'Ipoh',
        'state_id' => $state->id,
        'country_id' => $country->id,
        'is_hostel' => false,
        'epf_number' => '1234567890',
        'employment_start_date' => '1990-07-03',
        'employment_end_date' => null,
        'highest_education' => 'Bachelor of Science',
        'highest_education_country_id' => $country->id,
        'employment_type' => JobType::FULL_TIME->value,
        'employee_category_id' => $employee_category->id,
        'marriage_status' => MarriedStatus::SINGLE->value,
        'employee_session_id' => $employee_session->id,
    ];

    $response = $this->employeeService
        ->setEmployeeData($payload)
        ->updateEmployee($employee->id)
        ->toArray();

    expect($response)->toMatchArray($payload);

    $this->assertDatabaseCount($this->employeeTableName, 1);
    $this->assertDatabaseCount('users', 1);

    $this->assertDatabaseHas($this->employeeTableName, [
        'name->en' => $payload['name']['en'],
        'name->zh' => $payload['name']['zh'],
        'email' => $payload['email'],
        'personal_email' => $payload['personal_email'],
        'phone_number' => $payload['phone_number'],
        'badge_no' => $payload['badge_no'],
        'job_title_id' => $payload['job_title_id'],
        'nric' => $payload['nric'],
        'date_of_birth' => $payload['date_of_birth'],
        'gender' => $payload['gender'],
        'status' => EmployeeStatus::WORKING,
        'religion_id' => $payload['religion_id'],
        'race_id' => $payload['race_id'],
        'address' => $payload['address'],
        'address_2' => $payload['address_2'],
        'postal_code' => $payload['postal_code'],
        'city' => $payload['city'],
        'state_id' => $payload['state_id'],
        'country_id' => $payload['country_id'],
        'is_hostel' => $payload['is_hostel'],
        'epf_number' => $payload['epf_number'],
        'employment_start_date' => $payload['employment_start_date'],
        'employment_end_date' => $payload['employment_end_date'],
        'highest_education' => $payload['highest_education'],
        'highest_education_country_id' => $payload['highest_education_country_id'],
        'employment_type' => $payload['employment_type'],
        'employee_category_id' => $payload['employee_category_id'],
        'marriage_status' => $payload['marriage_status'],
        'employee_session_id' => $payload['employee_session_id'],
    ]);

    $this->assertDatabaseHas('users', [
        'email' => $employee->user->email,
        'phone_number' => $employee->user->phone_number,
    ]);

    // update without nullable values
    $payload = [
        'name' => [
            'en' => 'Godzilla',
            'zh' => '初二',
        ],
        'email' => '<EMAIL>',
        'phone_number' => '1234567890',
        'date_of_birth' => '1990-07-03',
        'gender' => Gender::MALE->value,
        'is_hostel' => false,
        'job_title_id' => $job_title->id,
        'address' => 'Some Address',
        'address_2' => 'Second part of address',
        'employment_start_date' => '1990-07-03',
        'employment_type' => JobType::FULL_TIME->value,
        'employee_category_id' => $employee_category->id,
    ];

    $response = $this->employeeService
        ->setEmployeeData($payload)
        ->updateEmployee($employee->id)
        ->toArray();

    $this->assertDatabaseHas($this->employeeTableName, [
        'name->en' => $payload['name']['en'],
        'name->zh' => $payload['name']['zh'],
        'email' => $payload['email'],
        'personal_email' => null,
        'phone_number' => $payload['phone_number'],
        'badge_no' => null,
        'job_title_id' => $payload['job_title_id'],
        'nric' => null,
        'date_of_birth' => $payload['date_of_birth'],
        'gender' => $payload['gender'],
        'status' => EmployeeStatus::WORKING,
        'race_id' => null,
        'address' => $payload['address'],
        'address_2' => $payload['address_2'],
        'postal_code' => null,
        'city' => null,
        'state_id' => null,
        'country_id' => null,
        'is_hostel' => $payload['is_hostel'],
        'epf_number' => null,
        'employment_start_date' => $payload['employment_start_date'],
        'employment_end_date' => null,
        'highest_education' => null,
        'highest_education_country_id' => null,
        'employment_type' => $payload['employment_type'],
        'employee_category_id' => $payload['employee_category_id'],
        'marriage_status' => null,
        'employee_session_id' => null,
    ]);

    $this->assertDatabaseHas('users', [
        'email' => $employee->user->email,
        'phone_number' => $employee->user->phone_number,
    ]);

    //update with id not exist
    $payload = [
        'name' => [
            'en' => 'Test 3',
        ],
    ];

    $has_error = false;

    try {
        $this->employeeService->updateEmployee(9999, $payload)->toArray();
    } catch (\Throwable $th) {
        expect($th)->toBeInstanceOf(ModelNotFoundException::class);
        $has_error = true;
    }
    expect($has_error)->toBeTrue();
});

test('deleteEmployee()', function () {
    $first = Employee::factory()->create();
    $others = Employee::factory(3)->create();

    $this->assertDatabaseCount($this->employeeTableName, 4);

    // Delete success
    $this->employeeService->deleteEmployee($first->id);

    $this->assertDatabaseCount($this->employeeTableName, 3);
    $this->assertDatabaseMissing($this->employeeTableName, ['id' => $first->id]);

    foreach ($others as $other) {
        $this->assertDatabaseHas($this->employeeTableName, ['id' => $other->id]);
    }

    // ID not exist
    $this->expectException(ModelNotFoundException::class);
    $this->employeeService->deleteEmployee(9999);
});


test('resignEmployee() error already resigned', function () {

    $employee = Employee::factory()->create([
        'status' => EmployeeStatus::RESIGNED->value,
        'employment_start_date' => '2024-01-01',
        'employment_end_date' => '2024-03-01',
    ]);

    $this->expectExceptionMessage('Cannot resign an employee that is not working.');
    $this->expectExceptionCode(27001);
    $this->employeeService->setEmployee($employee)->markEmployeeResignedAt('2024-05-01');

});

test('resignEmployee() error invalid resignation date', function () {

    $employee = Employee::factory()->create([
        'status' => EmployeeStatus::WORKING->value,
        'employment_start_date' => '2024-01-01',
        'employment_end_date' => null,
    ]);

    $this->expectExceptionMessage('Effective date of resignation cannot be before employment start date.');
    $this->expectExceptionCode(27002);
    $this->employeeService->setEmployee($employee)->markEmployeeResignedAt('2023-12-31');

});

test('resignEmployee() success - effective date in the past', function () {

    $employee = Employee::factory()->create([
        'status' => EmployeeStatus::WORKING->value,
        'employment_start_date' => '2024-01-01',
        'employment_end_date' => null,
    ]);

    $this->employeeService->setEmployee($employee)->markEmployeeResignedAt('2024-03-01');

    $this->assertDatabaseHas(Employee::class, [
        'id' => $employee->id,
        'status' => EmployeeStatus::RESIGNED,
        'employment_start_date' => '2024-01-01',
        'employment_end_date' => '2024-03-01',
    ]);

    // check whether employment history is updated (only updated when employee resigned/change job)
    $this->assertDatabaseHas(EmploymentHistory::class, [
        'employee_id' => $employee->id,
        'employment_start_date' => '2024-01-01',
        'employment_end_date' => '2024-03-01',
        'job_title_id' => $employee->job_title_id,
    ]);

    $this->assertDatabaseEmpty(PendingStudentEmployeeStatusChange::class);
});


test('resignEmployee success - effective date in the future', function () {

    // future date
    $resign_date = now(config('school.timezone'))->addDay()->toDateString();

    $employee = Employee::factory()->create([
        'status' => EmployeeStatus::WORKING->value,
        'employment_start_date' =>'2024-01-01',
        'employment_end_date' => null,
    ]);

    $this->employeeService->setEmployee($employee)
        ->setApiRequest(['effective_date' => $resign_date])
        ->markEmployeeResignedAt($resign_date);

    $this->assertDatabaseHas(Employee::class, [
        'id' => $employee->id,
        'status' => EmployeeStatus::WORKING->value, // status not updated yet
        'employment_start_date' => '2024-01-01',
        'employment_end_date' => null,
    ]);

    $this->assertDatabaseHas(PendingStudentEmployeeStatusChange::class, [
        'type' => PendingStudentEmployeeStatusChange::TYPE_LEAVE,
        'execution_date' => $resign_date,
        'data->effective_date' => $resign_date,
        'status' => PendingStudentEmployeeStatusChange::STATUS_PENDING,
        'status_changeable_type' => Employee::class,
        'status_changeable_id' => $employee->id,
    ]);

    $this->assertDatabaseEmpty(EmploymentHistory::class);

    // run command
    Artisan::call("execute:pending-status-change --execution_date={$resign_date}");

    $this->assertDatabaseHas(Employee::class, [
        'id' => $employee->id,
        'status' => EmployeeStatus::RESIGNED->value, // status updated to resigned
        'employment_start_date' => '2024-01-01',
        'employment_end_date' => $resign_date,
    ]);

    $this->assertDatabaseHas(PendingStudentEmployeeStatusChange::class, [
        'type' => PendingStudentEmployeeStatusChange::TYPE_LEAVE,
        'execution_date' => $resign_date,
        'data->effective_date' => $resign_date,
        'status' => PendingStudentEmployeeStatusChange::STATUS_SUCCESS,
        'status_changeable_type' => Employee::class,
        'status_changeable_id' => $employee->id,
    ]);

    $this->assertDatabaseHas(EmploymentHistory::class, [
        'employee_id' => $employee->id,
        'employment_start_date' => '2024-01-01',
        'employment_end_date' => $resign_date,
        'job_title_id' => $employee->job_title_id,
    ]);

});

test('reinstateEmployee() error not resigned', function () {

    $employee = Employee::factory()->create([
        'status' => EmployeeStatus::WORKING->value,
        'employment_start_date' => '2024-01-01',
        'employment_end_date' => null,
    ]);

    $this->expectExceptionMessage('Cannot reinstate an employee that is still working.');
    $this->expectExceptionCode(27003);
    $this->employeeService->setEmployee($employee)->markEmployeeReinstatedAt('2024-05-01');

});

test('reinstateEmployee() success - effective date in the past', function () {

    $employee = Employee::factory()->create([
        'status' => EmployeeStatus::RESIGNED->value,
        'employment_start_date' => '2024-01-01',
        'employment_end_date' => '2024-03-01',
    ]);

    $this->employeeService->setEmployee($employee)->markEmployeeReinstatedAt('2024-05-01');

    $this->assertDatabaseHas(Employee::class, [
        'id' => $employee->id,
        'status' => EmployeeStatus::WORKING,
        'employment_start_date' => '2024-05-01',
        'employment_end_date' => null,
    ]);

    // no employment history added.
    $this->assertDatabaseEmpty(EmploymentHistory::class);
    $this->assertDatabaseEmpty(PendingStudentEmployeeStatusChange::class);
});


test('reinstateEmployee() success - effective date in the future', function () {

    // future date
    $future_date = now()->addDays(3)->toDateString();

    $employee = Employee::factory()->create([
        'status' => EmployeeStatus::RESIGNED->value,
        'employment_start_date' => '2024-01-01',
        'employment_end_date' => '2024-03-01',
    ]);

    $this->employeeService->setEmployee($employee)
        ->setApiRequest(['effective_date' => $future_date])
        ->markEmployeeReinstatedAt($future_date);

    // no changes yet
    $this->assertDatabaseHas(Employee::class, [
        'id' => $employee->id,
        'status' => EmployeeStatus::RESIGNED->value,
        'employment_start_date' => '2024-01-01',
        'employment_end_date' => '2024-03-01',
    ]);

    // no employment history added.
    $this->assertDatabaseEmpty(EmploymentHistory::class);

    $this->assertDatabaseHas(PendingStudentEmployeeStatusChange::class, [
        'type' => PendingStudentEmployeeStatusChange::TYPE_RETURN,
        'execution_date' => $future_date,
        'data->effective_date' => $future_date,
        'status' => PendingStudentEmployeeStatusChange::STATUS_PENDING,
        'status_changeable_type' => Employee::class,
        'status_changeable_id' => $employee->id,
    ]);


    // run command
    Artisan::call("execute:pending-status-change --execution_date={$future_date}");

    $this->assertDatabaseHas(Employee::class, [
        'id' => $employee->id,
        'status' => EmployeeStatus::WORKING->value, // status updated to resigned
        'employment_start_date' => $future_date,
        'employment_end_date' => null,
    ]);

    $this->assertDatabaseHas(PendingStudentEmployeeStatusChange::class, [
        'type' => PendingStudentEmployeeStatusChange::TYPE_RETURN,
        'execution_date' => $future_date,
        'data->effective_date' => $future_date,
        'status' => PendingStudentEmployeeStatusChange::STATUS_SUCCESS,
        'status_changeable_type' => Employee::class,
        'status_changeable_id' => $employee->id,
    ]);

    // no employment history added.
    $this->assertDatabaseEmpty(EmploymentHistory::class);
});

test('reinstateEmployee() success - effective date in the past - for employee with no user attached', function () {
    $employee = Employee::factory()->create([
        'user_id' => null, // no user attached
        'status' => EmployeeStatus::RESIGNED->value,
        'employment_start_date' => '2024-01-01',
        'employment_end_date' => '2024-03-01',
    ]);

    $this->assertDatabaseCount($this->employeeTableName, 1);
    $this->assertDatabaseCount('users', 0);

    $this->employeeService->setEmployee($employee)->markEmployeeReinstatedAt('2024-05-01');

    $employee->refresh();

    $this->assertDatabaseCount('users', 1);
    $this->assertDatabaseHas(Employee::class, [
        'id' => $employee->id,
        'status' => EmployeeStatus::WORKING,
        'employment_start_date' => '2024-05-01',
        'employment_end_date' => null,
    ]);

    expect($employee->user)->not()->toBeNull();

    // no employment history added.
    $this->assertDatabaseEmpty(EmploymentHistory::class);
    $this->assertDatabaseEmpty(PendingStudentEmployeeStatusChange::class);
});


test('transferEmployee() error not working', function () {

    $employee = Employee::factory()->create([
        'status' => EmployeeStatus::RESIGNED->value,
        'employment_start_date' => '2024-01-01',
        'employment_end_date' => '2024-04-01',
    ]);

    $new_job_title = EmployeeJobTitle::factory()->create();

    $this->expectExceptionMessage('Cannot transfer an employee that is not working.');
    $this->expectExceptionCode(27004);
    $this->employeeService->setEmployee($employee)->setNewJobTitle($new_job_title)->transferEmployee('2024-05-01');

});


test('transferEmployee() error invalid effective date', function () {

    $employee = Employee::factory()->create([
        'status' => EmployeeStatus::WORKING->value,
        'employment_start_date' => '2024-01-01',
        'employment_end_date' => null,
    ]);

    $new_job_title = EmployeeJobTitle::factory()->create();

    $this->expectExceptionMessage('Effective date of transfer cannot be before employment start date.');
    $this->expectExceptionCode(27005);
    $this->employeeService->setEmployee($employee)->setNewJobTitle($new_job_title)->transferEmployee('2023-12-31');

});

test('transferEmployee() success - effective date in the past', function () {

    $old_job_title = EmployeeJobTitle::factory()->create();
    $new_job_title = EmployeeJobTitle::factory()->create();

    $employee = Employee::factory()->create([
        'status' => EmployeeStatus::WORKING->value,
        'employment_start_date' => '2024-01-01',
        'employment_end_date' => null,
        'job_title_id' => $old_job_title->id,
    ]);

    $this->employeeService->setEmployee($employee)->setNewJobTitle($new_job_title)->transferEmployee('2024-04-01');

    $this->assertDatabaseHas(Employee::class, [
        'id' => $employee->id,
        'status' => EmployeeStatus::WORKING,
        'employment_start_date' => '2024-04-01',
        'employment_end_date' => null,
        'job_title_id' => $new_job_title->id,
    ]);

    // check whether employment history is updated (only updated when employee resigned/change job)
    $this->assertDatabaseHas(EmploymentHistory::class, [
        'employee_id' => $employee->id,
        'employment_start_date' => '2024-01-01',
        'employment_end_date' => '2024-03-31',
        'job_title_id' => $old_job_title->id,
    ]);

    $this->assertDatabaseEmpty(PendingStudentEmployeeStatusChange::class);

});

test('transferEmployee success - effective date in the future', function () {

    // future date
    $future_date = now()->addDays(3)->toDateString();
    $resigned_date = now()->addDays(2)->toDateString();

    $old_job_title = EmployeeJobTitle::factory()->create();
    $new_job_title = EmployeeJobTitle::factory()->create();

    $employee = Employee::factory()->create([
        'status' => EmployeeStatus::WORKING->value,
        'employment_start_date' => '2024-01-01',
        'employment_end_date' => null,
        'job_title_id' => $old_job_title->id,
    ]);

    $this->employeeService->setEmployee($employee)
        ->setApiRequest(['effective_date' => $future_date, 'job_title_id' => $new_job_title->id])
        ->setNewJobTitle($new_job_title)
        ->transferEmployee($future_date);

    // nothing changed yet
    $this->assertDatabaseHas(Employee::class, [
        'id' => $employee->id,
        'status' => EmployeeStatus::WORKING,
        'employment_start_date' => '2024-01-01',
        'employment_end_date' => null,
        'job_title_id' => $old_job_title->id,
    ]);

    $this->assertDatabaseEmpty(EmploymentHistory::class);

    $this->assertDatabaseHas(PendingStudentEmployeeStatusChange::class, [
        'type' => PendingStudentEmployeeStatusChange::TYPE_TRANSFER,
        'execution_date' => $future_date,
        'data->effective_date' => $future_date,
        'status' => PendingStudentEmployeeStatusChange::STATUS_PENDING,
        'status_changeable_type' => Employee::class,
        'status_changeable_id' => $employee->id,
    ]);

    // run command
    Artisan::call("execute:pending-status-change --execution_date={$future_date}");

    // employee updated
    $this->assertDatabaseHas(Employee::class,[
        'id' => $employee->id,
        'status' => EmployeeStatus::WORKING,
        'employment_start_date' => $future_date,
        'employment_end_date' => null,
        'job_title_id' => $new_job_title->id,
    ]);
    $this->assertDatabaseHas(EmploymentHistory::class, [
        'employee_id' => $employee->id,
        'employment_start_date' => '2024-01-01',
        'employment_end_date' => $resigned_date,
        'job_title_id' => $old_job_title->id,
    ]);
    $this->assertDatabaseHas(PendingStudentEmployeeStatusChange::class, [
        'type' => PendingStudentEmployeeStatusChange::TYPE_TRANSFER,
        'execution_date' => $future_date,
        'data->effective_date' => $future_date,
        'status' => PendingStudentEmployeeStatusChange::STATUS_SUCCESS,
        'status_changeable_type' => Employee::class,
        'status_changeable_id' => $employee->id,
    ]);

});

test('isFutureDate()', function () {

    \Carbon\Carbon::setTestNow('2024-11-14 23:59:00'); // malaysia timezone 2024-11-15 07:59:59

    expect($this->employeeService->isFutureDate('2024-11-16'))->toBeTrue();
    expect($this->employeeService->isFutureDate('2024-11-16 00:00:01'))->toBeTrue();
    expect($this->employeeService->isFutureDate('2024-11-15'))->toBeFalse();
    expect($this->employeeService->isFutureDate('2024-11-15 23:59:00'))->toBeFalse();
    expect($this->employeeService->isFutureDate('2024-11-15 07:59:59'))->toBeFalse();
    expect($this->employeeService->isFutureDate('2024-11-15 08:00:00'))->toBeFalse();
    expect($this->employeeService->isFutureDate('2024-11-14'))->toBeFalse();
    expect($this->employeeService->isFutureDate('2024-11-13'))->toBeFalse();
});
