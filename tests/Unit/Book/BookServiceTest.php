<?php

use App\Enums\BookBinding;
use App\Enums\BookCondition;
use App\Enums\BookLoanSettingType;
use App\Enums\BookStatus;
use App\Enums\LibraryBookLoanStatus;
use App\Enums\LibraryMemberType;
use App\Models\Author;
use App\Models\Book;
use App\Models\BookCategory;
use App\Models\BookClassification;
use App\Models\BookLoanSetting;
use App\Models\BookSource;
use App\Models\BookSubClassification;
use App\Models\LibraryBookLoan;
use App\Models\LibraryMember;
use App\Models\Student;
use App\Services\BookService;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\Sequence;
use Illuminate\Database\Eloquent\ModelNotFoundException;

beforeEach(function () {
    $this->bookService = app(BookService::class);

    app()->setLocale('en');

    $this->testLocale = app()->getLocale();

    $this->table = resolve(Book::class)->getTable();
    $this->authorBookTable = 'author_book';
    $this->bookLoanSettingTable = resolve(BookLoanSetting::class)->getTable();
});

test('getAllPaginatedBooks()', function () {
    $book_sub_classifications = BookSubClassification::factory(2)->create();
    $authors = Author::factory(2)
        ->state(new Sequence(
            [
                'name' => 'David',
            ],
            [
                'name' => 'Simon'
            ]
        ))
        ->create();
    $books = Book::factory(4)->state(new Sequence(
        [
            'book_no' => '12345',
            'call_no' => '12345',
            'book_sub_classification_id' => $book_sub_classifications[0]->id,
            'title' => 'English',
            'isbn' => '123456789',
        ],
        [
            'book_no' => '12346',
            'call_no' => '12346',
            'book_sub_classification_id' => $book_sub_classifications[0]->id,
            'title' => 'English 2',
            'isbn' => '123456788',
        ],
        [
            'book_no' => '12347',
            'call_no' => '12347',
            'book_sub_classification_id' => $book_sub_classifications[1]->id,
            'title' => 'Tamil',
            'isbn' => '123456787',
        ],
        [
            'book_no' => '12348',
            'call_no' => '12348',
            'book_sub_classification_id' => $book_sub_classifications[1]->id,
            'title' => '全新思维',
            'isbn' => '123456786',
        ],
    ))->create();

    $books[0]->authors()->sync([$authors[0]->id]);
    $books[1]->authors()->sync([$authors[0]->id]);
    $books[2]->authors()->sync([$authors[1]->id]);
    $books[3]->authors()->sync([$authors[1]->id]);

    //Filter by title = English 2
    $payload = [
        'title' => 'English 2'
    ];
    $response = $this->bookService->getAllPaginatedBooks($payload)->toArray();

    expect($response['data'])->toHaveCount(1)
        ->sequence(
            fn($data) => $data->toHaveKey('title', 'English 2')
        );

    //Filter by partial title = English
    $payload = [
        'title' => 'English'
    ];
    $response = $this->bookService->getAllPaginatedBooks($payload)->toArray();

    expect($response['data'])->toHaveCount(2)
        ->sequence(
            fn($data) => $data->toHaveKey('title', 'English'),
            fn($data) => $data->toHaveKey('title', 'English 2')
        );

    //Filter by keywords - wildcard
    $payload = [
        'title' => 'glis'
    ];
    $response = $this->bookService->getAllPaginatedBooks($payload)->toArray();

    expect($response['data'])->toHaveCount(2)
        ->sequence(
            fn($data) => $data->toHaveKey('title', 'English'),
            fn($data) => $data->toHaveKey('title', 'English 2')
        );

    $payload = [
        'title' => '新思'
    ];
    $response = $this->bookService->getAllPaginatedBooks($payload)->toArray();

    expect($response['data'])->toHaveCount(1)
        ->sequence(
            fn($data) => $data->toHaveKey('title', '全新思维'),
        );

    $payload = [
        'title' => '全新思'
    ];
    $response = $this->bookService->getAllPaginatedBooks($payload)->toArray();

    expect($response['data'])->toHaveCount(1)
        ->sequence(
            fn($data) => $data->toHaveKey('title', '全新思维'),
        );

    $payload = [
        'title' => '思维'
    ];
    $response = $this->bookService->getAllPaginatedBooks($payload)->toArray();

    expect($response['data'])->toHaveCount(1)
        ->sequence(
            fn($data) => $data->toHaveKey('title', '全新思维'),
        );

    //Filter non-existing title = No Exist
    $payload = [
        'title' => 'No Exist'
    ];
    $response = $this->bookService->getAllPaginatedBooks($payload)->toArray();

    expect($response['data'])->toHaveCount(0);

    //Filter by author ids
    $payload = [
        'author_ids' => [$authors[0]->id]
    ];
    $response = $this->bookService->getAllPaginatedBooks($payload)->toArray();

    expect($response['data'])->toHaveCount(2)
        ->sequence(
            fn($data) => $data->toHaveKey('authors.0.id', $authors[0]->id),
            fn($data) => $data->toHaveKey('authors.0.id', $authors[0]->id)
        );

    //Filter non-existing author_ids
    $payload = [
        'author_ids' => [9999]
    ];
    $response = $this->bookService->getAllPaginatedBooks($payload)->toArray();

    expect($response['data'])->toHaveCount(0);

    //Filter by isbn
    $payload = [
        'isbn' => '123456789'
    ];
    $response = $this->bookService->getAllPaginatedBooks($payload)->toArray();

    expect($response['data'])->toHaveCount(1)
        ->sequence(
            fn($data) => $data->toHaveKey('isbn', '123456789'),
        );

    //Filter non-existing isbn
    $payload = [
        'isbn' => '999999'
    ];
    $response = $this->bookService->getAllPaginatedBooks($payload)->toArray();

    expect($response['data'])->toHaveCount(0);

    //Filter by book sub classification id = 1
    $payload = [
        'book_sub_classification_id' => $book_sub_classifications[1]->id
    ];
    $response = $this->bookService->getAllPaginatedBooks($payload)->toArray();

    expect($response['data'])->toHaveCount(2)
        ->sequence(
            fn($data) => $data->toHaveKey('book_sub_classification_id', $book_sub_classifications[1]->id)
        );

    //Filter non-existing book sub classification id = 99999
    $payload = [
        'book_sub_classification_id' => 99999
    ];
    $response = $this->bookService->getAllPaginatedBooks($payload)->toArray();

    expect($response['data'])->toHaveCount(0);

    //Filter by book_no = 12345
    $payload = [
        'book_no' => '12345'
    ];
    $response = $this->bookService->getAllPaginatedBooks($payload)->toArray();

    expect($response['data'])->toHaveCount(1)
        ->sequence(
            fn($data) => $data->toHaveKey('book_no', '12345')
        );

    //Filter non-existing book_no = No Exist
    $payload = [
        'book_no' => 'No Exist'
    ];
    $response = $this->bookService->getAllPaginatedBooks($payload)->toArray();

    expect($response['data'])->toHaveCount(0);

    //Filter by call_no = 12345
    $payload = [
        'call_no' => '12345'
    ];
    $response = $this->bookService->getAllPaginatedBooks($payload)->toArray();

    expect($response['data'])->toHaveCount(1)
        ->sequence(
            fn($data) => $data->toHaveKey('call_no', '12345')
        );

    //Filter non-existing call_no = No Exist
    $payload = [
        'call_no' => 'No Exist'
    ];
    $response = $this->bookService->getAllPaginatedBooks($payload)->toArray();

    expect($response['data'])->toHaveCount(0);

    //sort by title asc
    $payload = [
        'order_by' => ['title' => 'asc'],
    ];

    $response = $this->bookService->getAllPaginatedBooks($payload)->toArray();

    expect($response['data'])->toHaveCount(4)
        ->sequence(
            fn($data) => $data->toHaveKey('title', 'English'),
            fn($data) => $data->toHaveKey('title', 'English 2'),
            fn($data) => $data->toHaveKey('title', 'Tamil'),
            fn($data) => $data->toHaveKey('title', '全新思维'),
        );

    //sort by title desc
    $payload = [
        'order_by' => ['title' => 'desc'],
    ];

    $response = $this->bookService->getAllPaginatedBooks($payload)->toArray();

    expect($response['data'])->toHaveCount(4)
        ->sequence(
            fn($data) => $data->toHaveKey('title', '全新思维'),
            fn($data) => $data->toHaveKey('title', 'Tamil'),
            fn($data) => $data->toHaveKey('title', 'English 2'),
            fn($data) => $data->toHaveKey('title', 'English'),
        );

    //sort by id asc
    $payload = [
        'order_by' => ['id' => 'asc'],
    ];
    $response = $this->bookService->getAllPaginatedBooks($payload)->toArray();

    expect($response['data'])->toHaveCount(4)
        ->sequence(
            fn($data) => $data->toHaveKey('id', $books[0]->id),
            fn($data) => $data->toHaveKey('id', $books[1]->id),
            fn($data) => $data->toHaveKey('id', $books[2]->id),
            fn($data) => $data->toHaveKey('id', $books[3]->id),
        );

    //sort by id desc
    $payload = [
        'order_by' => ['id' => 'desc'],
    ];
    $response = $this->bookService->getAllPaginatedBooks($payload)->toArray();

    expect($response['data'])->toHaveCount(4)
        ->sequence(
            fn($data) => $data->toHaveKey('id', $books[3]->id),
            fn($data) => $data->toHaveKey('id', $books[2]->id),
            fn($data) => $data->toHaveKey('id', $books[1]->id),
            fn($data) => $data->toHaveKey('id', $books[0]->id),
        );
});

test('createBook()', function () {
    $book_classification = BookClassification::factory()->create();
    $book_sub_classification = BookSubClassification::factory()->create([
        'book_classification_id' => $book_classification->id,
    ]);
    $book_category = BookCategory::factory()->create();
    $book_source = BookSource::factory()->create();
    $author = Author::factory()->create([
        'name' => 'J.K. Rowling',
    ]);

    //store success
    $this->assertDatabaseCount($this->table, 0);
    $this->assertDatabaseCount($this->authorBookTable, 0);
    $this->assertDatabaseCount($this->bookLoanSettingTable, 0);

    $payload = [
        'book_no' => '123457',
        'call_no' => fake()->sentence(),
        'book_category_id' => $book_category->id,
        'book_classification_id' => $book_classification->id,
        'book_sub_classification_id' => $book_sub_classification->id,
        'book_source_id' => $book_source->id,
        'title' => fake()->title,
        'series' => fake()->sentence(),
        'edition' => fake()->sentence(),
        'remark' => fake()->sentence(),
        'topic' => fake()->title(),
        'location_1' => fake()->sentence(),
        'location_2' => fake()->sentence(),
        'isbn' => fake()->sentence(),
        'binding' => BookBinding::HARD_COVER->value,
        'publisher' => fake()->sentence(),
        'publisher_place' => fake()->address(),
        'published_date' => fake()->date(),
        'book_page' => 100,
        'words' => 1000,
        'book_size' => '12 inch',
        'cdrom' => false,
        'purchase_value' => 10,
        'lost_penalty_value' => 10,
        'authors' => [
            'J.K. Rowling',
            'J.R.R. Tolkien'
        ],
        'loan_settings' => [
            BookLoanSettingType::EMPLOYEE->value => [
                'loan_period_day' => 5,
                'can_borrow' => true
            ],
            BookLoanSettingType::STUDENT->value => [
                'loan_period_day' => 10,
                'can_borrow' => true
            ],
            BookLoanSettingType::LIBRARIAN->value => [
                'loan_period_day' => 15,
                'can_borrow' => true
            ],
            BookLoanSettingType::OTHERS->value => [
                'loan_period_day' => 20,
                'can_borrow' => false
            ],
        ]
    ];

    $response = $this->bookService->createBook($payload)->toArray();

    expect($response)->toMatchArray([
        'book_no' => $payload['book_no'],
        'call_no' => $payload['call_no'],
        'book_category_id' => $payload['book_category_id'],
        'book_classification_id' => $payload['book_classification_id'],
        'book_sub_classification_id' => $payload['book_sub_classification_id'],
        'book_source_id' => $payload['book_source_id'],
        'title' => $payload['title'],
        'series' => $payload['series'],
        'edition' => $payload['edition'],
        'remark' => $payload['remark'],
        'topic' => $payload['topic'],
        'location_1' => $payload['location_1'],
        'location_2' => $payload['location_2'],
        'isbn' => $payload['isbn'],
        'binding' => $payload['binding'],
        'publisher' => $payload['publisher'],
        'publisher_place' => $payload['publisher_place'],
        'book_page' => $payload['book_page'],
        'words' => $payload['words'],
        'book_size' => $payload['book_size'],
        'cdrom' => $payload['cdrom'],
        'purchase_value' => $payload['purchase_value'],
        'lost_penalty_value' => $payload['lost_penalty_value'],
    ])->and(Carbon::parse($response['published_date'])
        ->format('Y-m-d'))->toBe($payload['published_date']);

    $this->assertDatabaseCount($this->table, 1);

    $this->assertDatabaseHas($this->table, [
        'book_no' => $payload['book_no'],
        'call_no' => $payload['call_no'],
        'book_category_id' => $payload['book_category_id'],
        'book_classification_id' => $payload['book_classification_id'],
        'book_sub_classification_id' => $payload['book_sub_classification_id'],
        'book_source_id' => $payload['book_source_id'],
        'title' => $payload['title'],
        'series' => $payload['series'],
        'edition' => $payload['edition'],
        'remark' => $payload['remark'],
        'topic' => $payload['topic'],
        'location_1' => $payload['location_1'],
        'location_2' => $payload['location_2'],
        'isbn' => $payload['isbn'],
        'binding' => $payload['binding'],
        'publisher' => $payload['publisher'],
        'publisher_place' => $payload['publisher_place'],
        'published_date' => $payload['published_date'],
        'book_page' => $payload['book_page'],
        'words' => $payload['words'],
        'book_size' => $payload['book_size'],
        'cdrom' => $payload['cdrom'],
        'purchase_value' => $payload['purchase_value'],
        'lost_penalty_value' => $payload['lost_penalty_value']
    ]);

    //author_book table inserted 2 records
    $this->assertDatabaseCount($this->authorBookTable, 2);

    $this->assertDatabaseCount('master_authors', 2);
    foreach ($payload['authors'] as $author) {
        $this->assertDatabaseHas('master_authors', [
            'name' => $author
        ]);

        $this->assertDatabaseHas($this->authorBookTable, [
            'book_id' => $response['id'],
            'author_id' => Author::where('name', $author)->first()->id
        ]);
    }

    //book_loan_settings table inserted 4 records
    $this->assertDatabaseCount($this->bookLoanSettingTable, 4);

    foreach ($payload['loan_settings'] as $type => $value) {
        $this->assertDatabaseHas($this->bookLoanSettingTable, [
            'book_id' => $response['id'],
            'type' => $type,
            'loan_period_day' => $value['loan_period_day'],
            'can_borrow' => $value['can_borrow'],
        ]);
    }
});


test('updateBook()', function () {
    $book_classification = BookClassification::factory()->create();
    $book_sub_classification = BookSubClassification::factory()->create([
        'book_classification_id' => $book_classification->id,
    ]);
    $book_category = BookCategory::factory()->create();
    $book_source = BookSource::factory()->create();
    $author = Author::factory()->create([
        'name' => 'J.K. Rowling',
    ]);

    $book = Book::factory()->create([
        'book_no' => '123457',
        'call_no' => fake()->sentence(),
        'book_category_id' => $book_category->id,
        'book_classification_id' => $book_classification->id,
        'book_sub_classification_id' => $book_sub_classification->id,
        'book_source_id' => $book_source->id,
        'title' => fake()->title,
        'series' => fake()->sentence(),
        'edition' => fake()->sentence(),
        'remark' => fake()->sentence(),
        'topic' => fake()->title(),
        'location_1' => fake()->sentence(),
        'location_2' => fake()->sentence(),
        'isbn' => fake()->sentence(),
        'binding' => BookBinding::HARD_COVER->value,
        'publisher' => fake()->sentence(),
        'publisher_place' => fake()->address(),
        'published_date' => fake()->date(),
        'book_page' => 100,
        'words' => 1000,
        'book_size' => '12 inch',
        'cdrom' => false,
        'purchase_value' => 10,
        'lost_penalty_value' => 20,
    ]);

    BookLoanSetting::factory(4)
        ->state(new Sequence(
            [
                'book_id' => $book->id,
                'type' => BookLoanSettingType::STUDENT->value,
                'loan_period_day' => 0,
                'can_borrow' => false
            ],
            [
                'book_id' => $book->id,
                'type' => BookLoanSettingType::EMPLOYEE->value,
                'loan_period_day' => 0,
                'can_borrow' => false
            ],
            [
                'book_id' => $book->id,
                'type' => BookLoanSettingType::LIBRARIAN->value,
                'loan_period_day' => 0,
                'can_borrow' => false
            ],
            [
                'book_id' => $book->id,
                'type' => BookLoanSettingType::OTHERS->value,
                'loan_period_day' => 0,
                'can_borrow' => false
            ],
        ))
        ->create();

    $book_classification2 = BookClassification::factory()->create();
    $book_sub_classification2 = BookSubClassification::factory()->create([
        'book_classification_id' => $book_classification2->id,
    ]);
    $book_category2 = BookCategory::factory()->create();
    $book_source2 = BookSource::factory()->create();

    $payload = [
        'book_no' => '123457',
        'call_no' => fake()->sentence(),
        'book_category_id' => $book_category2->id,
        'book_classification_id' => $book_classification2->id,
        'book_sub_classification_id' => $book_sub_classification2->id,
        'book_source_id' => $book_source2->id,
        'title' => 'test 1',
        'series' => fake()->sentence(),
        'edition' => fake()->sentence(),
        'remark' => fake()->sentence(),
        'topic' => fake()->title(),
        'location_1' => fake()->sentence(),
        'location_2' => fake()->sentence(),
        'isbn' => fake()->sentence(),
        'binding' => BookBinding::HARD_COVER->value,
        'publisher' => fake()->sentence(),
        'publisher_place' => fake()->address(),
        'published_date' => fake()->date(),
        'book_page' => 100,
        'words' => 1000,
        'book_size' => '12 inch',
        'cdrom' => false,
        'purchase_value' => 20,
        'lost_penalty_value' => 40,
        'authors' => [
            'J.K. Rowling',
            'J.R.R. Tolkien'
        ],
        'loan_settings' => [
            BookLoanSettingType::EMPLOYEE->value => [
                'loan_period_day' => 10,
                'can_borrow' => true
            ],
            BookLoanSettingType::STUDENT->value => [
                'loan_period_day' => 10,
                'can_borrow' => true
            ],
            BookLoanSettingType::LIBRARIAN->value => [
                'loan_period_day' => 10,
                'can_borrow' => true
            ],
            BookLoanSettingType::OTHERS->value => [
                'loan_period_day' => 10,
                'can_borrow' => true
            ],
        ]
    ];

    $response = $this->bookService->updateBook($book->id, $payload)->toArray();

    $this->assertDatabaseCount($this->table, 1);
    $this->assertDatabaseCount($this->authorBookTable, 2);
    $this->assertDatabaseCount($this->bookLoanSettingTable, 4);

    expect($response)->toMatchArray([
        'book_no' => $payload['book_no'],
        'call_no' => $payload['call_no'],
        'book_category_id' => $payload['book_category_id'],
        'book_classification_id' => $payload['book_classification_id'],
        'book_sub_classification_id' => $payload['book_sub_classification_id'],
        'book_source_id' => $payload['book_source_id'],
        'title' => $payload['title'],
        'series' => $payload['series'],
        'edition' => $payload['edition'],
        'remark' => $payload['remark'],
        'topic' => $payload['topic'],
        'location_1' => $payload['location_1'],
        'location_2' => $payload['location_2'],
        'isbn' => $payload['isbn'],
        'binding' => $payload['binding'],
        'publisher' => $payload['publisher'],
        'publisher_place' => $payload['publisher_place'],
        'book_page' => $payload['book_page'],
        'words' => $payload['words'],
        'book_size' => $payload['book_size'],
        'cdrom' => $payload['cdrom'],
        'purchase_value' => $payload['purchase_value'],
        'lost_penalty_value' => $payload['lost_penalty_value'],
    ])->and(Carbon::parse($response['published_date'])
        ->format('Y-m-d'))->toBe($payload['published_date']);

    $this->assertDatabaseState($this->table, 1, [
        'book_no' => $payload['book_no'],
        'call_no' => $payload['call_no'],
        'book_category_id' => $payload['book_category_id'],
        'book_classification_id' => $payload['book_classification_id'],
        'book_sub_classification_id' => $payload['book_sub_classification_id'],
        'book_source_id' => $payload['book_source_id'],
        'title' => $payload['title'],
        'series' => $payload['series'],
        'edition' => $payload['edition'],
        'remark' => $payload['remark'],
        'topic' => $payload['topic'],
        'location_1' => $payload['location_1'],
        'location_2' => $payload['location_2'],
        'isbn' => $payload['isbn'],
        'binding' => $payload['binding'],
        'publisher' => $payload['publisher'],
        'publisher_place' => $payload['publisher_place'],
        'published_date' => $payload['published_date'],
        'book_page' => $payload['book_page'],
        'words' => $payload['words'],
        'book_size' => $payload['book_size'],
        'cdrom' => $payload['cdrom'],
        'purchase_value' => $payload['purchase_value'],
        'lost_penalty_value' => $payload['lost_penalty_value']
    ]);

    //author_book table inserted 2 records
    $this->assertDatabaseCount($this->authorBookTable, 2);

    $this->assertDatabaseCount('master_authors', 2);
    foreach ($payload['authors'] as $author) {
        $this->assertDatabaseHas('master_authors', [
            'name' => $author
        ]);

        $this->assertDatabaseHas($this->authorBookTable, [
            'book_id' => $response['id'],
            'author_id' => Author::where('name', $author)->first()->id
        ]);
    }

    //book_loan_settings table inserted 4 records
    $this->assertDatabaseCount($this->bookLoanSettingTable, 4);

    foreach ($payload['loan_settings'] as $type => $value) {
        $this->assertDatabaseHas($this->bookLoanSettingTable, [
            'book_id' => $response['id'],
            'type' => $type,
            'loan_period_day' => $value['loan_period_day'],
            'can_borrow' => $value['can_borrow'],
        ]);
    }

    //update with id not exist
    $payload['title'] = 'test 3';

    $this->expectException(ModelNotFoundException::class);
    $this->bookService->updateBook(9999, $payload)->toArray();
});

test('deleteBook()', function () {
    $book = Book::factory()->create();
    $other_books = Book::factory(3)->create();

    $this->assertDatabaseCount($this->table, 4);

    //delete success
    $this->bookService->deleteBook($book->id);

    $this->assertDatabaseCount($this->table, 3);
    $this->assertDatabaseMissing($this->table, ['id' => $book->id]);

    foreach ($other_books as $other_book) {
        $this->assertDatabaseHas($this->table, ['id' => $other_book->id]);
    }

    //id not exist
    $this->expectException(ModelNotFoundException::class);
    $this->bookService->deleteBook(9999);
});


test('deleteBook() failed due to used in existing loans', function () {

    $book = Book::factory()->create();

    $student = Student::factory()->create();

    $member_student = LibraryMember::factory()->create([
        'type' => LibraryMemberType::STUDENT,
        'userable_type' => Student::class,
        'userable_id' => $student->id,
    ]);

    LibraryBookLoan::factory()->create([
        'member_id' => $member_student->id,
        'book_id' => $book->id,
        'loan_status' => LibraryBookLoanStatus::RETURNED,
        'due_date' => now(),
    ]);

    $this->assertDatabaseCount($this->table, 1);

    //delete error
    $this->expectException(\App\Exceptions\CannotDeleteModelException::class);
    $this->bookService->deleteBook($book->id);

});


test('findLoanSettingVia()', function () {
    $book = Book::factory()->create();

    BookLoanSetting::factory(4)
        ->state(new Sequence(
            [
                'book_id' => $book->id,
                'type' => BookLoanSettingType::STUDENT->value,
            ],
            [
                'book_id' => $book->id,
                'type' => BookLoanSettingType::EMPLOYEE->value,
            ],
            [
                'book_id' => $book->id,
                'type' => BookLoanSettingType::LIBRARIAN->value,
            ],
            [
                'book_id' => $book->id,
                'type' => BookLoanSettingType::OTHERS->value,
            ],
        ))
        ->create();

    $response = $this->bookService->findLoanSettingVia($book->id, LibraryMemberType::EMPLOYEE)->toArray();

    expect($response)->toMatchArray([
        'book_id' => $book->id,
        'type' => BookLoanSettingType::EMPLOYEE->value,
    ]);
});

test('findBook()', function () {
    $books = Book::factory(3)->create();

    $response = $this->bookService->findBook($books[0]->id)->toArray();

    expect($response)->toMatchArray($books[0]->toArray());
});

test('recoverLostBook()', function () {
    $books = Book::factory(3)
        ->state(new Sequence(
            [
                'status' => BookStatus::AVAILABLE
            ],
            [
                'status' => BookStatus::BORROWED
            ],
            [
                'status' => BookStatus::LOST
            ],
        ))
        ->create();

    //Validation book must be lost
    expect(function () use ($books) {
        $this->bookService->recoverLostBook($books[0]);
    })->toThrow(function (Exception $e) {
        expect($e->getCode())->toBe(23001);
    }, 'Only lost book can be recovered.');

    //Success
    $book = $this->bookService->recoverLostBook($books[2]);

    expect($book)->toMatchArray([
        'id' => $books[2]->id,
        'status' => BookStatus::AVAILABLE->value
    ]);

    $this->assertDatabaseHas($this->table, [
        'id' => $books[2]->id,
        'status' => BookStatus::AVAILABLE->value
    ]);
});

test('createBook() - automatically sets status based on condition', function () {
    $book_classification = BookClassification::factory()->create();
    $book_sub_classification = BookSubClassification::factory()->create([
        'book_classification_id' => $book_classification->id,
    ]);
    $book_category = BookCategory::factory()->create();
    $book_source = BookSource::factory()->create();

    $base_payload = [
        'book_no' => '123457',
        'call_no' => fake()->sentence(),
        'book_category_id' => $book_category->id,
        'book_classification_id' => $book_classification->id,
        'book_sub_classification_id' => $book_sub_classification->id,
        'book_source_id' => $book_source->id,
        'title' => fake()->title,
        'binding' => BookBinding::HARD_COVER->value,
        'purchase_value' => 10,
        'lost_penalty_value' => 20,
        'authors' => ['Test Author'],
        'loan_settings' => [
            BookLoanSettingType::STUDENT->value => [
                'loan_period_day' => 10,
                'can_borrow' => true
            ],
        ]
    ];

    // Test WRITE_OFF condition sets status to NOT_AVAILABLE
    $payload = array_merge($base_payload, [
        'book_no' => '123458',
        'condition' => BookCondition::WRITE_OFF->value
    ]);

    $book = $this->bookService->createBook($payload);

    expect($book->status)->toBe(BookStatus::NOT_AVAILABLE)
        ->and($book->condition)->toBe(BookCondition::WRITE_OFF);

    // Test MISSING condition sets status to LOST
    $payload = array_merge($base_payload, [
        'book_no' => '123459',
        'condition' => BookCondition::MISSING->value
    ]);

    $book = $this->bookService->createBook($payload);

    expect($book->status)->toBe(BookStatus::LOST)
        ->and($book->condition)->toBe(BookCondition::MISSING);

    // Test GOOD condition sets status to AVAILABLE
    $payload = array_merge($base_payload, [
        'book_no' => '123460',
        'condition' => BookCondition::GOOD->value
    ]);

    $book = $this->bookService->createBook($payload);

    expect($book->status)->toBe(BookStatus::AVAILABLE)
        ->and($book->condition)->toBe(BookCondition::GOOD);

    // Test DAMAGE condition sets status to AVAILABLE
    $payload = array_merge($base_payload, [
        'book_no' => '123461',
        'condition' => BookCondition::DAMAGE->value
    ]);

    $book = $this->bookService->createBook($payload);

    expect($book->status)->toBe(BookStatus::AVAILABLE)
        ->and($book->condition)->toBe(BookCondition::DAMAGE);
});

test('updateBook() - automatically sets status based on condition', function () {
    $book = Book::factory()->create([
        'status' => BookStatus::AVAILABLE,
        'condition' => BookCondition::GOOD
    ]);

    $base_payload = [
        'book_no' => $book->book_no,
        'call_no' => $book->call_no,
        'book_category_id' => $book->book_category_id,
        'book_classification_id' => $book->book_classification_id,
        'book_sub_classification_id' => $book->book_sub_classification_id,
        'book_source_id' => $book->book_source_id,
        'title' => $book->title,
        'binding' => $book->binding->value,
        'purchase_value' => $book->purchase_value,
        'lost_penalty_value' => $book->lost_penalty_value,
        'authors' => ['Test Author'],
        'loan_settings' => [
            BookLoanSettingType::STUDENT->value => [
                'loan_period_day' => 10,
                'can_borrow' => true
            ],
        ]
    ];

    // Test WRITE_OFF condition sets status to NOT_AVAILABLE
    $payload = array_merge($base_payload, ['condition' => BookCondition::WRITE_OFF->value]);
    $updated_book = $this->bookService->updateBook($book->id, $payload);

    expect($updated_book->status)->toBe(BookStatus::NOT_AVAILABLE)
        ->and($updated_book->condition)->toBe(BookCondition::WRITE_OFF);

    // Test MISSING condition sets status to LOST
    $payload = array_merge($base_payload, ['condition' => BookCondition::MISSING->value]);
    $updated_book = $this->bookService->updateBook($book->id, $payload);

    expect($updated_book->status)->toBe(BookStatus::LOST)
        ->and($updated_book->condition)->toBe(BookCondition::MISSING);

    // Test GOOD condition sets status to AVAILABLE
    $payload = array_merge($base_payload, ['condition' => BookCondition::GOOD->value]);
    $updated_book = $this->bookService->updateBook($book->id, $payload);

    expect($updated_book->status)->toBe(BookStatus::AVAILABLE)
        ->and($updated_book->condition)->toBe(BookCondition::GOOD);
});


