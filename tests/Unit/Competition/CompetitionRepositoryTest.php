<?php

use App\Enums\CompetitionBonusType;
use App\Models\Award;
use App\Models\ClassModel;
use App\Models\Competition;
use App\Models\CompetitionRecord;
use App\Models\Department;
use App\Models\SemesterClass;
use App\Models\SemesterSetting;
use App\Models\Student;
use App\Models\StudentClass;
use App\Repositories\CompetitionRepository;
use Database\Seeders\InternationalizationSeeder;

beforeEach(function () {
    $this->seed([
        InternationalizationSeeder::class,
    ]);
    
    $this->competitionRepository = resolve(CompetitionRepository::class);
});

test('getModelClass()', function () {
    $response = $this->competitionRepository->getModelClass();

    expect($response)->toEqual(Competition::class);
});

test('getAll()', function () {
    $first_competition = Competition::factory()->create();
    $second_competition = Competition::factory()->create();
    $third_competition = Competition::factory()->create();

    $response = $this->competitionRepository->getAll(['order_by' => ['id' => 'asc']])->toArray();

    expect($response)->sequence(
        fn($response) => $response->toEqual($first_competition->toArray()),
        fn($response) => $response->toEqual($second_competition->toArray()),
        fn($response) => $response->toEqual($third_competition->toArray()),
    );
});

test('getAllPaginated()', function (int $expected_count, string $filter_by, mixed $filter_value, array $expected_model) {
    $department1 = Department::factory()->create(['id' => '1200', 'name->en' => 'Department A']);
    $department2 = Department::factory()->create(['name->en' => 'Department B']);
    $department3 = Department::factory()->create(['name->en' => 'Department C']);

    $first_competition = Competition::factory()->create([
        'name' => 'Lompat Galah',
        'department_id' => $department2->id,
        'date' => now()->toDateString(),
    ]);
    CompetitionRecord::factory()->create([
        'competition_id' => $first_competition->id,
        'student_id' => Student::factory()->create([
            'name->en' => 'apple',
            'name->zh' => '苹果',
            'student_number' => '00001',
        ])->id,
    ]);

    $second_competition = Competition::factory()->create([
        'name' => 'Driving',
        'department_id' => $department1->id,
        'date' => now()->addDay()->toDateString(),
    ]);
    CompetitionRecord::factory()->create([
        'competition_id' => $second_competition->id,
        'student_id' => Student::factory()->create([
            'name->en' => 'watermelon',
            'name->zh' => '西瓜',
            'student_number' => '00002',
        ])->id,
    ]);

    $third_competition = Competition::factory()->create([
        'name' => 'Wheelie',
        'department_id' => $department3->id,
        'date' => '2010-01-01'
    ]);
    CompetitionRecord::factory()->create([
        'competition_id' => $third_competition->id,
        'student_id' => Student::factory()->create([
            'name->en' => 'mango',
            'name->zh' => '芒果',
            'student_number' => '00003',
        ])->id,
    ]);

    $keys = [
        'first' => $first_competition,
        'second' => $second_competition,
        'third' => $third_competition,
    ];

    $result = $this->competitionRepository->getAllPaginated([$filter_by => $filter_value])->toArray();

    expect($result['data'])->toHaveCount($expected_count);

    foreach ($expected_model as $key => $value) {
        expect($result['data'][$key])->toEqual($keys[$value]->toArray());
    }
})->with([
    'filter by name = Lompat Galah' => [1, 'name', 'Lompat Galah', ['first']],
    'filter by name = Non Existing' => [0, 'name', 'Non Existing', []],
    'filter by department_id = 1200' => [1, 'department_id', '1200', ['second']],
    'filter by department_id = Non Existing' => [0, 'department_id', '10000000', []],
    'filter by date = 2010-01-01' => [1, 'date', '2010-01-01', ['third']],
    'filter by date = Non Existing' => [0, 'date', '2009-01-01', []],
    'filter by student name = apple' => [1, 'student_name', 'apple', ['first']],
    'filter by student name = 西瓜' => [1, 'student_name', '西瓜', ['second']],
    'filter by student number = 00002' => [1, 'student_number', '00002', ['second']],
    'sort by id asc' => [3, 'order_by', ['id' => 'asc'], ['first', 'second', 'third']],
    'sort by id desc' => [3, 'order_by', ['id' => 'desc'], ['third', 'second', 'first']],
    'sort by name asc' => [3, 'order_by', ['name' => 'asc'], ['second', 'first', 'third']],
    'sort by name desc' => [3, 'order_by', ['name' => 'desc'], ['third', 'first', 'second']],
    'sort by department_id asc' => [3, 'order_by', ['department_id' => 'asc'], ['first', 'third', 'second']],
    'sort by department_id desc' => [3, 'order_by', ['department_id' => 'desc'], ['second', 'third', 'first']],
]);

test('studentPerformanceReportByDateRangeData()', function () {
    $department = Department::factory()->create([
        'name->en' => 'department A',
        'name->zh' => '部门 A',
    ]);
    $department2 = Department::factory()->create([
        'name->en' => 'department B',
        'name->zh' => '部门 B',
    ]);

    $award2 = Award::factory()->create([
        'name->en' => 'second place',
        'name->zh' => '第二名',
        'sequence' => 1,
    ]);
    $award = Award::factory()->create([
        'name->en' => 'first place',
        'name->zh' => '第一名',
        'sequence' => 0,
    ]);

    $semester_setting = SemesterSetting::factory()->create();

    $semester_class = SemesterClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'class_id' => ClassModel::factory()->create([
            'name->en' => 'A class',
            'name->zh' => '班级 A',
        ])->id,
    ]);

    $semester_class2 = SemesterClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'class_id' => ClassModel::factory()->create([
            'name->en' => 'B class',
            'name->zh' => '班级 B',
        ])->id,
    ]);

    $student = Student::factory()->create([
        'name->en' => 'student A',
        'name->zh' => '学生 A',
        'student_number' => '0001',
    ]);
    StudentClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'semester_class_id' => $semester_class->id,
        'student_id' => $student->id,
    ]);

    $student2 = Student::factory()->create([
        'name->en' => 'student B',
        'name->zh' => '学生 B',
        'student_number' => '0002',
    ]);
    StudentClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'semester_class_id' => $semester_class2->id,
        'student_id' => $student2->id,
    ]);

    // competition with 1 record
    $first_competition = Competition::factory()->create([
        'name' => 'Lompat Galah',
        'department_id' => $department->id,
        'date' => '2025-01-08',
    ]);
    $first_competition_record = CompetitionRecord::factory()->create([
        'competition_id' => $first_competition->id,
        'award_id' => $award->id,
        'student_id' => $student->id,
        'semester_class_id' => $semester_class->id,
        'type_of_bonus' => CompetitionBonusType::PERFORMANCE->value,
        'mark' => 4,
    ]);
    // competition with 2 records
    $second_competition = Competition::factory()->create([
        'name' => 'Driving',
        'department_id' => $department2->id,
        'date' => '2025-01-09',
    ]);
    $second_competition_record = CompetitionRecord::factory()->create([
        'competition_id' => $second_competition->id,
        'award_id' => $award2->id,
        'student_id' => $student->id,
        'semester_class_id' => $semester_class->id,
        'type_of_bonus' => CompetitionBonusType::OFF_CAMPUS->value,
        'mark' => 5,
    ]);
    $second_competition_record2 = CompetitionRecord::factory()->create([
        'competition_id' => $second_competition->id,
        'award_id' => $award->id,
        'student_id' => $student2->id,
        'semester_class_id' => $semester_class2->id,
        'type_of_bonus' => CompetitionBonusType::OFF_CAMPUS->value,
        'mark' => 6,
    ]);

    app()->setLocale('zh');
    $data = $this->competitionRepository->studentPerformanceReportByDateRangeData('2025-01-08', '2025-01-09');

    expect($data)->toEqual([
        "header" => [
            "序",
            "项目",
            "奖项",
            "日期",
            "部门",
            "学生编号",
            "学生姓名",
            "班级",
            "年份",
            "奖励分数类型",
            "奖励分数",
        ],
        "body" => [
            $first_competition_record->id => [
                "no" => 1,
                "competition_name" => $first_competition->name,
                "award_name" => "第一名",
                "competition_date" => "2025-01-08",
                "department_name" => "部门 A",
                "student_number" => "0001",
                "student_name" => "学生 A",
                "class_name" => "班级 A",
                "competition_year" => 2025,
                "type_of_bonus" => CompetitionBonusType::PERFORMANCE->value,
                "mark_of_bonus" => "4",
            ],
            $second_competition_record2->id => [
                "no" => 2,
                "competition_name" => $second_competition->name,
                "award_name" => "第一名",
                "competition_date" => "2025-01-09",
                "department_name" => "部门 B",
                "student_number" => "0002",
                "student_name" => "学生 B",
                "class_name" => "班级 B",
                "competition_year" => 2025,
                "type_of_bonus" => CompetitionBonusType::OFF_CAMPUS->value,
                "mark_of_bonus" => "6",
            ],
            $second_competition_record->id => [
                "no" => 3,
                "competition_name" => $second_competition->name,
                "award_name" => "第二名",
                "competition_date" => "2025-01-09",
                "department_name" => "部门 B",
                "student_number" => "0001",
                "student_name" => "学生 A",
                "class_name" => "班级 A",
                "competition_year" => 2025,
                "type_of_bonus" => CompetitionBonusType::OFF_CAMPUS->value,
                "mark_of_bonus" => "5",
            ],
        ],
    ]);

    app()->setLocale('en');
    $data = $this->competitionRepository->studentPerformanceReportByDateRangeData('2025-01-08', '2025-01-09');

    expect($data)->toEqual([
        "header" => [
            "No",
            "Item",
            "Award",
            "Date",
            "Department",
            "Student Number",
            "Student Name",
            "Class",
            "Year",
            "Type of Bonus",
            "Mark of Bonus",
        ],
        "body" => [
            $first_competition_record->id => [
                "no" => 1,
                "competition_name" => $first_competition->name,
                "award_name" => "first place",
                "competition_date" => "2025-01-08",
                "department_name" => "department A",
                "student_number" => "0001",
                "student_name" => "student A",
                "class_name" => "A class",
                "competition_year" => 2025,
                "type_of_bonus" => CompetitionBonusType::PERFORMANCE->value,
                "mark_of_bonus" => "4",
            ],
            $second_competition_record2->id => [
                "no" => 2,
                "competition_name" => $second_competition->name,
                "award_name" => "first place",
                "competition_date" => "2025-01-09",
                "department_name" => "department B",
                "student_number" => "0002",
                "student_name" => "student B",
                "class_name" => "B class",
                "competition_year" => 2025,
                "type_of_bonus" => CompetitionBonusType::OFF_CAMPUS->value,
                "mark_of_bonus" => "6",
            ],
            $second_competition_record->id => [
                "no" => 3,
                "competition_name" => $second_competition->name,
                "award_name" => "second place",
                "competition_date" => "2025-01-09",
                "department_name" => "department B",
                "student_number" => "0001",
                "student_name" => "student A",
                "class_name" => "A class",
                "competition_year" => 2025,
                "type_of_bonus" => CompetitionBonusType::OFF_CAMPUS->value,
                "mark_of_bonus" => "5",
            ],
        ],
    ]);
});
