<?php

use App\Enums\DietaryRestriction;
use App\Enums\EnrollmentPaymentStatus;
use App\Enums\EnrollmentStatus;
use App\Enums\Gender;
use App\Enums\GuardianType;
use App\Enums\LiveStatus;
use App\Enums\MarriedStatus;
use App\Enums\SchoolLevel;
use App\Enums\StudentAdmissionType;
use App\Helpers\ConfigHelper;
use App\Jobs\NotifyGuardianOfEnrollmentStatusJob;
use App\Jobs\RenotifyGuardianOfEnrollmentStatusJob;
use App\Mail\EnrollmentStatusMail;
use App\Models\BillingDocument;
use App\Models\Country;
use App\Models\Course;
use App\Models\Enrollment;
use App\Models\EnrollmentExam;
use App\Models\EnrollmentExamMark;
use App\Models\EnrollmentGuardian;
use App\Models\EnrollmentSession;
use App\Models\EnrollmentUser;
use App\Models\Grade;
use App\Models\GuardianStudent;
use App\Models\HealthConcern;
use App\Models\Payment;
use App\Models\Product;
use App\Models\Race;
use App\Models\Religion;
use App\Models\School;
use App\Models\State;
use App\Models\Student;
use App\Models\Subject;
use App\Services\EnrollmentService;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\Sequence;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Queue;
use PhpOffice\PhpSpreadsheet\Shared\Date;

beforeEach(function () {
    $this->seedAccountingData();

    $this->enrollmentService = app(EnrollmentService::class);

    app()->setLocale('en');

    $this->locale = app()->getLocale();

    $this->enrollment_table_name = resolve(Enrollment::class)->getTable();
    $this->guardian_student_table_name = resolve(GuardianStudent::class)->getTable();
});

test('getAllPaginatedEnrollments()', function () {
    $enrollments = Enrollment::factory(2)->create();

    $this->mock(EnrollmentService::class, function (\Mockery\MockInterface $mock) use ($enrollments) {
        $mock->shouldReceive('getAllPaginatedEnrollments')
            ->once()
            ->andReturn(new LengthAwarePaginator($enrollments, 2, 1));
    });

    $response = app()->make(EnrollmentService::class)
        ->getAllPaginatedEnrollments()
        ->toArray();

    expect($response['data'])->toHaveCount(2)
        ->toHaveKey('0.id', $enrollments[0]->id)
        ->toHaveKey('1.id', $enrollments[1]->id);
});

test('getAllEnrollments()', function () {
    $enrollments = Enrollment::factory(2)->create();

    $this->mock(EnrollmentService::class, function (\Mockery\MockInterface $mock) use ($enrollments) {
        $mock->shouldReceive('getAllEnrollments')
            ->once()
            ->andReturn($enrollments);
    });

    $response = app()->make(EnrollmentService::class)
        ->getAllEnrollments()
        ->toArray();

    expect($response)->toHaveCount(2)
        ->toHaveKey('0.id', $enrollments[0]->id)
        ->toHaveKey('1.id', $enrollments[1]->id);
});

test('billingDocumentVoidedActionCallback', function () {

    // pending -> pending  OK
    $enrollment = Enrollment::factory()->create([
        'enrollment_status' => EnrollmentStatus::PENDING_PAYMENT,
    ]);

    $enrollment->billingDocumentVoidedActionCallback();

    $this->assertDatabaseHas(Enrollment::class, [
        'id' => $enrollment->id,
        'enrollment_status' => EnrollmentStatus::PENDING_PAYMENT,
    ]);


    // failed -> pending  OK
    $enrollment = Enrollment::factory()->create([
        'enrollment_status' => EnrollmentStatus::PAYMENT_FAILED,
    ]);

    $enrollment->billingDocumentVoidedActionCallback();

    $this->assertDatabaseHas(Enrollment::class, [
        'id' => $enrollment->id,
        'enrollment_status' => EnrollmentStatus::PENDING_PAYMENT,
    ]);


    // SUBMITTED -> unpaid not gonna run
    $enrollment = Enrollment::factory()->create([
        'enrollment_status' => EnrollmentStatus::SUBMITTED,
    ]);

    $enrollment->billingDocumentVoidedActionCallback();

    $this->assertDatabaseHas(Enrollment::class, [
        'id' => $enrollment->id,
        'enrollment_status' => EnrollmentStatus::SUBMITTED,
    ]);


    // APPROVED -> unpaid not gonna run
    $enrollment = Enrollment::factory()->create([
        'enrollment_status' => EnrollmentStatus::APPROVED,
    ]);

    $enrollment->billingDocumentVoidedActionCallback();

    $this->assertDatabaseHas(Enrollment::class, [
        'id' => $enrollment->id,
        'enrollment_status' => EnrollmentStatus::APPROVED,
    ]);


    // DRAFT -> unpaid not gonna run
    $enrollment = Enrollment::factory()->create([
        'enrollment_status' => EnrollmentStatus::DRAFT,
    ]);

    $enrollment->billingDocumentVoidedActionCallback();

    $this->assertDatabaseHas(Enrollment::class, [
        'id' => $enrollment->id,
        'enrollment_status' => EnrollmentStatus::DRAFT,
    ]);


    // ENROLLED -> unpaid not gonna run
    $enrollment = Enrollment::factory()->create([
        'enrollment_status' => EnrollmentStatus::ENROLLED,
    ]);

    $enrollment->billingDocumentVoidedActionCallback();

    $this->assertDatabaseHas(Enrollment::class, [
        'id' => $enrollment->id,
        'enrollment_status' => EnrollmentStatus::ENROLLED,
    ]);
});

test('getTemplateData() - for post and pre payment', function () {
    $enrollment_session = EnrollmentSession::factory()->create([
        'name' => 'Session 2025',
        'from_date' => '2025-01-01',
        'to_date' => '2025-03-01',
        'code' => '0001',
    ]);

    $subjects = Subject::factory(4)->state(new Sequence(
        [
            'code' => 'ENGLISH',
            'name->en' => 'English',
        ],
        [
            'code' => 'MATHS',
            'name->en' => 'Mathematics',
        ],
        [
            'code' => 'SCIENCE',
            'name->en' => 'Science',
        ],
        [
            'code' => 'ART',
            'name->en' => 'Art',
        ],
    ))->create();

    $enrollment_session->examSubjects()->attach($subjects);

    /**
     * invalid enrollment session id
     */

    $response = $this->enrollmentService->getTemplateData(110000, false);

    expect($response)->toBeArray()->toHaveCount(0);

    /**
     * get full template data
     */

    $expected_response = [
        'number' => '1',
        'exam_slip_number' => 'R6566 - SAMPLEDATA',
        'student_name_en' => 'John Doe - SAMPLEDATA',
        'student_name_zh' => '约翰·多 - SAMPLEDATA',
        'nric' => '110113101982 - SAMPLEDATA',
        'passport_number' => 'A1234567 - SAMPLEDATA',
        'religion' => 'Christian - SAMPLEDATA',
        'gender' => 'MALE - SAMPLEDATA',
        'guardian_phone_number' => '********** - SAMPLEDATA',
        'guardian_email' => '<EMAIL> - SAMPLEDATA',
        'guardian_name' => 'Jane Doe - SAMPLEDATA',
        'guardian_type' => 'MOTHER - SAMPLEDATA',
        'total_average' => '80.05',
        'status' => 'APPROVED - SAMPLEDATA',
        'address' => '123 Sample Street - SAMPLEDATA',
        'primary_school' => 'Pin Hwa - SAMPLEDATA',
        'hostel' => 'TRUE - SAMPLEDATA',
        'hostel_reason' => 'Home is 5km away - SAMPLEDATA',
        'have_siblings' => 'FALSE - SAMPLEDATA',
        'dietary_restriction' => 'NONE - SAMPLEDATA',
        'health_concern' => 'NONE - SAMPLEDATA',
        'foreigner' => 'FALSE - SAMPLEDATA',
        'nationality' => 'Malaysia - SAMPLEDATA',
        'conduct' => 'A+ - SAMPLEDATA',
        'remarks' => 'remarks - SAMPLEDATA',
        'register_date' => '2021/01/01 - SAMPLEDATA',
        'expiry_date' => '2021/01/01 - SAMPLEDATA',
    ];

    expect($enrollment_session->examSubjects)->toHaveCount(4);

    foreach ($enrollment_session->examSubjects->sortBy('code') as $subject) {
        $expected_response[$subject->code] = '80.05';
    }

    // get full template
    $response = $this->enrollmentService->getTemplateData($enrollment_session->id, false);

    expect($response['data'])->toEqual([$expected_response]);

    foreach ($enrollment_session->examSubjects as $subject) {
        expect($response['subject_lists'])->toHaveKey($subject->code);
    }


    /**
     * get lesser template data
     */

    $expected_response_2 = [
        'number' => '1',
        'exam_slip_number' => 'R6566 - SAMPLEDATA',
        'student_name_en' => 'John Doe - SAMPLEDATA',
        'nric' => '110113101982 - SAMPLEDATA',
        'passport_number' => 'A1234567 - SAMPLEDATA',
        'hostel' => 'TRUE - SAMPLEDATA',
        'total_average' => '80.05',
        'status' => 'APPROVED - SAMPLEDATA',
    ];

    foreach ($enrollment_session->examSubjects->sortBy('code') as $subject) {
        $expected_response_2[$subject->code] = '80.05';
    }

    // get post payment template
    $response_2 = $this->enrollmentService->getTemplateData($enrollment_session->id, true);

    expect($response_2['data'])->toEqual([$expected_response_2]);

    foreach ($enrollment_session->examSubjects as $subject) {
        expect($response_2['subject_lists'])->toHaveKey($subject->code);
    }
});


test('canBeUpdated()', function () {
    // no date set, can be updated
    $enrollment = Enrollment::factory()->create([
        'expiry_date' => null
    ]);

    expect($enrollment->canBeUpdated())->toBeTruthy();


    // expiry date set, but past date
    $enrollment->update([
        'expiry_date' => now()->subDays(1)->toDateString(),
    ]);

    expect($enrollment->canBeUpdated())->toBeFalsy();


    // expiry date set, but future date
    $enrollment->update([
        'expiry_date' => now()->addDays(1)->toDateString(),
    ]);

    expect($enrollment->canBeUpdated())->toBeTruthy();
});

test('transformExcelToCollection()', function () {
    $enrollment_session = EnrollmentSession::factory()->create([
        'name' => 'Session 2025',
        'from_date' => '2025-01-01',
        'to_date' => '2025-03-01',
        'code' => '0001',
    ]);

    $subjects = Subject::factory(2)->state(new Sequence(
        [
            'code' => '002',
            'name->en' => 'English',
        ],
        [
            'code' => '001',
            'name->en' => 'Mathematics',
        ],
    ))->create();

    $enrollment_session->examSubjects()->attach($subjects);

    // convert Enrollment Template Excel to collection
    $header = [
        'No.',
        '考生编号 / Exam Slip',
        'Status',
        'Expiry Date',
        'Register Date',
        '学生姓名',
        'Student Name',
        '性别 / Gender',
        '身份证号码 / IC Number',
        'Passport Number',
        '外藉生/Foreigner (True, False)',
        '国籍/Nationality',
        'Religion',
        '地址 / Address',
        '宿舍 / Hostel (True, False)',
        '原因/Reason',
        '膳食 / Dietary Restrictions',
        '健康问题 / Health Concern',
        '兄弟姐妹 / Have Siblings (True, False)',
        '就读小学 / Primary School',
        '总平均 / Total Average',
        '短文 / Short Essay',
        '华 / CN',
        '操行 / Conduct',
        '监护人类别 / Guardian Type',
        '监护人姓名 / Guardian Name',
        '监护人电话号码 / Guardian Phone Number',
        'Guardian Email',
        '备注 / Remarks',
    ];

    $row1 = [
        '1',
        'R6566',
        'APPROVED',
        Date::dateTimeToExcel(Carbon::parse('2025-01-20')),
        Date::dateTimeToExcel(Carbon::parse('2025-01-01')),
        '约翰·多',
        'John Doe',
        'MALE',
        '110113101982',
        'A1234567',
        'TRUE',
        'Malaysia',
        'Christian',
        '123 Sample Street',
        'TRUE',
        'Home is 5km away',
        'VEGETARIAN',
        'Adhd',
        'TRUE',
        'Pin Hwa',
        '82.05',
        '80.05',
        '81.05',
        'A+',
        'FATHER',
        'Frank Reynold',
        '6**********',
        '<EMAIL>',
        'remarks',
    ];

    $row2 = [
        '2',
        'R6566',
        'REJECTED',
        Date::dateTimeToExcel(Carbon::parse('2025-01-20')),
        Date::dateTimeToExcel(Carbon::parse('2025-01-01')),
        '约翰·多',
        'John Cena',
        'MALE',
        '110113101982',
        'A1234568',
        'TRUE',
        'Malaysia',
        'Buddhism',
        '555 Sample Street',
        'TRUE',
        'Home is 5km away',
        'VEGETARIAN',
        'Adhd',
        'TRUE',
        'SK KB',
        '83.05',
        '81.05',
        '82.05',
        'B',
        'MOTHER',
        'Dee Reynold',
        '60123456790',
        '<EMAIL>',
        'yohoo',
    ];

    $test_excel = createFakeExcelFileWithData([
        $header,
        $row1,
        $row2,
    ]);

    $response = $this->enrollmentService
        ->setImportFile($test_excel)
        ->setEnrollmentSession($enrollment_session)
        ->transformExcelToCollection();

    expect($response->getData())->toEqual([
        [
            'number' => mb_strtoupper($row1[0]),
            'exam_slip_number' => mb_strtoupper($row1[1]),
            'status' => mb_strtoupper($row1[2]),
            'expiry_date' => '2025-01-20',
            'register_date' => '2025-01-01',
            'student_name_zh' => mb_strtoupper($row1[5]),
            'student_name_en' => mb_strtoupper($row1[6]),
            'gender' => mb_strtoupper($row1[7]),
            'nric' => mb_strtoupper($row1[8]),
            'passport_number' => mb_strtoupper($row1[9]),
            'foreigner' => true,
            'nationality' => $row1[11],
            'religion' => $row1[12],
            'address' => mb_strtoupper($row1[13]),
            'hostel' => true,
            'hostel_reason' => $row1[15],
            'dietary_restriction' => $row1[16],
            'health_concern' => $row1[17],
            'have_siblings' => true,
            'primary_school' => $row1[19],
            'total_average' => $row1[20],
            "001" => $row1[21], // sorted by code
            "002" => $row1[22],
            'conduct' => $row1[23],
            'guardian_type' => mb_strtoupper($row1[24]),
            'guardian_name' => mb_strtoupper($row1[25]),
            'guardian_phone_number' => $row1[26],
            'guardian_email' => $row1[27],
            'remarks' => $row1[28],
        ],
        [
            'number' => mb_strtoupper($row2[0]),
            'exam_slip_number' => mb_strtoupper($row2[1]),
            'status' => mb_strtoupper($row2[2]),
            'expiry_date' => '2025-01-20',
            'register_date' => '2025-01-01',
            'student_name_zh' => mb_strtoupper($row2[5]),
            'student_name_en' => mb_strtoupper($row2[6]),
            'gender' => mb_strtoupper($row2[7]),
            'nric' => mb_strtoupper($row2[8]),
            'passport_number' => mb_strtoupper($row2[9]),
            'foreigner' => true,
            'nationality' => $row2[11],
            'religion' => $row2[12],
            'address' => mb_strtoupper($row2[13]),
            'hostel' => true,
            'hostel_reason' => $row2[15],
            'dietary_restriction' => $row2[16],
            'health_concern' => $row2[17],
            'have_siblings' => true,
            'primary_school' => $row2[19],
            'total_average' => $row2[20],
            "001" => $row2[21], // sorted by code
            "002" => $row2[22],
            'conduct' => $row2[23],
            'guardian_type' => mb_strtoupper($row2[24]),
            'guardian_name' => mb_strtoupper($row2[25]),
            'guardian_phone_number' => $row2[26],
            'guardian_email' => $row2[27],
            'remarks' => $row2[28],
        ]
    ]);
});

test('transformPostPaymentExcelToCollection()', function () {
    $enrollment_session = EnrollmentSession::factory()->create([
        'name' => 'Session 2025',
        'from_date' => '2025-01-01',
        'to_date' => '2025-03-01',
        'code' => '0001',
    ]);

    $subjects = Subject::factory(2)->state(new Sequence(
        [
            'code' => '002',
            'name->en' => 'English',
        ],
        [
            'code' => '001',
            'name->en' => 'Mathematics',
        ],
    ))->create();

    $enrollment_session->examSubjects()->attach($subjects);

    // convert Enrollment Template (POST PAYMENT) Excel to collection
    $row1 = ['1', 'SLIP_EXAM_001', 'John Doe', '110113101982', 'A1234567', 'TRUE', '82.05', '80.05', '81.05', 'APPROVED'];

    $row2 = ['2', 'SLIP_EXAM_002', 'John Cena', '110113101982', 'A1234568', 'TRUE', '83.05', '81.05', '82.05', 'REJECTED'];

    $header = [
        'No', '考生编号/Exam Slip', 'Student Name', '身份证号码 / IC Number', 'Passport Number', '宿舍 / Hostel (TRUE, FALSE)',
        '总平均 / Total Average', 'English Language', 'Mathematics', 'Status (APPROVED, REJECTED, SHORLISTED)'
    ];

    $test_excel = createFakeExcelFileWithData([
        $header,
        $row1,
        $row2,
    ]);

    $response = $this->enrollmentService
        ->setImportFile($test_excel)
        ->setEnrollmentSession($enrollment_session)
        ->transformPostPaymentExcelToCollection();

    expect($response->getData())->toEqual([
        [
            'number' => $row1[0],
            'exam_slip_number' => $row1[1],
            'student_name_en' => mb_strtoupper($row1[2]),
            'nric' => $row1[3],
            'passport_number' => $row1[4],
            'hostel' => true,
            'total_average' => $row1[6],
            "001" => $row1[7], // sorted by code
            "002" => $row1[8],
            'status' => $row1[9],
        ],
        [
            'number' => $row2[0],
            'exam_slip_number' => $row2[1],
            'student_name_en' => mb_strtoupper($row2[2]),
            'nric' => $row2[3],
            'passport_number' => $row2[4],
            'hostel' => true,
            'total_average' => $row2[6],
            "001" => $row2[7], // sorted by code
            "002" => $row2[8],
            'status' => $row2[9],
        ]
    ]);
});

test('validateBaseForImport', function () {
    $service = app(EnrollmentService::class);

    $enrollment_session = EnrollmentSession::factory()->create();

    $service->setEnrollmentSession($enrollment_session);

    // Valid data with all required fields
    $valid_data = [
        [
            'number' => '1',
            'exam_slip_number' => 'E1234',
            'student_name_en' => 'John Doe',
            'student_name_zh' => '约翰',
            'nric' => '123456789012',
            'passport_number' => 'A1234567',
            'status' => 'SHORTLISTED',
            'religion' => 'Christian',
            'gender' => 'MALE',
            'guardian_name' => 'Jane Doe',
            'guardian_type' => 'MOTHER',
            'guardian_phone_number' => '12345678',
            'guardian_email' => '<EMAIL>',
            'address' => '123 Street',
            'primary_school' => 'ABC School',
            'total_average' => '85.5',
            'hostel' => true,
            'hostel_reason' => '',
            'have_siblings' => true,
            'dietary_restriction' => 'NONE',
            'health_concern' => 'None',
            'foreigner' => false,
            'nationality' => '',
            'conduct' => 'A',
            'remarks' => 'Good student'
        ]
    ];

    // Test valid data - shouldn't have errors
    $service->setData($valid_data);
    $result = $service->validateBaseForImport()->getData();
    expect($result[0])->not->toHaveKey('errors');

    // Test missing required field
    $invalid_data = $valid_data;
    $invalid_data[0]['student_name_en'] = '';

    $service->setData($invalid_data);
    $result = $service->validateBaseForImport()->getData();
    expect($result[0]['errors']['student_name_en'][0])->toBe('The Student Name En field is required.');

    // Test missing both NRIC and passport
    $invalid_data = $valid_data;
    $invalid_data[0]['nric'] = '';
    $invalid_data[0]['passport_number'] = '';

    $service->setData($invalid_data);
    $result = $service->validateBaseForImport()->getData();
    expect($result[0]['errors']['nric'][0])->toBe('Either NRIC or Passport number must be filled.');

    // Test NRIC not 12 digits
    $invalid_data = $valid_data;
    $invalid_data[0]['nric'] = '12345';

    $service->setData($invalid_data);
    $result = $service->validateBaseForImport()->getData();
    expect($result[0]['errors']['nric'][0])->toBe('NRIC must be 12 digits.');

    // Test duplicate NRIC
    $duplicateData = [
        $valid_data[0],
        array_merge($valid_data[0], ['number' => '2', 'nric' => '123456789012'])
    ];

    $service->setData($duplicateData);
    $result = $service->validateBaseForImport()->getData();
    expect($result[1]['errors']['nric'][0])->toBe('NRIC is duplicated.');

    // Test duplicate passport number
    $duplicatePassportData = [
        $valid_data[0],
        array_merge($valid_data[0], ['number' => '2', 'nric' => '987654321098', 'passport_number' => 'A1234567'])
    ];

    $service->setData($duplicatePassportData);
    $result = $service->validateBaseForImport()->getData();
    expect($result[1]['errors']['passport_number'][0])->toBe('Passport number is duplicated.');


    // Test duplicate exam slip numbers
    $duplicate_exam_slip_numbers = [
        $valid_data[0],
        array_merge($valid_data[0], [
            'number' => '2',
            'nric' => '987654321011',
            'passport_number' => 'A1234167',
            'exam_slip_number' => 'E1234'
        ])
    ];

    $service->setData($duplicate_exam_slip_numbers);
    $result = $service->validateBaseForImport()->getData();
    expect($result[1]['errors']['exam_slip_number'][0])->toBe('Exam slip number is duplicated.');

    // Test empty exam slip numbers
    $new_valid_data = [
        array_merge($valid_data[0], [
            'number' => '1',
            'nric' => '987654321011',
            'passport_number' => 'A1234167',
            'exam_slip_number' => ''
        ]),
        array_merge($valid_data[0], [
            'number' => '2',
            'nric' => '987654321012',
            'passport_number' => 'A1234168',
            'exam_slip_number' => ''
        ]),
        array_merge($valid_data[0], [
            'number' => '2',
            'nric' => '987654321013',
            'passport_number' => 'A1234169',
            'exam_slip_number' => 'E12345'
        ])
    ];
    $service->setData($new_valid_data);

    $result = $service->validateBaseForImport()->getData();
    expect($result[0]['warnings']['exam_slip_number'][0])->toBe('Exam slip number is missing, marks will not be imported.');
    expect($result[1]['warnings']['exam_slip_number'][0])->toBe('Exam slip number is missing, marks will not be imported.');
    expect($result[2])->not->toHaveKey('warnings');

    // Test - guardian_email format is invalid
    $temp_data = [
        array_merge($valid_data[0], [
            'number' => '1',
            'guardian_email' => 'invalid-guardian_email-format',
        ]),
        array_merge($valid_data[0], [
            'number' => '2',
            'guardian_email' => '987654321012',
        ]),
        array_merge($valid_data[0], [
            'number' => '3',
            'guardian_email' => '=LOWER(SUBSTITUTE(A1, " ", "")) & "@example.com"',
        ]),
        array_merge($valid_data[0], [
            'number' => '4',
            'guardian_email' => '<EMAIL>',
        ])
    ];

    $service->setData($temp_data);
    $result = $service->validateBaseForImport()->getData();

    expect($result[0]['errors']['guardian_email'][0])->toBe(__('validation.email', ['attribute' => 'guardian email']));
    expect($result[1]['errors']['guardian_email'][0])->toBe(__('validation.email', ['attribute' => 'guardian email']));
    expect($result[2]['errors']['guardian_email'][0])->toBe(__('validation.email', ['attribute' => 'guardian email']));
    expect($result[3]['errors'])->not->toHaveKey('guardian_email');

    // Test - duplicate empty nric and passport number
    $temp_data = [
        array_merge($valid_data[0], [
            'number' => '1',
            'nric' => '999999999999',
            'passport_number' => 'A1234567',
            'exam_slip_number' => 'E1234',
        ]),
        array_merge($valid_data[0], [
            'number' => '2',
            'nric' => '999999999998',
            'passport_number' => '', // can be empty and not show duplicate passport_number
            'exam_slip_number' => 'E1235',
        ]),
        array_merge($valid_data[0], [
            'number' => '3',
            'nric' => '', // can be empty and not show duplicate nric
            'passport_number' => 'A1234568',
            'exam_slip_number' => 'E1236',
        ]),
        array_merge($valid_data[0], [
            'number' => '4',
            'nric' => '', // error
            'passport_number' => '', // error
            'exam_slip_number' => 'E1237',
        ]),
        array_merge($valid_data[0], [
            'number' => '5',
            'nric' => '999999999997',
            'passport_number' => '', // can be empty and not show duplicate passport_number
            'exam_slip_number' => 'E1238',
        ]),
        array_merge($valid_data[0], [
            'number' => '6',
            'nric' => '', // can be mpty and not show duplicate nric
            'passport_number' => 'A1234569',
            'exam_slip_number' => 'E1239',
        ]),
    ];


    $service->setData($temp_data);
    $result = $service->validateBaseForImport()->getData();

    expect($result[0])->not->toHaveKey('errors');
    expect($result[1])->not->toHaveKey('errors');
    expect($result[2])->not->toHaveKey('errors');
    expect($result[3])->toHaveKey('errors'); // have error because both nric and passport are empty
    expect($result[4])->not->toHaveKey('errors');
    expect($result[5])->not->toHaveKey('errors');


    // Test - empty total_average - total_average is now optional
    $temp_data = [
        array_merge($valid_data[0], [
            'number' => '1',
            'nric' => '999999999998',
            'passport_number' => '',
            'exam_slip_number' => 'E1234',
            'total_average' => '', // empty total_average
        ]),
        array_merge($valid_data[0], [
            'number' => '2',
            'nric' => '',
            'passport_number' => 'A1234568',
            'exam_slip_number' => 'E1235',
            'total_average' => '', // empty total_average
        ]),
    ];


    $service->setData($temp_data);
    $result = $service->validateBaseForImport()->getData();

    expect($result[0])->not->toHaveKey('errors');
    expect($result[1])->not->toHaveKey('errors');
});

test('validatePrePaymentInfoForImport', function () {
    $service = app(EnrollmentService::class);

    // Test invalid guardian type
    $invalid_guardian_data = [
        [
            'exam_slip_number' => 'E12345',
            'guardian_type' => 'INVALID_TYPE', // Invalid
            'have_siblings' => true,
            'dietary_restriction' => 'NONE',
            'foreigner' => false,
        ]
    ];

    $service->setData($invalid_guardian_data);
    $result = $service->validatePrePaymentInfoForImport()->getData();
    expect($result[0]['errors']['guardian_type'][0])->toBe('Guardian type must be GUARDIAN, FATHER or MOTHER.');

    // Test non-boolean for have_siblings field
    $invalid_siblings_data = [
        [
            'exam_slip_number' => 'E12345',
            'guardian_type' => 'FATHER',
            'have_siblings' => 'not-a-boolean', // Invalid
            'dietary_restriction' => 'NONE',
            'foreigner' => false,
        ]
    ];

    $service->setData($invalid_siblings_data);
    $result = $service->validatePrePaymentInfoForImport()->getData();
    expect($result[0]['errors']['have_siblings'][0])->toBe('Have siblings must be boolean.');

    // Test invalid dietary restriction
    $invalid_dietary_data = [
        [
            'exam_slip_number' => 'E12345',
            'guardian_type' => 'FATHER',
            'have_siblings' => true,
            'dietary_restriction' => 'INVALID_DIET', // Invalid
            'foreigner' => false,
        ]
    ];

    $service->setData($invalid_dietary_data);
    $result = $service->validatePrePaymentInfoForImport()->getData();
    expect($result[0]['errors']['dietary_restriction'][0])->toBe('Dietary restriction must be ' . implode(', ', DietaryRestriction::values()));

    // Test non-boolean foreigner
    $invalid_foreigner_data = [
        [
            'exam_slip_number' => 'E12345',
            'guardian_type' => 'FATHER',
            'have_siblings' => true,
            'dietary_restriction' => 'NONE',
            'foreigner' => 'not-a-boolean', // Invalid
        ]
    ];

    $service->setData($invalid_foreigner_data);
    $result = $service->validatePrePaymentInfoForImport()->getData();
    expect($result[0]['errors']['foreigner'][0])->toBe('Foreigner must be boolean.');

    // Test error for register_date and expiry_date
    $invalid_date_data = [
        [
            'exam_slip_number' => 'E12345',
            'guardian_type' => 'FATHER',
            'have_siblings' => true,
            'dietary_restriction' => 'NONE',
            'foreigner' => false,
            'register_date' => 'hahaha',
            'expiry_date' => 'yeet',
        ]
    ];

    $service->setData($invalid_date_data);
    $result = $service->validatePrePaymentInfoForImport()->getData();
    expect($result[0]['errors']['register_date'][0])->toBe('The register date field must be a valid date.')
        ->and($result[0]['errors']['expiry_date'][0])->toBe('The expiry date field must be a valid date.');

    // Test error for register_date and expiry_date -> left empty
    $invalid_date_data = [
        [
            'exam_slip_number' => 'E12345',
            'guardian_type' => 'FATHER',
            'have_siblings' => true,
            'dietary_restriction' => 'NONE',
            'foreigner' => false,
            'register_date' => '',
            'expiry_date' => '',
        ]
    ];

    $service->setData($invalid_date_data);
    $result = $service->validatePrePaymentInfoForImport()->getData();
    expect($result[0]['errors']['register_date'][0])->toBe('The register date field is required.')
        ->and($result[0]['errors']['expiry_date'][0])->toBe('The expiry date field is required.');


    // Test dietary restriction with valid values
    $valid_dietary_data = [
        [
            'exam_slip_number' => 'E12345',
            'guardian_type' => 'FATHER',
            'have_siblings' => true,
            'dietary_restriction' => DietaryRestriction::VEGETARIAN->value, // Valid
            'foreigner' => false,
        ],
        [
            'exam_slip_number' => 'E12346',
            'guardian_type' => 'MOTHER',
            'have_siblings' => false,
            'dietary_restriction' => DietaryRestriction::NONE->value, // Valid
            'foreigner' => true,
        ],
        [
            'exam_slip_number' => 'E12346',
            'guardian_type' => 'MOTHER',
            'have_siblings' => false,
            'dietary_restriction' => '', // Valid - can be empty
            'foreigner' => true,
        ],
    ];

    $service->setData($valid_dietary_data);
    $result = $service->validatePrePaymentInfoForImport()->getData();

    expect($result[0]['errors'])->not->toHaveKey('dietary_restriction');
    expect($result[1]['errors'])->not->toHaveKey('dietary_restriction');
    expect($result[2]['errors'])->not->toHaveKey('dietary_restriction');
});

test('validateStatusForImport', function () {
    $enrollment_session = EnrollmentSession::factory()->create();

    // Test case pre-payment validation (only APPROVED allowed)
    $pre_payment_data = [
        [
            'status' => EnrollmentStatus::APPROVED->value,
            'other_field' => 'value1',
            'errors' => [],
        ],
        // error
        [
            'status' => EnrollmentStatus::REJECTED->value,
            'other_field' => 'value2',
            'errors' => [],
        ],
        // error
        [
            'status' => EnrollmentStatus::SHORTLISTED->value,
            'other_field' => 'value3',
            'errors' => [],
        ],
    ];

    $result = $this->enrollmentService
        ->setData($pre_payment_data)
        ->setEnrollmentSession($enrollment_session)
        ->validateStatusForImport(true);

    $validated_data = $result->getData();

    expect($result->getImportErrorCount())->toBe(2);

    expect($validated_data[0]['status'])->toBe(EnrollmentStatus::APPROVED->value);
    expect($validated_data[0]['errors'])->toBeEmpty();

    // invalid status
    expect($validated_data[1]['status'])->toBe(EnrollmentStatus::REJECTED->value);
    expect($validated_data[1]['errors']['status'][0])->toBe(__('system_error.5004', ['attributes' => 'APPROVED']));

    // invalid status
    expect($validated_data[2]['status'])->toBe(EnrollmentStatus::SHORTLISTED->value);
    expect($validated_data[2]['errors']['status'][0])->toBe(__('system_error.5004', ['attributes' => 'APPROVED']));

    // Test case post-payment validation (APPROVED and REJECTED allowed)
    $post_payment_data = [
        [
            'status' => EnrollmentStatus::APPROVED->value,
            'other_field' => 'value1',
            'errors' => [],
        ],
        [
            'status' => EnrollmentStatus::REJECTED->value,
            'other_field' => 'value2',
            'errors' => [],
        ],
        [
            'status' => '', // empty status is valid
            'other_field' => 'value2',
            'errors' => [],
        ],
        // error
        [
            'status' => EnrollmentStatus::SHORTLISTED->value,
            'other_field' => 'value3',
            'errors' => [],
        ],
    ];

    $this->enrollmentService->setImportErrorCount(0);

    $result = $this->enrollmentService
        ->setData($post_payment_data)
        ->setEnrollmentSession($enrollment_session)
        ->validateStatusForImport(false);

    $validated_data = $result->getData();

    expect($result->getImportErrorCount())->toBe(1);

    expect($validated_data[0]['status'])->toBe(EnrollmentStatus::APPROVED->value);
    expect($validated_data[0]['errors'])->toBeEmpty();

    expect($validated_data[1]['status'])->toBe(EnrollmentStatus::REJECTED->value);
    expect($validated_data[1]['errors'])->toBeEmpty();

    expect($validated_data[2]['status'])->toBe('');
    expect($validated_data[2]['errors'])->toBeEmpty();

    expect($validated_data[3]['status'])->toBe(EnrollmentStatus::SHORTLISTED->value);
    expect($validated_data[3]['errors']['status'][0])->toBe(__('system_error.5004', [
        'attributes' => 'REJECTED, APPROVED'
    ]));
});

test('validateHostelForImport', function () {
    /**
     * pre payment
     *
     */

    $data = [
        ['hostel' => true], // valid
        ['hostel' => false], // valid
        ['hostel' => 'TRUE'], // invalid
        ['hostel' => ''], // invalid
        ['hostel' => 1], // invalid
        ['hostel' => null], // invalid
    ];

    $this->enrollmentService->setData($data);

    $result = $this->enrollmentService->validateHostelForImport(true);

    expect($result->getImportErrorCount())->toBe(4);
    expect($result->getData()[0])->not->toHaveKey('errors');
    expect($result->getData()[1])->not->toHaveKey('errors');
    expect($result->getData()[2]['errors']['hostel'][0])->toBe(__('system_error.5008'));
    expect($result->getData()[3]['errors']['hostel'][0])->toBe(__('system_error.5008'));
    expect($result->getData()[4]['errors']['hostel'][0])->toBe(__('system_error.5008'));
    expect($result->getData()[5]['errors']['hostel'][0])->toBe(__('system_error.5008'));

    /**
     * post payment
     *
     */

    $data = [
        ['hostel' => true], // valid
        ['hostel' => false], // valid
        ['hostel' => ''], // valid
        ['hostel' => null], // invalid
        ['hostel' => 'TRUE'], // invalid
        ['hostel' => 1], // invalid
    ];

    $this->enrollmentService->setData($data);
    $this->enrollmentService->setImportErrorCount(0);

    $result = $this->enrollmentService->validateHostelForImport(false);
    expect($result->getImportErrorCount())->toBe(3);
    expect($result->getData()[0])->not->toHaveKey('errors');
    expect($result->getData()[1])->not->toHaveKey('errors');
    expect($result->getData()[2])->not->toHaveKey('errors');
    expect($result->getData()[3]['errors']['hostel'][0])->toBe(__('system_error.5008'));
    expect($result->getData()[4]['errors']['hostel'][0])->toBe(__('system_error.5008'));
    expect($result->getData()[5]['errors']['hostel'][0])->toBe(__('system_error.5008'));
});

test('validateReligionForImport', function () {
    Religion::factory(2)->state(new Sequence(
        [
            'name->en' => 'Christianity',
            'name->zh' => '基督教',
        ],
        [
            'name->en' => 'Buddhism',
            'name->zh' => '佛教',
        ]
    ))->create();

    $service = app(EnrollmentService::class);

    // Test data with both existing and non-existing religions
    $test_data = [
        [
            'religion' => 'Christianity', // Exists
            'other_field' => 'value'
        ],
        [
            'religion' => 'Islam', // Doesn't exist
            'other_field' => 'value'
        ],
        [
            'religion' => 'Islam', // Doesn't exist
            'other_field' => 'value'
        ],
        [
            'religion' => '佛教', // Exists (in Chinese)
            'other_field' => 'value'
        ],
        [
            'religion' => 'Hinduism', // Doesn't exist
            'other_field' => 'value'
        ]
    ];

    $service->setData($test_data);
    $result = $service->validateReligionForImport()->getData();

    // Existing religion should not have warnings
    expect($result[0])->not->toHaveKey('warnings');
    expect($result[3])->not->toHaveKey('warnings');

    // Non-existing religions should have warnings and be added to creation list
    expect($result[1]['warnings']['religion'][0])
        ->toBe('Religion Islam does not exist in the database. It will be created.');
    expect($result[2]['warnings']['religion'][0])
        ->toBe('Religion Islam does not exist in the database. It will be created.');
    expect($result[4]['warnings']['religion'][0])
        ->toBe('Religion Hinduism does not exist in the database. It will be created.');

    // Check that they were added to the to-be-created list
    expect($service->getToBeCreatedReligions())->toContain('Islam');
    expect($service->getToBeCreatedReligions())->toContain('Hinduism');
    expect(count($service->getToBeCreatedReligions()))->toBe(2);
});

test('validateNationalityForImport', function () {
    Country::factory(2)->state(new Sequence(
        [
            'name->en' => 'Malaysia',
        ],
        [
            'name->en' => 'Singapore',
        ]
    ))->create();

    $service = app(EnrollmentService::class);

    // Test data with both existing and non-existing country
    $test_data = [
        [
            'nationality' => 'Malaysia', // Exists
            'other_field' => 'value'
        ],
        [
            'nationality' => 'Singapore', // Exists
            'other_field' => 'value'
        ],
        [
            'nationality' => 'Japan', // Doesn't exist
            'other_field' => 'value'
        ],
        [
            'nationality' => '', // Skipped
            'other_field' => 'value'
        ],
    ];

    $service->setData($test_data);
    $result = $service->validateNationalityForImport()->getData();

    // Existing country should not have warnings
    expect($result[0])->not->toHaveKey('warnings');
    expect($result[1])->not->toHaveKey('warnings');
    expect($result[3])->not->toHaveKey('warnings');

    // Non-existing country should have warnings and be added to creation list
    expect($result[2]['warnings']['nationality'][0])
        ->toBe('Country Japan does not exist in the database. It will be created.');

    // Check that they were added to the to-be-created list
    expect($service->getToBeCreatedCountries())->toContain('Japan');
    expect(count($service->getToBeCreatedCountries()))->toBe(1);
});

test('validateExamSlipNumberUniquenessForImport', function () {
    $existing_exam1 = EnrollmentExam::factory()->create([
        'exam_slip_number' => 'E12345',
    ]);

    $existing_exam2 = EnrollmentExam::factory()->create([
        'exam_slip_number' => 'E67890',
    ]);

    $data = [
        // new exam slip number
        [
            'exam_slip_number' => 'E11111',
            'other_field' => 'value1',
            'errors' => [],
        ],
        // invalid - exam slip number already exists in DB
        [
            'exam_slip_number' => 'E12345',
            'other_field' => 'value2',
            'errors' => [],
        ],
        // new exam slip number
        [
            'exam_slip_number' => 'E22222',
            'other_field' => 'value3',
            'errors' => [],
        ],
        // invalid - another existing exam slip number
        [
            'exam_slip_number' => 'E67890',
            'other_field' => 'value4',
            'errors' => [],
        ],
        // empty exam slip number (should be filtered out)
        [
            'exam_slip_number' => '',
            'other_field' => 'value5',
            'errors' => [],
        ],
    ];

    $result = $this->enrollmentService
        ->setData($data)
        ->validateExamSlipNumberUniquenessForImport();

    $validated_data = $result->getData();

    expect($result->getImportErrorCount())->toBe(2);

    // valid new exam slip number - no errors
    expect($validated_data[0]['exam_slip_number'])->toBe('E11111');
    expect($validated_data[0]['errors'])->toBeEmpty();

    // invalid existing exam slip number - should have error
    expect($validated_data[1]['exam_slip_number'])->toBe('E12345');
    expect($validated_data[1]['errors']['exam_slip_number'])->toBe([__('system_error.5030')]);

    // valid new exam slip number - no errors
    expect($validated_data[2]['exam_slip_number'])->toBe('E22222');
    expect($validated_data[2]['errors'])->toBeEmpty();

    // invalid existing exam slip number - should have error
    expect($validated_data[3]['exam_slip_number'])->toBe('E67890');
    expect($validated_data[3]['errors']['exam_slip_number'])->toBe([__('system_error.5030')]);

    // no errors (filtered out)
    expect($validated_data[4]['exam_slip_number'])->toBe('');
    expect($validated_data[4]['errors'])->toBeEmpty();
});

test('validateNRICForImport', function () {
    Student::factory(2)->state(new Sequence(
        [
            'nric' => '123456789012',
        ],
        [
            'nric' => '987654321098',
        ]
    ))->create();

    $enrollment_session = EnrollmentSession::factory()->create();

    Enrollment::factory(2)->state(new Sequence(
        [
            'nric' => '1111111111',
            'enrollment_session_id' => $enrollment_session->id,
        ],
        [
            'nric' => '2222222222',
            'enrollment_session_id' => $enrollment_session->id,
        ]
    ))->create();

    // Create test data with both existing and non-existing NRICs
    $test_data = [
        // STUDENT NRICs
        [
            'nric' => '123456789012', // Exists in DB
            'other_field' => 'value1'
        ],
        [
            'nric' => '111111111111', // Doesn't exist in DB
            'other_field' => 'value2'
        ],
        [
            'nric' => '987654321098', // Exists in DB
            'other_field' => 'value3'
        ],
        [
            'nric' => '222222222222', // Doesn't exist in DB
            'other_field' => 'value4'
        ],
        // ENROLLMENT NRICs
        [
            'nric' => '1111111111', // Exists in Enrollment
            'other_field' => 'value5'
        ],
        [
            'nric' => '2222222222', // Exists in Enrollment
            'other_field' => 'value6'
        ],
        [
            'nric' => '3333333333', // Doesn't exist in DB or Enrollment
            'other_field' => 'value7'
        ],
    ];

    $service = app(EnrollmentService::class);
    $service->setEnrollmentSession($enrollment_session)
        ->setData($test_data);

    // Run the validation
    $result = $service->validateNRICForImport()->getData();

    // Check that existing NRICs have errors
    expect($result[0]['errors']['nric'][0])
        ->toBe('NRIC 123456789012 already exists in the database.');
    expect($result[2]['errors']['nric'][0])
        ->toBe('NRIC 987654321098 already exists in the database.');

    // ENROLLMENT NRICs
    expect($result[4]['errors']['nric'][0])
        ->toBe('Enrollment with this NRIC already exists in the current enrollment session.');
    expect($result[5]['errors']['nric'][0])
        ->toBe('Enrollment with this NRIC already exists in the current enrollment session.');

    // Check that non-existing NRICs don't have errors
    expect($result[1])->not->toHaveKey('errors');
    expect($result[3])->not->toHaveKey('errors');
    expect($result[6])->not->toHaveKey('errors');
});

test('validatePassportNumberForImport', function () {
    Student::factory(2)->state(new Sequence(
        [
            'passport_number' => 'AAA1212121',
        ],
        [
            'passport_number' => 'B02910902',
        ]
    ))->create();

    $enrollment_session = EnrollmentSession::factory()->create();

    Enrollment::factory(2)->state(new Sequence(
        [
            'passport_number' => 'A-AA1111111111',
            'enrollment_session_id' => $enrollment_session->id,
        ],
        [
            'passport_number' => 'B-BB1111111111',
            'enrollment_session_id' => $enrollment_session->id,
        ]
    ))->create();

    // Create test data with both existing and non-existing passport_numbers
    $test_data = [
        // STUDENT PASSPORT NUMBERS
        [
            'passport_number' => 'AAA1212121', // Exists in DB
            'other_field' => 'value1'
        ],
        [
            'passport_number' => '111111111111', // Doesn't exist in DB
            'other_field' => 'value2'
        ],
        [
            'passport_number' => 'B02910902', // Exists in DB
            'other_field' => 'value3'
        ],
        [
            'passport_number' => '222222222222', // Doesn't exist in DB
            'other_field' => 'value4'
        ],
        // ENROLLMENT PASSPORT NUMBERS
        [
            'passport_number' => 'A-AA1111111111', // Exists in Enrollment
            'other_field' => 'value5'
        ],
        [
            'passport_number' => 'B-BB1111111111', // Exists in Enrollment
            'other_field' => 'value6'
        ],
        [
            'passport_number' => '3333333333', // Doesn't exist in DB or Enrollment
            'other_field' => 'value7'
        ],
    ];

    $service = app(EnrollmentService::class);
    $service->setEnrollmentSession($enrollment_session)
        ->setData($test_data);

    // Run the validation
    $result = $service->validatePassportNumberForImport()->getData();

    // Check that existing passport_numbers have errors
    expect($result[0]['errors']['passport_number'][0])
        ->toBe('Passport number AAA1212121 already exists in the database.');
    expect($result[2]['errors']['passport_number'][0])
        ->toBe('Passport number B02910902 already exists in the database.');

    // ENROLLMENT PASSPORT NUMBERS
    expect($result[4]['errors']['passport_number'][0])
        ->toBe('Enrollment with this Passport Number already exists in the current enrollment session.');
    expect($result[5]['errors']['passport_number'][0])
        ->toBe('Enrollment with this Passport Number already exists in the current enrollment session.');

    // Check that non-existing passport_numbers don't have errors
    expect($result[1])->not->toHaveKey('errors');
    expect($result[3])->not->toHaveKey('errors');
    expect($result[6])->not->toHaveKey('errors');
});

test('validatePrimarySchoolForImport', function () {
    School::factory(2)->state(new Sequence(
        [
            'name->en' => 'Sekolah Jenis Kebangsaan',
            'name->zh' => '国民型中学',
        ],
        [
            'name->en' => 'Pin Hwa',
            'name->zh' => '品华',
        ]
    ))->create();

    $service = app(EnrollmentService::class);

    // Test data with both existing and non-existing primary schools
    $test_data = [
        [
            'primary_school' => 'Sekolah Jenis Kebangsaan', // Exists in English
            'other_field' => 'value1'
        ],
        [
            'primary_school' => 'Sekolah Menengah', // Doesn't exist
            'other_field' => 'value2'
        ],
        [
            'primary_school' => '品华', // Exists in Chinese
            'other_field' => 'value3'
        ],
        [
            'primary_school' => 'International School', // Doesn't exist
            'other_field' => 'value4'
        ],
        [
            'primary_school' => 'International School', // Doesn't exist
            'other_field' => 'value4'
        ],
    ];

    $service->setData($test_data);
    $result = $service->validatePrimarySchoolForImport()->getData();

    // Check that existing schools don't have warnings
    expect($result[0])->not->toHaveKey('warnings');
    expect($result[2])->not->toHaveKey('warnings');

    // Check that non-existing schools have appropriate warnings
    expect($result[1]['warnings']['primary_school'][0])
        ->toBe('School Sekolah Menengah does not exist in the database. It will be created.');
    expect($result[3]['warnings']['primary_school'][0])
        ->toBe('School International School does not exist in the database. It will be created.');
    expect($result[4]['warnings']['primary_school'][0])
        ->toBe('School International School does not exist in the database. It will be created.');

    // Check that non-existing schools were added to the to-be-created list
    $to_be_created = $service->getToBeCreatedPrimarySchools();
    expect($to_be_created)->toContain('Sekolah Menengah');
    expect($to_be_created)->toContain('International School');
    expect(count($to_be_created))->toBe(2);
});

test('validateHealthConcernForImport', function () {
    HealthConcern::factory(2)->state(new Sequence(
        [
            'name->en' => 'Asthma',
            'name->zh' => '哮喘',
        ],
        [
            'name->en' => 'ADHD',
            'name->zh' => '注意力不足过动症',
        ]
    ))->create();

    $service = app(EnrollmentService::class);

    // Test data with both existing and non-existing health concerns
    $test_data = [
        [
            'health_concern' => 'Asthma', // Exists in English
            'other_field' => 'value1'
        ],
        [
            'health_concern' => 'Allergies', // Doesn't exist
            'other_field' => 'value2'
        ],
        [
            'health_concern' => '哮喘', // Exists in Chinese
            'other_field' => 'value3'
        ],
        [
            'health_concern' => 'Diabetes', // Doesn't exist
            'other_field' => 'value4'
        ],
        [
            'health_concern' => 'Diabetes', // Doesn't exist
            'other_field' => 'value4'
        ],
        [
            'health_concern' => '', // Valid - empty health concern
            'other_field' => 'value4'
        ],
    ];

    $service->setData($test_data);
    $result = $service->validateHealthConcernForImport()->getData();

    // Existing health concerns should not have warnings
    expect($result[0])->not->toHaveKey('warnings');
    expect($result[2])->not->toHaveKey('warnings');
    expect($result[5])->not->toHaveKey('warnings'); // empty health concern is valid

    // Non-existing health concerns should have warnings
    expect($result[1]['warnings']['health_concern'][0])
        ->toBe('Health concern Allergies does not exist in the database. It will be created.');
    expect($result[3]['warnings']['health_concern'][0])
        ->toBe('Health concern Diabetes does not exist in the database. It will be created.');
    expect($result[4]['warnings']['health_concern'][0])
        ->toBe('Health concern Diabetes does not exist in the database. It will be created.');

    // Check that non-existing health concerns were added to the to-be-created list
    $to_be_created = $service->getToBeCreatedHealthConcerns();
    expect($to_be_created)->toContain('Allergies');
    expect($to_be_created)->toContain('Diabetes');
    expect(count($to_be_created))->toBe(2);
});

test('validateSubjectForImport', function () {
    Subject::factory(3)->state(new Sequence(
        [
            'code' => 'ENG001',
            'name->en' => 'English',
        ],
        [
            'code' => 'MATH001',
            'name->en' => 'Mathematics',
        ],
        [
            'code' => 'SCI001',
            'name->en' => 'Science',
        ]
    ))->create();

    $enrollment_session = EnrollmentSession::factory()->create([
        'name' => 'Session 2025',
        'from_date' => '2025-01-01',
        'to_date' => '2025-03-01',
        'code' => '0001',
    ]);

    $enrollment_session->examSubjects()->attach(Subject::all());

    $service = app(EnrollmentService::class);
    $service->setEnrollmentSession($enrollment_session);

    // Test data with all required subjects
    $complete_data = [
        [
            'student_name_en' => 'John Doe',
            'ENG001' => '85.5',
            'MATH001' => '90.0',
            'SCI001' => '78.5',
        ],
        [
            'student_name_en' => 'Jane Smith',
            'ENG001' => '92.0',
            'MATH001' => '88.5',
            'SCI001' => '95.0',
        ]
    ];

    $service->setData($complete_data);
    $result = $service->validateSubjectForImport()->getData();

    // No errors should exist when all subjects are present
    expect($result[0])->not->toHaveKey('errors');
    expect($result[1])->not->toHaveKey('errors');

    // Test data with missing subjects
    $incomplete_data = [
        [
            'student_name_en' => 'John Doe',
            'ENG001' => '85.5',
            // Missing MATH001
            'SCI001' => '78.5',
        ],
        [
            'student_name_en' => 'Jane Smith',
            'ENG001' => '92.0',
            'MATH001' => '88.5',
            // Missing SCI001
        ]
    ];

    $service->setData($incomplete_data);
    $result = $service->validateSubjectForImport()->getData();

    // First row should have error for missing MATH001
    expect($result[0]['errors']['MATH001'][0])
        ->toBe('Subject MATH001 is missing in the import file.');

    // Second row should have error for missing SCI001
    expect($result[1]['errors']['SCI001'][0])
        ->toBe('Subject SCI001 is missing in the import file.');
});

test('validatePhoneNumberForImport', function () {
    $data = [
        [
            'guardian_phone_number' => '+6**********', // Valid Malaysian number
            'errors' => [],
        ],
        [
            'guardian_phone_number' => '**********', // Valid Malaysian number
            'errors' => [],
        ],
        [
            'guardian_phone_number' => '123456787', // Valid Malaysian number
            'errors' => [],
        ],
        [
            'guardian_phone_number' => 'invalid',
            'errors' => [],
        ],
        [
            'guardian_phone_number' => '+601234567', // invalid number - not enough digits
            'errors' => [],
        ],
    ];

    $this->enrollmentService->setData($data);

    $result = $this->enrollmentService->validatePhoneNumberForImport();

    $validated = $result->getData();

    expect($result->getImportErrorCount())->toBe(2);

    expect($validated[0]['guardian_phone_number'])->toBe('+6**********'); // Should be normalized to include country code
    expect($validated[0]['errors'])->toBeEmpty();

    expect($validated[1]['guardian_phone_number'])->toBe('+6**********'); // Should be normalized to include country code
    expect($validated[1]['errors'])->toBeEmpty();

    expect($validated[2]['guardian_phone_number'])->toBe('+60123456787'); // Should be normalized to include country code
    expect($validated[2]['errors'])->toBeEmpty();

    expect($validated[3]['guardian_phone_number'])->toBe('invalid');
    expect($validated[3]['errors']['guardian_phone_number'])->toBe([
        'Phone number without country code must start with Malaysian number: 601, 01, 1',
    ]);

    expect($validated[4]['guardian_phone_number'])->toBe('+601234567');
    expect($validated[4]['errors']['guardian_phone_number'])->toBe([
        'Invalid phone number.',
    ]);
});

test('validatePhoneNumberAndEmailUniquenessForImport', function () {
    $existing_user_1 = EnrollmentUser::factory()->create([
        'phone_number' => '+6**********',
        'email' => '<EMAIL>',
    ]);

    $existing_user_2 = EnrollmentUser::factory()->create([
        'phone_number' => '+60134567890',
        'email' => '<EMAIL>',
    ]);

    $data = [
        // Scenario 1: Valid - New phone number and email
        [
            'guardian_phone_number' => '+60145678901',
            'guardian_email' => '<EMAIL>',
            'errors' => [],
        ],
        // Scenario 2: Invalid - Existing phone number with different email
        [
            'guardian_phone_number' => '+6**********',
            'guardian_email' => '<EMAIL>',
            'errors' => [],
        ],
        // Scenario 3: Invalid - Existing email with different phone number
        [
            'guardian_phone_number' => '+60144444444',
            'guardian_email' => '<EMAIL>',
            'errors' => [],
        ],
        // Scenario 4: Valid - Existing phone number and email (same as existing_user_1)
        [
            'guardian_phone_number' => $existing_user_1->phone_number,
            'guardian_email' => $existing_user_1->email,
            'errors' => [],
        ],
        // Scenario 5: Valid - Existing phone number and email (same as existing_user_2)
        [
            'guardian_phone_number' => $existing_user_2->phone_number,
            'guardian_email' => $existing_user_2->email,
            'errors' => [],
        ],
    ];

    $this->enrollmentService->setData($data);

    $result = $this->enrollmentService->validatePhoneNumberAndEmailUniquenessForImport();

    $validated = $result->getData();

    // Check error count
    expect($result->getImportErrorCount())->toBe(2);

    // Scenario 1: Valid - No errors - new phone number and email
    expect($validated[0]['errors'])->toBeEmpty();

    // Scenario 2: Invalid - Existing phone number with different email
    expect($validated[1]['errors']['guardian_phone_number'])->toBe([
        'Phone number and email does not match with registered user.',
    ]);

    // Scenario 3: Invalid - Existing email with different phone number
    expect($validated[2]['errors']['guardian_email'])->toBe([
        'Phone number and email does not match with registered user.',
    ]);

    // Scenario 4: Valid - Existing phone number and email (same as existing_user_1)
    expect($validated[3]['errors'])->toBeEmpty();

    // Scenario 5: Valid - Existing phone number and email (same as existing_user_2)
    expect($validated[4]['errors'])->toBeEmpty();
});

test('validateEnrollmentFromDatabaseForImport', function () {
    $enrollment_session = EnrollmentSession::factory()->create();

    $existing_enrollment_1 = Enrollment::factory()->create([
        'nric' => '990101123456',
        'passport_number' => 'A1234567',
        'enrollment_session_id' => $enrollment_session->id,
    ]);

    $existing_enrollment_2 = Enrollment::factory()->create([
        'nric' => '990202123456',
        'passport_number' => null, // Only NRIC
        'enrollment_session_id' => $enrollment_session->id,
    ]);

    $existing_enrollment_3 = Enrollment::factory()->create([
        'nric' => null, // Only passport
        'passport_number' => 'B9876543',
        'enrollment_session_id' => $enrollment_session->id,
    ]);

    $data = [
        // Scenario 1: Both NRIC and passport match existing enrollment
        [
            'nric' => '990101123456',
            'passport_number' => 'A1234567',
            'errors' => [],
        ],
        // Scenario 2: Only NRIC matches an existing enrollment
        [
            'nric' => '990202123456',
            'passport_number' => null,
            'errors' => [],
        ],
        // Scenario 3: Only passport matches an existing enrollment
        [
            'nric' => null,
            'passport_number' => 'B9876543',
            'errors' => [],
        ],
        // Scenario 4: Both NRIC and passport provided, but neither exists in database
        [
            'nric' => '991111123456',
            'passport_number' => 'D1111111',
            'errors' => [],
        ],
        // Scenario 5: Only NRIC provided and it doesn't exist
        [
            'nric' => '992222123456',
            'passport_number' => null,
            'errors' => [],
        ],
        // Scenario 6: Only passport provided and it doesn't exist
        [
            'nric' => null,
            'passport_number' => 'E2222222',
            'errors' => [],
        ],
    ];

    $result = $this->enrollmentService
        ->setData($data)
        ->setEnrollmentSession($enrollment_session)
        ->validateEnrollmentFromDatabaseForImport();

    $validated_data = $result->getData();

    expect($result->getImportErrorCount())->toBe(3);

    // Scenario 1: Both NRIC and passport match
    expect($validated_data[0]['nric'])->toBe('990101123456');
    expect($validated_data[0]['passport_number'])->toBe('A1234567');
    expect($validated_data[0]['errors'])->toBeEmpty();

    // Scenario 2: Only NRIC matches
    expect($validated_data[1]['nric'])->toBe('990202123456');
    expect($validated_data[1]['passport_number'])->toBeEmpty();
    expect($validated_data[1]['errors'])->toBeEmpty();

    // Scenario 3: Only passport matches
    expect($validated_data[2]['nric'])->toBeEmpty();
    expect($validated_data[2]['passport_number'])->toBe('B9876543');
    expect($validated_data[2]['errors'])->toBeEmpty();

    // Scenario 4: Both NRIC and passport provided but neither exists
    expect($validated_data[3]['nric'])->toBe('991111123456');
    expect($validated_data[3]['passport_number'])->toBe('D1111111');
    expect($validated_data[3]['errors']['nric'])->toBe(['Enrollment with NRIC 991111123456 or Passport Number D1111111 does not exist in the database.']);

    // Scenario 5: Only NRIC provided and it doesn't exist
    expect($validated_data[4]['nric'])->toBe('992222123456');
    expect($validated_data[4]['passport_number'])->toBeEmpty();
    expect($validated_data[4]['errors']['nric'])->toBe(['Enrollment with NRIC 992222123456 does not exist in the database.']);

    // Scenario 6: Only passport provided and it doesn't exist
    expect($validated_data[5]['nric'])->toBeEmpty();
    expect($validated_data[5]['passport_number'])->toBe('E2222222');
    expect($validated_data[5]['errors']['passport_number'])->toBe(['Enrollment with Passport Number E2222222 does not exist in the database.']);
});

test('getValidatedData', function () {
    $subjects = Subject::factory(2)->state(new Sequence(
        [
            'code' => 'ENG001',
            'name->en' => 'English',
            'name->zh' => '英语',
        ],
        [
            'code' => 'MATH001',
            'name->en' => 'Mathematics',
            'name->zh' => '数学',
        ]
    ))->create();

    $enrollment_session = EnrollmentSession::factory()->create([
        'name' => 'Session 2025',
        'from_date' => '2025-01-01',
        'to_date' => '2025-03-01',
        'code' => '0001',
    ]);

    $enrollment_session->examSubjects()->attach(Subject::all());

    // Test data
    $test_data = [
        [
            'student_name_en' => 'John Doe',
            'nric' => '123456789012',
            'ENG001' => '85',
            'MATH001' => '90',
        ],
        [
            'student_name_en' => 'Jane Smith',
            'nric' => '987654321098',
            'ENG001' => '92',
            'MATH001' => '88',
        ]
    ];

    $service = app(EnrollmentService::class);
    $service->setData($test_data);
    $service->setEnrollmentSession($enrollment_session);

    // Get validated data
    $result = $service->getValidatedData();

    // Check structure
    expect($result)->toBeArray();
    expect($result)->toHaveKeys(['data', 'subject_lists']);

    expect($result['data'])->toBe($test_data);
    expect($result['subject_lists'])->toHaveCount(2);
});


test('fetchAllMapping', function () {
    /** @var EnrollmentService */
    $enrollment_service = resolve(EnrollmentService::class);

    $religion = Religion::factory()->create([
        'name' => [
            'en' => 'Buddhism',
            'zh' => '佛教',
        ],
    ]);

    $country = Country::factory()->create([
        'name' => [
            'en' => 'Malaysia',
            'zh' => '马来西亚',
        ],
    ]);

    $health_concern = HealthConcern::factory()->create([
        'name' => [
            'en' => 'Asthma',
            'zh' => '哮喘',
        ],
    ]);

    $primary_school = School::factory()->create([
        'name' => [
            'en' => 'Test Primary School',
            'zh' => '测试小学',
        ],
        'level' => SchoolLevel::PRIMARY,
    ]);

    $guardian = EnrollmentUser::factory()->create([
        'name' => [
            'en' => 'John Parent',
            'zh' => '约翰家长',
        ],
        'phone_number' => '+6**********',
        'email' => '<EMAIL>',
    ]);

    $test_data = [
        [
            'religion' => 'Buddhism',
            'nationality' => 'Malaysia',
            'health_concern' => 'Asthma',
            'primary_school' => 'Test Primary School',
            'guardian_phone_number' => '+6**********',
            'guardian_email' => '<EMAIL>',
        ],
        [
            'religion' => 'Buddhism',
            'nationality' => 'Malaysia',
            'health_concern' => 'Asthma',
            'primary_school' => 'Test Primary School',
            'guardian_phone_number' => '01234', // Partial match
            'guardian_email' => '<EMAIL>',
        ],
    ];

    $enrollment_service->setData($test_data);

    $mappings = $enrollment_service->fetchAllMapping();

    // Verify all mappings are returned correctly
    expect($mappings)->toBeArray()
        ->toHaveKeys(['religion_map', 'health_concern_map', 'primary_school_map', 'guardian_by_phone_map', 'guardian_by_email_map']);

    // Test religion mappings
    expect($mappings['religion_map'])->toHaveKey('Buddhism');
    expect($mappings['religion_map']['Buddhism']->id)->toBe($religion->id);

    // Test country mappings
    expect($mappings['country_map'])->toHaveKey('Malaysia');
    expect($mappings['country_map']['Malaysia']->id)->toBe($country->id);

    // Test health concern mappings
    expect($mappings['health_concern_map'])->toHaveKey('Asthma');
    expect($mappings['health_concern_map']['Asthma']->id)->toBe($health_concern->id);

    // Test primary school mappings
    expect($mappings['primary_school_map'])->toHaveKey('Test Primary School');
    expect($mappings['primary_school_map']['Test Primary School']->id)->toBe($primary_school->id);

    // Test guardian mappings by phone
    expect($mappings['guardian_by_phone_map'])->toHaveKey('+6**********');
    expect($mappings['guardian_by_phone_map']['+6**********']->id)->toBe($guardian->id);

    // Test guardian mappings by email
    expect($mappings['guardian_by_email_map'])->toHaveKey('<EMAIL>');
    expect($mappings['guardian_by_email_map']['<EMAIL>']->id)->toBe($guardian->id);


    /**
     * test empty data
     *
     */

    $enrollment_service->setData([]);

    $mappings = $enrollment_service->fetchAllMapping();

    expect($mappings)->toBeArray()
        ->toHaveKeys(['religion_map', 'health_concern_map', 'primary_school_map', 'guardian_by_phone_map', 'guardian_by_email_map']);

    expect($mappings['religion_map'])->toBeArray()->toBeEmpty();
    expect($mappings['health_concern_map'])->toBeArray()->toBeEmpty();
    expect($mappings['primary_school_map'])->toBeArray()->toBeEmpty();
    expect($mappings['guardian_by_phone_map'])->toBeArray()->toBeEmpty();
    expect($mappings['guardian_by_email_map'])->toBeArray()->toBeEmpty();


    /**
     * test multiple translation
     *
     */

    $religion = Religion::factory()->create([
        'name' => [
            'en' => 'Christianity',
            'zh' => '基督教',
        ],
    ]);

    $test_data = [
        [
            'religion' => 'Christianity',
            'health_concern' => '',
            'primary_school' => '',
            'guardian_phone_number' => '',
            'guardian_email' => '',
        ],
        [
            'religion' => '基督教', // Chinese translation
            'health_concern' => '',
            'primary_school' => '',
            'guardian_phone_number' => '',
            'guardian_email' => '',
        ],
    ];

    $enrollment_service->setData($test_data);

    $mappings = $enrollment_service->fetchAllMapping();

    expect($mappings['religion_map'])->toHaveKey('Christianity');
    expect($mappings['religion_map'])->toHaveKey('基督教');
    expect($mappings['religion_map']['Christianity']->id)->toBe($religion->id);
    expect($mappings['religion_map']['基督教']->id)->toBe($religion->id);
});


test('fetchEnrollmentsMapping', function () {
    $enrollment_session = EnrollmentSession::factory()->create();

    $enrollment1 = Enrollment::factory()->create([
        'nric' => '990101123456',
        'passport_number' => null,
        'enrollment_session_id' => $enrollment_session->id,
        'enrollment_status' => EnrollmentStatus::APPROVED,
        'payment_status' => EnrollmentPaymentStatus::PAID,
    ]);

    $enrollment2 = Enrollment::factory()->create([
        'nric' => null,
        'passport_number' => 'A1234567',
        'enrollment_session_id' => $enrollment_session->id,
        'enrollment_status' => EnrollmentStatus::SHORTLISTED,
        'payment_status' => EnrollmentPaymentStatus::UNPAID,
    ]);

    $enrollment3 = Enrollment::factory()->create([
        'nric' => '990202123456',
        'passport_number' => 'B7654321',
        'enrollment_session_id' => $enrollment_session->id,
        'enrollment_status' => EnrollmentStatus::REJECTED,
        'payment_status' => EnrollmentPaymentStatus::UNPAID,
    ]);

    // EXAM
    $exam1 = EnrollmentExam::factory()->create([
        'enrollment_id' => $enrollment1->id,
        'total_average' => 85.5,
    ]);

    $exam2 = EnrollmentExam::factory()->create([
        'enrollment_id' => $enrollment2->id,
        'total_average' => 85.5,
    ]);

    $exam3 = EnrollmentExam::factory()->create([
        'enrollment_id' => $enrollment3->id,
        'total_average' => 85.5,
    ]);

    $subject1 = Subject::factory()->create(['code' => 'MATH001']);
    $subject2 = Subject::factory()->create(['code' => 'ENG001']);

    // ENROLLMENT EXAM MARKS
    EnrollmentExamMark::factory()->create([
        'enrollment_exam_id' => $exam1->id,
        'subject_id' => $subject1->id,
        'mark' => 85.5,
    ]);

    EnrollmentExamMark::factory()->create([
        'enrollment_exam_id' => $exam1->id,
        'subject_id' => $subject2->id,
        'mark' => 85.5,
    ]);

    EnrollmentExamMark::factory()->create([
        'enrollment_exam_id' => $exam2->id,
        'subject_id' => $subject1->id,
        'mark' => 85.5,
    ]);

    EnrollmentExamMark::factory()->create([
        'enrollment_exam_id' => $exam2->id,
        'subject_id' => $subject2->id,
        'mark' => 85.5,
    ]);

    EnrollmentExamMark::factory()->create([
        'enrollment_exam_id' => $exam3->id,
        'subject_id' => $subject1->id,
        'mark' => 85.5,
    ]);

    EnrollmentExamMark::factory()->create([
        'enrollment_exam_id' => $exam3->id,
        'subject_id' => $subject2->id,
        'mark' => 85.5,
    ]);

    $data = [
        [
            'nric' => '990101123456',
            'passport_number' => null,
            'hostel' => true,
            'status' => EnrollmentStatus::APPROVED,
            'total_average' => 85.5,
            'MATH001' => 88.5,
            'ENG001' => 82.5,
        ],
        [
            'nric' => null,
            'passport_number' => 'A1234567',
            'hostel' => false,
            'status' => EnrollmentStatus::SHORTLISTED,
            'total_average' => 76.3,
            'MATH001' => 75.0,
        ],
        [
            'nric' => '990202123456',
            'passport_number' => 'B7654321',
            'hostel' => true,
            'status' => EnrollmentStatus::REJECTED,
            'total_average' => 92.1,
            'ENG001' => 95.0,
        ],
    ];

    $result = $this->enrollmentService
        ->setData($data)
        ->setEnrollmentSession($enrollment_session)
        ->fetchEnrollmentsMapping();

    expect($result)->toBeArray();
    expect($result)->toHaveKeys(['enrollment_by_nric_map', 'enrollment_by_passport_map']);

    // NRIC mapping
    $nric_map = $result['enrollment_by_nric_map'];
    expect($nric_map)->toBeArray();
    expect($nric_map)->toHaveCount(2);
    expect($nric_map)->toHaveKeys(['990101123456', '990202123456']);
    expect($nric_map['990101123456']->id)->toBe($enrollment1->id);
    expect($nric_map['990202123456']->id)->toBe($enrollment3->id);

    // passport mapping
    $passport_map = $result['enrollment_by_passport_map'];
    expect($passport_map)->toBeArray();
    expect($passport_map)->toHaveCount(2);
    expect($passport_map)->toHaveKeys(['A1234567', 'B7654321']);
    expect($passport_map['A1234567']->id)->toBe($enrollment2->id);
    expect($passport_map['B7654321']->id)->toBe($enrollment3->id);

    // check the eager loaded relationships
    expect($nric_map['990101123456']->enrollmentExams)->not->toBeNull();
    expect($nric_map['990101123456']->enrollmentExams->first()->examMarks)->toHaveCount(2);
    expect($nric_map['990101123456']->enrollmentExams->first()->examMarks->first()->subject)->not->toBeNull();
});

test('createReligionInBulk', function () {
    /** @var EnrollmentService */
    $enrollment_service = resolve(EnrollmentService::class);

    // Create nothing
    $enrollment_service->setToBeCreatedReligions([]);

    $enrollment_service->createReligionInBulk();

    $this->assertDatabaseCount('master_religions', 0);

    // Create 3 religions
    $religions = ['Buddhism', 'Sikhism', 'Jainism'];

    $enrollment_service->setToBeCreatedReligions($religions);

    $enrollment_service->createReligionInBulk();

    $this->assertDatabaseCount('master_religions', 3);

    $this->assertDatabaseHas('master_religions', [
        'name->en' => 'Buddhism',
        'name->zh' => 'Buddhism',
        'sequence' => 0,
    ]);

    $this->assertDatabaseHas('master_religions', [
        'name->en' => 'Sikhism',
        'name->zh' => 'Sikhism',
        'sequence' => 1,
    ]);

    $this->assertDatabaseHas('master_religions', [
        'name->en' => 'Jainism',
        'name->zh' => 'Jainism',
        'sequence' => 2,
    ]);

    // create 2 more
    $more_religions = ['Hinduism', 'Judaism'];

    $enrollment_service->setToBeCreatedReligions($more_religions);

    $enrollment_service->createReligionInBulk();

    $this->assertDatabaseCount('master_religions', 5);

    $this->assertDatabaseHas('master_religions', [
        'name->en' => 'Hinduism',
        'name->zh' => 'Hinduism',
        'sequence' => 3,
    ]);

    $this->assertDatabaseHas('master_religions', [
        'name->en' => 'Judaism',
        'name->zh' => 'Judaism',
        'sequence' => 4,
    ]);
});

test('createCountryInBulk', function () {
    /** @var EnrollmentService */
    $enrollment_service = resolve(EnrollmentService::class);

    // Create nothing
    $enrollment_service->setToBeCreatedCountries([]);

    $enrollment_service->createCountryInBulk();

    $this->assertDatabaseCount('master_countries', 0);

    // Create 2 countries
    $countries = ['Malaysia', 'Singapore'];

    $enrollment_service->setToBeCreatedCountries($countries);

    $enrollment_service->createCountryInBulk();

    $this->assertDatabaseCount('master_countries', 2);

    $this->assertDatabaseHas('master_countries', [
        'name->en' => 'Malaysia',
        'name->zh' => 'Malaysia',
    ]);

    $this->assertDatabaseHas('master_countries', [
        'name->en' => 'Singapore',
        'name->zh' => 'Singapore',
    ]);
});

test('getRemarks', function () {
    // both hostel_reason and remarks are provided
    $row_with_both = [
        'hostel_reason' => 'Too far from home',
        'remarks' => 'Special dietary requirements'
    ];

    $result = $this->enrollmentService->getRemarks($row_with_both);
    expect($result)->toBe("Hostel Reason: Too far from home \nRemarks: Special dietary requirements \n");

    // only hostel_reason is provided
    $row_with_hostel_reason = [
        'hostel_reason' => 'Distance issue',
        'remarks' => ''
    ];

    $result = $this->enrollmentService->getRemarks($row_with_hostel_reason);
    expect($result)->toBe("Hostel Reason: Distance issue \n");

    // only remarks is provided
    $row_with_remarks = [
        'hostel_reason' => '',
        'remarks' => 'Medical condition'
    ];

    $result = $this->enrollmentService->getRemarks($row_with_remarks);
    expect($result)->toBe("Remarks: Medical condition \n");

    // both fields are empty
    $row_empty = [
        'hostel_reason' => '',
        'remarks' => ''
    ];

    $result = $this->enrollmentService->getRemarks($row_empty);
    expect($result)->toBeNull();
});

test('createPrimarySchoolInBulk', function () {
    /** @var EnrollmentService */
    $enrollment_service = resolve(EnrollmentService::class);

    // create nothing
    $enrollment_service->setToBeCreatedPrimarySchools([]);

    $enrollment_service->createPrimarySchoolInBulk();

    $this->assertDatabaseCount('master_schools', 0);


    // create 3 primary schools
    $primary_schools = ['SK Bukit Jalil', 'SK Seri Petaling', 'SJKC Kung Man'];

    $enrollment_service->setToBeCreatedPrimarySchools($primary_schools);

    $enrollment_service->createPrimarySchoolInBulk();

    $this->assertDatabaseCount('master_schools', 3);

    foreach ($primary_schools as $primary_school) {
        $this->assertDatabaseHas('master_schools', [
            'name->en' => $primary_school,
            'name->zh' => $primary_school,
            'level' => SchoolLevel::PRIMARY,
        ]);
    }
});

test('createHealthConcernInBulk', function () {
    /** @var EnrollmentService */
    $enrollment_service = resolve(EnrollmentService::class);

    // create nothing
    $enrollment_service->setToBeCreatedHealthConcerns([]);

    $enrollment_service->createHealthConcernInBulk();

    $this->assertDatabaseCount('master_health_concerns', 0);


    // create 3 health concerns
    $health_concerns = ['Asthma', 'Diabetes', 'Allergies'];

    $enrollment_service->setToBeCreatedHealthConcerns($health_concerns);

    $enrollment_service->createHealthConcernInBulk();

    $this->assertDatabaseCount('master_health_concerns', 3);

    foreach ($health_concerns as $health_concern) {
        $this->assertDatabaseHas('master_health_concerns', [
            'name->en' => $health_concern,
            'name->zh' => $health_concern,
        ]);
    }
});

test('savePrePaymentImportedData() - normal data without exam slip number should not create enrollment_exam and marks', function () {
    Queue::fake();

    $enrollment_session = EnrollmentSession::factory()->create();

    $math_subject = Subject::factory()->create(['code' => 'MATH001']);
    $eng_subject = Subject::factory()->create(['code' => 'ENG001']);

    $enrollment_session->examSubjects()->attach([$math_subject->id, $eng_subject->id]);

    $religion = Religion::factory()->create(['name' => ['en' => 'Christian', 'zh' => 'Christian']]);
    $health_concern = HealthConcern::factory()->create(['name' => ['en' => 'None', 'zh' => 'None']]);
    $primary_school = School::factory()->create(['name' => ['en' => 'Pin Hwa', 'zh' => 'Pin Hwa']]);
    $country = Country::factory()->create(['name' => ['en' => 'Malaysia', 'zh' => '马来西亚']]);

    // Data without exam_slip_number (empty string)
    $data = [
        [
            'number' => '1',
            'exam_slip_number' => '', // Empty exam slip number
            'student_name_en' => 'John Doe',
            'student_name_zh' => '约翰·多',
            'nric' => '990101123456',
            'passport_number' => null,
            'religion' => 'Christian',
            'gender' => 'MALE',
            'guardian_phone_number' => '+6**********',
            'guardian_email' => '<EMAIL>',
            'guardian_name' => 'Jane Doe',
            'guardian_type' => 'MOTHER',
            'total_average' => '85.5',
            'MATH001' => '85.5',
            'ENG001' => '85.5',
            'status' => EnrollmentStatus::APPROVED->value,
            'address' => '123 Sample Street',
            'primary_school' => 'Pin Hwa',
            'hostel' => true,
            'hostel_reason' => '5km from school',
            'have_siblings' => false,
            'dietary_restriction' => DietaryRestriction::NONE->value,
            'health_concern' => 'None',
            'foreigner' => false,
            'nationality' => 'Malaysia',
            'conduct' => 'A+',
            'remarks' => 'Test remarks',
            'register_date' => '2021-01-01',
            'expiry_date' => '2021-01-20',
        ]
    ];

    $this->enrollmentService
        ->setData($data)
        ->setEnrollmentSession($enrollment_session)
        ->savePrePaymentImportedData();

    // Assert enrollment was created
    $this->assertDatabaseCount('enrollments', 1);

    $enrollment = Enrollment::where('nric', '990101123456')->first();
    expect($enrollment)->not->toBeNull()
        ->and($enrollment->enrollment_status)->toBe(EnrollmentStatus::APPROVED->value)
        ->and($enrollment->is_hostel)->toBe(true);

    // Assert NO enrollment_exam was created (because exam_slip_number is empty)
    $this->assertDatabaseCount('enrollment_exams', 0);

    // Assert NO enrollment_exam_marks were created
    $this->assertDatabaseCount('enrollment_exam_marks', 0);

    // Assert guardian was created
    $this->assertDatabaseCount('enrollment_users', 1);

    $guardian = EnrollmentUser::where('email', '<EMAIL>')->first();
    expect($guardian)->not->toBeNull()
        ->and($guardian->phone_number)->toBe('+6**********')
        ->and($guardian->name)->toBe('Jane Doe');

    // Assert notification job was dispatched
    Queue::assertPushed(NotifyGuardianOfEnrollmentStatusJob::class, 1);


    Enrollment::truncate();
    EnrollmentUser::truncate();

    // empty total_average, dietary_restriction, health_concern
    $data = [
        [
            'number' => '1',
            'exam_slip_number' => 'EXAM_SLIP',
            'student_name_en' => 'John Doe',
            'student_name_zh' => '约翰·多',
            'nric' => '990101123456',
            'passport_number' => null,
            'religion' => 'Christian',
            'gender' => 'MALE',
            'guardian_phone_number' => '+6**********',
            'guardian_email' => '<EMAIL>',
            'guardian_name' => 'Jane Doe',
            'guardian_type' => 'MOTHER',
            'total_average' => '', // Empty total_average
            'MATH001' => '85.5',
            'ENG001' => '85.5',
            'status' => EnrollmentStatus::APPROVED->value,
            'address' => '123 Sample Street',
            'primary_school' => 'Pin Hwa',
            'hostel' => true,
            'hostel_reason' => '5km from school',
            'have_siblings' => false,
            'dietary_restriction' => '',
            'health_concern' => '',
            'foreigner' => false,
            'nationality' => 'Malaysia',
            'conduct' => 'A+',
            'remarks' => 'Test remarks',
            'register_date' => '2021-01-01',
            'expiry_date' => '2021-01-20',
        ]
    ];

    $this->enrollmentService
        ->setData($data)
        ->setEnrollmentSession($enrollment_session)
        ->savePrePaymentImportedData();

    $this->assertDatabaseCount('enrollments', 1);
    $this->assertDatabaseCount('enrollment_exams', 0);
    $this->assertDatabaseCount('enrollment_users', 1);

    $this->assertDatabaseHas('enrollments', [
        'dietary_restriction' => null,
        'health_concern_id' => null,
    ]);
});

test('savePrePaymentImportedData() - successful', function () {
    Queue::fake();

    $enrollment_session = EnrollmentSession::factory()->create([
        'admission_year' => 2027,
    ]);
    $subject_1 = Subject::factory()->create(['code' => 'MATH']);
    $subject_2 = Subject::factory()->create(['code' => 'ENG']);

    $enrollment_session->examSubjects()->attach([
        $subject_1->id,
        $subject_2->id
    ]);

    /** @var EnrollmentService */
    $enrollment_service = resolve(EnrollmentService::class);
    $enrollment_service->setEnrollmentSession($enrollment_session);

    // all ok
    $import_data = [
        [
            'exam_slip_number' => 'E12345',
            'student_name_en' => 'John Doe',
            'student_name_zh' => '约翰·多',
            'nric' => '990101123456',
            'passport_number' => 'A1234567',
            'religion' => 'Buddhism',
            'gender' => 'MALE',
            'guardian_phone_number' => '**********',
            'guardian_email' => '<EMAIL>',
            'guardian_name' => 'Jane Doe',
            'guardian_type' => 'MOTHER',
            'total_average' => 85.5,
            'MATH' => 90,
            'ENG' => 81,
            'status' => 'APPROVED',
            'address' => '123 Main St',
            'primary_school' => 'Primary School ABC',
            'hostel' => true,
            'hostel_reason' => '5km from school',
            'have_siblings' => false,
            'dietary_restriction' => 'NONE',
            'health_concern' => 'Asthma',
            'foreigner' => false,
            'nationality' => 'Malaysia',
            'conduct' => 'A',
            'remarks' => 'Good student',
            'register_date' => '2021-01-02',
            'expiry_date' => '2021-01-22',
        ],
        [
            'exam_slip_number' => 'E12346',
            'student_name_en' => 'Alice Smith',
            'student_name_zh' => '爱丽丝·史密斯',
            'nric' => '990202123456',
            'passport_number' => 'B1234567',
            'religion' => 'Christianity',
            'gender' => 'FEMALE',
            'guardian_phone_number' => '**********',
            'guardian_email' => '<EMAIL>',
            'guardian_name' => 'John Smith',
            'guardian_type' => 'FATHER',
            'total_average' => 92.5,
            'MATH' => 95,
            'ENG' => 90,
            'status' => 'APPROVED',
            'address' => '456 Side St',
            'primary_school' => 'Primary School XYZ',
            'hostel' => false,
            'hostel_reason' => '4km from school',
            'have_siblings' => true,
            'dietary_restriction' => 'VEGETARIAN',
            'health_concern' => 'None',
            'foreigner' => false,
            'nationality' => 'Malaysia',
            'conduct' => 'A',
            'remarks' => null,
            'register_date' => '2021-01-01',
            'expiry_date' => '2021-01-20',
        ],
    ];

    $enrollment_1_remarks = $this->enrollmentService->getRemarks($import_data[0]);
    $enrollment_2_remarks = $this->enrollmentService->getRemarks($import_data[1]);

    $enrollment_service->setData($import_data);

    $buddhism = Religion::factory()->create(['name' => ['en' => 'Buddhism', 'zh' => '佛教']]);
    $christianity = Religion::factory()->create(['name' => ['en' => 'Christianity', 'zh' => '基督教']]);
    $asthma = HealthConcern::factory()->create(['name' => ['en' => 'Asthma', 'zh' => '哮喘']]);
    $none_health_concern = HealthConcern::factory()->create(['name' => ['en' => 'None', 'zh' => '无']]);
    $abc = School::factory()->create(['name' => ['en' => 'Primary School ABC', 'zh' => '小学ABC']]);
    $xyz = School::factory()->create(['name' => ['en' => 'Primary School XYZ', 'zh' => '小学XYZ']]);
    $country = Country::factory()->create(['name' => ['en' => 'Malaysia', 'zh' => '马来西亚']]);

    $enrollment_service->savePrePaymentImportedData();

    Queue::assertPushed(NotifyGuardianOfEnrollmentStatusJob::class, 2);

    // ENROLLMENTS
    $this->assertDatabaseCount('enrollments', 2);

    $this->assertDatabaseHas('enrollments', [
        'name->en' => 'JOHN DOE',
        'name->zh' => '约翰·多',
        'nric' => '990101123456',
        'passport_number' => 'A1234567',
        'religion_id' => $buddhism->id,
        'gender' => 'MALE',
        'address' => '123 Main St',
        'primary_school_id' => $abc->id,
        'is_hostel' => true,
        'have_siblings' => false,
        'dietary_restriction' => 'NONE',
        'health_concern_id' => $asthma->id,
        'is_foreigner' => false,
        'nationality_id' => $country->id,
        'conduct' => 'A',
        'remarks' => $enrollment_1_remarks,
        'enrollment_status' => 'APPROVED',
        'payment_status' => EnrollmentPaymentStatus::UNPAID->value,
        'enrollment_session_id' => $enrollment_session->id,
        'registration_date' => '2021-01-02',
        'expiry_date' => '2021-01-22',
        'admission_year' => 2027,
        'admission_grade_id' => $enrollment_session->admission_grade_id,
        'admission_type' => StudentAdmissionType::NEW->value,
    ]);

    $this->assertDatabaseHas('enrollments', [
        'name->en' => 'ALICE SMITH',
        'name->zh' => '爱丽丝·史密斯',
        'nric' => '990202123456',
        'passport_number' => 'B1234567',
        'religion_id' => $christianity->id,
        'gender' => 'FEMALE',
        'address' => '456 Side St',
        'primary_school_id' => $xyz->id,
        'is_hostel' => false,
        'have_siblings' => true,
        'dietary_restriction' => 'VEGETARIAN',
        'health_concern_id' => $none_health_concern->id,
        'is_foreigner' => false,
        'nationality_id' => $country->id,
        'conduct' => 'A',
        'remarks' => $enrollment_2_remarks,
        'enrollment_status' => 'APPROVED',
        'payment_status' => EnrollmentPaymentStatus::UNPAID->value,
        'enrollment_session_id' => $enrollment_session->id,
        'registration_date' => '2021-01-01',
        'expiry_date' => '2021-01-20',
        'admission_year' => 2027,
        'admission_grade_id' => $enrollment_session->admission_grade_id,
        'admission_type' => StudentAdmissionType::NEW->value,
    ]);

    // ENROLLMENT USERS
    $this->assertDatabaseCount('enrollment_users', 2);

    $this->assertDatabaseHas('enrollment_users', [
        'name->en' => 'Jane Doe',
        'phone_number' => '**********',
        'email' => '<EMAIL>',
    ]);

    $this->assertDatabaseHas('enrollment_users', [
        'name->en' => 'John Smith',
        'phone_number' => '**********',
        'email' => '<EMAIL>',
    ]);

    $this->assertDatabaseCount('enrollment_guardians', 2);

    $this->assertDatabaseHas('enrollment_guardians', [
        'name->en' => 'Jane Doe',
        'email' => '<EMAIL>',
        'phone_number' => '**********',
        'guardian_type' => 'MOTHER',
    ]);

    $this->assertDatabaseHas('enrollment_guardians', [
        'name->en' => 'John Smith',
        'email' => '<EMAIL>',
        'phone_number' => '**********',
        'guardian_type' => 'FATHER',
    ]);

    $first_guardian = EnrollmentUser::where([
        'phone_number' => '**********',
        'email' => '<EMAIL>',
    ])->first();

    $second_guardian = EnrollmentUser::where([
        'phone_number' => '**********',
        'email' => '<EMAIL>',
    ])->first();

    $enrollment = Enrollment::where('nric', '990101123456')->first();
    $enrollment_2 = Enrollment::where('nric', '990202123456')->first();

    expect($enrollment->enrollment_user_id)->toBe($first_guardian->id)
        ->and($enrollment_2->enrollment_user_id)->toBe($second_guardian->id);

    // ENROLLMENT EXAMS
    $this->assertDatabaseCount('enrollment_exams', 2);

    $this->assertDatabaseHas('enrollment_exams', [
        'enrollment_id' => $enrollment->id,
        'exam_slip_number' => 'E12345',
        'total_average' => 85.5,
    ]);

    $this->assertDatabaseHas('enrollment_exams', [
        'enrollment_id' => $enrollment_2->id,
        'exam_slip_number' => 'E12346',
        'total_average' => 92.5,
    ]);

    // ENROLLMENT EXAM MARKS
    $this->assertDatabaseCount('enrollment_exam_marks', 4); // 2 subjects × 2 students

    $enrollment_exam = EnrollmentExam::where('enrollment_id', $enrollment->id)->first();

    $this->assertDatabaseHas('enrollment_exam_marks', [
        'enrollment_exam_id' => $enrollment_exam->id,
        'subject_id' => $subject_1->id,
        'mark' => 90,
    ]);

    $this->assertDatabaseHas('enrollment_exam_marks', [
        'enrollment_exam_id' => $enrollment_exam->id,
        'subject_id' => $subject_2->id,
        'mark' => 81,
    ]);

    $enrollment_exam_2 = EnrollmentExam::where('enrollment_id', $enrollment_2->id)->first();

    $this->assertDatabaseHas('enrollment_exam_marks', [
        'enrollment_exam_id' => $enrollment_exam_2->id,
        'subject_id' => $subject_1->id,
        'mark' => 95,
    ]);

    $this->assertDatabaseHas('enrollment_exam_marks', [
        'enrollment_exam_id' => $enrollment_exam_2->id,
        'subject_id' => $subject_2->id,
        'mark' => 90,
    ]);
});

test('savePostPaymentImportedData', function () {
    Queue::fake();

    $enrollment_session = EnrollmentSession::factory()->create();

    $math_subject = Subject::factory()->create(['code' => 'MATH001']);
    $eng_subject = Subject::factory()->create(['code' => 'ENG001']);

    $enrollment_session->examSubjects()->attach([
        $math_subject->id,
        $eng_subject->id,
    ]);

    $enrollment1 = Enrollment::factory()->create([
        'nric' => '990101123456',
        'passport_number' => null,
        'enrollment_session_id' => $enrollment_session->id,
        'enrollment_status' => EnrollmentStatus::SHORTLISTED->value,
        'payment_status' => EnrollmentPaymentStatus::UNPAID->value,
        'is_hostel' => false,
    ]);

    $enrollment2 = Enrollment::factory()->create([
        'nric' => null,
        'passport_number' => 'A1234567',
        'enrollment_session_id' => $enrollment_session->id,
        'enrollment_status' => EnrollmentStatus::REJECTED->value,
        'payment_status' => EnrollmentPaymentStatus::UNPAID->value,
        'is_hostel' => false,
    ]);

    $enrollment3 = Enrollment::factory()->create([
        'nric' => '990202123456',
        'passport_number' => 'B9876543',
        'enrollment_session_id' => $enrollment_session->id,
        'enrollment_status' => EnrollmentStatus::APPROVED->value, // already paid
        'payment_status' => EnrollmentPaymentStatus::PAID->value,
        'is_hostel' => true,
    ]);

    $enrollment_exam1 = EnrollmentExam::factory()->create([
        'enrollment_id' => $enrollment1->id,
        'exam_slip_number' => 'E12345',
        'total_average' => 75.0,
    ]);

    $enrollment_exam2 = EnrollmentExam::factory()->create([
        'enrollment_id' => $enrollment2->id,
        'exam_slip_number' => 'E12346',
        'total_average' => 80.0,
    ]);

    $enrollment_exam3 = EnrollmentExam::factory()->create([
        'enrollment_id' => $enrollment3->id,
        'exam_slip_number' => 'E12347',
        'total_average' => 90.0,
    ]);

    // enrollment exam 1
    EnrollmentExamMark::factory()->create([
        'enrollment_exam_id' => $enrollment_exam1->id,
        'subject_id' => $math_subject->id,
        'mark' => 75.0,
    ]);

    EnrollmentExamMark::factory()->create([
        'enrollment_exam_id' => $enrollment_exam1->id,
        'subject_id' => $eng_subject->id,
        'mark' => 75.0,
    ]);

    // enrollment exam 2
    EnrollmentExamMark::factory()->create([
        'enrollment_exam_id' => $enrollment_exam2->id,
        'subject_id' => $math_subject->id,
        'mark' => 80.0,
    ]);

    EnrollmentExamMark::factory()->create([
        'enrollment_exam_id' => $enrollment_exam2->id,
        'subject_id' => $eng_subject->id,
        'mark' => 80.0,
    ]);

    EnrollmentExamMark::factory()->create([
        'enrollment_exam_id' => $enrollment_exam3->id,
        'subject_id' => $math_subject->id,
        'mark' => 90.0,
    ]);

    EnrollmentExamMark::factory()->create([
        'enrollment_exam_id' => $enrollment_exam3->id,
        'subject_id' => $eng_subject->id,
        'mark' => 90.0,
    ]);

    $data = [
        [
            'nric' => '990101123456',
            'passport_number' => '',
            'hostel' => true,
            'exam_slip_number' => 'E12345',
            'status' => EnrollmentStatus::APPROVED->value,
            'total_average' => 99.05,
            'MATH001' => 99.05,
            'ENG001' => 99.05,
        ],
        [
            'nric' => '',
            'passport_number' => 'A1234567',
            'hostel' => true,
            'exam_slip_number' => 'E12346',
            'status' => EnrollmentStatus::APPROVED->value,
            'total_average' => 88.05,
            'MATH001' => 88.05,
            'ENG001' => 88.05,
        ],
        [
            'nric' => '990202123456',
            'passport_number' => 'B9876543',
            'hostel' => true,
            'exam_slip_number' => 'E12347',
            'status' => EnrollmentStatus::APPROVED->value,
            'total_average' => 92.05,
            'MATH001' => 92.05,
            'ENG001' => 92.05,
        ]
    ];

    $this->enrollmentService
        ->setData($data)
        ->setEnrollmentSession($enrollment_session)
        ->savePostPaymentImportedData();

    Queue::assertPushed(RenotifyGuardianOfEnrollmentStatusJob::class, 2); // only 2 enrollments are to be notified

    // ENROLLMENTS

    $this->assertDatabaseCount('enrollments', 3);

    $this->assertDatabaseHas('enrollments', [
        'id' => $enrollment1->id,
        'nric' => '990101123456',
        'passport_number' => null,
        'is_hostel' => true,
        'enrollment_status' => EnrollmentStatus::APPROVED->value,
        'payment_status' => EnrollmentPaymentStatus::UNPAID->value,
        'enrollment_session_id' => $enrollment_session->id,
    ]);

    $this->assertDatabaseHas('enrollments', [
        'id' => $enrollment2->id,
        'nric' => null,
        'passport_number' => 'A1234567',
        'is_hostel' => true,
        'enrollment_status' => EnrollmentStatus::APPROVED->value,
        'payment_status' => EnrollmentPaymentStatus::UNPAID->value,
        'enrollment_session_id' => $enrollment_session->id,
    ]);

    $this->assertDatabaseHas('enrollments', [
        'id' => $enrollment3->id,
        'nric' => '990202123456',
        'passport_number' => 'B9876543',
        'is_hostel' => true,
        'enrollment_status' => EnrollmentStatus::APPROVED->value, // already paid
        'payment_status' => EnrollmentPaymentStatus::PAID->value,
        'enrollment_session_id' => $enrollment_session->id,
    ]);

    // ENROLLMENT EXAMS

    $this->assertDatabaseCount('enrollment_exams', 3);

    $this->assertDatabaseHas('enrollment_exams', [
        'enrollment_id' => $enrollment1->id,
        'total_average' => 99.05,
        'exam_slip_number' => 'E12345',
    ]);

    $this->assertDatabaseHas('enrollment_exams', [
        'enrollment_id' => $enrollment2->id,
        'total_average' => 88.05,
        'exam_slip_number' => 'E12346',
    ]);

    $this->assertDatabaseHas('enrollment_exams', [
        'enrollment_id' => $enrollment3->id,
        'total_average' => 92.05,
        'exam_slip_number' => 'E12347',
    ]);

    // ENROLLMENT EXAM MARKS

    $this->assertDatabaseCount('enrollment_exam_marks', 6); // 2 subjects × 3 students

    $this->assertDatabaseHas('enrollment_exam_marks', [
        'enrollment_exam_id' => $enrollment_exam1->id,
        'subject_id' => $math_subject->id,
        'mark' => 99.05,
    ]);

    $this->assertDatabaseHas('enrollment_exam_marks', [
        'enrollment_exam_id' => $enrollment_exam1->id,
        'subject_id' => $eng_subject->id,
        'mark' => 99.05,
    ]);

    $this->assertDatabaseHas('enrollment_exam_marks', [
        'enrollment_exam_id' => $enrollment_exam2->id,
        'subject_id' => $math_subject->id,
        'mark' => 88.05,
    ]);

    $this->assertDatabaseHas('enrollment_exam_marks', [
        'enrollment_exam_id' => $enrollment_exam2->id,
        'subject_id' => $eng_subject->id,
        'mark' => 88.05,
    ]);

    $this->assertDatabaseHas('enrollment_exam_marks', [
        'enrollment_exam_id' => $enrollment_exam3->id,
        'subject_id' => $math_subject->id,
        'mark' => 92.05,
    ]);

    $this->assertDatabaseHas('enrollment_exam_marks', [
        'enrollment_exam_id' => $enrollment_exam3->id,
        'subject_id' => $eng_subject->id,
        'mark' => 92.05,
    ]);
});

test('savePostPaymentImportedData, enrollment has multiple exams', function () {
    $enrollment_session = EnrollmentSession::factory()->create();

    $math_subject = Subject::factory()->create(['code' => 'MATH001']);
    $eng_subject = Subject::factory()->create(['code' => 'ENG001']);

    $enrollment_session->examSubjects()->attach([
        $math_subject->id,
        $eng_subject->id,
    ]);

    $enrollment1 = Enrollment::factory()->create([
        'nric' => '990101123456',
        'passport_number' => null,
        'enrollment_session_id' => $enrollment_session->id,
        'enrollment_status' => EnrollmentStatus::SHORTLISTED->value,
        'payment_status' => EnrollmentPaymentStatus::UNPAID->value,
        'is_hostel' => false,
    ]);

    $enrollment_exam_A = EnrollmentExam::factory()->create([
        'enrollment_id' => $enrollment1->id,
        'exam_slip_number' => '00000001',
        'total_average' => 50.0,
    ]);

    $enrollment_exam_B = EnrollmentExam::factory()->create([
        'enrollment_id' => $enrollment1->id,
        'exam_slip_number' => '00000002',
        'total_average' => 75.0,
    ]);


    // enrollment_exam_A
    EnrollmentExamMark::factory()->create([
        'enrollment_exam_id' => $enrollment_exam_A->id,
        'subject_id' => $math_subject->id,
        'mark' => 50.0,
    ]);

    EnrollmentExamMark::factory()->create([
        'enrollment_exam_id' => $enrollment_exam_A->id,
        'subject_id' => $eng_subject->id,
        'mark' => 50.0,
    ]);

    // enrollment_exam_B

    EnrollmentExamMark::factory()->create([
        'enrollment_exam_id' => $enrollment_exam_B->id,
        'subject_id' => $math_subject->id,
        'mark' => 75.0,
    ]);

    EnrollmentExamMark::factory()->create([
        'enrollment_exam_id' => $enrollment_exam_B->id,
        'subject_id' => $eng_subject->id,
        'mark' => 75.0,
    ]);

    // only updating enrollment_exam_B
    $data = [
        [
            'nric' => '990101123456',
            'passport_number' => null,
            'hostel' => true,
            'exam_slip_number' => '00000002',
            'status' => EnrollmentStatus::APPROVED->value,
            'total_average' => 100.0,
            'MATH001' => 100.0,
            'ENG001' => 100.0,
        ],
    ];

    $this->enrollmentService
        ->setData($data)
        ->setEnrollmentSession($enrollment_session)
        ->savePostPaymentImportedData();


    // ENROLLMENT EXAMS

    $this->assertDatabaseCount('enrollment_exams', 2);

    // EXAM A
    $this->assertDatabaseHas('enrollment_exams', [
        'enrollment_id' => $enrollment1->id,
        'exam_slip_number' => '00000001',
        'total_average' => 50.0,
    ]);

    // EXAM B
    $this->assertDatabaseHas('enrollment_exams', [
        'enrollment_id' => $enrollment1->id,
        'exam_slip_number' => '00000002',
        'total_average' => 100.0, // updated
    ]);

    // ENROLLMENT EXAM MARKS

    $this->assertDatabaseCount('enrollment_exam_marks', 4); // 2 subjects × 2 exams

    // Exam A

    $this->assertDatabaseHas('enrollment_exam_marks', [
        'enrollment_exam_id' => $enrollment_exam_A->id,
        'subject_id' => $math_subject->id,
        'mark' => 50.0,
    ]);

    $this->assertDatabaseHas('enrollment_exam_marks', [
        'enrollment_exam_id' => $enrollment_exam_A->id,
        'subject_id' => $eng_subject->id,
        'mark' => 50.0,
    ]);

    // Exam B

    $this->assertDatabaseHas('enrollment_exam_marks', [
        'enrollment_exam_id' => $enrollment_exam_B->id,
        'subject_id' => $math_subject->id,
        'mark' => 100.0, // updated
    ]);

    $this->assertDatabaseHas('enrollment_exam_marks', [
        'enrollment_exam_id' => $enrollment_exam_B->id,
        'subject_id' => $eng_subject->id,
        'mark' => 100.0, // updated
    ]);
});

test('savePostPaymentImportedData, enrollment with nullable fields', function () {
    $enrollment_session = EnrollmentSession::factory()->create();

    $math_subject = Subject::factory()->create(['code' => 'MATH001']);
    $eng_subject = Subject::factory()->create(['code' => 'ENG001']);

    $enrollment_session->examSubjects()->attach([
        $math_subject->id,
        $eng_subject->id,
    ]);

    $old_data = [
        'nric' => '990101123456',
        'passport_number' => null,
        'enrollment_session_id' => $enrollment_session->id,
        'enrollment_status' => EnrollmentStatus::SHORTLISTED->value,
        'payment_status' => EnrollmentPaymentStatus::UNPAID->value,
        'is_hostel' => false,
    ];

    $enrollment1 = Enrollment::factory()->create($old_data);

    $data = [
        // all empty, $enrollment1 data will remained
        [
            'nric' => '990101123456',
            'passport_number' => null,
            'hostel' => '',
            'exam_slip_number' => '',
            'status' => '',
            'total_average' => '',
            'MATH001' => '',
            'ENG001' => '',
        ],
    ];

    $this->enrollmentService
        ->setData($data)
        ->setEnrollmentSession($enrollment_session)
        ->savePostPaymentImportedData();

    $this->assertDatabaseCount('enrollments', 1);

    $this->assertDatabaseHas('enrollments', [
        // old data
        'nric' => '990101123456',
        'passport_number' => null,
        'enrollment_session_id' => $enrollment_session->id,
        'enrollment_status' => EnrollmentStatus::SHORTLISTED->value,
        'payment_status' => EnrollmentPaymentStatus::UNPAID->value,
        'is_hostel' => false,
    ]);


    // only update hostel field
    $data = [
        [
            'nric' => '990101123456',
            'passport_number' => null,
            'hostel' => true,
            'exam_slip_number' => '',
            'status' => '', // remains unchanged
            'total_average' => '',
            'MATH001' => '',
            'ENG001' => '',
        ],
    ];

    $this->enrollmentService
        ->setData($data)
        ->setEnrollmentSession($enrollment_session)
        ->savePostPaymentImportedData();

    $this->assertDatabaseCount('enrollments', 1);

    $this->assertDatabaseHas('enrollments', [
        'is_hostel' => true, // updated
    ]);


    // only update status field
    $data = [
        [
            'nric' => '990101123456',
            'passport_number' => null,
            'hostel' => '', // remains unchanged
            'exam_slip_number' => '',
            'status' => 'REJECTED', // to be updated
            'total_average' => '',
            'MATH001' => '',
            'ENG001' => '',
        ],
    ];

    $this->enrollmentService
        ->setData($data)
        ->setEnrollmentSession($enrollment_session)
        ->savePostPaymentImportedData();

    $this->assertDatabaseCount('enrollments', 1);

    $this->assertDatabaseHas('enrollments', [
        'is_hostel' => true, // remains unchanged
        'enrollment_status' => EnrollmentStatus::REJECTED->value, // updated
    ]);
});

test('getSiblings', function () {
    // no siblings
    $enrollment = Enrollment::factory()->create();

    expect($enrollment->getSiblings())
        ->toBeInstanceOf(Collection::class)
        ->toHaveCount(0);

    // add siblings
    $students = Student::factory(2)->create();

    $enrollment->update([
        'sibling_student_ids' => $students->pluck('id')->toArray(),
    ]);

    $new_siblings = $enrollment->getSiblings()->sortBy('id');

    expect($new_siblings)
        ->toBeInstanceOf(Collection::class)
        ->toHaveCount(2)
        ->sequence(
            fn($student) => $student->id === $students[0]->id,
            fn($student) => $student->id === $students[1]->id
        );
});

test('determineFees - full', function () {
    $course = Course::factory()->create([
        'name' => 'Test Course',
    ]);

    $malaysia = Country::factory()->create([
        'name' => ['en' => 'Malaysia', 'zh' => '马来西亚'],
    ]);

    $product_1 = Product::factory()->create([
        'name->en' => 'General Fee',
        'unit_price' => 1000,
    ]);

    $product_2 = Product::factory()->create([
        'name->en' => 'Hostel Fee',
        'unit_price' => 2000,
    ]);

    $temp_fee_assignment = [
        [
            'conditions' => null,
            'outcome' => [
                'product_id' => $product_1->id,
                'amount' => 1000,
                'period' => '2024-01-01',
            ]
        ],
        [
            'conditions' => [
                [
                    'field' => 'nationality_id',
                    'operator' => '=',
                    'value' => $malaysia->id,
                ],
                [
                    'field' => 'is_hostel',
                    'operator' => '=',
                    'value' => true,
                ],
            ],
            'outcome' => [
                'product_id' => $product_2->id,
                'amount' => 2000,
                'period' => '2024-02-01',
            ]
        ]
    ];

    $enrollment_session = EnrollmentSession::factory()->create([
        'name' => 'Enrollment Session TEST',
        'from_date' => now()->toDateString(),
        'to_date' => now()->addDays(30)->toDateString(),
        'code' => 'TEST_AA_SESSION',
        'is_active' => true,
        'course_id' => $course->id,
        'fee_assignment_settings' => $temp_fee_assignment,
    ]);

    // NON_HOSTEL ENROLLMENT
    $enrollment = Enrollment::factory()->create([
        'enrollment_session_id' => $enrollment_session->id,
        'nationality_id' => $malaysia->id,
        'name' => ['en' => 'John Doe', 'zh' => '约翰·多'],
        'nric' => '990101123456',
        'is_foreigner' => false,
        'is_hostel' => false,
    ]);

    $expected_fees = $enrollment->determineFees();

    expect($expected_fees)
        ->toBeArray()
        ->and($expected_fees[0]['product_id'])->toEqual($product_1->id)
        ->and($expected_fees[0]['amount'])->toEqual(1000)
        ->and($expected_fees[0]['period'])->toEqual('2024-01-01')
        ->and($expected_fees[0]['product']->toArray())->toEqual($product_1->toArray());
});

test('determineFees - v2 multiple scenarios', function () {
    $course = Course::factory()->create(['name' => 'Test Course']);

    $malaysia = Country::factory()->create(['name' => ['en' => 'Malaysia', 'zh' => '马来西亚']]);
    $indonesia = Country::factory()->create(['name' => ['en' => 'Indonesia', 'zh' => '印尼']]);

    $product_1 = Product::factory()->create(['name->en' => 'General Fee', 'unit_price' => 1000]);
    $product_2 = Product::factory()->create(['name->en' => 'Hostel Fee', 'unit_price' => 2000]);
    $product_3 = Product::factory()->create(['name->en' => 'Lesser Fee For Indonesian', 'unit_price' => 500]);
    $product_4 = Product::factory()->create(['name->en' => 'Admin Fee', 'unit_price' => 50]);

    $temp_fee_assignment = [
        // Scenario 1: General fee for all
        [
            'conditions' => null,
            'outcome' => [
                'product_id' => $product_1->id,
                'amount' => 1000,
                'period' => '2024-01-01',
            ]
        ],
        // Scenario 2: Hostel fee for Malaysian students in hostel
        [
            'conditions' => [
                [
                    'field' => 'nationality_id',
                    'operator' => '=',
                    'value' => $malaysia->id,
                ],
                [
                    'field' => 'is_hostel',
                    'operator' => '=',
                    'value' => true,
                ],
            ],
            'outcome' => [
                'product_id' => $product_2->id,
                'amount' => 2000,
                'period' => '2024-02-01',
            ]
        ],
        // Scenario 3: Lesser Fee for Indonesian students
        [
            'conditions' => [
                [
                    'field' => 'nationality_id',
                    'operator' => '=',
                    'value' => $indonesia->id,
                ],
            ],
            'outcome' => [
                'product_id' => $product_3->id,
                'amount' => 500,
                'period' => '2024-03-01',
            ]
        ],
        // Scenario 4: Admin fee for everyone NOT in hostel
        [
            'conditions' => [
                [
                    'field' => 'is_hostel',
                    'operator' => '!=',
                    'value' => true,
                ],
            ],
            'outcome' => [
                'product_id' => $product_4->id,
                'amount' => 50,
                'period' => '2024-04-01',
            ]
        ],
    ];

    $enrollment_session = EnrollmentSession::factory()->create([
        'name' => 'Enrollment Session TEST',
        'from_date' => now()->toDateString(),
        'to_date' => now()->addDays(30)->toDateString(),
        'code' => 'TEST_AA_SESSION',
        'is_active' => true,
        'course_id' => $course->id,
        'fee_assignment_settings' => $temp_fee_assignment,
    ]);

    // Test Case 1: Malaysian, NOT in hostel
    $enrollment_1 = Enrollment::factory()->create([
        'enrollment_session_id' => $enrollment_session->id,
        'nationality_id' => $malaysia->id,
        'is_hostel' => false,
    ]);

    $expected_fees_1 = $enrollment_1->determineFees();

    expect($expected_fees_1)
        ->toBeArray()
        ->toHaveCount(2)
        ->and($expected_fees_1[0]['product_id'])->toBe($product_1->id)
        ->and($expected_fees_1[1]['product_id'])->toBe($product_4->id);

    // Test Case 2: Malaysian, in hostel
    $enrollment_2 = Enrollment::factory()->create([
        'enrollment_session_id' => $enrollment_session->id,
        'nationality_id' => $malaysia->id,
        'is_hostel' => true,
    ]);

    $expected_fees_2 = $enrollment_2->determineFees();

    expect($expected_fees_2)
        ->toBeArray()
        ->toHaveCount(2)
        ->and($expected_fees_2[0]['product_id'])->toBe($product_1->id)
        ->and($expected_fees_2[1]['product_id'])->toBe($product_2->id);

    // Test Case 3: Indonesian, NOT in hostel
    $enrollment_3 = Enrollment::factory()->create([
        'enrollment_session_id' => $enrollment_session->id,
        'nationality_id' => $indonesia->id,
        'is_hostel' => false,
    ]);

    $expected_fees_3 = $enrollment_3->determineFees();

    expect($expected_fees_3)
        ->toBeArray()
        ->toHaveCount(3)
        ->and($expected_fees_3[0]['product_id'])->toBe($product_1->id)
        ->and($expected_fees_3[1]['product_id'])->toBe($product_3->id)
        ->and($expected_fees_3[2]['product_id'])->toBe($product_4->id);

    // Test Case 4: Indonesian, in hostel
    $enrollment_4 = Enrollment::factory()->create([
        'enrollment_session_id' => $enrollment_session->id,
        'nationality_id' => $indonesia->id,
        'is_hostel' => true,
    ]);

    $expected_fees_4 = $enrollment_4->determineFees();

    expect($expected_fees_4)
        ->toBeArray()
        ->toHaveCount(2)
        ->and($expected_fees_4[0]['product_id'])->toBe($product_1->id)
        ->and($expected_fees_4[1]['product_id'])->toBe($product_3->id);

    // Test Case 5: No nationality, NOT in hostel
    $enrollment_5 = Enrollment::factory()->create([
        'enrollment_session_id' => $enrollment_session->id,
        'nationality_id' => null,
        'is_hostel' => false,
    ]);

    $expected_fees_5 = $enrollment_5->determineFees();

    expect($expected_fees_5)
        ->toBeArray()
        ->toHaveCount(2)
        ->and($expected_fees_5[0]['product_id'])->toBe($product_1->id)
        ->and($expected_fees_5[1]['product_id'])->toBe($product_4->id);

    // Test Case 6: No nationality, in hostel
    $enrollment_6 = Enrollment::factory()->create([
        'enrollment_session_id' => $enrollment_session->id,
        'nationality_id' => null,
        'is_hostel' => true,
    ]);

    $expected_fees_6 = $enrollment_6->determineFees();

    expect($expected_fees_6)
        ->toBeArray()
        ->toHaveCount(1)
        ->and($expected_fees_6[0]['product_id'])->toBe($product_1->id);
});

test('markAsPaid', function () {
    $enrollment = Enrollment::factory()->create([
        'enrollment_status' => EnrollmentStatus::APPROVED,
        'payment_status' => EnrollmentPaymentStatus::UNPAID,
    ]);

    $payment = Payment::factory()->create([
        'paid_at' => '2024-06-20 09:00:00',
    ]);
    $payment2 = Payment::factory()->create([
        'paid_at' => '2024-06-21 10:00:00',
    ]);

    $enrollment->markAsPaid([$payment, $payment2]);
    $enrollment->refresh();

    expect($enrollment->payment_status)
        ->toBe(EnrollmentPaymentStatus::PAID);
});

test('delete : success', function () {
    $enrollment_user = EnrollmentUser::factory()->create();

    $enrollment = Enrollment::factory()->create([
        'enrollment_status' => EnrollmentStatus::APPROVED,
        'payment_status' => EnrollmentPaymentStatus::UNPAID,
        'enrollment_user_id' => $enrollment_user->id,
    ]);

    $exams = EnrollmentExam::factory(3)->create([
        'enrollment_id' => $enrollment->id,
    ]);

    $marks_for_exam_1 = EnrollmentExamMark::factory(3)->create([
        'enrollment_exam_id' => $exams[0]->id,
    ]);

    $marks_for_exam_2 = EnrollmentExamMark::factory(2)->create([
        'enrollment_exam_id' => $exams[1]->id,
    ]);

    $marks_for_exam_3 = EnrollmentExamMark::factory(1)->create([
        'enrollment_exam_id' => $exams[2]->id,
    ]);

    $guardians = EnrollmentGuardian::factory(2)->create([
        'enrollment_id' => $enrollment->id,
    ]);

    $enrollment->refresh();

    expect($enrollment->enrollmentExams)->toHaveCount(3)
        ->and($enrollment->enrollmentExams->sortBy('id')[0]->examMarks)->toHaveCount(3)
        ->and($enrollment->enrollmentExams->sortBy('id')[1]->examMarks)->toHaveCount(2)
        ->and($enrollment->enrollmentExams->sortBy('id')[2]->examMarks)->toHaveCount(1)
        ->and($enrollment->guardians)->toHaveCount(2);

    $this->assertDatabaseCount('enrollments', 1);
    $this->assertDatabaseCount('enrollment_users', 1);
    $this->assertDatabaseCount('enrollment_exams', 3);
    $this->assertDatabaseCount('enrollment_exam_marks', 6);
    $this->assertDatabaseCount('enrollment_guardians', 2);


    /**
     * DELETE
     */
    $this->enrollmentService->delete($enrollment);


    // ENROLLMENTUSER is not deleted
    $this->assertDatabaseHas('enrollment_users', [
        'id' => $enrollment_user->id,
    ]);

    // ENROLLMENT is deleted
    $this->assertDatabaseMissing('enrollments', [
        'id' => $enrollment->id,
    ]);

    // ALL EXAMS
    $this->assertDatabaseMissing('enrollment_exams', [
        'id' => $exams[0]->id,
    ]);

    $this->assertDatabaseMissing('enrollment_exams', [
        'id' => $exams[1]->id,
    ]);

    $this->assertDatabaseMissing('enrollment_exams', [
        'id' => $exams[2]->id,
    ]);

    // EXAM MARK 1
    $this->assertDatabaseMissing('enrollment_exam_marks', [
        'id' => $marks_for_exam_1[0]->id,
    ]);

    $this->assertDatabaseMissing('enrollment_exam_marks', [
        'id' => $marks_for_exam_1[1]->id,
    ]);

    $this->assertDatabaseMissing('enrollment_exam_marks', [
        'id' => $marks_for_exam_1[2]->id,
    ]);

    // EXAM MARK 2
    $this->assertDatabaseMissing('enrollment_exam_marks', [
        'id' => $marks_for_exam_2[0]->id,
    ]);

    $this->assertDatabaseMissing('enrollment_exam_marks', [
        'id' => $marks_for_exam_2[1]->id,
    ]);

    // EXAM MARK 3
    $this->assertDatabaseMissing('enrollment_exam_marks', [
        'id' => $marks_for_exam_3[0]->id,
    ]);

    // GUARDIANS
    $this->assertDatabaseMissing('enrollment_guardians', [
        'id' => $guardians[0]->id,
    ]);

    $this->assertDatabaseMissing('enrollment_guardians', [
        'id' => $guardians[1]->id,
    ]);

    $this->assertDatabaseCount('enrollments', 0);
    $this->assertDatabaseCount('enrollment_users', 1);
    $this->assertDatabaseCount('enrollment_exams', 0);
    $this->assertDatabaseCount('enrollment_exam_marks', 0);
    $this->assertDatabaseCount('enrollment_guardians', 0);
});

test('delete : failed because of validation', function () {

    // Cannot delete because there is PAID billing_documents
    $enrollment_user = EnrollmentUser::factory()->create();

    $enrollment = Enrollment::factory()->create([
        'enrollment_status' => EnrollmentStatus::APPROVED,
        'payment_status' => EnrollmentPaymentStatus::UNPAID,
        'enrollment_user_id' => $enrollment_user->id,
    ]);

    $billing_document = BillingDocument::factory()->create([
        'bill_to_type' => Enrollment::class,
        'bill_to_id' => $enrollment->id,
        'status' => BillingDocument::STATUS_CONFIRMED,
        'payment_status' => BillingDocument::PAYMENT_STATUS_PAID,
    ]);

    $this->assertDatabaseCount('enrollments', 1);
    $this->assertDatabaseCount('enrollment_users', 1);
    $this->assertDatabaseCount('billing_documents', 1);

    $enrollment->refresh();

    expect($enrollment->billingDocuments)->toHaveCount(1);


    /**
     * attempt to delete enrollment with PAID billing_document
     */
    expect(function () use ($enrollment) {
        $this->enrollmentService->delete($enrollment);
    })->toThrow(function (Exception $e) {
        expect($e->getCode())->toBe(5028);
    }, __('system_error.5028'));


    $this->assertDatabaseCount('enrollments', 1);
    $this->assertDatabaseCount('enrollment_users', 1);
    $this->assertDatabaseCount('billing_documents', 1);


    /**
     * delete ok
     */
    expect(function () use ($enrollment, $billing_document) {
        // update billing_document to UNPAID
        $billing_document->delete();

        $this->enrollmentService->delete($enrollment);
    })->not->toThrow(Exception::class);

    $this->assertDatabaseCount('enrollments', 0);
    $this->assertDatabaseCount('enrollment_users', 1);
    $this->assertDatabaseCount('billing_documents', 0);
});


test('extendExpiry', function () {
    $enrollment = Enrollment::factory()->create([
        'expiry_date' => '2023-01-01',
    ]);

    $new_expiry_date = Carbon::parse('2027-02-15');

    $updated_enrollment = $this->enrollmentService->extendExpiry($enrollment, $new_expiry_date);

    expect($updated_enrollment)->not->toBeNull();
    expect($updated_enrollment)->toBeInstanceOf(Enrollment::class);
    expect($updated_enrollment->id)->toBe($enrollment->id);
    expect($updated_enrollment->expiry_date)->toBe('2027-02-15');

    $this->assertDatabaseHas('enrollments', [
        'id' => $enrollment->id,
        'expiry_date' => '2027-02-15',
    ]);
});


test('update : as admin', function () {
    $enrollment = Enrollment::factory()->create();

    $grade = Grade::factory()->create();
    $race = Race::factory()->create();
    $state = State::factory()->create();
    $country = Country::factory()->create();
    $religion = Religion::factory()->create();
    $health_concern = HealthConcern::factory()->create();
    $school = School::factory()->create(['level' => SchoolLevel::PRIMARY]);

    $data = [
        'name' => ['en' => 'Admin Updated Name', 'zh' => '管理员更新名称'],
        'email' => '<EMAIL>',
        'phone_number' => '**********',
        'phone_number_2' => '**********',
        'nric' => '900101101234',
        'passport_number' => 'A12345678',
        'admission_year' => 2025,
        'admission_grade_id' => $grade->id,
        'join_date' => '2025-01-15',
        'leave_date' => null,
        'birthplace' => 'Kuala Lumpur',
        'nationality_id' => $country->id,
        'date_of_birth' => '2010-05-20',
        'gender' => Gender::MALE->value,
        'birth_cert_number' => 'BC123456',
        'race_id' => $race->id,
        'religion_id' => $religion->id,
        'address' => '123 Admin Updated St, Admin City',
        'postal_code' => '50000',
        'city' => 'Admin City',
        'state_id' => $state->id,
        'country_id' => $country->id,
        'address_2' => 'Apt 101, Admin Tower',
        'is_hostel' => true,
        'is_active' => true,
        'remarks' => 'Updated by admin with new details.',
        'custom_field' => ['key1' => 'admin_value1', 'key2' => 'admin_value2'],
        'dietary_restriction' => DietaryRestriction::VEGETARIAN->value,
        'health_concern_id' => $health_concern->id,
        'primary_school_id' => $school->id,
        'admission_type' => 'NEW',
        'have_siblings' => true,
        'is_foreigner' => false,
        'conduct' => 'A+',
        'enrollment_status' => EnrollmentStatus::APPROVED->value,
    ];

    $updated_enrollment = $this->enrollmentService->update($enrollment, $data, true);

    expect($updated_enrollment->toArray())->toMatchArray([
        'name' => [
            'en' => $data['name']['en'],
            'zh' => $data['name']['zh'],
        ],
        'email' => $data['email'],
        'phone_number' => $data['phone_number'],
        'phone_number_2' => $data['phone_number_2'],
        'nric' => $data['nric'],
        'passport_number' => $data['passport_number'],
        'admission_year' => $data['admission_year'],
        'admission_grade_id' => $data['admission_grade_id'],
        'join_date' => $data['join_date'],
        'leave_date' => $data['leave_date'],
        'birthplace' => $data['birthplace'],
        'nationality_id' => $data['nationality_id'],
        'date_of_birth' => $data['date_of_birth'],
        'gender' => $data['gender'],
        'birth_cert_number' => $data['birth_cert_number'],
        'race_id' => $data['race_id'],
        'religion_id' => $data['religion_id'],
        'address' => $data['address'],
        'postal_code' => $data['postal_code'],
        'city' => $data['city'],
        'state_id' => $data['state_id'],
        'country_id' => $data['country_id'],
        'address_2' => $data['address_2'],
        'is_hostel' => $data['is_hostel'],
        'is_active' => $data['is_active'],
        'remarks' => $data['remarks'],
        'custom_field' => $data['custom_field'],
        'dietary_restriction' => $data['dietary_restriction'],
        'health_concern_id' => $data['health_concern_id'],
        'primary_school_id' => $data['primary_school_id'],
        'admission_type' => $data['admission_type'],
        'have_siblings' => $data['have_siblings'],
        'is_foreigner' => $data['is_foreigner'],
        'conduct' => $data['conduct'],
    ]);

    $this->assertDatabaseHas('enrollments', [
        'name->en' => $data['name']['en'],
        'name->zh' => $data['name']['zh'],
        'email' => $data['email'],
        'phone_number' => $data['phone_number'],
        'phone_number_2' => $data['phone_number_2'],
        'nric' => $data['nric'],
        'passport_number' => $data['passport_number'],
        'admission_year' => $data['admission_year'],
        'admission_grade_id' => $data['admission_grade_id'],
        'join_date' => $data['join_date'],
        'leave_date' => $data['leave_date'],
        'birthplace' => $data['birthplace'],
        'nationality_id' => $data['nationality_id'],
        'date_of_birth' => $data['date_of_birth'],
        'gender' => $data['gender'],
        'birth_cert_number' => $data['birth_cert_number'],
        'race_id' => $data['race_id'],
        'religion_id' => $data['religion_id'],
        'address' => $data['address'],
        'postal_code' => $data['postal_code'],
        'city' => $data['city'],
        'state_id' => $data['state_id'],
        'country_id' => $data['country_id'],
        'address_2' => $data['address_2'],
        'is_hostel' => $data['is_hostel'],
        'is_active' => $data['is_active'],
        'remarks' => $data['remarks'],
        'dietary_restriction' => $data['dietary_restriction'],
        'health_concern_id' => $data['health_concern_id'],
        'primary_school_id' => $data['primary_school_id'],
        'admission_type' => $data['admission_type'],
        'have_siblings' => $data['have_siblings'],
        'is_foreigner' => $data['is_foreigner'],
        'conduct' => $data['conduct'],
    ]);
});

test('update : as user, limited fields', function () {
    $enrollment = Enrollment::factory()->create();

    $race = Race::factory()->create();
    $state = State::factory()->create();
    $country = Country::factory()->create();
    $religion = Religion::factory()->create();
    $health_concern = HealthConcern::factory()->create();
    $school = School::factory()->create(['level' => SchoolLevel::PRIMARY]);

    $data = [
        'name' => ['en' => 'John Doe', 'zh' => '管理员更新名称'],
        'email' => '<EMAIL>',
        'phone_number' => '**********',
        'phone_number_2' => '**********',
        'nric' => '900101101234',
        'passport_number' => 'A12345678',
        'birthplace' => 'Kuala Lumpur',
        'nationality_id' => $country->id,
        'date_of_birth' => '2010-05-20',
        'gender' => Gender::MALE->value,
        'birth_cert_number' => 'BC123456',
        'race_id' => $race->id,
        'religion_id' => $religion->id,
        'address' => '123 John Updated St, John City',
        'postal_code' => '50000',
        'city' => 'John City',
        'state_id' => $state->id,
        'country_id' => $country->id,
        'address_2' => 'Apt 101, John Tower',
        'dietary_restriction' => DietaryRestriction::VEGETARIAN->value,
        'health_concern_id' => $health_concern->id,
        'primary_school_id' => $school->id,
    ];

    $updated_enrollment = $this->enrollmentService->update($enrollment, $data, false);

    expect($updated_enrollment->toArray())->toMatchArray([
        'name' => [
            'en' => $enrollment->getTranslation('name', 'en'),
            'zh' => $enrollment->getTranslation('name', 'zh'),
        ],
        'email' => $data['email'],
        'phone_number' => $data['phone_number'],
        'phone_number_2' => $data['phone_number_2'],
        'nric' => $enrollment->nric,
        'passport_number' => $enrollment->passport_number,
        'birthplace' => $data['birthplace'],
        'nationality_id' => $data['nationality_id'],
        'date_of_birth' => $data['date_of_birth'],
        'gender' => $data['gender'],
        'birth_cert_number' => $data['birth_cert_number'],
        'race_id' => $data['race_id'],
        'religion_id' => $data['religion_id'],
        'address' => $data['address'],
        'postal_code' => $data['postal_code'],
        'city' => $data['city'],
        'state_id' => $data['state_id'],
        'country_id' => $data['country_id'],
        'address_2' => $data['address_2'],
        'dietary_restriction' => $data['dietary_restriction'],
        'health_concern_id' => $data['health_concern_id'],
        'primary_school_id' => $data['primary_school_id'],
    ]);

    $this->assertDatabaseHas('enrollments', [
        'name->en' => $enrollment->getTranslation('name', 'en'),
        'name->zh' => $enrollment->getTranslation('name', 'zh'),
        'email' => $data['email'],
        'phone_number' => $data['phone_number'],
        'phone_number_2' => $data['phone_number_2'],
        'nric' => $enrollment->nric,
        'passport_number' => $enrollment->passport_number,
        'birthplace' => $data['birthplace'],
        'nationality_id' => $data['nationality_id'],
        'date_of_birth' => $data['date_of_birth'],
        'gender' => $data['gender'],
        'birth_cert_number' => $data['birth_cert_number'],
        'race_id' => $data['race_id'],
        'religion_id' => $data['religion_id'],
        'address' => $data['address'],
        'postal_code' => $data['postal_code'],
        'city' => $data['city'],
        'state_id' => $data['state_id'],
        'country_id' => $data['country_id'],
        'address_2' => $data['address_2'],
        'dietary_restriction' => $data['dietary_restriction'],
        'health_concern_id' => $data['health_concern_id'],
        'primary_school_id' => $data['primary_school_id'],
    ]);
});

test('update : with guardians', function () {
    $enrollment = Enrollment::factory()->create();

    $guardian_1 = EnrollmentGuardian::factory()->create([
        'enrollment_id' => $enrollment->id,
    ]);
    $guardian_2 = EnrollmentGuardian::factory()->create([
        'enrollment_id' => $enrollment->id,
    ]);

    $race = Race::factory()->create();
    $state = State::factory()->create();
    $country = Country::factory()->create();
    $religion = Religion::factory()->create();
    $health_concern = HealthConcern::factory()->create();
    $school = School::factory()->create(['level' => SchoolLevel::PRIMARY]);

    $data = [
        'name' => ['en' => 'John Doe', 'zh' => '管理员更新名称'],
        'email' => '<EMAIL>',
        'phone_number' => '**********',
        'phone_number_2' => '**********',
        'nric' => '900101101234',
        'passport_number' => 'A12345678',
        'birthplace' => 'Kuala Lumpur',
        'nationality_id' => $country->id,
        'date_of_birth' => '2010-05-20',
        'gender' => Gender::MALE->value,
        'birth_cert_number' => 'BC123456',
        'race_id' => $race->id,
        'religion_id' => $religion->id,
        'address' => '123 John Updated St, John City',
        'postal_code' => '50000',
        'city' => 'John City',
        'state_id' => $state->id,
        'country_id' => $country->id,
        'address_2' => 'Apt 101, John Tower',
        'dietary_restriction' => DietaryRestriction::VEGETARIAN->value,
        'health_concern_id' => $health_concern->id,
        'primary_school_id' => $school->id,
        'guardians' => [
            [
                'name' => ['en' => 'John'],
                'email' => '<EMAIL>',
                'phone_number' => '**********',
                'nric' => '900101101234',
                'passport_number' => 'A12345678',
                'nationality_id' => $country->id,
                'race_id' => $race->id,
                'religion_id' => $religion->id,
                'education_id' => null,
                'married_status' => MarriedStatus::MARRIED->value,
                'live_status' => LiveStatus::NORMAL->value,
                'occupation' => 'Engineer',
                'occupation_description' => null,
                'is_primary' => true,
                'guardian_type' => GuardianType::FATHER->value,
            ],
            [
                'name' => ['en' => 'Maria'],
                'email' => '<EMAIL>',
                'phone_number' => '**********',
                'nric' => '900101101111',
                'passport_number' => 'B987654321',
                'nationality_id' => $country->id,
                'race_id' => $race->id,
                'religion_id' => $religion->id,
                'education_id' => null,
                'married_status' => MarriedStatus::SINGLE->value,
                'live_status' => LiveStatus::NORMAL->value,
                'occupation' => 'Teacher',
                'occupation_description' => 'Secondary school math teacher',
                'is_primary' => false,
                'guardian_type' => GuardianType::MOTHER->value,
            ],
        ],
    ];

    $enrollment->refresh();

    expect($enrollment->guardians->count())->toBe(2);

    $updated_enrollment = $this->enrollmentService->update($enrollment, $data, false);

    $this->assertDatabaseMissing('enrollment_guardians', [
        'id' => $guardian_1->id,
    ]);

    $this->assertDatabaseMissing('enrollment_guardians', [
        'id' => $guardian_2->id,
    ]);

    foreach ($data['guardians'] as $guardian_data) {
        $this->assertDatabaseHas('enrollment_guardians', [
            'enrollment_id' => $updated_enrollment->id,
            'name->en' => $guardian_data['name']['en'],
            'email' => $guardian_data['email'],
            'phone_number' => $guardian_data['phone_number'],
            'nric' => $guardian_data['nric'],
            'passport_number' => $guardian_data['passport_number'],
            'nationality_id' => $guardian_data['nationality_id'],
            'race_id' => $guardian_data['race_id'],
            'religion_id' => $guardian_data['religion_id'],
            'education_id' => $guardian_data['education_id'],
            'married_status' => $guardian_data['married_status'],
            'live_status' => $guardian_data['live_status'],
            'occupation' => $guardian_data['occupation'],
            'occupation_description' => $guardian_data['occupation_description'],
            'is_primary' => $guardian_data['is_primary'],
            'guardian_type' => $guardian_data['guardian_type'],
        ]);
    }
});

test('update (ADMIN) : with exams + marks', function () {
    $enrollment = Enrollment::factory()->create();

    // with exam + marks
    $exam = EnrollmentExam::factory()->create([
        'enrollment_id' => $enrollment->id,
        'total_average' => 85.05,
    ]);

    $marks = EnrollmentExamMark::factory(2)->create([
        'enrollment_exam_id' => $exam->id,
        'mark' => 85.05,
    ]);

    $race = Race::factory()->create();
    $state = State::factory()->create();
    $country = Country::factory()->create();
    $religion = Religion::factory()->create();
    $health_concern = HealthConcern::factory()->create();
    $school = School::factory()->create(['level' => SchoolLevel::PRIMARY]);

    $data = [
        'name' => ['en' => 'John Doe', 'zh' => '管理员更新名称'],
        'email' => '<EMAIL>',
        'phone_number' => '**********',
        'phone_number_2' => '**********',
        'nric' => '900101101234',
        'passport_number' => 'A12345678',
        'birthplace' => 'Kuala Lumpur',
        'nationality_id' => $country->id,
        'date_of_birth' => '2010-05-20',
        'gender' => Gender::MALE->value,
        'birth_cert_number' => 'BC123456',
        'race_id' => $race->id,
        'religion_id' => $religion->id,
        'address' => '123 John Updated St, John City',
        'postal_code' => '50000',
        'city' => 'John City',
        'state_id' => $state->id,
        'country_id' => $country->id,
        'address_2' => 'Apt 101, John Tower',
        'dietary_restriction' => DietaryRestriction::VEGETARIAN->value,
        'health_concern_id' => $health_concern->id,
        'primary_school_id' => $school->id,
        'enrollment_exams' => [
            [
                'exam_id' => $exam->id,
                'total_average' => 90.05,
                'marks' => [
                    [
                        'exam_mark_id' => $marks[0]->id,
                        'mark' => 85.05,
                    ],
                    [
                        'exam_mark_id' => $marks[1]->id,
                        'mark' => 95.05,
                    ],
                ],
            ],
        ],
    ];

    $enrollment->refresh();

    expect($enrollment->enrollmentExams->count())->toBe(1);
    expect($enrollment->enrollmentExams->first()->examMarks->count())->toBe(2);

    $updated_enrollment = $this->enrollmentService->update($enrollment, $data, false);

    $this->assertDatabaseHas('enrollment_exams', [
        'id' => $exam->id,
        'enrollment_id' => $updated_enrollment->id,
        'total_average' => 90.05, // updated
    ]);

    $this->assertDatabaseHas('enrollment_exam_marks', [
        'id' => $data['enrollment_exams'][0]['marks'][0]['exam_mark_id'],
        'enrollment_exam_id' => $exam->id,
        'mark' => 85.05, // updated
    ]);

    $this->assertDatabaseHas('enrollment_exam_marks', [
        'id' => $data['enrollment_exams'][0]['marks'][1]['exam_mark_id'],
        'enrollment_exam_id' => $exam->id,
        'mark' => 95.05, // updated
    ]);
});

test('update (ADMIN) : with sibling_student_numbers', function () {
    $enrollment = Enrollment::factory()->create([
        'sibling_student_ids' => [],
    ]);

    $race = Race::factory()->create();
    $state = State::factory()->create();
    $country = Country::factory()->create();
    $religion = Religion::factory()->create();
    $health_concern = HealthConcern::factory()->create();
    $school = School::factory()->create(['level' => SchoolLevel::PRIMARY]);

    $students = Student::factory(3)->state(new Sequence(
        [
            'student_number' => 'S00001',
        ],
        [
            'student_number' => 'S00002',
        ],
        [
            'student_number' => 'S00003',
        ],
    ))->create();

    $data = [
        'name' => ['en' => 'John Doe', 'zh' => '管理员更新名称'],
        'email' => '<EMAIL>',
        'phone_number' => '**********',
        'phone_number_2' => '**********',
        'nric' => '900101101234',
        'passport_number' => 'A12345678',
        'birthplace' => 'Kuala Lumpur',
        'nationality_id' => $country->id,
        'date_of_birth' => '2010-05-20',
        'gender' => Gender::MALE->value,
        'birth_cert_number' => 'BC123456',
        'race_id' => $race->id,
        'religion_id' => $religion->id,
        'address' => '123 John Updated St, John City',
        'postal_code' => '50000',
        'city' => 'John City',
        'state_id' => $state->id,
        'country_id' => $country->id,
        'address_2' => 'Apt 101, John Tower',
        'dietary_restriction' => DietaryRestriction::VEGETARIAN->value,
        'health_concern_id' => $health_concern->id,
        'primary_school_id' => $school->id,
        'sibling_student_numbers' => [
            $students[0]->student_number,
            $students[1]->student_number,
            $students[2]->student_number,
        ],
    ];

    $updated_enrollment = $this->enrollmentService->update($enrollment, $data, false);

    expect($updated_enrollment->sibling_student_ids)
        ->toBeArray()
        ->toHaveCount(3)
        ->toMatchArray([
            $students[2]->id,
            $students[1]->id,
            $students[0]->id,
        ]);

    $this->assertDatabaseHas('enrollments', [
        'id' => $updated_enrollment->id,
        'sibling_student_ids' => json_encode([
            $students[2]->id,
            $students[1]->id,
            $students[0]->id,
        ]),
    ]);


    // update to remove siblings
    $data['sibling_student_numbers'] = [];

    $updated_enrollment = $this->enrollmentService->update($enrollment, $data, false);

    expect($updated_enrollment->sibling_student_ids)
        ->toBeArray()
        ->toHaveCount(0)
        ->toMatchArray([]);

    $this->assertDatabaseHas('enrollments', [
        'id' => $updated_enrollment->id,
        'sibling_student_ids' => json_encode([]),
    ]);
});

test('updateEnrollmentStatus()', function () {
    $enrollment = Enrollment::factory()->create();

    $updated_enrollment = $this->enrollmentService->setEnrollment($enrollment)
        ->updateEnrollmentStatus(EnrollmentStatus::APPROVED);

    expect($updated_enrollment->enrollment_status)->toBe(EnrollmentStatus::APPROVED->value);

    $updated_enrollment = $this->enrollmentService->setEnrollment($enrollment)
        ->updateEnrollmentStatus(EnrollmentStatus::REJECTED);

    expect($updated_enrollment->enrollment_status)->toBe(EnrollmentStatus::REJECTED->value);
});

test('Mail - sending without reply to', function () {
    $enrollment = Enrollment::factory()->create();

    Mail::fake();
    Mail::to('<EMAIL>')->send(new EnrollmentStatusMail($enrollment));

    Mail::assertSent(EnrollmentStatusMail::class, function (EnrollmentStatusMail $mail) {
        return $mail->envelope()->replyTo == [];
    });
});

test('Mail - sending with reply to', function () {
    $enrollment = Enrollment::factory()->create();
    ConfigHelper::put(\App\Models\Config::ENROLLMENT_REPLY_TO_EMAIL, '<EMAIL>, <EMAIL>', \App\Models\Config::CATEGORY_GENERAL);

    Mail::fake();
    Mail::to('<EMAIL>')->send(new EnrollmentStatusMail($enrollment));

    Mail::assertSent(EnrollmentStatusMail::class, function (EnrollmentStatusMail $mail) {
        return $mail->hasReplyTo([
            '<EMAIL>',
            '<EMAIL>',
        ]);
    });
});
