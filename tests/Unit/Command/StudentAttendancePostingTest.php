<?php

use App\Console\Commands\Attendance\StudentAttendancePosting;
use App\Enums\AttendanceCheckInStatus;
use App\Enums\AttendanceCheckOutStatus;
use App\Enums\AttendanceStatus;
use App\Enums\ClassType;
use App\Enums\Day;
use App\Enums\LeaveApplicationStatus;
use App\Enums\PeriodAttendanceStatus;
use App\Helpers\ConfigHelper;
use App\Models\Attendance;
use App\Models\AttendanceArchive;
use App\Models\AttendanceInput;
use App\Models\AttendancePeriodOverride;
use App\Models\AttendancePeriodOverrideLeaveApplication;
use App\Models\Calendar;
use App\Models\CalendarSetting;
use App\Models\CalendarTarget;
use App\Models\Card;
use App\Models\ClassModel;
use App\Models\Config;
use App\Models\Country;
use App\Models\Employee;
use App\Models\Grade;
use App\Models\HealthConcern;
use App\Models\LeaveApplication;
use App\Models\LeaveApplicationPeriod;
use App\Models\LeaveApplicationType;
use App\Models\Period;
use App\Models\PeriodAttendance;
use App\Models\PeriodGroup;
use App\Models\PeriodLabel;
use App\Models\Race;
use App\Models\Religion;
use App\Models\SchoolAttendancePeriodOverride;
use App\Models\SemesterClass;
use App\Models\SemesterSetting;
use App\Models\State;
use App\Models\Student;
use App\Models\StudentClass;
use App\Models\StudentTimetable;
use App\Models\Timeslot;
use App\Models\TimeslotOverride;
use App\Models\TimeslotTeacher;
use App\Models\Timetable;
use App\Services\LeaveApplicationService;
use Carbon\Carbon;
use Database\Seeders\PermissionSeeder;
use Laravel\Sanctum\Sanctum;

beforeEach(function () {
    $this->seed([
        PermissionSeeder::class,
    ]);
    $this->attendanceTable = resolve(Attendance::class)->getTable();
    $this->attendanceInputTable = resolve(AttendanceInput::class)->getTable();
    $this->attendanceArchiveTable = resolve(AttendanceArchive::class)->getTable();
    $this->periodAttendanceTable = resolve(PeriodAttendance::class)->getTable();

    $this->systemEmployee = Employee::factory()->create(['employee_number' => Employee::SYSTEM_ID]);

    config(['school.timezone' => 'Asia/Kuala_Lumpur']);
});

test('getEarliestAndLatestRecord multiple inputs - debounce test', function () {
    ConfigHelper::put(Config::ATTENDANCE_TAP_CARD_INTERVAL_SECOND, 300, Config::CATEGORY_GENERAL);

    $attendance_inputs = collect([
        [
            'record_datetime' => '2025-02-16 12:00:00'
        ],
        [
            'record_datetime' => '2025-02-16 12:01:00'
        ],
        [
            'record_datetime' => '2025-02-16 12:02:00'
        ],
        [
            'record_datetime' => '2025-02-16 12:03:00'
        ],
        [
            'record_datetime' => '2025-02-16 12:04:00'
        ],
    ]);

    $service = app()->make(StudentAttendancePosting::class);
    $data = $service->setInterval()->getEarliestAndLatestRecord($attendance_inputs);

    expect($data)->toMatchArray([
        'earliest' => [
            'record_datetime' => '2025-02-16 12:00:00'
        ],
        'latest' => null
    ]);

    $attendance_inputs = collect([
        [
            'record_datetime' => '2025-02-16 12:00:00'
        ],
        [
            'record_datetime' => '2025-02-16 12:01:00'
        ],
        [
            'record_datetime' => '2025-02-16 12:02:00'
        ],
        [
            'record_datetime' => '2025-02-16 12:03:00'
        ],
        [
            'record_datetime' => '2025-02-16 12:04:00'
        ],
        [
            'record_datetime' => '2025-02-16 12:05:00'
        ],
    ]);

    $data = $service->getEarliestAndLatestRecord($attendance_inputs);

    expect($data)->toMatchArray([
        'earliest' => [
            'record_datetime' => '2025-02-16 12:00:00'
        ],
        'latest' => [
            'record_datetime' => '2025-02-16 12:05:00'
        ],
    ]);
});

test('getEarliestAndLatestRecord multiple inputs', function () {

    $attendance_inputs = collect([
        [
            'record_datetime' => '2025-02-16 12:00:00'
        ],
        [
            'record_datetime' => '2025-02-15 12:00:01'
        ],
        [
            'record_datetime' => '2025-02-15 12:00:01'
        ],
        [
            'record_datetime' => '2025-01-31 08:20:00'
        ],
        [
            'record_datetime' => '2025-02-01 23:59:12'
        ],
        [
            'record_datetime' => '2024-10-30 15:20:03'
        ],
    ]);

    $service = app()->make(StudentAttendancePosting::class);
    $data = $service->setInterval()->getEarliestAndLatestRecord($attendance_inputs);

    expect($data)->toMatchArray([
        'earliest' => [
            'record_datetime' => '2024-10-30 15:20:03'
        ],
        'latest' => [
            'record_datetime' => '2025-02-16 12:00:00'
        ]
    ]);
});


test('getEarliestAndLatestRecord single input', function () {

    $attendance_inputs = collect([
        [
            'record_datetime' => '2025-02-01 23:59:12'
        ],
    ]);

    $service = app()->make(StudentAttendancePosting::class);
    $data = $service->getEarliestAndLatestRecord($attendance_inputs);

    expect($data)->toMatchArray([
        'earliest' => [
            'record_datetime' => '2025-02-01 23:59:12'
        ],
        'latest' => null
    ]);
});


test('determineAttendanceCheckInStatus', function () {

    $service = app()->make(StudentAttendancePosting::class);

    $data = $service->setDate('2024-02-20')->determineAttendanceCheckInStatus('08:00:00', '2024-02-19 23:59:59');
    expect($data)->toBe(AttendanceCheckInStatus::ON_TIME->value);

    $data = $service->setDate('2024-02-20')->determineAttendanceCheckInStatus('08:00:00', '2024-02-20 00:00:00');
    expect($data)->toBe(AttendanceCheckInStatus::ON_TIME->value);

    $data = $service->setDate('2024-02-20')->determineAttendanceCheckInStatus('08:00:00', '2024-02-20 00:00:01');
    expect($data)->toBe(AttendanceCheckInStatus::LATE->value);

});


test('determineAttendanceCheckOutStatus', function () {

    $service = app()->make(StudentAttendancePosting::class);

    $data = $service->setDate('2024-02-20')->determineAttendanceCheckOutStatus('12:00:00', '2024-02-19 23:59:59');
    expect($data)->toBe(AttendanceCheckOutStatus::LEFT_EARLY->value);

    $data = $service->setDate('2024-02-20')->determineAttendanceCheckOutStatus('12:00:00', '2024-02-20 03:59:59');
    expect($data)->toBe(AttendanceCheckOutStatus::LEFT_EARLY->value);

    $data = $service->setDate('2024-02-20')->determineAttendanceCheckOutStatus('12:00:00', '2024-02-20 04:00:00');
    expect($data)->toBe(AttendanceCheckOutStatus::ON_TIME->value);

    $data = $service->setDate('2024-02-20')->determineAttendanceCheckOutStatus('12:00:00', '2024-02-20 04:00:01');
    expect($data)->toBe(AttendanceCheckOutStatus::ON_TIME->value);

});

function initData($date) {
    Timeslot::truncate();

    $student = Student::factory()->create(['is_active' => true]);
    $card = Card::factory()->create([
        'userable_type' => Student::class,
        'userable_id' => $student->id,
    ]);

    $student2 = Student::factory()->create(['is_active' => true]);
    $card2 = Card::factory()->create([
        'userable_type' => Student::class,
        'userable_id' => $student2->id,
    ]);

    $student3 = Student::factory()->create(['is_active' => true]);
    $card3 = Card::factory()->create([
        'userable_type' => Student::class,
        'userable_id' => $student3->id,
    ]);

    $active_calendar_2025 = Calendar::factory()->create([
        'year' => '2025',
        'name->en' => 'calendar 2025',
        'name->zh' => '日历 2025',
        'is_default' => true,
        'is_active' => true,
    ]);
    CalendarSetting::factory()->create([
        'calendar_id' => $active_calendar_2025->id,
        'date' => $date,
        'is_attendance_required' => true,
        'description' => null,
    ]);
    $calendar_targets = [];
    foreach ([$student->id, $student2->id, $student3->id] as $student_id) {
        $calendar_targets[] = [
            'calendar_id' => $active_calendar_2025->id,
            'priority' => 10,
            'calendar_targetable_type' => Student::class,
            'calendar_targetable_id' => $student_id,
        ];
    }
    CalendarTarget::insert($calendar_targets);

    $semester_setting = SemesterSetting::factory()->create();
    $main_class_j111 = ClassModel::factory()->create([
        'name->en' => 'J111',
        'name->zh' => '初一11',
    ]);
    $semester_class1 = SemesterClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'class_id' => $main_class_j111->id,
    ]);
    $period_group1 = PeriodGroup::factory()->create();
    $main_j111_timetable = Timetable::factory()->create([
        'name' => 'J111 timetable',
        'is_active' => true,
        'period_group_id' => $period_group1->id,
        'semester_class_id' => $semester_class1,
    ]);

    $period1 = Period::factory()->create([
        'period_group_id' => $period_group1->id,
        'period' => 1,
        'from_time' => '08:00:00',
        'to_time' => '08:30:00',
        'day' => Day::MONDAY,
        'display_group' => 1,
    ]);
    $period_label1 = PeriodLabel::factory()->create([
        'period_group_id' => $period_group1->id,
        'period' => 1,
    ]);
    $period2 = Period::factory()->create([
        'period_group_id' => $period_group1->id,
        'period' => 2,
        'from_time' => '08:30:00',
        'to_time' => '09:00:00',
        'day' => Day::MONDAY,
        'display_group' => 1,
    ]);
    $period_label2 = PeriodLabel::factory()->create([
        'period_group_id' => $period_group1->id,
        'period' => 2,
    ]);
    $period3 = Period::factory()->create([
        'period_group_id' => $period_group1->id,
        'period' => 3,
        'from_time' => '09:00:00',
        'to_time' => '10:00:00',
        'day' => Day::MONDAY,
        'display_group' => 1,
    ]);
    $period_label3 = PeriodLabel::factory()->create([
        'period_group_id' => $period_group1->id,
        'period' => 3,
    ]);

    StudentClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'semester_class_id' => $semester_class1->id,
        'student_id' => $student->id,
        'class_type' => ClassType::PRIMARY
    ]);
    StudentClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'semester_class_id' => $semester_class1->id,
        'student_id' => $student2->id,
        'class_type' => ClassType::PRIMARY
    ]);
    StudentClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'semester_class_id' => $semester_class1->id,
        'student_id' => $student3->id,
        'class_type' => ClassType::PRIMARY
    ]);

    $timeslot1 = Timeslot::factory()->create([
        'timetable_id' => $main_j111_timetable->id,
        'period_id' => $period1->id,
        'placeholder' => null,
        'attendance_from' => '08:00',
        'attendance_to' => '08:30',
    ]);
    $timeslot2 = Timeslot::factory()->create([
        'timetable_id' => $main_j111_timetable->id,
        'period_id' => $period2->id,
        'placeholder' => null,
        'attendance_from' => '08:30',
        'attendance_to' => '09:00',
    ]);
    $timeslot3 = Timeslot::factory()->create([
        'timetable_id' => $main_j111_timetable->id,
        'period_id' => $period3->id,
        'placeholder' => null,
        'attendance_from' => '09:00',
        'attendance_to' => '10:00',
    ]);

    TimeslotTeacher::factory()->create([
        'timeslot_id' => $timeslot1->id,
    ]);
    TimeslotTeacher::factory()->create([
        'timeslot_id' => $timeslot2->id,
    ]);
    TimeslotTeacher::factory()->create([
        'timeslot_id' => $timeslot3->id,
    ]);

    // 3am cronjob will create absent period attendance
    foreach ([$student->id, $student2->id, $student3->id] as $student_id) {
        $period_attendances = [
            [
                'student_id' => $student_id,
                'date' => $date,
                'timeslot_type' => Timeslot::class,
                'timeslot_id' => $timeslot1->id,
                'updated_by_employee_id' => null,
                'status' => PeriodAttendanceStatus::ABSENT->value,
                'leave_application_id' => null,
                'period' => $timeslot1->period->period,
            ],
            [
                'student_id' => $student_id,
                'date' => $date,
                'timeslot_type' => Timeslot::class,
                'timeslot_id' => $timeslot2->id,
                'updated_by_employee_id' => null,
                'status' => PeriodAttendanceStatus::ABSENT->value,
                'leave_application_id' => null,
                'period' => $timeslot2->period->period,
            ],
            [
                'student_id' => $student_id,
                'date' => $date,
                'timeslot_type' => Timeslot::class,
                'timeslot_id' => $timeslot3->id,
                'updated_by_employee_id' => null,
                'status' => PeriodAttendanceStatus::ABSENT->value,
                'leave_application_id' => null,
                'period' => $timeslot3->period->period,
            ]
        ];
        PeriodAttendance::insert($period_attendances);
    }
    StudentTimetable::refreshViewTable(false);

    return [$student, $student2, $student3];
}

test('run - 1 student, with attendance, 1 attendance input, without individual override', function () {
    $date = '2025-02-10'; // Monday
    $date_sub_1_day = '2025-02-09';

    [$student, $student2, $student3] = initData($date);

    Attendance::factory()->create([
        'attendance_recordable_type' => Student::class,
        'attendance_recordable_id' => $student->id,
        'date' => $date,
        'check_in_datetime' => null,
        'check_in_status' => null,
        'check_in_remarks' => null,
        'check_out_datetime' => null,
        'check_out_status' => null,
        'check_out_remarks' => null,
        'status' => AttendanceStatus::ABSENT,
    ]);

    AttendanceInput::factory()->create([
        'attendance_recordable_type' => Student::class,
        'attendance_recordable_id' => $student->id,
        'card_id' => $student->firstActiveCard->id,
        'remarks' => null,
        'date' => $date, // Monday
        'record_datetime' => "{$date_sub_1_day} 23:00:00", // UTC
        'is_manual' => false,
        'updated_by_employee_id' => null,
    ]);

    $this->assertDatabaseCount($this->attendanceTable, 1);
    $this->assertDatabaseCount($this->attendanceInputTable, 1);
    $this->assertDatabaseCount($this->attendanceArchiveTable, 0);
    $this->assertDatabaseCount($this->periodAttendanceTable, 9);

    Carbon::setTestNow("{$date} 01:00:00");

    // 1 student, with attendance, 1 attendance input, without override
    $this->artisan("posting:student-attendance-input --date={$date} --student_ids={$student->id}")  // only run for student 1
    ->assertExitCode(1);

    expect(PeriodAttendance::where('status', PeriodAttendanceStatus::ABSENT)->whereIn('student_id', [$student2->id, $student3->id])->count())->toBe(6);
    // timeslot inherit_from_school_attendance = true + student 1 tapped card
    expect(PeriodAttendance::where('status', PeriodAttendanceStatus::PRESENT)->where('student_id', $student->id)->count())->toBe(3);
    $this->assertDatabaseCount($this->periodAttendanceTable, 9);

    foreach (Timeslot::all() as $timeslot) {
        // student 1 period attendance init as present
        $this->assertDatabaseHas($this->periodAttendanceTable, [
            'student_id' => $student->id,
            'date' => $date,
            'timeslot_type' => Timeslot::class,
            'timeslot_id' => $timeslot->id,
            'status' => PeriodAttendanceStatus::PRESENT->value,
            'updated_by_employee_id' => $timeslot->period->period == 1 ? $this->systemEmployee->id : null,
        ]);

        // student 2 and 3 remains absent
        $this->assertDatabaseHas($this->periodAttendanceTable, [
            'student_id' => $student2->id,
            'date' => $date,
            'timeslot_type' => Timeslot::class,
            'timeslot_id' => $timeslot->id,
            'status' => PeriodAttendanceStatus::ABSENT->value,
            'updated_by_employee_id' => null,
        ]);
        $this->assertDatabaseHas($this->periodAttendanceTable, [
            'student_id' => $student3->id,
            'date' => $date,
            'timeslot_type' => Timeslot::class,
            'timeslot_id' => $timeslot->id,
            'status' => PeriodAttendanceStatus::ABSENT->value,
            'updated_by_employee_id' => null,
        ]);
    }

    $this->assertDatabaseCount($this->attendanceTable, 1);
    $this->assertDatabaseHas($this->attendanceTable, [
        'attendance_recordable_type' => Student::class,
        'attendance_recordable_id' => $student->id,
        'date' => $date,
        'check_in_datetime' => "{$date_sub_1_day} 23:00:00",
        'check_in_status' => AttendanceCheckInStatus::ON_TIME->value,
        'check_in_remarks' => null,
        'check_in_overridable_type' => null,
        'check_in_overridable_id' => null,
        'attendance_from' => Carbon::parse($date . ' 08:00:00', config('school.timezone'))->tz('UTC'),
        'check_out_datetime' => null,
        'check_out_status' => null,
        'check_out_remarks' => null,
        'check_out_overridable_type' => null,
        'check_out_overridable_id' => null,
        'attendance_to' => Carbon::parse($date . ' 10:00:00', config('school.timezone'))->tz('UTC'),
        'status' => AttendanceStatus::PRESENT->value,
        'card_id' => $student->firstActiveCard->id,
        'leave_application_id' => null,
        'is_error' => false,
        'error_message' => null,
    ]);

    $this->assertDatabaseCount($this->attendanceInputTable, 1);
    $this->assertDatabaseCount($this->attendanceArchiveTable, 1);
    $this->assertDatabaseHas($this->attendanceArchiveTable, [
        'attendance_recordable_type' => Student::class,
        'attendance_recordable_id' => $student->id,
        'date' => $date,
        'check_in_datetime' => null,
        'check_in_status' => null,
        'check_in_remarks' => null,
        'check_in_overridable_type' => null,
        'check_in_overridable_id' => null,
        'attendance_from' => null,
        'check_out_datetime' => null,
        'check_out_status' => null,
        'check_out_remarks' => null,
        'check_out_overridable_type' => null,
        'check_out_overridable_id' => null,
        'attendance_to' => null,
        'status' => AttendanceStatus::ABSENT->value,
        'card_id' => null,
        'leave_application_id' => null,
        'is_error' => false,
        'error_message' => null,
        'version' => "{$date} 01:00:00",
    ]);
});

test('run - 1 student, test period attendance id', function () {
    $date = '2025-02-10'; // Monday
    $date_sub_1_day = '2025-02-09';

    [$student, $student2, $student3] = initData($date);

    Attendance::factory()->create([
        'attendance_recordable_type' => Student::class,
        'attendance_recordable_id' => $student->id,
        'date' => $date,
        'check_in_datetime' => null,
        'check_in_status' => null,
        'check_in_remarks' => null,
        'check_out_datetime' => null,
        'check_out_status' => null,
        'check_out_remarks' => null,
        'status' => AttendanceStatus::ABSENT,
    ]);

    AttendanceInput::factory()->create([
        'attendance_recordable_type' => Student::class,
        'attendance_recordable_id' => $student->id,
        'card_id' => $student->firstActiveCard->id,
        'remarks' => null,
        'date' => $date, // Monday
        'record_datetime' => "{$date_sub_1_day} 23:00:00", // UTC
        'is_manual' => false,
        'updated_by_employee_id' => null,
    ]);

    $period_attendances = PeriodAttendance::where('student_id', $student->id)->get();
    $period_attendance_period_1 = $period_attendances->where('period', 1)->first();
    $period_attendance_period_2 = $period_attendances->where('period', 2)->first();
    $period_attendance_period_3 = $period_attendances->where('period', 3)->first();
    $period_attendance_period_3->delete();
    
    $period_attendances = PeriodAttendance::where('student_id', $student->id)->get();
    $this->assertDatabaseCount($this->attendanceTable, 1);
    $this->assertDatabaseCount($this->attendanceInputTable, 1);
    $this->assertDatabaseCount($this->attendanceArchiveTable, 0);
    expect(count($period_attendances))->toBe(2);

    Carbon::setTestNow("{$date} 01:00:00");

    $this->artisan("posting:student-attendance-input --date={$date} --student_ids={$student->id}")  // only run for student 1
    ->assertExitCode(1);

    // timeslot inherit_from_school_attendance = true + student 1 tapped card
    expect(PeriodAttendance::where('status', PeriodAttendanceStatus::PRESENT)->where('student_id', $student->id)->count())->toBe(3);
    $this->assertDatabaseCount($this->periodAttendanceTable, 9);

    $this->assertDatabaseHas($this->periodAttendanceTable, [
        'id' => $period_attendance_period_1->id, // preserve id
        'student_id' => $student->id,
        'date' => $date,
        'timeslot_type' => $period_attendance_period_1->timeslot_type,
        'timeslot_id' => $period_attendance_period_1->timeslot_id,
        'status' => PeriodAttendanceStatus::PRESENT->value,
        'updated_by_employee_id' => $this->systemEmployee->id,
    ]);
    $this->assertDatabaseHas($this->periodAttendanceTable, [
        'id' => $period_attendance_period_2->id, // preserve id
        'student_id' => $student->id,
        'date' => $date,
        'timeslot_type' => $period_attendance_period_2->timeslot_type,
        'timeslot_id' => $period_attendance_period_2->timeslot_id,
        'status' => PeriodAttendanceStatus::PRESENT->value,
        'updated_by_employee_id' => null,
    ]);
    $this->assertDatabaseHas($this->periodAttendanceTable, [
        'student_id' => $student->id,
        'date' => $date,
        'timeslot_type' => $period_attendance_period_3->timeslot_type,
        'timeslot_id' => $period_attendance_period_3->timeslot_id,
        'status' => PeriodAttendanceStatus::PRESENT->value,
        'updated_by_employee_id' => null,
    ]);

    $this->assertDatabaseCount($this->attendanceTable, 1);
    $this->assertDatabaseHas($this->attendanceTable, [
        'attendance_recordable_type' => Student::class,
        'attendance_recordable_id' => $student->id,
        'date' => $date,
        'check_in_datetime' => "{$date_sub_1_day} 23:00:00",
        'check_in_status' => AttendanceCheckInStatus::ON_TIME->value,
        'check_in_remarks' => null,
        'check_in_overridable_type' => null,
        'check_in_overridable_id' => null,
        'attendance_from' => Carbon::parse($date . ' 08:00:00', config('school.timezone'))->tz('UTC'),
        'check_out_datetime' => null,
        'check_out_status' => null,
        'check_out_remarks' => null,
        'check_out_overridable_type' => null,
        'check_out_overridable_id' => null,
        'attendance_to' => Carbon::parse($date . ' 10:00:00', config('school.timezone'))->tz('UTC'),
        'status' => AttendanceStatus::PRESENT->value,
        'card_id' => $student->firstActiveCard->id,
        'leave_application_id' => null,
        'is_error' => false,
        'error_message' => null,
    ]);

    $this->assertDatabaseCount($this->attendanceInputTable, 1);
    $this->assertDatabaseCount($this->attendanceArchiveTable, 1);
    $this->assertDatabaseHas($this->attendanceArchiveTable, [
        'attendance_recordable_type' => Student::class,
        'attendance_recordable_id' => $student->id,
        'date' => $date,
        'check_in_datetime' => null,
        'check_in_status' => null,
        'check_in_remarks' => null,
        'check_in_overridable_type' => null,
        'check_in_overridable_id' => null,
        'attendance_from' => null,
        'check_out_datetime' => null,
        'check_out_status' => null,
        'check_out_remarks' => null,
        'check_out_overridable_type' => null,
        'check_out_overridable_id' => null,
        'attendance_to' => null,
        'status' => AttendanceStatus::ABSENT->value,
        'card_id' => null,
        'leave_application_id' => null,
        'is_error' => false,
        'error_message' => null,
        'version' => "{$date} 01:00:00",
    ]);
});

test('run - multiple students - different condition', function () {
    $date = '2025-02-10'; // Monday
    $date_sub_1_day = '2025-02-09';
    
    [$student, $student2, $student3] = initData($date);

    // student 1, with attendance, 2 attendance input, second one is manual created by employee, without individual override, with timeslot override inherit_from_school_attendance = true
    // student 2, without attendance, 1 attendance input, with individual override
    // student 3, without attendance, without attendance input, with leave application (is_present = true)
    AttendanceInput::factory()->create([
        'attendance_recordable_type' => Student::class,
        'attendance_recordable_id' => $student->id,
        'card_id' => $student->firstActiveCard->id,
        'remarks' => null,
        'date' => $date, // Monday
        'record_datetime' => "{$date_sub_1_day} 23:00:00", // UTC, Malaysia timezone = 2025-02-10 07:00:00
        'is_manual' => false,
        'updated_by_employee_id' => null
    ]);
    AttendanceInput::factory()->create([
        'attendance_recordable_type' => Student::class,
        'attendance_recordable_id' => $student->id,
        'card_id' => null,
        'remarks' => 'manually create check out attendance input',
        'date' => $date,
        'record_datetime' => "{$date} 01:00:00", // UTC, Malaysia timezone = 2025-02-10 09:00:00
        'is_manual' => true,
        'updated_by_employee_id' => Employee::factory()->create(),
    ]);

    AttendanceInput::factory()->create([
        'attendance_recordable_type' => Student::class,
        'attendance_recordable_id' => $student2->id,
        'card_id' => $student2->firstActiveCard->id,
        'remarks' => null,
        'date' => $date, // Monday
        'record_datetime' => "{$date} 00:59:59", // UTC, Malaysia timezone = 2025-02-10 08:59:59
        'is_manual' => false,
        'updated_by_employee_id' => null
    ]);
    $student2_individual_override = AttendancePeriodOverride::factory()->create([
        'attendance_recordable_type' => Student::class,
        'attendance_recordable_id' => $student2->id,
        'period' => $date,
        'attendance_from' => '09:00:00',
        'attendance_to' => '15:00:00',
    ]);

    $student3_approved_leave_application = LeaveApplication::factory()->create([
        'leave_applicable_type' => Student::class,
        'leave_applicable_id' => $student3->id,
        'status' => LeaveApplicationStatus::APPROVED->value,
        'reason' => 'attend competition',
        'remarks' => null,
        'is_present' => true,
        'is_full_day' => true,
    ]);
    LeaveApplicationPeriod::factory()->create(['leave_application_id' => $student3_approved_leave_application->id, 'date' => $date, 'period' => 1]);
    LeaveApplicationPeriod::factory()->create(['leave_application_id' => $student3_approved_leave_application->id, 'date' => $date, 'period' => 2]);
    LeaveApplicationPeriod::factory()->create(['leave_application_id' => $student3_approved_leave_application->id, 'date' => $date, 'period' => 3]);

    $this->assertDatabaseCount($this->attendanceTable, 0);
    $this->assertDatabaseCount($this->attendanceInputTable, 3);
    $this->assertDatabaseCount($this->attendanceArchiveTable, 0);
    expect(PeriodAttendance::where('status', PeriodAttendanceStatus::ABSENT)->whereIn('student_id', [$student->id, $student2->id, $student3->id])->count())->toBe(9);

    Carbon::setTestNow("{$date} 02:00:00");

    $this->artisan("posting:student-attendance-input --date={$date}") // run for all students
    ->assertExitCode(1);

    // student 1 and student 2 tapped card, student 3 applied full day + is present leave (22/4/2025 update: full day + is present no longer set attendance to present)
    expect(PeriodAttendance::where('status', PeriodAttendanceStatus::PRESENT)->whereIn('student_id', [$student->id, $student2->id, $student3->id])->count())->toBe(6);
    expect(PeriodAttendance::where('status', PeriodAttendanceStatus::ABSENT)->whereIn('student_id', [$student3->id])->count())->toBe(3);
    // user can only create timeslot override for future date
    // expect(PeriodAttendance::where('status', PeriodAttendanceStatus::PRESENT)->whereIn('student_id', [$student->id, $student2->id, $student3->id])->where('timeslot_type', Timeslot::class)->count())->toBe(5); // 5 timeslot 1 override
    // $this->assertDatabaseHas($this->periodAttendanceTable, [
    //     'student_id' => $timeslot_override->student_id,
    //     'date' => $date,
    //     'timeslot_type' => TimeslotOverride::class, // updated timeslot to timeslot_override
    //     'timeslot_id' => $timeslot_override->id,
    //     'updated_by_employee_id' => null,
    //     'status' => PeriodAttendanceStatus::PRESENT->value,
    //     'leave_application_id' => null,
    //     'period' => $timeslot_override->period,
    // ]);

    $this->assertDatabaseCount($this->attendanceTable, 3);
    $this->assertDatabaseHas($this->attendanceTable, [
        'attendance_recordable_type' => Student::class,
        'attendance_recordable_id' => $student->id,
        'date' => $date,
        'check_in_datetime' => "{$date_sub_1_day} 23:00:00",
        'check_in_status' => AttendanceCheckInStatus::ON_TIME->value,
        'check_in_remarks' => null,
        'check_in_overridable_type' => null,
        'check_in_overridable_id' => null,
        'attendance_from' => Carbon::parse($date . ' 08:00:00', config('school.timezone'))->tz('UTC'),
        'check_out_datetime' => "{$date} 01:00:00",
        'check_out_status' => AttendanceCheckOutStatus::LEFT_EARLY->value,
        'check_out_remarks' => 'manually create check out attendance input',
        'check_out_overridable_type' => null,
        'check_out_overridable_id' => null,
        'attendance_to' => Carbon::parse($date . ' 10:00:00', config('school.timezone'))->tz('UTC'),
        'status' => AttendanceStatus::PRESENT->value,
        'card_id' => $student->firstActiveCard->id,
        'leave_application_id' => null,
        'is_error' => false,
        'error_message' => null,
    ]);
    $this->assertDatabaseHas($this->attendanceTable, [
        'attendance_recordable_type' => Student::class,
        'attendance_recordable_id' => $student2->id,
        'date' => $date,
        'check_in_datetime' => "{$date} 00:59:59",
        'check_in_status' => AttendanceCheckInStatus::ON_TIME->value,
        'check_in_remarks' => null,
        'check_in_overridable_type' => get_class($student2_individual_override),
        'check_in_overridable_id' => $student2_individual_override->id,
        'attendance_from' => Carbon::parse($date . ' 09:00:00', config('school.timezone'))->tz('UTC'),  // student2_individual_override's attendance_from
        'check_out_datetime' => null,
        'check_out_status' => null,
        'check_out_remarks' => null,
        'check_out_overridable_type' => get_class($student2_individual_override),
        'check_out_overridable_id' => $student2_individual_override->id,
        'attendance_to' => Carbon::parse($date . ' 15:00:00', config('school.timezone'))->tz('UTC'), // student2_individual_override's attendance_to
        'status' => AttendanceStatus::PRESENT->value,
        'card_id' => $student2->firstActiveCard->id,
        'leave_application_id' => null,
        'is_error' => false,
        'error_message' => null,
    ]);
    $this->assertDatabaseHas($this->attendanceTable, [
        'attendance_recordable_type' => Student::class,
        'attendance_recordable_id' => $student3->id,
        'date' => $date,
        'check_in_datetime' => null,
        'check_in_status' => null,
        'check_in_remarks' => null,
        'check_in_overridable_type' => null,
        'check_in_overridable_id' => null,
        'attendance_from' => null,
        'check_out_datetime' => null,
        'check_out_status' => null,
        'check_out_remarks' => null,
        'check_out_overridable_type' => null,
        'check_out_overridable_id' => null,
        'attendance_to' => null,
        'status' => AttendanceStatus::ABSENT->value,
        'card_id' => null,
        'leave_application_id' => null, // TODO: drop leave_application_id
        'is_error' => false,
        'error_message' => null,
    ]);
    $this->assertDatabaseCount($this->attendanceInputTable, 3);
    $this->assertDatabaseCount($this->attendanceArchiveTable, 3);
    $this->assertDatabaseHas($this->attendanceArchiveTable, [
        'attendance_recordable_type' => Student::class,
        'attendance_recordable_id' => $student->id,
        'date' => $date,
        'check_in_datetime' => null,
        'check_in_status' => null,
        'check_in_remarks' => null,
        'check_in_overridable_type' => null,
        'check_in_overridable_id' => null,
        'attendance_from' => null,
        'check_out_datetime' => null,
        'check_out_status' => null,
        'check_out_remarks' => null,
        'check_out_overridable_type' => null,
        'check_out_overridable_id' => null,
        'attendance_to' => null,
        'status' => AttendanceStatus::ABSENT->value,
        'card_id' => null,
        'leave_application_id' => null,
        'is_error' => false,
        'error_message' => null,
        'version' => "{$date} 02:00:00",
    ]);
    $this->assertDatabaseHas($this->attendanceArchiveTable, [
        'attendance_recordable_type' => Student::class,
        'attendance_recordable_id' => $student2->id,
        'date' => $date,
        'check_in_datetime' => null,
        'check_in_status' => null,
        'check_in_remarks' => null,
        'check_in_overridable_type' => null,
        'check_in_overridable_id' => null,
        'attendance_from' => null,
        'check_out_datetime' => null,
        'check_out_status' => null,
        'check_out_remarks' => null,
        'check_out_overridable_type' => null,
        'check_out_overridable_id' => null,
        'attendance_to' => null,
        'status' => AttendanceStatus::ABSENT->value,
        'card_id' => null,
        'leave_application_id' => null,
        'is_error' => false,
        'error_message' => null,
        'version' => "{$date} 02:00:00",
    ]);
    $this->assertDatabaseHas($this->attendanceArchiveTable, [
        'attendance_recordable_type' => Student::class,
        'attendance_recordable_id' => $student3->id,
        'date' => $date,
        'check_in_datetime' => null,
        'check_in_status' => null,
        'check_in_remarks' => null,
        'check_in_overridable_type' => null,
        'check_in_overridable_id' => null,
        'attendance_from' => null,
        'check_out_datetime' => null,
        'check_out_status' => null,
        'check_out_remarks' => null,
        'check_out_overridable_type' => null,
        'check_out_overridable_id' => null,
        'attendance_to' => null,
        'status' => AttendanceStatus::ABSENT->value,
        'card_id' => null,
        'leave_application_id' => null,
        'is_error' => false,
        'error_message' => null,
        'version' => "{$date} 02:00:00",
    ]);

});

test('run - 1 student without timetable', function () {
    $date = '2025-02-10'; // Monday
    $student = Student::factory()->create(['is_active' => true]);
    $card = Card::factory()->create([
        'userable_type' => Student::class,
        'userable_id' => $student->id,
    ]);
    $active_calendar_2025 = Calendar::factory()->create([
        'year' => '2025',
        'name->en' => 'calendar 2025',
        'name->zh' => '日历 2025',
        'is_default' => true,
        'is_active' => true,
    ]);
    CalendarSetting::factory()->create([
        'calendar_id' => $active_calendar_2025->id,
        'date' => $date,
        'is_attendance_required' => true,
        'description' => null,
    ]);
    CalendarTarget::create([
        'calendar_id' => $active_calendar_2025->id,
        'priority' => 10,
        'calendar_targetable_type' => Student::class,
        'calendar_targetable_id' => $student->id,
    ]);

    AttendanceInput::factory()->create([
        'attendance_recordable_type' => Student::class,
        'attendance_recordable_id' => $student->id,
        'card_id' => $card->id,
        'remarks' => null,
        'date' => $date,
        'record_datetime' => "{$date} 00:59:59", // UTC, Malaysia timezone = 2025-02-10 08:59:59
        'is_manual' => false,
        'updated_by_employee_id' => null
    ]);

    $this->assertDatabaseCount($this->attendanceTable, 0);
    $this->assertDatabaseCount($this->attendanceInputTable, 1);

    Carbon::setTestNow("{$date} 03:00:00");
    // without student_ids
    $this->artisan("posting:student-attendance-input --date={$date} --student_ids={$student->id}")
        ->assertExitCode(1);

    $this->assertDatabaseCount($this->attendanceTable, 1);
    // error, but will update status and card_id if attendance input was found
    $this->assertDatabaseHas($this->attendanceTable, [
        'attendance_recordable_type' => Student::class,
        'attendance_recordable_id' => $student->id,
        'date' => $date,
        'check_in_datetime' => null,
        'check_in_status' => null,
        'check_in_remarks' => null,
        'check_in_overridable_type' => null,
        'check_in_overridable_id' => null,
        'attendance_from' => null,
        'check_out_datetime' => null,
        'check_out_status' => null,
        'check_out_remarks' => null,
        'check_out_overridable_type' => null,
        'check_out_overridable_id' => null,
        'attendance_to' => null,
        'status' => AttendanceStatus::PRESENT->value,
        'card_id' => $card->id,
        'leave_application_id' => null,
        'is_error' => true,
        'error_message' => 'Student timetable not found.',
    ]);
    $this->assertDatabaseCount($this->attendanceInputTable, 1);
    $this->assertDatabaseCount($this->attendanceArchiveTable, 1);
    $this->assertDatabaseHas($this->attendanceArchiveTable, [
        'attendance_recordable_type' => Student::class,
        'attendance_recordable_id' => $student->id,
        'date' => $date,
        'check_in_datetime' => null,
        'check_in_status' => null,
        'check_in_remarks' => null,
        'check_in_overridable_type' => null,
        'check_in_overridable_id' => null,
        'attendance_from' => null,
        'check_out_datetime' => null,
        'check_out_status' => null,
        'check_out_remarks' => null,
        'check_out_overridable_type' => null,
        'check_out_overridable_id' => null,
        'attendance_to' => null,
        'status' => AttendanceStatus::ABSENT->value,
        'card_id' => null,
        'leave_application_id' => null,
        'is_error' => false,
        'error_message' => null,
        'version' => "{$date} 03:00:00",
    ]);
});

test('run - more than 200 students', function () {
    Student::truncate();
    $semester_setting = SemesterSetting::factory()->create();

    $semester_class1 = SemesterClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
    ]);

    $period_group1 = PeriodGroup::factory()->create(['number_of_periods' => 15]);
    $j111_timetable = Timetable::factory()->create([
        'name' => 'J111 timetable',
        'is_active' => true,
        'period_group_id' => $period_group1->id,
        'semester_class_id' => $semester_class1->id,
    ]);
    $timeslots = [];
    for ($i = 1; $i <= 15; $i++) {
        $period = Period::factory()->create([
            'period_group_id' => $period_group1->id,
            'period' => $i,
            'day' => Day::WEDNESDAY,
            'display_group' => 1,
        ]);
        PeriodLabel::factory()->create([
            'period_group_id' => $period_group1->id,
            'period' => $i,
            'is_attendance_required' => true,
        ]);
        $j111_timetable_timeslot = Timeslot::factory()->create([
            'timetable_id' => $j111_timetable->id,
            'period_id' => $period->id,
            'day' => Day::WEDNESDAY,
        ]);
        $timeslots[$i] = $j111_timetable_timeslot;
        TimeslotTeacher::factory()->create([
            'timeslot_id' => $j111_timetable_timeslot->id,
        ]);
    }

    $time_in = '2025-02-19 01:00:00';       // in UTC
    $today = '2025-02-19';
    $tomorrow = '2025-02-20';

    $grade = Grade::factory()->create();
    $country = Country::factory()->create();
    $race = Race::factory()->create();
    $religion = Religion::factory()->create();
    $state = State::factory()->create();
    $health_concern = HealthConcern::factory()->create();
    // 1000 active students
    $students = Student::factory(300)->make(
        [
            'admission_grade_id' => $grade->id,
            'birthplace' => $country->name,
            'nationality_id' => $country->id,
            'race_id' => $race->id,
            'religion_id' => $religion->id,
            'state_id' => $state->id,
            'country_id' => $country->id,
            'is_active' => true,
            'custom_field' => null,
            'health_concern_id' => $health_concern->id,
        ]
    )->toArray();
    foreach ($students as &$student) {
        $student['name'] = json_encode($student['name']);
    }
    foreach (array_chunk($students, 500) as $chunk_students) {
        Student::insert($chunk_students);
    }

    $active_calendar_2025 = Calendar::factory()->create([
        'year' => '2025',
        'name->en' => 'calendar 2025',
        'name->zh' => '日历 2025',
        'is_default' => true,
        'is_active' => true,
    ]);

    CalendarSetting::factory()->create([
        'calendar_id' => $active_calendar_2025->id,
        'date' => '2025-02-19',
        'is_attendance_required' => true,
        'description' => null,
    ]);

    $student_ids = Student::pluck('id')->toArray();
    $students_j111 = [];
    $calendar_targets = [];
    $attendance_inputs = [];
    $period_attendances = [];
    foreach ($student_ids as $id) {
        $students_j111[] = [
            'semester_setting_id' => $semester_class1->semester_setting_id,
            'semester_class_id' => $semester_class1->id,
            'student_id' => $id,
            'class_type' => ClassType::PRIMARY,
            'class_enter_date' => $today,
            'is_active' => true,
        ];
        $calendar_targets[] = [
            'calendar_id' => $active_calendar_2025->id,
            'priority' => 10,
            'calendar_targetable_type' => Student::class,
            'calendar_targetable_id' => $id,
        ];
        $attendance_inputs[] = [
            'attendance_recordable_type' => Student::class,
            'attendance_recordable_id' => $id,
            'card_id' => Card::factory()->create()->id,
            'remarks' => null,
            'date' => '2025-02-19',
            'record_datetime' => "2025-02-18 23:00:00", // UTC
            'is_manual' => false,
            'updated_by_employee_id' => null,
        ];
        $period_attendances[] = [
            'student_id' => $id,
            'date' => '2025-02-19',
            'timeslot_type' => Timeslot::class,
            'timeslot_id' => $timeslots[1]->id,
            'updated_by_employee_id' => null,
            'status' => PeriodAttendanceStatus::ABSENT->value,
            'leave_application_id' => null,
            'period' => 1,
        ];
        $period_attendances[] = [
            'student_id' => $id,
            'date' => '2025-02-19',
            'timeslot_type' => Timeslot::class,
            'timeslot_id' => $timeslots[2]->id,
            'updated_by_employee_id' => null,
            'status' => PeriodAttendanceStatus::ABSENT->value,
            'leave_application_id' => null,
            'period' => 2,
        ];
    }
    StudentClass::insert($students_j111);
    CalendarTarget::insert($calendar_targets);
    AttendanceInput::insert($attendance_inputs);
    PeriodAttendance::insert($period_attendances); // test create + update period attendances in 1 posting

    StudentTimetable::refreshViewTable(false);
    config([
        'school.timezone' => 'Asia/Kuala_Lumpur'
    ]);
    Carbon::setTestNow('2025-02-19 00:00:00');

    // before posting
    expect(Attendance::count())->toBe(0);
    expect(AttendanceArchive::count())->toBe(0);
    expect(PeriodAttendance::count())->toBe(600); // 300 * 2
    expect(PeriodAttendance::where('status', PeriodAttendanceStatus::ABSENT->value)->count())->toBe(600); // 300 * 2
    expect(PeriodAttendance::where('status', PeriodAttendanceStatus::PRESENT->value)->count())->toBe(0);

    $this->artisan("posting:student-attendance-input") // run for all students
    ->assertExitCode(1);

    expect(Attendance::count())->toBe(300);
    expect(AttendanceArchive::count())->toBe(300);
    expect(PeriodAttendance::count())->toBe(4500); // 300 * 15
    expect(PeriodAttendance::where('status', PeriodAttendanceStatus::ABSENT->value)->count())->toBe(0);
    expect(PeriodAttendance::where('status', PeriodAttendanceStatus::PRESENT->value)->count())->toBe(4500);

    $this->artisan("posting:student-attendance-input") // run again
    ->assertExitCode(1);
    expect(Attendance::count())->toBe(300);
    expect(AttendanceArchive::count())->toBe(600);
    expect(PeriodAttendance::count())->toBe(4500); // 300 * 15
});

test('run - process-check-out', function () {

    $semester_setting = SemesterSetting::factory()->create();
    $semester_class = SemesterClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
    ]);

    $period_group1 = PeriodGroup::factory()->create(['number_of_periods' => 3]);
    $j111_timetable = Timetable::factory()->create([
        'name' => 'J111 timetable',
        'is_active' => true,
        'period_group_id' => $period_group1->id,
        'semester_class_id' => $semester_class->id,
    ]);

    $studentAttendancePosting = app()->make(StudentAttendancePosting::class);
    $secondClassAffairsPeriod = StudentAttendancePosting::SECOND_CLASS_AFFAIR_PERIOD;
    $leaveTypeCodeForCheckOutForgotTapCard = StudentAttendancePosting::LEAVE_TYPE_FORGOT_TAP_CARD_ON_CHECK_OUT;

    // create period, period label, timeslot, timeslot teacher
    $period = Period::factory()->create([
        'period_group_id' => $period_group1->id,
        'period' => $secondClassAffairsPeriod,
        'day' => Day::WEDNESDAY,
        'display_group' => 1,
    ]);
    $period_label = PeriodLabel::factory()->create([
        'period_group_id' => $period_group1->id,
        'period' => $secondClassAffairsPeriod,
        'is_attendance_required' => false,
        'can_apply_leave' => true,
    ]);
    $j111_timetable_timeslot = Timeslot::factory()->create([
        'timetable_id' => $j111_timetable->id,
        'period_id' => $period->id,
        'day' => Day::WEDNESDAY,
        'attendance_from' => '08:00:00',
        'attendance_to' => '09:00:00',
    ]);

    $period2 = Period::factory()->create([
        'period_group_id' => $period_group1->id,
        'period' => $secondClassAffairsPeriod + 1,
        'day' => Day::WEDNESDAY,
        'display_group' => 1,
    ]);
    PeriodLabel::factory()->create([
        'period_group_id' => $period_group1->id,
        'period' => $secondClassAffairsPeriod + 1,
        'is_attendance_required' => true,
        'can_apply_leave' => true,
    ]);
    $j111_timetable_timeslot2 = Timeslot::factory()->create([
        'timetable_id' => $j111_timetable->id,
        'period_id' => $period2->id,
        'day' => Day::WEDNESDAY,
        'attendance_from' => '09:00:00',
        'attendance_to' => '10:00:00',
    ]);
    TimeslotTeacher::factory()->create([
        'timeslot_id' => $j111_timetable_timeslot2->id,
    ]);

    $period3 = Period::factory()->create([
        'period_group_id' => $period_group1->id,
        'period' => $secondClassAffairsPeriod + 2,
        'day' => Day::WEDNESDAY,
        'display_group' => 1,
    ]);
    PeriodLabel::factory()->create([
        'period_group_id' => $period_group1->id,
        'period' => $secondClassAffairsPeriod + 2,
        'is_attendance_required' => true,
        'can_apply_leave' => true,
    ]);
    $j111_timetable_timeslot3 = Timeslot::factory()->create([
        'timetable_id' => $j111_timetable->id,
        'period_id' => $period3->id,
        'day' => Day::WEDNESDAY,
        'attendance_from' => '10:00:00',
        'attendance_to' => '11:00:00',
    ]);
    TimeslotTeacher::factory()->create([
        'timeslot_id' => $j111_timetable_timeslot3->id,
    ]);

    $date = '2025-02-19';
    $date_sub_1_day = '2025-02-18';

    // 1. is second attendance posting of the day
    // 2. check_out_status = null (only 1 attendance input)
    // 3. no approved leave in period 15 (second 班务)
    // 4. 早退/无签离 leave application type exists

    // matches all condition
    $student = Student::factory()->create();

    // has leave
    $student2 = Student::factory()->create();
    $existing_leave_application_student2 = LeaveApplication::factory()->create([
        'leave_applicable_type' => Student::class,
        'leave_applicable_id' => $student2->id,
        'status' => LeaveApplicationStatus::APPROVED,
    ]);
    $existing_leave_application_student2->leaveApplicationPeriods()->create([
        'date' => $date,
        'period_label_id' => $period_label->id,
        'period' => $secondClassAffairsPeriod,
    ]);

    // !!
    // (24/04 update: period 15 is_attendance_required will set to false, hence not able to create timeslot override for period 15)
    // !!
    // has timeslot override in period 15
    // $student3 = Student::factory()->create();
    // TimeslotOverride::factory()->create([
    //     'student_id' => $student3->id,
    //     'date' => $date,
    //     'period' => $secondClassAffairsPeriod,
    //     'attendance_from' => '07:00:00',
    //     'attendance_to' => '09:00:00',
    //     'inherit_from_school_attendance' => true,
    //     'class_attendance_required' => true,
    //     'is_empty' => false,
    // ]);

    // check_out_status not null
    $student4 = Student::factory()->create();
    AttendanceInput::factory()->create([
        'attendance_recordable_type' => Student::class,
        'attendance_recordable_id' => $student4->id,
        'remarks' => null,
        'date' => $date,
        'record_datetime' => "{$date} 03:05:00", // UTC, malaysia timezone 11:05
    ]);

    // has individual override (manually created)
    $student5 = Student::factory()->create();
    $student5_individual_override = AttendancePeriodOverride::factory()->create([
        'attendance_recordable_type' => Student::class,
        'attendance_recordable_id' => $student5->id,
        'period' => $date,
        'attendance_from' => '07:30:00',
        'attendance_to' => '11:30:00',
        'updated_by_employee_id' => Employee::factory(),
    ]);

    $active_calendar_2025 = Calendar::factory()->create([
        'year' => '2025',
        'name->en' => 'calendar 2025',
        'name->zh' => '日历 2025',
        'is_default' => true,
        'is_active' => true,
    ]);
    CalendarSetting::factory()->create([
        'calendar_id' => $active_calendar_2025->id,
        'date' => $date,
        'is_attendance_required' => true,
        'description' => null,
    ]);
    $students_j111 = [];
    $calendar_targets = [];
    $attendance_inputs = [];
    foreach ([$student->id, $student2->id, $student4->id, $student5->id] as $id) {
        $students_j111[] = [
            'semester_setting_id' => $semester_class->semester_setting_id,
            'semester_class_id' => $semester_class->id,
            'student_id' => $id,
            'class_type' => ClassType::PRIMARY,
            'class_enter_date' => $date,
            'is_active' => true,
        ];
        $calendar_targets[] = [
            'calendar_id' => $active_calendar_2025->id,
            'priority' => 10,
            'calendar_targetable_type' => Student::class,
            'calendar_targetable_id' => $id,
        ];
        // check in
        $attendance_inputs[] = [
            'attendance_recordable_type' => Student::class,
            'attendance_recordable_id' => $id,
            'remarks' => null,
            'date' => $date,
            'record_datetime' => "{$date} 00:05:00", // UTC, malaysia timezone 08:05
        ];
    }
    StudentClass::insert($students_j111);
    CalendarTarget::insert($calendar_targets);
    AttendanceInput::insert($attendance_inputs);

    StudentTimetable::refreshViewTable(false);
    config([
        'school.timezone' => 'Asia/Kuala_Lumpur'
    ]);
    Carbon::setTestNow('2025-02-19 00:00:00');

    $this->artisan("posting:student-attendance-input") // run for all students
    ->assertExitCode(1);

    expect(Attendance::count())->toBe(4);
    expect(PeriodAttendance::count())->toBe(8);

    $this->assertDatabaseHas($this->attendanceTable, [
        'attendance_recordable_type' => Student::class,
        'attendance_recordable_id' => $student->id,
        'date' => $date,
        'check_in_datetime' => "{$date} 00:05:00",
        'check_in_status' => AttendanceCheckInStatus::ON_TIME,
        'check_in_overridable_type' => null,
        'check_in_overridable_id' => null,
        'attendance_from' => "{$date} 01:00:00", // 9am
        'check_out_datetime' => null,
        'check_out_status' => null,
        'check_out_overridable_type' => null,
        'check_out_overridable_id' => null,
        'attendance_to' => "{$date} 03:00:00", // 11am
        'status' => AttendanceStatus::PRESENT->value,
    ]);
    $this->assertDatabaseHas($this->attendanceTable, [
        'attendance_recordable_type' => Student::class,
        'attendance_recordable_id' => $student2->id,
        'date' => $date,
        'check_in_datetime' => "{$date} 00:05:00",
        'check_in_status' => AttendanceCheckInStatus::ON_TIME,
        'check_in_overridable_type' => null,
        'check_in_overridable_id' => null,
        'attendance_from' => "{$date} 01:00:00", // 9am
        'check_out_datetime' => null,
        'check_out_status' => null,
        'check_out_overridable_type' => null,
        'check_out_overridable_id' => null,
        'attendance_to' => "{$date} 03:00:00", // 11am
        'status' => AttendanceStatus::PRESENT->value,
    ]);
    $this->assertDatabaseHas($this->attendanceTable, [
        'attendance_recordable_type' => Student::class,
        'attendance_recordable_id' => $student4->id,
        'date' => $date,
        'check_in_datetime' => "{$date} 00:05:00",
        'check_in_status' => AttendanceCheckInStatus::ON_TIME,
        'check_in_overridable_type' => null,
        'check_in_overridable_id' => null,
        'attendance_from' => "{$date} 01:00:00", // 9am
        'check_out_datetime' => "{$date} 03:05:00",
        'check_out_status' => AttendanceCheckOutStatus::ON_TIME,
        'check_out_overridable_type' => null,
        'check_out_overridable_id' => null,
        'attendance_to' => "{$date} 03:00:00", // 11am
        'status' => AttendanceStatus::PRESENT->value,
    ]);
    $this->assertDatabaseHas($this->attendanceTable, [
        'attendance_recordable_type' => Student::class,
        'attendance_recordable_id' => $student5->id,
        'date' => $date,
        'check_in_datetime' => "{$date} 00:05:00",
        'check_in_status' => AttendanceCheckInStatus::LATE,
        'check_in_overridable_type' => AttendancePeriodOverride::class,
        'check_in_overridable_id' => $student5_individual_override->id,
        'attendance_from' => "{$date_sub_1_day} 23:30:00", // 730am
        'check_out_datetime' => null,
        'check_out_status' => null,
        'check_out_overridable_type' => AttendancePeriodOverride::class,
        'check_out_overridable_id' => $student5_individual_override->id,
        'attendance_to' => "{$date} 03:30:00", // 1130am
        'status' => AttendanceStatus::PRESENT->value,
    ]);

    expect(LeaveApplication::count())->toBe(1); // student2 existing leave
    expect(AttendancePeriodOverride::count())->toBe(1); // student5 existing individual
    expect(AttendancePeriodOverrideLeaveApplication::count())->toBe(0);


    // run without leave application type code 12
    expect(LeaveApplicationType::where('code', $leaveTypeCodeForCheckOutForgotTapCard)->count())->toBe(0);
    $this->artisan("posting:student-attendance-input --process-check-out") // run for all students
    ->assertExitCode(1);
    expect(LeaveApplication::count())->toBe(1); // student2 existing leave
    expect(AttendancePeriodOverride::count())->toBe(1); // student5 existing individual
    expect(AttendancePeriodOverrideLeaveApplication::count())->toBe(0); // student5 existing individual (manually created)


    // run with leave application type code 12
    $leave_application_type_code_12 = LeaveApplicationType::factory()->create([
        'name->en' => '早退/无签离',
        'average_point_deduction' => 3,
        'conduct_point_deduction' => 4,
        'code' => $leaveTypeCodeForCheckOutForgotTapCard
    ]);
    expect(LeaveApplicationType::where('code', $leaveTypeCodeForCheckOutForgotTapCard)->count())->toBe(1);
    $this->artisan("posting:student-attendance-input --process-check-out") // run for all students
    ->assertExitCode(1);
    expect(LeaveApplication::count())->toBe(3);
    $this->assertDatabaseHas('leave_applications', [
        "id" => $existing_leave_application_student2->id,
        "leave_applicable_type" => Student::class,
        "leave_applicable_id" => $student2->id,
        "status" => $existing_leave_application_student2->status,
        "leave_application_type_id" => $existing_leave_application_student2->leave_application_type_id,
        "reason" => $existing_leave_application_student2->reason,
        "average_point_deduction" => "0.00",
        "conduct_point_deduction" => "0.00",
    ]);
    $this->assertDatabaseHas('leave_applications', [
        "leave_applicable_type" => Student::class,
        "leave_applicable_id" => $student->id,
        "status" => LeaveApplicationStatus::APPROVED,
        "leave_application_type_id" => $leave_application_type_code_12->id,
        "reason" => '早退/无签离',
        "average_point_deduction" => "3.00",
        "conduct_point_deduction" => "4.00",
    ]);
    $this->assertDatabaseHas('leave_applications', [
        "leave_applicable_type" => Student::class,
        "leave_applicable_id" => $student5->id,
        "status" => LeaveApplicationStatus::APPROVED, // with individual override created manually (24/4 update: created as approved and not affecting individual override)
        "leave_application_type_id" => $leave_application_type_code_12->id,
        "reason" => '早退/无签离',
        "average_point_deduction" => "3.00",
        "conduct_point_deduction" => "4.00",
    ]);

    expect(AttendancePeriodOverrideLeaveApplication::count())->toBe(0); // student1 (24/4 update: not creating individual override, because is_attendance_required = false)
    expect(Attendance::count())->toBe(4);
    expect(PeriodAttendance::where('status', PeriodAttendanceStatus::PRESENT)->count())->toBe(8);
    expect(PeriodAttendance::whereNotNull('leave_application_id')->count())->toBe(0);
    $this->assertDatabaseHas($this->attendanceTable, [
        'attendance_recordable_type' => Student::class,
        'attendance_recordable_id' => $student->id,
        'date' => $date,
        'check_in_datetime' => "{$date} 00:05:00",
        'check_in_status' => AttendanceCheckInStatus::ON_TIME,
        'check_in_overridable_type' => null,
        'check_in_overridable_id' => null,
        'attendance_from' => "{$date} 01:00:00", // 9am
        'check_out_datetime' => null,
        'check_out_status' => null,
        'check_out_overridable_type' => null,
        'check_out_overridable_id' => null,
        'attendance_to' => "{$date} 03:00:00", // 11am
        'status' => AttendanceStatus::PRESENT->value,
    ]);
    $this->assertDatabaseHas($this->attendanceTable, [
        'attendance_recordable_type' => Student::class,
        'attendance_recordable_id' => $student2->id,
        'date' => $date,
        'check_in_datetime' => "{$date} 00:05:00",
        'check_in_status' => AttendanceCheckInStatus::ON_TIME,
        'check_in_overridable_type' => null,
        'check_in_overridable_id' => null,
        'attendance_from' => "{$date} 01:00:00", // 9am
        'check_out_datetime' => null,
        'check_out_status' => null,
        'check_out_overridable_type' => null,
        'check_out_overridable_id' => null,
        'attendance_to' => "{$date} 03:00:00", // 11am
        'status' => AttendanceStatus::PRESENT->value,
    ]);
    $this->assertDatabaseHas($this->attendanceTable, [
        'attendance_recordable_type' => Student::class,
        'attendance_recordable_id' => $student4->id,
        'date' => $date,
        'check_in_datetime' => "{$date} 00:05:00",
        'check_in_status' => AttendanceCheckInStatus::ON_TIME,
        'check_in_overridable_type' => null,
        'check_in_overridable_id' => null,
        'attendance_from' => "{$date} 01:00:00", // 9am
        'check_out_datetime' => "{$date} 03:05:00",
        'check_out_status' => AttendanceCheckOutStatus::ON_TIME,
        'check_out_overridable_type' => null,
        'check_out_overridable_id' => null,
        'attendance_to' => "{$date} 03:00:00", // 11am
        'status' => AttendanceStatus::PRESENT->value,
    ]);
    $this->assertDatabaseHas($this->attendanceTable, [
        'attendance_recordable_type' => Student::class,
        'attendance_recordable_id' => $student5->id,
        'date' => $date,
        'check_in_datetime' => "{$date} 00:05:00",
        'check_in_status' => AttendanceCheckInStatus::LATE,
        'check_in_overridable_type' => AttendancePeriodOverride::class,
        'check_in_overridable_id' => $student5_individual_override->id,
        'attendance_from' => "{$date_sub_1_day} 23:30:00", // 730am
        'check_out_datetime' => null,
        'check_out_status' => null,
        'check_out_overridable_type' => AttendancePeriodOverride::class,
        'check_out_overridable_id' => $student5_individual_override->id,
        'attendance_to' => "{$date} 03:30:00", // 1130am
        'status' => AttendanceStatus::PRESENT->value,
    ]);

    $employee = Employee::factory()->create();
    $employee->user->givePermissionTo(['leave-application-delete', 'leave-application-update']);
    Sanctum::actingAs($employee->user);

    // change leave type from 早退/无签离 to 放学后无拍卡免扣
    expect(AttendancePeriodOverride::where('attendance_recordable_type', Student::class)->where('attendance_recordable_id', $student->id)->count())->toBe(0);
    $student_forgot_tap_card_leave = LeaveApplication::where('leave_applicable_type', Student::class)
        ->where('leave_applicable_id', $student->id)
        ->where('status', LeaveApplicationStatus::APPROVED)
        ->where('reason', '早退/无签离')
        ->first();

    // set leave to pending
    app()->make(LeaveApplicationService::class)
        ->setEmployee(Employee::factory()->create())
        ->updateStatus([$student_forgot_tap_card_leave->id], LeaveApplicationStatus::PENDING->value);
        
    $student_forgot_tap_card_leave->refresh();
    expect($student_forgot_tap_card_leave->status)->toBe(LeaveApplicationStatus::PENDING);

    // change type, average_point_deduction, conduct_point_deduction
    $payload = [
        'period_group_id' => $period_group1->id,
        'period_label_ids' => [$period_label->id],
        'leave_application_type_id' => LeaveApplicationType::factory()->create([
            'name->en' => '放学后无拍卡免扣',
            'average_point_deduction' => 0,
            'conduct_point_deduction' => 0,
        ])->id,
        'from_date' => $date,
        'to_date' => $date,
        'reason' => '放学后无拍卡免扣',
        'remarks' => null,
        'proof' => null,
        'is_present' => true,
        'average_point_deduction' => 0,
        'conduct_point_deduction' => 0,
    ];
    $response = $this->putJson(route("leave-applications.update", $student_forgot_tap_card_leave->id), $payload)->json();
    expect($response)->toHaveSuccessGeneralResponse();
    expect(AttendancePeriodOverride::where('attendance_recordable_type', Student::class)->where('attendance_recordable_id', $student->id)->count())->toBe(0);

    // set leave to approved
    app()->make(LeaveApplicationService::class)
        ->setEmployee(Employee::factory()->create())
        ->updateStatus([$student_forgot_tap_card_leave->id], LeaveApplicationStatus::APPROVED->value);

    expect(AttendancePeriodOverride::where('attendance_recordable_type', Student::class)->where('attendance_recordable_id', $student->id)->count())->toBe(1);
    $individual_override = AttendancePeriodOverride::where('attendance_recordable_type', Student::class)->where('attendance_recordable_id', $student->id)->first();
    $this->assertDatabaseHas('attendance_period_override', [
        'attendance_recordable_type' => Student::class,
        'attendance_recordable_id' => $student->id,
        'period' => $date,
        'attendance_from' => '09:00:00', // period 2 attendance from
        'attendance_to' => '11:00:00', // period 3 attendance to
    ]);
    // after confirmed will repost student attendance
    $this->assertDatabaseHas($this->attendanceTable, [
        'attendance_recordable_type' => Student::class,
        'attendance_recordable_id' => $student->id,
        'date' => $date,
        'check_in_datetime' => "{$date} 00:05:00",
        'check_in_status' => AttendanceCheckInStatus::ON_TIME,
        'check_in_overridable_type' => AttendancePeriodOverride::class,
        'check_in_overridable_id' => $individual_override->id,
        'attendance_from' => "{$date} 01:00:00", // 9am
        'check_out_datetime' => null,
        'check_out_status' => null,
        'check_out_overridable_type' => AttendancePeriodOverride::class,
        'check_out_overridable_id' => $individual_override->id,
        'attendance_to' => "{$date} 03:00:00", // 11am
        'status' => AttendanceStatus::PRESENT->value,
    ]);
    

    // try create another leave (period 2) for $student (without individual override) and approve it
    $leave_student = LeaveApplication::factory()->create([
        'leave_applicable_type' => Student::class,
        'leave_applicable_id' => $student->id,
        'status' => LeaveApplicationStatus::PENDING->value,
    ]);
    LeaveApplicationPeriod::factory()->create([
        'leave_application_id' => $leave_student->id,
        'date' => $date,
        'period' => $period2->period,
    ]);

    app()->make(LeaveApplicationService::class)
        ->setEmployee(Employee::factory()->create())
        ->updateStatus([$leave_student->id], LeaveApplicationStatus::APPROVED->value);

    expect(AttendancePeriodOverrideLeaveApplication::count())->toBe(2); // period 1 and 2
    expect(AttendancePeriodOverride::where('period', $date)->where('attendance_recordable_id', $student->id)->count())->toBe(1);
    $this->assertDatabaseHas('attendance_period_override', [
        'attendance_recordable_type' => Student::class,
        'attendance_recordable_id' => $student->id,
        'period' => $date,
        'attendance_from' => "10:00:00", // period 3 attendance from
        'attendance_to' => "11:00:00", // period 3 attendance to
    ]);
    $this->assertDatabaseHas($this->attendanceTable, [
        'attendance_recordable_type' => Student::class,
        'attendance_recordable_id' => $student->id,
        'date' => $date,
        'check_in_datetime' => "{$date} 00:05:00",
        'check_in_status' => AttendanceCheckInStatus::ON_TIME,
        'check_in_overridable_type' => AttendancePeriodOverride::class,
        'check_in_overridable_id' => AttendancePeriodOverrideLeaveApplication::first()->attendance_period_override_id,
        'attendance_from' => "{$date} 02:00:00", // 10am (period 3 attendance from)
        'check_out_datetime' => null,
        'check_out_status' => null,
        'check_out_overridable_type' => AttendancePeriodOverride::class,
        'check_out_overridable_id' => AttendancePeriodOverrideLeaveApplication::first()->attendance_period_override_id,
        'attendance_to' => "{$date} 03:00:00", // 11am
        'status' => AttendanceStatus::PRESENT->value,
    ]);
    
    $response = $this->deleteJson(route("leave-applications.destroy", $leave_student->id))->json();
    expect($response)->toHaveSuccessGeneralResponse();
    expect(AttendancePeriodOverride::where('period', $date)->where('attendance_recordable_id', $student->id)->count())->toBe(1); // left period 1
    expect(AttendancePeriodOverrideLeaveApplication::count())->toBe(1); // left period 1
    $individual_override = AttendancePeriodOverride::where('attendance_recordable_type', Student::class)->where('attendance_recordable_id', $student->id)->first(); // will delete and create new one
    $this->assertDatabaseHas($this->attendanceTable, [
        'attendance_recordable_type' => Student::class,
        'attendance_recordable_id' => $student->id,
        'date' => $date,
        'check_in_datetime' => "{$date} 00:05:00",
        'check_in_status' => AttendanceCheckInStatus::ON_TIME,
        'check_in_overridable_type' => AttendancePeriodOverride::class,
        'check_in_overridable_id' => $individual_override->id,
        'attendance_from' => "{$date} 01:00:00", // 9am
        'check_out_datetime' => null,
        'check_out_status' => null,
        'check_out_overridable_type' => AttendancePeriodOverride::class,
        'check_out_overridable_id' => $individual_override->id,
        'attendance_to' => "{$date} 03:00:00", // 11am
        'status' => AttendanceStatus::PRESENT->value,
    ]);
});

test('run - check in late', function () {
    $semester_setting = SemesterSetting::factory()->create();
    $semester_class = SemesterClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
    ]);

    $period_group1 = PeriodGroup::factory()->create(['number_of_periods' => 2]);
    $j111_timetable = Timetable::factory()->create([
        'name' => 'J111 timetable',
        'is_active' => true,
        'period_group_id' => $period_group1->id,
        'semester_class_id' => $semester_class->id,
    ]);

    $firstClassAffairsPeriod = StudentAttendancePosting::FIRST_CLASS_AFFAIR_PERIOD;

    // create period, period label, timeslot, timeslot teacher
    $period = Period::factory()->create([
        'period_group_id' => $period_group1->id,
        'period' => $firstClassAffairsPeriod,
        'day' => Day::WEDNESDAY,
        'display_group' => 1,
    ]);
    PeriodLabel::factory()->create([
        'period_group_id' => $period_group1->id,
        'period' => $firstClassAffairsPeriod,
        'is_attendance_required' => true,
    ]);
    $j111_timetable_timeslot = Timeslot::factory()->create([
        'timetable_id' => $j111_timetable->id,
        'period_id' => $period->id,
        'day' => Day::WEDNESDAY,
        'attendance_from' => '08:00:00',
        'attendance_to' => '09:00:00',
    ]);
    TimeslotTeacher::factory()->create([
        'timeslot_id' => $j111_timetable_timeslot->id,
    ]);

    $period2 = Period::factory()->create([
        'period_group_id' => $period_group1->id,
        'period' => $firstClassAffairsPeriod + 1,
        'day' => Day::WEDNESDAY,
        'display_group' => 1,
    ]);
    PeriodLabel::factory()->create([
        'period_group_id' => $period_group1->id,
        'period' => $firstClassAffairsPeriod + 1,
        'is_attendance_required' => true,
    ]);
    $j111_timetable_timeslot2 = Timeslot::factory()->create([
        'timetable_id' => $j111_timetable->id,
        'period_id' => $period2->id,
        'day' => Day::WEDNESDAY,
        'attendance_from' => '09:00:00',
        'attendance_to' => '10:00:00',
    ]);
    TimeslotTeacher::factory()->create([
        'timeslot_id' => $j111_timetable_timeslot2->id,
    ]);


    $date = '2025-02-19';

    $student_period_attendance_period1_employee = Employee::factory()->create();
    $student = Student::factory()->create();
    $student_period_attendance_period1 = PeriodAttendance::factory()->create([
        'student_id' => $student->id,
        'date' => $date,
        'timeslot_type' => Timeslot::class,
        'timeslot_id' => $j111_timetable_timeslot->id,
        'updated_by_employee_id' => $student_period_attendance_period1_employee->id,
        'status' => PeriodAttendanceStatus::PRESENT->value,
        'leave_application_id' => null,
        'period' => 1,
    ]);
    $student_period_attendance_period2 = PeriodAttendance::factory()->create([
        'student_id' => $student->id,
        'date' => $date,
        'timeslot_type' => Timeslot::class,
        'timeslot_id' => $j111_timetable_timeslot2->id,
        'updated_by_employee_id' => null,
        'status' => PeriodAttendanceStatus::ABSENT->value,
        'leave_application_id' => null,
        'period' => 2,
    ]);
    $student2 = Student::factory()->create();
    $student2_period_attendance_period1 = PeriodAttendance::factory()->create([
        'student_id' => $student2->id,
        'date' => $date,
        'timeslot_type' => Timeslot::class,
        'timeslot_id' => $j111_timetable_timeslot->id,
        'updated_by_employee_id' => null,
        'status' => PeriodAttendanceStatus::ABSENT->value,
        'leave_application_id' => null,
        'period' => 1,
    ]);
    $student2_period_attendance_period2 = PeriodAttendance::factory()->create([
        'student_id' => $student2->id,
        'date' => $date,
        'timeslot_type' => Timeslot::class,
        'timeslot_id' => $j111_timetable_timeslot2->id,
        'updated_by_employee_id' => null,
        'status' => PeriodAttendanceStatus::ABSENT->value,
        'leave_application_id' => null,
        'period' => 2,
    ]);
    $student3 = Student::factory()->create();
    $student3_period_attendance_period1 = PeriodAttendance::factory()->create([
        'student_id' => $student3->id,
        'date' => $date,
        'timeslot_type' => Timeslot::class,
        'timeslot_id' => $j111_timetable_timeslot->id,
        'updated_by_employee_id' => null,
        'status' => PeriodAttendanceStatus::ABSENT->value,
        'leave_application_id' => null,
        'period' => 1,
    ]);
    $student3_period_attendance_period2 = PeriodAttendance::factory()->create([
        'student_id' => $student3->id,
        'date' => $date,
        'timeslot_type' => Timeslot::class,
        'timeslot_id' => $j111_timetable_timeslot2->id,
        'updated_by_employee_id' => null,
        'status' => PeriodAttendanceStatus::ABSENT->value,
        'leave_application_id' => null,
        'period' => 2,
    ]);

    $active_calendar_2025 = Calendar::factory()->create([
        'year' => '2025',
        'name->en' => 'calendar 2025',
        'name->zh' => '日历 2025',
        'is_default' => true,
        'is_active' => true,
    ]);
    CalendarSetting::factory()->create([
        'calendar_id' => $active_calendar_2025->id,
        'date' => $date,
        'is_attendance_required' => true,
        'description' => null,
    ]);
    $students_j111 = [];
    $calendar_targets = [];
    foreach ([$student->id, $student2->id, $student3->id] as $id) {
        $students_j111[] = [
            'semester_setting_id' => $semester_class->semester_setting_id,
            'semester_class_id' => $semester_class->id,
            'student_id' => $id,
            'class_type' => ClassType::PRIMARY,
            'class_enter_date' => $date,
            'is_active' => true,
        ];
        $calendar_targets[] = [
            'calendar_id' => $active_calendar_2025->id,
            'priority' => 10,
            'calendar_targetable_type' => Student::class,
            'calendar_targetable_id' => $id,
        ];
    }
    StudentClass::insert($students_j111);
    CalendarTarget::insert($calendar_targets);

    StudentTimetable::refreshViewTable(false);
    config([
        'school.timezone' => 'Asia/Kuala_Lumpur'
    ]);
    Carbon::setTestNow("$date 00:00:00");

    AttendanceInput::factory()->create([
        'attendance_recordable_type' => Student::class,
        'attendance_recordable_id' => $student->id,
        'card_id' => null,
        'remarks' => null,
        'date' => $date,
        'record_datetime' => "$date 00:30:00",
        'is_manual' => false,
        'updated_by_employee_id' => null,
        'terminal_id' => null,
    ]);
    AttendanceInput::factory()->create([
        'attendance_recordable_type' => Student::class,
        'attendance_recordable_id' => $student2->id,
        'card_id' => null,
        'remarks' => null,
        'date' => $date,
        'record_datetime' => "$date 00:31:00",
        'is_manual' => false,
        'updated_by_employee_id' => null,
        'terminal_id' => null,
    ]);
    AttendanceInput::factory()->create([
        'attendance_recordable_type' => Student::class,
        'attendance_recordable_id' => $student3->id,
        'card_id' => null,
        'remarks' => null,
        'date' => $date,
        'record_datetime' => "$date 00:00:00",
        'is_manual' => false,
        'updated_by_employee_id' => null,
        'terminal_id' => null,
    ]);

    // student 1 (late)
    // period 1 = updated_by_employee_id not null, present
    // period 2 = updated_by_employee_id null, absent

    // student 2 (late)
    // period 1 = updated_by_employee_id null, absent
    // period 2 = updated_by_employee_id null, absent

    // student 3 (on time)
    // period 1 = updated_by_employee_id null, absent
    // period 2 = updated_by_employee_id null, absent
    $this->artisan("posting:student-attendance-input") // run for all students
        ->assertExitCode(1);

    $this->assertDatabaseHas($this->attendanceTable, [
        'attendance_recordable_type' => Student::class,
        'attendance_recordable_id' => $student->id,
        'date' => $date,
        'check_in_datetime' => "{$date} 00:30:00",
        'check_in_status' => AttendanceCheckInStatus::LATE->value,
        'status' => AttendanceStatus::PRESENT->value,
    ]);
    $this->assertDatabaseHas($this->periodAttendanceTable, [
        'id' => $student_period_attendance_period1->id,
        'student_id' => $student->id,
        'date' => $date,
        'timeslot_type' => Timeslot::class,
        'timeslot_id' => $j111_timetable_timeslot->id,
        'updated_by_employee_id' => $this->systemEmployee->id, // overwrite to system employee
        'status' => PeriodAttendanceStatus::LATE->value,
        'leave_application_id' => null,
        'period' => 1,
    ]);
    $this->assertDatabaseHas($this->periodAttendanceTable, [
        'id' => $student_period_attendance_period2->id,
        'student_id' => $student->id,
        'date' => $date,
        'timeslot_type' => Timeslot::class,
        'timeslot_id' => $j111_timetable_timeslot2->id,
        'updated_by_employee_id' => null,
        'status' => PeriodAttendanceStatus::PRESENT->value,
        'leave_application_id' => null,
        'period' => 2,
    ]);

    $this->assertDatabaseHas($this->attendanceTable, [
        'attendance_recordable_type' => Student::class,
        'attendance_recordable_id' => $student2->id,
        'date' => $date,
        'check_in_datetime' => "{$date} 00:31:00",
        'check_in_status' => AttendanceCheckInStatus::LATE->value,
        'status' => AttendanceStatus::PRESENT->value,
    ]);
    $this->assertDatabaseHas($this->periodAttendanceTable, [
        'id' => $student2_period_attendance_period1->id,
        'student_id' => $student2->id,
        'date' => $date,
        'timeslot_type' => Timeslot::class,
        'timeslot_id' => $j111_timetable_timeslot->id,
        'updated_by_employee_id' => $this->systemEmployee->id,
        'status' => PeriodAttendanceStatus::LATE->value,
        'leave_application_id' => null,
        'period' => 1,
    ]);
    $this->assertDatabaseHas($this->periodAttendanceTable, [
        'id' => $student2_period_attendance_period2->id,
        'student_id' => $student2->id,
        'date' => $date,
        'timeslot_type' => Timeslot::class,
        'timeslot_id' => $j111_timetable_timeslot2->id,
        'updated_by_employee_id' => null,
        'status' => PeriodAttendanceStatus::PRESENT->value,
        'leave_application_id' => null,
        'period' => 2,
    ]);

    $this->assertDatabaseHas($this->attendanceTable, [
        'attendance_recordable_type' => Student::class,
        'attendance_recordable_id' => $student3->id,
        'date' => $date,
        'check_in_datetime' => "{$date} 00:00:00",
        'check_in_status' => AttendanceCheckInStatus::ON_TIME->value,
        'status' => AttendanceStatus::PRESENT->value,
    ]);
    $this->assertDatabaseHas($this->periodAttendanceTable, [
        'id' => $student3_period_attendance_period1->id,
        'student_id' => $student3->id,
        'date' => $date,
        'timeslot_type' => Timeslot::class,
        'timeslot_id' => $j111_timetable_timeslot->id,
        'updated_by_employee_id' => $this->systemEmployee->id,
        'status' => PeriodAttendanceStatus::PRESENT->value,
        'leave_application_id' => null,
        'period' => 1,
    ]);
    $this->assertDatabaseHas($this->periodAttendanceTable, [
        'id' => $student3_period_attendance_period2->id,
        'student_id' => $student3->id,
        'date' => $date,
        'timeslot_type' => Timeslot::class,
        'timeslot_id' => $j111_timetable_timeslot2->id,
        'updated_by_employee_id' => null,
        'status' => PeriodAttendanceStatus::PRESENT->value,
        'leave_application_id' => null,
        'period' => 2,
    ]);

    // set attendance from to 08:30
    SchoolAttendancePeriodOverride::factory()->create([
        'from' => $date,
        'to' => $date,
        'priority' => 1,
        'attendance_from' => '08:30:00',
        'attendance_to' => null,
    ]);
    $this->artisan("posting:student-attendance-input") // run for all students
        ->assertExitCode(1);

    $this->assertDatabaseHas($this->attendanceTable, [
        'attendance_recordable_type' => Student::class,
        'attendance_recordable_id' => $student->id,
        'date' => $date,
        'check_in_datetime' => "{$date} 00:30:00",
        'check_in_status' => AttendanceCheckInStatus::ON_TIME->value,
        'status' => AttendanceStatus::PRESENT->value,
    ]);
    $this->assertDatabaseHas($this->periodAttendanceTable, [
        'id' => $student_period_attendance_period1->id,
        'student_id' => $student->id,
        'date' => $date,
        'timeslot_type' => Timeslot::class,
        'timeslot_id' => $j111_timetable_timeslot->id,
        'updated_by_employee_id' => $this->systemEmployee->id,
        'status' => PeriodAttendanceStatus::PRESENT->value, // become present
        'leave_application_id' => null,
        'period' => 1,
    ]);
    $this->assertDatabaseHas($this->periodAttendanceTable, [
        'id' => $student_period_attendance_period2->id,
        'student_id' => $student->id,
        'date' => $date,
        'timeslot_type' => Timeslot::class,
        'timeslot_id' => $j111_timetable_timeslot2->id,
        'updated_by_employee_id' => null,
        'status' => PeriodAttendanceStatus::PRESENT->value,
        'leave_application_id' => null,
        'period' => 2,
    ]);

    $this->assertDatabaseHas($this->attendanceTable, [
        'attendance_recordable_type' => Student::class,
        'attendance_recordable_id' => $student2->id,
        'date' => $date,
        'check_in_datetime' => "{$date} 00:31:00",
        'check_in_status' => AttendanceCheckInStatus::LATE->value,
        'status' => AttendanceStatus::PRESENT->value,
    ]);
    $this->assertDatabaseHas($this->periodAttendanceTable, [
        'id' => $student2_period_attendance_period1->id,
        'student_id' => $student2->id,
        'date' => $date,
        'timeslot_type' => Timeslot::class,
        'timeslot_id' => $j111_timetable_timeslot->id,
        'updated_by_employee_id' => $this->systemEmployee->id,
        'status' => PeriodAttendanceStatus::LATE->value, // remain late (08:31)
        'leave_application_id' => null,
        'period' => 1,
    ]);
    $this->assertDatabaseHas($this->periodAttendanceTable, [
        'id' => $student2_period_attendance_period2->id,
        'student_id' => $student2->id,
        'date' => $date,
        'timeslot_type' => Timeslot::class,
        'timeslot_id' => $j111_timetable_timeslot2->id,
        'updated_by_employee_id' => null,
        'status' => PeriodAttendanceStatus::PRESENT->value,
        'leave_application_id' => null,
        'period' => 2,
    ]);

    $this->assertDatabaseHas($this->attendanceTable, [
        'attendance_recordable_type' => Student::class,
        'attendance_recordable_id' => $student3->id,
        'date' => $date,
        'check_in_datetime' => "{$date} 00:00:00",
        'check_in_status' => AttendanceCheckInStatus::ON_TIME->value,
        'status' => AttendanceStatus::PRESENT->value,
    ]);
    $this->assertDatabaseHas($this->periodAttendanceTable, [
        'id' => $student3_period_attendance_period1->id,
        'student_id' => $student3->id,
        'date' => $date,
        'timeslot_type' => Timeslot::class,
        'timeslot_id' => $j111_timetable_timeslot->id,
        'updated_by_employee_id' => $this->systemEmployee->id,
        'status' => PeriodAttendanceStatus::PRESENT->value,
        'leave_application_id' => null,
        'period' => 1,
    ]);
    $this->assertDatabaseHas($this->periodAttendanceTable, [
        'id' => $student3_period_attendance_period2->id,
        'student_id' => $student3->id,
        'date' => $date,
        'timeslot_type' => Timeslot::class,
        'timeslot_id' => $j111_timetable_timeslot2->id,
        'updated_by_employee_id' => null,
        'status' => PeriodAttendanceStatus::PRESENT->value,
        'leave_application_id' => null,
        'period' => 2,
    ]);
});