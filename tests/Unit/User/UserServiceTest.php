<?php

use App\Enums\PushNotificationPlatform;
use App\Models\Employee;
use App\Models\Guardian;
use App\Models\Role;
use App\Models\Student;
use App\Models\User;
use App\Services\PushNotification\AndroidPushNotificationService;
use App\Services\PushNotification\HuaweiPushNotificationService;
use App\Services\PushNotification\IosPushNotificationService;
use App\Services\UserService;
use Database\Seeders\CurrencySeeder;
use Illuminate\Database\Eloquent\Factories\Sequence;
use Illuminate\Support\Facades\Hash;
use Mockery\MockInterface;

beforeEach(function () {
    $this->seed(CurrencySeeder::class);
    $this->userService = resolve(UserService::class);

    app()->setLocale('en');

    $this->testLocale = app()->getLocale();

    $this->guardianTable = resolve(Guardian::class)->getTable();
});

test('getAllPaginatedUsers()', function (int $expected_count, mixed $filter_by, mixed $filter_value, array $expected_model) {
    $users = User::factory()->count(3)->create(new Sequence(
        [
            'email' => '<EMAIL>',
            'phone_number' => '999999999999999',
            'is_active' => true,
        ],
        [
            'email' => '<EMAIL>',
            'phone_number' => '7777777777777777',
            'is_active' => true,
        ],
        [
            'email' => '<EMAIL>',
            'phone_number' => '88888888888888888',
            'is_active' => false,
        ]
    ));
    $users = [
        'first' => $users[0],
        'second' => $users[1],
        'third' => $users[2],
    ];

    Student::factory()->create([
        'student_number' => '111111111111',
        'name->en' => 'Jon',
        'user_id' => $users['first']->id,
    ]);

    Student::factory()->create([
        'student_number' => '3333333333333333',
        'name->en' => 'Koko',
        'user_id' => $users['second']->id,
    ]);

    Employee::factory()->create([
        'employee_number' => 'EMPLOYEE-11111',
        'name->en' => 'Mackle',
        'user_id' => $users['third']->id,
    ]);

    $role = Role::factory()->create([
        'id' => 1111111
    ]);

    $users['first']->syncRoles($role);

    $payload = [];

    if (isset($filter_by) && isset($filter_value)) {
        $payload = [
            $filter_by => $filter_value
        ];
    }

    $result = $this->userService->getAllPaginatedUsers(array_merge($payload, ['includes' => 'userables']))->toArray();
    $result['data'] = collect($result['data'])->sortBy('id');

    expect($result['data'])->toHaveCount($expected_count);

    foreach ($expected_model as $key => $value) {
        expect($result['data'][$key])->toMatchArray($users[$value]->toArray());
    }
})->with([
    'no filter' => [3, null, null, ['first', 'second', 'third']],
    'filter by email = <EMAIL>' => [1, 'email', '<EMAIL>', ['first']],
    'filter by phone_number = 88888888888888888' => [1, 'phone_number', '88888888888888888', ['third']],
    'filter by is_active = false' => [1, 'is_active', false, ['third']],
    'filter by role_id = 1111111' => [1, 'role_id', 1111111, ['first']],
    'filter by user_type = Student' => [2, 'user_type', Student::class, ['first', 'second']],
    'filter by user_number = 111111111111' => [1, 'user_number', '111111111111', ['first']],
    'filter by user_name = Mackle' => [1, 'user_name', ['en' => 'Mackle'], ['third']],
    'sort by id asc' => [3, 'order_by', ['id' => 'asc'], ['first', 'second', 'third']],
    'sort by id desc' => [3, 'order_by', ['id' => 'desc'], ['third', 'second', 'first']],
]);

test('firstUserByEmail()', function () {
    $test_user1 = User::factory()->create([
        'email' => '<EMAIL>'
    ]);

    $test_user2 = User::factory()->create([
        'email' => '<EMAIL>'
    ]);

    $result = $this->userService->firstUserByEmail(['email' => '<EMAIL>'])->toArray();

    expect($result)->toMatchArray($test_user1->toArray());

    $result = $this->userService->firstUserByEmail(['email' => '<EMAIL>'])->toArray();

    expect($result)->toMatchArray($test_user2->toArray());
});

test('firstUserByPhoneNumber()', function () {
    $test_user1 = User::factory()->create([
        'phone_number' => '12345'
    ]);

    $test_user2 = User::factory()->create([
        'phone_number' => '55555'
    ]);

    $result = $this->userService->firstUserByPhoneNumber(['phone_number' => '12345'])->toArray();

    expect($result)->toMatchArray($test_user1->toArray());

    $result = $this->userService->firstUserByPhoneNumber(['phone_number' => '55555'])->toArray();

    expect($result)->toMatchArray($test_user2->toArray());
});

test('changePassword()', function () {
    $user = User::factory()->create([
        'email' => '<EMAIL>',
        'password' => Hash::make('123456'),
        'is_password_reset_required' => true
    ]);

    $password = '654321';
    $this->userService->changePassword($user, $password);

    $login_with_new_password = auth('api')->once([
        'email' => $user->email,
        'password' => $password
    ]);

    expect($login_with_new_password)->toBeTrue();

    $login_with_old_password = auth('api')->once([
        'email' => $user->email,
        'password' => '123456'
    ]);

    //after reset, is_password_reset_required change to false
    $this->assertDatabaseHas('users', [
        'id' => $user->id,
        'is_password_reset_required' => false
    ]);

    expect($login_with_old_password)->toBeFalse();
});

test('updateUser()', function () {
    $user = User::factory()->create([
        'email' => '<EMAIL>',
        'phone_number' => '**************',
        'password' => Hash::make('123456'),
        'is_active' => false,
        'is_password_reset_required' => true
    ]);

    $role = Role::factory()->create();

    $password = 'new_password';

    $payload = [
        'email' => '<EMAIL>',
        'phone_number' => 'updated1111111111',
        'password' => $password,
        'is_active' => true, // reactivate account
        'role_ids' => [$role->id]
    ];

    $this->userService->updateUser($user, $payload);

    $this->assertDatabaseHas('users', [
        'id' => $user->id,
        'email' => $payload['email'],
        'phone_number' => $payload['phone_number'],
        'is_active' => $payload['is_active'],
        'is_password_reset_required' => false
    ]);

    $user->refresh();

    expect($user->roles)->toHaveCount(1)
        ->and($user->hasRole([$role]))->toBeTruthy();

    $login_with_new_password = auth('api')->once([
        'email' => $user->email,
        'password' => $password
    ]);

    expect($login_with_new_password)->toBeTrue();

    $login_with_old_password = auth('api')->once([
        'email' => $user->email,
        'password' => '123456'
    ]);

    expect($login_with_old_password)->toBeFalse();

    //if no password pass, the is_password_reset_required will stay true
    $user2 = User::factory()->create([
        'email' => '<EMAIL>',
        'phone_number' => '**************',
        'password' => Hash::make('123456'),
        'is_active' => false,
        'is_password_reset_required' => true
    ]);

    $payload2 = [
        'email' => '<EMAIL>',
        'phone_number' => 'updated21111111111',
        'is_active' => true, // reactivate account
    ];

    $this->userService->updateUser($user2, $payload2);

    $this->assertDatabaseHas('users', [
        'id' => $user2->id,
        'is_password_reset_required' => true
    ]);
});

test('createUser() with default role', function () {

    $guardian_role = Role::create(['name' => 'Guardian']);
    $guardian_role2 = Role::create(['name' => 'Guardian 2']);

    $guardian_role->models()->create([
        'model' => Guardian::class,
    ]);

    $guardian_role2->models()->create([
        'model' => Guardian::class,
    ]);


    $this->assertDatabaseHas('model_has_default_role', [
        'model' => Guardian::class,
        'role_id' => $guardian_role->id,
    ]);

    $payload = [
        'email' => '<EMAIL>',
    ];

    $response = $this->userService->createUser($payload, Guardian::class)->toArray();

    expect($response)->toMatchArray([
        'email' => $payload['email']
    ]);

    $this->assertDatabaseCount('users', 1);

    $this->assertDatabaseHas('users', [
        'email' => '<EMAIL>',
        'is_password_reset_required' => true,
    ]);

    $this->assertDatabaseHas('model_has_roles', [
        'role_id' => $guardian_role->id,
        'model_type' => User::class,
        'model_id' => $response['id'],
    ]);

    $this->assertDatabaseHas('model_has_roles', [
        'role_id' => $guardian_role2->id,
        'model_type' => User::class,
        'model_id' => $response['id'],
    ]);
});

test('createUser() without email and phone number', function () {

    $guardian_role = Role::create(['name' => 'Guardian']);
    $guardian_role2 = Role::create(['name' => 'Guardian 2']);

    $guardian_role->models()->create([
        'model' => Guardian::class,
    ]);

    $guardian_role2->models()->create([
        'model' => Guardian::class,
    ]);

    $this->assertDatabaseHas('model_has_default_role', [
        'model' => Guardian::class,
        'role_id' => $guardian_role->id,
    ]);

    $payload = [];

    expect(function () use ($payload) {
        $this->userService->createUser($payload, Guardian::class);
    })->toThrow(function (Exception $e) {
        expect($e->getCode())->toBe(4010);
    }, 'Email or phone number is required to create user.');
});

test('updateUserPushNotificationSettings android', function () {

    $user = User::factory()->create([
        'push_notification_token' => null,
        'push_notification_platform' => null,
    ]);

    // This user token will be removed
    $user2 = User::factory()->create([
        'push_notification_token' => '123456abcdef',
        'push_notification_platform' => PushNotificationPlatform::PLATFORM_ANDROID->value,
    ]);

    $this->mock(AndroidPushNotificationService::class, function (MockInterface $mock) use (&$user) {
        $mock->shouldReceive('setUser')->withArgs(function ($e) use (&$user) {
            return $e->id === $user->id;
        })->once()->andReturnSelf();
        $mock->shouldReceive('registerTokenWithProvider')->once()->andReturn('123456abcdef');
        $mock->shouldReceive('deleteTokenAtProvider')->never();
    });

    $this->userService->updateUserPushNotificationSettings($user, PushNotificationPlatform::PLATFORM_ANDROID, '123456abcdef');

    $this->assertDatabaseHas('users', [
        'id' => $user->id,
        'push_notification_token' => '123456abcdef',
        'push_notification_platform' => PushNotificationPlatform::PLATFORM_ANDROID->value,
    ]);

    $this->assertDatabaseHas('users', [
        'id' => $user2->id,
        'push_notification_token' => null,
        'push_notification_platform' => null,
    ]);

});


test('updateUserPushNotificationSettings ios', function () {

    $user = User::factory()->create([
        'push_notification_token' => '111111',
        'push_notification_platform' => PushNotificationPlatform::PLATFORM_IOS->value,
    ]);

    $user2 = User::factory()->create([
        'push_notification_token' => 'aaabbb123',
        'push_notification_platform' => PushNotificationPlatform::PLATFORM_IOS->value,
    ]);

    $this->mock(IosPushNotificationService::class, function (MockInterface $mock) use (&$user) {
        $mock->shouldReceive('setUser')->withArgs(function ($e) use (&$user) {
            return $e->id === $user->id;
        })->once()->andReturnSelf();
        $mock->shouldReceive('registerTokenWithProvider')->once()->andReturn('aaabbb123');
        $mock->shouldReceive('deleteTokenAtProvider')->once()->andReturnTrue();     // test clear token if got existing token
    });

    $this->userService->updateUserPushNotificationSettings($user, PushNotificationPlatform::PLATFORM_IOS, 'aaabbb123');

    $this->assertDatabaseHas('users', [
        'id' => $user->id,
        'push_notification_token' => 'aaabbb123',
        'push_notification_platform' => PushNotificationPlatform::PLATFORM_IOS->value,
    ]);

    $this->assertDatabaseHas('users', [
        'id' => $user2->id,
        'push_notification_token' => null,
        'push_notification_platform' => null,
    ]);
});


test('updateUserPushNotificationSettings huawei', function () {

    $user = User::factory()->create([
        'push_notification_token' => null,
        'push_notification_platform' => null,
    ]);

    // This user token will be removed
    $user2 = User::factory()->create([
        'push_notification_token' => '5555666',
        'push_notification_platform' => PushNotificationPlatform::PLATFORM_HUAWEI->value,
    ]);

    $this->mock(HuaweiPushNotificationService::class, function (MockInterface $mock) use (&$user) {
        $mock->shouldReceive('setUser')->withArgs(function ($e) use (&$user) {
            return $e->id === $user->id;
        })->once()->andReturnSelf();
        $mock->shouldReceive('registerTokenWithProvider')->once()->andReturn('5555666');
        $mock->shouldReceive('deleteTokenAtProvider')->never();     // test clear token if got existing token
    });

    $this->userService->updateUserPushNotificationSettings($user, PushNotificationPlatform::PLATFORM_HUAWEI, '5555666');

    $this->assertDatabaseHas('users', [
        'id' => $user->id,
        'push_notification_token' => '5555666',
        'push_notification_platform' => PushNotificationPlatform::PLATFORM_HUAWEI->value,
    ]);

    $this->assertDatabaseHas('users', [
        'id' => $user2->id,
        'push_notification_token' => null,
        'push_notification_platform' => null,
    ]);

});

test('clearUserPushNotificationSettings()', function () {

    $user = User::factory()->create([
        'push_notification_token' => '1234556',
        'push_notification_platform' => PushNotificationPlatform::PLATFORM_ANDROID->value,
    ]);

    $this->mock(AndroidPushNotificationService::class, function (MockInterface $mock) {
        $mock->shouldReceive('deleteTokenAtProvider')->once()->andReturnTrue();
    });

    $this->userService->clearUserPushNotificationSettings($user);

    $this->assertDatabaseHas('users', [
        'id' => $user->id,
        'push_notification_token' => null,
        'push_notification_platform' => null,
    ]);
});

test('unlinkGuardian() failed', function () {
    $another_user = User::factory()->create();
    $another_guardian = Guardian::factory()->create([
        'user_id' => $another_user->id,
    ]);

    $user = User::factory()->create();

    expect(function () use ($another_guardian, $user) {
        $this->userService->unlinkGuardian($user, $another_guardian->id);
    })->toThrow(function (Exception $e) {
        expect($e->getCode())->toBe(4011);
    }, 'This guardian does not belongs to the user.');
});

test('unlinkGuardian() success', function () {
    $another_user = User::factory()->create();
    $another_guardian = Guardian::factory()->create([
        'user_id' => $another_user->id,
    ]);

    $this->userService->unlinkGuardian($another_user, $another_guardian->id);

    $this->assertDatabaseHas($this->guardianTable, [
        'id' => $another_guardian->id,
        'user_id' => null
    ]);
});
