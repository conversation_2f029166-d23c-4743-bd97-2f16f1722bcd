<?php

use App\Enums\BookStatus;

test('getLabel()', function () {
    expect(BookStatus::getLabel(BookStatus::AVAILABLE))->toBe('Available')
        ->and(BookStatus::getLabel(BookStatus::BORROWED))->toBe('Borrowed')
        ->and(BookStatus::getLabel(BookStatus::LOST))->toBe('Lost')
        ->and(BookStatus::getLabel(BookStatus::NOT_AVAILABLE))->toBe('Not Available');
});

test('values()', function () {
    expect(BookStatus::values())->toBe(['AVAILABLE', 'BORROWED', 'LOST', 'NOT_AVAILABLE']);
});

test('options()', function () {
    expect(BookStatus::options())->toEqual([
        ['value' => 'AVAILABLE', 'name' => 'Available'],
        ['value' => 'BORROWED', 'name' => 'Borrowed'],
        ['value' => 'LOST', 'name' => 'Lost'],
        ['value' => 'NOT_AVAILABLE', 'name' => 'Not Available'],
    ]);
});
