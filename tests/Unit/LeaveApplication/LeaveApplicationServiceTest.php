<?php

use App\Enums\AttendanceCheckInStatus;
use App\Enums\AttendanceStatus;
use App\Enums\ClassType;
use App\Enums\Day;
use App\Enums\LeaveApplicationStatus;
use App\Enums\PeriodAttendanceStatus;
use App\Models\Attendance;
use App\Models\AttendanceInput;
use App\Models\AttendancePeriodOverride;
use App\Models\AttendancePeriodOverrideLeaveApplication;
use App\Models\Calendar;
use App\Models\CalendarSetting;
use App\Models\CalendarTarget;
use App\Models\ClassModel;
use App\Models\Employee;
use App\Models\LeaveApplication;
use App\Models\LeaveApplicationPeriod;
use App\Models\LeaveApplicationType;
use App\Models\Media;
use App\Models\Period;
use App\Models\PeriodAttendance;
use App\Models\PeriodGroup;
use App\Models\PeriodLabel;
use App\Models\SemesterClass;
use App\Models\SemesterSetting;
use App\Models\Student;
use App\Models\StudentClass;
use App\Models\StudentTimetable;
use App\Models\Timeslot;
use App\Models\TimeslotOverride;
use App\Models\TimeslotTeacher;
use App\Models\Timetable;
use App\Services\LeaveApplicationService;
use Carbon\Carbon;
use Carbon\CarbonPeriod;
use Database\Seeders\InternationalizationSeeder;
use Database\Seeders\PermissionSeeder;
use Illuminate\Http\UploadedFile;
use Laravel\Sanctum\Sanctum;

beforeEach(function () {
    $this->seed([
        InternationalizationSeeder::class,
        PermissionSeeder::class,
    ]);

    $this->leaveApplicationService = app()->make(LeaveApplicationService::class);

    app()->setLocale('en');

    $this->table = resolve(LeaveApplication::class)->getTable();
    $this->period_table = resolve(LeaveApplicationPeriod::class)->getTable();
    $this->systemEmployee = Employee::factory()->create(['employee_number' => Employee::SYSTEM_ID, 'id' => 3333]);
});

test('getAllPaginatedLeaveApplications getAllLeaveApplications', function (int $expected_count, array $filters, array $expected_models, $expected_array_to_check = null) {

    $sick_leave = LeaveApplicationType::factory()->create([
        'name->en' => 'Sick Leave',
        'name->zh' => '病假',
    ]);

    $emergency_leave = LeaveApplicationType::factory()->create([
        'name->en' => 'Emergency Leave',
        'name->zh' => '紧急假',
    ]);

    $pending_student_application = LeaveApplication::factory()->create([
        'leave_applicable_type' => Student::class,
        'leave_applicable_id' => Student::factory(),
        'status' => LeaveApplicationStatus::PENDING->value,
        'leave_application_type_id' => $sick_leave->id,
        'is_present' => false,
    ]);

    LeaveApplicationPeriod::factory()->create([
        'leave_application_id' => $pending_student_application->id,
        'date' => '2024-12-05',
    ]);

    LeaveApplicationPeriod::factory()->create([
        'leave_application_id' => $pending_student_application->id,
        'date' => '2024-12-07',
    ]);

    Student::factory()->create(['id' => 999]);

    $employee = Employee::factory()->create();
    $employee->id = 555;
    $employee->save();

    $pending_teacher_application = LeaveApplication::factory()->create([
        'leave_applicable_type' => Employee::class,
        'leave_applicable_id' => $employee->id,
        'status' => LeaveApplicationStatus::PENDING->value,
        'leave_application_type_id' => $emergency_leave->id,
        'is_present' => false,
    ]);

    LeaveApplicationPeriod::factory()->create([
        'leave_application_id' => $pending_teacher_application->id,
        'date' => '2024-12-06',
    ]);

    $leave_applications = [
        'pending_student_application' => $pending_student_application,
        'pending_teacher_application' => $pending_teacher_application,
        'rejected_student_application' => LeaveApplication::factory()->create([
            'leave_applicable_type' => Student::class,
            'leave_applicable_id' => Student::factory(),
            'status' => LeaveApplicationStatus::REJECTED->value,
            'leave_application_type_id' => $sick_leave->id,
            'is_present' => true,
        ]),
    ];

    $list = [
        'status_list' => [
            'pending_status' => LeaveApplicationStatus::PENDING->value,
            'rejected_status' => LeaveApplicationStatus::REJECTED->value,
        ],
        'type_list' => [
            'sick_leave' => $sick_leave->id,
            'emergency_leave' => $emergency_leave->id,
        ],
    ];
    if ($expected_array_to_check) {
        // overwrite second_requestor, second_class_subject, first_substitute_teacher to actual id
        array_walk($filters, function (&$value) use ($list, $expected_array_to_check) {
            $value = $list[$expected_array_to_check][$value];
        });
    }

    $result_paginated = $this->leaveApplicationService->getAllPaginatedLeaveApplications($filters)->toArray();
    expect($result_paginated['data'])->toHaveCount($expected_count);
    foreach ($expected_models as $key => $model) {
        $expected_data = $leave_applications[$model]->toArray();
        expect($result_paginated['data'][$key])->toEqual($expected_data);
    }

    $result = $this->leaveApplicationService->getAllLeaveApplications($filters)->toArray();
    expect($result)->toHaveCount($expected_count);
    foreach ($expected_models as $key => $model) {
        $expected_data = $leave_applications[$model]->toArray();
        expect($result[$key])->toEqual($expected_data);
    }
})->with([
    'no filter' => [3, [], ['pending_student_application', 'pending_teacher_application', 'rejected_student_application']],
    'filter by status' => [2, ['status' => 'pending_status'], ['pending_student_application', 'pending_teacher_application'], 'status_list'],
    'filter by leave_application_type_id' => [2, ['leave_application_type_id' => 'sick_leave'], ['pending_student_application', 'rejected_student_application'], 'type_list'],
    'filter by leave_applicable_type' => [2, ['leave_applicable_type' => Student::class], ['pending_student_application', 'rejected_student_application']],
    'filter by leave_applicable_type leave_applicable_id' => [1, ['leave_applicable_type' => Employee::class, 'leave_applicable_id' => 555], ['pending_teacher_application']],
    'filter by leave_applicable_type leave_applicable_id without leave application' => [0, ['leave_applicable_type' => Student::class, 'leave_applicable_id' => 999], []],
    'filter by leave_application_date_from' => [2, ['leave_application_date_from' => '2024-12-06', 'order_by' => ['id' => 'asc']], ['pending_student_application', 'pending_teacher_application']],
    'filter by leave_application_date_to' => [1, ['leave_application_date_to' => '2024-12-05'], ['pending_student_application']],
    'filter by leave_application_date_from leave_application_date_to' => [1, ['leave_application_date_from' => '2024-12-01', 'leave_application_date_to' => '2024-12-05'], ['pending_student_application']],
    'filter by leave_application_date' => [1, ['leave_application_date' => '2024-12-06'], ['pending_teacher_application']],
    'filter by is_present' => [1, ['is_present' => true], ['rejected_student_application']],
    'sort by id asc' => [3, ['order_by' => ['id' => 'asc']], ['pending_student_application', 'pending_teacher_application', 'rejected_student_application']],
    'sort by id desc' => [3, ['order_by' => ['id' => 'desc']], ['rejected_student_application', 'pending_teacher_application', 'pending_student_application']],
]);

test('createLeaveApplication without proof', function () {
    $student = Student::factory()->create();

    $sick_leave = LeaveApplicationType::factory()->create([
        'name->en' => 'Sick Leave',
        'name->zh' => '病假',
    ]);

    $period_label1 = PeriodLabel::factory()->create();
    $period_label2 = PeriodLabel::factory()->create();

    $this->assertDatabaseCount($this->table, 0);

    $leave_application = $this->leaveApplicationService
        ->setLeaveApplicable($student)
        ->setLeaveApplicationType($sick_leave)
        ->setReason('test reason')
        ->setRemarks(null)
        ->setAveragePointDeduction(10)
        ->setConductPointDeduction(20)
        ->setFromDate('2024-12-01')
        ->setToDate('2024-12-01')
        ->setIsPresent(false)
        ->setPeriodLabelIds([$period_label1->id, $period_label2->id])
        ->setIsFullDay(false)
        ->createLeaveApplication();

    $this->assertDatabaseCount($this->table, 1);

    $this->assertDatabaseCount('media', 0);


    $this->assertDatabaseHas($this->table, [
        'id' => $leave_application->id,
        'status' => LeaveApplicationStatus::PENDING->value,
        'leave_application_type_id' => $sick_leave->id,
        'reason' => 'test reason',
        'remarks' => null,
        'is_present' => false,
        'average_point_deduction' => 10,
        'conduct_point_deduction' => 20,
    ]);

    $this->assertDatabaseCount($this->period_table, 2);

    $this->assertDatabaseHas($this->period_table, [
        'leave_application_id' => $leave_application->id,
        'period' => $period_label1->period,
        'date' => '2024-12-01',
    ]);
    $this->assertDatabaseHas($this->period_table, [
        'leave_application_id' => $leave_application->id,
        'period' => $period_label2->period,
        'date' => '2024-12-01',
    ]);

    expect($leave_application->leaveApplicationPeriods)->toHaveCount(2);

    expect($leave_application->leaveApplicable)->toBeInstanceOf(Student::class)
        ->and($leave_application->leaveApplicable->id)->toBe($student->id);

});

test('createLeaveApplication with proof', function () {
    $student = Student::factory()->create();

    $sick_leave = LeaveApplicationType::factory()->create([
        'name->en' => 'Sick Leave',
        'name->zh' => '病假',
    ]);

    $period_label1 = PeriodLabel::factory()->create();
    $period_label2 = PeriodLabel::factory()->create();

    $first_file = UploadedFile::fake()->create('first_file.png', 500);

    $this->assertDatabaseCount($this->table, 0);

    $leave_application = $this->leaveApplicationService
        ->setLeaveApplicable($student)
        ->setLeaveApplicationType($sick_leave)
        ->setReason('test reason')
        ->setRemarks(null)
        ->setAveragePointDeduction(10)
        ->setConductPointDeduction(20)
        ->setFromDate('2024-12-01')
        ->setToDate('2024-12-02')
        ->setProof($first_file)
        ->setIsPresent(false)
        ->setPeriodLabelIds([$period_label2->id, $period_label1->id])
        ->setIsFullDay(false)
        ->createLeaveApplication();

    $this->assertDatabaseCount($this->table, 1);

    $this->assertDatabaseCount('media', 1);

    $this->assertDatabaseHas('media', [
        'model_type' => get_class($leave_application),
        'model_id' => $leave_application->id,
        'collection_name' => LeaveApplication::PROOF,
        'name' => 'first_file',
    ]);

    $this->assertDatabaseHas($this->table, [
        'id' => $leave_application->id,
        'status' => LeaveApplicationStatus::PENDING->value,
        'leave_application_type_id' => $sick_leave->id,
        'reason' => 'test reason',
        'remarks' => null,
        'is_present' => false,
        'average_point_deduction' => 10,
        'conduct_point_deduction' => 20,
    ]);

    $this->assertDatabaseCount($this->period_table, 4);

    $this->assertDatabaseHas($this->period_table, [
        'leave_application_id' => $leave_application->id,
        'period' => $period_label1->period,
        'date' => '2024-12-01',
    ]);
    $this->assertDatabaseHas($this->period_table, [
        'leave_application_id' => $leave_application->id,
        'period' => $period_label2->period,
        'date' => '2024-12-01',
    ]);
    $this->assertDatabaseHas($this->period_table, [
        'leave_application_id' => $leave_application->id,
        'period' => $period_label1->period,
        'date' => '2024-12-02',
    ]);
    $this->assertDatabaseHas($this->period_table, [
        'leave_application_id' => $leave_application->id,
        'period' => $period_label2->period,
        'date' => '2024-12-02',
    ]);

    expect($leave_application->leaveApplicationPeriods)->toHaveCount(4);

});


test('updateLeaveApplication', function () {
    $student = Student::factory()->create();
    $emergency_leave = LeaveApplicationType::factory()->create([
        'name->en' => 'Emergency Leave',
        'name->zh' => '紧急假',
    ]);
    $sick_leave = LeaveApplicationType::factory()->create([
        'name->en' => 'Sick Leave',
        'name->zh' => '病假',
    ]);
    $leave_application = LeaveApplication::factory()->create([
        'leave_applicable_type' => Student::class,
        'leave_applicable_id' => $student->id,
        'status' => LeaveApplicationStatus::PENDING->value,
        'leave_application_type_id' => $emergency_leave->id,
        'reason' => 'test',
        'remarks' => null,
        'is_present' => false,
    ]);

    Media::factory()->create([
        'model_type' => get_class($leave_application),
        'model_id' => $leave_application->id,
        'collection_name' => LeaveApplication::PROOF,
        'name' => 'first_file',
    ]);

    $period_group = PeriodGroup::factory()->create();
    $period_label = PeriodLabel::factory()->create(['period_group_id' => $period_group->id, 'period' => 1]);
    $period_label2 = PeriodLabel::factory()->create(['period_group_id' => $period_group->id, 'period' => 2]);
    $period_label3 = PeriodLabel::factory()->create(['period_group_id' => $period_group->id, 'period' => 3]);
    LeaveApplicationPeriod::factory()->create(['leave_application_id' => $leave_application->id, 'date' => '2024-12-01', 'period' => $period_label->period]);
    LeaveApplicationPeriod::factory()->create(['leave_application_id' => $leave_application->id, 'date' => '2024-12-02', 'period' => $period_label->period]);

    $second_file = UploadedFile::fake()->create('second_file.png', 500);

    $this->assertDatabaseCount($this->table, 1);

    $leave_application = $this->leaveApplicationService
        ->setLeaveApplication($leave_application)
        ->setLeaveApplicationType($sick_leave)
        ->setReason('updated_reason')
        ->setRemarks('leave approved')
        ->setIsPresent(true)
        ->setFromDate('2024-12-01')
        ->setToDate('2024-12-07')
        ->setAveragePointDeduction(10)
        ->setConductPointDeduction(20)
        ->setPeriodLabelIds([$period_label2->id, $period_label3->id])
        ->setIsFullDay(false)
        ->setUpdateProof(true)
        ->setProof($second_file)
        ->updateLeaveApplication();

    $this->assertDatabaseCount($this->table, 1);
    $this->assertDatabaseCount('media', 1);

    $this->assertDatabaseHas('media', [
        'model_type' => get_class($leave_application),
        'model_id' => $leave_application->id,
        'collection_name' => LeaveApplication::PROOF,
        'name' => 'second_file',
    ]);

    $this->assertDatabaseHas($this->table, [
        'id' => $leave_application->id,
        'status' => LeaveApplicationStatus::PENDING->value,
        'leave_application_type_id' => $sick_leave->id,
        'reason' => 'updated_reason',
        'remarks' => 'leave approved',
        'is_present' => true,
    ]);

    expect(LeaveApplicationPeriod::all())->toHaveCount(14);

    $dates_to_check = ['2024-12-01', '2024-12-02', '2024-12-03', '2024-12-04', '2024-12-05', '2024-12-06', '2024-12-07'];
    foreach ($dates_to_check as $date) {
        $this->assertDatabaseMissing($this->period_table, [
            'leave_application_id' => $leave_application->id,
            'date' => $date,
            'period' => $period_label->period,
        ]);
        $this->assertDatabaseHas($this->period_table, [
            'leave_application_id' => $leave_application->id,
            'date' => $date,
            'period' => $period_label2->period,
        ]);
        $this->assertDatabaseHas($this->period_table, [
            'leave_application_id' => $leave_application->id,
            'date' => $date,
            'period' => $period_label3->period,
        ]);
    }
});

test('deleteLeaveApplication success', function () {

    $leave_application = LeaveApplication::factory()->create();
    LeaveApplicationPeriod::factory(3)->create([
        'leave_application_id' => $leave_application->id,
    ]);
    $this->assertDatabaseCount($this->table, 1);
    expect($leave_application->leaveApplicationPeriods)->toHaveCount(3);

    $this->leaveApplicationService->setLeaveApplication($leave_application)->deleteLeaveApplication();

    $this->assertDatabaseCount($this->table, 0);
    expect(LeaveApplicationPeriod::where('leave_application_id', $leave_application->id)->count())->toBe(0);

});

test('deleteLeaveApplication failed with existing student attendance period override', function () {

    $leave_application = LeaveApplication::factory()->create();
    LeaveApplicationPeriod::factory(3)->create([
        'leave_application_id' => $leave_application->id,
    ]);

    $attendance_period_override = AttendancePeriodOverride::factory()->create([
        'period' => '2025-02-04',
        'attendance_from' => '10:00:00',
        'attendance_to' => '14:00:00',
    ]);

    AttendancePeriodOverrideLeaveApplication::factory()->create([
        'attendance_period_override_id' => $attendance_period_override->id,
        'leave_application_id' => $leave_application->id,
    ]);

    $this->assertDatabaseCount($this->table, 1);
    expect($leave_application->leaveApplicationPeriods)->toHaveCount(3);

    $this->expectExceptionCode(50005);
    $this->expectExceptionMessage('Unable to delete leave application as it\'s being used');
    $this->leaveApplicationService->setLeaveApplication($leave_application)->deleteLeaveApplication();

});

test('deleteLeaveApplication failed with existing attendance', function () {

    $leave_application = LeaveApplication::factory()->create();
    LeaveApplicationPeriod::factory(3)->create([
        'leave_application_id' => $leave_application->id,
    ]);

    Attendance::factory()->create([
        'leave_application_id' => $leave_application->id,
        'attendance_recordable_type' => Student::class,
        'status' => AttendanceStatus::PRESENT->value,
    ]);

    $this->assertDatabaseCount($this->table, 1);
    expect($leave_application->leaveApplicationPeriods)->toHaveCount(3);

    $this->expectExceptionCode(50005);
    $this->expectExceptionMessage('Unable to delete leave application as it\'s being used');
    $this->leaveApplicationService->setLeaveApplication($leave_application)->deleteLeaveApplication();

});

test('deleteLeaveApplication failed with existing period attendance', function () {

    $leave_application = LeaveApplication::factory()->create();
    LeaveApplicationPeriod::factory(3)->create([
        'leave_application_id' => $leave_application->id,
    ]);

    PeriodAttendance::factory()->create([
        'leave_application_id' => $leave_application->id,
        'status' => PeriodAttendanceStatus::PRESENT->value,
    ]);

    $this->assertDatabaseCount($this->table, 1);
    expect($leave_application->leaveApplicationPeriods)->toHaveCount(3);

    $this->expectExceptionCode(50005);
    $this->expectExceptionMessage('Unable to delete leave application as it\'s being used');
    $this->leaveApplicationService->setLeaveApplication($leave_application)->deleteLeaveApplication();

});


test('updateStatus', function () {
    $student = Student::factory()->create(['is_active' => true]);
    $student2 = Student::factory()->create(['is_active' => true]);
    $student3 = Student::factory()->create(['is_active' => true]);
    $student4 = Student::factory()->create(['is_active' => true]);

    $active_calendar_2025 = Calendar::factory()->create([
        'year' => '2025',
        'name->en' => 'calendar 2025',
        'name->zh' => '日历 2025',
        'is_default' => true,
        'is_active' => true,
    ]);
    $dates = CarbonPeriod::create('2025-02-01', '2025-02-20');
    foreach ($dates as $date) {
        CalendarSetting::factory()->create([
            'calendar_id' => $active_calendar_2025->id,
            'date' => $date->toDateString(),
            'is_attendance_required' => true,
            'description' => null,
        ]);
    }
    $calendar_targets = [];
    foreach ([$student->id, $student2->id, $student3->id, $student4->id] as $student_id) {
        $calendar_targets[] = [
            'calendar_id' => $active_calendar_2025->id,
            'priority' => 10,
            'calendar_targetable_type' => Student::class,
            'calendar_targetable_id' => $student_id,
        ];
    }
    CalendarTarget::insert($calendar_targets);

    $semester_setting = SemesterSetting::factory()->create();
    $main_class_j111 = ClassModel::factory()->create([
        'name->en' => 'J111',
        'name->zh' => '初一11',
    ]);
    $semester_class1 = SemesterClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'class_id' => $main_class_j111->id,
    ]);
    $period_group1 = PeriodGroup::factory()->create();
    $main_j111_timetable = Timetable::factory()->create([
        'name' => 'J111 timetable',
        'is_active' => true,
        'period_group_id' => $period_group1->id,
        'semester_class_id' => $semester_class1,
    ]);

    $period1 = Period::factory()->create([
        'period_group_id' => $period_group1->id,
        'period' => 1,
        'from_time' => '08:00:00',
        'to_time' => '08:30:00',
        'day' => Day::MONDAY,
        'display_group' => 1,
    ]);
    $period_label1 = PeriodLabel::factory()->create([
        'period_group_id' => $period_group1->id,
        'period' => 1,
    ]);
    $period2 = Period::factory()->create([
        'period_group_id' => $period_group1->id,
        'period' => 2,
        'from_time' => '08:30:00',
        'to_time' => '09:00:00',
        'day' => Day::MONDAY,
        'display_group' => 1,
    ]);
    $period_label2 = PeriodLabel::factory()->create([
        'period_group_id' => $period_group1->id,
        'period' => 2,
    ]);
    $period3 = Period::factory()->create([
        'period_group_id' => $period_group1->id,
        'period' => 3,
        'from_time' => '09:00:00',
        'to_time' => '10:00:00',
        'day' => Day::MONDAY,
        'display_group' => 1,
    ]);
    $period_label3 = PeriodLabel::factory()->create([
        'period_group_id' => $period_group1->id,
        'period' => 3,
    ]);
    $period4 = Period::factory()->create([
        'period_group_id' => $period_group1->id,
        'period' => 4,
        'from_time' => '10:00:00',
        'to_time' => '10:30:00',
        'day' => Day::MONDAY,
        'display_group' => 1,
    ]);
    $period_label4 = PeriodLabel::factory()->create([
        'period_group_id' => $period_group1->id,
        'period' => 4,
    ]);

    StudentClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'semester_class_id' => $semester_class1->id,
        'student_id' => $student->id,
        'class_type' => ClassType::PRIMARY
    ]);

    StudentClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'semester_class_id' => $semester_class1->id,
        'student_id' => $student2->id,
        'class_type' => ClassType::PRIMARY
    ]);

    StudentClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'semester_class_id' => $semester_class1->id,
        'student_id' => $student3->id,
        'class_type' => ClassType::PRIMARY
    ]);

    StudentClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'semester_class_id' => $semester_class1->id,
        'student_id' => $student4->id,
        'class_type' => ClassType::PRIMARY
    ]);

    $timeslot1 = Timeslot::factory()->create([
        'timetable_id' => $main_j111_timetable->id,
        'period_id' => $period1->id,
        'placeholder' => null,
        'attendance_from' => '08:00',
        'attendance_to' => '08:30',
    ]);
    $timeslot2 = Timeslot::factory()->create([
        'timetable_id' => $main_j111_timetable->id,
        'period_id' => $period2->id,
        'placeholder' => null,
        'attendance_from' => '08:30',
        'attendance_to' => '09:00',
    ]);
    $timeslot3 = Timeslot::factory()->create([
        'timetable_id' => $main_j111_timetable->id,
        'period_id' => $period3->id,
        'placeholder' => null,
        'attendance_from' => '09:00',
        'attendance_to' => '10:00',
    ]);
    $timeslot4 = Timeslot::factory()->create([
        'timetable_id' => $main_j111_timetable->id,
        'period_id' => $period4->id,
        'placeholder' => null,
        'attendance_from' => '10:00',
        'attendance_to' => '10:30',
    ]);

    $timeslot_teacher1 = TimeslotTeacher::factory()->create([
        'timeslot_id' => $timeslot1->id,
    ]);
    $timeslot_teacher2 = TimeslotTeacher::factory()->create([
        'timeslot_id' => $timeslot2->id,
    ]);
    $timeslot_teacher3 = TimeslotTeacher::factory()->create([
        'timeslot_id' => $timeslot3->id,
    ]);
    $timeslot_teacher4 = TimeslotTeacher::factory()->create([
        'timeslot_id' => $timeslot4->id,
    ]);

    StudentTimetable::refreshViewTable(false);

    config([
        'school.timezone' => 'Asia/Kuala_Lumpur'
    ]);
    Carbon::setTestNow('2025-02-14 00:00:00');

    // 3 leaves, 2 approved 1 pending
    // approved, period 1
    $leave_application_student = LeaveApplication::factory()->create([
        'status' => LeaveApplicationStatus::APPROVED->value,
        'leave_applicable_type' => Student::class,
        'leave_applicable_id' => $student->id,
        'is_full_day' => false,
    ]);
    // approved, period 2
    $leave_application2_student = LeaveApplication::factory()->create([
        'status' => LeaveApplicationStatus::APPROVED->value,
        'leave_applicable_type' => Student::class,
        'leave_applicable_id' => $student->id,
        'is_full_day' => false,
    ]);
    // pending, period 3
    $leave_application3_student = LeaveApplication::factory()->create([
        'status' => LeaveApplicationStatus::PENDING->value,
        'leave_applicable_type' => Student::class,
        'leave_applicable_id' => $student->id,
        'is_full_day' => false,
    ]);
    // student 1
    // 2025-02-10 Monday
    // leave periods 1,2 - approved
    // leave period  3 - pending
    // 2025-02-11
    // leave period  1 - approved
    LeaveApplicationPeriod::factory()->create(['leave_application_id' => $leave_application_student->id, 'date' => '2025-02-10', 'period' => $period_label1->period]); // approved 2025-02-10
    LeaveApplicationPeriod::factory()->create(['leave_application_id' => $leave_application_student->id, 'date' => '2025-02-11', 'period' => $period_label1->period]); // approved but on 2025-02-11
    LeaveApplicationPeriod::factory()->create(['leave_application_id' => $leave_application2_student->id, 'date' => '2025-02-10', 'period' => $period_label2->period]); // approved 2025-02-10
    LeaveApplicationPeriod::factory()->create(['leave_application_id' => $leave_application3_student->id, 'date' => '2025-02-10', 'period' => $period_label3->period]); // pending
    PeriodAttendance::factory()->create([ // period attendance for pending leave
        'student_id' => $student->id,
        'date' => '2025-02-10',
        'timeslot_id' => $timeslot3->id,
        'updated_by_employee_id' => $timeslot_teacher3->employee_id,
        'status' => PeriodAttendanceStatus::PRESENT->value,
        'period' => $timeslot3->period->period,
    ]);
    $attendance_period_override_02_10 = AttendancePeriodOverride::factory()->create([
        'attendance_recordable_type' => Student::class,
        'attendance_recordable_id' => $student->id,
        'period' => '2025-02-10',
        'attendance_from' => $period3->from_time,
        'attendance_to' => $period4->to_time,
    ]);
    AttendancePeriodOverrideLeaveApplication::factory()->create([
        'attendance_period_override_id' => $attendance_period_override_02_10->id,
        'leave_application_id' => $leave_application_student->id,
    ]);
    AttendancePeriodOverrideLeaveApplication::factory()->create([
        'attendance_period_override_id' => $attendance_period_override_02_10->id,
        'leave_application_id' => $leave_application2_student->id,
    ]);
    $attendance_period_override_02_11 = AttendancePeriodOverride::factory()->create([
        'attendance_recordable_type' => Student::class,
        'attendance_recordable_id' => $student->id,
        'period' => '2025-02-11',
        'attendance_from' => $period2->from_time,
        'attendance_to' => $period4->to_time,
    ]);
    AttendancePeriodOverrideLeaveApplication::factory()->create([
        'attendance_period_override_id' => $attendance_period_override_02_11->id,
        'leave_application_id' => $leave_application_student->id,
    ]);
    // user tap card at 09:01, periods - 1,2 = 08:00:00 - 09:00:00
    AttendanceInput::factory()->create([
        'attendance_recordable_type' => Student::class,
        'attendance_recordable_id' => $student->id,
        'card_id' => null,
        'remarks' => null,
        'date' => '2025-02-10', // Monday
        'record_datetime' => '2025-02-10 01:01:00', // UTC, malaysia timezone = 09:01:00
        'is_manual' => false,
        'updated_by_employee_id' => null,
    ]);

    // 2025-02-10 Monday
    // student 2, leave periods - 3,4
    // 2 pending leaves
    $leave_application_student2 = LeaveApplication::factory()->create([
        'status' => LeaveApplicationStatus::PENDING->value,
        'leave_applicable_type' => Student::class,
        'leave_applicable_id' => $student2->id,
        'is_full_day' => false,
    ]);
    $leave_application2_student2 = LeaveApplication::factory()->create([
        'status' => LeaveApplicationStatus::PENDING->value,
        'leave_applicable_type' => Student::class,
        'leave_applicable_id' => $student2->id,
        'is_full_day' => false,
    ]);
    LeaveApplicationPeriod::factory()->create(['leave_application_id' => $leave_application_student2->id, 'date' => '2025-02-10', 'period' => $period_label3->period]);
    PeriodAttendance::factory()->create([ // period attendance for pending leave
        'student_id' => $student2->id,
        'date' => '2025-02-10',
        'timeslot_id' => $timeslot3->id,
        'updated_by_employee_id' => $timeslot_teacher3->employee_id,
        'status' => PeriodAttendanceStatus::PRESENT->value,
        'period' => $timeslot3->period->period,
    ]);
    LeaveApplicationPeriod::factory()->create(['leave_application_id' => $leave_application2_student2->id, 'date' => '2025-02-10', 'period' => $period_label4->period]);
    PeriodAttendance::factory()->create([ // period attendance for pending leave
        'student_id' => $student2->id,
        'date' => '2025-02-10',
        'timeslot_id' => $timeslot4->id,
        'updated_by_employee_id' => $timeslot_teacher4->employee_id,
        'status' => PeriodAttendanceStatus::PRESENT->value,
        'period' => $timeslot4->period->period,
    ]);


    // 2025-02-03 Monday
    // student 3, leave periods - 3,4
    $leave_application3 = LeaveApplication::factory()->create([
        'status' => LeaveApplicationStatus::PENDING->value,
        'leave_applicable_type' => Student::class,
        'leave_applicable_id' => $student3->id,
        'is_full_day' => false,
    ]);
    LeaveApplicationPeriod::factory()->create(['leave_application_id' => $leave_application3->id, 'date' => '2025-02-03', 'period' => $period_label3->period]);
    PeriodAttendance::factory()->create([ // period attendance for pending leave
        'student_id' => $student3->id,
        'date' => '2025-02-03',
        'timeslot_id' => $timeslot3->id,
        'updated_by_employee_id' => $timeslot_teacher3->employee_id,
        'status' => PeriodAttendanceStatus::PRESENT->value,
        'period' => $timeslot3->period->period,
    ]);
    LeaveApplicationPeriod::factory()->create(['leave_application_id' => $leave_application3->id, 'date' => '2025-02-03', 'period' => $period_label4->period]);
    PeriodAttendance::factory()->create([ // period attendance for pending leave
        'student_id' => $student3->id,
        'date' => '2025-02-03',
        'timeslot_id' => $timeslot4->id,
        'updated_by_employee_id' => $timeslot_teacher4->employee_id,
        'status' => PeriodAttendanceStatus::PRESENT->value,
        'period' => $timeslot4->period->period,
    ]);


    // 2025-02-17 Monday (future date)
    // student 4, leave periods - 3,4
    $leave_application4 = LeaveApplication::factory()->create([
        'status' => LeaveApplicationStatus::PENDING->value,
        'leave_applicable_type' => Student::class,
        'leave_applicable_id' => $student4->id,
        'is_full_day' => false,
    ]);
    LeaveApplicationPeriod::factory()->create(['leave_application_id' => $leave_application4->id, 'date' => '2025-02-17', 'period' => $period_label3->period]);
    LeaveApplicationPeriod::factory()->create(['leave_application_id' => $leave_application4->id, 'date' => '2025-02-17', 'period' => $period_label4->period]);
    $existing_period_attendance_period3_future_date = PeriodAttendance::factory()->create([ // period attendance for future date (period 3 only)
        'student_id' => $student4->id,
        'date' => '2025-02-17',
        'timeslot_id' => $timeslot3->id,
        'updated_by_employee_id' => $timeslot_teacher3->employee_id,
        'status' => PeriodAttendanceStatus::PRESENT->value,
        'period' => $timeslot3->period->period,
    ]);

    $this->assertDatabaseHas($this->table, [
        'id' => $leave_application3_student->id,
        'status' => LeaveApplicationStatus::PENDING->value,
    ]);
    $this->assertDatabaseHas($this->table, [
        'id' => $leave_application_student2->id,
        'status' => LeaveApplicationStatus::PENDING->value,
    ]);
    $this->assertDatabaseHas($this->table, [
        'id' => $leave_application2_student2->id,
        'status' => LeaveApplicationStatus::PENDING->value,
    ]);
    $this->assertDatabaseHas($this->table, [
        'id' => $leave_application3->id,
        'status' => LeaveApplicationStatus::PENDING->value,
    ]);
    $this->assertDatabaseHas($this->table, [
        'id' => $leave_application4->id,
        'status' => LeaveApplicationStatus::PENDING->value,
    ]);


    $employee = Employee::factory()->create();
    $employee->user->assignRole('Super Admin');
    Sanctum::actingAs($employee->user);

    app()->make(LeaveApplicationService::class)
        ->setEmployee($employee)
        ->updateStatus([$leave_application3_student->id, $leave_application_student2->id, $leave_application2_student2->id, $leave_application3->id, $leave_application4->id], LeaveapplicationStatus::APPROVED->value);

    // 5 pending -> approved, 2 approved
    $this->assertDatabaseCount($this->table, 7);
    $this->assertDatabaseHas($this->table, [
        'id' => $leave_application_student->id,
        'status' => LeaveApplicationStatus::APPROVED->value,
    ]);
    $this->assertDatabaseHas($this->table, [
        'id' => $leave_application2_student->id,
        'status' => LeaveApplicationStatus::APPROVED->value,
    ]);
    $this->assertDatabaseHas($this->table, [
        'id' => $leave_application3_student->id,
        'status' => LeaveApplicationStatus::APPROVED->value,
    ]);
    $this->assertDatabaseHas($this->table, [
        'id' => $leave_application_student2->id,
        'status' => LeaveApplicationStatus::APPROVED->value,
    ]);
    $this->assertDatabaseHas($this->table, [
        'id' => $leave_application2_student2->id,
        'status' => LeaveApplicationStatus::APPROVED->value,
    ]);
    $this->assertDatabaseHas($this->table, [
        'id' => $leave_application3->id,
        'status' => LeaveApplicationStatus::APPROVED->value,
    ]);
    $this->assertDatabaseHas($this->table, [
        'id' => $leave_application4->id,
        'status' => LeaveApplicationStatus::APPROVED->value,
    ]);

    $this->assertDatabaseCount('attendance_period_override', 5);
    $this->assertDatabaseCount('attendance_period_override_leave_application', 8);

    // student 1
    // not updated
    $this->assertDatabaseHas('attendance_period_override', [
        'attendance_recordable_type' => $attendance_period_override_02_11->attendance_recordable_type,
        'attendance_recordable_id' => $attendance_period_override_02_11->attendance_recordable_id,
        'period' => $attendance_period_override_02_11->period,
        'attendance_from' => $attendance_period_override_02_11->attendance_from,
        'attendance_to' => $attendance_period_override_02_11->attendance_to,
        'updated_by_employee_id' => $attendance_period_override_02_11->updated_by_employee_id,
    ]);
    $this->assertDatabaseHas('attendance_period_override_leave_application', [
        'leave_application_id' => $leave_application_student->id,
        'attendance_period_override_id' => $attendance_period_override_02_11->id,
    ]);

    // 1,2 approved -> 1,2,3 approved (3 leaves)
    $attendance_period_override_student = AttendancePeriodOverride::where('attendance_recordable_type', Student::class)
        ->where('attendance_recordable_id', $student->id)
        ->where('period', '2025-02-10')
        ->where('attendance_from', $period4->from_time)
        ->where('attendance_to', $period4->to_time)
        ->where('updated_by_employee_id', $employee->id)
        ->first();

    $this->assertDatabaseHas('attendance_period_override_leave_application', [
        'leave_application_id' => $leave_application_student->id,
        'attendance_period_override_id' => $attendance_period_override_student->id,
    ]);
    $this->assertDatabaseHas('attendance_period_override_leave_application', [
        'leave_application_id' => $leave_application2_student->id,
        'attendance_period_override_id' => $attendance_period_override_student->id,
    ]);
    $this->assertDatabaseHas('attendance_period_override_leave_application', [
        'leave_application_id' => $leave_application3_student->id,
        'attendance_period_override_id' => $attendance_period_override_student->id,
    ]);


    // all 3,4 approved but different date
    // student 2
    $attendance_period_override_student2 = AttendancePeriodOverride::where('attendance_recordable_type', Student::class)
        ->where('attendance_recordable_id', $student2->id)
        ->where('period', '2025-02-10')
        ->where('attendance_from', $period1->from_time)
        ->where('attendance_to', $period2->to_time)
        ->where('updated_by_employee_id', $employee->id)
        ->first();
    $this->assertNotNull($attendance_period_override_student2);
    $this->assertDatabaseHas('attendance_period_override_leave_application', [
        'leave_application_id' => $leave_application_student2->id,
        'attendance_period_override_id' => $attendance_period_override_student2->id,
    ]);
    $this->assertDatabaseHas('attendance_period_override_leave_application', [
        'leave_application_id' => $leave_application2_student2->id,
        'attendance_period_override_id' => $attendance_period_override_student2->id,
    ]);

    // student 3
    $attendance_period_override_student3 = AttendancePeriodOverride::where('attendance_recordable_type', Student::class)
        ->where('attendance_recordable_id', $student3->id)
        ->where('period', '2025-02-03')
        ->where('attendance_from', $period1->from_time)
        ->where('attendance_to', $period2->to_time)
        ->where('updated_by_employee_id', $employee->id)
        ->first();
    $this->assertNotNull($attendance_period_override_student3);
    $this->assertDatabaseHas('attendance_period_override_leave_application', [
        'leave_application_id' => $leave_application3->id,
        'attendance_period_override_id' => $attendance_period_override_student3->id,
    ]);

    // student 4
    $attendance_period_override_student4 = AttendancePeriodOverride::where('attendance_recordable_type', Student::class)
        ->where('attendance_recordable_id', $student4->id)
        ->where('period', '2025-02-17')
        ->where('attendance_from', $period1->from_time)
        ->where('attendance_to', $period2->to_time)
        ->where('updated_by_employee_id', $employee->id)
        ->first();
    $this->assertNotNull($attendance_period_override_student4);
    $this->assertDatabaseHas('attendance_period_override_leave_application', [
        'leave_application_id' => $leave_application4->id,
        'attendance_period_override_id' => $attendance_period_override_student4->id,
    ]);

    $this->assertDatabaseCount('attendances', 3); // only 3 backdated leaves (student1, student2, and student3)
    // another 2 attendances check_in_overridable and check_out_overridable columns will remain null, because no attendance input
    $this->assertDatabaseHas('attendances', [
        'attendance_recordable_type' => Student::class,
        'attendance_recordable_id' => $student->id,
        'date' => '2025-02-10',
        'check_in_datetime' => '2025-02-10 01:01:00',
        'check_in_status' => AttendanceCheckInStatus::ON_TIME->value,
        'check_in_remarks' => null,
        'check_in_overridable_type' => get_class($attendance_period_override_student),
        'check_in_overridable_id' => $attendance_period_override_student->id,
        'attendance_from' => '2025-02-10 02:00:00', // UTC +8 = 10am ($period4->from_time)
        'check_out_datetime' => null,
        'check_out_status' => null,
        'check_out_remarks' => null,
        'check_out_overridable_type' => get_class($attendance_period_override_student),
        'check_out_overridable_id' => $attendance_period_override_student->id,
        'attendance_to' => '2025-02-10 02:30:00', // UTC +8 = 10:30am ($period4->to_time)
        'status' => AttendanceStatus::PRESENT->value,
        'card_id' => null,
        'leave_application_id' => null,
        'is_error' => false,
        'error_message' => null,
    ]);

    $this->assertDatabaseCount('period_attendances', 9);
    // just for reference
    // LeaveApplicationPeriod::factory()->create(['leave_application_id' => $leave_application3_student->id, 'date' => '2025-02-10', 'period_label_id' => $period_label3->id, 'period' => $period3->period]);
    // PeriodAttendance::factory()->create([ // period attendance for pending leave
    //     'student_id' => $student->id,
    //     'date' => '2025-02-10',
    //     'timeslot_id' => $timeslot3->id,
    //     'updated_by_employee_id' => $timeslot_teacher3->employee_id,
    //     'status' => PeriodAttendanceStatus::PRESENT->value
    // ]);
    $this->assertDatabaseHas('period_attendances', [
        'student_id' => $student->id,
        'date' => '2025-02-10',
        'timeslot_id' => $timeslot3->id,
        'updated_by_employee_id' => $timeslot_teacher3->employee_id,
        'status' => PeriodAttendanceStatus::PRESENT->value,
        'leave_application_id' => $leave_application3_student->id,
    ]);
    // only student 1 tapped card
    $this->assertDatabaseHas('period_attendances', [
        'student_id' => $student->id,
        'date' => '2025-02-10',
        'timeslot_id' => $timeslot1->id,
        'updated_by_employee_id' => null,
        'status' => PeriodAttendanceStatus::PRESENT->value,
        'leave_application_id' => null,
    ]);
    $this->assertDatabaseHas('period_attendances', [
        'student_id' => $student->id,
        'date' => '2025-02-10',
        'timeslot_id' => $timeslot2->id,
        'updated_by_employee_id' => null,
        'status' => PeriodAttendanceStatus::PRESENT->value,
        'leave_application_id' => null,
    ]);
    $this->assertDatabaseHas('period_attendances', [
        'student_id' => $student->id,
        'date' => '2025-02-10',
        'timeslot_id' => $timeslot4->id,
        'updated_by_employee_id' => null,
        'status' => PeriodAttendanceStatus::PRESENT->value,
        'leave_application_id' => null,
    ]);
    // just for reference
    // LeaveApplicationPeriod::factory()->create(['leave_application_id' => $leave_application_student2->id, 'date' => '2025-02-10', 'period_label_id' => $period_label3->id, 'period' => $period3->period]);
    // PeriodAttendance::factory()->create([ // period attendance for pending leave
    //     'student_id' => $student2->id,
    //     'date' => '2025-02-10',
    //     'timeslot_id' => $timeslot3->id,
    //     'updated_by_employee_id' => $timeslot_teacher3->employee_id,
    //     'status' => PeriodAttendanceStatus::PRESENT->value
    // ]);
    // LeaveApplicationPeriod::factory()->create(['leave_application_id' => $leave_application2_student2->id, 'date' => '2025-02-10', 'period_label_id' => $period_label4->id, 'period' => $period4->period]);
    // PeriodAttendance::factory()->create([ // period attendance for pending leave
    //     'student_id' => $student2->id,
    //     'date' => '2025-02-10',
    //     'timeslot_id' => $timeslot4->id,
    //     'updated_by_employee_id' => $timeslot_teacher4->employee_id,
    //     'status' => PeriodAttendanceStatus::PRESENT->value
    // ]);
    $this->assertDatabaseHas('period_attendances', [
        'student_id' => $student2->id,
        'date' => '2025-02-10',
        'timeslot_id' => $timeslot3->id,
        'updated_by_employee_id' => $timeslot_teacher3->employee_id,
        'status' => PeriodAttendanceStatus::PRESENT->value,
        'leave_application_id' => $leave_application_student2->id,
    ]);
    $this->assertDatabaseHas('period_attendances', [
        'student_id' => $student2->id,
        'date' => '2025-02-10',
        'timeslot_id' => $timeslot4->id,
        'updated_by_employee_id' => $timeslot_teacher4->employee_id,
        'status' => PeriodAttendanceStatus::PRESENT->value,
        'leave_application_id' => $leave_application2_student2->id,
    ]);
    // just for reference
    // LeaveApplicationPeriod::factory()->create(['leave_application_id' => $leave_application3->id, 'date' => '2025-02-03', 'period_label_id' => $period_label3->id, 'period' => $period3->period]);
    // PeriodAttendance::factory()->create([ // period attendance for pending leave
    //     'student_id' => $student3->id,
    //     'date' => '2025-02-03',
    //     'timeslot_id' => $timeslot3->id,
    //     'updated_by_employee_id' => $timeslot_teacher3->employee_id,
    //     'status' => PeriodAttendanceStatus::PRESENT->value
    // ]);
    // LeaveApplicationPeriod::factory()->create(['leave_application_id' => $leave_application3->id, 'date' => '2025-02-03', 'period_label_id' => $period_label4->id, 'period' => $period4->period]);
    // PeriodAttendance::factory()->create([ // period attendance for pending leave
    //     'student_id' => $student3->id,
    //     'date' => '2025-02-03',
    //     'timeslot_id' => $timeslot4->id,
    //     'updated_by_employee_id' => $timeslot_teacher4->employee_id,
    //     'status' => PeriodAttendanceStatus::PRESENT->value
    // ]);
    $this->assertDatabaseHas('period_attendances', [
        'student_id' => $student3->id,
        'date' => '2025-02-03',
        'timeslot_id' => $timeslot3->id,
        'updated_by_employee_id' => $timeslot_teacher3->employee_id,
        'status' => PeriodAttendanceStatus::PRESENT->value,
        'leave_application_id' => $leave_application3->id,
    ]);
    $this->assertDatabaseHas('period_attendances', [
        'student_id' => $student3->id,
        'date' => '2025-02-03',
        'timeslot_id' => $timeslot4->id,
        'updated_by_employee_id' => $timeslot_teacher4->employee_id,
        'status' => PeriodAttendanceStatus::PRESENT->value,
        'leave_application_id' => $leave_application3->id,
    ]);
    // just for reference
    // LeaveApplicationPeriod::factory()->create(['leave_application_id' => $leave_application4->id, 'date' => '2025-02-17', 'period_label_id' => $period_label3->id, 'period' => $period3->period]);
    // LeaveApplicationPeriod::factory()->create(['leave_application_id' => $leave_application4->id, 'date' => '2025-02-17', 'period_label_id' => $period_label4->id, 'period' => $period4->period]);
    // future date won't create period attendance (if somehow already exists, it will still be updated)
    $this->assertDatabaseHas('period_attendances', [
        'student_id' => $student4->id,
        'date' => '2025-02-17',
        'timeslot_id' => $timeslot3->id,
        'updated_by_employee_id' => $timeslot_teacher3->employee_id,
        'status' => PeriodAttendanceStatus::PRESENT->value,
        'leave_application_id' => $leave_application4->id,
    ]);
    $this->assertDatabaseMissing('period_attendances', [
        'student_id' => $student4->id,
        'date' => '2025-02-17',
        'timeslot_id' => $timeslot4->id,
        'updated_by_employee_id' => null,
        'status' => PeriodAttendanceStatus::ABSENT->value,
        'leave_application_id' => $leave_application4->id,
    ]);

    // change back to pending
    // student 1 (only change one to pending, 2 remain approved, 1 other date + approved)
    // student 2 (change all to pending, total 2)
    // student 3 (change all to pending, total 1)
    // student 4 (change all to pending, total 1)
    app()->make(LeaveApplicationService::class)
        ->setEmployee($employee)
        ->updateStatus([$leave_application3_student->id, $leave_application_student2->id, $leave_application2_student2->id, $leave_application3->id, $leave_application4->id], LeaveapplicationStatus::PENDING->value);

    // 5 approved -> pending, 2 approved
    $this->assertDatabaseCount($this->table, 7);
    $this->assertDatabaseHas($this->table, [
        'id' => $leave_application_student->id,
        'status' => LeaveApplicationStatus::APPROVED->value,
    ]);
    $this->assertDatabaseHas($this->table, [
        'id' => $leave_application2_student->id,
        'status' => LeaveApplicationStatus::APPROVED->value,
    ]);
    $this->assertDatabaseHas($this->table, [
        'id' => $leave_application3_student->id,
        'status' => LeaveApplicationStatus::PENDING->value,
    ]);
    $this->assertDatabaseHas($this->table, [
        'id' => $leave_application_student2->id,
        'status' => LeaveApplicationStatus::PENDING->value,
    ]);
    $this->assertDatabaseHas($this->table, [
        'id' => $leave_application2_student2->id,
        'status' => LeaveApplicationStatus::PENDING->value,
    ]);
    $this->assertDatabaseHas($this->table, [
        'id' => $leave_application3->id,
        'status' => LeaveApplicationStatus::PENDING->value,
    ]);
    $this->assertDatabaseHas($this->table, [
        'id' => $leave_application4->id,
        'status' => LeaveApplicationStatus::PENDING->value,
    ]);

    $this->assertDatabaseCount('attendance_period_override', 2);
    $this->assertDatabaseCount('attendance_period_override_leave_application', 3); // 3 approved (student 1)

    // student 1
    // not updated
    $this->assertDatabaseHas('attendance_period_override', [
        'attendance_recordable_type' => $attendance_period_override_02_11->attendance_recordable_type,
        'attendance_recordable_id' => $attendance_period_override_02_11->attendance_recordable_id,
        'period' => $attendance_period_override_02_11->period,
        'attendance_from' => $attendance_period_override_02_11->attendance_from,
        'attendance_to' => $attendance_period_override_02_11->attendance_to,
        'updated_by_employee_id' => $attendance_period_override_02_11->updated_by_employee_id,
    ]);
    $this->assertDatabaseHas('attendance_period_override_leave_application', [
        'leave_application_id' => $leave_application_student->id,
        'attendance_period_override_id' => $attendance_period_override_02_11->id,
    ]);

    $attendance_period_override_student = AttendancePeriodOverride::where('attendance_recordable_type', Student::class)
        ->where('attendance_recordable_id', $student->id)
        ->where('period', '2025-02-10')
        ->where('attendance_from', $period3->from_time) // updated, removed leave period 3
        ->where('attendance_to', $period4->to_time) // updated, removed leave period 3
        ->where('updated_by_employee_id', $employee->id)
        ->first();
    $this->assertDatabaseHas('attendance_period_override_leave_application', [
        'leave_application_id' => $leave_application_student->id,
        'attendance_period_override_id' => $attendance_period_override_student->id,
    ]);
    $this->assertDatabaseHas('attendance_period_override_leave_application', [
        'leave_application_id' => $leave_application2_student->id,
        'attendance_period_override_id' => $attendance_period_override_student->id,
    ]);

    // re-post after attendance_period_override being deleted
    $this->assertDatabaseCount('attendances', 3); // only 3 backdated leaves
    $this->assertDatabaseHas('attendances', [
        'attendance_recordable_type' => Student::class,
        'attendance_recordable_id' => $student->id,
        'date' => '2025-02-10',
        'check_in_datetime' => '2025-02-10 01:01:00',
        'check_in_status' => AttendanceCheckInStatus::LATE->value,
        'check_in_remarks' => null,
        'check_in_overridable_type' => get_class($attendance_period_override_student),
        'check_in_overridable_id' => $attendance_period_override_student->id,
        'attendance_from' => '2025-02-10 01:00:00', // UTC +8 = 9am ($period3->from_time)
        'check_out_datetime' => null,
        'check_out_status' => null,
        'check_out_remarks' => null,
        'check_out_overridable_type' => get_class($attendance_period_override_student),
        'check_out_overridable_id' => $attendance_period_override_student->id,
        'attendance_to' => '2025-02-10 02:30:00', // UTC +8 = 10:30am ($period4->to_time)
        'status' => AttendanceStatus::PRESENT->value,
        'card_id' => null,
        'leave_application_id' => null,
        'is_error' => false,
        'error_message' => null,
    ]);

    $this->assertDatabaseCount('period_attendances', 9);
    // just for reference
    // LeaveApplicationPeriod::factory()->create(['leave_application_id' => $leave_application3_student->id, 'date' => '2025-02-10', 'period_label_id' => $period_label3->id, 'period' => $period3->period]);
    // PeriodAttendance::factory()->create([ // period attendance for pending leave
    //     'student_id' => $student->id,
    //     'date' => '2025-02-10',
    //     'timeslot_id' => $timeslot3->id,
    //     'updated_by_employee_id' => $timeslot_teacher3->employee_id,
    //     'status' => PeriodAttendanceStatus::PRESENT->value
    // ]);
    $this->assertDatabaseHas('period_attendances', [
        'student_id' => $student->id,
        'date' => '2025-02-10',
        'timeslot_id' => $timeslot3->id,
        'updated_by_employee_id' => $timeslot_teacher3->employee_id,
        'status' => PeriodAttendanceStatus::PRESENT->value,
        'leave_application_id' => null,
    ]);
    // only student 1 tapped card
    $this->assertDatabaseHas('period_attendances', [
        'student_id' => $student->id,
        'date' => '2025-02-10',
        'timeslot_id' => $timeslot1->id,
        'updated_by_employee_id' => $this->systemEmployee->id,
        'status' => PeriodAttendanceStatus::LATE->value,
        'leave_application_id' => null,
    ]);
    $this->assertDatabaseHas('period_attendances', [
        'student_id' => $student->id,
        'date' => '2025-02-10',
        'timeslot_id' => $timeslot2->id,
        'updated_by_employee_id' => null,
        'status' => PeriodAttendanceStatus::PRESENT->value,
        'leave_application_id' => null,
    ]);
    $this->assertDatabaseHas('period_attendances', [
        'student_id' => $student->id,
        'date' => '2025-02-10',
        'timeslot_id' => $timeslot4->id,
        'updated_by_employee_id' => null,
        'status' => PeriodAttendanceStatus::PRESENT->value,
        'leave_application_id' => null,
    ]);
    // just for reference
    // LeaveApplicationPeriod::factory()->create(['leave_application_id' => $leave_application_student2->id, 'date' => '2025-02-10', 'period_label_id' => $period_label3->id, 'period' => $period3->period]);
    // PeriodAttendance::factory()->create([ // period attendance for pending leave
    //     'student_id' => $student2->id,
    //     'date' => '2025-02-10',
    //     'timeslot_id' => $timeslot3->id,
    //     'updated_by_employee_id' => $timeslot_teacher3->employee_id,
    //     'status' => PeriodAttendanceStatus::PRESENT->value
    // ]);
    // LeaveApplicationPeriod::factory()->create(['leave_application_id' => $leave_application2_student2->id, 'date' => '2025-02-10', 'period_label_id' => $period_label4->id, 'period' => $period4->period]);
    // PeriodAttendance::factory()->create([ // period attendance for pending leave
    //     'student_id' => $student2->id,
    //     'date' => '2025-02-10',
    //     'timeslot_id' => $timeslot4->id,
    //     'updated_by_employee_id' => $timeslot_teacher4->employee_id,
    //     'status' => PeriodAttendanceStatus::PRESENT->value
    // ]);
    $this->assertDatabaseHas('period_attendances', [
        'student_id' => $student2->id,
        'date' => '2025-02-10',
        'timeslot_id' => $timeslot3->id,
        'updated_by_employee_id' => $timeslot_teacher3->employee_id,
        'status' => PeriodAttendanceStatus::PRESENT->value,
        'leave_application_id' => null,
    ]);
    $this->assertDatabaseHas('period_attendances', [
        'student_id' => $student2->id,
        'date' => '2025-02-10',
        'timeslot_id' => $timeslot4->id,
        'updated_by_employee_id' => $timeslot_teacher4->employee_id,
        'status' => PeriodAttendanceStatus::PRESENT->value,
        'leave_application_id' => null,
    ]);
    // just for reference
    // LeaveApplicationPeriod::factory()->create(['leave_application_id' => $leave_application3->id, 'date' => '2025-02-03', 'period_label_id' => $period_label3->id, 'period' => $period3->period]);
    // PeriodAttendance::factory()->create([ // period attendance for pending leave
    //     'student_id' => $student3->id,
    //     'date' => '2025-02-03',
    //     'timeslot_id' => $timeslot3->id,
    //     'updated_by_employee_id' => $timeslot_teacher3->employee_id,
    //     'status' => PeriodAttendanceStatus::PRESENT->value
    // ]);
    // LeaveApplicationPeriod::factory()->create(['leave_application_id' => $leave_application3->id, 'date' => '2025-02-03', 'period_label_id' => $period_label4->id, 'period' => $period4->period]);
    // PeriodAttendance::factory()->create([ // period attendance for pending leave
    //     'student_id' => $student3->id,
    //     'date' => '2025-02-03',
    //     'timeslot_id' => $timeslot4->id,
    //     'updated_by_employee_id' => $timeslot_teacher4->employee_id,
    //     'status' => PeriodAttendanceStatus::PRESENT->value
    // ]);
    $this->assertDatabaseHas('period_attendances', [
        'student_id' => $student3->id,
        'date' => '2025-02-03',
        'timeslot_id' => $timeslot3->id,
        'updated_by_employee_id' => $timeslot_teacher3->employee_id,
        'status' => PeriodAttendanceStatus::PRESENT->value,
        'leave_application_id' => null,
    ]);
    $this->assertDatabaseHas('period_attendances', [
        'student_id' => $student3->id,
        'date' => '2025-02-03',
        'timeslot_id' => $timeslot4->id,
        'updated_by_employee_id' => $timeslot_teacher4->employee_id,
        'status' => PeriodAttendanceStatus::PRESENT->value,
        'leave_application_id' => null,
    ]);
    // just for reference
    // LeaveApplicationPeriod::factory()->create(['leave_application_id' => $leave_application4->id, 'date' => '2025-02-17', 'period_label_id' => $period_label3->id, 'period' => $period3->period]);
    // LeaveApplicationPeriod::factory()->create(['leave_application_id' => $leave_application4->id, 'date' => '2025-02-17', 'period_label_id' => $period_label4->id, 'period' => $period4->period]);
    // future date won't create period attendance (if somehow already exists, it will still be updated)
    $this->assertDatabaseHas('period_attendances', [
        'student_id' => $student4->id,
        'date' => '2025-02-17',
        'timeslot_id' => $timeslot3->id,
        'updated_by_employee_id' => $timeslot_teacher3->employee_id,
        'status' => PeriodAttendanceStatus::PRESENT->value,
        'leave_application_id' => null,
    ]);
    $this->assertDatabaseMissing('period_attendances', [
        'student_id' => $student4->id,
        'date' => '2025-02-17',
        'timeslot_id' => $timeslot4->id,
        'updated_by_employee_id' => null,
        'status' => PeriodAttendanceStatus::ABSENT->value,
        'leave_application_id' => null,
    ]);
});

test('create and update status to approved - weekend no timeslot', function () {
    $student = Student::factory()->create(['is_active' => true]);

    $period_group1 = PeriodGroup::factory()->create();
    $period1 = Period::factory()->create([
        'period_group_id' => $period_group1->id,
        'period' => 1,
        'from_time' => '08:00:00',
        'to_time' => '08:30:00',
        'day' => Day::SATURDAY,
        'display_group' => 1,
    ]);
    $period_label1 = PeriodLabel::factory()->create([
        'period_group_id' => $period_group1->id,
        'period' => 1,
        'can_apply_leave' => true,
    ]);

    StudentTimetable::refreshViewTable(false);

    config([
        'school.timezone' => 'Asia/Kuala_Lumpur'
    ]);
    Carbon::setTestNow('2025-04-27 00:00:00');

    $sick_leave = LeaveApplicationType::factory()->create([
        'name->en' => 'Sick Leave',
        'name->zh' => '病假',
    ]);

    $leave_application = $this->leaveApplicationService
        ->setLeaveApplicable($student)
        ->setLeaveApplicationType($sick_leave)
        ->setReason('test reason')
        ->setRemarks(null)
        ->setAveragePointDeduction(10)
        ->setConductPointDeduction(20)
        ->setFromDate('2025-04-27')
        ->setToDate('2025-04-27')
        ->setIsPresent(false)
        ->setPeriodLabelIds([$period_label1->id])
        ->setIsFullDay(false)
        ->createLeaveApplication();

    $employee = Employee::factory()->create();
    $employee->user->assignRole('Super Admin');
    Sanctum::actingAs($employee->user);

    app()->make(LeaveApplicationService::class)
        ->setEmployee($employee)
        ->updateStatus([$leave_application->id], LeaveapplicationStatus::APPROVED->value);

    $leave_application->refresh();
    expect($leave_application->status)->toBe(LeaveapplicationStatus::APPROVED);
});

test('leaves relationship', function () {

    $student = Student::factory()->create([]);
    $employee = Employee::factory()->create([]);

    $leave_application1 = LeaveApplication::factory()->create([
        'leave_applicable_type' => Student::class,
        'leave_applicable_id' => $student->id,
        'status' => LeaveApplicationStatus::PENDING->value,
    ]);
    $leave_application2 = LeaveApplication::factory()->create([
        'leave_applicable_type' => Student::class,
        'leave_applicable_id' => $student->id,
        'status' => LeaveApplicationStatus::PENDING->value,
    ]);
    $leave_application3 = LeaveApplication::factory()->create([
        'leave_applicable_type' => Employee::class,
        'leave_applicable_id' => $employee->id,
        'status' => LeaveApplicationStatus::PENDING->value,
    ]);
    $leave_application4 = LeaveApplication::factory()->create([
        'leave_applicable_type' => Employee::class,
        'leave_applicable_id' => $employee->id,
        'status' => LeaveApplicationStatus::PENDING->value,
    ]);

    expect($student->leaves)->toHaveCount(2)
        ->and($student->leaves->pluck('id'))->toContain($leave_application1->id, $leave_application2->id);

    expect($employee->leaves)->toHaveCount(2)
        ->and($employee->leaves->pluck('id'))->toContain($leave_application3->id, $leave_application4->id);
});

test('determineIsFullDay', function () {
    $period_group = PeriodGroup::factory()->create(['number_of_periods' => 3]);

    expect($this->leaveApplicationService->determineIsFullDay($period_group, [1, 2]))->toBeFalse();
    expect($this->leaveApplicationService->determineIsFullDay($period_group, [1, 2, 3]))->toBeTrue();
});

test('validateStatusChange', function () {
    $student1 = Student::factory()->create();
    $student2 = Student::factory()->create();
    $employee = Employee::factory()->create();

    $leave_application_student1 = LeaveApplication::factory()->create([
        'leave_applicable_type' => Student::class,
        'leave_applicable_id' => $student1->id,
        'status' => LeaveApplicationStatus::PENDING->value,
    ]);
    LeaveApplicationPeriod::factory()->create([
        'leave_application_id' => $leave_application_student1->id,
        'date' => '2025-01-27',
    ]);
    LeaveApplicationPeriod::factory()->create([
        'leave_application_id' => $leave_application_student1->id,
        'date' => '2025-01-28',
    ]);
    $attendance_period_override_student1_0127 = AttendancePeriodOverride::factory()->create([
        'attendance_recordable_type' => Student::class,
        'attendance_recordable_id' => $student1->id,
        'period' => '2025-01-27',
    ]);
    $attendance_period_override_student1_0128 = AttendancePeriodOverride::factory()->create([
        'attendance_recordable_type' => Student::class,
        'attendance_recordable_id' => $student1->id,
        'period' => '2025-01-28',
    ]);
    $leave_application2_student1 = LeaveApplication::factory()->create([
        'leave_applicable_type' => Student::class,
        'leave_applicable_id' => $student1->id,
        'status' => LeaveApplicationStatus::PENDING->value,
    ]);
    LeaveApplicationPeriod::factory()->create([
        'leave_application_id' => $leave_application2_student1->id,
        'date' => '2025-01-29',
    ]);
    $attendance_period_override_student1_0129 = AttendancePeriodOverride::factory()->create([
        'attendance_recordable_type' => Student::class,
        'attendance_recordable_id' => $student1->id,
        'period' => '2025-01-29',
    ]);
    AttendancePeriodOverrideLeaveApplication::factory()->create([
        'attendance_period_override_id' => $attendance_period_override_student1_0129->id,
    ]);
    $leave_application_student2 = LeaveApplication::factory()->create([
        'leave_applicable_type' => Student::class,
        'leave_applicable_id' => $student2->id,
        'status' => LeaveApplicationStatus::REJECTED->value,
    ]);
    LeaveApplicationPeriod::factory()->create([
        'leave_application_id' => $leave_application_student2->id,
        'date' => '2025-01-28',
    ]);

    $leave_application_student3 = LeaveApplication::factory()->create([
        'leave_applicable_type' => Student::class,
        'leave_applicable_id' => $student2->id,
        'status' => LeaveApplicationStatus::PENDING->value,
    ]);
    LeaveApplicationPeriod::factory()->create([
        'leave_application_id' => $leave_application_student3->id,
        'date' => '2025-01-30',
    ]);

    $leave_application_employee = LeaveApplication::factory()->create([
        'leave_applicable_type' => Employee::class,
        'leave_applicable_id' => $employee->id,
        'status' => LeaveApplicationStatus::PENDING->value,
    ]);

    $new_status = LeaveApplicationStatus::APPROVED->value;
    $result = $this->leaveApplicationService->validateStatusChange([$leave_application_student1->id, $leave_application2_student1->id, $leave_application_student2->id, $leave_application_student3->id, $leave_application_employee->id], $new_status);

    expect($result)->toHaveCount(5)
        ->and($result[0]['leave_application']->id)->toBe($leave_application_student1->id)
        ->and($result[0]['status'])->toBeFalse()
        ->and($result[0]['error_messages'])->toContain(
            "Found existing attendance period override that was manually created on 2025-01-27, please delete it first.",
            "Found existing attendance period override that was manually created on 2025-01-28, please delete it first.",
        )
        ->and($result[0]['attendance_period_overrides']->pluck('id'))->toContain($attendance_period_override_student1_0127->id, $attendance_period_override_student1_0128->id)
        ->and($result[1]['leave_application']->id)->toBe($leave_application2_student1->id)
        ->and($result[1]['status'])->toBeTrue()
        ->and($result[1]['error_messages'])->toBeEmpty()
        ->and($result[1]['attendance_period_overrides'])->toBeEmpty()
        ->and($result[2]['leave_application']->id)->toBe($leave_application_student2->id)
        ->and($result[2]['status'])->toBeFalse()
        ->and($result[2]['error_messages'])->toContain('Please change leave application status to PENDING first.')
        ->and($result[2]['attendance_period_overrides'])->toBeEmpty()
        ->and($result[3]['leave_application']->id)->toBe($leave_application_student3->id)
        ->and($result[3]['status'])->toBeTrue()
        ->and($result[3]['error_messages'])->toBeEmpty()
        ->and($result[3]['attendance_period_overrides'])->toBeEmpty()
        ->and($result[4]['leave_application']->id)->toBe($leave_application_employee->id)
        ->and($result[4]['status'])->toBeTrue()
        ->and($result[4]['error_messages'])->toBeEmpty()
        ->and($result[4]['attendance_period_overrides'])->toBeEmpty();

});

test('shouldCreateIndividualOverride', function () {
    $leave_application_student1 = LeaveApplication::factory()->create([
        'leave_applicable_type' => Student::class,
        'leave_applicable_id' => Student::factory()->create()->id,
        'status' => LeaveApplicationStatus::PENDING->value,
        'is_full_day' => false,
    ]);
    expect($this->leaveApplicationService->shouldCreateIndividualOverride($leave_application_student1, LeaveApplicationStatus::APPROVED->value))->toBeTrue();
    // approved to approved
    $leave_application_student3 = LeaveApplication::factory()->create([
        'leave_applicable_type' => Student::class,
        'leave_applicable_id' => Student::factory()->create()->id,
        'status' => LeaveApplicationStatus::APPROVED->value,
        'is_full_day' => false,
    ]);
    // employee
    expect($this->leaveApplicationService->shouldCreateIndividualOverride($leave_application_student3, LeaveApplicationStatus::APPROVED->value))->toBeFalse();
    $leave_application_employee = LeaveApplication::factory()->create([
        'leave_applicable_type' => Employee::class,
        'leave_applicable_id' => Employee::factory()->create()->id,
        'status' => LeaveApplicationStatus::PENDING->value,
        'is_full_day' => false,
    ]);
    expect($this->leaveApplicationService->shouldCreateIndividualOverride($leave_application_employee, LeaveApplicationStatus::APPROVED->value))->toBeFalse();
});

test('validateLeaveApplicationIsEditable approved leave', function () {
    $leave_application_student = LeaveApplication::factory()->create([
        'leave_applicable_type' => Student::class,
        'leave_applicable_id' => Student::factory()->create()->id,
        'status' => LeaveApplicationStatus::APPROVED->value,
    ]);
    $this->expectExceptionCode(50006);
    $this->expectExceptionMessage('Not allowed to edit approved leave application.');
    expect($this->leaveApplicationService->validateLeaveApplicationIsEditable($leave_application_student));
});

test('validateLeaveApplicationIsEditable pending leave', function () {
    $leave_application_student = LeaveApplication::factory()->create([
        'leave_applicable_type' => Student::class,
        'leave_applicable_id' => Student::factory()->create()->id,
        'status' => LeaveApplicationStatus::PENDING->value,
    ]);
    expect($this->leaveApplicationService->validateLeaveApplicationIsEditable($leave_application_student))->toBeTrue();
});

test('getLeaveApplicationHistory', function () {
    $student = Student::factory()->create();
    // 03/16 approved = 1 period
    // 03/20 rejected = 1,2,3 period
    // 03/20 pending  = 1,2,3 period
    // period attendances
    // 03/20 absent   = 1,2 period
    // 03/20 late     = 3 period
    // expected output (leave applications only)
    // 03/16 approved = 1 period
    // 03/20 rejected = 1,2,3 period
    // 03/20 pending  = 1,2,3 period

    // period attendances
    $timeslot_period1 = Timeslot::factory()->create([
        'period_id' => Period::factory()->create([
            'period' => 1,
        ])->id,
    ]);
    $timeslot_period2 = Timeslot::factory()->create([
        'period_id' => Period::factory()->create([
            'period' => 2,
        ])->id,
    ]);
    $timeslot_override_period3 = TimeslotOverride::factory()->create([
        'student_id' => $student->id,
        'date' => '2025-03-20',
        'period' => 3,
        'class_attendance_required' => true,
    ]);

    $leave_application_2025_03_16 = LeaveApplication::factory()->create([
        'leave_applicable_type' => Student::class,
        'leave_applicable_id' => $student->id,
        'status' => LeaveApplicationStatus::APPROVED,
        'reason' => 'c',
    ]);
    LeaveApplicationPeriod::factory()->create([
        'leave_application_id' => $leave_application_2025_03_16->id,
        'date' => '2025-03-16',
        'period' => 1,
    ]);
    PeriodAttendance::factory()->create([
        'student_id' => $student->id,
        'status' => PeriodAttendanceStatus::ABSENT->value,
        'leave_application_id' => $leave_application_2025_03_16->id,
        'date' => '2025-03-16',
        'timeslot_id' => $timeslot_period1->id,
        'period' => $timeslot_period1->period->period,
    ]);
    $leave_application_2025_03_20_rejected = LeaveApplication::factory()->create([
        'leave_applicable_type' => Student::class,
        'leave_applicable_id' => $student->id,
        'status' => LeaveApplicationStatus::REJECTED,
        'reason' => 'b',
    ]);
    LeaveApplicationPeriod::factory()->create([
        'leave_application_id' => $leave_application_2025_03_20_rejected->id,
        'date' => '2025-03-20',
        'period' => 1,
    ]);
    LeaveApplicationPeriod::factory()->create([
        'leave_application_id' => $leave_application_2025_03_20_rejected->id,
        'date' => '2025-03-20',
        'period' => 2,
    ]);
    LeaveApplicationPeriod::factory()->create([
        'leave_application_id' => $leave_application_2025_03_20_rejected->id,
        'date' => '2025-03-20',
        'period' => 3,
    ]);
    $leave_application_2025_03_20_approved = LeaveApplication::factory()->create([
        'leave_applicable_type' => Student::class,
        'leave_applicable_id' => $student->id,
        'status' => LeaveApplicationStatus::APPROVED,
        'reason' => 'a',
    ]);
    LeaveApplicationPeriod::factory()->create([
        'leave_application_id' => $leave_application_2025_03_20_approved->id,
        'date' => '2025-03-20',
        'period' => 1,
    ]);
    LeaveApplicationPeriod::factory()->create([
        'leave_application_id' => $leave_application_2025_03_20_approved->id,
        'date' => '2025-03-20',
        'period' => 2,
    ]);
    $leave_application_2025_03_20_approved_period_3 = LeaveApplicationPeriod::factory()->create([
        'leave_application_id' => $leave_application_2025_03_20_approved->id,
        'date' => '2025-03-20',
        'period' => 3,
    ]);

    PeriodAttendance::factory()->create([
        'student_id' => $student->id,
        'status' => PeriodAttendanceStatus::ABSENT->value,
        'leave_application_id' => $leave_application_2025_03_20_approved->id,
        'date' => '2025-03-20',
        'timeslot_id' => $timeslot_period1->id,
        'period' => $timeslot_period1->period->period,
    ]);
    PeriodAttendance::factory()->create([
        'student_id' => $student->id,
        'status' => PeriodAttendanceStatus::ABSENT->value,
        'leave_application_id' => $leave_application_2025_03_20_approved->id,
        'date' => '2025-03-20',
        'timeslot_id' => $timeslot_period2->id,
        'period' => $timeslot_period2->period->period,
    ]);
    $period_attendance_2025_03_20_period_3 = PeriodAttendance::factory()->create([
        'student_id' => $student->id,
        'status' => PeriodAttendanceStatus::LATE->value,
        'leave_application_id' => $leave_application_2025_03_20_approved->id,
        'date' => '2025-03-20',
        'timeslot_id' => $timeslot_override_period3->id,
        'period' => $timeslot_override_period3->period,
    ]);

    $data = $this->leaveApplicationService->getLeaveApplicationHistory(Student::class, $student->id);
    expect($data)->toMatchArray([
        [
            'date' => '2025-03-20',
            'leave_application_type' => $leave_application_2025_03_20_approved->leaveApplicationType->getTranslations('name'),
            'periods' => [1, 2, 3],
            'leave_application_id' => $leave_application_2025_03_20_approved->id,
            'reason' => $leave_application_2025_03_20_approved->reason,
            'status' => $leave_application_2025_03_20_approved->status->value,
        ],
        [
            'date' => '2025-03-20',
            'leave_application_type' => $leave_application_2025_03_20_rejected->leaveApplicationType->getTranslations('name'),
            'periods' => [1, 2, 3],
            'leave_application_id' => $leave_application_2025_03_20_rejected->id,
            'reason' => $leave_application_2025_03_20_rejected->reason,
            'status' => $leave_application_2025_03_20_rejected->status->value,
        ],
        [
            'date' => '2025-03-16',
            'leave_application_type' => $leave_application_2025_03_16->leaveApplicationType->getTranslations('name'),
            'periods' => [1],
            'leave_application_id' => $leave_application_2025_03_16->id,
            'reason' => $leave_application_2025_03_16->reason,
            'status' => $leave_application_2025_03_16->status->value,
        ],
    ]);

    // remove period 3 on 2025_03_20 approved leave
    $leave_application_2025_03_20_approved_period_3->delete();
    $period_attendance_2025_03_20_period_3->update([
        'leave_application_id' => null
    ]);
    $data = $this->leaveApplicationService->getLeaveApplicationHistory(Student::class, $student->id);
    expect($data)->toMatchArray([
        [
            'date' => '2025-03-20',
            'leave_application_type' => $leave_application_2025_03_20_approved->leaveApplicationType->getTranslations('name'),
            'periods' => [1, 2], // removed 3
            'leave_application_id' => $leave_application_2025_03_20_approved->id,
            'reason' => $leave_application_2025_03_20_approved->reason,
            'status' => $leave_application_2025_03_20_approved->status->value,
        ],
        [
            'date' => '2025-03-20',
            'leave_application_type' => $leave_application_2025_03_20_rejected->leaveApplicationType->getTranslations('name'),
            'periods' => [1, 2, 3],
            'leave_application_id' => $leave_application_2025_03_20_rejected->id,
            'reason' => $leave_application_2025_03_20_rejected->reason,
            'status' => $leave_application_2025_03_20_rejected->status->value,
        ],
        // period attendance
        [
            'date' => '2025-03-20',
            'leave_application_type' => PeriodAttendanceStatus::getPeriodAttendanceTranslation(PeriodAttendanceStatus::LATE->value),
            'periods' => [3], // timeslot override
            'leave_application_id' => null,
            'reason' => null,
            'status' => null,
        ],
        [
            'date' => '2025-03-16',
            'leave_application_type' => $leave_application_2025_03_16->leaveApplicationType->getTranslations('name'),
            'periods' => [1],
            'leave_application_id' => $leave_application_2025_03_16->id,
            'reason' => $leave_application_2025_03_16->reason,
            'status' => $leave_application_2025_03_16->status->value,
        ],
    ]);
});

test('getLeaveApplicationHistory - period attendance has_mark_deduction = false', function () {
    $student = Student::factory()->create();

    // period attendances
    $timeslot_period1 = Timeslot::factory()->create([
        'period_id' => Period::factory()->create([
            'period' => 1,
        ])->id,
        'has_mark_deduction' => true,
    ]);
    $timeslot_period2 = Timeslot::factory()->create([
        'period_id' => Period::factory()->create([
            'period' => 2,
        ])->id,
        'has_mark_deduction' => false,
    ]);
    $timeslot_period3 = Timeslot::factory()->create([
        'period_id' => Period::factory()->create([
            'period' => 3,
        ])->id,
        'has_mark_deduction' => false,
    ]);

    PeriodAttendance::factory()->create([
        'student_id' => $student->id,
        'status' => PeriodAttendanceStatus::ABSENT->value,
        'leave_application_id' => null,
        'date' => '2025-03-16',
        'timeslot_type' => Timeslot::class,
        'timeslot_id' => $timeslot_period1->id,
        'period' => $timeslot_period1->period->period,
        'has_mark_deduction' => true,
    ]);
    PeriodAttendance::factory()->create([
        'student_id' => $student->id,
        'status' => PeriodAttendanceStatus::ABSENT->value,
        'leave_application_id' => null,
        'date' => '2025-03-16',
        'timeslot_type' => Timeslot::class,
        'timeslot_id' => $timeslot_period2->id,
        'period' => $timeslot_period2->period->period,
        'has_mark_deduction' => false,
    ]);
    PeriodAttendance::factory()->create([
        'student_id' => $student->id,
        'status' => PeriodAttendanceStatus::LATE->value,
        'leave_application_id' => null,
        'date' => '2025-03-16',
        'timeslot_type' => Timeslot::class,
        'timeslot_id' => $timeslot_period3->id,
        'period' => $timeslot_period3->period->period,
        'has_mark_deduction' => false,
    ]);

    $data = $this->leaveApplicationService->getLeaveApplicationHistory(Student::class, $student->id);
    expect($data)->toMatchArray([
        [
            'date' => '2025-03-16',
            'leave_application_type' => PeriodAttendanceStatus::getPeriodAttendanceTranslation(PeriodAttendanceStatus::ABSENT->value),
            'periods' => [1], // hide 2,3 because has_mark_deduction = false
            'leave_application_id' => null,
            'reason' => null,
            'status' => null,
        ],
    ]);
});

test('validateLeaveApplicationPeriodExistence', function () {
    $student = Student::factory()->create();

    $period_group = PeriodGroup::factory()->create(['number_of_periods' => 4]);
    $period_label1 = PeriodLabel::factory()->create([
        'period_group_id' => $period_group->id,
        'period' => 1,
    ]);
    $period_label2 = PeriodLabel::factory()->create([
        'period_group_id' => $period_group->id,
        'period' => 2,
    ]);
    $period_label3 = PeriodLabel::factory()->create([
        'period_group_id' => $period_group->id,
        'period' => 3,
    ]);
    $period_label4 = PeriodLabel::factory()->create([
        'period_group_id' => $period_group->id,
        'period' => 4,
    ]);

    // 2025-03-16 1,2
    // 2025-03-17 4
    $leave_application_pending_03_16 = LeaveApplication::factory()->create([
        'leave_applicable_type' => Student::class,
        'leave_applicable_id' => $student->id,
        'status' => LeaveApplicationStatus::PENDING,
    ]);
    LeaveApplicationPeriod::factory()->create([
        'leave_application_id' => $leave_application_pending_03_16->id,
        'date' => '2025-03-16',
        'period' => $period_label1->period,
    ]);
    LeaveApplicationPeriod::factory()->create([
        'leave_application_id' => $leave_application_pending_03_16->id,
        'date' => '2025-03-16',
        'period' => $period_label2->period,
    ]);

    $leave_application_pending_03_17 = LeaveApplication::factory()->create([
        'leave_applicable_type' => Student::class,
        'leave_applicable_id' => $student->id,
        'status' => LeaveApplicationStatus::PENDING,
    ]);
    LeaveApplicationPeriod::factory()->create([
        'leave_application_id' => $leave_application_pending_03_17->id,
        'date' => '2025-03-17',
        'period' => $period_label4->period,
    ]);

    // update existing pending leave application
    // 2025-03-16 1,2 -> // 2025-03-16 + 2025-03-17 1,2,3 (won't throw error)
    $this->leaveApplicationService
        ->setLeaveApplication($leave_application_pending_03_16)
        ->setLeaveApplicable($leave_application_pending_03_16->leaveApplicable)
        ->setFromDate('2025-03-16')
        ->setToDate('2025-03-17')
        ->setPeriodLabelIds([$period_label1->id, $period_label2->id, $period_label3->id])
        ->validateLeaveApplicationPeriodExistence(false);

    // 2025-03-16 1,2 -> // 2025-03-16 + 2025-03-17 1,2,3,4 (throw error)
    $status = false;
    try {
        $this->leaveApplicationService
            ->setLeaveApplication($leave_application_pending_03_16)
            ->setLeaveApplicable($leave_application_pending_03_16->leaveApplicable)
            ->setFromDate('2025-03-16')
            ->setToDate('2025-03-17')
            ->setPeriodLabelIds([$period_label1->id, $period_label2->id, $period_label3->id, $period_label4->id])
            ->validateLeaveApplicationPeriodExistence(false);
        expect($status)->toBeTrue();
    } catch (\Exception $e) {
        expect($e->getMessage())->toBe("Creation or update is not allowed for student {$student->getUserNumber()} as a leave application already exists for the same period. (2025-03-16 - 2025-03-17)")
            ->and($e->getCode())->toBe(50008);
    }

    // 2025-03-16 1,2 ->  2025-03-16 1,2,4 (won't throw error)
    $this->leaveApplicationService
        ->setLeaveApplicable($student)
        ->setFromDate('2025-03-16')
        ->setToDate('2025-03-16')
        ->setPeriodLabelIds([$period_label4->id])
        ->validateLeaveApplicationPeriodExistence(true);

    // set 03/17 leave status to rejected, then validate again
    $leave_application_pending_03_17->update([
        'status' => LeaveApplicationStatus::REJECTED,
    ]);
    // (won't throw error)
    $this->leaveApplicationService
        ->setLeaveApplication($leave_application_pending_03_16)
        ->setLeaveApplicable($leave_application_pending_03_16->leaveApplicable)
        ->setFromDate('2025-03-16')
        ->setToDate('2025-03-17')
        ->setPeriodLabelIds([$period_label1->id, $period_label2->id, $period_label3->id, $period_label4->id])
        ->validateLeaveApplicationPeriodExistence(false);


    // create new leave on 2025-03-16, period 1 (throw error)
    $status = false;
    try {
        $this->leaveApplicationService
            ->setLeaveApplicable($student)
            ->setFromDate('2025-03-16')
            ->setToDate('2025-03-16')
            ->setPeriodLabelIds([$period_label1->id])
            ->validateLeaveApplicationPeriodExistence(true);
        expect($status)->toBeTrue();
    } catch (\Exception $e) {
        expect($e->getMessage())->toBe("Creation or update is not allowed for student {$student->getUserNumber()} as a leave application already exists for the same period. (2025-03-16 - 2025-03-16)")
            ->and($e->getCode())->toBe(50008);
    }

    // create new leave on 2025-03-16, period 3,4 (won't throw error)
    $this->leaveApplicationService
        ->setLeaveApplicable($student)
        ->setFromDate('2025-03-16')
        ->setToDate('2025-03-16')
        ->setPeriodLabelIds([$period_label3->id, $period_label4->id])
        ->validateLeaveApplicationPeriodExistence(true);

    // create new leave on 2025-03-16 for another student, period 1 (won't throw error)
    $student2 = Student::factory()->create();
    $this->leaveApplicationService
        ->setLeaveApplicable($student2)
        ->setFromDate('2025-03-16')
        ->setToDate('2025-03-16')
        ->setPeriodLabelIds([$period_label1->id])
        ->validateLeaveApplicationPeriodExistence(true);
});
