<?php

use App\Enums\ClassType;
use App\Enums\ExportType;
use App\Enums\Gender;
use App\Enums\GuardianType;
use App\Enums\HostelBlockType;
use App\Enums\HostelInOutType;
use App\Enums\HostelMeritDemeritType;
use App\Enums\HostelRoomBedStatus;
use App\Enums\HostelRoomGender;
use App\Exports\GenericExcelExportViaView;
use App\Models\Card;
use App\Models\ClassModel;
use App\Models\Employee;
use App\Models\Grade;
use App\Models\GuardianStudent;
use App\Models\HostelBedAssignment;
use App\Models\HostelBlock;
use App\Models\HostelInOutRecord;
use App\Models\HostelMeritDemeritSetting;
use App\Models\HostelRewardPunishmentRecord;
use App\Models\HostelRewardPunishmentSetting;
use App\Models\HostelRoom;
use App\Models\HostelRoomBed;
use App\Models\Media;
use App\Models\SemesterClass;
use App\Models\SemesterSetting;
use App\Models\SemesterYearSetting;
use App\Models\Student;
use App\Models\StudentClass;
use App\Models\User;
use App\Services\DocumentPrintService;
use App\Services\ReportPrintService;
use App\Services\Reports\HostelReportService;
use Barryvdh\Snappy\Facades\SnappyPdf;
use Carbon\Carbon;
use Database\Seeders\InternationalizationSeeder;
use Illuminate\Database\Eloquent\Factories\Sequence;
use Maatwebsite\Excel\Facades\Excel;
use Mockery\MockInterface;

beforeEach(function () {
    $this->seed(InternationalizationSeeder::class);
});

test('getHostelBoarderListReportData', function () {
    $sem1 = SemesterSetting::factory()->create([
        'name' => 'Semester 1'
    ]);

    $sem2 = SemesterSetting::factory()->create([
        'name' => 'Semester 2'
    ]);

    $grade1 = Grade::factory()->create([
        'name->en' => 'Junior 1'
    ]);

    $grade2 = Grade::factory()->create([
        'name->en' => 'Junior 2'
    ]);

    $classes = ClassModel::factory(2)->create(new Sequence(
        [
            'name->en' => 'J111',
            'type' => ClassType::PRIMARY,
            'grade_id' => $grade1->id
        ],
        [
            'name->en' => 'J211',
            'type' => ClassType::PRIMARY,
            'grade_id' => $grade2->id
        ],
    ));

    $semester_classes = SemesterClass::factory(4)->create(new Sequence(
        [
            'semester_setting_id' => $sem1->id,
            'class_id' => $classes[0]->id // Grade 1
        ],
        [
            'semester_setting_id' => $sem1->id,
            'class_id' => $classes[1]->id // Grade 2
        ],
        [
            'semester_setting_id' => $sem2->id,
            'class_id' => $classes[0]->id // Grade 1
        ],
        [
            'semester_setting_id' => $sem2->id,
            'class_id' => $classes[1]->id // Grade 2
        ],
    ));

    $students[0] = Student::factory()->create([
        'is_hostel' => true,
        'student_number' => 'A120',
        'name->en' => 'Aaron'
    ]);
    $students[1] = Student::factory()->create([
        'is_hostel' => true,
        'student_number' => 'A121',
        'name->en' => 'Baby'
    ]);

    StudentClass::factory()->create([
        'semester_setting_id' => $sem1->id,
        'semester_class_id' => $semester_classes[0]->id, // Grade 1
        'student_id' => $students[0]->id,
        'class_enter_date' => now()->toDateString(),
    ]);

    StudentClass::factory()->create([
        'semester_setting_id' => $sem2->id,
        'semester_class_id' => $semester_classes[3]->id, // Grade 2
        'student_id' => $students[0]->id,
        'class_enter_date' => now()->subDays(5)->toDateString()
    ]);

    StudentClass::factory()->create([
        'semester_setting_id' => $sem1->id,
        'semester_class_id' => $semester_classes[1]->id, // Grade 2
        'student_id' => $students[1]->id,
        'class_enter_date' => now()->subDays(10)->toDateString()
    ]);

    $hostel_block_A = HostelBlock::factory()->create([
        'name->en' => 'Block A',
        'type' => HostelBlockType::STUDENT->value,
    ]);

    $hostel_block_B = HostelBlock::factory()->create([
        'name->en' => 'Block B',
        'type' => HostelBlockType::STUDENT->value,
    ]);

    $hostel_room1 = HostelRoom::factory()->create([
        'name' => 'Room 1',
        'hostel_block_id' => $hostel_block_A->id,
    ]);

    $hostel_room2 = HostelRoom::factory()->create([
        'name' => 'Room 2',
        'hostel_block_id' => $hostel_block_B->id,
    ]);

    $hostel_bed1 = HostelRoomBed::factory()->create([
        'name' => 'Bed 1',
        'hostel_room_id' => $hostel_room1->id,
    ]);

    $hostel_bed2 = HostelRoomBed::factory()->create([
        'name' => 'Bed 2',
        'hostel_room_id' => $hostel_room2->id,
    ]);

    HostelBedAssignment::factory()->create([
        'hostel_room_bed_id' => $hostel_bed1->id,
        'assignable_id' => $students[0]->id,
        'assignable_type' => Student::class,
        'start_date' => now()->subYear()->toDateString(),
        'end_date' => now()->toDateString()
    ]);

    HostelBedAssignment::factory()->create([
        'hostel_room_bed_id' => $hostel_bed2->id,
        'assignable_id' => $students[0]->id,
        'assignable_type' => Student::class,
        'end_date' => null
    ]);

    HostelBedAssignment::factory()->create([
        'hostel_room_bed_id' => $hostel_bed2->id,
        'assignable_id' => $students[1]->id,
        'assignable_type' => Student::class,
        'start_date' => now()->subYear()->toDateString(),
        'end_date' => now()->toDateString()
    ]);

    HostelBedAssignment::factory()->create([
        'hostel_room_bed_id' => $hostel_bed1->id,
        'assignable_id' => $students[1]->id,
        'assignable_type' => Student::class,
        'start_date' => now()->subYear()->toDateString(),
        'end_date' => null
    ]);

    /**
     * Prepared data
     * $student[0] belongs in Sem 1, Class J111 and Sem 2, Class J211
     * $student[1] belongs in Sem 1, Class J211
     */

    $this->partialMock(ReportPrintService::class, function (MockInterface $mock) {
        $mock->shouldReceive('upload')->twice()->andReturnSelf();
        $mock->shouldReceive('getFileUrl')->twice()->andReturn('some-random-url');
    });

    $hostelReportService = resolve(HostelReportService::class);

    $payload = [
        'report_language' => 'en',
        'semester_setting_id' => $sem1->id
    ];

    $file_name = 'hostels-report-by-boarders-name-list-unit-test';

    $expected_headers = ['No.', 'Room', 'Bed No.', 'Student Number', 'Name (en)', 'Name (zh)', 'Grade'];

    // Test Excel
    Excel::fake();

    $view_name = 'reports.hostels.by-boarders-name-list';

    $hostelReportService
        ->setExportType(ExportType::EXCEL->value)
        ->setReportViewName($view_name)
        ->setFileName('hostels-report-by-boarders-name-list-unit-test')
        ->getHostelBoarderListReportData($payload);

    Excel::assertStored(
        DocumentPrintService::PRINT_FOLDER . DIRECTORY_SEPARATOR . $file_name . '.xlsx',
        'local',
        function (GenericExcelExportViaView $export) use ($expected_headers, $view_name, $students) {

            expect($export->view()->name())->toBe($view_name);

            $view = $this->view($export->view()->name(), $export->view()->getData());

            $view->assertSee(__('hostel.title.boarders_list'));

            // Add th tag to the headers
            $expected_headers = array_map(function ($header) {
                return "<th>$header</th>";
            }, $expected_headers);

            $view->assertSeeInOrder($expected_headers, false);

            $view->assertSeeInOrder([
                '<td>1</td>',
                '<td>Room 1</td>',
                '<td>Bed 1</td>',
                '<td>' . $students[1]->student_number . '</td>',
                '<td>' . e($students[1]->getTranslation('name', 'en')) . '</td>',
                '<td>' . $students[1]->getTranslation('name', 'zh') . '</td>',
                '<td>Junior 2</td>',
                '<td>J211</td>'
            ], false);

            $view->assertSeeInOrder([
                '<td>2</td>',
                '<td>Room 2</td>',
                '<td>Bed 2</td>',
                '<td>' . $students[0]->student_number . '</td>',
                '<td>' . e($students[0]->getTranslation('name', 'en')) . '</td>',
                '<td>' . $students[0]->getTranslation('name', 'zh') . '</td>',
                '<td>Junior 1</td>',
                '<td>J111</td>'
            ], false);

            return true;
        }
    );


    // Test PDF
    SnappyPdf::fake();

    $hostelReportService
        ->setExportType(ExportType::PDF->value)
        ->setReportViewName($view_name)
        ->setFileName('hostels-report-by-boarders-name-list-unit-test')
        ->getHostelBoarderListReportData($payload);

    SnappyPdf::assertFileNameIs(storage_path('app' . DIRECTORY_SEPARATOR . DocumentPrintService::PRINT_FOLDER . DIRECTORY_SEPARATOR . $file_name . '.pdf'));
    SnappyPdf::assertViewIs('reports.hostels.by-boarders-name-list');

    foreach ($expected_headers as $header) {
        SnappyPdf::assertSee($header);
    }

    SnappyPdf::assertSee(__('hostel.title.boarders_list'));

    SnappyPdf::assertSee('Room 2');
    SnappyPdf::assertSee('Bed 2');
    SnappyPdf::assertSee($students[1]->student_number);
    SnappyPdf::assertSee(e($students[1]->getTranslation('name', 'en')));
    SnappyPdf::assertSee(e($students[1]->getTranslation('name', 'zh')));
    SnappyPdf::assertSee('Junior 2');
    SnappyPdf::assertSee('J211');

    SnappyPdf::assertSee('Room 1');
    SnappyPdf::assertSee('Bed 1');
    SnappyPdf::assertSee($students[0]->student_number);
    SnappyPdf::assertSee(e($students[0]->getTranslation('name', 'en')));
    SnappyPdf::assertSee(e($students[0]->getTranslation('name', 'zh')));
    SnappyPdf::assertSee('Junior 1');
    SnappyPdf::assertSee('J111');
});

test('getHostelBoarderListReportData, sorted correctly by block, room, bed', function () {
    $sem1 = SemesterSetting::factory()->create([
        'name' => 'Semester 1'
    ]);

    $grades = Grade::factory(2)->state(new Sequence(
        [
            'name->en' => 'Junior 1'
        ],
        [
            'name->en' => 'Junior 2'
        ],
    ))->create();

    $classes = ClassModel::factory(2)->create(new Sequence(
        [
            'name->en' => 'J111',
            'type' => ClassType::PRIMARY,
            'grade_id' => $grades[0]->id
        ],
        [
            'name->en' => 'J211',
            'type' => ClassType::PRIMARY,
            'grade_id' => $grades[1]->id
        ],
    ));

    $semester_classes = SemesterClass::factory(2)->create(new Sequence(
        [
            'semester_setting_id' => $sem1->id,
            'class_id' => $classes[0]->id // Grade 1
        ],
        [
            'semester_setting_id' => $sem1->id,
            'class_id' => $classes[1]->id // Grade 2
        ],
    ));

    $students = Student::factory(5)->create(new Sequence(
        [
            'is_hostel' => true,
            'student_number' => 'A120',
            'name->en' => 'Aaron'
        ],
        [
            'is_hostel' => true,
            'student_number' => 'A121',
            'name->en' => 'Ben'
        ],
        [
            'is_hostel' => true,
            'student_number' => 'A122',
            'name->en' => 'Cathy'
        ],
        [
            'is_hostel' => true,
            'student_number' => 'A123',
            'name->en' => 'David'
        ],
        [
            'is_hostel' => true,
            'student_number' => 'A124',
            'name->en' => 'Eva'
        ]
    ));

    $student_classes = StudentClass::factory(5)->state(new Sequence(
        [
            'semester_setting_id' => $sem1->id,
            'semester_class_id' => $semester_classes[0]->id, // J111
            'student_id' => $students[0]->id, // Aaron
            'class_enter_date' => now()->toDateString(),
        ],
        [
            'semester_setting_id' => $sem1->id,
            'semester_class_id' => $semester_classes[0]->id, // J111
            'student_id' => $students[2]->id, // Cathy
            'class_enter_date' => now()->toDateString(),
        ],
        [
            'semester_setting_id' => $sem1->id,
            'semester_class_id' => $semester_classes[0]->id, // J111
            'student_id' => $students[4]->id, // Eva
            'class_enter_date' => now()->toDateString(),
        ],
        [
            'semester_setting_id' => $sem1->id,
            'semester_class_id' => $semester_classes[1]->id, // J211
            'student_id' => $students[1]->id, // Ben
            'class_enter_date' => now()->toDateString(),
        ],

        [
            'semester_setting_id' => $sem1->id,
            'semester_class_id' => $semester_classes[1]->id, // J211
            'student_id' => $students[3]->id, // David
            'class_enter_date' => now()->toDateString(),
        ],
    ))->create();

    $girl_hostel_block = HostelBlock::factory()->create([
        'name->en' => 'Block G',
        'type' => HostelBlockType::STUDENT->value,
    ]);

    $boy_hostel_block = HostelBlock::factory()->create([
        'name->en' => 'Block B',
        'type' => HostelBlockType::STUDENT->value,
    ]);

    $girl_room_1 = HostelRoom::factory()->create([
        'name' => 'Room G1',
        'gender' => HostelRoomGender::FEMALE->value,
        'hostel_block_id' => $girl_hostel_block->id,
    ]);

    $girl_room_2 = HostelRoom::factory()->create([
        'name' => 'Room G2',
        'gender' => HostelRoomGender::FEMALE->value,
        'hostel_block_id' => $girl_hostel_block->id,
    ]);

    $boy_room_1 = HostelRoom::factory()->create([
        'name' => 'Room B1',
        'gender' => HostelRoomGender::MALE->value,
        'hostel_block_id' => $boy_hostel_block->id,
    ]);

    $beds = HostelRoomBed::factory(5)->state(new Sequence(
        [
            'name' => 'G1-101', // Cathy's bed
            'hostel_room_id' => $girl_room_1->id,
            'is_active' => true,
            'status' => HostelRoomBedStatus::OCCUPIED->value
        ],
        [
            'name' => 'G2-101', // Eva's bed
            'hostel_room_id' => $girl_room_2->id,
            'is_active' => true,
            'status' => HostelRoomBedStatus::OCCUPIED->value
        ],
        [
            'name' => 'B1-101', // Aaron's bed
            'hostel_room_id' => $boy_room_1->id,
            'is_active' => true,
            'status' => HostelRoomBedStatus::OCCUPIED->value
        ],
        [
            'name' => 'B1-102', // Ben's bed
            'hostel_room_id' => $boy_room_1->id,
            'is_active' => true,
            'status' => HostelRoomBedStatus::OCCUPIED->value
        ],
        [
            'name' => 'B1-103', // David's bed
            'hostel_room_id' => $boy_room_1->id,
            'is_active' => true,
            'status' => HostelRoomBedStatus::OCCUPIED->value
        ]
    ))->create();

    $bed_assignments = HostelBedAssignment::factory(5)->state(new Sequence(
        [
            'assignable_id' => $students[0]->id, // Aaron
            'assignable_type' => Student::class,
            'hostel_room_bed_id' => $beds[2]->id, // Aaron's bed
            'start_date' => now()->toDateString(),
            'end_date' => null,
        ],
        [
            'assignable_id' => $students[1]->id, // Ben
            'assignable_type' => Student::class,
            'hostel_room_bed_id' => $beds[3]->id, // Ben's bed
            'start_date' => now()->toDateString(),
            'end_date' => null,
        ],
        [
            'assignable_id' => $students[2]->id, // Cathy
            'assignable_type' => Student::class,
            'hostel_room_bed_id' => $beds[0]->id, // Cathy's bed
            'start_date' => now()->toDateString(),
            'end_date' => null,
        ],
        [
            'assignable_id' => $students[3]->id, // David
            'assignable_type' => Student::class,
            'hostel_room_bed_id' => $beds[4]->id, // David's bed
            'start_date' => now()->toDateString(),
            'end_date' => null,
        ],
        [
            'assignable_id' => $students[4]->id, // Eva
            'assignable_type' => Student::class,
            'hostel_room_bed_id' => $beds[1]->id, // Eva's bed
            'start_date' => now()->toDateString(),
            'end_date' => null,
        ]
    ))->create();


    $this->partialMock(ReportPrintService::class, function (MockInterface $mock) {
        $mock->shouldReceive('upload')->once()->andReturnSelf();
        $mock->shouldReceive('getFileUrl')->once()->andReturn('some-random-url');
    });

    $hostelReportService = resolve(HostelReportService::class);

    $payload = [
        'report_language' => 'en',
        'semester_setting_id' => $sem1->id
    ];

    $file_name = 'hostels-report-by-boarders-name-list-unit-test';

    // Test Excel
    Excel::fake();

    $view_name = 'reports.hostels.by-boarders-name-list';

    $hostelReportService
        ->setExportType(ExportType::EXCEL->value)
        ->setReportViewName($view_name)
        ->setFileName('hostels-report-by-boarders-name-list-unit-test')
        ->getHostelBoarderListReportData($payload);

    Excel::assertStored(
        DocumentPrintService::PRINT_FOLDER . DIRECTORY_SEPARATOR . $file_name . '.xlsx',
        'local',
        function (GenericExcelExportViaView $export) use ($view_name, $students) {

            expect($export->view()->name())->toBe($view_name);

            $view = $this->view($export->view()->name(), $export->view()->getData());

            $view->assertSeeInOrder([
                '<td>1</td>',
                '<td>Room B1</td>',
                '<td>B1-101</td>',
                '<td>A120</td>',
                '<td>Aaron</td>',
                '<td>' . $students[0]->getTranslation('name', 'zh') . '</td>',
                '<td>Junior 1</td>',
                '<td>J111</td>'
            ], false);

            $view->assertSeeInOrder([
                '<td>2</td>',
                '<td>Room B1</td>',
                '<td>B1-102</td>',
                '<td>A121</td>',
                '<td>Ben</td>',
                '<td>' . $students[1]->getTranslation('name', 'zh') . '</td>',
                '<td>Junior 2</td>',
                '<td>J211</td>'
            ], false);

            $view->assertSeeInOrder([
                '<td>3</td>',
                '<td>Room B1</td>',
                '<td>B1-103</td>',
                '<td>A123</td>',
                '<td>David</td>',
                '<td>' . $students[3]->getTranslation('name', 'zh') . '</td>',
                '<td>Junior 2</td>',
                '<td>J211</td>'
            ], false);

            $view->assertSeeInOrder([
                '<td>4</td>',
                '<td>Room G1</td>',
                '<td>G1-101</td>',
                '<td>A122</td>',
                '<td>Cathy</td>',
                '<td>' . $students[2]->getTranslation('name', 'zh') . '</td>',
                '<td>Junior 1</td>',
                '<td>J111</td>'
            ], false);

            $view->assertSeeInOrder([
                '<td>5</td>',
                '<td>Room G2</td>',
                '<td>G2-101</td>',
                '<td>A124</td>',
                '<td>Eva</td>',
                '<td>' . $students[4]->getTranslation('name', 'zh') . '</td>',
                '<td>Junior 1</td>',
                '<td>J111</td>'
            ], false);

            return true;
        }
    );
});

test('getAvailableBedReportData, preview data', function () {
    $hostelReportService = resolve(HostelReportService::class);

    $block = HostelBlock::factory()->create(['type' => HostelBlockType::STUDENT->value]);
    $room = HostelRoom::factory()->create(['name' => 'Room_A111', 'hostel_block_id' => $block->id, 'capacity' => 8]);
    $room_occupied_count = 4;
    $room_available_count = 4;
    HostelRoomBed::factory($room_occupied_count)->create([
        'hostel_room_id' => $room->id,
        'status' => HostelRoomBedStatus::OCCUPIED,  // 4 beds occupied
    ]);
    HostelRoomBed::factory($room_available_count)->create([
        'hostel_room_id' => $room->id,
        'status' => HostelRoomBedStatus::AVAILABLE,  // 4 beds available
    ]);

    $response = $hostelReportService->getAvailableBedReportData()->toArray();

    expect($response)->toHaveCount(1)->toEqual([
        [
            'id' => $room->id,
            'name' => $room->name,
            'capacity' => ($room_occupied_count + $room_available_count),
            'occupied_beds' => $room_occupied_count,
            'available_beds' => $room_available_count,
        ],
    ]);
});

test('getAvailableBedReportData, download excel / pdf', function () {
    $block = HostelBlock::factory()->create(['type' => HostelBlockType::STUDENT->value]);
    $room = HostelRoom::factory()->create(['name' => 'Room_A111', 'hostel_block_id' => $block->id, 'capacity' => 8]);
    $room_occupied_count = 4;
    $room_available_count = 4;
    HostelRoomBed::factory($room_occupied_count)->create([
        'hostel_room_id' => $room->id,
        'status' => HostelRoomBedStatus::OCCUPIED,  // 4 beds occupied
    ]);
    HostelRoomBed::factory($room_available_count)->create([
        'hostel_room_id' => $room->id,
        'status' => HostelRoomBedStatus::AVAILABLE,  // 4 beds available
    ]);

    $this->partialMock(ReportPrintService::class, function (MockInterface $mock) {
        $mock->shouldReceive('upload')->twice()->andReturnSelf();
        $mock->shouldReceive('getFileUrl')->twice()->andReturn('some-random-url');
    });

    $hostelReportService = resolve(HostelReportService::class);

    $payload = [
        'report_language' => 'en',
        'export_type' => ExportType::EXCEL->value,
    ];

    $file_name = 'hostels-report-by-available-bed';

    $expected_headers = ['Room', 'Capacity', 'Occupied', 'Available'];

    // Test Excel
    Excel::fake();

    $view_name = 'reports.hostels.by-available-bed';

    $hostelReportService
        ->setExportType(ExportType::EXCEL->value)
        ->setReportViewName($view_name)
        ->setFileName('hostels-report-by-available-bed')
        ->getAvailableBedReportData($payload);

    Excel::assertStored(
        DocumentPrintService::PRINT_FOLDER . DIRECTORY_SEPARATOR . $file_name . '.xlsx',
        'local',
        function (GenericExcelExportViaView $export) use (
            $expected_headers,
            $view_name,
            $room,
            $room_occupied_count,
            $room_available_count,
        ) {
            expect($export->view()->name())->toBe($view_name);

            $view = $this->view($export->view()->name(), $export->view()->getData());

            $view->assertSee(__('hostel.title.bed_availability'));

            // Add th tag to the headers
            $expected_headers = array_map(function ($header) {
                return "<th>$header</th>";
            }, $expected_headers);

            $view->assertSeeInOrder($expected_headers, false);

            $view->assertSee($room->name);
            $view->assertSee($room_occupied_count + $room_available_count);
            $view->assertSee($room_occupied_count);
            $view->assertSee($room_available_count);

            return true;
        }
    );


    // Test PDF
    SnappyPdf::fake();

    $hostelReportService
        ->setExportType(ExportType::PDF->value)
        ->setReportViewName($view_name)
        ->setFileName('hostels-report-by-available-bed')
        ->getAvailableBedReportData($payload);

    SnappyPdf::assertFileNameIs(storage_path('app' . DIRECTORY_SEPARATOR . DocumentPrintService::PRINT_FOLDER . DIRECTORY_SEPARATOR . $file_name . '.pdf'));
    SnappyPdf::assertViewIs('reports.hostels.by-available-bed');

    foreach ($expected_headers as $header) {
        SnappyPdf::assertSee($header);
    }

    SnappyPdf::assertSee(__('hostel.title.bed_availability'));

    SnappyPdf::assertSee($room->name);
    SnappyPdf::assertSee($room_occupied_count + $room_available_count);
    SnappyPdf::assertSee($room_occupied_count);
    SnappyPdf::assertSee($room_available_count);
});

test('getCheckoutRecordReportData, preview data', function () {
    $hostelReportService = resolve(HostelReportService::class);

    $user_student_1 = User::factory()->create();

    $student_1 = Student::factory()->create(['name->en' => 'Jones', 'user_id' => $user_student_1->id]);

    // 2 guardians for $student_1, expected only the first guardian will see
    $user_guardian_1 = User::factory()->withGuardian()->create();
    $user_guardian_2 = User::factory()->withGuardian()->create();

    GuardianStudent::factory()->create([
        'guardian_id' => $user_guardian_1->guardian->id,
        'type' => GuardianType::FATHER->value,
        'studenable_type' => Student::class,
        'studenable_id' => $student_1->id
    ]);

    GuardianStudent::factory()->create([
        'guardian_id' => $user_guardian_2->guardian->id,
        'type' => GuardianType::GUARDIAN->value,
        'studenable_type' => Student::class,
        'studenable_id' => $student_1->id
    ]);

    $semester_setting = SemesterSetting::factory()->create([
        'from' => '2024-01-01',
        'to' => '2024-06-30',
    ]);
    $next_semester_setting = SemesterSetting::factory()->create([
        'from' => '2024-07-01',
        'to' => '2024-12-31',
    ]);

    $first_grade = Grade::factory()->create();
    $next_grade = Grade::factory()->create();

    $first_class = ClassModel::factory()->create(['grade_id' => $first_grade->id]);
    $next_class = ClassModel::factory()->create(['grade_id' => $next_grade->id]);

    $first_sem_class = SemesterClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'class_id' => $first_class->id,
    ]);

    $next_sem_class = SemesterClass::factory()->create([
        'semester_setting_id' => $next_semester_setting->id,
        'class_id' => $next_class->id,
    ]);

    $sc1 = StudentClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'semester_class_id' => $first_sem_class->id,
        'student_id' => $student_1->id,
        'class_enter_date' => '2024-01-01',
    ]);

    $next_sc = StudentClass::factory()->create([
        'semester_setting_id' => $next_semester_setting->id,
        'semester_class_id' => $next_sem_class->id,
        'student_id' => $student_1->id,
        'class_enter_date' => '2024-07-01',
    ]);

    $boy_hostel_block = HostelBlock::factory()->create([
        'name->en' => 'Block B',
        'type' => HostelBlockType::STUDENT->value,
    ]);

    $boy_room_1 = HostelRoom::factory()->create([
        'name' => 'Room B1',
        'gender' => HostelRoomGender::MALE->value,
        'hostel_block_id' => $boy_hostel_block->id,
    ]);

    $beds = HostelRoomBed::factory(5)->state(new Sequence(
        [
            'name' => 'B1-101',
            'hostel_room_id' => $boy_room_1->id,
            'is_active' => true,
            'status' => HostelRoomBedStatus::OCCUPIED->value
        ],
        [
            'name' => 'B1-102',
            'hostel_room_id' => $boy_room_1->id,
            'is_active' => true,
            'status' => HostelRoomBedStatus::OCCUPIED->value
        ],
    ))->create();

    $bed_assignments = HostelBedAssignment::factory(2)->state(new Sequence(
        [
            'assignable_type' => Student::class,
            'assignable_id' => $student_1->id,
            'hostel_room_bed_id' => $beds[1]->id, // B1-102
            'start_date' => '2024-01-01',
            'end_date' => '2024-06-30',
        ],
        [
            'assignable_type' => Student::class,
            'assignable_id' => $student_1->id,
            'hostel_room_bed_id' => $beds[0]->id, // B1-101
            'start_date' => '2024-07-01',
            'end_date' => '2024-12-31',

        ],
    ))->create();


    $response = $hostelReportService->getCheckoutRecordReportData([
        'year' => $semester_setting->semesterYearSetting->year,
        'semester_setting_id' => $semester_setting->id,
    ])->toArray();

    expect($response)->toEqual(
        [
            [
                'id' => $bed_assignments[1]->id,
                'start_date' => $bed_assignments[1]->start_date->toISOString(),
                'end_date' => $bed_assignments[1]->end_date->toISOString(),
                'hostel_room_bed_id' => $bed_assignments[1]->hostel_room_bed_id,
                'remarks' => $bed_assignments[1]->remarks,
                'class_name' => $first_class->getTranslations('name'), // class_name will get from chosen semester_setting_id
                'grade_name' => $first_grade->getTranslations('name'), // grade_name will get from chosen semester_setting_id
                'bed_name' => 'B1-101',
                'room_name' => 'Room B1',
                'student_name' => $student_1->getTranslations('name'),
                'phone_number' => $student_1->phone_number,
                'student_number' => $student_1->student_number,
                'nric' => $student_1->nric,
                'guardian' => $user_guardian_1->guardian->getTranslations('name'),
                'block_name' => $bed_assignments[1]->bed->hostelRoom->hostelBlock->getTranslation('name', app()->getLocale()),
            ],
            [
                'id' => $bed_assignments[0]->id,
                'start_date' => $bed_assignments[0]->start_date->toISOString(),
                'end_date' => $bed_assignments[0]->end_date->toISOString(),
                'hostel_room_bed_id' => $bed_assignments[0]->hostel_room_bed_id,
                'remarks' => $bed_assignments[0]->remarks,
                'class_name' => $first_class->getTranslations('name'), // class_name will get from chosen semester_setting_id
                'grade_name' => $first_grade->getTranslations('name'), // grade_name will get from chosen semester_setting_id
                'bed_name' => 'B1-102',
                'room_name' => 'Room B1',
                'student_name' => $student_1->getTranslations('name'),
                'phone_number' => $student_1->phone_number,
                'student_number' => $student_1->student_number,
                'nric' => $student_1->nric,
                'guardian' => $user_guardian_1->guardian->getTranslations('name'),
                'block_name' => $bed_assignments[0]->bed->hostelRoom->hostelBlock->getTranslation('name', app()->getLocale()),
            ],
        ]
    );
});

test('getCheckoutRecordReportData, download excel / pdf', function () {
    $user_student_1 = User::factory()->create();

    $student_1 = Student::factory()->create(['name->en' => 'Jones', 'user_id' => $user_student_1->id]);

    // 2 guardians for $student_1, expected only the first guardian will see
    $user_guardian_1 = User::factory()->withGuardian()->create();
    $user_guardian_2 = User::factory()->withGuardian()->create();

    GuardianStudent::factory()->create([
        'guardian_id' => $user_guardian_1->guardian->id,
        'type' => GuardianType::FATHER->value,
        'studenable_type' => Student::class,
        'studenable_id' => $student_1->id
    ]);

    GuardianStudent::factory()->create([
        'guardian_id' => $user_guardian_2->guardian->id,
        'type' => GuardianType::GUARDIAN->value,
        'studenable_type' => Student::class,
        'studenable_id' => $student_1->id
    ]);

    $semester_setting = SemesterSetting::factory()->create([
        'from' => '2024-01-01',
        'to' => '2024-06-30',
    ]);
    $next_semester_setting = SemesterSetting::factory()->create([
        'from' => '2024-07-01',
        'to' => '2024-12-31',
    ]);

    $first_grade = Grade::factory()->create();
    $next_grade = Grade::factory()->create();

    $first_class = ClassModel::factory()->create(['grade_id' => $first_grade->id]);
    $next_class = ClassModel::factory()->create(['grade_id' => $next_grade->id]);

    $first_sem_class = SemesterClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'class_id' => $first_class->id,
    ]);

    $next_sem_class = SemesterClass::factory()->create([
        'semester_setting_id' => $next_semester_setting->id,
        'class_id' => $next_class->id,
    ]);

    $sc1 = StudentClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'semester_class_id' => $first_sem_class->id,
        'student_id' => $student_1->id,
        'class_enter_date' => '2024-01-01',
    ]);

    $next_sc = StudentClass::factory()->create([
        'semester_setting_id' => $next_semester_setting->id,
        'semester_class_id' => $next_sem_class->id,
        'student_id' => $student_1->id,
        'class_enter_date' => '2024-07-01',
    ]);

    $bed_assignment_1 = HostelBedAssignment::factory()->create([
        'assignable_type' => Student::class,
        'assignable_id' => $student_1->id,
        'start_date' => '2024-01-01',
        'end_date' => '2024-06-30',
    ]);

    $bed_assignment_2 = HostelBedAssignment::factory()->create([
        'assignable_type' => Student::class,
        'assignable_id' => $student_1->id,
        'start_date' => '2024-07-01',
        'end_date' => '2024-12-31',
    ]);


    /**
     * download start
     */
    $this->partialMock(ReportPrintService::class, function (MockInterface $mock) {
        $mock->shouldReceive('upload')->twice()->andReturnSelf();
        $mock->shouldReceive('getFileUrl')->twice()->andReturn('some-random-url');
    });

    $hostelReportService = resolve(HostelReportService::class);

    $payload = [
        'export_type' => ExportType::EXCEL->value,
        'year' => $semester_setting->semesterYearSetting->year,
        'semester_setting_id' => $semester_setting->id,
    ];

    $title = __('hostel.title.checkout_record', ['year' => $payload['year']]);

    $file_name = 'hostels-report-by-checkout-record';

    $expected_headers = [
        'No.',
        'Room',
        'Bed No.',
        'Student No.',
        'Name',
        'I/C No.',
        'Phone No.',
        'Guardian',
        'Class',
        'Check-In Date',
        'Check-Out Date',
        'Remarks',
    ];

    // Test Excel
    Excel::fake();

    $view_name = 'reports.hostels.by-checkout-record';

    $hostelReportService
        ->setExportType(ExportType::EXCEL->value)
        ->setReportViewName($view_name)
        ->setFileName('hostels-report-by-checkout-record')
        ->getCheckoutRecordReportData($payload);

    Excel::assertStored(
        DocumentPrintService::PRINT_FOLDER . DIRECTORY_SEPARATOR . $file_name . '.xlsx',
        'local',
        function (GenericExcelExportViaView $export) use (
            $expected_headers,
            $view_name,
            $bed_assignment_1,
            $bed_assignment_2,
            $student_1,
            $first_grade,
            $first_class,
            $title,
            $user_guardian_1,
        ) {
            expect($export->view()->name())->toBe($view_name);

            $view = $this->view($export->view()->name(), $export->view()->getData());

            $view->assertSee($title, false);

            // Add th tag to the headers
            $expected_headers = array_map(function ($header) {
                return "<th>$header</th>";
            }, $expected_headers);

            $view->assertSeeInOrder($expected_headers, false);

            $view->assertSee($user_guardian_1->guardian->getTranslation('name', 'en'));
            $view->assertSee($user_guardian_1->guardian->getTranslation('name', 'zh'));

            $view->assertSee($bed_assignment_1->bed->hostelRoom->name);
            $view->assertSee($bed_assignment_1->bed->name);
            $view->assertSee($student_1->student_number);
            $view->assertSee($student_1->getTranslation('name', 'en'));
            $view->assertSee($student_1->getTranslation('name', 'zh'));
            $view->assertSee($student_1->nric);
            $view->assertSee($student_1->phone_number);
            $view->assertSee($first_class->getTranslation('name', 'en'));
            $view->assertSee($bed_assignment_1->start_date->toDateString());
            $view->assertSee($bed_assignment_1->end_date->toDateString());
            $view->assertSee($bed_assignment_1->remarks);

            $view->assertSee($bed_assignment_2->bed->hostelRoom->name);
            $view->assertSee($bed_assignment_2->bed->name);
            $view->assertSee($student_1->student_number);
            $view->assertSee($student_1->getTranslation('name', 'en'));
            $view->assertSee($student_1->getTranslation('name', 'zh'));
            $view->assertSee($student_1->nric);
            $view->assertSee($student_1->phone_number);
            $view->assertSee($first_class->getTranslation('name', 'en'));
            $view->assertSee($bed_assignment_2->start_date->toDateString());
            $view->assertSee($bed_assignment_2->end_date->toDateString());
            $view->assertSee($bed_assignment_2->remarks);

            return true;
        }
    );

    // Test PDF
    SnappyPdf::fake();

    $hostelReportService
        ->setExportType(ExportType::PDF->value)
        ->setReportViewName($view_name)
        ->setFileName('hostels-report-by-checkout-record')
        ->getCheckoutRecordReportData($payload);

    SnappyPdf::assertFileNameIs(storage_path('app' . DIRECTORY_SEPARATOR . DocumentPrintService::PRINT_FOLDER . DIRECTORY_SEPARATOR . $file_name . '.pdf'));
    SnappyPdf::assertViewIs('reports.hostels.by-checkout-record');

    foreach ($expected_headers as $header) {
        SnappyPdf::assertSee($header);
    }

    SnappyPdf::assertSee($title);

    SnappyPdf::assertSee(e($user_guardian_1->guardian->getTranslation('name', 'en')));
    SnappyPdf::assertSee(e($user_guardian_1->guardian->getTranslation('name', 'zh')));

    SnappyPdf::assertSee($bed_assignment_1->bed->hostelRoom->name);
    SnappyPdf::assertSee($bed_assignment_1->bed->name);
    SnappyPdf::assertSee($student_1->student_number);
    SnappyPdf::assertSee(e($student_1->getTranslation('name', 'en')));
    SnappyPdf::assertSee(e($student_1->getTranslation('name', 'zh')));
    SnappyPdf::assertSee($student_1->nric);
    SnappyPdf::assertSee($student_1->phone_number);
    SnappyPdf::assertSee(e($first_class->getTranslation('name', 'en')));
    SnappyPdf::assertSee($bed_assignment_1->start_date->toDateString());
    SnappyPdf::assertSee($bed_assignment_1->end_date->toDateString());
    SnappyPdf::assertSee($bed_assignment_1->remarks);

    SnappyPdf::assertSee($bed_assignment_2->bed->hostelRoom->name);
    SnappyPdf::assertSee($bed_assignment_2->bed->name);
    SnappyPdf::assertSee($student_1->student_number);
    SnappyPdf::assertSee(e($student_1->getTranslation('name', 'en')));
    SnappyPdf::assertSee(e($student_1->getTranslation('name', 'zh')));
    SnappyPdf::assertSee($student_1->nric);
    SnappyPdf::assertSee($student_1->phone_number);
    SnappyPdf::assertSee(e($first_class->getTranslation('name', 'en')));
    SnappyPdf::assertSee($bed_assignment_2->start_date->toDateString());
    SnappyPdf::assertSee($bed_assignment_2->end_date->toDateString());
    SnappyPdf::assertSee($bed_assignment_2->remarks);
});

test('getBoardersListInfoReportData, preview data', function () {
    $hostelReportService = resolve(HostelReportService::class);

    $sem1 = SemesterSetting::factory()->create([
        'name' => 'Semester 1'
    ]);

    $sem2 = SemesterSetting::factory()->create([
        'name' => 'Semester 2'
    ]);

    $grade1 = Grade::factory()->create([
        'name->en' => 'Junior 1'
    ]);

    $grade2 = Grade::factory()->create([
        'name->en' => 'Junior 2'
    ]);

    $classes = ClassModel::factory(2)->create(new Sequence(
        [
            'name->en' => 'J111',
            'type' => ClassType::PRIMARY,
            'grade_id' => $grade1->id
        ],
        [
            'name->en' => 'J211',
            'type' => ClassType::PRIMARY,
            'grade_id' => $grade2->id
        ],
    ));

    $semester_classes = SemesterClass::factory(4)->create(new Sequence(
        [
            'semester_setting_id' => $sem1->id,
            'class_id' => $classes[0]->id // Grade 1
        ],
        [
            'semester_setting_id' => $sem1->id,
            'class_id' => $classes[1]->id // Grade 2
        ],
        [
            'semester_setting_id' => $sem2->id,
            'class_id' => $classes[0]->id // Grade 1
        ],
        [
            'semester_setting_id' => $sem2->id,
            'class_id' => $classes[1]->id // Grade 2
        ],
    ));

    $students = Student::factory(4)->create(new Sequence(
        [
            'name->en' => 'John Jones',
            'is_hostel' => true,

        ],
        [
            'name->en' => 'Dwayne',
            'is_hostel' => true,

        ],
        [
            'name->en' => 'Karen',
            'is_hostel' => true,

        ],
        [
            'name->en' => 'Kaley',
            'is_hostel' => true,

        ],
    ));

    $user_guardians = User::factory(2)->withGuardian()->create(); // 2 guardians for $students[0]

    $gs1 = GuardianStudent::factory()->create([
        'guardian_id' => $user_guardians[0]->guardian->id,
        'type' => GuardianType::FATHER->value,
        'studenable_type' => Student::class,
        'studenable_id' => $students[0]->id
    ]);

    $gs2 = GuardianStudent::factory()->create([
        'guardian_id' => $user_guardians[1]->guardian->id,
        'type' => GuardianType::MOTHER->value,
        'studenable_type' => Student::class,
        'studenable_id' => $students[0]->id
    ]);

    $user_guardians2 = User::factory(2)->withGuardian()->create(); // 2 guardians for $students[1]

    $gs3 = GuardianStudent::factory()->create([
        'guardian_id' => $user_guardians2[0]->guardian->id,
        'type' => GuardianType::FATHER->value,
        'studenable_type' => Student::class,
        'studenable_id' => $students[1]->id
    ]);

    $gs4 = GuardianStudent::factory()->create([
        'guardian_id' => $user_guardians2[1]->guardian->id,
        'type' => GuardianType::MOTHER->value,
        'studenable_type' => Student::class,
        'studenable_id' => $students[1]->id
    ]);

    $user_guardians3 = User::factory(2)->withGuardian()->create(); // 2 guardians for $students[2]

    $gs5 = GuardianStudent::factory()->create([
        'guardian_id' => $user_guardians3[0]->guardian->id,
        'type' => GuardianType::FATHER->value,
        'studenable_type' => Student::class,
        'studenable_id' => $students[2]->id
    ]);

    $gs6 = GuardianStudent::factory()->create([
        'guardian_id' => $user_guardians3[1]->guardian->id,
        'type' => GuardianType::MOTHER->value,
        'studenable_type' => Student::class,
        'studenable_id' => $students[2]->id
    ]);


    StudentClass::factory()->create([
        'semester_setting_id' => $sem1->id,
        'semester_class_id' => $semester_classes[0]->id, // Grade 1
        'student_id' => $students[0]->id,
        'class_enter_date' => now()->toDateString()
    ]);

    StudentClass::factory()->create([
        'semester_setting_id' => $sem1->id,
        'semester_class_id' => $semester_classes[0]->id, // Grade 1
        'student_id' => $students[1]->id,
        'class_enter_date' => now()->toDateString()
    ]);

    StudentClass::factory()->create([
        'semester_setting_id' => $sem1->id,
        'semester_class_id' => $semester_classes[0]->id, // Grade 1
        'student_id' => $students[2]->id,
        'class_enter_date' => now()->toDateString()
    ]);

    StudentClass::factory()->create([
        'semester_setting_id' => $sem1->id,
        'semester_class_id' => $semester_classes[0]->id, // Grade 1
        'student_id' => $students[3]->id,
        'class_enter_date' => now()->toDateString()
    ]);

    $hostel_block = HostelBlock::factory()->create([
        'name->en' => 'Block A',
    ]);

    $hostel_room1 = HostelRoom::factory()->create([
        'name' => 'Room 1',
        'hostel_block_id' => $hostel_block->id,
    ]);

    $hostel_bed2 = HostelRoomBed::factory()->create([
        'name' => 'Bed 2',
        'hostel_room_id' => $hostel_room1->id,
    ]);

    $hostel_bed1 = HostelRoomBed::factory()->create([
        'name' => 'Bed 1',
        'hostel_room_id' => $hostel_room1->id,
    ]);


    $bed_assignment1 = HostelBedAssignment::factory()->create([
        'hostel_room_bed_id' => $hostel_bed1->id,
        'assignable_id' => $students[0]->id,
        'assignable_type' => Student::class,
        'start_date' => now()->toDateString()
    ]);

    $bed_assignment2 = HostelBedAssignment::factory()->create([
        'hostel_room_bed_id' => $hostel_bed2->id,
        'assignable_id' => $students[1]->id,
        'assignable_type' => Student::class,
        'start_date' => now()->toDateString()
    ]);


    // expect only get 1 guardian for each students
    $response = $hostelReportService->getBoardersListInfoReportData([
        'semester_setting_id' => $sem1->id,
    ])->keyBy('student_id');


    // student[0] has 2 guardians, bed assignment in bed1 but only see 1 guardians
    expect($response[$students[0]->id])->toEqual([
        'student_id' => $students[0]->id,
        'student_number' => $students[0]->student_number,
        'student_name' => $students[0]->getTranslations('name'),
        'student_nric' => $students[0]->nric,
        'student_address' => $students[0]->address,
        'student_phone_number' => $students[0]->phone_number,
        'student_date_of_birth' => $students[0]->date_of_birth,
        'start_date' => $bed_assignment1->start_date->toDateString(),
        'end_date' => $bed_assignment1->end_date?->toDateString(),
        'block_name' => $hostel_block->getTranslation('name', app()->getLocale()),
        'bed_name' => $hostel_bed1->name,
        'room_name' => $hostel_room1->name,
        'class_name' => $classes[0]->getTranslations('name'),
        'grade_name' => $grade1->getTranslations('name'),
        'guardians' => [
            [
                'id' => $user_guardians[0]->guardian->id,
                'name' => $user_guardians[0]->guardian->getTranslations('name'),
                'nric' => $user_guardians[0]->guardian->nric,
                'phone_number' => $user_guardians[0]->guardian->phone_number,
                'pivot' => $gs1->toArray(),
            ],
        ]
    ]);

    // student[1] has 2 guardians, bed assignment in bed2 but only see 1 guardians
    expect($response[$students[1]->id])->toEqual([
        'student_id' => $students[1]->id,
        'student_number' => $students[1]->student_number,
        'student_name' => $students[1]->getTranslations('name'),
        'student_nric' => $students[1]->nric,
        'student_address' => $students[1]->address,
        'student_phone_number' => $students[1]->phone_number,
        'student_date_of_birth' => $students[1]->date_of_birth,
        'start_date' => $bed_assignment2->start_date->toDateString(),
        'end_date' => $bed_assignment2->end_date?->toDateString(),
        'block_name' => $hostel_block->getTranslation('name', app()->getLocale()),
        'bed_name' => $hostel_bed2->name,
        'room_name' => $hostel_room1->name,
        'class_name' => $classes[0]->getTranslations('name'),
        'grade_name' => $grade1->getTranslations('name'),
        'guardians' => [
            [
                'id' => $user_guardians2[0]->guardian->id,
                'name' => $user_guardians2[0]->guardian->getTranslations('name'),
                'nric' => $user_guardians2[0]->guardian->nric,
                'phone_number' => $user_guardians2[0]->guardian->phone_number,
                'pivot' => $gs3->toArray(),
            ],
        ]
    ]);

    // student[2] has 2 guardians, has no bed assignment but only see 1 guardians
    expect($response[$students[2]->id])->toEqual([
        'student_id' => $students[2]->id,
        'student_number' => $students[2]->student_number,
        'student_name' => $students[2]->getTranslations('name'),
        'student_nric' => $students[2]->nric,
        'student_address' => $students[2]->address,
        'student_phone_number' => $students[2]->phone_number,
        'student_date_of_birth' => $students[2]->date_of_birth,
        'start_date' => null,
        'end_date' => null,
        'block_name' => null,
        'bed_name' => null,
        'room_name' => null,
        'class_name' => $classes[0]->getTranslations('name'),
        'grade_name' => $grade1->getTranslations('name'),
        'guardians' => [
            [
                'id' => $user_guardians3[0]->guardian->id,
                'name' => $user_guardians3[0]->guardian->getTranslations('name'),
                'nric' => $user_guardians3[0]->guardian->nric,
                'phone_number' => $user_guardians3[0]->guardian->phone_number,
                'pivot' => $gs5->toArray(),
            ],
        ]
    ]);

    // student[3] has no guardians, hoas no bed assignment
    expect($response[$students[3]->id])->toEqual([
        'student_id' => $students[3]->id,
        'student_number' => $students[3]->student_number,
        'student_name' => $students[3]->getTranslations('name'),
        'student_nric' => $students[3]->nric,
        'student_address' => $students[3]->address,
        'student_phone_number' => $students[3]->phone_number,
        'student_date_of_birth' => $students[3]->date_of_birth,
        'start_date' => null,
        'end_date' => null,
        'block_name' => null,
        'bed_name' => null,
        'room_name' => null,
        'class_name' => $classes[0]->getTranslations('name'),
        'grade_name' => $grade1->getTranslations('name'),
        'guardians' => []
    ]);
});

test('getBoardersListInfoReportData, download excel / pdf', function () {
    $sem1 = SemesterSetting::factory()->create([
        'name' => 'Semester 1'
    ]);

    $sem2 = SemesterSetting::factory()->create([
        'name' => 'Semester 2'
    ]);

    $grade1 = Grade::factory()->create([
        'name->en' => 'Junior 1'
    ]);

    $grade2 = Grade::factory()->create([
        'name->en' => 'Junior 2'
    ]);

    $classes = ClassModel::factory(2)->create(new Sequence(
        [
            'name->en' => 'J111',
            'type' => ClassType::PRIMARY,
            'grade_id' => $grade1->id
        ],
        [
            'name->en' => 'J211',
            'type' => ClassType::PRIMARY,
            'grade_id' => $grade2->id
        ],
    ));

    $semester_classes = SemesterClass::factory(4)->create(new Sequence(
        [
            'semester_setting_id' => $sem1->id,
            'class_id' => $classes[0]->id // Grade 1
        ],
        [
            'semester_setting_id' => $sem1->id,
            'class_id' => $classes[1]->id // Grade 2
        ],
        [
            'semester_setting_id' => $sem2->id,
            'class_id' => $classes[0]->id // Grade 1
        ],
        [
            'semester_setting_id' => $sem2->id,
            'class_id' => $classes[1]->id // Grade 2
        ],
    ));

    $students = Student::factory()->count(4)->create([
        'is_hostel' => true,
    ]);

    $user_guardians = User::factory(2)->withGuardian()->create(); // 2 guardians for $students[0]

    $gs1 = GuardianStudent::factory()->create([
        'guardian_id' => $user_guardians[0]->guardian->id,
        'type' => GuardianType::FATHER->value,
        'studenable_type' => Student::class,
        'studenable_id' => $students[0]->id
    ]);

    $gs2 = GuardianStudent::factory()->create([
        'guardian_id' => $user_guardians[1]->guardian->id,
        'type' => GuardianType::MOTHER->value,
        'studenable_type' => Student::class,
        'studenable_id' => $students[0]->id
    ]);

    $user_guardians2 = User::factory(2)->withGuardian()->create(); // 2 guardians for $students[1]

    $gs3 = GuardianStudent::factory()->create([
        'guardian_id' => $user_guardians2[0]->guardian->id,
        'type' => GuardianType::FATHER->value,
        'studenable_type' => Student::class,
        'studenable_id' => $students[1]->id
    ]);

    $gs4 = GuardianStudent::factory()->create([
        'guardian_id' => $user_guardians2[1]->guardian->id,
        'type' => GuardianType::MOTHER->value,
        'studenable_type' => Student::class,
        'studenable_id' => $students[1]->id
    ]);

    $user_guardians3 = User::factory(2)->withGuardian()->create(); // 2 guardians for $students[2]

    $gs5 = GuardianStudent::factory()->create([
        'guardian_id' => $user_guardians3[0]->guardian->id,
        'type' => GuardianType::FATHER->value,
        'studenable_type' => Student::class,
        'studenable_id' => $students[2]->id
    ]);

    $gs6 = GuardianStudent::factory()->create([
        'guardian_id' => $user_guardians3[1]->guardian->id,
        'type' => GuardianType::MOTHER->value,
        'studenable_type' => Student::class,
        'studenable_id' => $students[2]->id
    ]);


    StudentClass::factory()->create([
        'semester_setting_id' => $sem1->id,
        'semester_class_id' => $semester_classes[0]->id, // Grade 1
        'student_id' => $students[0]->id,
        'class_enter_date' => now()->toDateString()
    ]);

    StudentClass::factory()->create([
        'semester_setting_id' => $sem1->id,
        'semester_class_id' => $semester_classes[0]->id, // Grade 1
        'student_id' => $students[1]->id,
        'class_enter_date' => now()->toDateString()
    ]);

    StudentClass::factory()->create([
        'semester_setting_id' => $sem1->id,
        'semester_class_id' => $semester_classes[0]->id, // Grade 1
        'student_id' => $students[2]->id,
        'class_enter_date' => now()->toDateString()
    ]);

    StudentClass::factory()->create([
        'semester_setting_id' => $sem1->id,
        'semester_class_id' => $semester_classes[0]->id, // Grade 1
        'student_id' => $students[3]->id,
        'class_enter_date' => now()->toDateString()
    ]);


    $hostel_room1 = HostelRoom::factory()->create([
        'name' => 'Room 1'
    ]);

    $hostel_bed2 = HostelRoomBed::factory()->create([
        'name' => 'Bed 2',
        'hostel_room_id' => $hostel_room1->id,
    ]);

    $hostel_bed1 = HostelRoomBed::factory()->create([
        'name' => 'Bed 1',
        'hostel_room_id' => $hostel_room1->id,
    ]);


    $bed_assignment1 = HostelBedAssignment::factory()->create([
        'hostel_room_bed_id' => $hostel_bed1->id,
        'assignable_id' => $students[0]->id,
        'assignable_type' => Student::class,
        'start_date' => now()->toDateString()
    ]);

    $bed_assignment2 = HostelBedAssignment::factory()->create([
        'hostel_room_bed_id' => $hostel_bed2->id,
        'assignable_id' => $students[1]->id,
        'assignable_type' => Student::class,
        'start_date' => now()->toDateString()
    ]);


    /**
     * download start
     */
    $this->partialMock(ReportPrintService::class, function (MockInterface $mock) {
        $mock->shouldReceive('upload')->twice()->andReturnSelf();
        $mock->shouldReceive('getFileUrl')->twice()->andReturn('some-random-url');
    });

    $hostelReportService = resolve(HostelReportService::class);

    $payload = [
        'export_type' => ExportType::EXCEL->value,
        'semester_setting_id' => $sem1->id,
    ];

    $title = __('hostel.title.boarders_list_info', ['semester_setting' => $sem1->name]);

    $file_name = 'hostels-report-by-boarders-list-information';

    $expected_headers = [
        'Room',
        'Bed No.',
        'Student No.',
        'Name',
        'I/C No.',
        'Address',
        'Phone No.',
        'Guardian',
        'Class',
        'Check-In Date',
    ];

    // Test Excel
    Excel::fake();

    $view_name = 'reports.hostels.by-boarders-list-information';

    $hostelReportService
        ->setExportType(ExportType::EXCEL->value)
        ->setReportViewName($view_name)
        ->setFileName('hostels-report-by-boarders-list-information')
        ->getBoardersListInfoReportData($payload);

    Excel::assertStored(
        DocumentPrintService::PRINT_FOLDER . DIRECTORY_SEPARATOR . $file_name . '.xlsx',
        'local',
        function (GenericExcelExportViaView $export) use (
            $expected_headers,
            $view_name,
            $title,
            $students,
            $bed_assignment1,
            $bed_assignment2,
            $hostel_bed1,
            $hostel_bed2,
            $hostel_room1,
            $classes,
            $grade1,
            $user_guardians,
            $user_guardians2,
            $user_guardians3,
        ) {
            expect($export->view()->name())->toBe($view_name);

            $view = $this->view($export->view()->name(), $export->view()->getData());

            $view->assertSee($title);

            // Add th tag to the headers
            $expected_headers = array_map(function ($header) {
                return "<th>$header</th>";
            }, $expected_headers);

            $view->assertSeeInOrder($expected_headers, false);

            foreach ($students as $student) {
                $view->assertSee($student->student_number);
                $view->assertSee($student->getTranslation('name', 'en'));
                $view->assertSee($student->getTranslation('name', 'zh'));
                $view->assertSee($student->nric);
                $view->assertSee($student->address);
                $view->assertSee($student->phone_number);
            }

            $view->assertSee($bed_assignment1->start_date->toDateString());
            $view->assertSee($bed_assignment1->end_date?->toDateString());

            $view->assertSee($bed_assignment2->start_date->toDateString());
            $view->assertSee($bed_assignment2->end_date?->toDateString());

            $view->assertSee($hostel_bed1->name);
            $view->assertSee($hostel_bed2->name);
            $view->assertSee($hostel_room1->name);

            $view->assertSee($classes[0]->getTranslation('name', 'en'));

            $view->assertSee($user_guardians[0]->guardian->getTranslation('name', 'en'));
            $view->assertSee($user_guardians[0]->guardian->getTranslation('name', 'zh'));

            $view->assertSee($user_guardians2[0]->guardian->getTranslation('name', 'en'));
            $view->assertSee($user_guardians2[0]->guardian->getTranslation('name', 'zh'));

            $view->assertSee($user_guardians3[0]->guardian->getTranslation('name', 'en'));
            $view->assertSee($user_guardians3[0]->guardian->getTranslation('name', 'zh'));

            return true;
        }
    );

    // Test PDF
    SnappyPdf::fake();

    $hostelReportService
        ->setExportType(ExportType::PDF->value)
        ->setReportViewName($view_name)
        ->setFileName('hostels-report-by-boarders-list-information')
        ->getBoardersListInfoReportData($payload);

    SnappyPdf::assertFileNameIs(storage_path('app' . DIRECTORY_SEPARATOR . DocumentPrintService::PRINT_FOLDER . DIRECTORY_SEPARATOR . $file_name . '.pdf'));
    SnappyPdf::assertViewIs('reports.hostels.by-boarders-list-information');

    foreach ($expected_headers as $header) {
        SnappyPdf::assertSee($header);
    }

    foreach ($students as $student) {
        SnappyPdf::assertSee($student->student_number);
        SnappyPdf::assertSee(e($student->getTranslation('name', 'en')));
        SnappyPdf::assertSee(e($student->getTranslation('name', 'zh')));
        SnappyPdf::assertSee($student->nric);
        SnappyPdf::assertSee(e($student->address));
        SnappyPdf::assertSee($student->phone_number);
    }

    SnappyPdf::assertSee($bed_assignment1->start_date->toDateString());
    SnappyPdf::assertSee($bed_assignment2->start_date->toDateString());

    SnappyPdf::assertSee($hostel_bed1->name);
    SnappyPdf::assertSee($hostel_bed2->name);
    SnappyPdf::assertSee($hostel_room1->name);

    SnappyPdf::assertSee(e($classes[0]->getTranslation('name', 'en')));

    SnappyPdf::assertSee(e($user_guardians[0]->guardian->getTranslation('name', 'en')));
    SnappyPdf::assertSee(e($user_guardians[0]->guardian->getTranslation('name', 'zh')));

    SnappyPdf::assertSee(e($user_guardians2[0]->guardian->getTranslation('name', 'en')));
    SnappyPdf::assertSee(e($user_guardians2[0]->guardian->getTranslation('name', 'zh')));

    SnappyPdf::assertSee(e($user_guardians3[0]->guardian->getTranslation('name', 'en')));
    SnappyPdf::assertSee(e($user_guardians3[0]->guardian->getTranslation('name', 'zh')));
});

test('getBoardersContactInfoReportData, preview data', function () {
    $hostelReportService = resolve(HostelReportService::class);

    $sem1 = SemesterSetting::factory()->create([
        'name' => 'Semester 1'
    ]);

    $sem2 = SemesterSetting::factory()->create([
        'name' => 'Semester 2'
    ]);

    $grade1 = Grade::factory()->create([
        'name->en' => 'Junior 1'
    ]);

    $grade2 = Grade::factory()->create([
        'name->en' => 'Junior 2'
    ]);

    $classes = ClassModel::factory(2)->create(new Sequence(
        [
            'name->en' => 'J111',
            'type' => ClassType::PRIMARY,
            'grade_id' => $grade1->id
        ],
        [
            'name->en' => 'J211',
            'type' => ClassType::PRIMARY,
            'grade_id' => $grade2->id
        ],
    ));

    $semester_classes = SemesterClass::factory(4)->create(new Sequence(
        [
            'semester_setting_id' => $sem1->id,
            'class_id' => $classes[0]->id // Grade 1
        ],
        [
            'semester_setting_id' => $sem1->id,
            'class_id' => $classes[1]->id // Grade 2
        ],
        [
            'semester_setting_id' => $sem2->id,
            'class_id' => $classes[0]->id // Grade 1
        ],
        [
            'semester_setting_id' => $sem2->id,
            'class_id' => $classes[1]->id // Grade 2
        ],
    ));

    $students = Student::factory(4)->create(new Sequence(
        [
            'name->en' => 'John Jones',
            'is_hostel' => true,

        ],
        [
            'name->en' => 'Dwayne',
            'is_hostel' => true,

        ],
        [
            'name->en' => 'Karen',
            'is_hostel' => true,

        ],
        [
            'name->en' => 'Kaley',
            'is_hostel' => true,

        ],
    ));

    $user_guardians = User::factory(2)->withGuardian()->create(); // 2 guardians for $students[0]

    $gs1 = GuardianStudent::factory()->create([
        'guardian_id' => $user_guardians[0]->guardian->id,
        'type' => GuardianType::FATHER->value,
        'studenable_type' => Student::class,
        'studenable_id' => $students[0]->id
    ]);

    $gs2 = GuardianStudent::factory()->create([
        'guardian_id' => $user_guardians[1]->guardian->id,
        'type' => GuardianType::MOTHER->value,
        'studenable_type' => Student::class,
        'studenable_id' => $students[0]->id
    ]);

    $user_guardians2 = User::factory(2)->withGuardian()->create(); // 2 guardians for $students[1]

    $gs3 = GuardianStudent::factory()->create([
        'guardian_id' => $user_guardians2[0]->guardian->id,
        'type' => GuardianType::FATHER->value,
        'studenable_type' => Student::class,
        'studenable_id' => $students[1]->id
    ]);

    $gs4 = GuardianStudent::factory()->create([
        'guardian_id' => $user_guardians2[1]->guardian->id,
        'type' => GuardianType::MOTHER->value,
        'studenable_type' => Student::class,
        'studenable_id' => $students[1]->id
    ]);

    $user_guardians3 = User::factory(2)->withGuardian()->create(); // 2 guardians for $students[2]

    $gs5 = GuardianStudent::factory()->create([
        'guardian_id' => $user_guardians3[0]->guardian->id,
        'type' => GuardianType::FATHER->value,
        'studenable_type' => Student::class,
        'studenable_id' => $students[2]->id
    ]);

    $gs6 = GuardianStudent::factory()->create([
        'guardian_id' => $user_guardians3[1]->guardian->id,
        'type' => GuardianType::MOTHER->value,
        'studenable_type' => Student::class,
        'studenable_id' => $students[2]->id
    ]);


    StudentClass::factory()->create([
        'semester_setting_id' => $sem1->id,
        'semester_class_id' => $semester_classes[0]->id, // Grade 1
        'student_id' => $students[0]->id,
        'class_enter_date' => now()->toDateString()
    ]);

    StudentClass::factory()->create([
        'semester_setting_id' => $sem1->id,
        'semester_class_id' => $semester_classes[0]->id, // Grade 1
        'student_id' => $students[1]->id,
        'class_enter_date' => now()->toDateString()
    ]);

    StudentClass::factory()->create([
        'semester_setting_id' => $sem1->id,
        'semester_class_id' => $semester_classes[0]->id, // Grade 1
        'student_id' => $students[2]->id,
        'class_enter_date' => now()->toDateString()
    ]);

    StudentClass::factory()->create([
        'semester_setting_id' => $sem1->id,
        'semester_class_id' => $semester_classes[0]->id, // Grade 1
        'student_id' => $students[3]->id,
        'class_enter_date' => now()->toDateString()
    ]);

    $hostel_block = HostelBlock::factory()->create([
        'name->en' => 'Block A',
    ]);

    $hostel_room1 = HostelRoom::factory()->create([
        'name' => 'Room 1',
        'hostel_block_id' => $hostel_block->id,
    ]);

    $hostel_bed2 = HostelRoomBed::factory()->create([
        'name' => 'Bed 2',
        'hostel_room_id' => $hostel_room1->id,
    ]);

    $hostel_bed1 = HostelRoomBed::factory()->create([
        'name' => 'Bed 1',
        'hostel_room_id' => $hostel_room1->id,
    ]);


    $bed_assignment1 = HostelBedAssignment::factory()->create([
        'hostel_room_bed_id' => $hostel_bed1->id,
        'assignable_id' => $students[0]->id,
        'assignable_type' => Student::class,
        'start_date' => now()->toDateString()
    ]);

    $bed_assignment2 = HostelBedAssignment::factory()->create([
        'hostel_room_bed_id' => $hostel_bed2->id,
        'assignable_id' => $students[1]->id,
        'assignable_type' => Student::class,
        'start_date' => now()->toDateString()
    ]);


    // expect get all guardians for each students
    $response = $hostelReportService->getBoardersContactInfoReportData([
        'semester_setting_id' => $sem1->id,
    ])->keyBy('student_id');

    // student[0] has 2 guardians, bed assignment in bed1
    expect($response[$students[0]->id])->toEqual([
        'student_id' => $students[0]->id,
        'student_number' => $students[0]->student_number,
        'student_name' => $students[0]->getTranslations('name'),
        'student_nric' => $students[0]->nric,
        'student_address' => $students[0]->address,
        'student_phone_number' => $students[0]->phone_number,
        'student_date_of_birth' => $students[0]->date_of_birth,
        'start_date' => $bed_assignment1->start_date->toDateString(),
        'end_date' => $bed_assignment1->end_date?->toDateString(),
        'block_name' => $hostel_block->getTranslation('name', app()->getLocale()),
        'bed_name' => $hostel_bed1->name,
        'room_name' => $hostel_room1->name,
        'class_name' => $classes[0]->getTranslations('name'),
        'grade_name' => $grade1->getTranslations('name'),
        'guardians' => [
            [
                'id' => $user_guardians[0]->guardian->id,
                'name' => $user_guardians[0]->guardian->getTranslations('name'),
                'nric' => $user_guardians[0]->guardian->nric,
                'phone_number' => $user_guardians[0]->guardian->phone_number,
                'pivot' => $gs1->toArray(),
            ],
            [
                'id' => $user_guardians[1]->guardian->id,
                'name' => $user_guardians[1]->guardian->getTranslations('name'),
                'nric' => $user_guardians[1]->guardian->nric,
                'phone_number' => $user_guardians[1]->guardian->phone_number,
                'pivot' => $gs2->toArray(),
            ],
        ]
    ]);

    // student[1] has 2 guardians, bed assignment in bed2
    expect($response[$students[1]->id])->toEqual([
        'student_id' => $students[1]->id,
        'student_number' => $students[1]->student_number,
        'student_name' => $students[1]->getTranslations('name'),
        'student_nric' => $students[1]->nric,
        'student_address' => $students[1]->address,
        'student_phone_number' => $students[1]->phone_number,
        'student_date_of_birth' => $students[1]->date_of_birth,
        'start_date' => $bed_assignment2->start_date->toDateString(),
        'end_date' => $bed_assignment2->end_date?->toDateString(),
        'block_name' => $hostel_block->getTranslation('name', app()->getLocale()),
        'bed_name' => $hostel_bed2->name,
        'room_name' => $hostel_room1->name,
        'class_name' => $classes[0]->getTranslations('name'),
        'grade_name' => $grade1->getTranslations('name'),
        'guardians' => [
            [
                'id' => $user_guardians2[0]->guardian->id,
                'name' => $user_guardians2[0]->guardian->getTranslations('name'),
                'nric' => $user_guardians2[0]->guardian->nric,
                'phone_number' => $user_guardians2[0]->guardian->phone_number,
                'pivot' => $gs3->toArray(),
            ],
            [
                'id' => $user_guardians2[1]->guardian->id,
                'name' => $user_guardians2[1]->guardian->getTranslations('name'),
                'nric' => $user_guardians2[1]->guardian->nric,
                'phone_number' => $user_guardians2[1]->guardian->phone_number,
                'pivot' => $gs4->toArray(),
            ],
        ]
    ]);

    // student[2] has 2 guardians, hoas no bed assignment
    expect($response[$students[2]->id])->toEqual([
        'student_id' => $students[2]->id,
        'student_number' => $students[2]->student_number,
        'student_name' => $students[2]->getTranslations('name'),
        'student_nric' => $students[2]->nric,
        'student_address' => $students[2]->address,
        'student_phone_number' => $students[2]->phone_number,
        'student_date_of_birth' => $students[2]->date_of_birth,
        'start_date' => null,
        'end_date' => null,
        'block_name' => null,
        'bed_name' => null,
        'room_name' => null,
        'class_name' => $classes[0]->getTranslations('name'),
        'grade_name' => $grade1->getTranslations('name'),
        'guardians' => [
            [
                'id' => $user_guardians3[0]->guardian->id,
                'name' => $user_guardians3[0]->guardian->getTranslations('name'),
                'nric' => $user_guardians3[0]->guardian->nric,
                'phone_number' => $user_guardians3[0]->guardian->phone_number,
                'pivot' => $gs5->toArray(),
            ],
            [
                'id' => $user_guardians3[1]->guardian->id,
                'name' => $user_guardians3[1]->guardian->getTranslations('name'),
                'nric' => $user_guardians3[1]->guardian->nric,
                'phone_number' => $user_guardians3[1]->guardian->phone_number,
                'pivot' => $gs6->toArray(),
            ],
        ]
    ]);

    // student[3] has no guardians, hoas no bed assignment
    expect($response[$students[3]->id])->toEqual([
        'student_id' => $students[3]->id,
        'student_number' => $students[3]->student_number,
        'student_name' => $students[3]->getTranslations('name'),
        'student_nric' => $students[3]->nric,
        'student_address' => $students[3]->address,
        'student_phone_number' => $students[3]->phone_number,
        'student_date_of_birth' => $students[3]->date_of_birth,
        'start_date' => null,
        'end_date' => null,
        'block_name' => null,
        'bed_name' => null,
        'room_name' => null,
        'class_name' => $classes[0]->getTranslations('name'),
        'grade_name' => $grade1->getTranslations('name'),
        'guardians' => []
    ]);
});

test('getBoardersContactInfoReportData, download excel / pdf', function () {
    $sem1 = SemesterSetting::factory()->create([
        'name' => 'Semester 1'
    ]);

    $sem2 = SemesterSetting::factory()->create([
        'name' => 'Semester 2'
    ]);

    $grade1 = Grade::factory()->create([
        'name->en' => 'Junior 1'
    ]);

    $grade2 = Grade::factory()->create([
        'name->en' => 'Junior 2'
    ]);

    $classes = ClassModel::factory(2)->create(new Sequence(
        [
            'name->en' => 'J111',
            'type' => ClassType::PRIMARY,
            'grade_id' => $grade1->id
        ],
        [
            'name->en' => 'J211',
            'type' => ClassType::PRIMARY,
            'grade_id' => $grade2->id
        ],
    ));

    $semester_classes = SemesterClass::factory(4)->create(new Sequence(
        [
            'semester_setting_id' => $sem1->id,
            'class_id' => $classes[0]->id // Grade 1
        ],
        [
            'semester_setting_id' => $sem1->id,
            'class_id' => $classes[1]->id // Grade 2
        ],
        [
            'semester_setting_id' => $sem2->id,
            'class_id' => $classes[0]->id // Grade 1
        ],
        [
            'semester_setting_id' => $sem2->id,
            'class_id' => $classes[1]->id // Grade 2
        ],
    ));

    $students = Student::factory()->count(4)->create([
        'is_hostel' => true,
    ]);

    $user_guardians = User::factory(2)->withGuardian()->create(); // 2 guardians for $students[0]

    $gs1 = GuardianStudent::factory()->create([
        'guardian_id' => $user_guardians[0]->guardian->id,
        'type' => GuardianType::FATHER->value,
        'studenable_type' => Student::class,
        'studenable_id' => $students[0]->id
    ]);

    $gs2 = GuardianStudent::factory()->create([
        'guardian_id' => $user_guardians[1]->guardian->id,
        'type' => GuardianType::MOTHER->value,
        'studenable_type' => Student::class,
        'studenable_id' => $students[0]->id
    ]);

    $user_guardians2 = User::factory(2)->withGuardian()->create(); // 2 guardians for $students[1]

    $gs3 = GuardianStudent::factory()->create([
        'guardian_id' => $user_guardians2[0]->guardian->id,
        'type' => GuardianType::FATHER->value,
        'studenable_type' => Student::class,
        'studenable_id' => $students[1]->id
    ]);

    $gs4 = GuardianStudent::factory()->create([
        'guardian_id' => $user_guardians2[1]->guardian->id,
        'type' => GuardianType::MOTHER->value,
        'studenable_type' => Student::class,
        'studenable_id' => $students[1]->id
    ]);

    $user_guardians3 = User::factory(2)->withGuardian()->create(); // 2 guardians for $students[2]

    $gs5 = GuardianStudent::factory()->create([
        'guardian_id' => $user_guardians3[0]->guardian->id,
        'type' => GuardianType::FATHER->value,
        'studenable_type' => Student::class,
        'studenable_id' => $students[2]->id
    ]);

    $gs6 = GuardianStudent::factory()->create([
        'guardian_id' => $user_guardians3[1]->guardian->id,
        'type' => GuardianType::MOTHER->value,
        'studenable_type' => Student::class,
        'studenable_id' => $students[2]->id
    ]);


    StudentClass::factory()->create([
        'semester_setting_id' => $sem1->id,
        'semester_class_id' => $semester_classes[0]->id, // Grade 1
        'student_id' => $students[0]->id,
        'class_enter_date' => now()->toDateString()
    ]);

    StudentClass::factory()->create([
        'semester_setting_id' => $sem1->id,
        'semester_class_id' => $semester_classes[0]->id, // Grade 1
        'student_id' => $students[1]->id,
        'class_enter_date' => now()->toDateString()
    ]);

    StudentClass::factory()->create([
        'semester_setting_id' => $sem1->id,
        'semester_class_id' => $semester_classes[0]->id, // Grade 1
        'student_id' => $students[2]->id,
        'class_enter_date' => now()->toDateString()
    ]);

    StudentClass::factory()->create([
        'semester_setting_id' => $sem1->id,
        'semester_class_id' => $semester_classes[0]->id, // Grade 1
        'student_id' => $students[3]->id,
        'class_enter_date' => now()->toDateString()
    ]);


    $hostel_room1 = HostelRoom::factory()->create([
        'name' => 'Room 1'
    ]);

    $hostel_bed2 = HostelRoomBed::factory()->create([
        'name' => 'Bed 2',
        'hostel_room_id' => $hostel_room1->id,
    ]);

    $hostel_bed1 = HostelRoomBed::factory()->create([
        'name' => 'Bed 1',
        'hostel_room_id' => $hostel_room1->id,
    ]);


    $bed_assignment1 = HostelBedAssignment::factory()->create([
        'hostel_room_bed_id' => $hostel_bed1->id,
        'assignable_id' => $students[0]->id,
        'assignable_type' => Student::class,
        'start_date' => now()->toDateString()
    ]);

    $bed_assignment2 = HostelBedAssignment::factory()->create([
        'hostel_room_bed_id' => $hostel_bed2->id,
        'assignable_id' => $students[1]->id,
        'assignable_type' => Student::class,
        'start_date' => now()->toDateString()
    ]);


    /**
     * download start
     */
    $this->partialMock(ReportPrintService::class, function (MockInterface $mock) {
        $mock->shouldReceive('upload')->twice()->andReturnSelf();
        $mock->shouldReceive('getFileUrl')->twice()->andReturn('some-random-url');
    });

    $hostelReportService = resolve(HostelReportService::class);

    $payload = [
        'export_type' => ExportType::EXCEL->value,
        'semester_setting_id' => $sem1->id,
    ];

    $title = __('hostel.title.boarders_contact', ['semester_setting' => $sem1->name]);

    $file_name = 'hostels-report-by-boarders-contact-information';

    $expected_headers = [
        'Room',
        'Bed',
        'Student No.',
        'Student Name',
        'Class',
        'Guardian Type',
        'Name',
        'NRIC',
        'Phone Number',
    ];

    // Test Excel
    Excel::fake();

    $view_name = 'reports.hostels.by-boarders-contact-information';

    $hostelReportService
        ->setExportType(ExportType::EXCEL->value)
        ->setReportViewName($view_name)
        ->setFileName('hostels-report-by-boarders-contact-information')
        ->getBoardersContactInfoReportData($payload);

    Excel::assertStored(
        DocumentPrintService::PRINT_FOLDER . DIRECTORY_SEPARATOR . $file_name . '.xlsx',
        'local',
        function (GenericExcelExportViaView $export) use (
            $expected_headers,
            $view_name,
            $title,
            $students,
            $hostel_bed1,
            $hostel_bed2,
            $hostel_room1,
            $classes,
            $grade1,
            $user_guardians,
            $user_guardians2,
            $user_guardians3,
            $gs1,
            $gs2,
            $gs3,
            $gs4,
            $gs5,
            $gs6,
        ) {
            expect($export->view()->name())->toBe($view_name);

            $view = $this->view($export->view()->name(), $export->view()->getData());

            $view->assertSee($title);

            // Add th tag to the headers
            $expected_headers = array_map(function ($header) {
                return "<th>$header</th>";
            }, $expected_headers);

            $view->assertSeeInOrder($expected_headers, false);

            foreach ($students as $student) {
                $view->assertSee($student->student_number);
                $view->assertSee($student->getTranslation('name', 'en'));
                $view->assertSee($student->getTranslation('name', 'zh'));
            }

            $view->assertSee($hostel_bed1->name);
            $view->assertSee($hostel_bed2->name);
            $view->assertSee($hostel_room1->name);

            $view->assertSee($classes[0]->getTranslation('name', 'en'));

            $view->assertSee($user_guardians[0]->guardian->getTranslation('name', 'en'));
            $view->assertSee($user_guardians[0]->guardian->getTranslation('name', 'zh'));
            $view->assertSee($user_guardians[0]->guardian->nric);
            $view->assertSee($user_guardians[0]->guardian->phone_number);

            $view->assertSee($user_guardians[1]->guardian->getTranslation('name', 'en'));
            $view->assertSee($user_guardians[1]->guardian->getTranslation('name', 'zh'));
            $view->assertSee($user_guardians[1]->guardian->nric);
            $view->assertSee($user_guardians[1]->guardian->phone_number);

            $view->assertSee($user_guardians2[0]->guardian->getTranslation('name', 'en'));
            $view->assertSee($user_guardians2[0]->guardian->getTranslation('name', 'zh'));
            $view->assertSee($user_guardians2[0]->guardian->nric);
            $view->assertSee($user_guardians2[0]->guardian->phone_number);

            $view->assertSee($user_guardians2[1]->guardian->getTranslation('name', 'en'));
            $view->assertSee($user_guardians2[1]->guardian->getTranslation('name', 'zh'));
            $view->assertSee($user_guardians2[1]->guardian->nric);
            $view->assertSee($user_guardians2[1]->guardian->phone_number);

            $view->assertSee($user_guardians3[0]->guardian->getTranslation('name', 'en'));
            $view->assertSee($user_guardians3[0]->guardian->getTranslation('name', 'zh'));
            $view->assertSee($user_guardians3[0]->guardian->nric);
            $view->assertSee($user_guardians3[0]->guardian->phone_number);

            $view->assertSee($user_guardians3[1]->guardian->getTranslation('name', 'en'));
            $view->assertSee($user_guardians3[1]->guardian->getTranslation('name', 'zh'));
            $view->assertSee($user_guardians3[1]->guardian->nric);
            $view->assertSee($user_guardians3[1]->guardian->phone_number);

            $view->assertSee($gs1->type);
            $view->assertSee($gs2->type);
            $view->assertSee($gs3->type);
            $view->assertSee($gs4->type);
            $view->assertSee($gs5->type);
            $view->assertSee($gs6->type);

            return true;
        }
    );

    // Test PDF
    SnappyPdf::fake();

    $hostelReportService
        ->setExportType(ExportType::PDF->value)
        ->setReportViewName($view_name)
        ->setFileName('hostels-report-by-boarders-contact-information')
        ->getBoardersContactInfoReportData($payload);

    SnappyPdf::assertFileNameIs(storage_path('app' . DIRECTORY_SEPARATOR . DocumentPrintService::PRINT_FOLDER . DIRECTORY_SEPARATOR . $file_name . '.pdf'));
    SnappyPdf::assertViewIs('reports.hostels.by-boarders-contact-information');

    SnappyPdf::assertSee($title);

    foreach ($expected_headers as $header) {
        SnappyPdf::assertSee($header);
    }

    foreach ($students as $student) {
        SnappyPdf::assertSee($student->student_number);
        SnappyPdf::assertSee(e($student->getTranslation('name', 'en')));
        SnappyPdf::assertSee(e($student->getTranslation('name', 'zh')));
    }

    SnappyPdf::assertSee($hostel_bed1->name);
    SnappyPdf::assertSee($hostel_bed2->name);
    SnappyPdf::assertSee($hostel_room1->name);

    SnappyPdf::assertSee(e($classes[0]->getTranslation('name', 'en')));

    SnappyPdf::assertSee(e($user_guardians[0]->guardian->getTranslation('name', 'en')));
    SnappyPdf::assertSee(e($user_guardians[0]->guardian->getTranslation('name', 'zh')));
    SnappyPdf::assertSee($user_guardians[0]->guardian->nric);
    SnappyPdf::assertSee($user_guardians[0]->guardian->phone_number);

    SnappyPdf::assertSee(e($user_guardians[1]->guardian->getTranslation('name', 'en')));
    SnappyPdf::assertSee(e($user_guardians[1]->guardian->getTranslation('name', 'zh')));
    SnappyPdf::assertSee($user_guardians[1]->guardian->nric);
    SnappyPdf::assertSee($user_guardians[1]->guardian->phone_number);

    SnappyPdf::assertSee(e($user_guardians2[0]->guardian->getTranslation('name', 'en')));
    SnappyPdf::assertSee(e($user_guardians2[0]->guardian->getTranslation('name', 'zh')));
    SnappyPdf::assertSee($user_guardians2[0]->guardian->nric);
    SnappyPdf::assertSee($user_guardians2[0]->guardian->phone_number);

    SnappyPdf::assertSee(e($user_guardians2[1]->guardian->getTranslation('name', 'en')));
    SnappyPdf::assertSee(e($user_guardians2[1]->guardian->getTranslation('name', 'zh')));
    SnappyPdf::assertSee($user_guardians2[1]->guardian->nric);
    SnappyPdf::assertSee($user_guardians2[1]->guardian->phone_number);

    SnappyPdf::assertSee(e($user_guardians3[0]->guardian->getTranslation('name', 'en')));
    SnappyPdf::assertSee(e($user_guardians3[0]->guardian->getTranslation('name', 'zh')));
    SnappyPdf::assertSee($user_guardians3[0]->guardian->nric);
    SnappyPdf::assertSee($user_guardians3[0]->guardian->phone_number);

    SnappyPdf::assertSee(e($user_guardians3[1]->guardian->getTranslation('name', 'en')));
    SnappyPdf::assertSee(e($user_guardians3[1]->guardian->getTranslation('name', 'zh')));
    SnappyPdf::assertSee($user_guardians3[1]->guardian->nric);
    SnappyPdf::assertSee($user_guardians3[1]->guardian->phone_number);

    SnappyPdf::assertSee($gs1->type->value);
    SnappyPdf::assertSee($gs2->type->value);
    SnappyPdf::assertSee($gs3->type->value);
    SnappyPdf::assertSee($gs4->type->value);
    SnappyPdf::assertSee($gs5->type->value);
    SnappyPdf::assertSee($gs6->type->value);
});

test('getBoardersDateOfBirthReportData, preview data', function () {
    $hostelReportService = resolve(HostelReportService::class);

    $sem1 = SemesterSetting::factory()->create([
        'name' => 'Semester 1'
    ]);

    $sem2 = SemesterSetting::factory()->create([
        'name' => 'Semester 2'
    ]);

    $grade1 = Grade::factory()->create([
        'name->en' => 'Junior 1'
    ]);

    $grade2 = Grade::factory()->create([
        'name->en' => 'Junior 2'
    ]);

    $classes = ClassModel::factory(2)->create(new Sequence(
        [
            'name->en' => 'J111',
            'type' => ClassType::PRIMARY,
            'grade_id' => $grade1->id
        ],
        [
            'name->en' => 'J211',
            'type' => ClassType::PRIMARY,
            'grade_id' => $grade2->id
        ],
    ));

    $semester_classes = SemesterClass::factory(4)->create(new Sequence(
        [
            'semester_setting_id' => $sem1->id,
            'class_id' => $classes[0]->id // Grade 1
        ],
        [
            'semester_setting_id' => $sem1->id,
            'class_id' => $classes[1]->id // Grade 2
        ],
        [
            'semester_setting_id' => $sem2->id,
            'class_id' => $classes[0]->id // Grade 1
        ],
        [
            'semester_setting_id' => $sem2->id,
            'class_id' => $classes[1]->id // Grade 2
        ],
    ));

    $students = Student::factory()->count(4)->create([
        'is_hostel' => true
    ]);

    $user_guardians = User::factory(2)->withGuardian()->create(); // 2 guardians for $students[0]

    $gs1 = GuardianStudent::factory()->create([
        'guardian_id' => $user_guardians[0]->guardian->id,
        'type' => GuardianType::FATHER->value,
        'studenable_type' => Student::class,
        'studenable_id' => $students[0]->id
    ]);

    $gs2 = GuardianStudent::factory()->create([
        'guardian_id' => $user_guardians[1]->guardian->id,
        'type' => GuardianType::MOTHER->value,
        'studenable_type' => Student::class,
        'studenable_id' => $students[0]->id
    ]);

    $user_guardians2 = User::factory(2)->withGuardian()->create(); // 2 guardians for $students[1]

    $gs3 = GuardianStudent::factory()->create([
        'guardian_id' => $user_guardians2[0]->guardian->id,
        'type' => GuardianType::FATHER->value,
        'studenable_type' => Student::class,
        'studenable_id' => $students[1]->id
    ]);

    $gs4 = GuardianStudent::factory()->create([
        'guardian_id' => $user_guardians2[1]->guardian->id,
        'type' => GuardianType::MOTHER->value,
        'studenable_type' => Student::class,
        'studenable_id' => $students[1]->id
    ]);

    $user_guardians3 = User::factory(2)->withGuardian()->create(); // 2 guardians for $students[2]

    $gs5 = GuardianStudent::factory()->create([
        'guardian_id' => $user_guardians3[0]->guardian->id,
        'type' => GuardianType::FATHER->value,
        'studenable_type' => Student::class,
        'studenable_id' => $students[2]->id
    ]);

    $gs6 = GuardianStudent::factory()->create([
        'guardian_id' => $user_guardians3[1]->guardian->id,
        'type' => GuardianType::MOTHER->value,
        'studenable_type' => Student::class,
        'studenable_id' => $students[2]->id
    ]);


    StudentClass::factory()->create([
        'semester_setting_id' => $sem1->id,
        'semester_class_id' => $semester_classes[0]->id, // Grade 1
        'student_id' => $students[0]->id,
        'class_enter_date' => now()->toDateString()
    ]);

    StudentClass::factory()->create([
        'semester_setting_id' => $sem1->id,
        'semester_class_id' => $semester_classes[0]->id, // Grade 1
        'student_id' => $students[1]->id,
        'class_enter_date' => now()->toDateString()
    ]);

    StudentClass::factory()->create([
        'semester_setting_id' => $sem1->id,
        'semester_class_id' => $semester_classes[0]->id, // Grade 1
        'student_id' => $students[2]->id,
        'class_enter_date' => now()->toDateString()
    ]);

    StudentClass::factory()->create([
        'semester_setting_id' => $sem1->id,
        'semester_class_id' => $semester_classes[0]->id, // Grade 1
        'student_id' => $students[3]->id,
        'class_enter_date' => now()->toDateString()
    ]);

    $hostel_block = HostelBlock::factory()->create([
        'name->en' => 'Block A',
    ]);

    $hostel_room1 = HostelRoom::factory()->create([
        'name' => 'Room 1',
        'hostel_block_id' => $hostel_block->id,
    ]);

    $hostel_bed2 = HostelRoomBed::factory()->create([
        'name' => 'Bed 2',
        'hostel_room_id' => $hostel_room1->id,
    ]);

    $hostel_bed1 = HostelRoomBed::factory()->create([
        'name' => 'Bed 1',
        'hostel_room_id' => $hostel_room1->id,
    ]);


    $bed_assignment1 = HostelBedAssignment::factory()->create([
        'hostel_room_bed_id' => $hostel_bed1->id,
        'assignable_id' => $students[0]->id,
        'assignable_type' => Student::class,
        'start_date' => now()->toDateString()
    ]);

    $bed_assignment2 = HostelBedAssignment::factory()->create([
        'hostel_room_bed_id' => $hostel_bed2->id,
        'assignable_id' => $students[1]->id,
        'assignable_type' => Student::class,
        'start_date' => now()->toDateString()
    ]);


    // expect get no guardians for each students
    $response = $hostelReportService->getBoardersDateOfBirthReportData([
        'semester_setting_id' => $sem1->id,
    ])->keyBy('student_id');


    // student[0] has 2 guardians, bed assignment in bed1, see no guardians
    expect($response[$students[0]->id])->toEqual([
        'student_id' => $students[0]->id,
        'student_number' => $students[0]->student_number,
        'student_name' => $students[0]->getTranslations('name'),
        'student_nric' => $students[0]->nric,
        'student_address' => $students[0]->address,
        'student_phone_number' => $students[0]->phone_number,
        'student_date_of_birth' => $students[0]->date_of_birth,
        'start_date' => $bed_assignment1->start_date->toDateString(),
        'end_date' => $bed_assignment1->end_date?->toDateString(),
        'block_name' => $hostel_block->getTranslation('name', app()->getLocale()),
        'bed_name' => $hostel_bed1->name,
        'room_name' => $hostel_room1->name,
        'class_name' => $classes[0]->getTranslations('name'),
        'grade_name' => $grade1->getTranslations('name'),
        'guardians' => []
    ]);

    // student[1] has 2 guardians, bed assignment in bed2, see no guardians
    expect($response[$students[1]->id])->toEqual([
        'student_id' => $students[1]->id,
        'student_number' => $students[1]->student_number,
        'student_name' => $students[1]->getTranslations('name'),
        'student_nric' => $students[1]->nric,
        'student_address' => $students[1]->address,
        'student_phone_number' => $students[1]->phone_number,
        'student_date_of_birth' => $students[1]->date_of_birth,
        'start_date' => $bed_assignment2->start_date->toDateString(),
        'end_date' => $bed_assignment2->end_date?->toDateString(),
        'block_name' => $hostel_block->getTranslation('name', app()->getLocale()),
        'bed_name' => $hostel_bed2->name,
        'room_name' => $hostel_room1->name,
        'class_name' => $classes[0]->getTranslations('name'),
        'grade_name' => $grade1->getTranslations('name'),
        'guardians' => []
    ]);

    // student[2] has 2 guardians, hoas no bed assignment, see no guardians
    expect($response[$students[2]->id])->toEqual([
        'student_id' => $students[2]->id,
        'student_number' => $students[2]->student_number,
        'student_name' => $students[2]->getTranslations('name'),
        'student_nric' => $students[2]->nric,
        'student_address' => $students[2]->address,
        'student_phone_number' => $students[2]->phone_number,
        'student_date_of_birth' => $students[2]->date_of_birth,
        'start_date' => null,
        'end_date' => null,
        'block_name' => null,
        'bed_name' => null,
        'room_name' => null,
        'class_name' => $classes[0]->getTranslations('name'),
        'grade_name' => $grade1->getTranslations('name'),
        'guardians' => []
    ]);

    // student[3] has no guardians, hoas no bed assignment
    expect($response[$students[3]->id])->toEqual([
        'student_id' => $students[3]->id,
        'student_number' => $students[3]->student_number,
        'student_name' => $students[3]->getTranslations('name'),
        'student_nric' => $students[3]->nric,
        'student_address' => $students[3]->address,
        'student_phone_number' => $students[3]->phone_number,
        'student_date_of_birth' => $students[3]->date_of_birth,
        'start_date' => null,
        'end_date' => null,
        'block_name' => null,
        'bed_name' => null,
        'room_name' => null,
        'class_name' => $classes[0]->getTranslations('name'),
        'grade_name' => $grade1->getTranslations('name'),
        'guardians' => []
    ]);
});

test('getBoardersDateOfBirthReportData, download excel / pdf', function () {
    $sem1 = SemesterSetting::factory()->create([
        'name' => 'Semester 1'
    ]);

    $sem2 = SemesterSetting::factory()->create([
        'name' => 'Semester 2'
    ]);

    $grade1 = Grade::factory()->create([
        'name->en' => 'Junior 1'
    ]);

    $grade2 = Grade::factory()->create([
        'name->en' => 'Junior 2'
    ]);

    $classes = ClassModel::factory(2)->create(new Sequence(
        [
            'name->en' => 'J111',
            'type' => ClassType::PRIMARY,
            'grade_id' => $grade1->id
        ],
        [
            'name->en' => 'J211',
            'type' => ClassType::PRIMARY,
            'grade_id' => $grade2->id
        ],
    ));

    $semester_classes = SemesterClass::factory(4)->create(new Sequence(
        [
            'semester_setting_id' => $sem1->id,
            'class_id' => $classes[0]->id // Grade 1
        ],
        [
            'semester_setting_id' => $sem1->id,
            'class_id' => $classes[1]->id // Grade 2
        ],
        [
            'semester_setting_id' => $sem2->id,
            'class_id' => $classes[0]->id // Grade 1
        ],
        [
            'semester_setting_id' => $sem2->id,
            'class_id' => $classes[1]->id // Grade 2
        ],
    ));

    $students = Student::factory(4)->create(new Sequence(
        [
            'name->en' => 'John Jones',
            'is_hostel' => true,

        ],
        [
            'name->en' => 'Dwayne',
            'is_hostel' => true,

        ],
        [
            'name->en' => 'Karen',
            'is_hostel' => true,
        ],
        [
            'name->en' => 'Kaley',
            'is_hostel' => true,
        ],
    ));

    $user_guardians = User::factory(2)->withGuardian()->create(); // 2 guardians for $students[0]

    $gs1 = GuardianStudent::factory()->create([
        'guardian_id' => $user_guardians[0]->guardian->id,
        'type' => GuardianType::FATHER->value,
        'studenable_type' => Student::class,
        'studenable_id' => $students[0]->id
    ]);

    $gs2 = GuardianStudent::factory()->create([
        'guardian_id' => $user_guardians[1]->guardian->id,
        'type' => GuardianType::MOTHER->value,
        'studenable_type' => Student::class,
        'studenable_id' => $students[0]->id
    ]);

    $user_guardians2 = User::factory(2)->withGuardian()->create(); // 2 guardians for $students[1]

    $gs3 = GuardianStudent::factory()->create([
        'guardian_id' => $user_guardians2[0]->guardian->id,
        'type' => GuardianType::FATHER->value,
        'studenable_type' => Student::class,
        'studenable_id' => $students[1]->id
    ]);

    $gs4 = GuardianStudent::factory()->create([
        'guardian_id' => $user_guardians2[1]->guardian->id,
        'type' => GuardianType::MOTHER->value,
        'studenable_type' => Student::class,
        'studenable_id' => $students[1]->id
    ]);

    $user_guardians3 = User::factory(2)->withGuardian()->create(); // 2 guardians for $students[2]

    $gs5 = GuardianStudent::factory()->create([
        'guardian_id' => $user_guardians3[0]->guardian->id,
        'type' => GuardianType::FATHER->value,
        'studenable_type' => Student::class,
        'studenable_id' => $students[2]->id
    ]);

    $gs6 = GuardianStudent::factory()->create([
        'guardian_id' => $user_guardians3[1]->guardian->id,
        'type' => GuardianType::MOTHER->value,
        'studenable_type' => Student::class,
        'studenable_id' => $students[2]->id
    ]);


    StudentClass::factory()->create([
        'semester_setting_id' => $sem1->id,
        'semester_class_id' => $semester_classes[0]->id, // Grade 1
        'student_id' => $students[0]->id,
        'class_enter_date' => now()->toDateString()
    ]);

    StudentClass::factory()->create([
        'semester_setting_id' => $sem1->id,
        'semester_class_id' => $semester_classes[0]->id, // Grade 1
        'student_id' => $students[1]->id,
        'class_enter_date' => now()->toDateString()
    ]);

    StudentClass::factory()->create([
        'semester_setting_id' => $sem1->id,
        'semester_class_id' => $semester_classes[0]->id, // Grade 1
        'student_id' => $students[2]->id,
        'class_enter_date' => now()->toDateString()
    ]);

    StudentClass::factory()->create([
        'semester_setting_id' => $sem1->id,
        'semester_class_id' => $semester_classes[0]->id, // Grade 1
        'student_id' => $students[3]->id,
        'class_enter_date' => now()->toDateString()
    ]);


    $hostel_room1 = HostelRoom::factory()->create([
        'name' => 'Room 1'
    ]);

    $hostel_bed2 = HostelRoomBed::factory()->create([
        'name' => 'Bed 2',
        'hostel_room_id' => $hostel_room1->id,
    ]);

    $hostel_bed1 = HostelRoomBed::factory()->create([
        'name' => 'Bed 1',
        'hostel_room_id' => $hostel_room1->id,
    ]);


    $bed_assignment1 = HostelBedAssignment::factory()->create([
        'hostel_room_bed_id' => $hostel_bed1->id,
        'assignable_id' => $students[0]->id,
        'assignable_type' => Student::class,
        'start_date' => now()->toDateString()
    ]);

    $bed_assignment2 = HostelBedAssignment::factory()->create([
        'hostel_room_bed_id' => $hostel_bed2->id,
        'assignable_id' => $students[1]->id,
        'assignable_type' => Student::class,
        'start_date' => now()->toDateString()
    ]);


    /**
     * download start
     */
    $this->partialMock(ReportPrintService::class, function (MockInterface $mock) {
        $mock->shouldReceive('upload')->twice()->andReturnSelf();
        $mock->shouldReceive('getFileUrl')->twice()->andReturn('some-random-url');
    });

    $hostelReportService = resolve(HostelReportService::class);

    $payload = [
        'export_type' => ExportType::EXCEL->value,
        'semester_setting_id' => $sem1->id,
    ];

    $title = __('hostel.title.boarders_date_of_birth', ['semester_setting' => $sem1->name]);

    $file_name = 'hostels-report-by-boarders-date-of-birth';

    $expected_headers = [
        'No.',
        'Room',
        'Bed No.',
        'Student No.',
        'Student Name (zh)',
        'Student Name (en)',
        'Class',
        'Date Of Birth',
    ];

    // Test Excel
    Excel::fake();

    $view_name = 'reports.hostels.by-boarders-date-of-birth';

    $hostelReportService
        ->setExportType(ExportType::EXCEL->value)
        ->setReportViewName($view_name)
        ->setFileName('hostels-report-by-boarders-date-of-birth')
        ->getBoardersDateOfBirthReportData($payload);

    Excel::assertStored(
        DocumentPrintService::PRINT_FOLDER . DIRECTORY_SEPARATOR . $file_name . '.xlsx',
        'local',
        function (GenericExcelExportViaView $export) use (
            $expected_headers,
            $view_name,
            $title,
            $students,
            $hostel_bed1,
            $hostel_bed2,
            $hostel_room1,
            $classes,
            $grade1,
        ) {
            expect($export->view()->name())->toBe($view_name);

            $view = $this->view($export->view()->name(), $export->view()->getData());

            $view->assertSee($title);

            // Add th tag to the headers
            $expected_headers = array_map(function ($header) {
                return "<th>$header</th>";
            }, $expected_headers);

            $view->assertSeeInOrder($expected_headers, false);

            foreach ($students as $student) {
                $view->assertSee($student->student_number);
                $view->assertSee($student->getTranslation('name', 'en'));
                $view->assertSee($student->getTranslation('name', 'zh'));
                $view->assertSee($student->date_of_birth);
            }

            $view->assertSee($hostel_bed1->name);
            $view->assertSee($hostel_bed2->name);
            $view->assertSee($hostel_room1->name);

            $view->assertSee($classes[0]->getTranslation('name', 'en'));

            return true;
        }
    );

    // Test PDF
    SnappyPdf::fake();

    $hostelReportService
        ->setExportType(ExportType::PDF->value)
        ->setReportViewName($view_name)
        ->setFileName('hostels-report-by-boarders-date-of-birth')
        ->getBoardersDateOfBirthReportData($payload);

    SnappyPdf::assertFileNameIs(storage_path('app' . DIRECTORY_SEPARATOR . DocumentPrintService::PRINT_FOLDER . DIRECTORY_SEPARATOR . $file_name . '.pdf'));
    SnappyPdf::assertViewIs('reports.hostels.by-boarders-date-of-birth');

    SnappyPdf::assertSee($title);

    foreach ($expected_headers as $header) {
        SnappyPdf::assertSee($header);
    }

    foreach ($students as $student) {
        SnappyPdf::assertSee($student->student_number);
        SnappyPdf::assertSee(e($student->getTranslation('name', 'en')));
        SnappyPdf::assertSee(e($student->getTranslation('name', 'zh')));
        SnappyPdf::assertSee($student->date_of_birth);
    }

    SnappyPdf::assertSee($hostel_bed1->name);
    SnappyPdf::assertSee($hostel_bed2->name);
    SnappyPdf::assertSee($hostel_room1->name);

    SnappyPdf::assertSee(e($classes[0]->getTranslation('name', 'en')));
});

test('getBoardersStaybackReportData, preview data', function () {
    $hostelReportService = resolve(HostelReportService::class);

    $sem1 = SemesterSetting::factory()->create([
        'name' => '2025 Semester 1'
    ]);

    $grades = Grade::factory(2)->create(new Sequence(
        [
            'name->en' => 'Junior 1'
        ],
        [
            'name->en' => 'Junior 2'
        ],
    ));

    $classes = ClassModel::factory(2)->create(new Sequence(
        [
            'name->en' => 'J111',
            'type' => ClassType::PRIMARY,
            'grade_id' => $grades[0]->id
        ],
        [
            'name->en' => 'J211',
            'type' => ClassType::PRIMARY,
            'grade_id' => $grades[1]->id
        ],
    ));

    $semester_classes = SemesterClass::factory(4)->create(new Sequence(
        [
            'semester_setting_id' => $sem1->id,
            'class_id' => $classes[0]->id // Grade 1
        ],
        [
            'semester_setting_id' => $sem1->id,
            'class_id' => $classes[1]->id // Grade 2
        ],
    ));


    $students = Student::factory(4)->create(new Sequence(
        [
            'name->en' => 'John Jones',
            'gender' => Gender::MALE->value,
            'is_hostel' => true,

        ],
        [
            'name->en' => 'Dwayne',
            'gender' => Gender::MALE->value,
            'is_hostel' => true,

        ],
        [
            'name->en' => 'Karen',
            'gender' => Gender::FEMALE->value,
            'is_hostel' => true,

        ],
        [
            'name->en' => 'Kaley',
            'gender' => Gender::FEMALE->value,
            'is_hostel' => true,

        ],
    ));

    StudentClass::factory()->create([
        'semester_setting_id' => $sem1->id,
        'semester_class_id' => $semester_classes[0]->id, // Grade 1
        'student_id' => $students[0]->id,
        'class_enter_date' => now()->toDateString()
    ]);

    StudentClass::factory()->create([
        'semester_setting_id' => $sem1->id,
        'semester_class_id' => $semester_classes[0]->id, // Grade 1
        'student_id' => $students[1]->id,
        'class_enter_date' => now()->toDateString()
    ]);

    StudentClass::factory()->create([
        'semester_setting_id' => $sem1->id,
        'semester_class_id' => $semester_classes[1]->id, // Grade 2
        'student_id' => $students[2]->id,
        'class_enter_date' => now()->toDateString()
    ]);

    StudentClass::factory()->create([
        'semester_setting_id' => $sem1->id,
        'semester_class_id' => $semester_classes[1]->id, // Grade 2
        'student_id' => $students[3]->id,
        'class_enter_date' => now()->toDateString()
    ]);

    $hostel_block = HostelBlock::factory()->create([
        'name->en' => 'Block A',
    ]);

    $hostel_rooms = HostelRoom::factory(2)->create(new Sequence(
        [
            'name' => 'Room 1',
            'hostel_block_id' => $hostel_block->id,
        ],
        [
            'name' => 'Room 2',
            'hostel_block_id' => $hostel_block->id,
        ],
    ));


    $hostel_beds = HostelRoomBed::factory(4)->create(new Sequence(
        [
            'name' => 'Bed 1',
            'hostel_room_id' => $hostel_rooms[1]->id,
        ],
        [
            'name' => 'Bed 2',
            'hostel_room_id' => $hostel_rooms[1]->id,
        ],
        [
            'name' => 'Bed 3',
            'hostel_room_id' => $hostel_rooms[0]->id,
        ],
        [
            'name' => 'Bed 4',
            'hostel_room_id' => $hostel_rooms[0]->id,
        ],
    ));


    $hostel_bed_assignments = HostelBedAssignment::factory(4)->create(new Sequence(
        [
            'hostel_room_bed_id' => $hostel_beds[0]->id,
            'assignable_id' => $students[0]->id,
            'assignable_type' => Student::class,
            'start_date' => now()->toDateString(),
        ],
        [
            'hostel_room_bed_id' => $hostel_beds[1]->id,
            'assignable_id' => $students[1]->id,
            'assignable_type' => Student::class,
            'start_date' => now()->toDateString(),
        ],
        [
            'hostel_room_bed_id' => $hostel_beds[2]->id,
            'assignable_id' => $students[2]->id,
            'assignable_type' => Student::class,
            'start_date' => now()->toDateString(),
        ],
        [
            'hostel_room_bed_id' => $hostel_beds[3]->id,
            'assignable_id' => $students[3]->id,
            'assignable_type' => Student::class,
            'start_date' => now()->toDateString(),
        ],
    ));


    // expect get ALL
    $response = $hostelReportService->getBoardersStaybackReportData();

    expect($response)
        ->toHaveKey('0.student_name.en', 'Karen')
        ->toHaveKey('1.student_name.en', 'Kaley')
        ->toHaveKey('2.student_name.en', 'John Jones')
        ->toHaveKey('3.student_name.en', 'Dwayne');

    /**
     *  filter by MALE students only
     */
    $payload = [
        'gender' => Gender::MALE->value,
    ];

    $response = $hostelReportService->getBoardersStaybackReportData($payload);

    expect($response)
        ->toHaveKey('0.student_name.en', 'John Jones')
        ->toHaveKey('1.student_name.en', 'Dwayne');


    /**
     *  filter by FEMALE students only
     */
    $payload = [
        'gender' => Gender::FEMALE->value,
    ];

    $response = $hostelReportService->getBoardersStaybackReportData($payload);

    expect($response)
        ->toHaveKey('0.student_name.en', 'Karen')
        ->toHaveKey('1.student_name.en', 'Kaley');
});

test('getBoardersStaybackReportData, download excel / pdf', function () {
    $sem1 = SemesterSetting::factory()->create([
        'name' => '2025 Semester 1'
    ]);

    $grades = Grade::factory(2)->create(new Sequence(
        [
            'name->en' => 'Junior 1'
        ],
        [
            'name->en' => 'Junior 2'
        ],
    ));

    $classes = ClassModel::factory(2)->create(new Sequence(
        [
            'name->en' => 'J111',
            'type' => ClassType::PRIMARY,
            'grade_id' => $grades[0]->id
        ],
        [
            'name->en' => 'J211',
            'type' => ClassType::PRIMARY,
            'grade_id' => $grades[1]->id
        ],
    ));

    $semester_classes = SemesterClass::factory(4)->create(new Sequence(
        [
            'semester_setting_id' => $sem1->id,
            'class_id' => $classes[0]->id // Grade 1
        ],
        [
            'semester_setting_id' => $sem1->id,
            'class_id' => $classes[1]->id // Grade 2
        ],
    ));


    $students = Student::factory(4)->create(new Sequence(
        [
            'name->en' => 'John Jones',
            'gender' => Gender::MALE->value,
            'is_hostel' => true,

        ],
        [
            'name->en' => 'Dwayne',
            'gender' => Gender::MALE->value,
            'is_hostel' => true,

        ],
        [
            'name->en' => 'Karen',
            'gender' => Gender::FEMALE->value,
            'is_hostel' => true,

        ],
        [
            'name->en' => 'Kaley',
            'gender' => Gender::FEMALE->value,
            'is_hostel' => true,

        ],
    ));

    StudentClass::factory()->create([
        'semester_setting_id' => $sem1->id,
        'semester_class_id' => $semester_classes[0]->id, // Grade 1
        'student_id' => $students[0]->id,
        'class_enter_date' => now()->toDateString()
    ]);

    StudentClass::factory()->create([
        'semester_setting_id' => $sem1->id,
        'semester_class_id' => $semester_classes[0]->id, // Grade 1
        'student_id' => $students[1]->id,
        'class_enter_date' => now()->toDateString()
    ]);

    StudentClass::factory()->create([
        'semester_setting_id' => $sem1->id,
        'semester_class_id' => $semester_classes[1]->id, // Grade 2
        'student_id' => $students[2]->id,
        'class_enter_date' => now()->toDateString()
    ]);

    StudentClass::factory()->create([
        'semester_setting_id' => $sem1->id,
        'semester_class_id' => $semester_classes[1]->id, // Grade 2
        'student_id' => $students[3]->id,
        'class_enter_date' => now()->toDateString()
    ]);


    $hostel_rooms = HostelRoom::factory(2)->create(new Sequence(
        [
            'name' => 'Room 1'
        ],
        [
            'name' => 'Room 2'
        ],
    ));


    $hostel_beds = HostelRoomBed::factory(4)->create(new Sequence(
        [
            'name' => 'Bed 1',
            'hostel_room_id' => $hostel_rooms[1]->id,
        ],
        [
            'name' => 'Bed 2',
            'hostel_room_id' => $hostel_rooms[1]->id,
        ],
        [
            'name' => 'Bed 3',
            'hostel_room_id' => $hostel_rooms[0]->id,
        ],
        [
            'name' => 'Bed 4',
            'hostel_room_id' => $hostel_rooms[0]->id,
        ],
    ));


    $hostel_bed_assignments = HostelBedAssignment::factory(4)->create(new Sequence(
        [
            'hostel_room_bed_id' => $hostel_beds[0]->id,
            'assignable_id' => $students[0]->id,
            'assignable_type' => Student::class,
            'start_date' => now()->toDateString(),
        ],
        [
            'hostel_room_bed_id' => $hostel_beds[1]->id,
            'assignable_id' => $students[1]->id,
            'assignable_type' => Student::class,
            'start_date' => now()->toDateString(),
        ],
        [
            'hostel_room_bed_id' => $hostel_beds[2]->id,
            'assignable_id' => $students[2]->id,
            'assignable_type' => Student::class,
            'start_date' => now()->toDateString(),
        ],
        [
            'hostel_room_bed_id' => $hostel_beds[3]->id,
            'assignable_id' => $students[3]->id,
            'assignable_type' => Student::class,
            'start_date' => now()->toDateString(),
        ],
    ));


    /**
     * download start
     */
    $this->partialMock(ReportPrintService::class, function (MockInterface $mock) {
        $mock->shouldReceive('upload')->twice()->andReturnSelf();
        $mock->shouldReceive('getFileUrl')->twice()->andReturn('some-random-url');
    });

    $hostelReportService = resolve(HostelReportService::class);

    $payload = [
        'export_type' => ExportType::EXCEL->value,
        'gender' => Gender::MALE->value,
    ];

    $today = now()->toDateString();
    $title = Gender::getLabelFromString($payload['gender']) . " Hostel Boarders ({$today})";

    $file_name = 'hostels-report-by-boarders-stayback';

    $expected_headers = [
        'No.',
        'Room - Bed',
        'Student No.',
        'Student Name',
        'Class',
        'Boarding Roll Call',
        'Friday (Evening)',
        'Friday (Bedtime)',
        'Saturday (Morning)',
        'Saturday (Evening)',
        'Saturday (Bedtime)',
        'Sunday (Morning)',
    ];

    // Test Excel
    Excel::fake();

    $view_name = 'reports.hostels.by-boarders-stayback';

    $hostelReportService
        ->setExportType(ExportType::EXCEL->value)
        ->setReportViewName($view_name)
        ->setFileName('hostels-report-by-boarders-stayback')
        ->getBoardersStaybackReportData($payload);

    Excel::assertStored(
        DocumentPrintService::PRINT_FOLDER . DIRECTORY_SEPARATOR . $file_name . '.xlsx',
        'local',
        function (GenericExcelExportViaView $export) use (
            $expected_headers,
            $view_name,
            $title,
            $students,
            $hostel_bed_assignments,
            $classes,
            $grades,
        ) {
            expect($export->view()->name())->toBe($view_name);

            $view = $this->view($export->view()->name(), $export->view()->getData());

            $view->assertSee($title);

            foreach ($expected_headers as $header) {
                $view->assertSee($header);
            }

            foreach ($students as $student) {
                if ($student->gender === Gender::MALE->value) {
                    $view->assertSee($student->student_number);
                    $view->assertSee($student->getTranslation('name', 'en'));
                    $view->assertSee($student->getTranslation('name', 'zh'));
                }
            }

            foreach ($hostel_bed_assignments as $assignment) {
                if ($assignment->assignable->gender === Gender::MALE->value) {
                    $view->assertSee($assignment->bed->name);
                    $view->assertSee($assignment->bed->hostelRoom->name);
                }
            }

            $view->assertSee($classes[0]->getTranslation('name', 'en'));

            return true;
        }
    );

    // Test PDF
    SnappyPdf::fake();

    $hostelReportService
        ->setExportType(ExportType::PDF->value)
        ->setReportViewName($view_name)
        ->setFileName('hostels-report-by-boarders-stayback')
        ->getBoardersStaybackReportData($payload);

    SnappyPdf::assertFileNameIs(storage_path('app' . DIRECTORY_SEPARATOR . DocumentPrintService::PRINT_FOLDER . DIRECTORY_SEPARATOR . $file_name . '.pdf'));
    SnappyPdf::assertViewIs('reports.hostels.by-boarders-stayback');

    foreach ($expected_headers as $header) {
        SnappyPdf::assertSee($header);
    }


    foreach ($students as $student) {
        if ($student->gender === Gender::MALE->value) {
            SnappyPdf::assertSee($student->student_number);
            SnappyPdf::assertSee(e($student->getTranslation('name', 'en')));
            SnappyPdf::assertSee(e($student->getTranslation('name', 'zh')));
        }
    }

    foreach ($hostel_bed_assignments as $assignment) {
        if ($assignment->assignable->gender === Gender::MALE->value) {
            SnappyPdf::assertSee($assignment->bed->name);
            SnappyPdf::assertSee($assignment->bed->hostelRoom->name);
        }
    }

    SnappyPdf::assertSee(e($classes[0]->getTranslation('name', 'en')));
});

test('getEmployeeLodgingReportData, download excel / pdf', function () {
    $employee_blocks = HostelBlock::factory(2)->employeeBlock()->create(new Sequence(
        [
            'name->en' => 'Block A',
        ],
        [
            'name->en' => 'Block B',
        ],
    ));

    $hostel_rooms = HostelRoom::factory(3)->create(new Sequence(
        [
            'name' => 'Room 100',
            'hostel_block_id' => $employee_blocks[0]->id, // Block A
        ],
        [
            'name' => 'Room 200',
            'hostel_block_id' => $employee_blocks[0]->id, // Block A
        ],
        [
            'name' => 'Room ZZZ',
            'hostel_block_id' => $employee_blocks[1]->id, // Block B
        ],
    ));

    $hostel_beds = HostelRoomBed::factory(3)->create(new Sequence(
        [
            'name' => 'Bed 1',
            'hostel_room_id' => $hostel_rooms[0]->id,
            'status' => HostelRoomBedStatus::OCCUPIED->value,
        ],
        [
            'name' => 'Bed 1',
            'hostel_room_id' => $hostel_rooms[1]->id,
            'status' => HostelRoomBedStatus::OCCUPIED->value,
        ],
        [
            'name' => 'Bed 1',
            'hostel_room_id' => $hostel_rooms[2]->id,
            'status' => HostelRoomBedStatus::OCCUPIED->value,
        ],
    ));

    $employees = Employee::factory(4)->create(new Sequence(
        [
            'address' => "School & 1 O'Connor. , ? ! : ; \" ( ) [ ] { } < > ... - – — / \ | _ ~ + = & * ^ % $ # @  < > ≠ ≈ ≤ ≥ ± × ÷ ∞ π √ ∫ ∑ ∏ € £ ¥ ¢ ₹ © ® ™ ° § ♥ ★ ✓ ✗ • † ‡ → ← ↑ ↓ ↔`",
            'name->en' => 'Albert',
            'gender' => Gender::MALE->value,
            'is_hostel' => true,
            'employment_start_date' => '2020-01-20',
        ],
        [
            'name->en' => 'CEO King G',
            'gender' => Gender::MALE->value,
            'is_hostel' => true,
            'employment_start_date' => '2024-05-01',
        ],
        [
            'name->en' => 'Karen',
            'gender' => Gender::FEMALE->value,
            'is_hostel' => true,
            'employment_start_date' => '2022-02-05',
        ],
        [
            'name->en' => 'Koko',
            'gender' => Gender::FEMALE->value,
            'is_hostel' => true, // no room assignment, expect to be excluded
            'employment_start_date' => '2023-01-10',
        ],
    ));

    $hostel_bed_assignments = HostelBedAssignment::factory(4)->create(new Sequence(
        [
            'hostel_room_bed_id' => $hostel_beds[0]->id,
            'assignable_id' => $employees[0]->id,
            'assignable_type' => Employee::class,
            'start_date' => now()->toDateString(),
        ],
        [
            'hostel_room_bed_id' => $hostel_beds[1]->id,
            'assignable_id' => $employees[1]->id,
            'assignable_type' => Employee::class,
            'start_date' => now()->toDateString(),
        ],
        [
            'hostel_room_bed_id' => $hostel_beds[2]->id,
            'assignable_id' => $employees[2]->id,
            'assignable_type' => Employee::class,
            'start_date' => now()->toDateString(),
        ],
    ));


    /**
     * download start
     */
    $this->partialMock(ReportPrintService::class, function (MockInterface $mock) {
        $mock->shouldReceive('upload')->twice()->andReturnSelf();
        $mock->shouldReceive('getFileUrl')->twice()->andReturn('some-random-url');
    });

    $hostelReportService = resolve(HostelReportService::class);

    $payload = [
        'export_type' => ExportType::EXCEL->value,
        'gender' => Gender::MALE->value,
    ];

    $title = 'Employee Lodging';

    $file_name = 'hostels-report-by-employee-lodging';

    $expected_headers = [
        'Block',
        'Room',
        'Employee Name (zh)',
        'Employee Name (en)',
        'Gender',
        'Phone Number',
        'Address',
        'Report Date',
    ];

    // Test Excel
    Excel::fake();

    $view_name = 'reports.hostels.by-employee-lodging';

    $hostelReportService
        ->setExportType(ExportType::EXCEL->value)
        ->setReportViewName($view_name)
        ->setFileName('hostels-report-by-employee-lodging')
        ->getEmployeeLodgingReportData($payload);

    Excel::assertStored(
        DocumentPrintService::PRINT_FOLDER . DIRECTORY_SEPARATOR . $file_name . '.xlsx',
        'local',
        function (GenericExcelExportViaView $export) use (
            $expected_headers,
            $view_name,
            $title,
            $employee_blocks,
            $hostel_rooms,
            $employees,
        ) {
            expect($export->view()->name())->toBe($view_name);

            $view = $this->view($export->view()->name(), $export->view()->getData());

            $view->assertSee($title);

            foreach ($expected_headers as $header) {
                $view->assertSee($header);
            }

            foreach ($employee_blocks as $block) {
                $view->assertSee($block->getTranslation('name', 'en'));
                $view->assertSee($block->getTranslation('name', 'zh'));
            }

            foreach ($hostel_rooms as $room) {
                $view->assertSee($room->name);
            }

            foreach ($employees as $key => $employee) {
                if ($key == 3) { // $employee[3] has no room assignment
                    continue;
                }

                $view->assertSee($employee->getTranslation('name', 'en'));
                $view->assertSee($employee->getTranslation('name', 'zh'));
                $view->assertSee($employee->gender->value);
                $view->assertSee($employee->phone_number);
                $view->assertSee($employee->address);
                $view->assertSee($employee->employment_start_date);
            }

            return true;
        }
    );


    // Test PDF
    SnappyPdf::fake();

    $hostelReportService
        ->setExportType(ExportType::PDF->value)
        ->setReportViewName($view_name)
        ->setFileName('hostels-report-by-employee-lodging')
        ->getEmployeeLodgingReportData($payload);

    SnappyPdf::assertFileNameIs(storage_path('app' . DIRECTORY_SEPARATOR . DocumentPrintService::PRINT_FOLDER . DIRECTORY_SEPARATOR . $file_name . '.pdf'));
    SnappyPdf::assertViewIs('reports.hostels.by-employee-lodging');

    SnappyPdf::assertSee($title);

    foreach ($expected_headers as $header) {
        SnappyPdf::assertSee($header);
    }

    foreach ($employee_blocks as $block) {
        SnappyPdf::assertSee(e($block->getTranslation('name', 'en')));
        SnappyPdf::assertSee(e($block->getTranslation('name', 'zh')));
    }

    foreach ($hostel_rooms as $room) {
        SnappyPdf::assertSee($room->name);
    }

    foreach ($employees as $key => $employee) {
        if ($key == 3) { // $employee[3] has no room assignment
            continue;
        }

        SnappyPdf::assertSee(e($employee->getTranslation('name', 'en')));
        SnappyPdf::assertSee(e($employee->getTranslation('name', 'zh')));
        SnappyPdf::assertSee($employee->gender->value);
        SnappyPdf::assertSee($employee->phone_number);
        SnappyPdf::assertSee(e($employee->address));
        SnappyPdf::assertSee($employee->employment_start_date);
    }
});


test('getGoHomeOrOutReportData, preview data', function () {
    $hostelReportService = resolve(HostelReportService::class);

    $warden = Employee::factory()->create([
        'name->en' => 'Warden Johnson',
    ]);

    $yet_return_students = Student::factory(2)->create(new Sequence(
        [
            'name->en' => 'Yet Return 1 Jon',
        ],
        [
            'name->en' => 'Yet Return 2 Kon',
        ],
    ));

    $leave_school_students = Student::factory(2)->create(new Sequence(
        [
            'name->en' => 'Leave School 1 David',
        ],
        [
            'name->en' => 'Leave School 2 Paulo',
        ],
    ));

    $returned_students = Student::factory(2)->create(new Sequence(
        [
            'name->en' => 'Returned 1 Charlie',
        ],
        [
            'name->en' => 'Returned 2 Frank',
        ],
    ));


    $cards = Card::factory(6)->create(new Sequence(
        [
            'card_number' => '111111',
            'userable_type' => Student::class,
            'userable_id' => $yet_return_students[0]->id,
        ],
        [
            'card_number' => '222222',
            'userable_type' => Student::class,
            'userable_id' => $yet_return_students[1]->id,
        ],
        [
            'card_number' => '333333',
            'userable_type' => Student::class,
            'userable_id' => $leave_school_students[0]->id,
        ],
        [
            'card_number' => '444444',
            'userable_type' => Student::class,
            'userable_id' => $leave_school_students[1]->id,
        ],
        [
            'card_number' => '5555555',
            'userable_type' => Student::class,
            'userable_id' => $returned_students[0]->id,
        ],
        [
            'card_number' => '666666',
            'userable_type' => Student::class,
            'userable_id' => $returned_students[1]->id,
        ],
    ));

    $hostel_block = HostelBlock::factory()->create([
        'name->en' => 'Primary Block',
    ]);

    $hostel_rooms = HostelRoom::factory(2)->create(new Sequence(
        [
            'name' => 'Room 1',
            'hostel_block_id' => $hostel_block->id,  // 3 bed in this room
        ],
        [
            'name' => 'Room AAA',
            'hostel_block_id' => $hostel_block->id,  // 3 bed in this room
        ],
    ));

    $hostel_beds = HostelRoomBed::factory(6)->create(new Sequence(
        [
            'name' => 'Bed 1',
            'hostel_room_id' => $hostel_rooms[0]->id,
        ],
        [
            'name' => 'Bed 2',
            'hostel_room_id' => $hostel_rooms[0]->id,
        ],
        [
            'name' => 'Bed 3',
            'hostel_room_id' => $hostel_rooms[0]->id,
        ],
        [
            'name' => 'Bed A1',
            'hostel_room_id' => $hostel_rooms[1]->id,
        ],
        [
            'name' => 'Bed A2',
            'hostel_room_id' => $hostel_rooms[1]->id,
        ],
        [
            'name' => 'Bed A2',
            'hostel_room_id' => $hostel_rooms[1]->id,
        ],
    ));

    $hostel_bed_assignments = HostelBedAssignment::factory(6)->create(new Sequence(
        [
            'hostel_room_bed_id' => $hostel_beds[0]->id,
            'assignable_id' => $yet_return_students[0]->id,
            'assignable_type' => Student::class,
            'start_date' => now()->toDateString(),
        ],
        [
            'hostel_room_bed_id' => $hostel_beds[1]->id,
            'assignable_id' => $yet_return_students[1]->id,
            'assignable_type' => Student::class,
            'start_date' => now()->toDateString(),
        ],
        [
            'hostel_room_bed_id' => $hostel_beds[2]->id,
            'assignable_id' => $leave_school_students[0]->id,
            'assignable_type' => Student::class,
            'start_date' => now()->toDateString(),
        ],
        [
            'hostel_room_bed_id' => $hostel_beds[3]->id,
            'assignable_id' => $leave_school_students[1]->id,
            'assignable_type' => Student::class,
            'start_date' => now()->toDateString(),
        ],
        [
            'hostel_room_bed_id' => $hostel_beds[4]->id,
            'assignable_id' => $returned_students[0]->id,
            'assignable_type' => Student::class,
            'start_date' => now()->toDateString(),
        ],
        [
            'hostel_room_bed_id' => $hostel_beds[5]->id,
            'assignable_id' => $returned_students[1]->id,
            'assignable_type' => Student::class,
            'start_date' => now()->toDateString(),
        ],
    ));

    $yet_returned_records = HostelInOutRecord::factory(2)->create(new Sequence(
        [
            'student_id' => $yet_return_students[0]->id,
            'check_out_datetime' => '2024-01-20 01:00:00',
            'check_in_datetime' => null,
            'card_no' => null,
            'type' => HostelInOutType::HOME->value,
            'check_out_by' => $warden->user_id,
        ],
        [
            'student_id' => $yet_return_students[1]->id,
            'check_out_datetime' => '2024-01-21 02:00:00',
            'check_in_datetime' => null,
            'card_no' => 'P12333',
            'type' => HostelInOutType::OUTING->value,
            'check_out_by' => $warden->user_id,
        ],
    ));

    $leave_school_records = HostelInOutRecord::factory(2)->create(new Sequence(
        [
            'student_id' => $leave_school_students[0]->id,
            'check_out_datetime' => '2024-02-20 08:00:00',
            'check_in_datetime' => null,
            'card_no' => null,
            'type' => HostelInOutType::HOME->value,
            'check_out_by' => $warden->user_id,
        ],
        [
            'student_id' => $leave_school_students[1]->id,
            'check_out_datetime' => '2024-02-20 08:00:00',
            'check_in_datetime' => '2024-02-21 08:00:00',
            'card_no' => 'P111',
            'type' => HostelInOutType::OUTING->value,
            'check_out_by' => $warden->user_id,
            'check_in_by' => $warden->user_id,
        ],
    ));

    $returned_records = HostelInOutRecord::factory(2)->create(new Sequence(
        [
            'student_id' => $returned_students[0]->id,
            'check_out_datetime' => '2024-03-01 08:00:00',
            'check_in_datetime' => '2024-03-02 08:00:00',
            'type' => HostelInOutType::HOME->value,
            'card_no' => null,
            'check_out_by' => $warden->user_id,
            'check_in_by' => $warden->user_id,
        ],
        [
            'student_id' => $returned_students[1]->id,
            'check_out_datetime' => '2024-03-01 08:00:00',
            'check_in_datetime' => '2024-03-02 08:00:00',
            'type' => HostelInOutType::OUTING->value,
            'card_no' => 'P22222',
            'check_out_by' => $warden->user_id,
            'check_in_by' => $warden->user_id,
        ],
    ));


    // ### Get yet_return students, type = HOME
    $response = $hostelReportService->getGoHomeOrOutReportData([
        'yet_return' => true,
        'in_out_type' => HostelInOutType::HOME->value,
        'date_from' => '2024-01-19',
        'date_to' => '2024-01-21',
    ]);

    expect($response)
        ->toHaveCount(1)
        ->toHaveKey('0.student_name.en', $yet_return_students[0]->getTranslation('name', 'en'));


    // ### Get yet_return students, type = OUTING
    $response = $hostelReportService->getGoHomeOrOutReportData([
        'yet_return' => true,
        'in_out_type' => HostelInOutType::OUTING->value,
        'date_from' => '2024-01-20',
        'date_to' => '2024-01-22',
    ])->toArray();


    expect($response)
        ->toHaveCount(1)
        ->toHaveKey('0.student_name.en', $yet_return_students[1]->getTranslation('name', 'en'));


    // ### Get leave_school students, type = HOME, get all leave from hostel no mattter returned or not
    $response = $hostelReportService->getGoHomeOrOutReportData([
        'leave_school' => true,
        'in_out_type' => HostelInOutType::HOME->value,
        'date_from' => '2024-02-20',
        'date_to' => '2024-02-22',
    ])->toArray();


    expect($response)
        ->toHaveCount(1)
        ->toHaveKey('0.student_name.en', $leave_school_students[0]->getTranslation('name', 'en'));


    // ### Get leave_school students, type = OUTING, get all leave from hostel no mattter returned or not
    $response = $hostelReportService->getGoHomeOrOutReportData([
        'leave_school' => true,
        'in_out_type' => HostelInOutType::OUTING->value,
        'date_from' => '2024-02-20',
        'date_to' => '2024-02-22',
    ])->toArray();


    expect($response)
        ->toHaveCount(1)
        ->toHaveKey('0.student_name.en', $leave_school_students[1]->getTranslation('name', 'en'));


    // ### Get returned students, type = HOME, get completed record from hostel
    $response = $hostelReportService->getGoHomeOrOutReportData([
        'returned' => true,
        'in_out_type' => HostelInOutType::HOME->value,
        'date_from' => '2024-03-01',
        'date_to' => '2024-03-02',
    ])->toArray();


    expect($response)
        ->toHaveCount(1)
        ->toHaveKey('0.student_name.en', $returned_students[0]->getTranslation('name', 'en'));


    // ### Get returned students, type = OUTING, get completed record from hostel
    $response = $hostelReportService->getGoHomeOrOutReportData([
        'returned' => true,
        'in_out_type' => HostelInOutType::OUTING->value,
        'date_from' => '2024-03-01',
        'date_to' => '2024-03-02',
    ])->toArray();

    expect($response)
        ->toHaveCount(1)
        ->toHaveKey('0.student_name.en', $returned_students[1]->getTranslation('name', 'en'));
});

test('getGoHomeOrOutReportData, download excel / pdf', function () {
    $warden = Employee::factory()->create([
        'name->en' => 'Warden Johnson',
    ]);

    $yet_return_students = Student::factory(2)->create(new Sequence(
        [
            'name->en' => 'Yet Return 1 Jon',
        ],
        [
            'name->en' => 'Yet Return 2 Kon',
        ],
    ));

    $leave_school_students = Student::factory(2)->create(new Sequence(
        [
            'name->en' => 'Leave School 1 David',
        ],
        [
            'name->en' => 'Leave School 2 Paulo',
        ],
    ));

    $returned_students = Student::factory(2)->create(new Sequence(
        [
            'name->en' => 'Returned 1 Charlie',
        ],
        [
            'name->en' => 'Returned 2 Frank',
        ],
    ));


    $cards = Card::factory(6)->create(new Sequence(
        [
            'card_number' => '111111',
            'userable_type' => Student::class,
            'userable_id' => $yet_return_students[0]->id,
        ],
        [
            'card_number' => '222222',
            'userable_type' => Student::class,
            'userable_id' => $yet_return_students[1]->id,
        ],
        [
            'card_number' => '333333',
            'userable_type' => Student::class,
            'userable_id' => $leave_school_students[0]->id,
        ],
        [
            'card_number' => '444444',
            'userable_type' => Student::class,
            'userable_id' => $leave_school_students[1]->id,
        ],
        [
            'card_number' => '5555555',
            'userable_type' => Student::class,
            'userable_id' => $returned_students[0]->id,
        ],
        [
            'card_number' => '666666',
            'userable_type' => Student::class,
            'userable_id' => $returned_students[1]->id,
        ],
    ));

    $hostel_block = HostelBlock::factory()->create([
        'name->en' => 'Primary Block',
    ]);

    $hostel_rooms = HostelRoom::factory(2)->create(new Sequence(
        [
            'name' => 'Room 1',
            'hostel_block_id' => $hostel_block->id,  // 3 bed in this room
        ],
        [
            'name' => 'Room AAA',
            'hostel_block_id' => $hostel_block->id,  // 3 bed in this room
        ],
    ));

    $hostel_beds = HostelRoomBed::factory(6)->create(new Sequence(
        [
            'name' => 'Bed 1',
            'hostel_room_id' => $hostel_rooms[0]->id,
        ],
        [
            'name' => 'Bed 2',
            'hostel_room_id' => $hostel_rooms[0]->id,
        ],
        [
            'name' => 'Bed 3',
            'hostel_room_id' => $hostel_rooms[0]->id,
        ],
        [
            'name' => 'Bed A1',
            'hostel_room_id' => $hostel_rooms[1]->id,
        ],
        [
            'name' => 'Bed A2',
            'hostel_room_id' => $hostel_rooms[1]->id,
        ],
        [
            'name' => 'Bed A2',
            'hostel_room_id' => $hostel_rooms[1]->id,
        ],
    ));

    $hostel_bed_assignments = HostelBedAssignment::factory(6)->create(new Sequence(
        [
            'hostel_room_bed_id' => $hostel_beds[0]->id,
            'assignable_id' => $yet_return_students[0]->id,
            'assignable_type' => Student::class,
            'start_date' => now()->toDateString(),
        ],
        [
            'hostel_room_bed_id' => $hostel_beds[1]->id,
            'assignable_id' => $yet_return_students[1]->id,
            'assignable_type' => Student::class,
            'start_date' => now()->toDateString(),
        ],
        [
            'hostel_room_bed_id' => $hostel_beds[2]->id,
            'assignable_id' => $leave_school_students[0]->id,
            'assignable_type' => Student::class,
            'start_date' => now()->toDateString(),
        ],
        [
            'hostel_room_bed_id' => $hostel_beds[3]->id,
            'assignable_id' => $leave_school_students[1]->id,
            'assignable_type' => Student::class,
            'start_date' => now()->toDateString(),
        ],
        [
            'hostel_room_bed_id' => $hostel_beds[4]->id,
            'assignable_id' => $returned_students[0]->id,
            'assignable_type' => Student::class,
            'start_date' => now()->toDateString(),
        ],
        [
            'hostel_room_bed_id' => $hostel_beds[5]->id,
            'assignable_id' => $returned_students[1]->id,
            'assignable_type' => Student::class,
            'start_date' => now()->toDateString(),
        ],
    ));

    $yet_returned_records = HostelInOutRecord::factory(2)->create(new Sequence(
        [
            'student_id' => $yet_return_students[0]->id,
            'check_out_datetime' => '2024-01-20 01:00:00',
            'check_in_datetime' => null,
            'card_no' => null,
            'type' => HostelInOutType::HOME->value,
            'check_out_by' => $warden->user_id,
        ],
        [
            'student_id' => $yet_return_students[1]->id,
            'check_out_datetime' => '2024-01-21 02:00:00',
            'check_in_datetime' => null,
            'card_no' => 'P12333',
            'type' => HostelInOutType::OUTING->value,
            'check_out_by' => $warden->user_id,
        ],
    ));

    $leave_school_records = HostelInOutRecord::factory(2)->create(new Sequence(
        [
            'student_id' => $leave_school_students[0]->id,
            'check_out_datetime' => '2024-02-20 08:00:00',
            'check_in_datetime' => null,
            'card_no' => null,
            'type' => HostelInOutType::HOME->value,
            'check_out_by' => $warden->user_id,
        ],
        [
            'student_id' => $leave_school_students[1]->id,
            'check_out_datetime' => '2024-02-20 08:00:00',
            'check_in_datetime' => '2024-02-21 08:00:00',
            'card_no' => 'P111',
            'type' => HostelInOutType::OUTING->value,
            'check_out_by' => $warden->user_id,
            'check_in_by' => $warden->user_id,
        ],
    ));

    $returned_records = HostelInOutRecord::factory(2)->create(new Sequence(
        [
            'student_id' => $returned_students[0]->id,
            'check_out_datetime' => '2024-03-01 08:00:00',
            'check_in_datetime' => '2024-03-02 08:00:00',
            'type' => HostelInOutType::HOME->value,
            'card_no' => null,
            'check_out_by' => $warden->user_id,
            'check_in_by' => $warden->user_id,
        ],
        [
            'student_id' => $returned_students[1]->id,
            'check_out_datetime' => '2024-03-01 08:00:00',
            'check_in_datetime' => '2024-03-02 08:00:00',
            'type' => HostelInOutType::OUTING->value,
            'card_no' => 'P22222',
            'check_out_by' => $warden->user_id,
            'check_in_by' => $warden->user_id,
        ],
    ));


    /**
     * download start
     */
    $this->partialMock(ReportPrintService::class, function (MockInterface $mock) {
        $mock->shouldReceive('upload')->andReturnSelf();
        $mock->shouldReceive('getFileUrl')->andReturn('some-random-url');
    });

    $hostelReportService = resolve(HostelReportService::class);

    /**
     *
     *
     *
     *
     * ### download yet_return students, type = HOME
     *
     *
     *
     *
     */
    $payload = [
        'export_type' => ExportType::EXCEL->value,
        'yet_return' => true,
        'in_out_type' => HostelInOutType::HOME->value,
        'date_from' => '2024-01-19',
        'date_to' => '2024-01-21',
    ];

    $title = "Hostel Boarders Go Home Yet Return (2024-01-19 - 2024-01-21)";

    $file_name = 'hostels-report-by-boarders-go-home-or-out';

    $expected_headers = [
        'No.',
        'Room',
        'Student No.',
        'Name',
        'Go Home Date',
        'Go Home Time',
        'Create By',
        'Return Date',
        'Return Time',
        'Signing Parent',
        'Update By',
        'Reason',
    ];

    // Test Excel
    Excel::fake();

    $view_name = 'reports.hostels.by-boarders-go-home';

    $hostelReportService
        ->setExportType(ExportType::EXCEL->value)
        ->setReportViewName($view_name)
        ->setFileName('hostels-report-by-boarders-go-home-or-out')
        ->getGoHomeOrOutReportData($payload);

    Excel::assertStored(
        DocumentPrintService::PRINT_FOLDER . DIRECTORY_SEPARATOR . $file_name . '.xlsx',
        'local',
        function (GenericExcelExportViaView $export) use (
            $expected_headers,
            $view_name,
            $title,
            $hostel_rooms,
            $yet_return_students,
            $yet_returned_records,
            $warden,
        ) {
            expect($export->view()->name())->toBe($view_name);

            $view = $this->view($export->view()->name(), $export->view()->getData());

            $view->assertSee($title);

            foreach ($expected_headers as $header) {
                $view->assertSee($header);
            }

            $view->assertSee($hostel_rooms[0]->name);
            $view->assertSee($yet_return_students[0]->student_number);
            $view->assertSee($yet_return_students[0]->getTranslation('name', 'en'));
            $view->assertSee(Carbon::parse($yet_returned_records[0]->check_out_datetime)->setTimezone(config('school.timezone'))->toDateString());
            $view->assertSee(Carbon::parse($yet_returned_records[0]->check_out_datetime)->setTimezone(config('school.timezone'))->format('h:i A'));
            $view->assertSee($yet_returned_records[0]->reason);
            $view->assertSee($yet_returned_records[0]->guardian->nric);
            $view->assertSee($warden->getTranslation('name', 'en'));

            return true;
        }
    );

    // Test PDF
    SnappyPdf::fake();

    $hostelReportService
        ->setExportType(ExportType::PDF->value)
        ->setReportViewName($view_name)
        ->setFileName('hostels-report-by-boarders-go-home-or-out')
        ->getGoHomeOrOutReportData($payload);

    SnappyPdf::assertFileNameIs(storage_path('app' . DIRECTORY_SEPARATOR . DocumentPrintService::PRINT_FOLDER . DIRECTORY_SEPARATOR . $file_name . '.pdf'));
    SnappyPdf::assertViewIs('reports.hostels.by-boarders-go-home');

    SnappyPdf::assertSee($title);

    foreach ($expected_headers as $header) {
        SnappyPdf::assertSee($header);
    }

    SnappyPdf::assertSee($hostel_rooms[0]->name);
    SnappyPdf::assertSee($yet_return_students[0]->student_number);
    SnappyPdf::assertSee(e($yet_return_students[0]->getTranslation('name', 'en')));
    SnappyPdf::assertSee(Carbon::parse($yet_returned_records[0]->check_out_datetime)->setTimezone(config('school.timezone'))->toDateString());
    SnappyPdf::assertSee(Carbon::parse($yet_returned_records[0]->check_out_datetime)->setTimezone(config('school.timezone'))->format('h:i A'));
    SnappyPdf::assertSee($yet_returned_records[0]->guardian->nric);
    SnappyPdf::assertSee(e($warden->getTranslation('name', 'en')));


    /**
     *
     *
     *
     *
     * ### download yet_return students, type = OUTING
     *
     *
     *
     *
     */
    $payload = [
        'export_type' => ExportType::EXCEL->value,
        'yet_return' => true,
        'in_out_type' => HostelInOutType::OUTING->value,
        'date_from' => '2024-01-20',
        'date_to' => '2024-01-22',
    ];

    $title = "Hostel Boarders Go Outing Yet Return (2024-01-20 - 2024-01-22)";

    $file_name = 'hostels-report-by-boarders-go-home-or-out';

    $expected_headers = [
        'No.',
        'Card No.',
        'Room',
        'Student No.',
        'Name',
        'Go Out Date',
        'Go Out Time',
        'Create By',
        'Return Date',
        'Return Time',
        'Signing Parent',
        'Update By',
        'Reason',
    ];

    // Test Excel
    Excel::fake();

    $view_name = 'reports.hostels.by-boarders-go-outing';

    $hostelReportService
        ->setExportType(ExportType::EXCEL->value)
        ->setReportViewName($view_name)
        ->setFileName('hostels-report-by-boarders-go-home-or-out')
        ->getGoHomeOrOutReportData($payload);

    Excel::assertStored(
        DocumentPrintService::PRINT_FOLDER . DIRECTORY_SEPARATOR . $file_name . '.xlsx',
        'local',
        function (GenericExcelExportViaView $export) use (
            $expected_headers,
            $view_name,
            $title,
            $hostel_rooms,
            $yet_return_students,
            $yet_returned_records,
            $warden,
            $cards,
        ) {
            expect($export->view()->name())->toBe($view_name);

            $view = $this->view($export->view()->name(), $export->view()->getData());

            $view->assertSee($title);

            foreach ($expected_headers as $header) {
                $view->assertSee($header);
            }

            $view->assertSee($yet_returned_records[1]->card_no);
            $view->assertSee($hostel_rooms[0]->name);
            $view->assertSee($yet_return_students[1]->student_number);
            $view->assertSee($yet_return_students[1]->getTranslation('name', 'en'));
            $view->assertSee(Carbon::parse($yet_returned_records[1]->check_out_datetime)->setTimezone(config('school.timezone'))->toDateString());
            $view->assertSee(Carbon::parse($yet_returned_records[1]->check_out_datetime)->setTimezone(config('school.timezone'))->format('h:i A'));
            $view->assertSee($yet_returned_records[1]->reason);
            $view->assertSee($yet_returned_records[1]->guardian->nric);
            $view->assertSee($warden->getTranslation('name', 'en'));

            return true;
        }
    );

    // Test PDF
    SnappyPdf::fake();

    $hostelReportService
        ->setExportType(ExportType::PDF->value)
        ->setReportViewName($view_name)
        ->setFileName('hostels-report-by-boarders-go-home-or-out')
        ->getGoHomeOrOutReportData($payload);

    SnappyPdf::assertFileNameIs(storage_path('app' . DIRECTORY_SEPARATOR . DocumentPrintService::PRINT_FOLDER . DIRECTORY_SEPARATOR . $file_name . '.pdf'));
    SnappyPdf::assertViewIs('reports.hostels.by-boarders-go-outing');

    SnappyPdf::assertSee($title);

    foreach ($expected_headers as $header) {
        SnappyPdf::assertSee($header);
    }

    SnappyPdf::assertSee($yet_returned_records[1]->card_no);
    SnappyPdf::assertSee($hostel_rooms[0]->name);
    SnappyPdf::assertSee($yet_return_students[1]->student_number);
    SnappyPdf::assertSee(e($yet_return_students[1]->getTranslation('name', 'en')));
    SnappyPdf::assertSee(Carbon::parse($yet_returned_records[1]->check_out_datetime)->setTimezone(config('school.timezone'))->toDateString());
    SnappyPdf::assertSee(Carbon::parse($yet_returned_records[1]->check_out_datetime)->setTimezone(config('school.timezone'))->format('h:i A'));
    SnappyPdf::assertSee($yet_returned_records[1]->guardian->nric);
    SnappyPdf::assertSee(e($warden->getTranslation('name', 'en')));


    /**
     *
     *
     *
     *
     * ### download leave_school students, type = HOME
     *
     *
     *
     *
     */
    $payload = [
        'export_type' => ExportType::EXCEL->value,
        'leave_school' => true,
        'in_out_type' => HostelInOutType::HOME->value,
        'date_from' => '2024-02-20',
        'date_to' => '2024-02-22',
    ];

    $title = "Hostel Boarders Leave For Home (2024-02-20 - 2024-02-22)";

    $file_name = 'hostels-report-by-boarders-go-home-or-out';

    $expected_headers = [
        'No.',
        'Room',
        'Student No.',
        'Name',
        'Go Home Date',
        'Go Home Time',
        'Create By',
        'Return Date',
        'Return Time',
        'Signing Parent',
        'Update By',
        'Reason',
    ];

    // Test Excel
    Excel::fake();

    $view_name = 'reports.hostels.by-boarders-go-home';

    $hostelReportService
        ->setExportType(ExportType::EXCEL->value)
        ->setReportViewName($view_name)
        ->setFileName('hostels-report-by-boarders-go-home-or-out')
        ->getGoHomeOrOutReportData($payload);

    Excel::assertStored(
        DocumentPrintService::PRINT_FOLDER . DIRECTORY_SEPARATOR . $file_name . '.xlsx',
        'local',
        function (GenericExcelExportViaView $export) use (
            $expected_headers,
            $view_name,
            $title,
            $hostel_rooms,
            $leave_school_students,
            $leave_school_records,
            $warden,
        ) {
            expect($export->view()->name())->toBe($view_name);

            $view = $this->view($export->view()->name(), $export->view()->getData());

            $view->assertSee($title);

            foreach ($expected_headers as $header) {
                $view->assertSee($header);
            }

            $view->assertSee($hostel_rooms[0]->name);
            $view->assertSee($leave_school_students[0]->student_number);
            $view->assertSee($leave_school_students[0]->getTranslation('name', 'en'));
            $view->assertSee(Carbon::parse($leave_school_records[0]->check_out_datetime)->setTimezone(config('school.timezone'))->toDateString());
            $view->assertSee(Carbon::parse($leave_school_records[0]->check_out_datetime)->setTimezone(config('school.timezone'))->format('h:i A'));
            $view->assertSee($leave_school_records[0]->reason);
            $view->assertSee($leave_school_records[0]->guardian->nric);
            $view->assertSee($warden->getTranslation('name', 'en'));

            return true;
        }
    );

    // Test PDF
    SnappyPdf::fake();

    $hostelReportService
        ->setExportType(ExportType::PDF->value)
        ->setReportViewName($view_name)
        ->setFileName('hostels-report-by-boarders-go-home-or-out')
        ->getGoHomeOrOutReportData($payload);

    SnappyPdf::assertFileNameIs(storage_path('app' . DIRECTORY_SEPARATOR . DocumentPrintService::PRINT_FOLDER . DIRECTORY_SEPARATOR . $file_name . '.pdf'));
    SnappyPdf::assertViewIs('reports.hostels.by-boarders-go-home');

    SnappyPdf::assertSee($title);

    foreach ($expected_headers as $header) {
        SnappyPdf::assertSee($header);
    }

    SnappyPdf::assertSee($hostel_rooms[0]->name);
    SnappyPdf::assertSee($leave_school_students[0]->student_number);
    SnappyPdf::assertSee(e($leave_school_students[0]->getTranslation('name', 'en')));
    SnappyPdf::assertSee(Carbon::parse($leave_school_records[0]->check_out_datetime)->setTimezone(config('school.timezone'))->toDateString());
    SnappyPdf::assertSee(Carbon::parse($leave_school_records[0]->check_out_datetime)->setTimezone(config('school.timezone'))->format('h:i A'));
    SnappyPdf::assertSee($leave_school_records[0]->guardian->nric);
    SnappyPdf::assertSee(e($warden->getTranslation('name', 'en')));


    /**
     *
     *
     *
     *
     * ### download leave_school students, type = OUTING
     *
     *
     *
     *
     */
    $payload = [
        'export_type' => ExportType::EXCEL->value,
        'leave_school' => true,
        'in_out_type' => HostelInOutType::OUTING->value,
        'date_from' => '2024-02-20',
        'date_to' => '2024-02-22',
    ];

    $title = "Hostel Boarders Leave For Outing (2024-02-20 - 2024-02-22)";

    $file_name = 'hostels-report-by-boarders-go-home-or-out';

    $expected_headers = [
        'No.',
        'Card No.',
        'Room',
        'Student No.',
        'Name',
        'Go Out Date',
        'Go Out Time',
        'Create By',
        'Return Date',
        'Return Time',
        'Signing Parent',
        'Update By',
        'Reason',
    ];

    // Test Excel
    Excel::fake();

    $view_name = 'reports.hostels.by-boarders-go-outing';

    $hostelReportService
        ->setExportType(ExportType::EXCEL->value)
        ->setReportViewName($view_name)
        ->setFileName('hostels-report-by-boarders-go-home-or-out')
        ->getGoHomeOrOutReportData($payload);

    Excel::assertStored(
        DocumentPrintService::PRINT_FOLDER . DIRECTORY_SEPARATOR . $file_name . '.xlsx',
        'local',
        function (GenericExcelExportViaView $export) use (
            $expected_headers,
            $view_name,
            $title,
            $hostel_rooms,
            $leave_school_students,
            $leave_school_records,
            $warden,
            $cards,
        ) {
            expect($export->view()->name())->toBe($view_name);

            $view = $this->view($export->view()->name(), $export->view()->getData());

            $view->assertSee($title);

            foreach ($expected_headers as $header) {
                $view->assertSee($header);
            }

            $view->assertSee($leave_school_records[1]->card_no);
            $view->assertSee($hostel_rooms[1]->name);
            $view->assertSee($leave_school_students[1]->student_number);
            $view->assertSee($leave_school_students[1]->getTranslation('name', 'en'));
            $view->assertSee(Carbon::parse($leave_school_records[1]->check_out_datetime)->setTimezone(config('school.timezone'))->toDateString());
            $view->assertSee(Carbon::parse($leave_school_records[1]->check_out_datetime)->setTimezone(config('school.timezone'))->format('h:i A'));
            $view->assertSee($leave_school_records[1]->reason);
            $view->assertSee($leave_school_records[1]->guardian->nric);
            $view->assertSee($warden->getTranslation('name', 'en'));

            return true;
        }
    );

    // Test PDF
    SnappyPdf::fake();

    $hostelReportService
        ->setExportType(ExportType::PDF->value)
        ->setReportViewName($view_name)
        ->setFileName('hostels-report-by-boarders-go-home-or-out')
        ->getGoHomeOrOutReportData($payload);

    SnappyPdf::assertFileNameIs(storage_path('app' . DIRECTORY_SEPARATOR . DocumentPrintService::PRINT_FOLDER . DIRECTORY_SEPARATOR . $file_name . '.pdf'));
    SnappyPdf::assertViewIs('reports.hostels.by-boarders-go-outing');

    SnappyPdf::assertSee($title);

    foreach ($expected_headers as $header) {
        SnappyPdf::assertSee($header);
    }

    SnappyPdf::assertSee($leave_school_records[1]->card_no);
    SnappyPdf::assertSee($hostel_rooms[1]->name);
    SnappyPdf::assertSee($leave_school_students[1]->student_number);
    SnappyPdf::assertSee(e($leave_school_students[1]->getTranslation('name', 'en')));
    SnappyPdf::assertSee(Carbon::parse($leave_school_records[1]->check_out_datetime)->setTimezone(config('school.timezone'))->toDateString());
    SnappyPdf::assertSee(Carbon::parse($leave_school_records[1]->check_out_datetime)->setTimezone(config('school.timezone'))->format('h:i A'));
    SnappyPdf::assertSee($leave_school_records[1]->guardian->nric);
    SnappyPdf::assertSee(e($warden->getTranslation('name', 'en')));


    /**
     *
     *
     *
     *
     * ### download returned students, type = HOME, get completed record from hostel
     *
     *
     *
     *
     */
    $payload = [
        'export_type' => ExportType::EXCEL->value,
        'returned' => true,
        'in_out_type' => HostelInOutType::HOME->value,
        'date_from' => '2024-03-01',
        'date_to' => '2024-03-02',
    ];

    $title = "Hostel Boarders Return From Home (2024-03-01 - 2024-03-02)";

    $file_name = 'hostels-report-by-boarders-go-home-or-out';

    $expected_headers = [
        'No.',
        'Room',
        'Student No.',
        'Name',
        'Go Home Date',
        'Go Home Time',
        'Create By',
        'Return Date',
        'Return Time',
        'Signing Parent',
        'Update By',
        'Reason',
    ];

    // Test Excel
    Excel::fake();

    $view_name = 'reports.hostels.by-boarders-go-home';

    $hostelReportService
        ->setExportType(ExportType::EXCEL->value)
        ->setReportViewName($view_name)
        ->setFileName('hostels-report-by-boarders-go-home-or-out')
        ->getGoHomeOrOutReportData($payload);

    Excel::assertStored(
        DocumentPrintService::PRINT_FOLDER . DIRECTORY_SEPARATOR . $file_name . '.xlsx',
        'local',
        function (GenericExcelExportViaView $export) use (
            $expected_headers,
            $view_name,
            $title,
            $hostel_rooms,
            $returned_students,
            $returned_records,
            $warden,
        ) {
            expect($export->view()->name())->toBe($view_name);

            $view = $this->view($export->view()->name(), $export->view()->getData());

            $view->assertSee($title);

            foreach ($expected_headers as $header) {
                $view->assertSee($header);
            }

            $view->assertSee($hostel_rooms[1]->name);
            $view->assertSee($returned_students[0]->student_number);
            $view->assertSee($returned_students[0]->getTranslation('name', 'en'));
            $view->assertSee(Carbon::parse($returned_records[0]->check_out_datetime)->setTimezone(config('school.timezone'))->toDateString());
            $view->assertSee(Carbon::parse($returned_records[0]->check_out_datetime)->setTimezone(config('school.timezone'))->format('h:i A'));
            $view->assertSee(Carbon::parse($returned_records[0]->check_in_datetime)->setTimezone(config('school.timezone'))->toDateString());
            $view->assertSee(Carbon::parse($returned_records[0]->check_in_datetime)->setTimezone(config('school.timezone'))->format('h:i A'));
            $view->assertSee($returned_records[0]->reason);
            $view->assertSee($returned_records[0]->guardian->nric);
            $view->assertSee($warden->getTranslation('name', 'en'));

            return true;
        }
    );

    // Test PDF
    SnappyPdf::fake();

    $hostelReportService
        ->setExportType(ExportType::PDF->value)
        ->setReportViewName($view_name)
        ->setFileName('hostels-report-by-boarders-go-home-or-out')
        ->getGoHomeOrOutReportData($payload);

    SnappyPdf::assertFileNameIs(storage_path('app' . DIRECTORY_SEPARATOR . DocumentPrintService::PRINT_FOLDER . DIRECTORY_SEPARATOR . $file_name . '.pdf'));
    SnappyPdf::assertViewIs('reports.hostels.by-boarders-go-home');

    SnappyPdf::assertSee($title);

    foreach ($expected_headers as $header) {
        SnappyPdf::assertSee($header);
    }

    SnappyPdf::assertSee($hostel_rooms[1]->name);
    SnappyPdf::assertSee($returned_students[0]->student_number);
    SnappyPdf::assertSee(e($returned_students[0]->getTranslation('name', 'en')));
    SnappyPdf::assertSee(Carbon::parse($returned_records[0]->check_out_datetime)->setTimezone(config('school.timezone'))->toDateString());
    SnappyPdf::assertSee(Carbon::parse($returned_records[0]->check_out_datetime)->setTimezone(config('school.timezone'))->format('h:i A'));
    SnappyPdf::assertSee(Carbon::parse($returned_records[0]->check_in_datetime)->setTimezone(config('school.timezone'))->toDateString());
    SnappyPdf::assertSee(Carbon::parse($returned_records[0]->check_in_datetime)->setTimezone(config('school.timezone'))->format('h:i A'));
    SnappyPdf::assertSee($returned_records[0]->guardian->nric);
    SnappyPdf::assertSee(e($warden->getTranslation('name', 'en')));


    /**
     *
     *
     *
     *
     * ### download returned students, type = OUTING
     *
     *
     *
     *
     */
    $payload = [
        'export_type' => ExportType::EXCEL->value,
        'returned' => true,
        'in_out_type' => HostelInOutType::OUTING->value,
        'date_from' => '2024-03-01',
        'date_to' => '2024-03-02',
    ];

    $title = "Hostel Boarders Return From Outing (2024-03-01 - 2024-03-02)";

    $file_name = 'hostels-report-by-boarders-go-home-or-out';

    $expected_headers = [
        'No.',
        'Card No.',
        'Room',
        'Student No.',
        'Name',
        'Go Out Date',
        'Go Out Time',
        'Create By',
        'Return Date',
        'Return Time',
        'Signing Parent',
        'Update By',
        'Reason',
    ];

    // Test Excel
    Excel::fake();

    $view_name = 'reports.hostels.by-boarders-go-outing';

    $hostelReportService
        ->setExportType(ExportType::EXCEL->value)
        ->setReportViewName($view_name)
        ->setFileName('hostels-report-by-boarders-go-home-or-out')
        ->getGoHomeOrOutReportData($payload);

    Excel::assertStored(
        DocumentPrintService::PRINT_FOLDER . DIRECTORY_SEPARATOR . $file_name . '.xlsx',
        'local',
        function (GenericExcelExportViaView $export) use (
            $expected_headers,
            $view_name,
            $title,
            $hostel_rooms,
            $returned_students,
            $returned_records,
            $warden,
            $cards,
        ) {
            expect($export->view()->name())->toBe($view_name);

            $view = $this->view($export->view()->name(), $export->view()->getData());

            $view->assertSee($title);

            foreach ($expected_headers as $header) {
                $view->assertSee($header);
            }

            $view->assertSee($returned_records[1]->card_no);
            $view->assertSee($hostel_rooms[1]->name);
            $view->assertSee($returned_students[1]->student_number);
            $view->assertSee($returned_students[1]->getTranslation('name', 'en'));
            $view->assertSee(Carbon::parse($returned_records[1]->check_out_datetime)->setTimezone(config('school.timezone'))->toDateString());
            $view->assertSee(Carbon::parse($returned_records[1]->check_out_datetime)->setTimezone(config('school.timezone'))->format('h:i A'));
            $view->assertSee(Carbon::parse($returned_records[1]->check_in_datetime)->setTimezone(config('school.timezone'))->toDateString());
            $view->assertSee(Carbon::parse($returned_records[1]->check_in_datetime)->setTimezone(config('school.timezone'))->format('h:i A'));
            $view->assertSee($returned_records[1]->reason);
            $view->assertSee($returned_records[1]->guardian->nric);
            $view->assertSee($warden->getTranslation('name', 'en'));

            return true;
        }
    );

    // Test PDF
    SnappyPdf::fake();

    $hostelReportService
        ->setExportType(ExportType::PDF->value)
        ->setReportViewName($view_name)
        ->setFileName('hostels-report-by-boarders-go-home-or-out')
        ->getGoHomeOrOutReportData($payload);

    SnappyPdf::assertFileNameIs(storage_path('app' . DIRECTORY_SEPARATOR . DocumentPrintService::PRINT_FOLDER . DIRECTORY_SEPARATOR . $file_name . '.pdf'));
    SnappyPdf::assertViewIs('reports.hostels.by-boarders-go-outing');

    SnappyPdf::assertSee($title);

    foreach ($expected_headers as $header) {
        SnappyPdf::assertSee($header);
    }

    SnappyPdf::assertSee($returned_records[1]->card_no);
    SnappyPdf::assertSee($hostel_rooms[1]->name);
    SnappyPdf::assertSee($returned_students[1]->student_number);
    SnappyPdf::assertSee(e($returned_students[1]->getTranslation('name', 'en')));
    SnappyPdf::assertSee(Carbon::parse($returned_records[1]->check_out_datetime)->setTimezone(config('school.timezone'))->toDateString());
    SnappyPdf::assertSee(Carbon::parse($returned_records[1]->check_out_datetime)->setTimezone(config('school.timezone'))->format('h:i A'));
    SnappyPdf::assertSee(Carbon::parse($returned_records[1]->check_in_datetime)->setTimezone(config('school.timezone'))->toDateString());
    SnappyPdf::assertSee(Carbon::parse($returned_records[1]->check_in_datetime)->setTimezone(config('school.timezone'))->format('h:i A'));
    SnappyPdf::assertSee($returned_records[1]->guardian->nric);
    SnappyPdf::assertSee(e($warden->getTranslation('name', 'en')));
});


test('getChangeRoomReportData, preview data', function () {
    $hostelReportService = resolve(HostelReportService::class);

    $sem_year = SemesterYearSetting::create([
        'year' => 2025
    ]);

    $sem1 = SemesterSetting::factory()->create([
        'name' => '2025 Semester 1',
        'semester_year_setting_id' => $sem_year->id,
    ]);

    $grades = Grade::factory(2)->create(new Sequence(
        [
            'name->en' => 'Junior 1'
        ],
        [
            'name->en' => 'Junior 2'
        ],
    ));

    $classes = ClassModel::factory(2)->create(new Sequence(
        [
            'name->en' => 'J111',
            'type' => ClassType::PRIMARY,
            'grade_id' => $grades[0]->id
        ],
        [
            'name->en' => 'J211',
            'type' => ClassType::PRIMARY,
            'grade_id' => $grades[1]->id
        ],
    ));

    $semester_classes = SemesterClass::factory(4)->create(new Sequence(
        [
            'semester_setting_id' => $sem1->id,
            'class_id' => $classes[0]->id // Grade 1
        ],
        [
            'semester_setting_id' => $sem1->id,
            'class_id' => $classes[1]->id // Grade 2
        ],
    ));


    $students = Student::factory(4)->create(new Sequence(
        [
            'name->en' => 'John Jones',
            'gender' => Gender::MALE->value,
            'is_hostel' => true,

        ],
        [
            'name->en' => 'Dwayne',
            'gender' => Gender::MALE->value,
            'is_hostel' => true,

        ],
        [
            'name->en' => 'Frank',
            'gender' => Gender::MALE->value,
            'is_hostel' => true,

        ],
        [
            'name->en' => 'Charlie',
            'gender' => Gender::MALE->value,
            'is_hostel' => true,

        ],
    ));

    StudentClass::factory()->create([
        'semester_setting_id' => $sem1->id,
        'semester_class_id' => $semester_classes[0]->id, // Grade 1
        'student_id' => $students[0]->id,
        'class_enter_date' => '2025-01-05',
    ]);

    StudentClass::factory()->create([
        'semester_setting_id' => $sem1->id,
        'semester_class_id' => $semester_classes[0]->id, // Grade 1
        'student_id' => $students[1]->id,
        'class_enter_date' => '2025-01-05',
    ]);

    StudentClass::factory()->create([
        'semester_setting_id' => $sem1->id,
        'semester_class_id' => $semester_classes[1]->id, // Grade 2
        'student_id' => $students[2]->id,
        'class_enter_date' => '2025-01-05',
    ]);

    StudentClass::factory()->create([
        'semester_setting_id' => $sem1->id,
        'semester_class_id' => $semester_classes[1]->id, // Grade 2
        'student_id' => $students[3]->id,
        'class_enter_date' => '2025-01-05',
    ]);


    $hostel_rooms = HostelRoom::factory(2)->create(new Sequence(
        [
            'name' => 'Room 1'
        ],
        [
            'name' => 'Room 2'
        ],
    ));


    $hostel_beds = HostelRoomBed::factory(4)->create(new Sequence(
        [
            'name' => 'Bed 1',
            'hostel_room_id' => $hostel_rooms[0]->id,    // Room 1
        ],
        [
            'name' => 'Bed 2',
            'hostel_room_id' => $hostel_rooms[0]->id,    // Room 1
        ],
        [
            'name' => 'Bed 3',
            'hostel_room_id' => $hostel_rooms[1]->id,    // Room 2
        ],
        [
            'name' => 'Bed 4',
            'hostel_room_id' => $hostel_rooms[1]->id,    // Room 2
        ],
    ));

    $hostel_bed_assignments = HostelBedAssignment::factory(4)->create(new Sequence(
        [
            'previous_hostel_room_bed_id' => $hostel_beds[3]->id,
            'hostel_room_bed_id' => $hostel_beds[0]->id,
            'assignable_id' => $students[0]->id,
            'assignable_type' => Student::class,
            'start_date' => '2025-01-01',
            'end_date' => '2025-12-20',
        ],
        [
            'previous_hostel_room_bed_id' => $hostel_beds[2]->id,
            'hostel_room_bed_id' => $hostel_beds[1]->id,
            'assignable_id' => $students[1]->id,
            'assignable_type' => Student::class,
            'start_date' => '2025-01-01',
            'end_date' => '2025-12-20',
        ],
        [
            'previous_hostel_room_bed_id' => $hostel_beds[1]->id,
            'hostel_room_bed_id' => $hostel_beds[2]->id,
            'assignable_id' => $students[2]->id,
            'assignable_type' => Student::class,
            'start_date' => '2025-01-01',
            'end_date' => '2025-12-20',
        ],
        [
            'previous_hostel_room_bed_id' => $hostel_beds[0]->id,
            'hostel_room_bed_id' => $hostel_beds[3]->id,
            'assignable_id' => $students[3]->id,
            'assignable_type' => Student::class,
            'start_date' => '2025-01-01',
            'end_date' => null, // havent checkout yet
        ],
    ));

    $response = $hostelReportService->getChangeRoomReportData([
        'year' => $sem1->semesterYearSetting->year,
        'semester_setting_id' => $sem1->id,
    ]);

    expect($response)
        ->toHaveKey('0.student_name.en', 'John Jones')
        ->toHaveKey('1.student_name.en', 'Dwayne')
        ->toHaveKey('2.student_name.en', 'Frank')
        ->toHaveKey('3.student_name.en', 'Charlie');
});

test('getChangeRoomReportData, download excel / pdf', function () {

    $sem_year = SemesterYearSetting::create([
        'year' => 2025
    ]);

    $sem1 = SemesterSetting::factory()->create([
        'name' => '2025 Semester 1',
        'semester_year_setting_id' => $sem_year->id,
    ]);

    $grades = Grade::factory(2)->create(new Sequence(
        [
            'name->en' => 'Junior 1'
        ],
        [
            'name->en' => 'Junior 2'
        ],
    ));

    $classes = ClassModel::factory(2)->create(new Sequence(
        [
            'name->en' => 'J111',
            'type' => ClassType::PRIMARY,
            'grade_id' => $grades[0]->id
        ],
        [
            'name->en' => 'J211',
            'type' => ClassType::PRIMARY,
            'grade_id' => $grades[1]->id
        ],
    ));

    $semester_classes = SemesterClass::factory(4)->create(new Sequence(
        [
            'semester_setting_id' => $sem1->id,
            'class_id' => $classes[0]->id // Grade 1
        ],
        [
            'semester_setting_id' => $sem1->id,
            'class_id' => $classes[1]->id // Grade 2
        ],
    ));


    $students = Student::factory(4)->create(new Sequence(
        [
            'name->en' => 'John Jones',
            'gender' => Gender::MALE->value,
            'is_hostel' => true,

        ],
        [
            'name->en' => 'Dwayne',
            'gender' => Gender::MALE->value,
            'is_hostel' => true,

        ],
        [
            'name->en' => 'Frank',
            'gender' => Gender::MALE->value,
            'is_hostel' => true,

        ],
        [
            'name->en' => 'Charlie',
            'gender' => Gender::MALE->value,
            'is_hostel' => true,

        ],
    ));

    StudentClass::factory()->create([
        'semester_setting_id' => $sem1->id,
        'semester_class_id' => $semester_classes[0]->id, // Grade 1
        'student_id' => $students[0]->id,
        'class_enter_date' => '2025-01-05',
    ]);

    StudentClass::factory()->create([
        'semester_setting_id' => $sem1->id,
        'semester_class_id' => $semester_classes[0]->id, // Grade 1
        'student_id' => $students[1]->id,
        'class_enter_date' => '2025-01-05',
    ]);

    StudentClass::factory()->create([
        'semester_setting_id' => $sem1->id,
        'semester_class_id' => $semester_classes[1]->id, // Grade 2
        'student_id' => $students[2]->id,
        'class_enter_date' => '2025-01-05',
    ]);

    StudentClass::factory()->create([
        'semester_setting_id' => $sem1->id,
        'semester_class_id' => $semester_classes[1]->id, // Grade 2
        'student_id' => $students[3]->id,
        'class_enter_date' => '2025-01-05',
    ]);


    $hostel_rooms = HostelRoom::factory(2)->create(new Sequence(
        [
            'name' => 'Room 1'
        ],
        [
            'name' => 'Room 2'
        ],
    ));


    $hostel_beds = HostelRoomBed::factory(4)->create(new Sequence(
        [
            'name' => 'Bed 1',
            'hostel_room_id' => $hostel_rooms[0]->id,    // Room 1
        ],
        [
            'name' => 'Bed 2',
            'hostel_room_id' => $hostel_rooms[0]->id,    // Room 1
        ],
        [
            'name' => 'Bed 3',
            'hostel_room_id' => $hostel_rooms[1]->id,    // Room 2
        ],
        [
            'name' => 'Bed 4',
            'hostel_room_id' => $hostel_rooms[1]->id,    // Room 2
        ],
    ));

    $hostel_bed_assignments = HostelBedAssignment::factory(4)->create(new Sequence(
        [
            'previous_hostel_room_bed_id' => $hostel_beds[3]->id,
            'hostel_room_bed_id' => $hostel_beds[0]->id,
            'assignable_id' => $students[0]->id,
            'assignable_type' => Student::class,
            'start_date' => '2025-01-01',
            'end_date' => '2025-12-20',
        ],
        [
            'previous_hostel_room_bed_id' => $hostel_beds[2]->id,
            'hostel_room_bed_id' => $hostel_beds[1]->id,
            'assignable_id' => $students[1]->id,
            'assignable_type' => Student::class,
            'start_date' => '2025-01-01',
            'end_date' => '2025-12-20',
        ],
        [
            'previous_hostel_room_bed_id' => $hostel_beds[1]->id,
            'hostel_room_bed_id' => $hostel_beds[2]->id,
            'assignable_id' => $students[2]->id,
            'assignable_type' => Student::class,
            'start_date' => '2025-01-01',
            'end_date' => '2025-12-20',
        ],
        [
            'previous_hostel_room_bed_id' => $hostel_beds[0]->id,
            'hostel_room_bed_id' => $hostel_beds[3]->id,
            'assignable_id' => $students[3]->id,
            'assignable_type' => Student::class,
            'start_date' => '2025-01-01',
            'end_date' => null, // havent checkout yet
        ],
    ));


    /**
     * download start
     */
    $this->partialMock(ReportPrintService::class, function (MockInterface $mock) {
        $mock->shouldReceive('upload')->twice()->andReturnSelf();
        $mock->shouldReceive('getFileUrl')->twice()->andReturn('some-random-url');
    });

    $hostelReportService = resolve(HostelReportService::class);

    $payload = [
        'export_type' => ExportType::EXCEL->value,
        'year' => $sem1->semesterYearSetting->year,
        'semester_setting_id' => $sem1->id,
    ];

    $title = $payload['year'] . ' Change Room Name List';

    $file_name = 'hostels-report-by-change-room-record';

    $expected_headers = [
        'Old Room',
        'Room',
        'Student No.',
        'Name',
        'New Room',
        'Class',
        'Check-In Date',
        'Check-Out Date',
    ];

    // Test Excel
    Excel::fake();

    $view_name = 'reports.hostels.by-change-room-record';

    $hostelReportService
        ->setExportType(ExportType::EXCEL->value)
        ->setReportViewName($view_name)
        ->setFileName('hostels-report-by-change-room-record')
        ->getChangeRoomReportData($payload);

    Excel::assertStored(
        DocumentPrintService::PRINT_FOLDER . DIRECTORY_SEPARATOR . $file_name . '.xlsx',
        'local',
        function (GenericExcelExportViaView $export) use (
            $expected_headers,
            $view_name,
            $title,
            $students,
            $hostel_bed_assignments,
            $classes,
            $grades,
            $hostel_rooms,
            $hostel_beds,
        ) {
            expect($export->view()->name())->toBe($view_name);

            $view = $this->view($export->view()->name(), $export->view()->getData());

            $view->assertSee($title);

            foreach ($expected_headers as $header) {
                $view->assertSee($header);
            }

            foreach ($students as $student) {
                $view->assertSee($student->student_number);
                $view->assertSee($student->getTranslation('name', 'en'));
            }

            foreach ($hostel_rooms as $hostel_room) {
                $view->assertSee($hostel_room->name);
            }

            foreach ($hostel_beds as $hostel_bed) {
                $view->assertSee($hostel_bed->name);
            }

            foreach ($classes as $class) {
                $view->assertSee($class->getTranslation('name', 'en'));
            }

            foreach ($hostel_bed_assignments as $hostel_bed_assignment) {
                $view->assertSee($hostel_bed_assignment->start_date?->toDateString());
                $view->assertSee($hostel_bed_assignment->end_date?->toDateString());
            }

            return true;
        }
    );

    // Test PDF
    SnappyPdf::fake();

    $hostelReportService
        ->setExportType(ExportType::PDF->value)
        ->setReportViewName($view_name)
        ->setFileName('hostels-report-by-change-room-record')
        ->getChangeRoomReportData($payload);

    SnappyPdf::assertFileNameIs(storage_path('app' . DIRECTORY_SEPARATOR . DocumentPrintService::PRINT_FOLDER . DIRECTORY_SEPARATOR . $file_name . '.pdf'));
    SnappyPdf::assertViewIs('reports.hostels.by-change-room-record');

    SnappyPdf::assertSee($title);

    foreach ($expected_headers as $header) {
        SnappyPdf::assertSee($header);
    }

    foreach ($students as $student) {
        SnappyPdf::assertSee($student->student_number);
        SnappyPdf::assertSee(e($student->getTranslation('name', 'en')));
    }

    foreach ($hostel_rooms as $hostel_room) {
        SnappyPdf::assertSee($hostel_room->name);
    }

    foreach ($hostel_beds as $hostel_bed) {
        SnappyPdf::assertSee($hostel_bed->name);
    }

    foreach ($classes as $class) {
        SnappyPdf::assertSee(e($class->getTranslation('name', 'en')));
    }

    foreach ($hostel_bed_assignments as $hostel_bed_assignment) {
        SnappyPdf::assertSee($hostel_bed_assignment->start_date?->toDateString());
        SnappyPdf::assertSee($hostel_bed_assignment->end_date?->toDateString() ?? '');
    }
});


test('getHostelRewardPunishmentReportByBlock()', function () {

    // person in charge
    $person_in_charge_A = Employee::factory()->create();
    $person_in_charge_B = Employee::factory()->create();

    // student
    $student_A = Student::factory()->create();
    $student_A2 = Student::factory()->create();
    $student_A3 = Student::factory()->create();
    $student_C = Student::factory()->create();

    // hostel block sample data
    $hostel_block_A = HostelBlock::factory()->create([
        'name->en' => fake()->name,
        'name->zh' => fake('zh_CN')->name,
        'code' => 'BLOCK-A',
        'type' => HostelBlockType::STUDENT
    ]);

    $hostel_block_C = HostelBlock::factory()->create([
        'name->en' => fake()->name,
        'name->zh' => fake('zh_CN')->name,
        'code' => 'BLOCK-B',
        'type' => HostelBlockType::STUDENT
    ]);

    // hostel room sample data
    $hostel_room_A = HostelRoom::factory()->create([
        'hostel_block_id' => $hostel_block_A->id,
        'name' => 'Room A01',
        'gender' => HostelRoomGender::MALE->value,
        'capacity' => 100,
        'remarks' => fake()->realText(),
        'is_active' => true,
    ]);
    $hostel_room_A2 = HostelRoom::factory()->create([
        'hostel_block_id' => $hostel_block_A->id,
        'name' => 'Room A02',
        'gender' => HostelRoomGender::MALE->value,
        'capacity' => 100,
        'remarks' => fake()->realText(),
        'is_active' => true,
    ]);
    $hostel_room_A3 = HostelRoom::factory()->create([
        'hostel_block_id' => $hostel_block_A->id,
        'name' => 'Room A03',
        'gender' => HostelRoomGender::MALE->value,
        'capacity' => 100,
        'remarks' => fake()->realText(),
        'is_active' => true,
    ]);
    $hostel_room_C = HostelRoom::factory()->create([
        'hostel_block_id' => $hostel_block_C->id,
        'name' => 'Room C01',
        'gender' => HostelRoomGender::MALE->value,
        'capacity' => 100,
        'remarks' => fake()->realText(),
        'is_active' => true,
    ]);

    // hostel room bed sample data
    $hostel_room_bed_A = HostelRoomBed::factory()->create([
        'hostel_room_id' => $hostel_room_A->id,
        'name' => fake()->ean8(),
        'is_active' => true,
        'status' => HostelRoomBedStatus::OCCUPIED->value
    ]);
    $hostel_room_bed_A2 = HostelRoomBed::factory()->create([
        'hostel_room_id' => $hostel_room_A2->id,
        'name' => fake()->ean8(),
        'is_active' => true,
        'status' => HostelRoomBedStatus::OCCUPIED->value
    ]);
    $hostel_room_bed_A3 = HostelRoomBed::factory()->create([
        'hostel_room_id' => $hostel_room_A2->id,
        'name' => fake()->ean8(),
        'is_active' => true,
        'status' => HostelRoomBedStatus::OCCUPIED->value
    ]);
    $hostel_room_bed_C = HostelRoomBed::factory()->create([
        'hostel_room_id' => $hostel_room_C->id,
        'name' => fake()->ean8(),
        'is_active' => true,
        'status' => HostelRoomBedStatus::OCCUPIED->value
    ]);

    // hostel bed assignment sample data
    $hostel_bed_assignment_A = HostelBedAssignment::factory()->create([
        'assignable_id' => $student_A->id,
        'assignable_type' => Student::class,
        'hostel_room_bed_id' => $hostel_room_bed_A->id,
        'previous_hostel_room_bed_id' => null,
        'assigned_by' => User::factory(),
        'start_date' => strtotime("2023-12-01"),
        'end_date' => null,
        'remarks' => fake()->text(100),
    ]);

    $hostel_bed_assignment_A2 = HostelBedAssignment::factory()->create([
        'assignable_id' => $student_A2->id,
        'assignable_type' => Student::class,
        'hostel_room_bed_id' => $hostel_room_bed_A2->id,
        'previous_hostel_room_bed_id' => null,
        'assigned_by' => User::factory(),
        'start_date' => strtotime("2024-01-02"),
        'end_date' => null,
        'remarks' => fake()->text(100),
    ]);

    $hostel_bed_assignment_A3 = HostelBedAssignment::factory()->create([
        'assignable_id' => $student_A3->id,
        'assignable_type' => Student::class,
        'hostel_room_bed_id' => $hostel_room_bed_A3->id,
        'previous_hostel_room_bed_id' => null,
        'assigned_by' => User::factory(),
        'start_date' => strtotime("2024-01-02"),
        'end_date' => strtotime("2024-01-03"),
        'remarks' => fake()->text(100),
    ]);

    $hostel_bed_assignment_C = HostelBedAssignment::factory()->create([
        'assignable_id' => $student_C->id,
        'assignable_type' => Student::class,
        'hostel_room_bed_id' => $hostel_room_bed_C->id,
        'previous_hostel_room_bed_id' => null,
        'assigned_by' => User::factory(),
        'start_date' => strtotime("2023-12-01"),
        'end_date' => null,
        'remarks' => fake()->text(100),
    ]);

    // merit demerit setting data
    $hostel_merit_demerit_setting_A = HostelMeritDemeritSetting::factory()->create([
        'type' => HostelMeritDemeritType::DEMERIT->value,
        'name' => 'Illegal Item -5 marks'
    ]);

    $hostel_merit_demerit_setting_B = HostelMeritDemeritSetting::factory()->create([
        'type' => HostelMeritDemeritType::DEMERIT->value,
        'name' => 'Other -5 marks'
    ]);

    $hostel_merit_demerit_setting_C = HostelMeritDemeritSetting::factory()->create([
        'type' => HostelMeritDemeritType::MERIT->value,
        'name' => 'Charity +2 marks'
    ]);

    // reward punishment setting data
    $hostel_reward_punishment_settings_A = HostelRewardPunishmentSetting::factory()->create([
        'hostel_merit_demerit_setting_id' => $hostel_merit_demerit_setting_A->id,
        'code' => fake()->uuid(),
        'name' => 'Bring Electronic item to school',
        'points' => -5
    ]);

    $hostel_reward_punishment_settings_B = HostelRewardPunishmentSetting::factory()->create([
        'hostel_merit_demerit_setting_id' => $hostel_merit_demerit_setting_B->id,
        'code' => fake()->uuid(),
        'name' => 'Did not clean toilet/room',
        'points' => -5
    ]);

    $hostel_reward_punishment_settings_C = HostelRewardPunishmentSetting::factory()->create([
        'hostel_merit_demerit_setting_id' => $hostel_merit_demerit_setting_C->id,
        'code' => fake()->uuid(),
        'name' => 'Volunteer at old folks home',
        'points' => 2
    ]);

    // hostel reward punishment record data
    $hostel_reward_punishment_records = HostelRewardPunishmentRecord::factory(7)->create(new Sequence(
        [
            'person_in_charge_id' => $person_in_charge_A->id,
            'student_id' => $student_A->id,
            'hostel_reward_punishment_setting_id' => $hostel_reward_punishment_settings_A->id,
            'remark' => 'Bad student',
            'date' => '2024-06-15'
        ],
        [
            'person_in_charge_id' => $person_in_charge_A->id,
            'student_id' => $student_A->id,
            'hostel_reward_punishment_setting_id' => $hostel_reward_punishment_settings_B->id,
            'remark' => 'Bad student',
            'date' => '2024-12-31'
        ],
        [
            'person_in_charge_id' => $person_in_charge_B->id,
            'student_id' => $student_A2->id,
            'hostel_reward_punishment_setting_id' => $hostel_reward_punishment_settings_C->id,
            'remark' => 'Good Student',
            'date' => '2024-01-05'
        ],
        [
            'person_in_charge_id' => $person_in_charge_B->id,
            'student_id' => $student_A2->id,
            'hostel_reward_punishment_setting_id' => $hostel_reward_punishment_settings_C->id,
            'remark' => 'Bravo',
            'date' => '2024-01-01'
        ],
        [
            'person_in_charge_id' => $person_in_charge_B->id,
            'student_id' => $student_A2->id,
            'hostel_reward_punishment_setting_id' => $hostel_reward_punishment_settings_A->id,
            'remark' => 'Excellent',
            'date' => '2023-10-20'
        ],
        [
            'person_in_charge_id' => $person_in_charge_B->id,
            'student_id' => $student_C->id,
            'hostel_reward_punishment_setting_id' => $hostel_reward_punishment_settings_A->id,
            'remark' => 'Bad Student',
            'date' => '2024-10-20'
        ],
        [
            'person_in_charge_id' => $person_in_charge_B->id,
            'student_id' => $student_A3->id,
            'hostel_reward_punishment_setting_id' => $hostel_reward_punishment_settings_A->id,
            'remark' => 'Naughty',
            'date' => '2024-10-25'
        ],
    ));

    $this->partialMock(ReportPrintService::class, function (MockInterface $mock) {
        $mock->shouldReceive('upload')->once()->andReturnSelf();
        $mock->shouldReceive('getFileUrl')->once()->andReturn('some-random-url');
    });

    // PDF test
    SnappyPdf::fake();

    $hostelReportService = resolve(HostelReportService::class);

    $payload = [
        'block_id' => $hostel_block_A->id,
        'date_from' => '2024-01-01',
        'date_to' => '2024-12-31'
    ];
    $file_name = 'hostels-report-by-reward-punishment-block-unit-test';
    $view_name = 'reports.hostels.by-reward-punishment-block';

    $hostelReportService
        ->setExportType(ExportType::PDF->value)
        ->setReportViewName($view_name)
        ->setFileName('hostels-report-by-reward-punishment-block-unit-test')
        ->getHostelRewardPunishmentReportByBlock($payload);

    SnappyPdf::assertFileNameIs(storage_path('app' . DIRECTORY_SEPARATOR . DocumentPrintService::PRINT_FOLDER . DIRECTORY_SEPARATOR . $file_name . '.pdf'));
    SnappyPdf::assertViewIs('reports.hostels.by-reward-punishment-block');

    // check headers
    $expected_headers = ['Date', 'Block', 'Room No.', 'Student No.', 'Name (en)', 'Name (zh)', 'Merit/Demerit', 'Item', 'PIC', 'Remark'];
    foreach ($expected_headers as $header) {
        SnappyPdf::assertSee($header);
    }

    // check title
    SnappyPdf::assertSee(__('Reward and Punishment Record by Block'));

    // reward punishment 1
    SnappyPdf::assertSee('2024-06-15');
    SnappyPdf::assertSee($student_A->student_number);
    SnappyPdf::assertSee(e($student_A->getTranslation('name', 'en')));
    SnappyPdf::assertSee($student_A->getTranslation('name', 'zh'));
    SnappyPdf::assertSee($hostel_block_A->code);
    SnappyPdf::assertSee($hostel_room_A->name);
    SnappyPdf::assertSee($hostel_merit_demerit_setting_A->name);
    SnappyPdf::assertSee($hostel_reward_punishment_settings_A->name);
    SnappyPdf::assertSee(e($person_in_charge_A->name));
    SnappyPdf::assertSee($hostel_reward_punishment_records[0]->remark);

    // reward punishment 2
    SnappyPdf::assertSee('2024-12-31');
    SnappyPdf::assertSee($hostel_block_A->code);
    SnappyPdf::assertSee($hostel_room_A->name);
    SnappyPdf::assertSee($hostel_merit_demerit_setting_B->name);
    SnappyPdf::assertSee($hostel_reward_punishment_settings_B->name);
    SnappyPdf::assertSee(e($person_in_charge_A->name));
    SnappyPdf::assertSee($hostel_reward_punishment_records[1]->remark);

    // reward punishment 3
    SnappyPdf::assertSee('2024-01-05');
    SnappyPdf::assertSee($student_A2->student_number);
    SnappyPdf::assertSee(e($student_A2->getTranslation('name', 'en')));
    SnappyPdf::assertSee($student_A2->getTranslation('name', 'zh'));
    SnappyPdf::assertSee($hostel_block_A->code);
    SnappyPdf::assertSee($hostel_room_A2->name);
    SnappyPdf::assertSee($hostel_merit_demerit_setting_C->name);
    SnappyPdf::assertSee($hostel_reward_punishment_settings_C->name);
    SnappyPdf::assertSee(e($person_in_charge_B->name));
    SnappyPdf::assertSee($hostel_reward_punishment_records[2]->remark);

    // reward punishment 4
    SnappyPdf::assertSee('2024-01-01');
    SnappyPdf::assertSee('<td></td>'); // $hostel_bed_assignment_A2 start date at 2024-01-02, 2024-01-01 will show empty room and block
    SnappyPdf::assertSee($hostel_reward_punishment_records[3]->remark);

    // reward punishment 5 (missing because date not within 2024-01-01 to 2024-12-31)
    SnappyPdf::assertDontSee($hostel_reward_punishment_records[4]->remark);
    SnappyPdf::assertDontSee('2023-10-20');

    // reward punishment 6 (missing because different block)
    SnappyPdf::assertDontSee(e($student_C->student_number));
    SnappyPdf::assertDontSee($student_C->getTranslation('name', 'en'));
    SnappyPdf::assertDontSee($student_C->getTranslation('name', 'zh'));
    SnappyPdf::assertDontSee($hostel_reward_punishment_records[5]->remark);
    SnappyPdf::assertDontSee($hostel_block_C->code);
    SnappyPdf::assertDontSee($hostel_room_C->name);
    SnappyPdf::assertDontSee('2024-10-20');

    // reward punishment 7 (missing because student A3 bed not active ($hostel_bed_assignment_A3 end date not null))
    SnappyPdf::assertDontSee(e($student_A3->student_number));
    SnappyPdf::assertDontSee($student_A3->getTranslation('name', 'en'));
    SnappyPdf::assertDontSee($student_A3->getTranslation('name', 'zh'));
    SnappyPdf::assertDontSee($hostel_reward_punishment_records[6]->remark);
    SnappyPdf::assertDontSee('2024-10-25');
});

test('getHostelRewardPunishmentReportByStudent()', function () {

    // person in charge
    $person_in_charge_A = Employee::factory()->create();
    $person_in_charge_B = Employee::factory()->create();

    // student
    $student_A = Student::factory()->create();
    $student_B = Student::factory()->create();

    $photo = Media::factory()->create([
        'model_type' => Student::class,
        'model_id' => $student_A->id,
        'file_name' => 'first_file.png',
        'collection_name' => 'photo'
    ]);

    // hostel block sample data
    $hostel_block_A = HostelBlock::factory()->create([
        'name->en' => fake()->name,
        'name->zh' => fake('zh_CN')->name,
        'code' => 'BLOCK-A',
        'type' => HostelBlockType::STUDENT
    ]);

    $hostel_block_B = HostelBlock::factory()->create([
        'name->en' => fake()->name,
        'name->zh' => fake('zh_CN')->name,
        'code' => 'BLOCK-B',
        'type' => HostelBlockType::STUDENT
    ]);

    // hostel room sample data
    $hostel_room_A = HostelRoom::factory()->create([
        'hostel_block_id' => $hostel_block_A->id,
        'name' => 'ROOM A01',
        'gender' => HostelRoomGender::MALE->value,
        'capacity' => 100,
        'remarks' => fake()->realText(),
        'is_active' => true,
    ]);
    $hostel_room_A2 = HostelRoom::factory()->create([
        'hostel_block_id' => $hostel_block_A->id,
        'name' => 'ROOM A02',
        'gender' => HostelRoomGender::MALE->value,
        'capacity' => 100,
        'remarks' => fake()->realText(),
        'is_active' => true,
    ]);

    $hostel_room_B = HostelRoom::factory()->create([
        'hostel_block_id' => $hostel_block_B->id,
        'name' => 'ROOM B01',
        'gender' => HostelRoomGender::MALE->value,
        'capacity' => 100,
        'remarks' => fake()->realText(),
        'is_active' => true,
    ]);

    // hostel room bed sample data
    $hostel_room_bed_A = HostelRoomBed::factory()->create([
        'hostel_room_id' => $hostel_room_A->id,
        'name' => fake()->ean8(),
        'is_active' => true,
        'status' => HostelRoomBedStatus::OCCUPIED->value
    ]);
    $hostel_room_bed_A2 = HostelRoomBed::factory()->create([
        'hostel_room_id' => $hostel_room_A2->id,
        'name' => fake()->ean8(),
        'is_active' => true,
        'status' => HostelRoomBedStatus::OCCUPIED->value
    ]);

    $hostel_room_bed_B = HostelRoomBed::factory()->create([
        'hostel_room_id' => $hostel_room_B->id,
        'name' => fake()->ean8(),
        'is_active' => true,
        'status' => HostelRoomBedStatus::OCCUPIED->value
    ]);

    // hostel bed assignment sample data
    $hostel_bed_assignment_A = HostelBedAssignment::factory()->create([
        'assignable_id' => $student_A->id,
        'assignable_type' => Student::class,
        'hostel_room_bed_id' => $hostel_room_bed_A->id,
        'previous_hostel_room_bed_id' => null,
        'assigned_by' => User::factory(),
        'start_date' => strtotime("2024-04-30"),
        'end_date' => strtotime("2024-06-16"),
        'remarks' => fake()->text(100),
    ]);
    $hostel_bed_assignment_A2 = HostelBedAssignment::factory()->create([
        'assignable_id' => $student_A->id,
        'assignable_type' => Student::class,
        'hostel_room_bed_id' => $hostel_room_bed_A2->id,
        'previous_hostel_room_bed_id' => null,
        'assigned_by' => User::factory(),
        'start_date' => strtotime("2024-06-16"),
        'end_date' => null,
        'remarks' => fake()->text(100),
    ]);

    $hostel_bed_assignment_B = HostelBedAssignment::factory()->create([
        'assignable_id' => $student_B->id,
        'assignable_type' => Student::class,
        'hostel_room_bed_id' => $hostel_room_bed_B->id,
        'previous_hostel_room_bed_id' => null,
        'assigned_by' => User::factory(),
        'start_date' => strtotime("2024-01-01"),
        'end_date' => strtotime("2024-02-01"),
        'remarks' => fake()->text(100),
    ]);

    $hostel_bed_assignment_C = HostelBedAssignment::factory()->create([
        'assignable_id' => $student_B->id,
        'assignable_type' => Student::class,
        'hostel_room_bed_id' => $hostel_room_bed_B->id,
        'previous_hostel_room_bed_id' => null,
        'assigned_by' => User::factory(),
        'start_date' => strtotime("2023-04-30"),
        'end_date' => strtotime("2023-10-30"),
        'remarks' => fake()->text(100),
    ]);

    // merit demerit setting data
    $hostel_merit_demerit_setting_A = HostelMeritDemeritSetting::factory()->create([
        'type' => HostelMeritDemeritType::DEMERIT->value,
        'name' => 'Illegal Item -5 marks'
    ]);

    $hostel_merit_demerit_setting_B = HostelMeritDemeritSetting::factory()->create([
        'type' => HostelMeritDemeritType::DEMERIT->value,
        'name' => 'Other -5 marks'
    ]);

    $hostel_merit_demerit_setting_C = HostelMeritDemeritSetting::factory()->create([
        'type' => HostelMeritDemeritType::MERIT->value,
        'name' => 'Charity +2 marks'
    ]);

    $hostel_merit_demerit_setting_D = HostelMeritDemeritSetting::factory()->create([
        'type' => HostelMeritDemeritType::MERIT->value,
        'name' => 'Charity +3 marks'
    ]);

    // reward punishment setting data
    $hostel_reward_punishment_settings_A = HostelRewardPunishmentSetting::factory()->create([
        'hostel_merit_demerit_setting_id' => $hostel_merit_demerit_setting_A->id,
        'code' => fake()->uuid(),
        'name' => 'Bring Electronic item to school',
        'points' => -5
    ]);

    $hostel_reward_punishment_settings_B = HostelRewardPunishmentSetting::factory()->create([
        'hostel_merit_demerit_setting_id' => $hostel_merit_demerit_setting_B->id,
        'code' => fake()->uuid(),
        'name' => 'Did not clean toilet/room',
        'points' => -5
    ]);

    $hostel_reward_punishment_settings_C = HostelRewardPunishmentSetting::factory()->create([
        'hostel_merit_demerit_setting_id' => $hostel_merit_demerit_setting_C->id,
        'code' => fake()->uuid(),
        'name' => 'Volunteer at old folks home',
        'points' => 2
    ]);

    $hostel_reward_punishment_settings_D = HostelRewardPunishmentSetting::factory()->create([
        'hostel_merit_demerit_setting_id' => $hostel_merit_demerit_setting_D->id,
        'code' => fake()->uuid(),
        'name' => 'Volunteer at old folks home 2',
        'points' => 3
    ]);

    // hostel reward punishment record data
    $hostel_reward_punishment_records = HostelRewardPunishmentRecord::factory(5)->create(new Sequence(
        [
            'person_in_charge_id' => $person_in_charge_A->id,
            'student_id' => $student_A->id,
            'hostel_reward_punishment_setting_id' => $hostel_reward_punishment_settings_A->id,
            'remark' => 'Bad student',
            'date' => '2024-01-01'
        ],
        [
            'person_in_charge_id' => $person_in_charge_A->id,
            'student_id' => $student_A->id,
            'hostel_reward_punishment_setting_id' => $hostel_reward_punishment_settings_A->id,
            'remark' => 'Bad student',
            'date' => '2024-06-15'
        ],
        [
            'person_in_charge_id' => $person_in_charge_A->id,
            'student_id' => $student_A->id,
            'hostel_reward_punishment_setting_id' => $hostel_reward_punishment_settings_B->id,
            'remark' => 'Bad student',
            'date' => '2024-12-31'
        ],
        [
            'person_in_charge_id' => $person_in_charge_B->id,
            'student_id' => $student_B->id,
            'hostel_reward_punishment_setting_id' => $hostel_reward_punishment_settings_C->id,
            'remark' => 'Good Student',
            'date' => '2024-01-05'
        ],
        [
            'person_in_charge_id' => $person_in_charge_B->id,
            'student_id' => $student_A->id,
            'hostel_reward_punishment_setting_id' => $hostel_reward_punishment_settings_D->id,
            'remark' => 'Excellent',
            'date' => '2023-10-20'
        ],
    ));

    $this->partialMock(ReportPrintService::class, function (MockInterface $mock) {
        $mock->shouldReceive('upload')->once()->andReturnSelf();
        $mock->shouldReceive('getFileUrl')->once()->andReturn('some-random-url');
    });

    // PDF test
    SnappyPdf::fake();

    $hostelReportService = resolve(HostelReportService::class);

    $payload = [
        'student_id' => $student_A->id,
        'date_from' => '2024-01-01',
        'date_to' => '2024-12-31'
    ];

    $file_name = 'hostels-report-by-reward-punishment-student-unit-test';
    $view_name = 'reports.hostels.by-reward-punishment-student';

    $hostelReportService
        ->setExportType(ExportType::PDF->value)
        ->setReportViewName($view_name)
        ->setFileName('hostels-report-by-reward-punishment-student-unit-test')
        ->getHostelRewardPunishmentReportByStudent($payload);

    SnappyPdf::assertFileNameIs(storage_path('app' . DIRECTORY_SEPARATOR . DocumentPrintService::PRINT_FOLDER . DIRECTORY_SEPARATOR . $file_name . '.pdf'));
    SnappyPdf::assertViewIs('reports.hostels.by-reward-punishment-student');

    $expected_headers = ['Date', 'Room No.', 'Merit/Demerit', 'Item', 'PIC', 'Deduction Points', 'Balance Points', 'Remarks'];
    foreach ($expected_headers as $header) {
        SnappyPdf::assertSee($header);
    }

    // check title
    SnappyPdf::assertSee(__('Individual Reward and Punishment Record 2024'));

    SnappyPdf::assertSee('Room: '.$hostel_room_A2->name .'/'. $hostel_room_bed_A2->name);
    SnappyPdf::assertSee($student_A->student_number);
    SnappyPdf::assertSee(e($student_A->getTranslation('name', 'en')));
    SnappyPdf::assertSee($student_A->getTranslation('name', 'zh'));
    SnappyPdf::assertDontSee('Room: '.$hostel_room_B->name .'/'. $hostel_room_bed_B->name);
    SnappyPdf::assertDontSee($student_B->student_number);
    SnappyPdf::assertDontSee(e($student_B->getTranslation('name', 'en')));
    SnappyPdf::assertDontSee($student_B->getTranslation('name', 'zh'));

    // check repeating items
    SnappyPdf::assertSee('<td>25</td>');
    SnappyPdf::assertSee('<td>20</td>');
    SnappyPdf::assertSee('<td>20</td>');
    SnappyPdf::assertSee('Deducted Points: -15');
    SnappyPdf::assertSee('Balance Points: 15');
    SnappyPdf::assertSee($photo->file_name);

    // reward punishment 1
    SnappyPdf::assertSee('2024-01-01');
    SnappyPdf::assertSee('<td></td>'); // start date = 2024-04-30, end date = 2024-06-16
    SnappyPdf::assertSee($hostel_merit_demerit_setting_A->name);
    SnappyPdf::assertSee($hostel_reward_punishment_settings_A->name);
    SnappyPdf::assertSee(e($person_in_charge_A->name));
    SnappyPdf::assertSee($hostel_reward_punishment_records[0]->remark);

    // reward punishment 2
    SnappyPdf::assertSee('2024-06-15');
    SnappyPdf::assertSee('<td>'.$hostel_room_A->name .'/'. $hostel_room_bed_A->name.'</td>'); // start date = 2024-04-30, end date = 2024-06-16
    SnappyPdf::assertSee($hostel_merit_demerit_setting_B->name);
    SnappyPdf::assertSee($hostel_reward_punishment_settings_B->name);
    SnappyPdf::assertSee(e($person_in_charge_A->name));
    SnappyPdf::assertSee($hostel_reward_punishment_records[1]->remark);

    // reward punishment 3
    SnappyPdf::assertSee('2024-12-31');
    SnappyPdf::assertSee('<td>'.$hostel_room_A2->name .'/'. $hostel_room_bed_A2->name.'</td>'); // start date = 2024-04-30, end date = 2024-06-16
    SnappyPdf::assertSee($hostel_merit_demerit_setting_B->name);
    SnappyPdf::assertSee($hostel_reward_punishment_settings_B->name);
    SnappyPdf::assertSee(e($person_in_charge_A->name));
    SnappyPdf::assertSee($hostel_reward_punishment_records[2]->remark);

    // reward punishment 4 should be missing because it belongs to student B
    SnappyPdf::assertDontSee('2024-01-05');
    SnappyPdf::assertDontSee('<td>'.$hostel_room_B->name .'/'. $hostel_room_bed_B->name.'</td>');
    SnappyPdf::assertDontSee($hostel_merit_demerit_setting_C->name);
    SnappyPdf::assertDontSee($hostel_reward_punishment_settings_C->name);
    SnappyPdf::assertDontSee(e($person_in_charge_B->name));
    SnappyPdf::assertDontSee($hostel_reward_punishment_records[3]->remark);

    // reward punishment 5 should be missing because the date is 2023-10-20
    SnappyPdf::assertDontSee('2023-10-20');
    SnappyPdf::assertDontSee($hostel_merit_demerit_setting_D->name);
    SnappyPdf::assertDontSee($hostel_reward_punishment_settings_D->name);
    SnappyPdf::assertDontSee(e($person_in_charge_B->name));
    SnappyPdf::assertDontSee($hostel_reward_punishment_records[4]->remark);


    $this->partialMock(ReportPrintService::class, function (MockInterface $mock) {
        $mock->shouldReceive('upload')->once()->andReturnSelf();
        $mock->shouldReceive('getFileUrl')->once()->andReturn('some-random-url');
    });

    // PDF test
    SnappyPdf::fake();

    $hostelReportService = resolve(HostelReportService::class);

    $payload = [
        'student_id' => $student_B->id,
        'date_from' => '2024-01-01',
        'date_to' => '2024-12-31'
    ];

    $file_name = 'hostels-report-by-reward-punishment-student-unit-test';
    $view_name = 'reports.hostels.by-reward-punishment-student';

    $hostelReportService
        ->setExportType(ExportType::PDF->value)
        ->setReportViewName($view_name)
        ->setFileName('hostels-report-by-reward-punishment-student-unit-test')
        ->getHostelRewardPunishmentReportByStudent($payload);

    SnappyPdf::assertFileNameIs(storage_path('app' . DIRECTORY_SEPARATOR . DocumentPrintService::PRINT_FOLDER . DIRECTORY_SEPARATOR . $file_name . '.pdf'));
    SnappyPdf::assertViewIs('reports.hostels.by-reward-punishment-student');

    $expected_headers = ['Date', 'Room No.', 'Merit/Demerit', 'Item', 'PIC', 'Deduction Points', 'Balance Points', 'Remarks'];
    foreach ($expected_headers as $header) {
        SnappyPdf::assertSee($header);
    }

    SnappyPdf::assertSee('Room: N/A'); // no active bed
    SnappyPdf::assertDontSee($student_A->student_number);
    SnappyPdf::assertDontSee(e($student_A->getTranslation('name', 'en')));
    SnappyPdf::assertDontSee($student_A->getTranslation('name', 'zh'));
    SnappyPdf::assertDontSee('Room: '.$hostel_room_B->name .'/'. $hostel_room_bed_B->name);
    SnappyPdf::assertSee($student_B->student_number);
    SnappyPdf::assertSee(e($student_B->getTranslation('name', 'en')));
    SnappyPdf::assertSee($student_B->getTranslation('name', 'zh'));

    SnappyPdf::assertSee('2024-01-05');
    SnappyPdf::assertSee('<td>'.$hostel_room_B->name .'/'. $hostel_room_bed_B->name.'</td>'); // start date = 2024-01-01, end date = 2024-02-01
    SnappyPdf::assertSee($hostel_merit_demerit_setting_C->name);
    SnappyPdf::assertSee($hostel_reward_punishment_settings_C->name);
    SnappyPdf::assertSee(e($person_in_charge_B->name));
    SnappyPdf::assertSee($hostel_reward_punishment_records[3]->remark);
});

test('getHostelRewardPunishmentReportByRoom()', function () {

    // person in charge
    $person_in_charge_A = Employee::factory()->create();
    $person_in_charge_B = Employee::factory()->create();

    // student
    $student_A = Student::factory()->create();
    $student_B = Student::factory()->create();

    // hostel block sample data
    $hostel_block_A = HostelBlock::factory()->create([
        'name->en' => fake()->name,
        'name->zh' => fake('zh_CN')->name,
        'code' => 'BLOCK-A',
        'type' => HostelBlockType::STUDENT
    ]);

    $hostel_block_B = HostelBlock::factory()->create([
        'name->en' => fake()->name,
        'name->zh' => fake('zh_CN')->name,
        'code' => 'BLOCK-B',
        'type' => HostelBlockType::STUDENT
    ]);

    // hostel room sample data
    $hostel_room_A = HostelRoom::factory()->create([
        'hostel_block_id' => $hostel_block_A->id,
        'name' => 'ROOM A01',
        'gender' => HostelRoomGender::MALE->value,
        'capacity' => 100,
        'remarks' => fake()->realText(),
        'is_active' => true,
    ]);
    $hostel_room_A2 = HostelRoom::factory()->create([
        'hostel_block_id' => $hostel_block_A->id,
        'name' => 'ROOM A02',
        'gender' => HostelRoomGender::MALE->value,
        'capacity' => 100,
        'remarks' => fake()->realText(),
        'is_active' => true,
    ]);

    $hostel_room_B = HostelRoom::factory()->create([
        'hostel_block_id' => $hostel_block_B->id,
        'name' => 'ROOM B01',
        'gender' => HostelRoomGender::MALE->value,
        'capacity' => 100,
        'remarks' => fake()->realText(),
        'is_active' => true,
    ]);

    // hostel room bed sample data
    $hostel_room_bed_A = HostelRoomBed::factory()->create([
        'hostel_room_id' => $hostel_room_A->id,
        'name' => fake()->ean8(),
        'is_active' => true,
        'status' => HostelRoomBedStatus::OCCUPIED->value
    ]);
    $hostel_room_bed_A2 = HostelRoomBed::factory()->create([
        'hostel_room_id' => $hostel_room_A2->id,
        'name' => fake()->ean8(),
        'is_active' => true,
        'status' => HostelRoomBedStatus::OCCUPIED->value
    ]);

    $hostel_room_bed_B = HostelRoomBed::factory()->create([
        'hostel_room_id' => $hostel_room_B->id,
        'name' => fake()->ean8(),
        'is_active' => true,
        'status' => HostelRoomBedStatus::OCCUPIED->value
    ]);

    // hostel bed assignment sample data
    $hostel_bed_assignment_A = HostelBedAssignment::factory()->create([
        'assignable_id' => $student_A->id,
        'assignable_type' => Student::class,
        'hostel_room_bed_id' => $hostel_room_bed_A->id,
        'previous_hostel_room_bed_id' => null,
        'assigned_by' => User::factory(),
        'start_date' => strtotime("2024-04-30"),
        'end_date' => strtotime("2024-06-16"),
        'remarks' => fake()->text(100),
    ]);
    $hostel_bed_assignment_A2 = HostelBedAssignment::factory()->create([
        'assignable_id' => $student_A->id,
        'assignable_type' => Student::class,
        'hostel_room_bed_id' => $hostel_room_bed_A2->id,
        'previous_hostel_room_bed_id' => null,
        'assigned_by' => User::factory(),
        'start_date' => strtotime("2024-06-16"),
        'end_date' => null,
        'remarks' => fake()->text(100),
    ]);

    $hostel_bed_assignment_B = HostelBedAssignment::factory()->create([
        'assignable_id' => $student_B->id,
        'assignable_type' => Student::class,
        'hostel_room_bed_id' => $hostel_room_bed_B->id,
        'previous_hostel_room_bed_id' => null,
        'assigned_by' => User::factory(),
        'start_date' => strtotime("2024-04-30"),
        'end_date' => null,
        'remarks' => fake()->text(100),
    ]);

    $hostel_bed_assignment_C = HostelBedAssignment::factory()->create([
        'assignable_id' => $student_B->id,
        'assignable_type' => Student::class,
        'hostel_room_bed_id' => $hostel_room_bed_B->id,
        'previous_hostel_room_bed_id' => null,
        'assigned_by' => User::factory(),
        'start_date' => strtotime("2023-04-30"),
        'end_date' => strtotime("2023-10-30"),
        'remarks' => fake()->text(100),
    ]);

    // merit demerit setting data
    $hostel_merit_demerit_setting_A = HostelMeritDemeritSetting::factory()->create([
        'type' => HostelMeritDemeritType::DEMERIT->value,
        'name' => 'Illegal Item -5 marks'
    ]);

    $hostel_merit_demerit_setting_B = HostelMeritDemeritSetting::factory()->create([
        'type' => HostelMeritDemeritType::DEMERIT->value,
        'name' => 'Other -5 marks'
    ]);

    $hostel_merit_demerit_setting_C = HostelMeritDemeritSetting::factory()->create([
        'type' => HostelMeritDemeritType::MERIT->value,
        'name' => 'Charity +2 marks'
    ]);

    // reward punishment setting data
    $hostel_reward_punishment_settings_A = HostelRewardPunishmentSetting::factory()->create([
        'hostel_merit_demerit_setting_id' => $hostel_merit_demerit_setting_A->id,
        'code' => fake()->uuid(),
        'name' => 'Bring Electronic item to school',
        'points' => -5
    ]);

    $hostel_reward_punishment_settings_B = HostelRewardPunishmentSetting::factory()->create([
        'hostel_merit_demerit_setting_id' => $hostel_merit_demerit_setting_B->id,
        'code' => fake()->uuid(),
        'name' => 'Did not clean toilet/room',
        'points' => -5
    ]);

    $hostel_reward_punishment_settings_C = HostelRewardPunishmentSetting::factory()->create([
        'hostel_merit_demerit_setting_id' => $hostel_merit_demerit_setting_C->id,
        'code' => fake()->uuid(),
        'name' => 'Volunteer at old folks home',
        'points' => 2

    ]);

    // hostel reward punishment record data
    $hostel_reward_punishment_records = HostelRewardPunishmentRecord::factory(4)->create(new Sequence(
        [
            'person_in_charge_id' => $person_in_charge_A->id,
            'student_id' => $student_A->id,
            'hostel_reward_punishment_setting_id' => $hostel_reward_punishment_settings_A->id,
            'remark' => 'Bad student',
            'date' => '2024-06-15'
        ],
        [
            'person_in_charge_id' => $person_in_charge_A->id,
            'student_id' => $student_A->id,
            'hostel_reward_punishment_setting_id' => $hostel_reward_punishment_settings_B->id,
            'remark' => 'Bad student',
            'date' => '2024-12-31'
        ],
        [
            'person_in_charge_id' => $person_in_charge_B->id,
            'student_id' => $student_B->id,
            'hostel_reward_punishment_setting_id' => $hostel_reward_punishment_settings_C->id,
            'remark' => 'Good Student',
            'date' => '2024-01-01'
        ],
        [
            'person_in_charge_id' => $person_in_charge_B->id,
            'student_id' => $student_B->id,
            'hostel_reward_punishment_setting_id' => $hostel_reward_punishment_settings_A->id,
            'remark' => 'Good Student',
            'date' => '2023-10-20'
        ],
    ));

    $this->partialMock(ReportPrintService::class, function (MockInterface $mock) {
        $mock->shouldReceive('upload')->once()->andReturnSelf();
        $mock->shouldReceive('getFileUrl')->once()->andReturn('some-random-url');
    });

    // PDF test
    SnappyPdf::fake();

    $hostelReportService = resolve(HostelReportService::class);

    $payload = [
        'date_from' => '2024-01-01',
        'date_to' => '2024-12-31'
    ];

    $file_name = 'hostels-report-by-reward-punishment-student-unit-test';
    $view_name = 'reports.hostels.by-reward-punishment-room';

    $hostelReportService
        ->setExportType(ExportType::PDF->value)
        ->setReportViewName($view_name)
        ->setFileName('hostels-report-by-reward-punishment-student-unit-test')
        ->getHostelRewardPunishmentReportByRoom($payload);

    SnappyPdf::assertFileNameIs(storage_path('app' . DIRECTORY_SEPARATOR . DocumentPrintService::PRINT_FOLDER . DIRECTORY_SEPARATOR . $file_name . '.pdf'));
    SnappyPdf::assertViewIs('reports.hostels.by-reward-punishment-room');

    $expected_headers = ['Date', 'Room No.', 'Merit/Demerit', 'Item', 'PIC', 'Deduction Points', 'Balance Points', 'Remarks'];
    foreach ($expected_headers as $header) {
        SnappyPdf::assertSee($header);
    }

    // check title
    SnappyPdf::assertSee(__('Reward and Punishment Record by Room 2024'));


    // check repeating items
    SnappyPdf::assertSee('25');
    SnappyPdf::assertSee('20');

    SnappyPdf::assertSee('Room: '.$hostel_room_A2->name .'/'. $hostel_room_bed_A2->name);
    SnappyPdf::assertSee('Room: '.$hostel_room_B->name .'/'. $hostel_room_bed_B->name);

    // check reward punishment 1
    SnappyPdf::assertSee('2024-06-15');
    SnappyPdf::assertSee($student_A->student_number);
    SnappyPdf::assertSee(e($student_A->getTranslation('name', 'en')));
    SnappyPdf::assertSee($student_A->getTranslation('name', 'zh'));
    SnappyPdf::assertSee('<td>'.$hostel_room_A->name .'/'. $hostel_room_bed_A->name.'</td>'); // start date = 2024-04-30, end date = 2024-06-16
    SnappyPdf::assertSee($hostel_merit_demerit_setting_A->name);
    SnappyPdf::assertSee($hostel_reward_punishment_settings_A->name);
    SnappyPdf::assertSee(e($person_in_charge_A->name));
    SnappyPdf::assertSee($hostel_reward_punishment_records[0]->remark);

    // check reward punishment 2
    SnappyPdf::assertSee('2024-12-31');
    SnappyPdf::assertSee($student_A->student_number);
    SnappyPdf::assertSee(e($student_A->getTranslation('name', 'en')));
    SnappyPdf::assertSee($student_A->getTranslation('name', 'zh'));
    SnappyPdf::assertSee('<td>'.$hostel_room_A2->name .'/'. $hostel_room_bed_A2->name.'</td>'); // start date = 2024-06-16, end date = null
    SnappyPdf::assertSee($hostel_merit_demerit_setting_B->name);
    SnappyPdf::assertSee($hostel_reward_punishment_settings_B->name);
    SnappyPdf::assertSee(e($person_in_charge_A->name));
    SnappyPdf::assertSee($hostel_reward_punishment_records[1]->remark);

    // check reward punishment 3
    SnappyPdf::assertSee('2024-01-01');
    SnappyPdf::assertSee($student_B->student_number);
    SnappyPdf::assertSee(e($student_B->getTranslation('name', 'en')));
    SnappyPdf::assertSee($student_B->getTranslation('name', 'zh'));
    SnappyPdf::assertDontSee('<td>'.$hostel_room_B->name .'/'. $hostel_room_bed_B->name.'</td>'); // start date = 2024-04-30, end date = null
    SnappyPdf::assertSee($hostel_merit_demerit_setting_C->name);
    SnappyPdf::assertSee($hostel_reward_punishment_settings_C->name);
    SnappyPdf::assertSee(e($person_in_charge_B->name));
    SnappyPdf::assertSee($hostel_reward_punishment_records[2]->remark);


    // Test with Room ID filter
    $this->partialMock(ReportPrintService::class, function (MockInterface $mock) {
        $mock->shouldReceive('upload')->once()->andReturnSelf();
        $mock->shouldReceive('getFileUrl')->once()->andReturn('some-random-url');
    });

    SnappyPdf::fake();

    $hostelReportService = resolve(HostelReportService::class);

    $payload = [
        'date_from' => '2024-01-01',
        'date_to' => '2024-12-31',
        'room_id' => $hostel_room_A2->id
    ];

    $hostelReportService
        ->setExportType(ExportType::PDF->value)
        ->setReportViewName($view_name)
        ->setFileName('hostels-report-by-reward-punishment-student-unit-test')
        ->getHostelRewardPunishmentReportByRoom($payload);

    SnappyPdf::assertSee('Room: '.$hostel_room_A2->name .'/'. $hostel_room_bed_A2->name);
    SnappyPdf::assertDontSee('Room: '.$hostel_room_B->name .'/'. $hostel_room_bed_B->name);

    // check reward punishment 1
    SnappyPdf::assertSee('2024-06-15');
    SnappyPdf::assertSee($student_A->student_number);
    SnappyPdf::assertSee(e($student_A->getTranslation('name', 'en')));
    SnappyPdf::assertSee($student_A->getTranslation('name', 'zh'));
    SnappyPdf::assertSee('<td>'.$hostel_room_A->name .'/'. $hostel_room_bed_A->name.'</td>'); // start date = 2024-04-30, end date = 2024-06-16
    SnappyPdf::assertSee($hostel_merit_demerit_setting_A->name);
    SnappyPdf::assertSee($hostel_reward_punishment_settings_A->name);
    SnappyPdf::assertSee(e($person_in_charge_A->name));
    SnappyPdf::assertSee($hostel_reward_punishment_records[0]->remark);

    // check reward punishment 2
    SnappyPdf::assertSee('2024-12-31');
    SnappyPdf::assertSee($student_A->student_number);
    SnappyPdf::assertSee(e($student_A->getTranslation('name', 'en')));
    SnappyPdf::assertSee($student_A->getTranslation('name', 'zh'));
    SnappyPdf::assertSee('<td>'.$hostel_room_A2->name .'/'. $hostel_room_bed_A2->name.'</td>'); // start date = 2024-06-16, end date = null
    SnappyPdf::assertSee($hostel_merit_demerit_setting_B->name);
    SnappyPdf::assertSee($hostel_reward_punishment_settings_B->name);
    SnappyPdf::assertSee(e($person_in_charge_A->name));
    SnappyPdf::assertSee($hostel_reward_punishment_records[1]->remark);

    // check reward punishment 3
    SnappyPdf::assertDontSee('2024-01-01');
    SnappyPdf::assertDontSee($student_B->student_number);
    SnappyPdf::assertDontSee(e($student_B->getTranslation('name', 'en')));
    SnappyPdf::assertDontSee($student_B->getTranslation('name', 'zh'));
    SnappyPdf::assertDontSee($hostel_merit_demerit_setting_C->name);
    SnappyPdf::assertDontSee($hostel_reward_punishment_settings_C->name);
    SnappyPdf::assertDontSee(e($person_in_charge_B->name));
    SnappyPdf::assertDontSee($hostel_reward_punishment_records[2]->remark);


    // Test with Room ID filter
    $this->partialMock(ReportPrintService::class, function (MockInterface $mock) {
        $mock->shouldReceive('upload')->once()->andReturnSelf();
        $mock->shouldReceive('getFileUrl')->once()->andReturn('some-random-url');
    });

    SnappyPdf::fake();

    $hostelReportService = resolve(HostelReportService::class);

    $payload = [
        'date_from' => '2024-01-01',
        'date_to' => '2024-12-31',
        'room_id' => $hostel_room_B->id
    ];

    $hostelReportService
        ->setExportType(ExportType::PDF->value)
        ->setReportViewName($view_name)
        ->setFileName('hostels-report-by-reward-punishment-student-unit-test')
        ->getHostelRewardPunishmentReportByRoom($payload);

    SnappyPdf::assertDontSee('Room: '.$hostel_room_A2->name .'/'. $hostel_room_bed_A2->name);
    SnappyPdf::assertSee('Room: '.$hostel_room_B->name .'/'. $hostel_room_bed_B->name);

    // check reward punishment 1
    SnappyPdf::assertDontSee('2024-06-15');
    SnappyPdf::assertDontSee($student_A->student_number);
    SnappyPdf::assertDontSee(e($student_A->getTranslation('name', 'en')));
    SnappyPdf::assertDontSee($student_A->getTranslation('name', 'zh'));
    SnappyPdf::assertDontSee('<td>'.$hostel_room_A->name .'/'. $hostel_room_bed_A->name.'</td>'); // start date = 2024-04-30, end date = 2024-06-16
    SnappyPdf::assertDontSee($hostel_merit_demerit_setting_A->name);
    SnappyPdf::assertDontSee($hostel_reward_punishment_settings_A->name);
    SnappyPdf::assertDontSee(e($person_in_charge_A->name));
    SnappyPdf::assertDontSee($hostel_reward_punishment_records[0]->remark);

    // check reward punishment 2
    SnappyPdf::assertDontSee('2024-12-31');
    SnappyPdf::assertDontSee('<td>'.$hostel_room_A2->name .'/'. $hostel_room_bed_A2->name.'</td>'); // start date = 2024-06-16, end date = null
    SnappyPdf::assertDontSee($hostel_merit_demerit_setting_B->name);
    SnappyPdf::assertDontSee($hostel_reward_punishment_settings_B->name);
    SnappyPdf::assertDontSee($hostel_reward_punishment_records[1]->remark);

    // check reward punishment 3
    SnappyPdf::assertSee('2024-01-01');
    SnappyPdf::assertSee($student_B->student_number);
    SnappyPdf::assertSee(e($student_B->getTranslation('name', 'en')));
    SnappyPdf::assertSee($student_B->getTranslation('name', 'zh'));
    SnappyPdf::assertDontSee('<td>'.$hostel_room_B->name .'/'. $hostel_room_bed_B->name.'</td>'); // start date = 2024-04-30, end date = null
    SnappyPdf::assertSee($hostel_merit_demerit_setting_C->name);
    SnappyPdf::assertSee($hostel_reward_punishment_settings_C->name);
    SnappyPdf::assertSee(e($person_in_charge_B->name));
    SnappyPdf::assertSee($hostel_reward_punishment_records[2]->remark);
});
