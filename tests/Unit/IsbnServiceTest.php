<?php

use App\Services\IsbnService;


beforeEach(function () {
    $this->isbnService = app()->make(IsbnService::class);
});

test('request()', function () {
    expect($this->isbnService->getItem())->toEqual([]);
    $this->isbnService->request('isbn:9780440539810');
    expect($this->isbnService->getItem())->toHaveKeys(['selfLink', 'volumeInfo', 'saleInfo', 'accessInfo']);
})->skip('Skip for now as the API has limit');

test('populateBookSize()', function () {
    $book_size_string = $this->isbnService->populateBookSize([]);
    expect($book_size_string)->toEqual("");

    $dimensions = [
        'test1' => '123',
        'test2' => '3121',
    ];
    $book_size_string = $this->isbnService->populateBookSize($dimensions);
    expect($book_size_string)->toEqual("test1: 123, test2: 3121");
});

test('mapItem() getBookTitle setBookTitle', function () {
    // default null
    expect($this->isbnService->getBookTitle())->toBeNull();

    // set item
    $item = [
        'volumeInfo' => [
            'title' => 123 // must be string
        ]
    ];
    $this->isbnService->setItem($item);
    $this->isbnService->mapItem();

    expect($this->isbnService->getBookTitle())->toBeNull();

    // set item
    $item = [
        'volumeInfo' => [
            'title' => '123'
        ]
    ];
    $this->isbnService->setItem($item);
    $this->isbnService->mapItem();

    // book title updated
    expect($this->isbnService->getBookTitle())->toEqual('123');
});

test('mapItem() getAuthors setAuthors', function () {
    // default empty array
    expect($this->isbnService->getAuthors())->toEqual([]);

    // set item
    $item = [
        'volumeInfo' => [
            'authors' => 123 // must be array
        ]
    ];
    $this->isbnService->setItem($item);
    $this->isbnService->mapItem();

    expect($this->isbnService->getAuthors())->toEqual([]);

    // set item
    $item = [
        'volumeInfo' => [
            'authors' => ['author1', 'author2']
        ]
    ];
    $this->isbnService->setItem($item);
    $this->isbnService->mapItem();

    // book authors updated
    expect($this->isbnService->getAuthors())->toEqual(['author1', 'author2']);
});

test('mapItem() getBookPage setBookPage', function () {
    // default null
    expect($this->isbnService->getBookPage())->toBeNull();

    // set item
    $item = [
        'volumeInfo' => [
            'pageCount' => "test" // must be integer
        ]
    ];
    $this->isbnService->setItem($item);
    $this->isbnService->mapItem();

    expect($this->isbnService->getBookPage())->toBeNull();

    // set item
    $item = [
        'volumeInfo' => [
            'pageCount' => 100
        ]
    ];
    $this->isbnService->setItem($item);
    $this->isbnService->mapItem();

    // book page updated
    expect($this->isbnService->getBookPage())->toEqual(100);
});

test('mapItem() getBookPublisher setBookPublisher', function () {
    // default null
    expect($this->isbnService->getBookPublisher())->toBeNull();

    // set item
    $item = [
        'volumeInfo' => [
            'publisher' => 123 // must be string
        ]
    ];
    $this->isbnService->setItem($item);
    $this->isbnService->mapItem();

    expect($this->isbnService->getBookPublisher())->toBeNull();

    // set item
    $item = [
        'volumeInfo' => [
            'publisher' => 'publisher'
        ]
    ];
    $this->isbnService->setItem($item);
    $this->isbnService->mapItem();

    // book publisher updated
    expect($this->isbnService->getBookPublisher())->toEqual('publisher');
});

test('mapItem() getBookPublishedDate setBookPublishedDate', function () {
    // default null
    expect($this->isbnService->getBookPublishedDate())->toBeNull();

    // set item
    $item = [
        'volumeInfo' => [
            'publishedDate' => 2024 // must be string
        ]
    ];
    $this->isbnService->setItem($item);
    $this->isbnService->mapItem();

    expect($this->isbnService->getBookPublishedDate())->toBeNull();

    // set item
    $item = [
        'volumeInfo' => [
            'publishedDate' => "2024" // if only year shouldn't update published date
        ]
    ];
    $this->isbnService->setItem($item);
    $this->isbnService->mapItem();

    expect($this->isbnService->getBookPublishedDate())->toBeNull();

    // set item
    $item = [
        'volumeInfo' => [
            'publishedDate' => "2024*"
        ]
    ];
    $this->isbnService->setItem($item);
    $this->isbnService->mapItem();

    // book published date updated
    expect($this->isbnService->getBookPublishedDate())->toBeNull();

    // set item
    $item = [
        'volumeInfo' => [
            'publishedDate' => "2024-01-01"
        ]
    ];
    $this->isbnService->setItem($item);
    $this->isbnService->mapItem();

    // book published date updated
    expect($this->isbnService->getBookPublishedDate())->toEqual('2024-01-01');
});

test('mapItem() getBookSize setBookSize', function () {
    // default null
    expect($this->isbnService->getBookSize())->toBeNull();

    // set item
    $item = [
        'volumeInfo' => [
            'dimensions' => 123 // must be array
        ]
    ];
    $this->isbnService->setItem($item);
    $this->isbnService->mapItem();

    expect($this->isbnService->getBookSize())->toBeNull();

    // set item
    $item = [
        'volumeInfo' => [
            'dimensions' => ["key" => "123"]
        ]
    ];
    $this->isbnService->setItem($item);
    $this->isbnService->mapItem();

    // book published date updated
    expect($this->isbnService->getBookSize())->toEqual("key: 123");
});
