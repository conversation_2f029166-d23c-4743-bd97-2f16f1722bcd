<?php

use App\Models\ClassModel;
use App\Models\ClassSubject;
use App\Models\ClassSubjectStudent;
use App\Models\ClassSubjectTeacher;
use App\Models\Employee;
use App\Models\Exam;
use App\Models\Grade;
use App\Models\GradingFramework;
use App\Models\GradingScheme;
use App\Models\GradingSchemeItem;
use App\Models\ReportCardOutput;
use App\Models\ReportCardOutputComponent;
use App\Models\ResultSource;
use App\Models\ResultSourceSubject;
use App\Models\ResultSourceSubjectComponent;
use App\Models\ResultsPostingHeader;
use App\Models\ResultsPostingLineItem;
use App\Models\SchoolProfile;
use App\Models\SemesterClass;
use App\Models\SemesterSetting;
use App\Models\SemesterYearSetting;
use App\Models\Student;
use App\Models\StudentClass;
use App\Models\StudentGradingFramework;
use App\Models\Subject;
use App\Repositories\ExamRepository;
use App\Services\Exam\ExamResultsPostingService;
use App\Services\Exam\StudentGradingFrameworkService;
use Carbon\Carbon;
use Database\Seeders\InternationalizationSeeder;
use Illuminate\Database\Eloquent\Factories\Sequence;

beforeEach(function () {
    $this->seed(InternationalizationSeeder::class);
    $this->examRepository = app(ExamRepository::class);
    $this->examResultsPostingService = app(ExamResultsPostingService::class);

    app()->setLocale('en');

    $this->testLocale = app()->getLocale();

    $this->table = resolve(Exam::class)->getTable();
});

test('getModelClass()', function () {
    $response = $this->examRepository->getModelClass();

    expect($response)->toEqual(Exam::class);
});

test('getAll()', function (int $expected_count, array $filters, array $expected_model) {
    $test_date = Carbon::parse('2024-05-01')->format('Y-m-d');
    Carbon::setTestNow($test_date);

    $keys = [
        'first' => Exam::factory()->create([
            'code' => 'A001',
            'name->en' => 'English Exam 1',
            'name->zh' => '英语考试1',
            'start_date' => Carbon::parse('2024-03-01')->format('Y-m-d'),
            'end_date' => Carbon::parse('2024-04-30')->format('Y-m-d'),
            'results_entry_period_from' => Carbon::parse('2024-04-25 16:00:00'),
            'results_entry_period_to' => Carbon::parse('2024-05-15 15:59:59'),
            'description' => 'This is the last exam'
        ]),
        'second' => Exam::factory()->create([
            'code' => 'A002',
            'name->en' => 'English Exam 2',
            'name->zh' => '英语考试2',
            'start_date' => Carbon::parse('2024-10-01')->format('Y-m-d'),
            'end_date' => Carbon::parse('2024-11-30')->format('Y-m-d'),
            'results_entry_period_from' => Carbon::parse('2024-10-25 16:00:00'),
            'results_entry_period_to' => Carbon::parse('2024-11-15 15:59:59'),
            'description' => 'This is the last last exam'
        ]),
        'third' => Exam::factory()->create([
            'code' => 'B003',
            'name->en' => 'Math Exam 1',
            'name->zh' => '数学考试 1',
            'start_date' => Carbon::parse('2024-03-01')->format('Y-m-d'),
            'end_date' => Carbon::parse('2024-04-30')->format('Y-m-d'),
            'results_entry_period_from' => Carbon::parse('2024-04-25 16:00:00'),
            'results_entry_period_to' => Carbon::parse('2024-05-15 15:59:59'),
            'description' => 'For real this time'
        ]),
    ];

    $result = $this->examRepository->getAll($filters)->toArray();
    expect($result)->toHaveCount($expected_count);

    foreach ($expected_model as $key => $value) {
        expect($result[$key])->toEqual($keys[$value]->toArray());
    }
})->with([
    'get all data' => [3, [], ['first', 'second', 'third']],
    'filter by name = English Exam 1' => [1, ['name' => 'English Exam 1'], ['first']],
    'filter by name = Math Exam 1' => [1, ['name' => 'Math Exam 1'], ['third']],
    'filter by name = English Exam' => [2, ['name' => 'English Exam'], ['first', 'second']],
    'filter by name = Non Existing' => [0, ['name' => 'Bla Bla'], []],
    'filter by code = A001' => [1, ['code' => 'A001'], ['first']],
    'filter by code = B001' => [1, ['code' => 'B003'], ['third']],
    'filter by code = NONEXISTINGCODE' => [0, ['code' => 'Bla Bla'], []],
    'filter by result entry period (April to June)' => [2, ['results_entry_period_open' => true], ['first', 'third']],
    'sort by id asc' => [3, ['order_by' => ['id' => 'asc']], ['first', 'second', 'third']],
    'sort by id desc' => [3, ['order_by' => ['id' => 'desc']], ['third', 'second', 'first']],
    'sort by code asc' => [3, ['order_by' => ['code' => 'asc']], ['first', 'second', 'third']],
    'sort by code desc' => [3, ['order_by' => ['code' => 'desc']], ['third', 'second', 'first']],
    'sort by name asc' => [3, ['order_by' => ['name' => 'asc']], ['first', 'second', 'third']],
    'sort by name desc' => [3, ['order_by' => ['name' => 'desc']], ['third', 'second', 'first']],
]);

test('getAllPaginated()', function (int $expected_count, array $filters, array $expected_model) {
    $test_date = Carbon::parse('2024-05-01')->format('Y-m-d');
    Carbon::setTestNow($test_date);

    $keys = [
        'first' => Exam::factory()->create([
            'code' => 'A001',
            'name->en' => 'English Exam 1',
            'name->zh' => '英语考试1',
            'start_date' => Carbon::parse('2024-03-01')->format('Y-m-d'),
            'end_date' => Carbon::parse('2024-04-30')->format('Y-m-d'),
            'results_entry_period_from' => Carbon::parse('2024-04-25 16:00:00'),
            'results_entry_period_to' => Carbon::parse('2024-05-15 15:59:59'),
            'description' => 'This is the last exam'
        ]),
        'second' => Exam::factory()->create([
            'code' => 'A002',
            'name->en' => 'English Exam 2',
            'name->zh' => '英语考试2',
            'start_date' => Carbon::parse('2024-10-01')->format('Y-m-d'),
            'end_date' => Carbon::parse('2024-11-30')->format('Y-m-d'),
            'results_entry_period_from' => Carbon::parse('2024-10-25 16:00:00'),
            'results_entry_period_to' => Carbon::parse('2024-11-15 15:59:59'),
            'description' => 'This is the last last exam'
        ]),
        'third' => Exam::factory()->create([
            'code' => 'B003',
            'name->en' => 'Math Exam 1',
            'name->zh' => '数学考试 1',
            'start_date' => Carbon::parse('2024-03-01')->format('Y-m-d'),
            'end_date' => Carbon::parse('2024-04-30')->format('Y-m-d'),
            'results_entry_period_from' => Carbon::parse('2024-04-25 16:00:00'),
            'results_entry_period_to' => Carbon::parse('2024-05-15 15:59:59'),
            'description' => 'For real this time'
        ]),
    ];

    $result = $this->examRepository->getAllPaginated($filters)->toArray();
    expect($result['data'])->toHaveCount($expected_count);

    foreach ($expected_model as $key => $value) {
        expect($result['data'][$key])->toEqual($keys[$value]->toArray());
    }
})->with([
    'filter by name = English Exam 1' => [1, ['name' => 'English Exam 1'], ['first']],
    'filter by name = Math Exam 1' => [1, ['name' => 'Math Exam 1'], ['third']],
    'filter by name = English Exam' => [2, ['name' => 'English Exam'], ['first', 'second']],
    'filter by name = Non Existing' => [0, ['name' => 'Bla Bla'], []],
    'filter by code = A001' => [1, ['code' => 'A001'], ['first']],
    'filter by code = B001' => [1, ['code' => 'B003'], ['third']],
    'filter by code = NONEXISTINGCODE' => [0, ['code' => 'Bla Bla'], []],
    'filter by result entry period (April to June)' => [
        2,
        [
            'results_entry_period_open' => true,
            'results_entry_period_from' => Carbon::parse('2024-04-01 00:00:00'),
            'results_entry_period_to' => Carbon::parse('2024-06-01 00:00:00'),
        ],
        ['first', 'third']
    ],
    'sort by id asc' => [3, ['order_by' => ['id' => 'asc']], ['first', 'second', 'third']],
    'sort by id desc' => [3, ['order_by' => ['id' => 'desc']], ['third', 'second', 'first']],
    'sort by code asc' => [3, ['order_by' => ['code' => 'asc']], ['first', 'second', 'third']],
    'sort by code desc' => [3, ['order_by' => ['code' => 'desc']], ['third', 'second', 'first']],
    'sort by name asc' => [3, ['order_by' => ['name' => 'asc']], ['first', 'second', 'third']],
    'sort by name desc' => [3, ['order_by' => ['name' => 'desc']], ['third', 'second', 'first']],
]);

function initExamData($settings, $subject_list)
{
    $gradingScheme = GradingScheme::factory()->withItems()->create([
        'name' => 'Default',
        'code' => 'DEFAULT',
    ]);

    GradingSchemeItem::factory()->create([
        'grading_scheme_id' => $gradingScheme->id,
        'from' => 0,
        'to' => 0,
        'name' => 'Not Applicable',
        'display_as_name' => 'N/A',
        'extra_marks' => 0,
    ]);
    GradingSchemeItem::factory()->create([
        'grading_scheme_id' => $gradingScheme->id,
        'from' => 1,
        'to' => 59.99,
        'name' => 'dumb dumb',
        'display_as_name' => 'FAILED',
        'extra_marks' => 0,
    ]);
    GradingSchemeItem::factory()->create([
        'grading_scheme_id' => $gradingScheme->id,
        'from' => 60,
        'to' => 69.99,
        'name' => 'C',
        'display_as_name' => 'C',
        'extra_marks' => 0,
    ]);
    GradingSchemeItem::factory()->create([
        'grading_scheme_id' => $gradingScheme->id,
        'from' => 70,
        'to' => 79.99,
        'name' => 'B',
        'display_as_name' => 'B',
        'extra_marks' => 0,
    ]);
    GradingSchemeItem::factory()->create([
        'grading_scheme_id' => $gradingScheme->id,
        'from' => 80,
        'to' => 89.99,
        'name' => 'A',
        'display_as_name' => 'A',
        'extra_marks' => 1,
    ]);
    GradingSchemeItem::factory()->create([
        'grading_scheme_id' => $gradingScheme->id,
        'from' => 90,
        'to' => 100,
        'name' => 'A+',
        'display_as_name' => 'A+',
        'extra_marks' => 2,
    ]);

    foreach ($settings as $semester_class_id => $student_list) {
        foreach ($subject_list as $subject) {
            $class_subject = ClassSubject::factory()->create([
                'semester_class_id' => $semester_class_id,
                'subject_id' => $subject->id,
            ]);
            ClassSubjectTeacher::factory()->create([
                'class_subject_id' => $class_subject->id
            ]);
            foreach ($student_list as $s) {
                ClassSubjectStudent::factory()->create([
                    'class_subject_id' => $class_subject->id,
                    'student_id' => $s->id,
                ]);
            }
        }
    }
}

test('examinationResultByExamReportData()', function () {

    $student2 = Student::factory()->create(['name' => 'Student2']);
    $student = Student::factory()->create(['name' => 'Student1']);
    $student3 = Student::factory()->create(['name' => 'Student3']);
    $student4 = Student::factory()->create(['name' => 'Student4']);

    $grading_framework = GradingFramework::factory()->create([
        'is_active' => true,
        'code' => 'SEM2',
        'configuration' => json_decode(file_get_contents(storage_path('tests/grading-framework-config-1.json')), true),
    ]);

    $junior_grade = Grade::factory()->create([
        'name->en' => 'Junior'
    ]);
    $senior_grade = Grade::factory()->create([
        'name->en' => 'Senior'
    ]);

    $junior_first_class = ClassModel::factory()->create([
        'name->en' => 'J111',
        'grade_id' => $junior_grade->id,
    ]);
    $junior_second_class = ClassModel::factory()->create([
        'name->en' => 'J112',
        'grade_id' => $junior_grade->id,
    ]);
    $senior_first_class = ClassModel::factory()->create([
        'name->en' => 'S111',
        'grade_id' => $senior_grade->id,
    ]);

    $semester_setting = SemesterSetting::factory()->create([
        'name' => '2024 Semester 2',
        'from' => '2024-06-01',
        'to' => '2024-12-30',
        'is_current_semester' => true,
        'code' => 'SEM2', // same as grading_framework code
        'semester_year_setting_id' => SemesterYearSetting::create(['year' => 2024])->id,
    ]);

    $first_semester_class = SemesterClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'class_id' => $junior_first_class->id,
        'is_active' => true
    ]);
    $second_semester_class = SemesterClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'class_id' => $junior_second_class->id,
        'is_active' => true
    ]);
    $third_semester_class = SemesterClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'class_id' => $senior_first_class->id,
        'is_active' => true
    ]);

    // student 1, 2 - junior first class
    // student 3 - junior second class
    // student 4 - senior first class
    $student2_class = StudentClass::factory()->create([
        'student_id' => $student2->id,
        'semester_class_id' => $first_semester_class->id,
        'seat_no' => 1,
        'semester_setting_id' => $semester_setting->id
    ]);
    $student_class = StudentClass::factory()->create([
        'student_id' => $student->id,
        'semester_class_id' => $first_semester_class->id,
        'seat_no' => 2,
        'semester_setting_id' => $semester_setting->id
    ]);
    $student3_class = StudentClass::factory()->create([
        'student_id' => $student3->id,
        'semester_class_id' => $second_semester_class->id,
        'seat_no' => 1,
        'semester_setting_id' => $semester_setting->id
    ]);
    $student4_class = StudentClass::factory()->create([
        'student_id' => $student4->id,
        'semester_class_id' => $third_semester_class->id,
        'seat_no' => 1,
        'semester_setting_id' => $semester_setting->id
    ]);

    $settings = [
        $first_semester_class->id => [$student, $student2],
        $second_semester_class->id => [$student3],
        $third_semester_class->id => [$student4],
    ];

    $exam_sem1 = Exam::factory()->create([
        'code' => 'SEM1EXAM',
        'results_entry_period_from' => '2024-11-20 16:00:00',
        'results_entry_period_to' => '2024-11-30 15:59:59'
    ]);
    $exam_sem2 = Exam::factory()->create([
        'code' => 'SEM2EXAM',
        'results_entry_period_from' => '2024-11-01 16:00:00',
        'results_entry_period_to' => '2024-11-21 15:59:59'
    ]);
    $exam_final = Exam::factory()->create([
        'code' => 'FINALEXAM',
        'results_entry_period_from' => '2024-12-01 16:00:00',
        'results_entry_period_to' => '2024-12-30 15:59:59'
    ]);

    $subjects = Subject::factory(5)->state(new Sequence(
        ['code' => '01', 'name->en' => 'BM', 'name->zh' => '马来西亚文'],
        ['code' => '02', 'name->en' => 'English', 'name->zh' => '英文'],
        ['code' => '03', 'name->en' => 'Math', 'name->zh' => '数学'],
        ['code' => '04', 'name->en' => 'History', 'name->zh' => '历史'],
        ['code' => '70', 'name->en' => 'Geography', 'name->zh' => '地理'],
    ))->create();

    initExamData($settings, $subjects);
    Carbon::setTestNow('2024-11-21');

    // setup student's grading framework
    $sgf1 = app()->make(StudentGradingFrameworkService::class)
        ->setStudent($student)
        ->setGradingFramework($grading_framework)
        ->applyGradingFrameworkForPeriod(\Carbon\Carbon::parse('2024-01-01'), \Carbon\Carbon::parse('2024-12-31'), 2024)
        ->getStudentGradingFramework();
    $sgf2 = app()->make(StudentGradingFrameworkService::class)
        ->setStudent($student2)
        ->setGradingFramework($grading_framework)
        ->applyGradingFrameworkForPeriod(\Carbon\Carbon::parse('2024-01-01'), \Carbon\Carbon::parse('2024-12-31'), 2024)
        ->getStudentGradingFramework();
    $sgf3 = app()->make(StudentGradingFrameworkService::class)
        ->setStudent($student3)
        ->setGradingFramework($grading_framework)
        ->applyGradingFrameworkForPeriod(\Carbon\Carbon::parse('2024-01-01'), \Carbon\Carbon::parse('2024-12-31'), 2024)
        ->getStudentGradingFramework();
    $sgf4 = app()->make(StudentGradingFrameworkService::class)
        ->setStudent($student4)
        ->setGradingFramework($grading_framework)
        ->applyGradingFrameworkForPeriod(\Carbon\Carbon::parse('2024-01-01'), \Carbon\Carbon::parse('2024-12-31'), 2024)
        ->getStudentGradingFramework();

    StudentGradingFramework::whereIn('id', [$sgf1->id, $sgf2->id, $sgf3->id, $sgf4->id])->update(['academic_year' => $semester_setting->semesterYearSetting->year]);

    ResultSourceSubject::truncate();
    ResultSourceSubjectComponent::truncate();

    $result_source_student1 = $sgf1->resultSources->where('code', $exam_sem2->code)->first();
    $result_source_student2 = $sgf2->resultSources->where('code', $exam_sem2->code)->first();
    $result_source_student3 = $sgf3->resultSources->where('code', $exam_sem2->code)->first();
    $result_source_student4 = $sgf4->resultSources->where('code', $exam_sem2->code)->first();

    // student 1 (BM - 0, English - null, Math - 80 (45 + 35), History - null, Geography - 70)
    // student 2 (BM - 80, English - 70, Math - 60, History - 77, Geography - 80)
    // student 3 (BM - 55, English - 66, Math - 77, History - 88, Geography - 99)
    // student 4 (BM - 11, English - 22, History - 44, Geography - 55)

    // student 1
    $student_subject_component_bm = ResultSourceSubjectComponent::factory()->create([
        'result_source_subject_id' => ResultSourceSubject::factory()->create([
            'subject_id' => $subjects[0]->id,
            'result_source_id' => $result_source_student1->id,
        ])->id,
        'code' => 'FINAL',
        'weightage_percent' => 100,
        'actual_score' => 0,
    ]);
    $student_subject_component_english = ResultSourceSubjectComponent::factory()->create([
        'result_source_subject_id' => ResultSourceSubject::factory()->create([
            'subject_id' => $subjects[1]->id,
            'result_source_id' => $result_source_student1->id,
        ])->id,
        'code' => 'FINAL',
        'weightage_percent' => 100,
        'actual_score' => null,
    ]);
    $student_result_source_subject_math = ResultSourceSubject::factory()->create([
        'subject_id' => $subjects[2]->id,
        'result_source_id' => $result_source_student1->id,
    ]);
    ResultSourceSubjectComponent::factory()->create([
        'result_source_subject_id' => $student_result_source_subject_math->id,
        'code' => 'FINAL',
        'weightage_percent' => 100,
        'actual_score' => 45,
    ]);
    ResultSourceSubjectComponent::factory()->create([
        'result_source_subject_id' => $student_result_source_subject_math->id,
        'code' => 'FINAL',
        'weightage_percent' => 100,
        'actual_score' => 35,
    ]);
    $student_subject_component_geography = ResultSourceSubjectComponent::factory()->create([
        'result_source_subject_id' => ResultSourceSubject::factory()->create([
            'subject_id' => $subjects[4]->id,
            'result_source_id' => $result_source_student1->id,
        ])->id,
        'code' => 'FINAL',
        'weightage_percent' => 100,
        'actual_score' => 70,
    ]);

    // student 2
    $student2_subject_component_bm = ResultSourceSubjectComponent::factory()->create([
        'result_source_subject_id' => ResultSourceSubject::factory()->create([
            'subject_id' => $subjects[0]->id,
            'result_source_id' => $result_source_student2->id,
        ])->id,
        'code' => 'FINAL',
        'weightage_percent' => 100,
        'actual_score' => 80,
    ]);
    $student2_subject_component_english = ResultSourceSubjectComponent::factory()->create([
        'result_source_subject_id' => ResultSourceSubject::factory()->create([
            'subject_id' => $subjects[1]->id,
            'result_source_id' => $result_source_student2->id,
        ])->id,
        'code' => 'FINAL',
        'weightage_percent' => 100,
        'actual_score' => 70,
    ]);
    $student2_subject_component_math = ResultSourceSubjectComponent::factory()->create([
        'result_source_subject_id' => ResultSourceSubject::factory()->create([
            'subject_id' => $subjects[2]->id,
            'result_source_id' => $result_source_student2->id,
        ])->id,
        'code' => 'FINAL',
        'weightage_percent' => 100,
        'actual_score' => 60,
    ]);
    $student2_subject_component_history = ResultSourceSubjectComponent::factory()->create([
        'result_source_subject_id' => ResultSourceSubject::factory()->create([
            'subject_id' => $subjects[3]->id,
            'result_source_id' => $result_source_student2->id,
        ])->id,
        'code' => 'FINAL',
        'weightage_percent' => 100,
        'actual_score' => 77,
    ]);
    $student2_subject_component_geography = ResultSourceSubjectComponent::factory()->create([
        'result_source_subject_id' => ResultSourceSubject::factory()->create([
            'subject_id' => $subjects[4]->id,
            'result_source_id' => $result_source_student2->id,
        ])->id,
        'code' => 'FINAL',
        'weightage_percent' => 100,
        'actual_score' => 80,
    ]);

    // student 3
    $student3_subject_component_bm = ResultSourceSubjectComponent::factory()->create([
        'result_source_subject_id' => ResultSourceSubject::factory()->create([
            'subject_id' => $subjects[0]->id,
            'result_source_id' => $result_source_student3->id,
        ])->id,
        'code' => 'FINAL',
        'weightage_percent' => 100,
        'actual_score' => 55,
    ]);
    $student3_subject_component_english = ResultSourceSubjectComponent::factory()->create([
        'result_source_subject_id' => ResultSourceSubject::factory()->create([
            'subject_id' => $subjects[1]->id,
            'result_source_id' => $result_source_student3->id,
        ])->id,
        'code' => 'FINAL',
        'weightage_percent' => 100,
        'actual_score' => 66,
    ]);
    $student3_subject_component_math = ResultSourceSubjectComponent::factory()->create([
        'result_source_subject_id' => ResultSourceSubject::factory()->create([
            'subject_id' => $subjects[2]->id,
            'result_source_id' => $result_source_student3->id,
        ])->id,
        'code' => 'FINAL',
        'weightage_percent' => 100,
        'actual_score' => 77,
    ]);
    $student3_subject_component_history = ResultSourceSubjectComponent::factory()->create([
        'result_source_subject_id' => ResultSourceSubject::factory()->create([
            'subject_id' => $subjects[3]->id,
            'result_source_id' => $result_source_student3->id,
        ])->id,
        'code' => 'FINAL',
        'weightage_percent' => 100,
        'actual_score' => 88,
    ]);
    $student3_subject_component_geography = ResultSourceSubjectComponent::factory()->create([
        'result_source_subject_id' => ResultSourceSubject::factory()->create([
            'subject_id' => $subjects[4]->id,
            'result_source_id' => $result_source_student3->id,
        ])->id,
        'code' => 'FINAL',
        'weightage_percent' => 100,
        'actual_score' => 99,
    ]);

    // student 4
    $student4_subject_component_bm = ResultSourceSubjectComponent::factory()->create([
        'result_source_subject_id' => ResultSourceSubject::factory()->create([
            'subject_id' => $subjects[0]->id,
            'result_source_id' => $result_source_student4->id,
        ])->id,
        'code' => 'FINAL',
        'weightage_percent' => 100,
        'actual_score' => 11,
    ]);
    $student4_subject_component_english = ResultSourceSubjectComponent::factory()->create([
        'result_source_subject_id' => ResultSourceSubject::factory()->create([
            'subject_id' => $subjects[1]->id,
            'result_source_id' => $result_source_student4->id,
        ])->id,
        'code' => 'FINAL',
        'weightage_percent' => 100,
        'actual_score' => 22,
    ]);
    $student4_subject_component_history = ResultSourceSubjectComponent::factory()->create([
        'result_source_subject_id' => ResultSourceSubject::factory()->create([
            'subject_id' => $subjects[3]->id,
            'result_source_id' => $result_source_student4->id,
        ])->id,
        'code' => 'FINAL',
        'weightage_percent' => 100,
        'actual_score' => 44,
    ]);
    $student4_subject_component_geography = ResultSourceSubjectComponent::factory()->create([
        'result_source_subject_id' => ResultSourceSubject::factory()->create([
            'subject_id' => $subjects[4]->id,
            'result_source_id' => $result_source_student4->id,
        ])->id,
        'code' => 'FINAL',
        'weightage_percent' => 100,
        'actual_score' => 55,
    ]);

    $data = $this->examRepository->examinationResultByExamReportData($semester_setting, $exam_sem2, [
        $first_semester_class->id,
        $third_semester_class->id,
    ]);

    expect($data)->toMatchArray([
        'data_by_classes' => [
            [
                'class_name' => 'J111',
                'header' => [
                    'seat_no' => 'Seat No.',
                    'student_no' => 'Student No.',
                    'student_name' => 'Student Name',
                    'subject_' . $subjects[0]->id => 'BM',
                    'subject_' . $subjects[1]->id => 'English',
                    'subject_' . $subjects[2]->id => 'Math',
                    'subject_' . $subjects[3]->id => 'History',
                    'subject_' . $subjects[4]->id => 'Geography',
                ],
                'data' => [
                    [
                        'seat_no' => $student2_class->seat_no, // 1
                        'student_no' => $student2->student_number,
                        'student_name' => $student2->getFormattedTranslations('name'),
                        'subject_' . $subjects[0]->id => (int) $this->examResultsPostingService->calculateResultSourceSubjectActualScore($student2_subject_component_bm->resultSourceSubject),
                        'subject_' . $subjects[1]->id => (int) $this->examResultsPostingService->calculateResultSourceSubjectActualScore($student2_subject_component_english->resultSourceSubject),
                        'subject_' . $subjects[2]->id => (int) $this->examResultsPostingService->calculateResultSourceSubjectActualScore($student2_subject_component_math->resultSourceSubject),
                        'subject_' . $subjects[3]->id => (int) $this->examResultsPostingService->calculateResultSourceSubjectActualScore($student2_subject_component_history->resultSourceSubject),
                        'subject_' . $subjects[4]->id => (int) $this->examResultsPostingService->calculateResultSourceSubjectActualScore($student2_subject_component_geography->resultSourceSubject),
                    ],
                    [
                        'seat_no' => $student_class->seat_no, // 2
                        'student_no' => $student->student_number,
                        'student_name' => $student->getFormattedTranslations('name'),
                        'subject_' . $subjects[0]->id => '0', // $student_subject_component_bm
                        'subject_' . $subjects[1]->id => '0', // $student_subject_component_english
                        'subject_' . $subjects[2]->id => '80',
                        'subject_' . $subjects[3]->id => null,
                        'subject_' . $subjects[4]->id => (int) $this->examResultsPostingService->calculateResultSourceSubjectActualScore($student_subject_component_geography->resultSourceSubject),
                    ],
                ]
            ],
            [
                'class_name' => 'S111',
                'header' => [
                    'seat_no' => 'Seat No.',
                    'student_no' => 'Student No.',
                    'student_name' => 'Student Name',
                    'subject_' . $subjects[0]->id => 'BM',
                    'subject_' . $subjects[1]->id => 'English',
                    'subject_' . $subjects[3]->id => 'History',
                    'subject_' . $subjects[4]->id => 'Geography',
                ],
                'data' => [
                    [
                        'seat_no' => $student4_class->seat_no, // 1
                        'student_no' => $student4->student_number,
                        'student_name' => $student4->getFormattedTranslations('name'),
                        'subject_' . $subjects[0]->id => (int) $this->examResultsPostingService->calculateResultSourceSubjectActualScore($student4_subject_component_bm->resultSourceSubject),
                        'subject_' . $subjects[1]->id => (int) $this->examResultsPostingService->calculateResultSourceSubjectActualScore($student4_subject_component_english->resultSourceSubject),
                        'subject_' . $subjects[3]->id => (int) $this->examResultsPostingService->calculateResultSourceSubjectActualScore($student4_subject_component_history->resultSourceSubject),
                        'subject_' . $subjects[4]->id => (int) $this->examResultsPostingService->calculateResultSourceSubjectActualScore($student4_subject_component_geography->resultSourceSubject),
                    ],
                ]
            ],
        ]
    ]);
});

test('examinationResultByStudentReportData()', function () {

    $student2 = Student::factory()->create(['name' => 'Student2']);
    $student = Student::factory()->create(['name' => 'Student1']);
    $student3 = Student::factory()->create(['name' => 'Student3']);
    $student4 = Student::factory()->create(['name' => 'Student4']);

    $grading_framework = GradingFramework::factory()->create([
        'is_active' => true,
        'code' => 'SEM2',
        'configuration' => json_decode(file_get_contents(storage_path('tests/grading-framework-config-1.json')), true),
    ]);

    $junior_grade = Grade::factory()->create([
        'name->en' => 'Junior'
    ]);
    $senior_grade = Grade::factory()->create([
        'name->en' => 'Senior'
    ]);

    $junior_first_class = ClassModel::factory()->create([
        'name->en' => 'J111',
        'grade_id' => $junior_grade->id,
    ]);
    $junior_second_class = ClassModel::factory()->create([
        'name->en' => 'J112',
        'grade_id' => $junior_grade->id,
    ]);
    $senior_first_class = ClassModel::factory()->create([
        'name->en' => 'S111',
        'grade_id' => $senior_grade->id,
    ]);

    $semester_setting = SemesterSetting::factory()->create([
        'name' => '2024 Semester 2',
        'from' => '2024-06-01',
        'to' => '2024-12-30',
        'is_current_semester' => true,
        'code' => 'SEM2', // same as grading_framework code
        'semester_year_setting_id' => SemesterYearSetting::create(['year' => 2024])->id,
    ]);

    $employee_junior_first_class = Employee::factory()->create([
        'name->en' => 'junior first class teacher',
        'name->zh' => 'junior first class teacher ZH',
    ]);
    $employee_junior_second_class = Employee::factory()->create([
        'name->en' => 'junior second class teacher',
        'name->zh' => 'junior second class teacher ZH',
    ]);
    $employee_senior_first_class = Employee::factory()->create([
        'name->en' => 'senior first class teacher',
        'name->zh' => 'senior first class teacher ZH',
    ]);
    $first_semester_class = SemesterClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'class_id' => $junior_first_class->id,
        'is_active' => true,
        'homeroom_teacher_id' => $employee_junior_first_class->id,
    ]);
    $second_semester_class = SemesterClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'class_id' => $junior_second_class->id,
        'is_active' => true,
        'homeroom_teacher_id' => $employee_junior_second_class->id,
    ]);
    $third_semester_class = SemesterClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'class_id' => $senior_first_class->id,
        'is_active' => true,
        'homeroom_teacher_id' => $employee_senior_first_class->id,
    ]);

    // student 1, 2 - junior first class
    // student 3 - junior second class
    // student 4 - senior first class
    $student2_class = StudentClass::factory()->create([
        'student_id' => $student2->id,
        'semester_class_id' => $first_semester_class->id,
        'seat_no' => 1,
        'semester_setting_id' => $semester_setting->id
    ]);
    $student_class = StudentClass::factory()->create([
        'student_id' => $student->id,
        'semester_class_id' => $first_semester_class->id,
        'seat_no' => 2,
        'semester_setting_id' => $semester_setting->id
    ]);
    $student3_class = StudentClass::factory()->create([
        'student_id' => $student3->id,
        'semester_class_id' => $second_semester_class->id,
        'seat_no' => 1,
        'semester_setting_id' => $semester_setting->id
    ]);
    $student4_class = StudentClass::factory()->create([
        'student_id' => $student4->id,
        'semester_class_id' => $third_semester_class->id,
        'seat_no' => 1,
        'semester_setting_id' => $semester_setting->id
    ]);

    $settings = [
        $first_semester_class->id => [$student, $student2],
        $second_semester_class->id => [$student3],
        $third_semester_class->id => [$student4],
    ];

    $exam_sem1 = Exam::factory()->create([
        'code' => 'SEM1EXAM',
        'results_entry_period_from' => '2024-11-20 16:00:00',
        'results_entry_period_to' => '2024-11-30 15:59:59'
    ]);
    $exam_sem2 = Exam::factory()->create([
        'code' => 'SEM2EXAM',
        'results_entry_period_from' => '2024-11-01 16:00:00',
        'results_entry_period_to' => '2024-11-21 15:59:59'
    ]);
    $exam_final = Exam::factory()->create([
        'code' => 'FINALEXAM',
        'results_entry_period_from' => '2024-12-01 16:00:00',
        'results_entry_period_to' => '2024-12-30 15:59:59'
    ]);

    $subjects = Subject::factory(5)->state(new Sequence(
        ['code' => '01', 'name->en' => 'BM', 'name->zh' => '马来西亚文'],
        ['code' => '02', 'name->en' => 'English', 'name->zh' => '英文'],
        ['code' => '03', 'name->en' => 'Math', 'name->zh' => '数学'],
        ['code' => '04', 'name->en' => 'History', 'name->zh' => '历史'],
        ['code' => '70', 'name->en' => 'Geography', 'name->zh' => '地理'],
    ))->create();

    initExamData($settings, $subjects);
    Carbon::setTestNow('2024-11-21');

    // setup student's grading framework
    $sgf1 = app()->make(StudentGradingFrameworkService::class)
        ->setStudent($student)
        ->setGradingFramework($grading_framework)
        ->applyGradingFrameworkForPeriod(\Carbon\Carbon::parse('2024-01-01'), \Carbon\Carbon::parse('2024-12-31'), 2024)
        ->getStudentGradingFramework();
    $sgf2 = app()->make(StudentGradingFrameworkService::class)
        ->setStudent($student2)
        ->setGradingFramework($grading_framework)
        ->applyGradingFrameworkForPeriod(\Carbon\Carbon::parse('2024-01-01'), \Carbon\Carbon::parse('2024-12-31'), 2024)
        ->getStudentGradingFramework();
    $sgf3 = app()->make(StudentGradingFrameworkService::class)
        ->setStudent($student3)
        ->setGradingFramework($grading_framework)
        ->applyGradingFrameworkForPeriod(\Carbon\Carbon::parse('2024-01-01'), \Carbon\Carbon::parse('2024-12-31'), 2024)
        ->getStudentGradingFramework();
    $sgf4 = app()->make(StudentGradingFrameworkService::class)
        ->setStudent($student4)
        ->setGradingFramework($grading_framework)
        ->applyGradingFrameworkForPeriod(\Carbon\Carbon::parse('2024-01-01'), \Carbon\Carbon::parse('2024-12-31'), 2024)
        ->getStudentGradingFramework();

    StudentGradingFramework::whereIn('id', [$sgf1->id, $sgf2->id, $sgf3->id, $sgf4->id])->update(['academic_year' => $semester_setting->semesterYearSetting->year]);

    ResultSourceSubject::truncate();
    ResultSourceSubjectComponent::truncate();

    $result_source_student1 = $sgf1->resultSources->where('code', $exam_sem2->code)->first();
    $result_source_student2 = $sgf2->resultSources->where('code', $exam_sem2->code)->first();
    $result_source_student3 = $sgf3->resultSources->where('code', $exam_sem2->code)->first();
    $result_source_student4 = $sgf4->resultSources->where('code', $exam_sem2->code)->first();

    // student 1 (BM - 0, English - null, Math - 80 (45 + 35), History - null, Geography - 70)
    // student 2 (BM - 80, English - 70, Math - 60, History - 77, Geography - 80)
    // student 3 (BM - 55, English - 66, Math - 77, History - 88, Geography - 99)
    // student 4 (BM - 11, English - 22, Math - 33, History - 44, Geography - 55)

    // student 1
    $student_subject_component_bm = ResultSourceSubjectComponent::factory()->create([
        'result_source_subject_id' => ResultSourceSubject::factory()->create([
            'subject_id' => $subjects[0]->id,
            'result_source_id' => $result_source_student1->id,
        ])->id,
        'code' => 'FINAL',
        'weightage_percent' => 100,
        'actual_score' => 0,
    ]);
    $student_subject_component_english = ResultSourceSubjectComponent::factory()->create([
        'result_source_subject_id' => ResultSourceSubject::factory()->create([
            'subject_id' => $subjects[1]->id,
            'result_source_id' => $result_source_student1->id,
        ])->id,
        'code' => 'FINAL',
        'weightage_percent' => 100,
        'actual_score' => null,
    ]);
    $student_result_source_subject_math = ResultSourceSubject::factory()->create([
        'subject_id' => $subjects[2]->id,
        'result_source_id' => $result_source_student1->id,
    ]);
    ResultSourceSubjectComponent::factory()->create([
        'result_source_subject_id' => $student_result_source_subject_math->id,
        'code' => 'FINAL',
        'weightage_percent' => 100,
        'actual_score' => 45,
    ]);
    ResultSourceSubjectComponent::factory()->create([
        'result_source_subject_id' => $student_result_source_subject_math->id,
        'code' => 'FINAL',
        'weightage_percent' => 100,
        'actual_score' => 35,
    ]);
    $student_subject_component_geography = ResultSourceSubjectComponent::factory()->create([
        'result_source_subject_id' => ResultSourceSubject::factory()->create([
            'subject_id' => $subjects[4]->id,
            'result_source_id' => $result_source_student1->id,
        ])->id,
        'code' => 'FINAL',
        'weightage_percent' => 100,
        'actual_score' => 70,
    ]);

    // student 2
    $student2_subject_component_bm = ResultSourceSubjectComponent::factory()->create([
        'result_source_subject_id' => ResultSourceSubject::factory()->create([
            'subject_id' => $subjects[0]->id,
            'result_source_id' => $result_source_student2->id,
        ])->id,
        'code' => 'FINAL',
        'weightage_percent' => 100,
        'actual_score' => 80,
    ]);
    $student2_subject_component_english = ResultSourceSubjectComponent::factory()->create([
        'result_source_subject_id' => ResultSourceSubject::factory()->create([
            'subject_id' => $subjects[1]->id,
            'result_source_id' => $result_source_student2->id,
        ])->id,
        'code' => 'FINAL',
        'weightage_percent' => 100,
        'actual_score' => 70,
    ]);
    $student2_subject_component_math = ResultSourceSubjectComponent::factory()->create([
        'result_source_subject_id' => ResultSourceSubject::factory()->create([
            'subject_id' => $subjects[2]->id,
            'result_source_id' => $result_source_student2->id,
        ])->id,
        'code' => 'FINAL',
        'weightage_percent' => 100,
        'actual_score' => 60,
    ]);
    $student2_subject_component_history = ResultSourceSubjectComponent::factory()->create([
        'result_source_subject_id' => ResultSourceSubject::factory()->create([
            'subject_id' => $subjects[3]->id,
            'result_source_id' => $result_source_student2->id,
        ])->id,
        'code' => 'FINAL',
        'weightage_percent' => 100,
        'actual_score' => 77,
    ]);
    $student2_subject_component_geography = ResultSourceSubjectComponent::factory()->create([
        'result_source_subject_id' => ResultSourceSubject::factory()->create([
            'subject_id' => $subjects[4]->id,
            'result_source_id' => $result_source_student2->id,
        ])->id,
        'code' => 'FINAL',
        'weightage_percent' => 100,
        'actual_score' => 80,
    ]);

    // student 3
    $student3_subject_component_bm = ResultSourceSubjectComponent::factory()->create([
        'result_source_subject_id' => ResultSourceSubject::factory()->create([
            'subject_id' => $subjects[0]->id,
            'result_source_id' => $result_source_student3->id,
        ])->id,
        'code' => 'FINAL',
        'weightage_percent' => 100,
        'actual_score' => 55,
    ]);
    $student3_subject_component_english = ResultSourceSubjectComponent::factory()->create([
        'result_source_subject_id' => ResultSourceSubject::factory()->create([
            'subject_id' => $subjects[1]->id,
            'result_source_id' => $result_source_student3->id,
        ])->id,
        'code' => 'FINAL',
        'weightage_percent' => 100,
        'actual_score' => 66,
    ]);
    $student3_subject_component_math = ResultSourceSubjectComponent::factory()->create([
        'result_source_subject_id' => ResultSourceSubject::factory()->create([
            'subject_id' => $subjects[2]->id,
            'result_source_id' => $result_source_student3->id,
        ])->id,
        'code' => 'FINAL',
        'weightage_percent' => 100,
        'actual_score' => 77,
    ]);
    $student3_subject_component_history = ResultSourceSubjectComponent::factory()->create([
        'result_source_subject_id' => ResultSourceSubject::factory()->create([
            'subject_id' => $subjects[3]->id,
            'result_source_id' => $result_source_student3->id,
        ])->id,
        'code' => 'FINAL',
        'weightage_percent' => 100,
        'actual_score' => 88,
    ]);
    $student3_subject_component_geography = ResultSourceSubjectComponent::factory()->create([
        'result_source_subject_id' => ResultSourceSubject::factory()->create([
            'subject_id' => $subjects[4]->id,
            'result_source_id' => $result_source_student3->id,
        ])->id,
        'code' => 'FINAL',
        'weightage_percent' => 100,
        'actual_score' => 99,
    ]);

    // student 4
    $student4_subject_component_bm = ResultSourceSubjectComponent::factory()->create([
        'result_source_subject_id' => ResultSourceSubject::factory()->create([
            'subject_id' => $subjects[0]->id,
            'result_source_id' => $result_source_student4->id,
        ])->id,
        'code' => 'FINAL',
        'weightage_percent' => 100,
        'actual_score' => 11,
    ]);
    $student4_subject_component_english = ResultSourceSubjectComponent::factory()->create([
        'result_source_subject_id' => ResultSourceSubject::factory()->create([
            'subject_id' => $subjects[1]->id,
            'result_source_id' => $result_source_student4->id,
        ])->id,
        'code' => 'FINAL',
        'weightage_percent' => 100,
        'actual_score' => 22,
    ]);
    $student4_subject_component_math = ResultSourceSubjectComponent::factory()->create([
        'result_source_subject_id' => ResultSourceSubject::factory()->create([
            'subject_id' => $subjects[2]->id,
            'result_source_id' => $result_source_student4->id,
        ])->id,
        'code' => 'FINAL',
        'weightage_percent' => 100,
        'actual_score' => 33,
    ]);
    $student4_subject_component_history = ResultSourceSubjectComponent::factory()->create([
        'result_source_subject_id' => ResultSourceSubject::factory()->create([
            'subject_id' => $subjects[3]->id,
            'result_source_id' => $result_source_student4->id,
        ])->id,
        'code' => 'FINAL',
        'weightage_percent' => 100,
        'actual_score' => 44,
    ]);
    $student4_subject_component_geography = ResultSourceSubjectComponent::factory()->create([
        'result_source_subject_id' => ResultSourceSubject::factory()->create([
            'subject_id' => $subjects[4]->id,
            'result_source_id' => $result_source_student4->id,
        ])->id,
        'code' => 'FINAL',
        'weightage_percent' => 100,
        'actual_score' => 55,
    ]);

    SchoolProfile::factory()->create([
        'principal_name->en' => 'principal name',
        'principal_name->zh' => 'principal name ZH',
    ]);

    $data = $this->examRepository->examinationResultByStudentReportData($semester_setting, $exam_sem2, [
        $first_semester_class->id,
        $third_semester_class->id,
    ]);

    expect($data)->toMatchArray([
        [
            'exam' => $exam_sem2->getFormattedTranslations('name'),
            'seat_no' => 1,
            'student_no' => $student2->student_number,
            'student_name_zh' => $student2->getTranslation('name', 'zh'),
            'student_name_en' => $student2->getTranslation('name', 'en'),
            'class_name' => $junior_first_class->getTranslation('name', 'en'),
            'homeroom_teacher' => 'junior first class teacher ZH',
            'principal_name' => 'principal name ZH',
            'scores' => [
                [
                    'subject_name_zh' => '马来西亚文',
                    'subject_name_en' => 'BM',
                    'score' => '80.00',
                ],
                [
                    'subject_name_zh' => '英文',
                    'subject_name_en' => 'English',
                    'score' => '70.00',
                ],
                [
                    'subject_name_zh' => '数学',
                    'subject_name_en' => 'Math',
                    'score' => '60.00',
                ],
                [
                    'subject_name_zh' => '历史',
                    'subject_name_en' => 'History',
                    'score' => '77.00',
                ],
                [
                    'subject_name_zh' => '地理',
                    'subject_name_en' => 'Geography',
                    'score' => '80.00',
                ],
            ],
        ],
        [
            'exam' => $exam_sem2->getFormattedTranslations('name'),
            'seat_no' => 2,
            'student_no' => $student->student_number,
            'student_name_zh' => $student->getTranslation('name', 'zh'),
            'student_name_en' => $student->getTranslation('name', 'en'),
            'class_name' => $junior_first_class->getTranslation('name', 'en'),
            'homeroom_teacher' => 'junior first class teacher ZH',
            'principal_name' => 'principal name ZH',
            'scores' => [
                [
                    'subject_name_zh' => '马来西亚文',
                    'subject_name_en' => 'BM',
                    'score' => '0.00',
                ],
                [
                    'subject_name_zh' => '英文',
                    'subject_name_en' => 'English',
                    'score' => null,
                ],
                [
                    'subject_name_zh' => '数学',
                    'subject_name_en' => 'Math',
                    'score' => '80.00',
                ],
                [
                    'subject_name_zh' => '地理',
                    'subject_name_en' => 'Geography',
                    'score' => '70.00',
                ],
            ],
        ],
        [
            'exam' => $exam_sem2->getFormattedTranslations('name'),
            'seat_no' => 1,
            'student_no' => $student4->student_number,
            'student_name_zh' => $student4->getTranslation('name', 'zh'),
            'student_name_en' => $student4->getTranslation('name', 'en'),
            'class_name' => $senior_first_class->getTranslation('name', 'en'),
            'homeroom_teacher' => 'senior first class teacher ZH',
            'principal_name' => 'principal name ZH',
            'scores' => [
                [
                    'subject_name_zh' => '马来西亚文',
                    'subject_name_en' => 'BM',
                    'score' => '11.00',
                ],
                [
                    'subject_name_zh' => '英文',
                    'subject_name_en' => 'English',
                    'score' => '22.00',
                ],
                [
                    'subject_name_zh' => '数学',
                    'subject_name_en' => 'Math',
                    'score' => '33.00',
                ],
                [
                    'subject_name_zh' => '历史',
                    'subject_name_en' => 'History',
                    'score' => '44.00',
                ],
                [
                    'subject_name_zh' => '地理',
                    'subject_name_en' => 'Geography',
                    'score' => '55.00',
                ],
            ],
        ],
    ]);
});

test('examinationResultBySemesterClassData', function () {
    $student2 = Student::factory()->create(['name' => 'Student2']);
    $student = Student::factory()->create(['name' => 'Student1']);
    $student3 = Student::factory()->create(['name' => 'Student3']);

    $junior_grade = Grade::factory()->create([
        'name->en' => 'Junior'
    ]);
    $junior_first_class = ClassModel::factory()->create([
        'name->en' => 'J111',
        'name->zh' => '一年11班',
        'grade_id' => $junior_grade->id,
    ]);
    $junior_second_class = ClassModel::factory()->create([
        'name->en' => 'J112',
        'name->zh' => '一年12班',
        'grade_id' => $junior_grade->id,
    ]);
    $semester_setting = SemesterSetting::factory()->create([
        'semester_year_setting_id' => SemesterYearSetting::factory()->create([
            'year' => 2025
        ]),
    ]);
    $first_semester_class = SemesterClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'class_id' => $junior_first_class->id,
        'is_active' => true
    ]);
    $second_semester_class = SemesterClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'class_id' => $junior_second_class->id,
        'is_active' => true
    ]);
    // student 1, 2 - junior first class
    // student 3 - junior second class
    $student2_class = StudentClass::factory()->create([
        'student_id' => $student2->id,
        'semester_class_id' => $first_semester_class->id,
        'seat_no' => 1,
        'semester_setting_id' => $semester_setting->id
    ]);
    $student_class = StudentClass::factory()->create([
        'student_id' => $student->id,
        'semester_class_id' => $first_semester_class->id,
        'seat_no' => 2,
        'semester_setting_id' => $semester_setting->id
    ]);
    $student3_class = StudentClass::factory()->create([
        'student_id' => $student3->id,
        'semester_class_id' => $second_semester_class->id,
        'seat_no' => 1,
        'semester_setting_id' => $semester_setting->id
    ]);

    $header1 = ResultsPostingHeader::factory()->create([
        'status' => ResultsPostingHeader::STATUS_COMPLETED,
        'student_ids' => [$student->id, $student2->id, $student3->id],
        'semester_setting_id' => $semester_setting->id,
        'grade_id' => $junior_grade->id,
        'report_card_output_code' => 'SEM1RESULT',
    ]);

    $chinese_subject = Subject::factory()->create([
        'name->en' => 'Chinese Language',
        'name->zh' => '华文',
        'code' => 'CHINESE'
    ]);

    $english_subject = Subject::factory()->create([
        'name->en' => 'English Language',
        'name->zh' => '英文',
        'code' => 'ENGLISH'
    ]);

    $english_oral_subject = Subject::factory()->create([
        'name->en' => 'English Oral',
        'name->zh' => '英文会话',
        'code' => 'ENG_ORAL'
    ]);

    // result source
    $result_source_sem1 = ResultSource::factory()->create([
        'code' => 'S001',
        'name->en' => '1st Semester',
        'name->zh' => '第一学期',
        'student_grading_framework_id' => StudentGradingFramework::factory()->create([
            'student_id' => $student->id,
            'academic_year' => '2025'
        ]),
    ]);
    $result_source_sem2 = ResultSource::factory()->create([
        'code' => 'S002',
        'name->en' => '2nd Semester',
        'name->zh' => '第二学期',
        'student_grading_framework_id' => StudentGradingFramework::factory()->create([
            'student_id' => $student->id,
            'academic_year' => '2025'
        ]),
    ]);
    $result_source_subject_english_sem1 = ResultSourceSubject::factory()->create([
        'result_source_id' => $result_source_sem1->id,
        'subject_id' => $english_subject->id,
        'is_exempted' => false,
    ]);
    $result_source_subject_english_sem2 = ResultSourceSubject::factory()->create([
        'result_source_id' => $result_source_sem2->id,
        'subject_id' => $english_subject->id,
        'is_exempted' => true,
    ]);

    $line_items = [
        [
            'header_id' => $header1->id,
            'semester_class_id' => $first_semester_class->id,
            'student_id' => $student->id,
            'subject_id' => null,
            'report_card_output_component_code' => 'GT',
            'total' => 1440.44,
        ],
        [
            'header_id' => $header1->id,
            'semester_class_id' => $first_semester_class->id,
            'student_id' => $student->id,
            'subject_id' => null,
            'report_card_output_component_code' => 'GA',
            'total' => 38.88,
        ],
        [
            'header_id' => $header1->id,
            'semester_class_id' => $first_semester_class->id,
            'student_id' => $student->id,
            'subject_id' => null,
            'report_card_output_component_code' => 'MS',
            'total' => 4,
        ],
        [
            'header_id' => $header1->id,
            'semester_class_id' => $first_semester_class->id,
            'student_id' => $student->id,
            'subject_id' => null,
            'report_card_output_component_code' => 'MA',
            'total' => 2,
        ],
        [
            'header_id' => $header1->id,
            'semester_class_id' => $first_semester_class->id,
            'student_id' => $student->id,
            'subject_id' => null,
            'report_card_output_component_code' => 'SYS_NET_AVG',
            'total' => 40,
            'label' => 'D',
            'class_rank' => '2',
            'class_population' => '15',
            'grade_rank' => '2',
            'grade_population' => '15',
        ],
        [
            'header_id' => $header1->id,
            'semester_class_id' => $first_semester_class->id,
            'student_id' => $student->id,
            'subject_id' => null,
            'report_card_output_component_code' => 'CONDUCT',
            'label' => 'C',
        ],
        [
            'header_id' => $header1->id,
            'semester_class_id' => $first_semester_class->id,
            'student_id' => $student->id,
            'report_card_output_component_code' => $chinese_subject->code,
            'subject_id' => $chinese_subject->id,
            'total' => 70.7,
            'output_type' => ReportCardOutputComponent::OUTPUT_TYPE_SCORE,
        ],
        [
            'header_id' => $header1->id,
            'semester_class_id' => $first_semester_class->id,
            'student_id' => $student->id,
            'report_card_output_component_id' => ReportCardOutputComponent::factory()->create([
                'total_formula' => 'VAR("RESULTSOURCE[' . $result_source_sem2->code . '].SUBJECT[].score")'
            ])->id,
            'report_card_output_component_code' => $english_subject->code,
            'subject_id' => $english_subject->id,
            'total' => 80,
            'output_type' => null,
        ],
        [
            'header_id' => $header1->id,
            'semester_class_id' => $first_semester_class->id,
            'student_id' => $student->id,
            'report_card_output_component_code' => $english_oral_subject->code,
            'subject_id' => $english_oral_subject->id,
            'label' => 'A+',
            'output_type' => ReportCardOutputComponent::OUTPUT_TYPE_GRADE,
        ],
        [
            'header_id' => $header1->id,
            'semester_class_id' => $first_semester_class->id,
            'student_id' => $student2->id,
            'subject_id' => null,
            'report_card_output_component_code' => 'CONDUCT',
            'label' => 'E',
        ],
        [
            'header_id' => $header1->id,
            'semester_class_id' => $first_semester_class->id,
            'student_id' => $student2->id,
            'subject_id' => null,
            'report_card_output_component_code' => 'SYS_NET_AVG',
            'total' => 38,
            'label' => 'E',
            'class_rank' => '11',
            'class_population' => '15',
            'grade_rank' => '11',
            'grade_population' => '15',
        ],
        [
            'header_id' => $header1->id,
            'semester_class_id' => $second_semester_class->id,
            'student_id' => $student3->id,
            'subject_id' => null,
            'report_card_output_component_code' => 'CONDUCT',
            'label' => 'A',
        ],
        [
            'header_id' => $header1->id,
            'semester_class_id' => $second_semester_class->id,
            'student_id' => $student3->id,
            'report_card_output_component_code' => $chinese_subject->code,
            'subject_id' => $chinese_subject->id,
            'total' => 90,
            'output_type' => ReportCardOutputComponent::OUTPUT_TYPE_SCORE,
        ],
        [
            'header_id' => $header1->id,
            'semester_class_id' => $second_semester_class->id,
            'student_id' => $student3->id,
            'report_card_output_component_code' => $english_oral_subject->code,
            'subject_id' => $english_oral_subject->id,
            'label' => 'A',
            'output_type' => ReportCardOutputComponent::OUTPUT_TYPE_GRADE,
        ],
    ];

    foreach ($line_items as $line_item) {
        ResultsPostingLineItem::factory()->create($line_item);
    }

    $data = $this->examRepository->examinationResultBySemesterClassData($header1, $first_semester_class, true);
    expect($data)->toMatchArray([
        'class' => $junior_first_class->getFormattedTranslations('name'),
        'header' => [
            'seat_no' => 'Seat No.',
            'student_no' => 'Student No.',
            'student_name_en' => 'Student Name',
            'student_name_zh' => 'Student Name',
            'gross_total' => 'Gross Total',
            'gross_average' => 'Gross Average',
            'mark_subtracted' => 'Mark Subtracted',
            'mark_added' => 'Mark Added',
            'net_average' => 'Net Average',
            'grade_exam' => 'Grade',
            'position_in_class' => 'Position In Class',
            'position_in_standard' => 'Position In Standard',
            'conduct' => 'Conduct',
            'subject_' . $chinese_subject->id => 'Chinese Language',
            'subject_' . $english_subject->id => 'English Language',
            'subject_' . $english_oral_subject->id => 'English Oral',
        ],
        'body' => [
            [
                'seat_no' => $student_class->seat_no,
                'student_no' => $student->student_number,
                'student_name_en' => $student->getTranslation('name', 'en'),
                'student_name_zh' => $student->getTranslation('name', 'zh'),
                'gross_total' => '1440.44',
                'gross_average' => '38.88',
                'mark_subtracted' => '4.00',
                'mark_added' => '2.00',
                'net_average' => '40.00',
                'grade_exam' => 'D',
                'position_in_class' => '2 / 15',
                'position_in_standard' => '2 / 15',
                'conduct' => 'C',
                'subject_' . $chinese_subject->id => '70.70',
                'subject_' . $english_subject->id => '-', // is_exempted = true
                'subject_' . $english_oral_subject->id => 'A+',
            ],
            [
                'seat_no' => $student2_class->seat_no,
                'student_no' => $student2->student_number,
                'student_name_en' => $student2->getTranslation('name', 'en'),
                'student_name_zh' => $student2->getTranslation('name', 'zh'),
                'gross_total' => '',
                'gross_average' => '',
                'mark_subtracted' => '',
                'mark_added' => '',
                'net_average' => '38.00',
                'grade_exam' => 'E',
                'position_in_class' => '11 / 15',
                'position_in_standard' => '11 / 15',
                'conduct' => 'E',
                'subject_' . $chinese_subject->id => '',
                'subject_' . $english_subject->id => '',
                'subject_' . $english_oral_subject->id => '',
            ],
        ]
    ]);

    // without position_in_standard
    $data = $this->examRepository->examinationResultBySemesterClassData($header1, $first_semester_class, false);
    expect($data)->toMatchArray([
        'class' => $junior_first_class->getFormattedTranslations('name'),
        'header' => [
            'seat_no' => 'Seat No.',
            'student_no' => 'Student No.',
            'student_name_en' => 'Student Name',
            'student_name_zh' => 'Student Name',
            'gross_total' => 'Gross Total',
            'gross_average' => 'Gross Average',
            'mark_subtracted' => 'Mark Subtracted',
            'mark_added' => 'Mark Added',
            'net_average' => 'Net Average',
            'grade_exam' => 'Grade',
            'position_in_class' => 'Position In Class',
            'conduct' => 'Conduct',
            'subject_' . $chinese_subject->id => 'Chinese Language',
            'subject_' . $english_subject->id => 'English Language',
            'subject_' . $english_oral_subject->id => 'English Oral',
        ],
        'body' => [
            [
                'seat_no' => $student_class->seat_no,
                'student_no' => $student->student_number,
                'student_name_en' => $student->getTranslation('name', 'en'),
                'student_name_zh' => $student->getTranslation('name', 'zh'),
                'gross_total' => '1440.44',
                'gross_average' => '38.88',
                'mark_subtracted' => '4.00',
                'mark_added' => '2.00',
                'net_average' => '40.00',
                'grade_exam' => 'D',
                'position_in_class' => '2 / 15',
                'conduct' => 'C',
                'subject_' . $chinese_subject->id => '70.70',
                'subject_' . $english_subject->id => '-', // is_exempted = true
                'subject_' . $english_oral_subject->id => 'A+',
            ],
            [
                'seat_no' => $student2_class->seat_no,
                'student_no' => $student2->student_number,
                'student_name_en' => $student2->getTranslation('name', 'en'),
                'student_name_zh' => $student2->getTranslation('name', 'zh'),
                'gross_total' => '',
                'gross_average' => '',
                'mark_subtracted' => '',
                'mark_added' => '',
                'net_average' => '38.00',
                'grade_exam' => 'E',
                'position_in_class' => '11 / 15',
                'conduct' => 'E',
                'subject_' . $chinese_subject->id => '',
                'subject_' . $english_subject->id => '',
                'subject_' . $english_oral_subject->id => '',
            ],
        ]
    ]);
});

test('determineStudentIsExempted', function () {
    $student = Student::factory()->create(['name' => 'Student1']); // with student grading framework
    $student2 = Student::factory()->create(['name' => 'Student2']); // without student grading framework

    $chinese_subject = Subject::factory()->create([
        'name->en' => 'Chinese Language',
        'name->zh' => '华文',
        'code' => 'CHINESE'
    ]);
    $english_subject = Subject::factory()->create([
        'name->en' => 'English Language',
        'name->zh' => '英文',
        'code' => 'ENGLISH'
    ]);

    $student_grading_framework = StudentGradingFramework::factory()->create([
        'student_id' => $student->id,
        'academic_year' => '2025'
    ]);

    $result_source_sem1 = ResultSource::factory()->create([
        'code' => 'SEM1EXAM',
        'name->en' => '1st Semester',
        'name->zh' => '第一学期',
        'student_grading_framework_id' => $student_grading_framework->id,
    ]);

    $result_source_sem2 = ResultSource::factory()->create([
        'code' => 'SEM2EXAM',
        'name->en' => '2nd Semester',
        'name->zh' => '第二学期',
        'student_grading_framework_id' => $student_grading_framework->id,
    ]);

    $result_source_subject_sem1_english = ResultSourceSubject::factory()->create([
        'result_source_id' => $result_source_sem1->id,
        'subject_id' => $english_subject->id,
        'is_exempted' => false,
    ]);

    $result_source_subject_sem2_english = ResultSourceSubject::factory()->create([
        'result_source_id' => $result_source_sem2->id,
        'subject_id' => $english_subject->id,
        'is_exempted' => false,
    ]);

    $report_card_output_2025_sem1result = ReportCardOutput::factory()->create([
        'code' => 'SEM1RESULT',
        'student_grading_framework_id' => $student_grading_framework->id,
    ]);

    $report_card_output_2025_sem2result = ReportCardOutput::factory()->create([
        'code' => 'SEM2RESULT',
        'student_grading_framework_id' => $student_grading_framework->id,
    ]);

    $report_card_output_2025_finalresult = ReportCardOutput::factory()->create([
        'code' => 'FINALRESULT',
        'student_grading_framework_id' => $student_grading_framework->id,
        'is_final' => true
    ]);

    $sem1result_component = ReportCardOutputComponent::factory()->create([
        'report_card_output_id' => $report_card_output_2025_sem1result->id,
        'code' => 'ENGLISH',
    ]);

    $sem2result_component = ReportCardOutputComponent::factory()->create([
        'report_card_output_id' => $report_card_output_2025_sem2result->id,
        'code' => 'ENGLISH',
    ]);

    $finalresult_component = ReportCardOutputComponent::factory()->create([
        'report_card_output_id' => $report_card_output_2025_finalresult->id,
        'code' => 'ENGLISH',
    ]);


    $chinese_component = ReportCardOutputComponent::factory()->create([
        'report_card_output_id' => $report_card_output_2025_sem1result->id,
        'code' => 'CHINESE',
        'subject_id' => $chinese_subject->id,
        'total_formula' => null,
    ]);

    $english_component1 = ReportCardOutputComponent::factory()->create([
        'report_card_output_id' => $report_card_output_2025_sem1result->id,
        'code' => 'ENGLISH',
        'subject_id' => $english_subject->id,
        'total_formula' => 'VAR("RESULTSOURCE[' . $result_source_sem1->code . '].SUBJECT[ENGLISH].score")'
    ]);
    $english_component2 = ReportCardOutputComponent::factory()->create([
        'report_card_output_id' => $report_card_output_2025_sem2result->id,
        'code' => 'ENGLISH',
        'subject_id' => $english_subject->id,
        'total_formula' => 'VAR("RESULTSOURCE[' . $result_source_sem2->code . '].SUBJECT[ENGLISH].score")'
    ]);
    $english_component3 = ReportCardOutputComponent::factory()->create([
        'report_card_output_id' => $report_card_output_2025_finalresult->id,
        'code' => 'ENGLISH',
        'subject_id' => $english_subject->id,
        'total_formula' => '(VAR("RESULTSOURCE[' . $result_source_sem1->code . '].SUBJECT[ENGLISH].score") * 0.4) + (VAR("RESULTSOURCE[' . $result_source_sem2->code . '].SUBJECT[ENGLISH].score") * 0.6)'
    ]);

    // total formula is null
    expect($this->examRepository->determineStudentIsExempted($chinese_subject->id, $chinese_component, $student))->toBeFalse();
    // without student grading framework
    expect($this->examRepository->determineStudentIsExempted($english_subject->id, $english_component1, $student2))->toBeFalse();
    // result source subject not found
    expect($this->examRepository->determineStudentIsExempted($english_subject->id, $english_component1, $student))->toBeFalse();
    // SEM1EXAM and SEM2EXAM both not exempted
    expect($this->examRepository->determineStudentIsExempted($english_subject->id, $english_component3, $student))->toBeFalse();
    // $result_source_subject_english's is_exempted = false
    expect($this->examRepository->determineStudentIsExempted($english_subject->id, $english_component1, $student))->toBeFalse();
    // $result_source_subject_english's is_exempted = true
    $result_source_subject_sem1_english->update([
        'is_exempted' => true,
    ]);
    $student->refresh();
    // SEM1EXAM is exempted
    expect($this->examRepository->determineStudentIsExempted($english_subject->id, $english_component1, $student))->toBeTrue();
    // SEM2EXAM is not exempted
    expect($this->examRepository->determineStudentIsExempted($english_subject->id, $english_component2, $student))->toBeFalse();
    // Result to be true because SEM1EXAM is exempted
    expect($this->examRepository->determineStudentIsExempted($english_subject->id, $english_component3, $student))->toBeTrue();

    $result_source_subject_sem1_english->update([
        'is_exempted' => false,
    ]);
    $result_source_subject_sem2_english->update([
        'is_exempted' => true,
    ]);
    $student->refresh();
    // SEM1EXAM is not exempted
    expect($this->examRepository->determineStudentIsExempted($english_subject->id, $english_component1, $student))->toBeFalse();
    // SEM2EXAM is exempted
    expect($this->examRepository->determineStudentIsExempted($english_subject->id, $english_component2, $student))->toBeTrue();
    // Result to be true because SEM2EXAM is exempted
    expect($this->examRepository->determineStudentIsExempted($english_subject->id, $english_component3, $student))->toBeTrue();
});
