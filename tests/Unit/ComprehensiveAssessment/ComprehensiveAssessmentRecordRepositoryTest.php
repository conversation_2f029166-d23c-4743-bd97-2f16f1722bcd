<?php

use App\Enums\ComprehensiveAssessmentResult;
use App\Models\ComprehensiveAssessmentCategory;
use App\Models\ComprehensiveAssessmentQuestion;
use App\Models\ComprehensiveAssessmentRecord;
use App\Models\Employee;
use App\Models\SemesterClass;
use App\Models\SemesterSetting;
use App\Models\Student;
use App\Repositories\ComprehensiveAssessmentRecordRepository;

beforeEach(function () {
    $this->comprehensiveAssessmentRecordRepository = resolve(ComprehensiveAssessmentRecordRepository::class);

    app()->setLocale('en');

    $this->testLocale = app()->getLocale();

    $this->first_comprehensive_assessment_category = ComprehensiveAssessmentCategory::factory()->create();

    $this->second_comprehensive_assessment_category = ComprehensiveAssessmentCategory::factory()->create();

    $this->first_comprehensive_assessment_question = ComprehensiveAssessmentQuestion::factory()->create([
        'comprehensive_assessment_category_id' => $this->first_comprehensive_assessment_category->id,
    ]);

    $this->second_comprehensive_assessment_question = ComprehensiveAssessmentQuestion::factory()->create([
        'comprehensive_assessment_category_id' => $this->second_comprehensive_assessment_category->id,
    ]);

    $this->first_semester_setting = SemesterSetting::factory()->create();

    $this->second_semester_setting = SemesterSetting::factory()->create();

    $this->first_student = Student::factory()->create();

    $this->second_student = Student::factory()->create();

    $this->first_class = SemesterClass::factory()->create([
        'semester_setting_id' => $this->first_semester_setting->id,
    ]);

    $this->second_class = SemesterClass::factory()->create([
        'semester_setting_id' => $this->second_semester_setting->id,
    ]);

    $this->first_employee_user = Employee::factory()->create()->user;

    $this->second_employee_user = Employee::factory()->create()->user;
});

test('getModelClass()', function () {
    $response = $this->comprehensiveAssessmentRecordRepository->getModelClass();

    expect($response)->toEqual(ComprehensiveAssessmentRecord::class);
});

test('getAll()', function () {
    $first_comprehensive_assessment_result = ComprehensiveAssessmentRecord::factory()->create([
        'semester_setting_id' => $this->first_semester_setting->id,
        'student_id' => $this->first_student->id,
        'semester_class_id' => $this->first_class->id,
        'comprehensive_assessment_question_id' => $this->first_comprehensive_assessment_question->id,
        'result' => ComprehensiveAssessmentResult::ACHIEVED,
        'created_by_id' => $this->first_employee_user->id,
    ]);

    $second_comprehensive_assessment_result = ComprehensiveAssessmentRecord::factory()->create([
        'semester_setting_id' => $this->first_semester_setting->id,
        'student_id' => $this->first_student->id,
        'semester_class_id' => $this->first_class->id,
        'comprehensive_assessment_question_id' => $this->second_comprehensive_assessment_question->id,
        'result' => ComprehensiveAssessmentResult::ACHIEVED,
        'created_by_id' => $this->first_employee_user->id,
    ]);

    $third_comprehensive_assessment_result = ComprehensiveAssessmentRecord::factory()->create([
        'semester_setting_id' => $this->second_semester_setting->id,
        'student_id' => $this->second_student->id,
        'semester_class_id' => $this->second_class->id,
        'comprehensive_assessment_question_id' => $this->first_comprehensive_assessment_question->id,
        'result' => ComprehensiveAssessmentResult::ACHIEVED,
        'created_by_id' => $this->second_employee_user->id,
    ]);


    $response = $this->comprehensiveAssessmentRecordRepository->getAll(['order_by' => ['id' => 'asc']])->toArray();

    expect($response)->toEqual([
        $first_comprehensive_assessment_result->toArray(),
        $second_comprehensive_assessment_result->toArray(),
        $third_comprehensive_assessment_result->toArray(),
    ]);

    //filter by comprehensive_assessment_category_id
    $response = $this->comprehensiveAssessmentRecordRepository->getAllPaginated([
        'comprehensive_assessment_category_id' => $this->first_comprehensive_assessment_category->id,
        'order_by' => ['id' => 'asc'],
    ])->toArray();

    expect($response['data'])->toHaveCount(2)
        ->sequence(
            fn($item) => $item->toEqual($first_comprehensive_assessment_result->toArray()),
            fn($item) => $item->toEqual($third_comprehensive_assessment_result->toArray()),
        );

    // Filter by semester_class_id
    $response = $this->comprehensiveAssessmentRecordRepository->getAllPaginated([
        'semester_class_id' => $this->first_class->id,
        'order_by' => ['id' => 'asc'],
    ])->toArray();

    expect($response['data'])->toHaveCount(2)
        ->sequence(
            fn($item) => $item->toEqual($first_comprehensive_assessment_result->toArray()),
            fn($item) => $item->toEqual($second_comprehensive_assessment_result->toArray())
        );

    // Filter by student_id
    $response = $this->comprehensiveAssessmentRecordRepository->getAllPaginated([
        'student_id' => $this->first_student->id,
        'order_by' => ['id' => 'asc'],
    ])->toArray();

    expect($response['data'])->toHaveCount(2)
        ->sequence(
            fn($item) => $item->toEqual($first_comprehensive_assessment_result->toArray()),
            fn($item) => $item->toEqual($second_comprehensive_assessment_result->toArray())
        );

    // Filter by semester_setting_id
    $response = $this->comprehensiveAssessmentRecordRepository->getAllPaginated([
        'semester_setting_id' => $this->first_semester_setting->id,
        'order_by' => ['id' => 'asc'],
    ])->toArray();

    expect($response['data'])->toHaveCount(2)
        ->sequence(
            fn($item) => $item->toEqual($first_comprehensive_assessment_result->toArray()),
            fn($item) => $item->toEqual($second_comprehensive_assessment_result->toArray())
        );
});

test('getAllPaginated()', function () {
    $first_comprehensive_assessment_result = ComprehensiveAssessmentRecord::factory()->create([
        'semester_setting_id' => $this->first_semester_setting->id,
        'student_id' => $this->first_student->id,
        'semester_class_id' => $this->first_class->id,
        'comprehensive_assessment_question_id' => $this->first_comprehensive_assessment_question->id,
        'result' => ComprehensiveAssessmentResult::ACHIEVED,
        'created_by_id' => $this->first_employee_user->id,
    ]);

    $second_comprehensive_assessment_result = ComprehensiveAssessmentRecord::factory()->create([
        'semester_setting_id' => $this->first_semester_setting->id,
        'student_id' => $this->first_student->id,
        'semester_class_id' => $this->first_class->id,
        'comprehensive_assessment_question_id' => $this->second_comprehensive_assessment_question->id,
        'result' => ComprehensiveAssessmentResult::ACHIEVED,
        'created_by_id' => $this->first_employee_user->id,
    ]);

    $third_comprehensive_assessment_result = ComprehensiveAssessmentRecord::factory()->create([
        'semester_setting_id' => $this->second_semester_setting->id,
        'student_id' => $this->second_student->id,
        'semester_class_id' => $this->second_class->id,
        'comprehensive_assessment_question_id' => $this->first_comprehensive_assessment_question->id,
        'result' => ComprehensiveAssessmentResult::ACHIEVED,
        'created_by_id' => $this->second_employee_user->id,
    ]);

    // Sort by id asc
    $response = $this->comprehensiveAssessmentRecordRepository->getAllPaginated([
        'order_by' => ['id' => 'asc'],
    ])->toArray();

    expect($response['data'][0]['id'])->toEqual($first_comprehensive_assessment_result->id)
        ->and($response['data'][1]['id'])->toEqual($second_comprehensive_assessment_result->id)
        ->and($response['data'][2]['id'])->toEqual($third_comprehensive_assessment_result->id);


    // Sort by id desc
    $response = $this->comprehensiveAssessmentRecordRepository->getAllPaginated([
        'order_by' => ['id' => 'desc'],
    ])->toArray();

    expect($response['data'][0]['id'])->toEqual($third_comprehensive_assessment_result->id)
        ->and($response['data'][1]['id'])->toEqual($second_comprehensive_assessment_result->id)
        ->and($response['data'][2]['id'])->toEqual($first_comprehensive_assessment_result->id);

});
