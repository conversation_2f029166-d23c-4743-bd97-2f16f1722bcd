<?php

use App\Enums\ClassType;
use App\Enums\ExportType;
use App\Exports\BillingDocumentsByDailyCollectionExport;
use App\Models\Bank;
use App\Models\BillingDocument;
use App\Models\BillingDocumentLineItem;
use App\Models\ClassModel;
use App\Models\CurrentStudentClassAndGrade;
use App\Models\DiscountSetting;
use App\Models\GlAccount;
use App\Models\Grade;
use App\Models\Payment;
use App\Models\PaymentMethod;
use App\Models\PaymentRequest;
use App\Models\Product;
use App\Models\SemesterClass;
use App\Models\SemesterSetting;
use App\Models\Student;
use App\Models\StudentClass;
use App\Models\UnpaidItem;
use App\Repositories\BillingDocumentRepository;
use App\Services\DocumentPrintService;
use App\Services\ReportPrintService;
use App\Services\Reports\BillingDocumentReportService;
use Carbon\Carbon;
use Database\Seeders\InternationalizationSeeder;
use Illuminate\Database\Eloquent\Factories\Sequence;
use Maatwebsite\Excel\Facades\Excel;
use Mockery\MockInterface;

beforeEach(function () {
    $this->seed(InternationalizationSeeder::class);
});

test('getDailyCollectionReportData, test excel content, with or without product_ids (ADVANCE + Discount + Normal Line Item)', function () {

    $grade = Grade::factory()->create([
        'name->en' => 'J1',
    ]);
    $class = ClassModel::factory()->create(['name->en' => 'J111', 'grade_id' => $grade->id]);

    // ensure that only main class is used
    $elective_class = ClassModel::factory()->create(['name->en' => 'other', 'grade_id' => null, 'type' => ClassType::ELECTIVE]);
    $cocu_class = ClassModel::factory()->create(['name->en' => 'cocu', 'grade_id' => null, 'type' => ClassType::SOCIETY]);

    $semester_setting1 = SemesterSetting::factory()->create([
        'name' => '2025 Semester 1',
        'is_current_semester' => true,
    ]);

    $semester_class1 = SemesterClass::factory()->create([
        'semester_setting_id' => $semester_setting1->id,
        'class_id' => $class->id,
    ]);

    $students = Student::factory(2)->state(new Sequence(
        [
            'name->en' => 'Albert',
            'name->zh' => '阿尔',
        ],
        [
            'name->en' => 'Bobby',
            'name->zh' => '鲍比',
        ],
    ))->create();

    foreach ($students as $student) {
        StudentClass::factory()->create([
            'semester_setting_id' => $semester_setting1->id,
            'semester_class_id' => $semester_class1->id,
            'student_id' => $student->id,
            'class_type' => ClassType::PRIMARY
        ]);
    }

    $product = Product::factory()->create([
        'name->en' => 'School Fees',
        'code' => '**********',
        'gl_account_code' => GlAccount::CODE_SCHOOL_FEES,
    ]);

    $unrelated_product = Product::factory()->create([
        'name->en' => 'Other Fees',
        'code' => '**********',
        'gl_account_code' => GlAccount::CODE_OTHERS,
    ]);

    $albert_discount = DiscountSetting::factory()->create([
        'basis' => DiscountSetting::BASIS_FIXED_AMOUNT,
        'basis_amount' => 40,
        'max_amount' => null,
        'used_amount' => 0,
        'effective_from' => '2024-01-01',
        'effective_to' => '2024-12-31',
        'gl_account_codes' => json_encode([GlAccount::CODE_SCHOOL_FEES]),
        'source_type' => null,
        'source_id' => null,
        'userable_type' => Student::class,
        'userable_id' => $students[0]->id,
        'is_active' => true,
    ]);

    $bobby_discount = DiscountSetting::factory()->create([
        'basis' => DiscountSetting::BASIS_FIXED_AMOUNT,
        'basis_amount' => 50,
        'max_amount' => null,
        'used_amount' => 0,
        'effective_from' => '2024-01-01',
        'effective_to' => '2024-12-31',
        'gl_account_codes' => json_encode([GlAccount::CODE_SCHOOL_FEES]),
        'source_type' => null,
        'source_id' => null,
        'userable_type' => Student::class,
        'userable_id' => $students[1]->id,
        'is_active' => true,
    ]);

    $billing_documents = BillingDocument::factory(2)->state(new Sequence(
        [
            'status' => BillingDocument::STATUS_CONFIRMED,
            'payment_status' => BillingDocument::PAYMENT_STATUS_PAID,
            'type' => BillingDocument::TYPE_INVOICE,
            'sub_type' => BillingDocument::SUB_TYPE_FEES,
            'document_date' => '2024-12-01',
            'amount_after_tax' => 300, // 400 (line_item_1) - 40 (discount_line_item_1) - 60 (advance) = 300
            'bill_to_type' => $students[0]->getBillToType(),
            'bill_to_id' => $students[0]->getBillToId(),
            'bill_to_name' => $students[0]->getBillToName(),
            'bill_to_reference_number' => $students[0]->getBillToReferenceNumber(),
            'paid_at' => '2024-12-01 12:40:00',
        ],
        [
            'status' => BillingDocument::STATUS_CONFIRMED,
            'payment_status' => BillingDocument::PAYMENT_STATUS_PAID,
            'type' => BillingDocument::TYPE_INVOICE,
            'sub_type' => BillingDocument::SUB_TYPE_FEES,
            'document_date' => '2024-09-01',
            'amount_after_tax' => 300, // 100 (line_item_1) + 350 (line_item_2) - 50 (discount_line_item_1) - 50 (discount_line_item_2) - 50 (advance)= 300
            'bill_to_type' => $students[1]->getBillToType(),
            'bill_to_id' => $students[1]->getBillToId(),
            'bill_to_name' => $students[1]->getBillToName(),
            'bill_to_reference_number' => $students[1]->getBillToReferenceNumber(),
            'paid_at' => '2024-09-01 12:40:00',
        ],
    ))->create();

    $unpaid_items = UnpaidItem::factory(3)->state(new Sequence(
        [
            'status' => UnpaidItem::STATUS_PAID,
            'billing_document_id' => $billing_documents[0]->id,
            'description' => 'Albert School Fees Jan 2024',
            'period' => '2024-01-01',
        ],
        [
            'status' => UnpaidItem::STATUS_PAID,
            'billing_document_id' => $billing_documents[1]->id,
            'description' => 'Bobby School Fees Mar 2024',
            'period' => '2024-03-01',
        ],
        [
            'status' => UnpaidItem::STATUS_PAID,
            'billing_document_id' => $billing_documents[1]->id,
            'description' => 'Bobby School Fees April 2024',
            'period' => '2024-04-01',
        ],
    ))->create();

    $line_items = BillingDocumentLineItem::factory(8)->state(new Sequence(
    // $billing_documents[0]
        [
            'billing_document_id' => $billing_documents[0]->id,
            'description' => 'Albert School Fees Jan 2024 $product',
            'billable_item_id' => $unpaid_items[0]->id,
            'billable_item_type' => get_class($unpaid_items[0]),
            'amount_before_tax' => 400,
            'product_id' => $product->id,
            'gl_account_code' => $product->gl_account_code,
        ],
        [
            'billing_document_id' => $billing_documents[0]->id,
            'product_id' => $product->id,
            'gl_account_code' => $product->gl_account_code,
            'billable_item_type' => null,
            'billable_item_id' => null,
            'is_discount' => true,
            'discount_id' => $albert_discount->id,
            'discount_original_line_item_id' => null, // to be updated next
            'description' => 'Discount for Albert School Fees Jan 2024 $product',
            'amount_before_tax' => -60,
        ],
        [
            'billing_document_id' => $billing_documents[0]->id,
            'amount_before_tax' => -40,
            'gl_account_code' => GlAccount::CODE_SCHOOL_FEES,
            'offset_billing_document_id' => $billing_documents[0]->id,
            'description' => 'Less advance payment for Albert SCH0001.',
            'product_id' => null,
            'billable_item_type' => null,
            'billable_item_id' => null,
            'is_discount' => false,
            'discount_id' => null,
            'discount_original_line_item_id' => null,
        ],
        // $billing_documents[1]
        [
            'billing_document_id' => $billing_documents[1]->id,
            'description' => 'Bobby School Fees Mar 2024 $unrelated_product',
            'billable_item_id' => $unpaid_items[1]->id,
            'billable_item_type' => get_class($unpaid_items[1]),
            'amount_before_tax' => 100,
            'product_id' => $unrelated_product->id,
            'gl_account_code' => $unrelated_product->gl_account_code,
        ],
        [
            'billing_document_id' => $billing_documents[1]->id,
            'description' => 'Bobby School Fees April 2024 $product',
            'billable_item_id' => $unpaid_items[2]->id,
            'billable_item_type' => get_class($unpaid_items[2]),
            'amount_before_tax' => 350,
            'product_id' => $product->id,
            'gl_account_code' => $product->gl_account_code,
        ],
        [
            'billing_document_id' => $billing_documents[1]->id,
            'product_id' => $product->id,
            'gl_account_code' => $product->gl_account_code,
            'billable_item_type' => null,
            'billable_item_id' => null,
            'is_discount' => true,
            'discount_id' => $bobby_discount->id,
            'discount_original_line_item_id' => null, // to be updated next
            'description' => 'Discount for Bobby School Fees Mar 2024 $unrelated_product',
            'amount_before_tax' => -50,
        ],
        [
            'billing_document_id' => $billing_documents[1]->id,
            'product_id' => $product->id,
            'gl_account_code' => $product->gl_account_code,
            'billable_item_type' => null,
            'billable_item_id' => null,
            'is_discount' => true,
            'discount_id' => $bobby_discount->id,
            'discount_original_line_item_id' => null, // to be updated next
            'description' => 'Discount for Bobby School Fees April 2024 $product',
            'amount_before_tax' => -50,
        ],
        [
            'billing_document_id' => $billing_documents[1]->id,
            'amount_before_tax' => -50,
            'gl_account_code' => GlAccount::CODE_SCHOOL_FEES,
            'offset_billing_document_id' => $billing_documents[1]->id,
            'description' => "Less advance payment for Bobby SCH0002.",
            'product_id' => null,
            'billable_item_type' => null,
            'billable_item_id' => null,
            'is_discount' => false,
            'discount_id' => null,
            'discount_original_line_item_id' => null,
        ],
    ))->create();

    // update discount line item with the original line item id
    $line_items[1]->update([
        'discount_original_line_item_id' => $line_items[0]->id,
    ]);
    $line_items[5]->update([
        'discount_original_line_item_id' => $line_items[3]->id,
    ]);
    $line_items[6]->update([
        'discount_original_line_item_id' => $line_items[4]->id,
    ]);

    $payment_methods = PaymentMethod::factory(4)->state(new Sequence(
        [
            'code' => PaymentMethod::CODE_CASH,
            'name' => 'Cash',
        ],
        [
            'code' => PaymentMethod::CODE_FPX,
            'name' => 'FPX',
        ],
        [
            'code' => PaymentMethod::CODE_CHEQUE,
            'name' => 'Cheque',
        ],
        [
            'code' => PaymentMethod::CODE_BANK_TRANSFER,
            'name' => 'Bank Transfer',
        ]
    ))->create();

    $maybank = Bank::factory()->create([
        'name' => 'Maybank',
        'code' => 'MBB',
    ]);

    $payment_requests = PaymentRequest::factory(4)->state(new Sequence(
        [
            'billing_document_id' => $billing_documents[0]->id,
            'payment_method_id' => $payment_methods[0]->id, // Cash
            'payment_reference_no' => 'CASH-1111',
            'amount' => 100,
            'bank_id' => null,
            'status' => PaymentRequest::STATUS_APPROVED,
        ],
        [
            'billing_document_id' => $billing_documents[0]->id,
            'payment_method_id' => $payment_methods[1]->id, // FPX
            'payment_reference_no' => 'FPX-2222',
            'amount' => 200,
            'bank_id' => null,
            'status' => PaymentRequest::STATUS_APPROVED,
        ],
        [
            'billing_document_id' => $billing_documents[1]->id,
            'payment_method_id' => $payment_methods[2]->id, // Cheque
            'payment_reference_no' => 'CHEQUE-3333',
            'amount' => 150,
            'bank_id' => $maybank->id,
            'status' => PaymentRequest::STATUS_APPROVED,
        ],
        [
            'billing_document_id' => $billing_documents[1]->id,
            'payment_method_id' => $payment_methods[3]->id, // Bank Transfer
            'payment_reference_no' => 'BANK-4444',
            'amount' => 150,
            'bank_id' => $maybank->id,
            'status' => PaymentRequest::STATUS_APPROVED,
        ],
    ))->create();

    $payments = Payment::factory(4)->state(new Sequence(
        [
            'billing_document_id' => $billing_documents[0]->id,
            'payment_method_id' => $payment_methods[0]->id, // Cash
            'payment_reference_no' => 'CASH-1111',
            'amount_received' => 100,
            'paid_at' => now()->toDateTimeString(),
            'payment_source_type' => get_class($payment_requests[0]),
            'payment_source_id' => $payment_requests[0]->id,
        ],
        [
            'billing_document_id' => $billing_documents[0]->id,
            'payment_method_id' => $payment_methods[1]->id, // FPX
            'payment_reference_no' => 'FPX-2222',
            'amount_received' => 200,
            'paid_at' => now()->toDateTimeString(),
            'payment_source_type' => get_class($payment_requests[1]),
            'payment_source_id' => $payment_requests[1]->id,
        ],
        [
            'billing_document_id' => $billing_documents[1]->id,
            'payment_method_id' => $payment_methods[2]->id, // Cheque
            'payment_reference_no' => 'CHEQUE-3333',
            'amount_received' => 150,
            'paid_at' => now()->toDateTimeString(),
            'payment_source_type' => get_class($payment_requests[2]),
            'payment_source_id' => $payment_requests[2]->id,
        ],
        [
            'billing_document_id' => $billing_documents[1]->id,
            'payment_method_id' => $payment_methods[3]->id, // Bank Transfer
            'payment_reference_no' => 'BANK-4444',
            'amount_received' => 150,
            'paid_at' => now()->toDateTimeString(),
            'payment_source_type' => get_class($payment_requests[3]),
            'payment_source_id' => $payment_requests[3]->id,
        ],
    ))->create();

    CurrentStudentClassAndGrade::refreshViewTable(false);

    /**
     * download start
     */
    $this->partialMock(ReportPrintService::class, function (MockInterface $mock) {
        $mock->shouldReceive('upload')->twice()->andReturnSelf();
        $mock->shouldReceive('getFileUrl')->twice()->andReturn('url');
    });

    $billing_document_report_service = resolve(BillingDocumentReportService::class);

    $file_name = 'billing-document-report-by-daily-collection';

    $expected_headers = [
        __('general.no'),
        __('general.payment_date'),
        __('general.invoice_date'),
        __('general.invoice_no'),
        __('general.bill_to_name'),
        __('general.bill_to_reference_no'),
        __('general.class'),
        'School Fees (Jan 2024)',
        'Other Fees (Mar 2024)',
        'School Fees (Apr 2024)',
        'Advance',
        __('general.total_amount'),
        __('general.bank_charges'),
        __('general.payment'),
        __('general.reference_no'),
        __('general.bank'),
    ];

    // Test Excel
    Excel::fake();

    $view_name = 'reports.billing-documents.by-daily-collection';

    // not filtering by product
    $billing_document_report_service
        ->setExportType(ExportType::EXCEL->value)
        ->setReportViewName($view_name)
        ->setFileName($file_name)
        ->getDailyCollectionReportData([
            'payment_date_from' => '2024-01-01',
            'payment_date_to' => '2024-12-31',
        ]);

    Excel::assertStored(
        DocumentPrintService::PRINT_FOLDER . DIRECTORY_SEPARATOR . $file_name . '.xlsx',
        'local',
        function (BillingDocumentsByDailyCollectionExport $export) use (
            $expected_headers,
            $billing_documents,
            $maybank,
            $students,
        ) {
            expect($export->headings())->toBe($expected_headers);

            $collection = $export->collection();

            // 2 for Albert's invoice + 2 for Bobby's invoice + 1 summary row)
            expect($collection)->toHaveCount(5);

            // Albert's first payment row (row 0)
            $albert_row1 = $collection->get(0);
            expect($albert_row1['no'])->toBe(1);
            expect($albert_row1['payment_date'])->toBe(Carbon::parse($billing_documents[0]->paid_at)->tz(config('school.timezone'))->format('Y-m-d'));
            expect($albert_row1['invoice_date'])->toBe($billing_documents[0]->document_date);
            expect($albert_row1['invoice_no'])->toBe($billing_documents[0]->reference_no);
            expect($albert_row1['bill_to_name'])->toBe('Albert 阿尔'); // both names
            expect($albert_row1['bill_to_reference_no'])->toBe($students[0]->getBillToReferenceNumber());
            expect($albert_row1['class'])->toBe('J111');
            expect($albert_row1['School Fees (Jan 2024)'])->toBe('340.00'); // 400 - 60 discount
            expect($albert_row1['Other Fees (Mar 2024)'])->toBe('');
            expect($albert_row1['School Fees (Apr 2024)'])->toBe('');
            expect($albert_row1['Advance'])->toBe('-40.00');
            expect($albert_row1['total_amount'])->toBe('300.00');
            expect($albert_row1['bank_charges'])->toBe('0.85'); // FPX bank charges from Albert's invoice
            expect($albert_row1['payment'])->toBe('CASH');
            expect($albert_row1['reference_no'])->toBe('CASH-1111');
            expect($albert_row1['bank'])->toBe('');

            // Albert's second payment row (row 1)
            $albert_row2 = $collection->get(1);
            expect($albert_row2['no'])->toBe(''); // empty because of merged cell
            expect($albert_row2['payment_date'])->toBe('');
            expect($albert_row2['invoice_date'])->toBe('');
            expect($albert_row2['invoice_no'])->toBe('');
            expect($albert_row2['bill_to_name'])->toBe('');
            expect($albert_row2['bill_to_reference_no'])->toBe('');
            expect($albert_row2['class'])->toBe('');
            expect($albert_row2['School Fees (Jan 2024)'])->toBe('');
            expect($albert_row2['Other Fees (Mar 2024)'])->toBe('');
            expect($albert_row2['School Fees (Apr 2024)'])->toBe('');
            expect($albert_row2['Advance'])->toBe('');
            expect($albert_row2['total_amount'])->toBe('');
            expect($albert_row2['bank_charges'])->toBe('');
            expect($albert_row2['payment'])->toBe('FPX');
            expect($albert_row2['reference_no'])->toBe('FPX-2222');
            expect($albert_row2['bank'])->toBe('');

            // Bobby's first payment row (row 2)
            $bobby_row1 = $collection->get(2);
            expect($bobby_row1['no'])->toBe(2);
            expect($bobby_row1['payment_date'])->toBe(Carbon::parse($billing_documents[1]->paid_at)->tz(config('school.timezone'))->format('Y-m-d'));
            expect($bobby_row1['invoice_date'])->toBe($billing_documents[1]->document_date);
            expect($bobby_row1['invoice_no'])->toBe($billing_documents[1]->reference_no);
            expect($bobby_row1['bill_to_name'])->toBe('Bobby 鲍比'); // both names
            expect($bobby_row1['bill_to_reference_no'])->toBe($students[1]->getBillToReferenceNumber());
            expect($bobby_row1['class'])->toBe('J111');
            expect($bobby_row1['School Fees (Jan 2024)'])->toBe('');
            expect($bobby_row1['Other Fees (Mar 2024)'])->toBe('50.00'); // 100 - 50 discount
            expect($bobby_row1['School Fees (Apr 2024)'])->toBe('300.00'); // 350 - 50 discount
            expect($bobby_row1['Advance'])->toBe('-50.00');
            expect($bobby_row1['total_amount'])->toBe('300.00');
            expect($bobby_row1['bank_charges'])->toBe('0');
            expect($bobby_row1['payment'])->toBe('CHEQUE');
            expect($bobby_row1['reference_no'])->toBe('CHEQUE-3333');
            expect($bobby_row1['bank'])->toBe($maybank->name . ' (' . $maybank->swift_code . ')');

            // Bobby's second payment row (row 3)
            $bobby_row2 = $collection->get(3);
            expect($bobby_row2['no'])->toBe(''); // empty because of merged cell
            expect($bobby_row2['payment_date'])->toBe('');
            expect($bobby_row2['invoice_date'])->toBe('');
            expect($bobby_row2['invoice_no'])->toBe('');
            expect($bobby_row2['bill_to_name'])->toBe('');
            expect($bobby_row2['bill_to_reference_no'])->toBe('');
            expect($bobby_row2['class'])->toBe('');
            expect($bobby_row2['School Fees (Jan 2024)'])->toBe('');
            expect($bobby_row2['Other Fees (Mar 2024)'])->toBe('');
            expect($bobby_row2['School Fees (Apr 2024)'])->toBe('');
            expect($bobby_row2['Advance'])->toBe('');
            expect($bobby_row2['total_amount'])->toBe('');
            expect($bobby_row2['bank_charges'])->toBe('');
            expect($bobby_row2['payment'])->toBe('BANK');
            expect($bobby_row2['reference_no'])->toBe('BANK-4444');
            expect($bobby_row2['bank'])->toBe($maybank->name . ' (' . $maybank->swift_code . ')');

            // summary row (row 4)
            $summary_row = $collection->get(4);

            expect($summary_row['no'])->toBe('');
            expect($summary_row['payment_date'])->toBe('');
            expect($summary_row['invoice_date'])->toBe('');
            expect($summary_row['invoice_no'])->toBe('');
            expect($summary_row['bill_to_name'])->toBe('');
            expect($summary_row['bill_to_reference_no'])->toBe('');
            expect($summary_row['class'])->toBe(__('general.total'));
            expect($summary_row['School Fees (Jan 2024)'])->toBe('340.00');
            expect($summary_row['Other Fees (Mar 2024)'])->toBe('50.00');
            expect($summary_row['School Fees (Apr 2024)'])->toBe('300.00');
            expect($summary_row['Advance'])->toBe('-90.00'); // -40 + -50
            expect($summary_row['total_amount'])->toBe('600.00'); // 340 + 50 + 300 - 90
            expect($summary_row['bank_charges'])->toBe('0.85');

            // payment dates
            expect($export->paymentDateFrom)->toBe('2024-01-01');
            expect($export->paymentDateTo)->toBe('2024-12-31');

            // payment methods summary
            $expected_payment_methods = [
                'CASH' => '100.00',
                'FPX' => '200.00',
                'CHEQUE' => '150.00',
                'BANK' => '150.00'
            ];
            expect($export->paymentMethods)->toBe($expected_payment_methods);

            // products summary
            $expected_products = [
                'School Fees (Jan 2024)' => '340.00',
                'Other Fees (Mar 2024)' => '50.00',
                'School Fees (Apr 2024)' => '300.00',
                'Advance' => '-90.00'
            ];
            expect($export->products)->toBe($expected_products);

            // totals
            expect($export->totalForProducts)->toBe('600.00');
            expect($export->summaryAmountForBankCharges)->toBe('0.85');

            return true;
        }
    );


    /**
     * only filter billing_document with the $product->id
     */

    $billing_document_report_service
        ->setExportType(ExportType::EXCEL->value)
        ->setReportViewName($view_name)
        ->setFileName($file_name)
        ->getDailyCollectionReportData([
            'payment_date_from' => '2024-01-01',
            'payment_date_to' => '2024-12-31',
            'product_ids' => [$product->id], // include only this product
        ]);

    $expected_headers = [
        __('general.no'),
        __('general.payment_date'),
        __('general.invoice_date'),
        __('general.invoice_no'),
        __('general.bill_to_name'),
        __('general.bill_to_reference_no'),
        __('general.class'),
        'School Fees (Jan 2024)', // only this product
        'School Fees (Apr 2024)', // only this product
        __('general.total_amount'),
        __('general.bank_charges'),
        __('general.payment'),
        __('general.reference_no'),
        __('general.bank'),
    ];

    Excel::assertStored(
        DocumentPrintService::PRINT_FOLDER . DIRECTORY_SEPARATOR . $file_name . '.xlsx',
        'local',
        function (BillingDocumentsByDailyCollectionExport $export) use (
            $expected_headers,
        ) {
            // exclude "Other Fees (Mar 2024)" and "Advance")
            expect($export->headings())->toBe($expected_headers);

            $collection = $export->collection();

            expect($collection)->toHaveCount(5);

            // Albert's first payment row
            $albert_row1 = $collection->get(0);
            expect($albert_row1['no'])->toBe(1);
            expect($albert_row1['School Fees (Jan 2024)'])->toBe('340.00'); // 400 - 60 discount
            expect($albert_row1['School Fees (Apr 2024)'])->toBe('');
            expect($albert_row1['total_amount'])->toBe('340.00');
            expect($albert_row1['payment'])->toBe('CASH');

            // Albert's second payment row
            $albert_row2 = $collection->get(1);
            expect($albert_row2['payment'])->toBe('FPX');
            expect($albert_row2['School Fees (Jan 2024)'])->toBe('');
            expect($albert_row2['School Fees (Apr 2024)'])->toBe('');

            // Bobby's first payment row
            $bobby_row1 = $collection->get(2);
            expect($bobby_row1['no'])->toBe(2);
            expect($bobby_row1['School Fees (Jan 2024)'])->toBe('');
            expect($bobby_row1['School Fees (Apr 2024)'])->toBe('300.00'); // 350 - 50 discount
            expect($bobby_row1['total_amount'])->toBe('300.00');
            expect($bobby_row1['payment'])->toBe('CHEQUE');

            // Bobby's second payment row
            $bobby_row2 = $collection->get(3);
            expect($bobby_row2['payment'])->toBe('BANK');
            expect($bobby_row2['School Fees (Jan 2024)'])->toBe('');
            expect($bobby_row2['School Fees (Apr 2024)'])->toBe('');

            // summary row (should only $product)
            $summary_row = $collection->get(4);
            expect($summary_row['class'])->toBe(__('general.total'));
            expect($summary_row['School Fees (Jan 2024)'])->toBe('340.00');
            expect($summary_row['School Fees (Apr 2024)'])->toBe('300.00');
            expect($summary_row['total_amount'])->toBe('640.00'); // 340 + 300
            expect($summary_row['bank_charges'])->toBe('0.85');

            // products summary
            $expected_products = [
                'School Fees (Jan 2024)' => '340.00',
                'School Fees (Apr 2024)' => '300.00'
            ];
            expect($export->products)->toBe($expected_products);

            // totals
            expect($export->totalForProducts)->toBe('640.00');
            expect($export->summaryAmountForBankCharges)->toBe('0.85');

            return true;
        }
    );
});

test('mapForDailyCollectionReport', function () {
    $grade = Grade::factory()->create([
        'name->en' => 'Junior 1',
    ]);

    $class = ClassModel::factory()->create(['name->en' => 'J111', 'grade_id' => $grade->id]);

    $semester_setting1 = SemesterSetting::factory()->create([
        'name' => '2025 Semester 1',
        'is_current_semester' => true,
    ]);

    $semester_class1 = SemesterClass::factory()->create([
        'semester_setting_id' => $semester_setting1->id,
        'class_id' => $class->id,
    ]);

    $students = Student::factory(2)->state(new Sequence(
        [
            'name->en' => 'Albert',
            'name->zh' => '阿尔',
        ],
        [
            'name->en' => 'Bobby',
            'name->zh' => '鲍比',
        ],
    ))->create();

    foreach ($students as $student) {
        StudentClass::factory()->create([
            'semester_setting_id' => $semester_setting1->id,
            'semester_class_id' => $semester_class1->id,
            'student_id' => $student->id,
            'class_type' => ClassType::PRIMARY
        ]);
    }

    $product = Product::factory()->create([
        'name->en' => 'School Fees',
        'code' => '**********',
        'gl_account_code' => GlAccount::CODE_SCHOOL_FEES,
    ]);

    $unrelated_product = Product::factory()->create([
        'name->en' => 'Other Fees',
        'code' => '**********',
        'gl_account_code' => GlAccount::CODE_OTHERS,
    ]);

    $albert_discount = DiscountSetting::factory()->create([
        'basis' => DiscountSetting::BASIS_FIXED_AMOUNT,
        'basis_amount' => 40,
        'max_amount' => null,
        'used_amount' => 0,
        'effective_from' => '2024-01-01',
        'effective_to' => '2024-12-31',
        'gl_account_codes' => json_encode([GlAccount::CODE_SCHOOL_FEES]),
        'source_type' => null,
        'source_id' => null,
        'userable_type' => Student::class,
        'userable_id' => $students[0]->id,
        'is_active' => true,
    ]);

    $bobby_discount = DiscountSetting::factory()->create([
        'basis' => DiscountSetting::BASIS_FIXED_AMOUNT,
        'basis_amount' => 50,
        'max_amount' => null,
        'used_amount' => 0,
        'effective_from' => '2024-01-01',
        'effective_to' => '2024-12-31',
        'gl_account_codes' => json_encode([GlAccount::CODE_SCHOOL_FEES]),
        'source_type' => null,
        'source_id' => null,
        'userable_type' => Student::class,
        'userable_id' => $students[1]->id,
        'is_active' => true,
    ]);

    $billing_documents = BillingDocument::factory(2)->state(new Sequence(
        [
            'status' => BillingDocument::STATUS_CONFIRMED,
            'payment_status' => BillingDocument::PAYMENT_STATUS_PAID,
            'type' => BillingDocument::TYPE_INVOICE,
            'sub_type' => BillingDocument::SUB_TYPE_FEES,
            'document_date' => '2024-12-01',
            'amount_after_tax' => 300, // 400 (line_item_1) - 40 (discount_line_item_1) - 60 (advance) = 300
            'bill_to_type' => $students[0]->getBillToType(),
            'bill_to_id' => $students[0]->getBillToId(),
            'bill_to_name' => $students[0]->getBillToName(),
            'bill_to_reference_number' => $students[0]->getBillToReferenceNumber(),
            'paid_at' => '2024-12-01 12:40:00',
        ],
        [
            'status' => BillingDocument::STATUS_CONFIRMED,
            'payment_status' => BillingDocument::PAYMENT_STATUS_PAID,
            'type' => BillingDocument::TYPE_INVOICE,
            'sub_type' => BillingDocument::SUB_TYPE_FEES,
            'document_date' => '2024-09-01',
            'amount_after_tax' => 300, // 100 (line_item_1) + 350 (line_item_2) - 50 (discount_line_item_1) - 50 (discount_line_item_2) - 50 (advance)= 300
            'bill_to_type' => $students[1]->getBillToType(),
            'bill_to_id' => $students[1]->getBillToId(),
            'bill_to_name' => $students[1]->getBillToName(),
            'bill_to_reference_number' => $students[1]->getBillToReferenceNumber(),
            'paid_at' => '2024-09-01 12:40:00',
        ],
    ))->create();

    $unpaid_items = UnpaidItem::factory(3)->state(new Sequence(
        [
            'status' => UnpaidItem::STATUS_PAID,
            'billing_document_id' => $billing_documents[0]->id,
            'description' => 'Albert School Fees Jan 2024',
            'period' => '2024-01-01',
        ],
        [
            'status' => UnpaidItem::STATUS_PAID,
            'billing_document_id' => $billing_documents[1]->id,
            'description' => 'Bobby School Fees Mar 2024',
            'period' => '2024-03-01',
        ],
        [
            'status' => UnpaidItem::STATUS_PAID,
            'billing_document_id' => $billing_documents[1]->id,
            'description' => 'Bobby School Fees April 2024',
            'period' => '2024-04-01',
        ],
    ))->create();

    $line_items = BillingDocumentLineItem::factory(8)->state(new Sequence(
    // $billing_documents[0]
        [
            'billing_document_id' => $billing_documents[0]->id,
            'description' => 'Albert School Fees Jan 2024 $product',
            'billable_item_id' => $unpaid_items[0]->id,
            'billable_item_type' => get_class($unpaid_items[0]),
            'amount_before_tax' => 400,
            'product_id' => $product->id,
            'gl_account_code' => $product->gl_account_code,
        ],
        [
            'billing_document_id' => $billing_documents[0]->id,
            'product_id' => $product->id,
            'gl_account_code' => $product->gl_account_code,
            'billable_item_type' => null,
            'billable_item_id' => null,
            'is_discount' => true,
            'discount_id' => $albert_discount->id,
            'discount_original_line_item_id' => null, // to be updated next
            'description' => 'Discount for Albert School Fees Jan 2024 $product',
            'amount_before_tax' => -60,
        ],
        [
            'billing_document_id' => $billing_documents[0]->id,
            'amount_before_tax' => -40,
            'gl_account_code' => GlAccount::CODE_SCHOOL_FEES,
            'offset_billing_document_id' => $billing_documents[0]->id,
            'description' => 'Less advance payment for Albert SCH0001.',
            'product_id' => null,
            'billable_item_type' => null,
            'billable_item_id' => null,
            'is_discount' => false,
            'discount_id' => null,
            'discount_original_line_item_id' => null,
        ],
        // $billing_documents[1]
        [
            'billing_document_id' => $billing_documents[1]->id,
            'description' => 'Bobby School Fees Mar 2024 $unrelated_product',
            'billable_item_id' => $unpaid_items[1]->id,
            'billable_item_type' => get_class($unpaid_items[1]),
            'amount_before_tax' => 100,
            'product_id' => $unrelated_product->id,
            'gl_account_code' => $unrelated_product->gl_account_code,
        ],
        [
            'billing_document_id' => $billing_documents[1]->id,
            'description' => 'Bobby School Fees April 2024 $product',
            'billable_item_id' => $unpaid_items[2]->id,
            'billable_item_type' => get_class($unpaid_items[2]),
            'amount_before_tax' => 350,
            'product_id' => $product->id,
            'gl_account_code' => $product->gl_account_code,
        ],
        [
            'billing_document_id' => $billing_documents[1]->id,
            'product_id' => $product->id,
            'gl_account_code' => $product->gl_account_code,
            'billable_item_type' => null,
            'billable_item_id' => null,
            'is_discount' => true,
            'discount_id' => $bobby_discount->id,
            'discount_original_line_item_id' => null, // to be updated next
            'description' => 'Discount for Bobby School Fees Mar 2024 $unrelated_product',
            'amount_before_tax' => -50,
        ],
        [
            'billing_document_id' => $billing_documents[1]->id,
            'product_id' => $product->id,
            'gl_account_code' => $product->gl_account_code,
            'billable_item_type' => null,
            'billable_item_id' => null,
            'is_discount' => true,
            'discount_id' => $bobby_discount->id,
            'discount_original_line_item_id' => null, // to be updated next
            'description' => 'Discount for Bobby School Fees April 2024 $product',
            'amount_before_tax' => -50,
        ],
        [
            'billing_document_id' => $billing_documents[1]->id,
            'amount_before_tax' => -50,
            'gl_account_code' => GlAccount::CODE_SCHOOL_FEES,
            'offset_billing_document_id' => $billing_documents[1]->id,
            'description' => "Less advance payment for Bobby SCH0002.",
            'product_id' => null,
            'billable_item_type' => null,
            'billable_item_id' => null,
            'is_discount' => false,
            'discount_id' => null,
            'discount_original_line_item_id' => null,
        ],
    ))->create();

    // update discount line item with the original line item id
    $line_items[1]->update([
        'discount_original_line_item_id' => $line_items[0]->id,
    ]);
    $line_items[5]->update([
        'discount_original_line_item_id' => $line_items[3]->id,
    ]);
    $line_items[6]->update([
        'discount_original_line_item_id' => $line_items[4]->id,
    ]);

    $payment_methods = PaymentMethod::factory(4)->state(new Sequence(
        [
            'code' => PaymentMethod::CODE_CASH,
            'name' => 'Cash',
        ],
        [
            'code' => PaymentMethod::CODE_FPX,
            'name' => 'FPX',
        ],
        [
            'code' => PaymentMethod::CODE_CHEQUE,
            'name' => 'Cheque',
        ],
        [
            'code' => PaymentMethod::CODE_BANK_TRANSFER,
            'name' => 'Bank Transfer',
        ]
    ))->create();

    $maybank = Bank::factory()->create([
        'name' => 'Maybank',
        'code' => 'MBB',
    ]);

    $payment_requests = PaymentRequest::factory(4)->state(new Sequence(
        [
            'billing_document_id' => $billing_documents[0]->id,
            'payment_method_id' => $payment_methods[0]->id, // Cash
            'payment_reference_no' => 'CASH-1111',
            'amount' => 100,
            'bank_id' => null,
            'status' => PaymentRequest::STATUS_APPROVED,
        ],
        [
            'billing_document_id' => $billing_documents[0]->id,
            'payment_method_id' => $payment_methods[1]->id, // FPX
            'payment_reference_no' => 'FPX-2222',
            'amount' => 200,
            'bank_id' => null,
            'status' => PaymentRequest::STATUS_APPROVED,
        ],
        [
            'billing_document_id' => $billing_documents[1]->id,
            'payment_method_id' => $payment_methods[2]->id, // Cheque
            'payment_reference_no' => 'CHEQUE-3333',
            'amount' => 150,
            'bank_id' => $maybank->id,
            'status' => PaymentRequest::STATUS_APPROVED,
        ],
        [
            'billing_document_id' => $billing_documents[1]->id,
            'payment_method_id' => $payment_methods[3]->id, // Bank Transfer
            'payment_reference_no' => 'BANK-4444',
            'amount' => 150,
            'bank_id' => $maybank->id,
            'status' => PaymentRequest::STATUS_APPROVED,
        ],
    ))->create();

    $payments = Payment::factory(4)->state(new Sequence(
        [
            'billing_document_id' => $billing_documents[0]->id,
            'payment_method_id' => $payment_methods[0]->id, // Cash
            'payment_reference_no' => 'CASH-1111',
            'amount_received' => 100,
            'paid_at' => now()->toDateTimeString(),
            'payment_source_type' => get_class($payment_requests[0]),
            'payment_source_id' => $payment_requests[0]->id,
        ],
        [
            'billing_document_id' => $billing_documents[0]->id,
            'payment_method_id' => $payment_methods[1]->id, // FPX
            'payment_reference_no' => 'FPX-2222',
            'amount_received' => 200,
            'paid_at' => now()->toDateTimeString(),
            'payment_source_type' => get_class($payment_requests[1]),
            'payment_source_id' => $payment_requests[1]->id,
        ],
        [
            'billing_document_id' => $billing_documents[1]->id,
            'payment_method_id' => $payment_methods[2]->id, // Cheque
            'payment_reference_no' => 'CHEQUE-3333',
            'amount_received' => 150,
            'paid_at' => now()->toDateTimeString(),
            'payment_source_type' => get_class($payment_requests[2]),
            'payment_source_id' => $payment_requests[2]->id,
        ],
        [
            'billing_document_id' => $billing_documents[1]->id,
            'payment_method_id' => $payment_methods[3]->id, // Bank Transfer
            'payment_reference_no' => 'BANK-4444',
            'amount_received' => 150,
            'paid_at' => now()->toDateTimeString(),
            'payment_source_type' => get_class($payment_requests[3]),
            'payment_source_id' => $payment_requests[3]->id,
        ],
    ))->create();

    CurrentStudentClassAndGrade::refreshViewTable(false);


    // getPaidInvoiceReportData is already tested in the BillingDocumentRepositoryTest
    $mocked_data = (new BillingDocumentRepository)->getPaidInvoiceReportData([
        'payment_date_from' => '2024-01-01',
        'payment_date_to' => '2024-12-31',
        'product_ids' => [],
    ]);


    $billing_document_report_service = resolve(BillingDocumentReportService::class);

    /**
     *
     * map data without filtering by product_id
     *
     */

    $result = $billing_document_report_service->mapForDailyCollectionReport($mocked_data, []);

    expect($result)->toEqual([
        'data' => [
            [
                'payment_date' => Carbon::parse($billing_documents[0]->paid_at)->tz(config('school.timezone'))->format('Y-m-d'),
                'document_date' => $billing_documents[0]->document_date,
                'reference_no' => $billing_documents[0]->reference_no,
                'bill_to_name' => 'Albert 阿尔', // both names
                'bill_to_reference_number' => $billing_documents[0]->bill_to_reference_number,
                'class_name' => 'J111',
                'grade_name' => 'Junior 1',
                'products' => [
                    'School Fees (Jan 2024)' => '340.00', // 400 - 40
                    'Advance' => '-40.00', // advance payment for this invoice
                ],
                'total_amount' => '300.00',
                'payments' => [
                    [
                        'payment_method' => 'CASH',
                        'payment_reference_no' => 'CASH-1111',
                        'bank' => null,
                        'bank_swift_code' => null,
                    ],
                    [
                        'payment_method' => 'FPX',
                        'payment_reference_no' => 'FPX-2222',
                        'bank' => null,
                        'bank_swift_code' => null,
                    ],
                ],
                'bank_charges' => 0.85,
            ],
            [
                'payment_date' => Carbon::parse($billing_documents[1]->paid_at)->tz(config('school.timezone'))->format('Y-m-d'),
                'document_date' => $billing_documents[1]->document_date,
                'reference_no' => $billing_documents[1]->reference_no,
                'bill_to_name' => 'Bobby 鲍比', // both names
                'bill_to_reference_number' => $billing_documents[1]->bill_to_reference_number,
                'class_name' => 'J111',
                'grade_name' => 'Junior 1',
                'products' => [
                    'Other Fees (Mar 2024)' => '50.00', // 100 - 50
                    'School Fees (Apr 2024)' => '300.00', // 350 - 50
                    'Advance' => '-50.00', // advance payment for this invoice
                ],
                'total_amount' => '300.00',
                'payments' => [
                    [
                        'payment_method' => 'CHEQUE',
                        'payment_reference_no' => 'CHEQUE-3333',
                        'bank' => $maybank->name,
                        'bank_swift_code' => $maybank->swift_code,
                    ],
                    [
                        'payment_method' => 'BANK',
                        'payment_reference_no' => 'BANK-4444',
                        'bank' => $maybank->name,
                        'bank_swift_code' => $maybank->swift_code,
                    ],
                ],
                'bank_charges' => 0, // not fpx so no bank charges
            ]
        ],
        'products' => [
            'School Fees (Jan 2024)' => '340.00', // 400 - 40
            'Other Fees (Mar 2024)' => '50.00', // 100 - 50
            'School Fees (Apr 2024)' => '300.00', // 350 - 50
            'Advance' => '-90.00', // total advance for 2 invoices
        ],
        'payment_methods' => [
            'CASH' => '100.00',
            'FPX' => '200.00',
            'CHEQUE' => '150.00',
            'BANK' => '150.00',
        ],
        'total_for_products' => '600.00', // 340 + 50 + 300 - 90
        'summary_amount_for_products' => '600.00', // 340 + 50 + 300 - 90
        'summary_amount_for_bank_charges' => 0.85, // 0.85
    ]);


    /**
     *
     * map data WITH filtering by product_id
     * expect only this product to be included in the report, no advance payment
     *
     */

    $mocked_data = (new BillingDocumentRepository)->getPaidInvoiceReportData([
        'payment_date_from' => '2024-01-01',
        'payment_date_to' => '2024-12-31',
        'product_ids' => [$product->id], // include only this product
    ]);

    $result = $billing_document_report_service->mapForDailyCollectionReport($mocked_data, [$product->id]);

    expect($result)->toEqual([
        'data' => [
            [
                'payment_date' => Carbon::parse($billing_documents[0]->paid_at)->tz(config('school.timezone'))->format('Y-m-d'),
                'document_date' => $billing_documents[0]->document_date,
                'reference_no' => $billing_documents[0]->reference_no,
                'bill_to_name' => 'Albert 阿尔', // both names
                'bill_to_reference_number' => $billing_documents[0]->bill_to_reference_number,
                'class_name' => 'J111',
                'grade_name' => 'Junior 1',
                'products' => [
                    'School Fees (Jan 2024)' => '340.00', // 400 - 60 (Discount)
                ],
                'total_amount' => '340.00',
                'payments' => [
                    [
                        'payment_method' => 'CASH',
                        'payment_reference_no' => 'CASH-1111',
                        'bank' => null,
                        'bank_swift_code' => null,
                    ],
                    [
                        'payment_method' => 'FPX',
                        'payment_reference_no' => 'FPX-2222',
                        'bank' => null,
                        'bank_swift_code' => null,
                    ],
                ],
                'bank_charges' => 0.85,
            ],
            [
                'payment_date' => Carbon::parse($billing_documents[1]->paid_at)->tz(config('school.timezone'))->format('Y-m-d'),
                'document_date' => $billing_documents[1]->document_date,
                'reference_no' => $billing_documents[1]->reference_no,
                'bill_to_name' => 'Bobby 鲍比', // both names
                'bill_to_reference_number' => $billing_documents[1]->bill_to_reference_number,
                'class_name' => 'J111',
                'grade_name' => 'Junior 1',
                'products' => [
                    'School Fees (Apr 2024)' => '300.00', // 350 - 50
                ],
                'total_amount' => '300.00',
                'payments' => [
                    [
                        'payment_method' => 'CHEQUE',
                        'payment_reference_no' => 'CHEQUE-3333',
                        'bank' => $maybank->name,
                        'bank_swift_code' => $maybank->swift_code,
                    ],
                    [
                        'payment_method' => 'BANK',
                        'payment_reference_no' => 'BANK-4444',
                        'bank' => $maybank->name,
                        'bank_swift_code' => $maybank->swift_code,
                    ],
                ],
                'bank_charges' => 0, // not fpx so no bank charges
            ]
        ],
        'products' => [
            'School Fees (Jan 2024)' => '340.00', // 400 - 60 (Discount)
            'School Fees (Apr 2024)' => '300.00', // 350 - 50 (Discount)
        ],
        'payment_methods' => [
            'CASH' => '100.00',
            'FPX' => '200.00',
            'CHEQUE' => '150.00',
            'BANK' => '150.00',
        ],
        'total_for_products' => '640.00', // 340 + 300
        'summary_amount_for_products' => '600.00', // summary remains the same
        'summary_amount_for_bank_charges' => 0.85, // summary remains the same
    ]);
});

test('recalculateLineItems', function () {
    $student = Student::factory()->create([
        'name->en' => 'Albert',
        'name->zh' => 'Albert2',
    ]);

    $product = Product::factory()->create([
        'name->en' => 'School Fees',
        'code' => '**********',
        'gl_account_code' => GlAccount::CODE_SCHOOL_FEES,
    ]);

    $unrelated_product = Product::factory()->create([
        'name->en' => 'Other Fees',
        'code' => '**********',
        'gl_account_code' => GlAccount::CODE_OTHERS,
    ]);

    $albert_discount = DiscountSetting::factory()->create([
        'basis' => DiscountSetting::BASIS_FIXED_AMOUNT,
        'basis_amount' => 40,
        'max_amount' => null,
        'used_amount' => 0,
        'effective_from' => '2024-01-01',
        'effective_to' => '2024-12-31',
        'gl_account_codes' => json_encode([GlAccount::CODE_SCHOOL_FEES]),
        'source_type' => null,
        'source_id' => null,
        'userable_type' => Student::class,
        'userable_id' => $student->id,
        'is_active' => true,
    ]);

    $billing_documents = BillingDocument::factory(2)->state(new Sequence(
        [
            'status' => BillingDocument::STATUS_CONFIRMED,
            'payment_status' => BillingDocument::PAYMENT_STATUS_PAID,
            'type' => BillingDocument::TYPE_INVOICE,
            'sub_type' => BillingDocument::SUB_TYPE_FEES,
            'document_date' => '2024-12-01',
            'amount_after_tax' => 300, // 400 (line_item_1) - 40 (discount_line_item_1) - 60 (advance) = 300
            'bill_to_type' => $student->getBillToType(),
            'bill_to_id' => $student->getBillToId(),
            'bill_to_name' => $student->getBillToName(),
            'bill_to_reference_number' => $student->getBillToReferenceNumber(),
            'paid_at' => '2024-12-01 12:40:00',
        ],
        [
            'status' => BillingDocument::STATUS_CONFIRMED,
            'payment_status' => BillingDocument::PAYMENT_STATUS_PAID,
            'type' => BillingDocument::TYPE_INVOICE,
            'sub_type' => BillingDocument::SUB_TYPE_FEES,
            'document_date' => '2024-09-01',
            'amount_after_tax' => 300, // 100 (line_item_1) + 350 (line_item_2) - 50 (discount_line_item_1) - 50 (discount_line_item_2) - 50 (advance)= 300
            'bill_to_type' => $student->getBillToType(),
            'bill_to_id' => $student->getBillToId(),
            'bill_to_name' => $student->getBillToName(),
            'bill_to_reference_number' => $student->getBillToReferenceNumber(),
            'paid_at' => '2024-09-01 12:40:00',
        ],
    ))->create();

    $unpaid_items = UnpaidItem::factory(3)->state(new Sequence(
        [
            'status' => UnpaidItem::STATUS_PAID,
            'billing_document_id' => $billing_documents[0]->id,
            'description' => 'Albert School Fees Jan 2024',
            'period' => '2024-01-01',
        ],
        [
            'status' => UnpaidItem::STATUS_PAID,
            'billing_document_id' => $billing_documents[1]->id,
            'description' => 'Bobby School Fees Mar 2024',
            'period' => '2024-03-01',
        ],
        [
            'status' => UnpaidItem::STATUS_PAID,
            'billing_document_id' => $billing_documents[1]->id,
            'description' => 'Bobby School Fees April 2024',
            'period' => '2024-04-01',
        ],
    ))->create();

    $line_items = BillingDocumentLineItem::factory(8)->state(new Sequence(
    // $billing_documents[0]
        [
            'billing_document_id' => $billing_documents[0]->id,
            'description' => 'Albert School Fees Jan 2024 $product',
            'billable_item_id' => $unpaid_items[0]->id,
            'billable_item_type' => get_class($unpaid_items[0]),
            'amount_before_tax' => 400,
            'product_id' => $product->id,
            'gl_account_code' => $product->gl_account_code,
        ],
        [
            'billing_document_id' => $billing_documents[0]->id,
            'product_id' => $product->id,
            'gl_account_code' => $product->gl_account_code,
            'billable_item_type' => null,
            'billable_item_id' => null,
            'is_discount' => true,
            'discount_id' => $albert_discount->id,
            'discount_original_line_item_id' => null, // to be updated next
            'description' => 'Discount for Albert School Fees Jan 2024 $product',
            'amount_before_tax' => -60,
        ],
        [
            'billing_document_id' => $billing_documents[0]->id,
            'amount_before_tax' => -40,
            'gl_account_code' => GlAccount::CODE_SCHOOL_FEES,
            'offset_billing_document_id' => $billing_documents[0]->id,
            'description' => 'Less advance payment for Albert SCH0001.',
            'product_id' => null,
            'billable_item_type' => null,
            'billable_item_id' => null,
            'is_discount' => false,
            'discount_id' => null,
            'discount_original_line_item_id' => null,
        ],
        // $billing_documents[1]
        [
            'billing_document_id' => $billing_documents[1]->id,
            'description' => 'Albert School Fees Mar 2024 $unrelated_product',
            'billable_item_id' => $unpaid_items[1]->id,
            'billable_item_type' => get_class($unpaid_items[1]),
            'amount_before_tax' => 100,
            'product_id' => $unrelated_product->id,
            'gl_account_code' => $unrelated_product->gl_account_code,
        ],
        [
            'billing_document_id' => $billing_documents[1]->id,
            'description' => 'Albert School Fees April 2024 $product',
            'billable_item_id' => $unpaid_items[2]->id,
            'billable_item_type' => get_class($unpaid_items[2]),
            'amount_before_tax' => 350,
            'product_id' => $product->id,
            'gl_account_code' => $product->gl_account_code,
        ],
        [
            'billing_document_id' => $billing_documents[1]->id,
            'product_id' => $product->id,
            'gl_account_code' => $product->gl_account_code,
            'billable_item_type' => null,
            'billable_item_id' => null,
            'is_discount' => true,
            'discount_id' => $albert_discount->id,
            'discount_original_line_item_id' => null, // to be updated next
            'description' => 'Discount for Albert School Fees Mar 2024 $unrelated_product',
            'amount_before_tax' => -50,
        ],
        [
            'billing_document_id' => $billing_documents[1]->id,
            'product_id' => $product->id,
            'gl_account_code' => $product->gl_account_code,
            'billable_item_type' => null,
            'billable_item_id' => null,
            'is_discount' => true,
            'discount_id' => $albert_discount->id,
            'discount_original_line_item_id' => null, // to be updated next
            'description' => 'Discount for Albert School Fees April 2024 $product',
            'amount_before_tax' => -50,
        ],
        [
            'billing_document_id' => $billing_documents[1]->id,
            'amount_before_tax' => -50,
            'gl_account_code' => GlAccount::CODE_SCHOOL_FEES,
            'offset_billing_document_id' => $billing_documents[1]->id,
            'description' => "Less advance payment for Albert SCH0002.",
            'product_id' => null,
            'billable_item_type' => null,
            'billable_item_id' => null,
            'is_discount' => false,
            'discount_id' => null,
            'discount_original_line_item_id' => null,
        ],
    ))->create();

    // update discount line item with the original line item id
    $line_items[1]->update([
        'discount_original_line_item_id' => $line_items[0]->id,
    ]);
    $line_items[5]->update([
        'discount_original_line_item_id' => $line_items[3]->id,
    ]);
    $line_items[6]->update([
        'discount_original_line_item_id' => $line_items[4]->id,
    ]);

    $payment_methods = PaymentMethod::factory(2)->state(new Sequence(
        [
            'code' => PaymentMethod::CODE_CASH,
            'name' => 'Cash',
        ],
        [
            'code' => PaymentMethod::CODE_FPX,
            'name' => 'FPX',
        ],
    ))->create();

    $payments = Payment::factory(2)->state(new Sequence(
        [
            'billing_document_id' => $billing_documents[0]->id,
            'payment_method_id' => $payment_methods[0]->id, // Cash
            'payment_reference_no' => $billing_documents[0]->reference_no,
            'amount_received' => 300,
        ],
        [
            'billing_document_id' => $billing_documents[1]->id,
            'payment_method_id' => $payment_methods[1]->id, // FPX
            'payment_reference_no' => 'REF:' . fake()->uuid,
            'amount_received' => 300,
        ],
    ))->create();


    $billing_document_report_service = resolve(BillingDocumentReportService::class);

    /**
     * $billing_documents[0]
     *
     *     *  * line_items[0] -> amount_before_tax = 400  (original)
     *     *  * line_items[1] -> amount_before_tax = -60  (discount)
     *     *  * line_items[2] -> amount_before_tax = -40  (advance)
     *
     *     * expect to get 340 -> 400 - 60 = 340
     *     * expect to get advance line item =  -40
     */

    $result = $billing_document_report_service->recalculateLineItems($billing_documents[0]);

    expect($result)->toHaveCount(2)
        ->and($result[$line_items[0]->id]['billing_document_id'])->toBe($billing_documents[0]->id)
        ->and($result[$line_items[0]->id]['amount_before_tax'])->toBe('340.00')
        ->and($result[$line_items[2]->id]['billing_document_id'])->toBe($billing_documents[0]->id)
        ->and($result[$line_items[2]->id]['amount_before_tax'])->toBe('-40.00');


    /**
     * $billing_documents[1]
     *
     *    *  * line_items[3] -> amount_before_tax = 100  (original)
     *    *  * line_items[4] -> amount_before_tax = 350  (original)
     *    *  * line_items[5] -> amount_before_tax = -50  (discount) for $line_items[3]
     *    *  * line_items[6] -> amount_before_tax = -50  (discount) for $line_items[4]
     *    *  * line_items[7] -> amount_before_tax = -50  (advance)
     *
     *    *  * expect line_items[3] to be 50 -> 100 - 50 = 50
     *    *  * expect line_items[4] to be 300 -> 350 - 50 = 300
     *    *  * expect line_items[7] to be -50 (advance)
     */

    $result = $billing_document_report_service->recalculateLineItems($billing_documents[1]);

    expect($result)->toHaveCount(3)
        ->and($result[$line_items[3]->id]['billing_document_id'])->toBe($billing_documents[1]->id)
        ->and($result[$line_items[3]->id]['amount_before_tax'])->toBe('50.00')
        ->and($result[$line_items[4]->id]['billing_document_id'])->toBe($billing_documents[1]->id)
        ->and($result[$line_items[4]->id]['amount_before_tax'])->toBe('300.00')
        ->and($result[$line_items[7]->id]['billing_document_id'])->toBe($billing_documents[1]->id)
        ->and($result[$line_items[7]->id]['amount_before_tax'])->toBe('-50.00');
});

test('groupLineItemsByProduct', function () {
    /**
     *
     * WITHOUT product ids filter
     *
     */

    // the paylod from this come from the output of the recalculateLineItems
    $mock_payload = [
        '1' => [
            'id' => 1,
            'product_id' => 101,
            'amount_before_tax' => '100.00',
            'billable_item' => ['period' => '2023-01-01'],
            'product' => ['name' => ['en' => 'Math Course']]
        ],
        '2' => [
            'id' => 2,
            'product_id' => 101, // Same product, same month
            'amount_before_tax' => '50.00',
            'billable_item' => ['period' => '2023-01-01'],
            'product' => ['name' => ['en' => 'Math Course']]
        ],
        '3' => [
            'id' => 3,
            'product_id' => 102, // Different product
            'amount_before_tax' => '75.00',
            'billable_item' => ['period' => '2023-01-01'],
            'product' => ['name' => ['en' => 'Science Course']]
        ],
        '4' => [
            'id' => 4,
            'product_id' => 101, // Same product, different month
            'amount_before_tax' => '120.00',
            'billable_item' => ['period' => '2023-02-01'],
            'product' => ['name' => ['en' => 'Math Course']]
        ],
        '5' => [
            'id' => 5,
            'product_id' => null,
            'offset_billing_document_id' => 999, // Advance payment
            'amount_before_tax' => '-25.00'
        ]
    ];

    $filtered_product_ids = [];
    $products = [];

    $billing_document_report_service = resolve(BillingDocumentReportService::class);

    $result = $billing_document_report_service->groupLineItemsByProduct($mock_payload, $filtered_product_ids, $products);

    expect($result)->toEqual([
        'grouped' => [
            'Math Course (Jan 2023)' => '150.00', // 100 + 50
            'Science Course (Jan 2023)' => '75.00', // 75
            'Math Course (Feb 2023)' => '120.00', // 120
            'Advance' => '-25.00', // Advance payment
        ],
        'total_amount_for_products' => '320.00', // 150 + 75 + 120 - 25
    ]);

    expect($products)->toHaveCount(4);


    /**
     *
     * with product ids filter
     * only filter Math
     *
     */

    $filtered_product_ids = [101];
    $products = [];

    $result = $billing_document_report_service->groupLineItemsByProduct($mock_payload, $filtered_product_ids, $products);

    expect($result)->toEqual([
        'grouped' => [
            'Math Course (Jan 2023)' => '150.00', // 100 + 50
            'Math Course (Feb 2023)' => '120.00', // 120
        ],
        'total_amount_for_products' => '270.00', // 150 + 120 = 270
    ]);

    expect($products)->toHaveCount(2);
});

test('mapPayments', function () {
    $billing_documents = BillingDocument::factory(2)->state(new Sequence(
        [
            'status' => BillingDocument::STATUS_CONFIRMED,
            'payment_status' => BillingDocument::PAYMENT_STATUS_PAID,
            'type' => BillingDocument::TYPE_INVOICE,
            'sub_type' => BillingDocument::SUB_TYPE_FEES,
            'document_date' => '2024-12-01',
            'amount_after_tax' => 300, // 400 (line_item_1) - 40 (discount_line_item_1) - 60 (advance) = 300
            'bill_to_type' => Student::class,
            'bill_to_id' => 1,
            'bill_to_name' => 'Albert',
            'bill_to_reference_number' => 'SCH0001',
            'paid_at' => '2024-12-01 12:40:00',
        ],
        [
            'status' => BillingDocument::STATUS_CONFIRMED,
            'payment_status' => BillingDocument::PAYMENT_STATUS_PAID,
            'type' => BillingDocument::TYPE_INVOICE,
            'sub_type' => BillingDocument::SUB_TYPE_FEES,
            'document_date' => '2024-09-01',
            'amount_after_tax' => 300, // 100 (line_item_1) + 350 (line_item_2) - 50 (discount_line_item_1) - 50 (discount_line_item_2) - 50 (advance)= 300
            'bill_to_type' => Student::class,
            'bill_to_id' => 2,
            'bill_to_name' => 'Bobby',
            'bill_to_reference_number' => 'SCH0002',
            'paid_at' => '2024-09-01 12:40:00',
        ],
    ))->create();

    $payment_methods = PaymentMethod::factory(4)->state(new Sequence(
        [
            'code' => PaymentMethod::CODE_CASH,
            'name' => 'Cash',
        ],
        [
            'code' => PaymentMethod::CODE_FPX,
            'name' => 'FPX',
        ],
        [
            'code' => PaymentMethod::CODE_CHEQUE,
            'name' => 'Cheque',
        ],
        [
            'code' => PaymentMethod::CODE_BANK_TRANSFER,
            'name' => 'Bank Transfer',
        ]
    ))->create();

    $maybank = Bank::factory()->create([
        'name' => 'Maybank',
        'code' => 'MBB',
    ]);

    $payment_requests = PaymentRequest::factory(4)->state(new Sequence(
        [
            'billing_document_id' => $billing_documents[0]->id,
            'payment_method_id' => $payment_methods[0]->id, // Cash
            'payment_reference_no' => 'CASH-1111',
            'amount' => 100,
            'bank_id' => null,
        ],
        [
            'billing_document_id' => $billing_documents[0]->id,
            'payment_method_id' => $payment_methods[1]->id, // FPX
            'payment_reference_no' => 'FPX-2222',
            'amount' => 200,
            'bank_id' => null,
        ],
        [
            'billing_document_id' => $billing_documents[1]->id,
            'payment_method_id' => $payment_methods[2]->id, // Cheque
            'payment_reference_no' => 'CHEQUE-3333',
            'amount' => 150,
            'bank_id' => $maybank->id,
        ],
        [
            'billing_document_id' => $billing_documents[1]->id,
            'payment_method_id' => $payment_methods[3]->id, // Bank Transfer
            'payment_reference_no' => 'BANK-4444',
            'amount' => 150,
            'bank_id' => $maybank->id,
        ],
    ))->create();

    $payments = Payment::factory(4)->state(new Sequence(
        [
            'billing_document_id' => $billing_documents[0]->id,
            'payment_method_id' => $payment_methods[0]->id, // Cash
            'payment_reference_no' => 'CASH-1111',
            'amount_received' => 100,
            'paid_at' => now()->toDateTimeString(),
            'payment_source_type' => get_class($payment_requests[0]),
            'payment_source_id' => $payment_requests[0]->id,
        ],
        [
            'billing_document_id' => $billing_documents[0]->id,
            'payment_method_id' => $payment_methods[1]->id, // FPX
            'payment_reference_no' => 'FPX-2222',
            'amount_received' => 200,
            'paid_at' => now()->toDateTimeString(),
            'payment_source_type' => get_class($payment_requests[1]),
            'payment_source_id' => $payment_requests[1]->id,
        ],
        [
            'billing_document_id' => $billing_documents[1]->id,
            'payment_method_id' => $payment_methods[2]->id, // Cheque
            'payment_reference_no' => 'CHEQUE-3333',
            'amount_received' => 150,
            'paid_at' => now()->toDateTimeString(),
            'payment_source_type' => get_class($payment_requests[2]),
            'payment_source_id' => $payment_requests[2]->id,
        ],
        [
            'billing_document_id' => $billing_documents[1]->id,
            'payment_method_id' => $payment_methods[3]->id, // Bank Transfer
            'payment_reference_no' => 'BANK-4444',
            'amount_received' => 150,
            'paid_at' => now()->toDateTimeString(),
            'payment_source_type' => get_class($payment_requests[3]),
            'payment_source_id' => $payment_requests[3]->id,
        ],
    ))->create();


    $billing_document_report_service = resolve(BillingDocumentReportService::class);


    /**
     *
     * with FPX payments
     *
     */
    $temp_payment_methods = [];
    $is_fpx = false;

    $result = $billing_document_report_service->mapPayments($billing_documents[0], $temp_payment_methods, $is_fpx);

    expect($result)->toEqual([
        [
            'payment_method' => $payment_methods[0]->code,
            'payment_reference_no' => 'CASH-1111',
            'bank' => null,
            'bank_swift_code' => null,
        ],
        [
            'payment_method' => $payment_methods[1]->code,
            'payment_reference_no' => 'FPX-2222',
            'bank' => null,
            'bank_swift_code' => null,
        ]
    ]);

    expect($temp_payment_methods)
        ->toHaveCount(2)
        ->toEqual([
            $payment_methods[0]->code => 100,
            $payment_methods[1]->code => 200,
        ]);

    expect($is_fpx)->toBeTrue();


    /**
     *
     * without FPX payments
     *
     */
    $temp_payment_methods = [];
    $is_fpx = false;

    $result = $billing_document_report_service->mapPayments($billing_documents[1], $temp_payment_methods, $is_fpx);

    expect($result)->toEqual([
        [
            'payment_method' => $payment_methods[2]->code,
            'payment_reference_no' => 'CHEQUE-3333',
            'bank' => $maybank->name,
            'bank_swift_code' => $maybank->swift_code,
        ],
        [
            'payment_method' => $payment_methods[3]->code,
            'payment_reference_no' => 'BANK-4444',
            'bank' => $maybank->name,
            'bank_swift_code' => $maybank->swift_code,
        ]
    ]);

    expect($temp_payment_methods)
        ->toHaveCount(2)
        ->toEqual([
            $payment_methods[2]->code => 150,
            $payment_methods[3]->code => 150,
        ]);

    expect($is_fpx)->toBeFalse();
});
