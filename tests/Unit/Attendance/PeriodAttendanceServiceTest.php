<?php

use App\Enums\ClassType;
use App\Enums\Day;
use App\Enums\LeaveApplicationStatus;
use App\Enums\PeriodAttendanceStatus;
use App\Models\LeaveApplication;
use App\Models\LeaveApplicationPeriod;
use App\Models\Period;
use App\Models\PeriodAttendance;
use App\Models\PeriodGroup;
use App\Models\PeriodLabel;
use App\Models\SemesterClass;
use App\Models\SemesterSetting;
use App\Models\Student;
use App\Models\StudentClass;
use App\Models\StudentTimetable;
use App\Models\Timeslot;
use App\Models\TimeslotTeacher;
use App\Models\Timetable;
use App\Services\PeriodAttendanceService;
use Carbon\Carbon;

beforeEach(function () {
    $this->periodAttendanceService = resolve(PeriodAttendanceService::class);
    app()->setLocale('en');
    $this->table = resolve(PeriodAttendance::class)->getTable();
});

test('updatePeriodAttendanceByLeaveApplications()', function () {
    $student = Student::factory()->create(['is_active' => true]);
    $student2 = Student::factory()->create(['is_active' => true]);

    $semester_setting = SemesterSetting::factory()->create();
    $semester_class1 = SemesterClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
    ]);
    $period_group1 = PeriodGroup::factory()->create();
    $main_j111_timetable = Timetable::factory()->create([
        'name' => 'J111 timetable',
        'is_active' => true,
        'period_group_id' => $period_group1->id,
        'semester_class_id' => $semester_class1,
    ]);

    $period1 = Period::factory()->create([
        'period_group_id' => $period_group1->id,
        'period' => 1,
        'from_time' => '08:00:00',
        'to_time' => '08:30:00',
        'day' => Day::MONDAY,
        'display_group' => 1,
    ]);
    $period_label1 = PeriodLabel::factory()->create([
        'period_group_id' => $period_group1->id,
        'period' => 1,
    ]);
    $period2 = Period::factory()->create([
        'period_group_id' => $period_group1->id,
        'period' => 2,
        'from_time' => '08:30:00',
        'to_time' => '09:00:00',
        'day' => Day::MONDAY,
        'display_group' => 1,
    ]);
    $period_label2 = PeriodLabel::factory()->create([
        'period_group_id' => $period_group1->id,
        'period' => 2,
    ]);

    StudentClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'semester_class_id' => $semester_class1->id,
        'student_id' => $student->id,
        'class_type' => ClassType::PRIMARY
    ]);

    StudentClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'semester_class_id' => $semester_class1->id,
        'student_id' => $student2->id,
        'class_type' => ClassType::PRIMARY
    ]);

    $timeslot1 = Timeslot::factory()->create([
        'timetable_id' => $main_j111_timetable->id,
        'period_id' => $period1->id,
        'placeholder' => null,
        'attendance_from' => '08:00',
        'attendance_to' => '08:30',
    ]);
    $timeslot2 = Timeslot::factory()->create([
        'timetable_id' => $main_j111_timetable->id,
        'period_id' => $period2->id,
        'placeholder' => null,
        'attendance_from' => '08:30',
        'attendance_to' => '09:00',
    ]);

    $timeslot_teacher1 = TimeslotTeacher::factory()->create([
        'timeslot_id' => $timeslot1->id,
    ]);
    $timeslot_teacher2 = TimeslotTeacher::factory()->create([
        'timeslot_id' => $timeslot2->id,
    ]);

    StudentTimetable::refreshViewTable(false);

    // student 1 leave - only period 1
    // student 2 leave - period 1 and 2
    $leave_application_student1 = LeaveApplication::factory()->create([
        'status' => LeaveApplicationStatus::PENDING->value,
        'leave_applicable_type' => Student::class,
        'leave_applicable_id' => $student->id,
        'is_full_day' => false,
    ]);
    LeaveApplicationPeriod::factory()->create(['leave_application_id' => $leave_application_student1->id, 'date' => '2025-02-10', 'period' => $period1->period]);
    PeriodAttendance::factory()->create([
        'student_id' => $student->id,
        'date' => '2025-02-10',
        'timeslot_id' => $timeslot1->id,
        'updated_by_employee_id' => $timeslot_teacher1->employee_id,
        'attendance_taken_at' => '2025-02-10 08:00:00',
        'status' => PeriodAttendanceStatus::PRESENT->value,
        'leave_application_id' => null,
        'period' => $timeslot1->period->period,
    ]);
    PeriodAttendance::factory()->create([
        'student_id' => $student->id,
        'date' => '2025-02-10',
        'timeslot_id' => $timeslot2->id,
        'updated_by_employee_id' => $timeslot_teacher2->employee_id,
        'attendance_taken_at' => '2025-02-10 08:30:00',
        'status' => PeriodAttendanceStatus::PRESENT->value,
        'leave_application_id' => null,
        'period' => $timeslot2->period->period,
    ]);

    $leave_application_student2 = LeaveApplication::factory()->create([
        'status' => LeaveApplicationStatus::PENDING->value,
        'leave_applicable_type' => Student::class,
        'leave_applicable_id' => $student2->id,
        'is_full_day' => false,
    ]);
    LeaveApplicationPeriod::factory()->create(['leave_application_id' => $leave_application_student2->id, 'date' => '2025-02-10', 'period' => $period1->period]);
    PeriodAttendance::factory()->create([
        'student_id' => $student2->id,
        'date' => '2025-02-10',
        'timeslot_id' => $timeslot1->id,
        'updated_by_employee_id' => $timeslot_teacher1->employee_id,
        'attendance_taken_at' => '2025-02-10 08:00:00',
        'status' => PeriodAttendanceStatus::PRESENT->value,
        'leave_application_id' => null,
        'period' => $timeslot1->period->period,
    ]);
    LeaveApplicationPeriod::factory()->create(['leave_application_id' => $leave_application_student2->id, 'date' => '2025-02-10', 'period' => $period2->period]);
    PeriodAttendance::factory()->create([
        'student_id' => $student2->id,
        'date' => '2025-02-10',
        'timeslot_id' => $timeslot2->id,
        'updated_by_employee_id' => $timeslot_teacher2->employee_id,
        'attendance_taken_at' => '2025-02-10 08:30:00',
        'status' => PeriodAttendanceStatus::PRESENT->value,
        'leave_application_id' => null,
        'period' => $timeslot2->period->period,
    ]);

    $this->periodAttendanceService
        ->setLeaveapplications(collect([$leave_application_student1, $leave_application_student2]))
        ->setLeaveApplicationNewStatus(LeaveApplicationStatus::APPROVED->value)
        ->updatePeriodAttendanceByLeaveApplications();

    $this->assertDatabaseCount($this->table, 4);
    $this->assertDatabaseHas($this->table, [
        'student_id' => $student->id,
        'date' => '2025-02-10',
        'timeslot_id' => $timeslot1->id,
        'updated_by_employee_id' => $timeslot_teacher1->employee_id,
        'attendance_taken_at' => '2025-02-10 08:00:00',
        'status' => PeriodAttendanceStatus::PRESENT->value,
        'leave_application_id' => $leave_application_student1->id,
    ]);
    $this->assertDatabaseHas($this->table, [
        'student_id' => $student->id,
        'date' => '2025-02-10',
        'timeslot_id' => $timeslot2->id,
        'updated_by_employee_id' => $timeslot_teacher2->employee_id,
        'attendance_taken_at' => '2025-02-10 08:30:00',
        'status' => PeriodAttendanceStatus::PRESENT->value,
        'leave_application_id' => null,
    ]);
    $this->assertDatabaseHas($this->table, [
        'student_id' => $student2->id,
        'date' => '2025-02-10',
        'timeslot_id' => $timeslot1->id,
        'updated_by_employee_id' => $timeslot_teacher1->employee_id,
        'attendance_taken_at' => '2025-02-10 08:00:00',
        'status' => PeriodAttendanceStatus::PRESENT->value,
        'leave_application_id' => $leave_application_student2->id,
    ]);
    $this->assertDatabaseHas($this->table, [
        'student_id' => $student2->id,
        'date' => '2025-02-10',
        'timeslot_id' => $timeslot2->id,
        'updated_by_employee_id' => $timeslot_teacher2->employee_id,
        'attendance_taken_at' => '2025-02-10 08:30:00',
        'status' => PeriodAttendanceStatus::PRESENT->value,
        'leave_application_id' => $leave_application_student2->id,
    ]);

    $this->periodAttendanceService
        ->setLeaveapplications(collect([$leave_application_student1, $leave_application_student2]))
        ->setLeaveApplicationNewStatus(LeaveApplicationStatus::PENDING->value)
        ->updatePeriodAttendanceByLeaveApplications();

    $this->assertDatabaseCount($this->table, 4);
    $this->assertDatabaseHas($this->table, [
        'student_id' => $student->id,
        'date' => '2025-02-10',
        'timeslot_id' => $timeslot1->id,
        'updated_by_employee_id' => $timeslot_teacher1->employee_id,
        'attendance_taken_at' => '2025-02-10 08:00:00', // Make sure this doesn't change, only the updated_at will be updated
        'status' => PeriodAttendanceStatus::PRESENT->value,
        'leave_application_id' => null,
    ]);
    $this->assertDatabaseHas($this->table, [
        'student_id' => $student->id,
        'date' => '2025-02-10',
        'timeslot_id' => $timeslot2->id,
        'updated_by_employee_id' => $timeslot_teacher2->employee_id,
        'attendance_taken_at' => '2025-02-10 08:30:00', // Make sure this doesn't change, only the updated_at will be updated
        'status' => PeriodAttendanceStatus::PRESENT->value,
        'leave_application_id' => null,
    ]);
    $this->assertDatabaseHas($this->table, [
        'student_id' => $student2->id,
        'date' => '2025-02-10',
        'timeslot_id' => $timeslot1->id,
        'updated_by_employee_id' => $timeslot_teacher1->employee_id,
        'attendance_taken_at' => '2025-02-10 08:00:00', // Make sure this doesn't change, only the updated_at will be updated
        'status' => PeriodAttendanceStatus::PRESENT->value,
        'leave_application_id' => null,
    ]);
    $this->assertDatabaseHas($this->table, [
        'student_id' => $student2->id,
        'date' => '2025-02-10',
        'timeslot_id' => $timeslot2->id,
        'updated_by_employee_id' => $timeslot_teacher2->employee_id,
        'attendance_taken_at' => '2025-02-10 08:30:00', // Make sure this doesn't change, only the updated_at will be updated
        'status' => PeriodAttendanceStatus::PRESENT->value,
        'leave_application_id' => null,
    ]);
});

test('updatePeriodAttendanceByLeaveApplications() - batchDelete', function () {
    $student = Student::factory()->create(['is_active' => true]);
    $student2 = Student::factory()->create(['is_active' => true]);

    $semester_setting = SemesterSetting::factory()->create();
    $semester_class1 = SemesterClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
    ]);
    $period_group1 = PeriodGroup::factory()->create();
    $main_j111_timetable = Timetable::factory()->create([
        'name' => 'J111 timetable',
        'is_active' => true,
        'period_group_id' => $period_group1->id,
        'semester_class_id' => $semester_class1,
    ]);

    $period1 = Period::factory()->create([
        'period_group_id' => $period_group1->id,
        'period' => 1,
        'from_time' => '08:00:00',
        'to_time' => '08:30:00',
        'day' => Day::MONDAY,
        'display_group' => 1,
    ]);
    $period_label1 = PeriodLabel::factory()->create([
        'period_group_id' => $period_group1->id,
        'period' => 1,
    ]);
    $period2 = Period::factory()->create([
        'period_group_id' => $period_group1->id,
        'period' => 2,
        'from_time' => '08:30:00',
        'to_time' => '09:00:00',
        'day' => Day::MONDAY,
        'display_group' => 1,
    ]);
    $period_label2 = PeriodLabel::factory()->create([
        'period_group_id' => $period_group1->id,
        'period' => 2,
    ]);

    StudentClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'semester_class_id' => $semester_class1->id,
        'student_id' => $student->id,
        'class_type' => ClassType::PRIMARY
    ]);

    StudentClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'semester_class_id' => $semester_class1->id,
        'student_id' => $student2->id,
        'class_type' => ClassType::PRIMARY
    ]);
    $timeslot2 = Timeslot::factory()->create([
        'timetable_id' => $main_j111_timetable->id,
        'period_id' => $period2->id,
        'placeholder' => null,
        'attendance_from' => '08:30',
        'attendance_to' => '09:00',
    ]);
    $timeslot_teacher2 = TimeslotTeacher::factory()->create([
        'timeslot_id' => $timeslot2->id,
    ]);

    StudentTimetable::refreshViewTable(false);

    // student 1 - without period attendance
    // student 2 - with period attendance
    $leave_application_student1 = LeaveApplication::factory()->create([
        'status' => LeaveApplicationStatus::PENDING->value,
        'leave_applicable_type' => Student::class,
        'leave_applicable_id' => $student->id,
        'is_full_day' => false,
    ]);
    LeaveApplicationPeriod::factory()->create(['leave_application_id' => $leave_application_student1->id, 'date' => '2025-02-10', 'period' => $period1->period]);

    PeriodAttendance::factory()->create([
        'student_id' => $student2->id,
        'date' => '2025-02-10',
        'timeslot_id' => $timeslot2->id,
        'updated_by_employee_id' => $timeslot_teacher2->employee_id,
        'status' => PeriodAttendanceStatus::PRESENT->value,
        'leave_application_id' => null,
        'period' => $timeslot2->period->period,
    ]);

    $this->periodAttendanceService
        ->setLeaveapplications(collect([$leave_application_student1]))
        ->setLeaveApplicationNewStatus(LeaveApplicationStatus::APPROVED->value)
        ->updatePeriodAttendanceByLeaveApplications();

    expect(PeriodAttendance::count())->toBe(1);
});
