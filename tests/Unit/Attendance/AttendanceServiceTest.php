<?php

use App\Enums\AttendanceCheckInStatus;
use App\Enums\AttendanceCheckOutStatus;
use App\Enums\AttendanceStatus;
use App\Enums\ClassType;
use App\Enums\Day;
use App\Enums\LeaveApplicationStatus;
use App\Enums\PeriodAttendanceStatus;
use App\Models\Attendance;
use App\Models\AttendanceInput;
use App\Models\Calendar;
use App\Models\CalendarSetting;
use App\Models\CalendarTarget;
use App\Models\ClassModel;
use App\Models\ClassSubject;
use App\Models\Employee;
use App\Models\LatestPrimaryClassBySemesterSettingView;
use App\Models\LeaveApplication;
use App\Models\LeaveApplicationPeriod;
use App\Models\Period;
use App\Models\PeriodAttendance;
use App\Models\PeriodGroup;
use App\Models\PeriodLabel;
use App\Models\SemesterClass;
use App\Models\SemesterSetting;
use App\Models\Student;
use App\Models\StudentClass;
use App\Models\Subject;
use App\Models\SubstituteRecord;
use App\Models\Timeslot;
use App\Models\TimeslotOverride;
use App\Models\TimeslotTeacher;
use App\Models\Timetable;
use App\Services\AttendanceService;
use Carbon\Carbon;
use Database\Seeders\InternationalizationSeeder;

beforeEach(function () {
    $this->seed([
        InternationalizationSeeder::class,
    ]);

    $this->attendanceService = app()->make(AttendanceService::class);
    app()->setLocale('en');
    $this->attendance_table = resolve(Attendance::class)->getTable();
    $this->attendance_input_table = resolve(AttendanceInput::class)->getTable();
});

test('getAllPaginatedAttendances getAllAttendances', function (int $expected_count, array $filters, array $expected_models) {
    $student = Student::factory()->create();
    $student->id = 1;
    $student->save();

    $student_attendance_late = Attendance::factory()->create([
        'attendance_recordable_type' => Student::class,
        'attendance_recordable_id' => $student->id,
        'date' => '2024-12-01',
        'check_in_datetime' => '2024-12-01 08:00:00',
        'check_in_status' => AttendanceCheckInStatus::LATE->value,
        'check_out_datetime' => null,
        'check_out_status' => null,
        'status' => AttendanceStatus::PRESENT->value,
        'card_id' => null,
    ]);

    $student_attendance_on_time = Attendance::factory()->create([
        'attendance_recordable_type' => Student::class,
        'attendance_recordable_id' => Student::factory(),
        'date' => '2024-12-02',
        'check_in_datetime' => '2024-12-01 07:00:00',
        'check_in_status' => AttendanceCheckInStatus::ON_TIME->value,
        'check_out_datetime' => null,
        'check_out_status' => null,
        'status' => AttendanceStatus::PRESENT->value,
        'card_id' => null,
    ]);

    $student_attendance_absent = Attendance::factory()->create([
        'attendance_recordable_type' => Student::class,
        'attendance_recordable_id' => Student::factory(),
        'date' => '2024-12-02',
        'check_in_datetime' => null,
        'check_in_status' => null,
        'check_out_datetime' => null,
        'check_out_status' => null,
        'status' => AttendanceStatus::ABSENT->value,
        'card_id' => null,
    ]);


    $teacher_attendance_on_time = Attendance::factory()->create([
        'attendance_recordable_type' => Employee::class,
        'attendance_recordable_id' => Employee::factory(),
        'date' => '2024-12-03',
        'check_in_datetime' => '2024-12-01 07:00:00',
        'check_in_status' => AttendanceCheckInStatus::ON_TIME->value,
        'check_out_datetime' => '2024-12-01 15:00:00',
        'check_out_status' => AttendanceCheckOutStatus::ON_TIME->value,
        'status' => AttendanceStatus::PRESENT->value,
        'card_id' => null,
    ]);

    $semester_setting1 = SemesterSetting::factory()->create(['id' => 100]);
    $semester_setting1_semester_class = SemesterClass::factory()->create([
        'id' => 100,
        'semester_setting_id' => $semester_setting1->id,
    ]);
    $semester_setting1_semester_class2 = SemesterClass::factory()->create([
        'id' => 200,
        'semester_setting_id' => $semester_setting1->id,
    ]);

    $semester_setting2 = SemesterSetting::factory()->create(['id' => 200]);
    $semester_setting2_semester_class = SemesterClass::factory()->create([
        'id' => 300,
        'semester_setting_id' => $semester_setting2->id,
    ]);

    StudentClass::factory()->create([
        'semester_setting_id' => $semester_setting1->id,
        'semester_class_id' => $semester_setting1_semester_class->id,
        'student_id' => $student->id,
        'is_active' => true,
    ]);
    StudentClass::factory()->create([
        'semester_setting_id' => $semester_setting1->id,
        'semester_class_id' => $semester_setting1_semester_class2->id,
        'student_id' => $student_attendance_on_time->attendance_recordable_id,
        'is_active' => true,
    ]);
    StudentClass::factory()->create([
        'semester_setting_id' => $semester_setting2->id,
        'semester_class_id' => $semester_setting2_semester_class->id,
        'student_id' => $student_attendance_absent->attendance_recordable_id,
        'is_active' => true,
    ]);

    $attendances = [
        'student_attendance_late' => $student_attendance_late,
        'student_attendance_on_time' => $student_attendance_on_time,
        'student_attendance_absent' => $student_attendance_absent,
        'teacher_attendance_on_time' => $teacher_attendance_on_time,
    ];

    $filters['order_by'] ??= ['id' => 'asc'];

    $result_paginated = $this->attendanceService->getAllPaginatedAttendances($filters)->toArray();
    expect($result_paginated['data'])->toHaveCount($expected_count);
    foreach ($expected_models as $key => $model) {
        $expected_data = $attendances[$model]->toArray();
        expect($result_paginated['data'][$key])->toEqual($expected_data);
    }
    $result = $this->attendanceService->getAllAttendances($filters)->toArray();
    expect($result)->toHaveCount($expected_count);
    foreach ($expected_models as $key => $model) {
        $expected_data = $attendances[$model]->toArray();
        expect($result[$key])->toEqual($expected_data);
    }
})->with([
    'no filter' => [4, [], ['student_attendance_late', 'student_attendance_on_time', 'student_attendance_absent', 'teacher_attendance_on_time']],
    'filter by date' => [2, ['date' => '2024-12-02'], ['student_attendance_on_time', 'student_attendance_absent']],
    'filter by date_from' => [3, ['date_from' => '2024-12-02'], ['student_attendance_on_time', 'student_attendance_absent', 'teacher_attendance_on_time']],
    'filter by date_to' => [3, ['date_to' => '2024-12-02'], ['student_attendance_late', 'student_attendance_on_time', 'student_attendance_absent']],
    'filter by date_from date_to' => [2, ['date_from' => '2024-12-02', 'date_to' => '2024-12-02'], ['student_attendance_on_time', 'student_attendance_absent']],
    'filter by status' => [3, ['status' => AttendanceStatus::PRESENT], ['student_attendance_late', 'student_attendance_on_time', 'teacher_attendance_on_time']],
    'filter by check_in_status' => [1, ['check_in_status' => AttendanceCheckInStatus::LATE], ['student_attendance_late']],
    'filter by check_out_status' => [1, ['check_out_status' => AttendanceCheckOutStatus::ON_TIME], ['teacher_attendance_on_time']],
    'filter by attendance_recordable_type' => [3, ['attendance_recordable_type' => Student::class], ['student_attendance_late', 'student_attendance_on_time', 'student_attendance_absent']],
    'filter by attendance_recordable_type attendance_recordable_id' => [1, ['attendance_recordable_type' => Student::class, 'attendance_recordable_id' => 1], ['student_attendance_late']],
    'filter by semester_setting_id' => [2, ['semester_setting_id' => 100], ['student_attendance_late', 'student_attendance_on_time']],
    'filter by semester_class_id' => [1, ['semester_class_id' => 200], ['student_attendance_on_time']],
    'filter by semester_setting_id and semester_class_id' => [1, ['semester_setting_id' => 100, 'semester_class_id' => 200], ['student_attendance_on_time']],
    'sort by id asc' => [4, ['order_by' => ['id' => 'asc']], ['student_attendance_late', 'student_attendance_on_time', 'student_attendance_absent', 'teacher_attendance_on_time']],
    'sort by id desc' => [4, ['order_by' => ['id' => 'desc']], ['teacher_attendance_on_time', 'student_attendance_absent', 'student_attendance_on_time', 'student_attendance_late']],
]);

test('deleteAttendance', function () {
    $attendance = Attendance::factory()->create();
    $this->assertDatabaseCount($this->attendance_table, 1);

    $this->attendanceService->deleteAttendance($attendance);
    $this->assertDatabaseCount($this->attendance_table, 0);
});

test('getFormattedPeriodOptionLabel', function () {
    $semester_class = SemesterClass::factory()->create([
        'class_id' => ClassModel::factory()->create([
            'name->en' => 'J111',
        ]),
    ]);
    $timetable = Timetable::factory()->create(['semester_class_id' => $semester_class->id]);
    $period_group = PeriodGroup::factory()->create();
    $period = Period::factory()->create(['period' => 1, 'period_group_id' => $period_group->id]);
    $period_label = PeriodLabel::factory()->create([
        'period_group_id' => $period_group->id,
        'period' => 1,
        'name->en' => '第一节',
        'name->zh' => '第一节',
        'is_attendance_required' => true,
    ]);
    $timeslot = Timeslot::factory()->create([
        'timetable_id' => $timetable->id,
        'day' => Day::MONDAY->value, // 2024-12-30 is Monday
        'attendance_from' => '08:00:00',
        'attendance_to' => '09:00:00',
        'period_id' => $period->id,
        'class_subject_id' => ClassSubject::factory()->create([
            'subject_id' => Subject::factory()->create([
                'name->en' => 'Math',
            ]),
        ]),
    ]);

    $period2 = Period::factory()->create(['period' => 2, 'period_group_id' => $period_group->id]);
    $period_label2 = PeriodLabel::factory()->create([
        'period_group_id' => $period_group->id,
        'period' => 2,
        'name->en' => '第二节',
        'name->zh' => '第二节',
        'is_attendance_required' => true,
    ]);
    $timeslot2 = Timeslot::factory()->create([
        'timetable_id' => $timetable->id,
        'day' => Day::MONDAY->value, // 2024-12-30 is Monday
        'attendance_from' => '09:00:00',
        'attendance_to' => '09:30:00',
        'period_id' => $period2->id,
        'class_subject_id' => null,
        'placeholder' => '班务',
    ]);

    $period3 = Period::factory()->create(['period' => 3, 'period_group_id' => $period_group->id]);
    $period_label3 = PeriodLabel::factory()->create([
        'period_group_id' => $period_group->id,
        'period' => 3,
        'name->en' => '第三节',
        'name->zh' => '第三节',
        'is_attendance_required' => true,
    ]);
    $timeslot3 = Timeslot::factory()->create([
        'timetable_id' => $timetable->id,
        'day' => Day::MONDAY->value, // 2024-12-30 is Monday
        'attendance_from' => '09:30:00',
        'attendance_to' => '10:00:00',
        'period_id' => $period3->id,
        'class_subject_id' => null,
        'placeholder' => '班务',
    ]);

    $timeslot_override = TimeslotOverride::factory()->create([
        'period' => 4,
        'placeholder' => 'sports',
        'attendance_from' => '10:00:00',
        'attendance_to' => '02:00:00',
    ]);


    $label = $this->attendanceService->getFormattedPeriodOptionLabel([$timeslot])['label'];
    expect($label)->toBe("[J111]第一节 (08:00 - 09:00) - Math");

    $label = $this->attendanceService->getFormattedPeriodOptionLabel([$timeslot2])['label'];
    expect($label)->toBe("[J111]第二节 (09:00 - 09:30) - 班务");

    $label = $this->attendanceService->getFormattedPeriodOptionLabel([$timeslot2, $timeslot3])['label'];
    expect($label)->toBe("[J111]第二节, 第三节 (09:00 - 10:00) - 班务");

    $label = $this->attendanceService->getFormattedPeriodOptionLabel([$timeslot_override])['label'];
    expect($label)->toBe("Period 4 (10:00 - 02:00) - sports (Override)");
});

test('getPeriodsByTimeslotTeacher', function () {
    // user with employee account
    $employee = Employee::factory()->create();
    $employee2 = Employee::factory()->create();     // irrelevant teacher

    $semester_class = SemesterClass::factory()->create();
    $semester_class2 = SemesterClass::factory()->create();

    $timetable = Timetable::factory()->create(['semester_class_id' => $semester_class->id]);
    $timetable2 = Timetable::factory()->create(['semester_class_id' => $semester_class2->id]);

    $period = Period::factory()->create(['period' => 1]);

    // employee1 teach class 1 in period 1
    // employee2 teach class 2 in period 1
    $timeslot_employee1 = Timeslot::factory()->create([
        'timetable_id' => $timetable->id,
        'day' => Day::MONDAY->value, // 2024-12-30 is Monday
        'attendance_from' => '08:00:00',
        'attendance_to' => '09:00:00',
        'period_id' => $period->id,
    ]);
    TimeslotTeacher::factory()->create([
        'employee_id' => $employee->id,
        'timeslot_id' => $timeslot_employee1->id,
    ]);
    $timeslot_employee2 = Timeslot::factory()->create([
        'timetable_id' => $timetable2->id,
        'day' => Day::MONDAY->value, // 2024-12-30 is Monday
        'attendance_from' => '08:00:00',
        'attendance_to' => '09:00:00',
        'period_id' => $period->id,
    ]);
    TimeslotTeacher::factory()->create([
        'employee_id' => $employee2->id,
        'timeslot_id' => $timeslot_employee2->id,
    ]);

    // employee1 teach class 2 in period 2
    // employee2 teach class 1 in period 2
    $period2 = Period::factory()->create(['period' => 2]);
    $timeslot2_employee1 = Timeslot::factory()->create([
        'timetable_id' => $timetable2->id,
        'day' => Day::MONDAY->value, // 2024-12-30 is Monday
        'attendance_from' => '09:00:00',
        'attendance_to' => '10:00:00',
        'period_id' => $period2->id,
    ]);
    TimeslotTeacher::factory()->create([
        'employee_id' => $employee->id,
        'timeslot_id' => $timeslot2_employee1->id,
    ]);
    $timeslot2_employee2 = Timeslot::factory()->create([
        'timetable_id' => $timetable->id,
        'day' => Day::MONDAY->value, // 2024-12-30 is Monday
        'attendance_from' => '09:00:00',
        'attendance_to' => '10:00:00',
        'period_id' => $period2->id,
    ]);
    TimeslotTeacher::factory()->create([
        'employee_id' => $employee2->id,
        'timeslot_id' => $timeslot2_employee2->id,
    ]);

    config(['school.timezone' => 'Asia/Kuala_Lumpur']);
    Carbon::setTestNow('2024-12-30 00:00:00'); // malaysia timezone 2024-12-30 08:00:00

    // employee 1
    $data = $this->attendanceService
        ->setEmployee($employee)
        ->setDate('2024-12-30')
        ->getPeriodsByTimeslotTeacher();

    expect($data)->toMatchArray([
        [
            'timeslot_id' => [$timeslot_employee1->id],
            'timeslot_type' => Timeslot::class,
            'label' => $this->attendanceService->getFormattedPeriodOptionLabel([$timeslot_employee1])['label'],
            'period_labels' => $this->attendanceService->getFormattedPeriodOptionLabel([$timeslot_employee1])['period_labels'],
            'current_class' => true,
            'is_disabled' => false, // 08:00:00 - 09:00:00
            'period' => 1,
        ],
        [
            'timeslot_id' => [$timeslot2_employee1->id],
            'timeslot_type' => Timeslot::class,
            'label' => $this->attendanceService->getFormattedPeriodOptionLabel([$timeslot2_employee1])['label'],
            'period_labels' => $this->attendanceService->getFormattedPeriodOptionLabel([$timeslot2_employee1])['period_labels'],
            'current_class' => false,
            'is_disabled' => true, // 09:00:00 - 10:00:00
            'period' => 2,
        ],
    ]);

    // employee 2
    $data = $this->attendanceService
        ->setEmployee($employee2)
        ->setDate('2024-12-30')
        ->getPeriodsByTimeslotTeacher();

    expect($data)->toMatchArray([
        [
            'timeslot_id' => [$timeslot_employee2->id],
            'timeslot_type' => Timeslot::class,
            'label' => $this->attendanceService->getFormattedPeriodOptionLabel([$timeslot_employee2])['label'],
            'period_labels' => $this->attendanceService->getFormattedPeriodOptionLabel([$timeslot_employee2])['period_labels'],
            'current_class' => true,
            'is_disabled' => false, // 08:00:00 - 09:00:00
            'period' => 1,
        ],
        [
            'timeslot_id' => [$timeslot2_employee2->id],
            'timeslot_type' => Timeslot::class,
            'label' => $this->attendanceService->getFormattedPeriodOptionLabel([$timeslot2_employee2])['label'],
            'period_labels' => $this->attendanceService->getFormattedPeriodOptionLabel([$timeslot2_employee2])['period_labels'],
            'current_class' => false,
            'is_disabled' => true, // 09:00:00 - 10:00:00
            'period' => 2,
        ],
    ]);
});

test('getPeriodsByTimeslotTeacher - with substitute records', function () {
    // user with employee account
    $employee = Employee::factory()->create();
    $employee2 = Employee::factory()->create();

    $period = Period::factory()->create(['period' => 1]);
    $period2 = Period::factory()->create(['period' => 2]);

    $employee_timetable = Timetable::factory()->create();
    $employee2_timetable = Timetable::factory()->create();


    // employee 1 = timeslot 1, timeslot 2 (as substitute teacher)
    // employee 2 = timeslot 1, timeslot 2 (requestor)
    $employee_timeslot_period1 = Timeslot::factory()->create([
        'timetable_id' => $employee_timetable->id,
        'day' => Day::MONDAY->value, // 2024-12-30 is Monday
        'attendance_from' => '08:00:00',
        'attendance_to' => '09:00:00',
        'period_id' => $period->id,
    ]);
    TimeslotTeacher::factory()->create([
        'employee_id' => $employee->id,
        'timeslot_id' => $employee_timeslot_period1->id,
    ]);
    $employee2_timeslot_period1 = Timeslot::factory()->create([
        'timetable_id' => $employee2_timetable->id,
        'day' => Day::MONDAY->value, // 2024-12-30 is Monday
        'attendance_from' => '08:00:00',
        'attendance_to' => '09:00:00',
        'period_id' => $period->id,
    ]);
    TimeslotTeacher::factory()->create([
        'employee_id' => $employee2->id,
        'timeslot_id' => $employee2_timeslot_period1->id,
    ]);
    $employee2_timeslot_period2 = Timeslot::factory()->create([
        'timetable_id' => $employee2_timetable->id,
        'day' => Day::MONDAY->value, // 2024-12-30 is Monday
        'attendance_from' => '09:00:00',
        'attendance_to' => '10:00:00',
        'period_id' => $period2->id,
    ]);
    TimeslotTeacher::factory()->create([
        'employee_id' => $employee2->id,
        'timeslot_id' => $employee2_timeslot_period2->id,
    ]);

    // substitute records
    SubstituteRecord::factory()->create([
        'timeslot_id' => $employee2_timeslot_period1->id,
        'substitute_teacher_id' => $employee2->id, // if somehow teacher assign himself as substitute teacher
        'remarks' => 'MC',
        'substitute_date' => '2024-12-30', // Monday
        'requestor_id' => $employee2->id,
    ]);
    SubstituteRecord::factory()->create([
        'timeslot_id' => $employee2_timeslot_period2->id,
        'substitute_teacher_id' => $employee->id,
        'remarks' => 'MC',
        'substitute_date' => '2024-12-30', // Monday
        'requestor_id' => $employee2->id,
    ]);


    // last week monday substitute record
    $substitute_timeslot_different_date_same_day = Timeslot::factory()->create([
        'timetable_id' => $employee2_timetable->id,
        'day' => Day::MONDAY->value, // 2024-12-23 is Monday
        'attendance_from' => '08:00:00',
        'attendance_to' => '09:00:00',
        'period_id' => $period->id,
    ]);
    TimeslotTeacher::factory()->create([
        'employee_id' => $employee2->id,
        'timeslot_id' => $substitute_timeslot_different_date_same_day->id,
    ]);
    SubstituteRecord::factory()->create([
        'timeslot_id' => $substitute_timeslot_different_date_same_day->id,
        'substitute_teacher_id' => $employee->id,
        'remarks' => 'AL',
        'substitute_date' => '2024-12-23', // last week monday
        'requestor_id' => $employee2->id,
        'day' => Day::MONDAY,
    ]);


    config(['school.timezone' => 'Asia/Kuala_Lumpur']);
    Carbon::setTestNow('2024-12-30 00:00:00'); // malaysia timezone 2024-12-30 08:00:00

    $data = $this->attendanceService
        ->setEmployee($employee)
        ->setDate('2024-12-30')
        ->getPeriodsByTimeslotTeacher();

    // only show substitute on 2024-12-30
    expect($data)->toMatchArray([
        [
            'timeslot_id' => [$employee_timeslot_period1->id],
            'timeslot_type' => Timeslot::class,
            'label' => $this->attendanceService->getFormattedPeriodOptionLabel([$employee_timeslot_period1])['label'],
            'period_labels' => $this->attendanceService->getFormattedPeriodOptionLabel([$employee_timeslot_period1])['period_labels'],
            'current_class' => true,
            'is_disabled' => false, // 08:00:00 - 09:00:00
            'period' => 1,
        ],
        [
            'timeslot_id' => [$employee2_timeslot_period2->id],
            'timeslot_type' => Timeslot::class,
            'label' => $this->attendanceService->getFormattedPeriodOptionLabel([$employee2_timeslot_period2])['label'],
            'period_labels' => $this->attendanceService->getFormattedPeriodOptionLabel([$employee2_timeslot_period2])['period_labels'],
            'current_class' => false,
            'is_disabled' => true, // 09:00:00 - 10:00:00
            'period' => 2,
        ],
    ]);

    $data = $this->attendanceService
        ->setEmployee($employee2)
        ->setDate('2024-12-30')
        ->getPeriodsByTimeslotTeacher();

    // hide timeslot 2
    expect($data)->toMatchArray([
        [
            'timeslot_id' => [$employee2_timeslot_period1->id],
            'timeslot_type' => Timeslot::class,
            'label' => $this->attendanceService->getFormattedPeriodOptionLabel([$employee2_timeslot_period1])['label'],
            'period_labels' => $this->attendanceService->getFormattedPeriodOptionLabel([$employee2_timeslot_period1])['period_labels'],
            'current_class' => true,
            'is_disabled' => false, // 08:00:00 - 09:00:00
            'period' => 1,
        ],
    ]);
});

test('getPeriodsByTimeslotTeacher - without timeslots', function () {
    // user with employee account
    $employee = Employee::factory()->create();

    $semester_class = SemesterClass::factory()->create();
    $timetable = Timetable::factory()->create(['semester_class_id' => $semester_class->id]);
    $period = Period::factory()->create(['period' => 1]);
    $timeslot = Timeslot::factory()->create([
        'timetable_id' => $timetable->id,
        'day' => Day::MONDAY->value, // 2024-12-30 is Monday
        'attendance_from' => '08:00:00',
        'attendance_to' => '09:00:00',
        'period_id' => $period->id,
    ]);

    $semester_class2 = SemesterClass::factory()->create();
    $timetable2 = Timetable::factory()->create(['semester_class_id' => $semester_class2->id]);
    $period2 = Period::factory()->create(['period' => 2]);
    $timeslot2 = Timeslot::factory()->create([
        'timetable_id' => $timetable2->id,
        'day' => Day::MONDAY->value, // 2024-12-30 is Monday
        'attendance_from' => '09:00:00',
        'attendance_to' => '10:00:00',
        'period_id' => $period2->id,
    ]);

    expect(TimeslotTeacher::where('employee_id', $employee->id)->count())->toBe(0);

    config(['school.timezone' => 'Asia/Kuala_Lumpur']);
    Carbon::setTestNow('2024-12-30 00:00:00'); // malaysia timezone 2024-12-30 08:00:00

    $data = $this->attendanceService
        ->setEmployee($employee)
        ->setDate('2024-12-30')
        ->getPeriodsByTimeslotTeacher();

    expect($data)->toMatchArray([]);
});

test('getPeriodsByTimeslotTeacher - with timeslot override', function () {
    // user with employee account
    $employee = Employee::factory()->create();
    $date = '2024-12-30';

    $semester_class = SemesterClass::factory()->create();
    $timetable = Timetable::factory()->create(['semester_class_id' => $semester_class->id]);
    $period = Period::factory()->create(['period' => 1]);
    $timeslot = Timeslot::factory()->create([
        'timetable_id' => $timetable->id,
        'day' => Day::MONDAY->value, // 2024-12-30 is Monday
        'attendance_from' => '08:00:00',
        'attendance_to' => '09:00:00',
        'period_id' => $period->id,
    ]);
    TimeslotTeacher::factory()->create([
        'employee_id' => $employee->id,
        'timeslot_id' => $timeslot->id,
    ]);

    // timeslot override
    $student = Student::factory()->create();
    $student2 = Student::factory()->create();
    $timeslot_override = TimeslotOverride::factory()->create([
        'student_id' => $student->id,
        'date' => $date,
        'period' => 2,
        'placeholder' => 'test',
        'employee_id' => $employee->id,
        'attendance_from' => '09:00:00',
        'attendance_to' => '10:00:00',
        'class_attendance_required' => true,
        'is_empty' => false,
    ]);
    $timeslot_override2 = TimeslotOverride::factory()->create([
        'student_id' => $student2->id,
        'date' => $date,
        'period' => 2,
        'placeholder' => 'test',
        'employee_id' => $employee->id,
        'attendance_from' => '09:00:00',
        'attendance_to' => '10:00:00',
        'class_attendance_required' => true,
        'is_empty' => false,
    ]);

    config(['school.timezone' => 'Asia/Kuala_Lumpur']);
    Carbon::setTestNow("{$date} 01:01:00"); // malaysia timezone 2024-12-30 09:01:00

    $data = $this->attendanceService
        ->setEmployee($employee)
        ->setDate($date)
        ->getPeriodsByTimeslotTeacher();

    expect($data)->toMatchArray([
        [
            'timeslot_id' => [$timeslot->id],
            'timeslot_type' => Timeslot::class,
            'label' => $this->attendanceService->getFormattedPeriodOptionLabel([$timeslot])['label'],
            'period_labels' => $this->attendanceService->getFormattedPeriodOptionLabel([$timeslot])['period_labels'],
            'current_class' => false,
            'is_disabled' => false,
            'period' => 1,
        ],
        [
            'timeslot_id' => [$timeslot_override->period], // timeslot override will pass period as timeslot_id
            'timeslot_type' => TimeslotOverride::class,
            'label' => $this->attendanceService->getFormattedPeriodOptionLabel([$timeslot_override])['label'],
            'period_labels' => $this->attendanceService->getFormattedPeriodOptionLabel([$timeslot_override])['period_labels'],
            'current_class' => true,
            'is_disabled' => false,
            'period' => 2,
        ],
    ]);
});

test('getPeriodsByTimeslotTeacher - with continuous period', function () {
    // user with employee account
    $employee = Employee::factory()->create();

    $date = '2024-12-30';
    $date_sub_1_week = '2024-12-23';

    $semester_class = SemesterClass::factory()->create();
    $timetable = Timetable::factory()->create(['semester_class_id' => $semester_class->id]);

    $semester_class2 = SemesterClass::factory()->create();
    $timetable2 = Timetable::factory()->create(['semester_class_id' => $semester_class2->id]);
    $period_group = PeriodGroup::factory()->create();
    $period = Period::factory()->create(['period' => 1, 'period_group_id' => $period_group->id]);
    $period2 = Period::factory()->create(['period' => 2, 'period_group_id' => $period_group->id]);
    $period3 = Period::factory()->create(['period' => 3, 'period_group_id' => $period_group->id]);
    $period4 = Period::factory()->create(['period' => 4, 'period_group_id' => $period_group->id]);
    $period5 = Period::factory()->create(['period' => 5, 'period_group_id' => $period_group->id]);
    $period6 = Period::factory()->create(['period' => 6, 'period_group_id' => $period_group->id]);
    $period7 = Period::factory()->create(['period' => 7, 'period_group_id' => $period_group->id]);

    // period 1 -> 3 continuous period (班务)
    $timeslot = Timeslot::factory()->create([
        'timetable_id' => $timetable->id,
        'day' => Day::MONDAY->value, // 2024-12-30 is Monday
        'attendance_from' => '08:00:00',
        'attendance_to' => '09:00:00',
        'period_id' => $period->id,
        'class_subject_id' => null,
        'placeholder' => '班务',
    ]);
    $timeslot2 = Timeslot::factory()->create([
        'timetable_id' => $timetable->id,
        'day' => Day::MONDAY->value, // 2024-12-30 is Monday
        'attendance_from' => '09:00:00',
        'attendance_to' => '10:00:00',
        'period_id' => $period2->id,
        'class_subject_id' => null,
        'placeholder' => '班务',
    ]);
    $timeslot3 = Timeslot::factory()->create([
        'timetable_id' => $timetable->id,
        'day' => Day::MONDAY->value, // 2024-12-30 is Monday
        'attendance_from' => '10:00:00',
        'attendance_to' => '11:00:00',
        'period_id' => $period3->id,
        'class_subject_id' => null,
        'placeholder' => '班务',
    ]);
    // period 4 not continuous period + is override
    // timeslot4 should be overwritten by timeslot overwrite
    $timeslot4 = Timeslot::factory()->create([
        'timetable_id' => $timetable->id,
        'day' => Day::MONDAY->value, // 2024-12-30 is Monday
        'attendance_from' => '11:00:00',
        'attendance_to' => '11:30:00',
        'period_id' => $period4->id,
        'class_subject_id' => null,
        'placeholder' => 'something',
    ]);
    $timeslot_override_period4 = TimeslotOverride::factory()->create([
        'date' => $date,
        'period' => 4,
        'placeholder' => 'test',
        'employee_id' => $employee->id,
        'attendance_from' => '11:00:00',
        'attendance_to' => '11:30:00',
        'class_attendance_required' => true,
        'is_empty' => false,
    ]);
    // period 5 -> 6 continuous period (Math)
    $math_class_subject = ClassSubject::factory()->create([
        'semester_class_id' => $semester_class->id,
        'subject_id' => Subject::factory()->create([
            'name->en' => 'Math',
        ]),
    ]);
    $timeslot5 = Timeslot::factory()->create([
        'timetable_id' => $timetable->id,
        'day' => Day::MONDAY->value, // 2024-12-30 is Monday
        'attendance_from' => '11:30:00',
        'attendance_to' => '12:00:00',
        'period_id' => $period5->id,
        'placeholder' => null,
        'class_subject_id' => $math_class_subject->id,
    ]);
    $timeslot6 = Timeslot::factory()->create([
        'timetable_id' => $timetable->id,
        'day' => Day::MONDAY->value, // 2024-12-30 is Monday
        'attendance_from' => '12:00:00',
        'attendance_to' => '12:30:00',
        'period_id' => $period6->id,
        'placeholder' => null,
        'class_subject_id' => $math_class_subject->id,
    ]);
    // period 7 another class (Math)
    $math_class_subject2 = ClassSubject::factory()->create([
        'semester_class_id' => $semester_class2->id,
        'subject_id' => Subject::factory()->create([
            'name->en' => 'Math',
        ]),
    ]);
    $timeslot7 = Timeslot::factory()->create([
        'timetable_id' => $timetable2->id,
        'day' => Day::MONDAY->value, // 2024-12-30 is Monday
        'attendance_from' => '12:30:00',
        'attendance_to' => '13:00:00',
        'period_id' => $period7->id,
        'placeholder' => null,
        'class_subject_id' => $math_class_subject2->id,
    ]);
    foreach ([$timeslot, $timeslot2, $timeslot3, $timeslot5, $timeslot6, $timeslot7] as $t) {
        TimeslotTeacher::factory()->create([
            'employee_id' => $employee->id,
            'timeslot_id' => $t->id,
        ]);
    }

    config(['school.timezone' => 'Asia/Kuala_Lumpur']);
    Carbon::setTestNow("{$date} 03:01:00"); // malaysia timezone 2024-12-30 11:01:00

    $data = $this->attendanceService
        ->setEmployee($employee)
        ->setDate($date)
        ->getPeriodsByTimeslotTeacher();

    expect($data)->toMatchArray([
        [
            'timeslot_id' => [$timeslot->id, $timeslot2->id, $timeslot3->id],
            'timeslot_type' => Timeslot::class,
            'label' => $this->attendanceService->getFormattedPeriodOptionLabel([$timeslot, $timeslot2, $timeslot3])['label'],
            'period_labels' => $this->attendanceService->getFormattedPeriodOptionLabel([$timeslot, $timeslot2, $timeslot3])['period_labels'],
            'current_class' => false,
            'is_disabled' => false, // 08:00:00 - 11:00:00
            'period' => 1,
        ],
        [
            'timeslot_id' => [$timeslot_override_period4->period],       // for timeslot override will return period instead of ID
            'timeslot_type' => TimeslotOverride::class,
            'label' => $this->attendanceService->getFormattedPeriodOptionLabel([$timeslot_override_period4])['label'],
            'period_labels' => $this->attendanceService->getFormattedPeriodOptionLabel([$timeslot_override_period4])['period_labels'],
            'current_class' => true,
            'is_disabled' => false, // 11:00:00 - 11:30:00
            'period' => 4,
        ],
        [
            'timeslot_id' => [$timeslot5->id, $timeslot6->id],
            'timeslot_type' => Timeslot::class,
            'label' => $this->attendanceService->getFormattedPeriodOptionLabel([$timeslot5, $timeslot6])['label'],
            'period_labels' => $this->attendanceService->getFormattedPeriodOptionLabel([$timeslot5, $timeslot6])['period_labels'],
            'current_class' => false,
            'is_disabled' => true,  // 11:30:00 - 12:30:00
            'period' => 5,
        ],
        [
            'timeslot_id' => [$timeslot7->id],
            'timeslot_type' => Timeslot::class,
            'label' => $this->attendanceService->getFormattedPeriodOptionLabel([$timeslot7])['label'],
            'period_labels' => $this->attendanceService->getFormattedPeriodOptionLabel([$timeslot7])['period_labels'],
            'current_class' => false,
            'is_disabled' => true, // 12:30:00 - 13:00:00
            'period' => 7,
        ],
    ]);

    // past date (current_class all false)
    $timeslot_override_period4->update(['date' => $date_sub_1_week]);
    $data = $this->attendanceService
        ->setEmployee($employee)
        ->setDate($date_sub_1_week)
        ->getPeriodsByTimeslotTeacher();

    expect($data)->toMatchArray([
        [
            'timeslot_id' => [$timeslot->id, $timeslot2->id, $timeslot3->id],
            'timeslot_type' => Timeslot::class,
            'label' => $this->attendanceService->getFormattedPeriodOptionLabel([$timeslot, $timeslot2, $timeslot3])['label'],
            'period_labels' => $this->attendanceService->getFormattedPeriodOptionLabel([$timeslot, $timeslot2, $timeslot3])['period_labels'],
            'current_class' => false,
            'is_disabled' => false, // 08:00:00 - 11:00:00
            'period' => 1,
        ],
        [
            'timeslot_id' => [$timeslot_override_period4->period],       // for timeslot override will return period instead of ID
            'timeslot_type' => TimeslotOverride::class,
            'label' => $this->attendanceService->getFormattedPeriodOptionLabel([$timeslot_override_period4])['label'],
            'period_labels' => $this->attendanceService->getFormattedPeriodOptionLabel([$timeslot_override_period4])['period_labels'],
            'current_class' => false,
            'is_disabled' => false, // 11:00:00 - 11:30:00
            'period' => 4,
        ],
        [
            'timeslot_id' => [$timeslot5->id, $timeslot6->id],
            'timeslot_type' => Timeslot::class,
            'label' => $this->attendanceService->getFormattedPeriodOptionLabel([$timeslot5, $timeslot6])['label'],
            'period_labels' => $this->attendanceService->getFormattedPeriodOptionLabel([$timeslot5, $timeslot6])['period_labels'],
            'current_class' => false,
            'is_disabled' => false,  // 11:30:00 - 12:30:00
            'period' => 5,
        ],
        [
            'timeslot_id' => [$timeslot7->id],
            'timeslot_type' => Timeslot::class,
            'label' => $this->attendanceService->getFormattedPeriodOptionLabel([$timeslot7])['label'],
            'period_labels' => $this->attendanceService->getFormattedPeriodOptionLabel([$timeslot7])['period_labels'],
            'current_class' => false,
            'is_disabled' => false, // 12:30:00 - 13:00:00
            'period' => 7,
        ],
    ]);
});

test('getClassAttendanceByTimeslot and bulkUpdateClassAttendance (1 timeslot)', function () {
    $employee = Employee::factory()->create();

    $semester_class = SemesterClass::factory()->create();
    $timetable = Timetable::factory()->create(['semester_class_id' => $semester_class->id]);

    $period_group = PeriodGroup::factory()->create();
    $period = Period::factory()->create([
        'period_group_id' => $period_group->id,
        'period' => 2,
    ]);
    $period_label = PeriodLabel::factory()->create([
        'period_group_id' => $period_group->id,
        'period' => 2,
        'name->en' => '第一节',
        'name->zh' => '第一节',
        'is_attendance_required' => true,
    ]);

    $timeslot = Timeslot::factory()->create([
        'timetable_id' => $timetable->id,
        'day' => Day::MONDAY->value, // 2024-12-30 is Monday
        'period_id' => $period->id,
        'attendance_from' => '08:00:00',
        'attendance_to' => '08:30:00',
    ]);
    TimeslotTeacher::factory()->create([
        'employee_id' => $employee->id,
        'timeslot_id' => $timeslot->id,
    ]);

    $date = '2024-12-30';
    $student = Student::factory()->create([
        'name->en' => 'a'
    ]);
    $student2 = Student::factory()->create([
        'name->en' => 'b'
    ]);
    $student3 = Student::factory()->create([
        'name->en' => 'd'
    ]);
    $student4 = Student::factory()->create([
        'name->en' => 'c'
    ]);

    StudentClass::factory()->create([
        'semester_setting_id' => $semester_class->semester_setting_id,
        'semester_class_id' => $semester_class->id,
        'class_type' => ClassType::PRIMARY->value,
        'student_id' => $student->id,
        'seat_no' => 2,
    ]);
    StudentClass::factory()->create([
        'semester_setting_id' => $semester_class->semester_setting_id,
        'semester_class_id' => $semester_class->id,
        'class_type' => ClassType::PRIMARY->value,
        'student_id' => $student2->id,
        'seat_no' => 1,
    ]);
    StudentClass::factory()->create([
        'semester_setting_id' => $semester_class->semester_setting_id,
        'semester_class_id' => $semester_class->id,
        'class_type' => ClassType::PRIMARY->value,
        'student_id' => $student3->id,
        'seat_no' => null,
    ]);
    StudentClass::factory()->create([
        'semester_setting_id' => $semester_class->semester_setting_id,
        'semester_class_id' => $semester_class->id,
        'class_type' => ClassType::PRIMARY->value,
        'student_id' => $student4->id,
        'seat_no' => null,
    ]);

    // student 1
    $approved_student_application = LeaveApplication::factory()->create([
        'leave_applicable_type' => Student::class,
        'leave_applicable_id' => $student->id,
        'status' => LeaveApplicationStatus::APPROVED->value,
        'is_present' => true,
        'reason' => 'school competition',
        'remarks' => 'test',
    ]);

    LeaveApplicationPeriod::factory()->create([
        'leave_application_id' => $approved_student_application->id,
        'date' => $date,
        'period' => PeriodLabel::factory()->create([
            'period' => $timeslot->period->period
        ])->period,
    ]);

    $period_attendance_student = PeriodAttendance::factory()->create([
        'student_id' => $student->id,
        'timeslot_type' => Timeslot::class,
        'timeslot_id' => $timeslot->id,
        'date' => $date,
        'status' => PeriodAttendanceStatus::PRESENT->value,
        'leave_application_id' => $approved_student_application->id,
        'period' => $timeslot->period->period,
    ]);

    $student_school_attendance = Attendance::factory()->create([
        'attendance_recordable_type' => Student::class,
        'attendance_recordable_id' => $student->id,
        'date' => $date,
        'check_in_datetime' => "{$date} 01:00:00",
        'check_in_status' => AttendanceCheckInStatus::ON_TIME->value,
        'check_in_remarks' => null,
        'check_in_overridable_type' => null,
        'check_in_overridable_id' => null,
        'attendance_from' => null,
        'check_out_datetime' => null,
        'check_out_status' => null,
        'check_out_remarks' => null,
        'check_out_overridable_type' => null,
        'check_out_overridable_id' => null,
        'attendance_to' => null,
        'status' => AttendanceStatus::PRESENT,
        'card_id' => null,
        'leave_application_id' => null,
        'is_error' => false,
        'error_message' => null,
    ]);

    // student 2
    $updated_by_employee = Employee::factory()->create();
    $period_attendance_student2 = PeriodAttendance::factory()->create([
        'student_id' => $student2->id,
        'timeslot_type' => Timeslot::class,
        'timeslot_id' => $timeslot->id,
        'date' => $date,
        'status' => PeriodAttendanceStatus::PRESENT->value,
        'leave_application_id' => null,
        'period' => $timeslot->period->period,
        'updated_by_employee_id' => $updated_by_employee,
    ]);

    // student 3 and 4
    $period_attendance_student3 = PeriodAttendance::factory()->create([
        'student_id' => $student3->id,
        'timeslot_type' => Timeslot::class,
        'timeslot_id' => $timeslot->id,
        'date' => $date,
        'status' => PeriodAttendanceStatus::ABSENT->value,
        'leave_application_id' => null,
        'period' => $timeslot->period->period,
        'updated_by_employee_id' => null,
    ]);
    $period_attendance_student4 = PeriodAttendance::factory()->create([
        'student_id' => $student4->id,
        'timeslot_type' => Timeslot::class,
        'timeslot_id' => $timeslot->id,
        'date' => $date,
        'status' => PeriodAttendanceStatus::ABSENT->value,
        'leave_application_id' => null,
        'period' => $timeslot->period->period,
        'updated_by_employee_id' => null,
    ]);

    DB::statement('REFRESH MATERIALIZED VIEW latest_primary_class_by_semester_setting_views');

    $data = $this->attendanceService
        ->setTimeslotType(Timeslot::class)
        ->setTimeslotIds([$timeslot->id])
        ->setDate($date)
        ->setEmployee($employee)
        ->getClassAttendanceByTimeslot();

    expect($data)->toMatchArray([
        "periods" => [
            $timeslot->period->period => [
                "period" => $timeslot->period->period,
                "label" => "第一节 (08:00 - 08:30)",
            ],
        ],
        "students" => [
            [
                'student_id' => $student4->id,
                'student_number' => $student4->student_number,
                'student_name' => 'c',
                'student_name_translations' => $student4->translations,
                'student_photo' => $student4->photo,
                'seat_no' => null,
                'school_check_in_datetime' => null,
                'current_class' => $student4->primaryClass->semesterClass->classModel->getFormattedTranslations('name'),
                'class_attendances' => [
                    $timeslot->period->period => [
                        'period' => $timeslot->period->period,
                        'timeslot_type' => Timeslot::class,
                        'timeslot_id' => $timeslot->id,
                        'class_attendance_status' => PeriodAttendanceStatus::ABSENT->value,
                        'is_editable' => false,
                        'is_default' => true,
                        'leave_application_id' => null,
                        'leave_application_info' => null,
                        'updated_by_employee' => [],
                    ]
                ]
            ],
            [
                'student_id' => $student3->id,
                'student_number' => $student3->student_number,
                'student_name' => 'd',
                'student_name_translations' => $student3->translations,
                'student_photo' => $student3->photo,
                'seat_no' => null,
                'school_check_in_datetime' => null,
                'current_class' => $student3->primaryClass->semesterClass->classModel->getFormattedTranslations('name'),
                'class_attendances' => [
                    $timeslot->period->period => [
                        'period' => $timeslot->period->period,
                        'timeslot_type' => Timeslot::class,
                        'timeslot_id' => $timeslot->id,
                        'class_attendance_status' => PeriodAttendanceStatus::ABSENT->value,
                        'is_editable' => false,
                        'is_default' => true,
                        'leave_application_id' => null,
                        'leave_application_info' => null,
                        'updated_by_employee' => [],
                    ]
                ]
            ],
            [
                'student_id' => $student2->id,
                'student_number' => $student2->student_number,
                'student_name' => 'b',
                'student_name_translations' => $student2->translations,
                'student_photo' => $student2->photo,
                'seat_no' => 1,
                'school_check_in_datetime' => null,
                'current_class' => $student2->primaryClass->semesterClass->classModel->getFormattedTranslations('name'),
                'class_attendances' => [
                    $timeslot->period->period => [
                        'period' => $timeslot->period->period,
                        'timeslot_type' => Timeslot::class,
                        'timeslot_id' => $timeslot->id,
                        'class_attendance_status' => PeriodAttendanceStatus::PRESENT->value,
                        'is_editable' => false,
                        'is_default' => false,
                        'leave_application_id' => null,
                        'leave_application_info' => null,
                        'updated_by_employee' => [
                            'employee_name' => $updated_by_employee->name,
                            'employee_name_translations' => $updated_by_employee->getTranslations('name'),
                            'attendance_recorded_at' => $period_attendance_student2->updated_at,
                        ],
                    ]
                ]
            ],
            [
                'student_id' => $student->id,
                'student_number' => $student->student_number,
                'student_name' => 'a',
                'student_name_translations' => $student->translations,
                'student_photo' => $student->photo,
                'seat_no' => 2,
                'school_check_in_datetime' => Carbon::parse($student_school_attendance->check_in_datetime)->toISOString(),
                'current_class' => $student->primaryClass->semesterClass->classModel->getFormattedTranslations('name'),
                'class_attendances' => [
                    $timeslot->period->period => [
                        'period' => $timeslot->period->period,
                        'timeslot_type' => Timeslot::class,
                        'timeslot_id' => $timeslot->id,
                        'class_attendance_status' => PeriodAttendanceStatus::PRESENT->value,
                        'is_editable' => false,
                        'is_default' => true,
                        'leave_application_id' => $approved_student_application->id,
                        'leave_application_info' => 'school competition (test)',
                        'updated_by_employee' => [],
                    ]
                ]
            ],
        ]
    ]);

    // student 1 - with leave_application
    // student 2, 3, 4 - no leave application
    $payload = [
        'date' => '2024-12-30',
        'class_attendances' => [
            [
                'timeslot_type' => Timeslot::class,
                'timeslot_id' => $timeslot->id,
                'period' => $timeslot->period->period,
                'students' => [
                    [
                        'student_id' => $student->id,
                        'class_attendance_status' => PeriodAttendanceStatus::PRESENT->value,
                    ],
                    [
                        'student_id' => $student2->id,
                        'class_attendance_status' => PeriodAttendanceStatus::ABSENT->value,
                    ],
                    [
                        'student_id' => $student3->id,
                        'class_attendance_status' => PeriodAttendanceStatus::PRESENT->value,
                    ],
                    [
                        'student_id' => $student4->id,
                        'class_attendance_status' => PeriodAttendanceStatus::LATE->value,
                    ],
                ],
            ]
        ]
    ];

    Carbon::setTestNow('2024-12-30 08:00:00'); // malaysia timezone 2024-12-30 08:00:00

    $summary = $this->attendanceService
        ->setPeriod($payload['class_attendances'][0]['period'])
        ->setEmployee($employee)
        ->setDate($payload['date'])
        ->bulkUpdateClassAttendance($payload['class_attendances'][0]['students']);

    expect($summary)->toBe([
        "header" => "第一节 (08:00 - 08:30)",
        "body" => [
            [
                "LATE" => 1,
            ],
            [
                "ABSENT" => 1,
            ],
            [
                "PRESENT" => 2,
            ],
        ],
        "footer" => [
            'TOTAL' => 4,
        ]
    ]);

    expect(PeriodAttendance::count())->toBe(4);
    $this->assertDatabaseHas(resolve(PeriodAttendance::class)->getTable(), [
        'id' => $period_attendance_student->id, // preserve id
        'student_id' => $student->id,
        'date' => $date,
        'timeslot_type' => Timeslot::class,
        'timeslot_id' => $timeslot->id,
        'updated_by_employee_id' => $employee->id,
        'attendance_taken_at' => '2024-12-30 08:00:00',
        'status' => PeriodAttendanceStatus::PRESENT->value,
        'leave_application_id' => $approved_student_application->id,
        'period' => $timeslot->period->period,
    ]);

    $this->assertDatabaseHas(resolve(PeriodAttendance::class)->getTable(), [
        'id' => $period_attendance_student2->id, // preserve id
        'student_id' => $student2->id,
        'date' => $date,
        'timeslot_type' => Timeslot::class,
        'timeslot_id' => $timeslot->id,
        'updated_by_employee_id' => $employee->id,
        'attendance_taken_at' => '2024-12-30 08:00:00',
        'status' => PeriodAttendanceStatus::ABSENT->value,
        'leave_application_id' => null,
        'period' => $timeslot->period->period,
    ]);
    $this->assertDatabaseHas(resolve(PeriodAttendance::class)->getTable(), [
        'id' => $period_attendance_student3->id, // preserve id
        'student_id' => $student3->id,
        'date' => $date,
        'timeslot_type' => Timeslot::class,
        'timeslot_id' => $timeslot->id,
        'updated_by_employee_id' => $employee->id,
        'attendance_taken_at' => '2024-12-30 08:00:00',
        'status' => PeriodAttendanceStatus::PRESENT->value,
        'leave_application_id' => null,
        'period' => $timeslot->period->period,
    ]);
    $this->assertDatabaseHas(resolve(PeriodAttendance::class)->getTable(), [
        'id' => $period_attendance_student4->id, // preserve id
        'student_id' => $student4->id,
        'date' => $date,
        'timeslot_type' => Timeslot::class,
        'timeslot_id' => $timeslot->id,
        'updated_by_employee_id' => $employee->id,
        'attendance_taken_at' => '2024-12-30 08:00:00',
        'status' => PeriodAttendanceStatus::LATE->value,
        'leave_application_id' => null,
        'period' => $timeslot->period->period,
    ]);
});

test('getClassAttendanceByTimeslot and bulkUpdateClassAttendance (continuous period)', function () {
    $employee = Employee::factory()->create();

    $semester_class = SemesterClass::factory()->create();
    $timetable = Timetable::factory()->create(['semester_class_id' => $semester_class->id]);

    $period_group = PeriodGroup::factory()->create();
    $period = Period::factory()->create([
        'period_group_id' => $period_group->id,
        'period' => 2,
    ]);
    $period_label = PeriodLabel::factory()->create([
        'period_group_id' => $period_group->id,
        'period' => 2,
        'name->en' => '第一节',
        'name->zh' => '第一节',
        'is_attendance_required' => true,
    ]);
    $period2 = Period::factory()->create([
        'period_group_id' => $period_group->id,
        'period' => 3
    ]);
    $period_label2 = PeriodLabel::factory()->create([
        'period_group_id' => $period_group->id,
        'period' => 3,
        'name->en' => '第二节',
        'name->zh' => '第二节',
        'is_attendance_required' => true,
    ]);
    $timeslot = Timeslot::factory()->create([
        'timetable_id' => $timetable->id,
        'day' => Day::MONDAY->value, // 2024-12-30 is Monday
        'period_id' => $period->id,
        'class_subject_id' => null,
        'placeholder' => '班务',
        'attendance_from' => '08:00:00',
        'attendance_to' => '08:30:00',
    ]);
    $timeslot2 = Timeslot::factory()->create([
        'timetable_id' => $timetable->id,
        'day' => Day::MONDAY->value, // 2024-12-30 is Monday
        'period_id' => $period2->id,
        'class_subject_id' => null,
        'placeholder' => '班务',
        'attendance_from' => '08:30:00',
        'attendance_to' => '09:00:00',
    ]);
    TimeslotTeacher::factory()->create([
        'employee_id' => $employee->id,
        'timeslot_id' => $timeslot->id,
    ]);
    TimeslotTeacher::factory()->create([
        'employee_id' => $employee->id,
        'timeslot_id' => $timeslot2->id,
    ]);

    $date = '2024-12-30';
    $student = Student::factory()->create([
        'name->en' => 'a'
    ]);
    $student2 = Student::factory()->create([
        'name->en' => 'b'
    ]);

    StudentClass::factory()->create([
        'semester_setting_id' => $semester_class->semester_setting_id,
        'semester_class_id' => $semester_class->id,
        'class_type' => ClassType::PRIMARY->value,
        'student_id' => $student->id,
        'seat_no' => 2,
    ]);
    StudentClass::factory()->create([
        'semester_setting_id' => $semester_class->semester_setting_id,
        'semester_class_id' => $semester_class->id,
        'class_type' => ClassType::PRIMARY->value,
        'student_id' => $student2->id,
        'seat_no' => 1,
    ]);

    // student 1
    $approved_student_application = LeaveApplication::factory()->create([
        'leave_applicable_type' => Student::class,
        'leave_applicable_id' => $student->id,
        'status' => LeaveApplicationStatus::APPROVED->value,
        'is_present' => true,
        'reason' => 'school competition',
        'remarks' => 'test',
    ]);

    LeaveApplicationPeriod::factory()->create([
        'leave_application_id' => $approved_student_application->id,
        'date' => $date,
        'period' => PeriodLabel::factory()->create([
            'period' => $timeslot->period->period
        ])->period,
    ]);

    PeriodAttendance::factory()->create([
        'student_id' => $student->id,
        'timeslot_type' => Timeslot::class,
        'timeslot_id' => $timeslot->id,
        'date' => $date,
        'status' => PeriodAttendanceStatus::PRESENT->value,
        'leave_application_id' => $approved_student_application->id,
        'period' => $timeslot->period->period,
    ]);
    PeriodAttendance::factory()->create([
        'student_id' => $student->id,
        'timeslot_type' => Timeslot::class,
        'timeslot_id' => $timeslot2->id,
        'date' => $date,
        'status' => PeriodAttendanceStatus::ABSENT->value,
        'leave_application_id' => null,
        'period' => $timeslot2->period->period,
    ]);

    $student_school_attendance = Attendance::factory()->create([
        'attendance_recordable_type' => Student::class,
        'attendance_recordable_id' => $student->id,
        'date' => $date,
        'check_in_datetime' => "{$date} 01:00:00",
        'check_in_status' => AttendanceCheckInStatus::ON_TIME->value,
        'check_in_remarks' => null,
        'check_in_overridable_type' => null,
        'check_in_overridable_id' => null,
        'attendance_from' => null,
        'check_out_datetime' => null,
        'check_out_status' => null,
        'check_out_remarks' => null,
        'check_out_overridable_type' => null,
        'check_out_overridable_id' => null,
        'attendance_to' => null,
        'status' => AttendanceStatus::PRESENT,
        'card_id' => null,
        'leave_application_id' => null,
        'is_error' => false,
        'error_message' => null,
    ]);

    // student 2
    $student2_period_attendance_period1 = PeriodAttendance::factory()->create([
        'student_id' => $student2->id,
        'timeslot_type' => Timeslot::class,
        'timeslot_id' => $timeslot->id,
        'date' => $date,
        'status' => PeriodAttendanceStatus::PRESENT->value,
        'leave_application_id' => null,
        'updated_by_employee_id' => $employee->id,
        'period' => $timeslot->period->period,
    ]);
    PeriodAttendance::factory()->create([
        'student_id' => $student2->id,
        'timeslot_type' => Timeslot::class,
        'timeslot_id' => $timeslot2->id,
        'date' => $date,
        'status' => PeriodAttendanceStatus::ABSENT->value,
        'leave_application_id' => null,
        'period' => $timeslot2->period->period,
    ]);

    LatestPrimaryClassBySemesterSettingView::refreshViewTable(false);

    $data = $this->attendanceService
        ->setTimeslotType(Timeslot::class)
        ->setTimeslotIds([$timeslot->id, $timeslot2->id])
        ->setDate($date)
        ->setEmployee($employee)
        ->getClassAttendanceByTimeslot();


    expect($data)->toMatchArray([
        "periods" => [
            $timeslot->period->period => [
                "period" => $timeslot->period->period,
                "label" => '第一节 (08:00 - 08:30)',
            ],
            $timeslot2->period->period => [
                "period" => $timeslot2->period->period,
                "label" => '第二节 (08:30 - 09:00)',
            ],
        ],
        "students" => [
            [
                'student_id' => $student2->id,
                'student_number' => $student2->student_number,
                'student_name' => 'b',
                'student_name_translations' => $student2->translations,
                'student_photo' => $student2->photo,
                'seat_no' => 1,
                'school_check_in_datetime' => null,
                'current_class' => $student2->primaryClass->semesterClass->classModel->getFormattedTranslations('name'),
                'class_attendances' => [
                    $timeslot->period->period => [
                        'period' => $timeslot->period->period,
                        'timeslot_type' => Timeslot::class,
                        'timeslot_id' => $timeslot->id,
                        'class_attendance_status' => PeriodAttendanceStatus::PRESENT->value,
                        'is_editable' => false,
                        'is_default' => false, // updated_by_employee_id not null
                        'leave_application_id' => null,
                        'leave_application_info' => null,
                        'updated_by_employee' => [
                            'employee_name' => $employee->name,
                            'employee_name_translations' => $employee->getTranslations('name'),
                            'attendance_recorded_at' => $student2_period_attendance_period1->updated_at,
                        ],
                    ],
                    $timeslot2->period->period => [
                        'period' => $timeslot2->period->period,
                        'timeslot_type' => Timeslot::class,
                        'timeslot_id' => $timeslot2->id,
                        'class_attendance_status' => PeriodAttendanceStatus::ABSENT->value,
                        'is_editable' => false,
                        'is_default' => true,
                        'leave_application_id' => null,
                        'leave_application_info' => null,
                        'updated_by_employee' => [],
                    ],
                ]
            ],
            [
                'student_id' => $student->id,
                'student_number' => $student->student_number,
                'student_name' => 'a',
                'student_name_translations' => $student->translations,
                'student_photo' => $student->photo,
                'seat_no' => 2,
                'school_check_in_datetime' => Carbon::parse($student_school_attendance->check_in_datetime)->toISOString(),
                'current_class' => $student->primaryClass->semesterClass->classModel->getFormattedTranslations('name'),
                'class_attendances' => [
                    $timeslot->period->period => [
                        'period' => $timeslot->period->period,
                        'timeslot_type' => Timeslot::class,
                        'timeslot_id' => $timeslot->id,
                        'class_attendance_status' => PeriodAttendanceStatus::PRESENT->value,
                        'is_editable' => false,
                        'is_default' => true,
                        'leave_application_id' => $approved_student_application->id,
                        'leave_application_info' => 'school competition (test)',
                        'updated_by_employee' => [],
                    ],
                    $timeslot2->period->period => [
                        'period' => $timeslot2->period->period,
                        'timeslot_type' => Timeslot::class,
                        'timeslot_id' => $timeslot2->id,
                        'class_attendance_status' => PeriodAttendanceStatus::ABSENT->value,
                        'is_editable' => false,
                        'is_default' => true,
                        'leave_application_id' => null,
                        'leave_application_info' => null,
                        'updated_by_employee' => [],
                    ],
                ]
            ],
        ]
    ]);

    // student 1 - with leave_application
    // student 2, 3, 4 - no leave application
    $payload = [
        'date' => '2024-12-30',
        'class_attendances' => [
            [
                'timeslot_type' => Timeslot::class,
                'timeslot_id' => $timeslot->id,
                'period' => $timeslot->period->period,
                'students' => [
                    [
                        'student_id' => $student->id,
                        'class_attendance_status' => PeriodAttendanceStatus::ABSENT->value,
                    ],
                    [
                        'student_id' => $student2->id,
                        'class_attendance_status' => PeriodAttendanceStatus::ABSENT->value,
                    ],
                ],
            ],
            [
                'timeslot_type' => Timeslot::class,
                'timeslot_id' => $timeslot2->id,
                'period' => $timeslot2->period->period,
                'students' => [
                    [
                        'student_id' => $student->id,
                        'class_attendance_status' => PeriodAttendanceStatus::PRESENT->value,
                    ],
                    [
                        'student_id' => $student2->id,
                        'class_attendance_status' => PeriodAttendanceStatus::PRESENT->value,
                    ],
                ],
            ]
        ]
    ];

    Carbon::setTestNow('2024-12-30 08:00:00'); // utc timezone 2024-12-30 08:00:00

    // timeslot 1
    $summary = $this->attendanceService
        ->setPeriod($payload['class_attendances'][0]['period'])
        ->setEmployee($employee)
        ->setDate($payload['date'])
        ->bulkUpdateClassAttendance($payload['class_attendances'][0]['students']);

    expect($summary)->toBe([
        "header" => "第一节 (08:00 - 08:30)",
        "body" => [
            [
                "LATE" => 0,
            ],
            [
                "ABSENT" => 2,
            ],
            [
                "PRESENT" => 0,
            ],
        ],
        "footer" => [
            'TOTAL' => 2,
        ]
    ]);

    // timeslot 2
    $summary = $this->attendanceService
        ->setPeriod($payload['class_attendances'][1]['period'])
        ->setEmployee($employee)
        ->setDate($payload['date'])
        ->bulkUpdateClassAttendance($payload['class_attendances'][1]['students']);

    expect($summary)->toBe([
        "header" => "第二节 (08:30 - 09:00)",
        "body" => [
            [
                "LATE" => 0,
            ],
            [
                "ABSENT" => 0,
            ],
            [
                "PRESENT" => 2,
            ],
        ],
        "footer" => [
            'TOTAL' => 2,
        ]
    ]);

    expect(PeriodAttendance::count())->toBe(4);
    $this->assertDatabaseHas(resolve(PeriodAttendance::class)->getTable(), [
        'student_id' => $student->id,
        'date' => $date,
        'timeslot_type' => Timeslot::class,
        'timeslot_id' => $timeslot->id,
        'updated_by_employee_id' => $employee->id,
        'attendance_taken_at' => '2024-12-30 08:00:00',
        'status' => PeriodAttendanceStatus::ABSENT->value,
        'leave_application_id' => $approved_student_application->id,
        'period' => $timeslot->period->period,
    ]);
    $this->assertDatabaseHas(resolve(PeriodAttendance::class)->getTable(), [
        'student_id' => $student2->id,
        'date' => $date,
        'timeslot_type' => Timeslot::class,
        'timeslot_id' => $timeslot->id,
        'updated_by_employee_id' => $employee->id,
        'attendance_taken_at' => '2024-12-30 08:00:00',
        'status' => PeriodAttendanceStatus::ABSENT->value,
        'leave_application_id' => null,
        'period' => $timeslot->period->period,
    ]);
    $this->assertDatabaseHas(resolve(PeriodAttendance::class)->getTable(), [
        'student_id' => $student->id,
        'date' => $date,
        'timeslot_type' => Timeslot::class,
        'timeslot_id' => $timeslot2->id,
        'updated_by_employee_id' => $employee->id,
        'attendance_taken_at' => '2024-12-30 08:00:00',
        'status' => PeriodAttendanceStatus::PRESENT->value,
        'leave_application_id' => null,
        'period' => $timeslot2->period->period,
    ]);
    $this->assertDatabaseHas(resolve(PeriodAttendance::class)->getTable(), [
        'student_id' => $student2->id,
        'date' => $date,
        'timeslot_type' => Timeslot::class,
        'timeslot_id' => $timeslot2->id,
        'updated_by_employee_id' => $employee->id,
        'attendance_taken_at' => '2024-12-30 08:00:00',
        'status' => PeriodAttendanceStatus::PRESENT->value,
        'leave_application_id' => null,
        'period' => $timeslot2->period->period,
    ]);
});

test('getClassAttendanceByTimeslot and bulkUpdateClassAttendance (timeslot override)', function () {
    $employee = Employee::factory()->create();
    $date = '2024-12-30';

    $student = Student::factory()->create([
        'name->en' => 'a'
    ]);
    $student2 = Student::factory()->create([
        'name->en' => 'c'
    ]);
    $student3 = Student::factory()->create([
        'name->en' => 'b'
    ]);
    $student4 = Student::factory()->create([
        'name->en' => 'd'
    ]);

    StudentClass::factory()->create([
        'class_type' => ClassType::PRIMARY->value,
        'student_id' => $student->id,
        'seat_no' => 1,
    ]);
    StudentClass::factory()->create([
        'class_type' => ClassType::PRIMARY->value,
        'student_id' => $student2->id,
        'seat_no' => 2,
    ]);
    StudentClass::factory()->create([
        'class_type' => ClassType::PRIMARY->value,
        'student_id' => $student3->id,
        'seat_no' => 3,
    ]);
    StudentClass::factory()->create([
        'class_type' => ClassType::PRIMARY->value,
        'student_id' => $student4->id,
        'seat_no' => 4,
    ]);

    $timeslot_override_period1_student = TimeslotOverride::factory()->create([
        'student_id' => $student->id,
        'date' => $date,
        'period' => 2,
        'attendance_from' => '07:00:00',
        'attendance_to' => '07:30:00',
        'employee_id' => $employee->id,
        'class_attendance_required' => true,
    ]);
    $timeslot_override_period1_student2 = TimeslotOverride::factory()->create([
        'student_id' => $student2->id,
        'date' => $date,
        'period' => 2,
        'attendance_from' => '07:00:00',
        'attendance_to' => '07:30:00',
        'employee_id' => $employee->id,
        'class_attendance_required' => true,
    ]);
    $timeslot_override_period1_student3 = TimeslotOverride::factory()->create([
        'student_id' => $student3->id,
        'date' => $date,
        'period' => 2,
        'attendance_from' => '07:00:00',
        'attendance_to' => '07:30:00',
        'employee_id' => $employee->id,
        'class_attendance_required' => true,
    ]);
    // period attendances, only student 1 has leave application
    $approved_student_application = LeaveApplication::factory()->create([
        'leave_applicable_type' => Student::class,
        'leave_applicable_id' => $student->id,
        'status' => LeaveApplicationStatus::APPROVED->value,
        'is_present' => true,
        'reason' => 'school competition',
        'remarks' => 'test',
    ]);
    LeaveApplicationPeriod::factory()->create([
        'leave_application_id' => $approved_student_application->id,
        'date' => $date,
        'period' => PeriodLabel::factory()->create([
            'period' => $timeslot_override_period1_student->period
        ])->period,
    ]);
    PeriodAttendance::factory()->create([
        'student_id' => $student->id,
        'timeslot_type' => TimeslotOverride::class,
        'timeslot_id' => $timeslot_override_period1_student->id,
        'date' => $date,
        'status' => PeriodAttendanceStatus::PRESENT->value,
        'leave_application_id' => $approved_student_application->id,
        'period' => $timeslot_override_period1_student->period,
    ]);
    PeriodAttendance::factory()->create([
        'student_id' => $student2->id,
        'timeslot_type' => TimeslotOverride::class,
        'timeslot_id' => $timeslot_override_period1_student2->id,
        'date' => $date,
        'status' => PeriodAttendanceStatus::ABSENT->value,
        'leave_application_id' => null,
        'period' => $timeslot_override_period1_student2->period,
    ]);
    PeriodAttendance::factory()->create([
        'student_id' => $student3->id,
        'timeslot_type' => TimeslotOverride::class,
        'timeslot_id' => $timeslot_override_period1_student3->id,
        'date' => $date,
        'status' => PeriodAttendanceStatus::ABSENT->value,
        'leave_application_id' => null,
        'period' => $timeslot_override_period1_student3->period,
    ]);

    $timeslot_override_period4_student4 = TimeslotOverride::factory()->create([
        'student_id' => $student4->id,
        'date' => $date,
        'period' => 4,
        'attendance_from' => '09:00:00',
        'attendance_to' => '09:30:00',
        'employee_id' => $employee->id,
        'class_attendance_required' => true,
    ]);
    PeriodAttendance::factory()->create([
        'student_id' => $student4->id,
        'timeslot_type' => TimeslotOverride::class,
        'timeslot_id' => $timeslot_override_period4_student4->id,
        'date' => $date,
        'status' => PeriodAttendanceStatus::ABSENT->value,
        'leave_application_id' => null,
        'period' => $timeslot_override_period4_student4->period,
    ]);

    $student_school_attendance = Attendance::factory()->create([
        'attendance_recordable_type' => Student::class,
        'attendance_recordable_id' => $student->id,
        'date' => $date,
        'check_in_datetime' => "{$date} 01:00:00",
        'check_in_status' => AttendanceCheckInStatus::ON_TIME->value,
        'check_in_remarks' => null,
        'check_in_overridable_type' => null,
        'check_in_overridable_id' => null,
        'attendance_from' => null,
        'check_out_datetime' => null,
        'check_out_status' => null,
        'check_out_remarks' => null,
        'check_out_overridable_type' => null,
        'check_out_overridable_id' => null,
        'attendance_to' => null,
        'status' => AttendanceStatus::PRESENT,
        'card_id' => null,
        'leave_application_id' => null,
        'is_error' => false,
        'error_message' => null,
    ]);

    DB::statement('REFRESH MATERIALIZED VIEW latest_primary_class_by_semester_setting_views');

    $data = $this->attendanceService
        ->setTimeslotType(TimeslotOverride::class)
        ->setTimeslotIds([$timeslot_override_period1_student->period]) // pass period instead of timeslot override id
        ->setEmployee($employee)
        ->setDate($date)
        ->getClassAttendanceByTimeslot();

    expect($data)->toMatchArray([
        "periods" => [
            $timeslot_override_period1_student->period => [
                "period" => $timeslot_override_period1_student->period,
                "label" => "Period {$timeslot_override_period1_student->period} (07:00 - 07:30)",
            ],
        ],
        "students" => [
            [
                'student_id' => $student->id,
                'student_number' => $student->student_number,
                'student_name' => 'a',
                'student_name_translations' => $student->translations,
                'student_photo' => $student->photo,
                'seat_no' => null, // timeslot override won't have seat_no
                'school_check_in_datetime' => Carbon::parse($student_school_attendance->check_in_datetime)->toISOString(),
                'current_class' => $student->primaryClass->semesterClass->classModel->getFormattedTranslations('name'),
                'class_attendances' => [
                    $timeslot_override_period1_student->period => [
                        'period' => $timeslot_override_period1_student->period,
                        'timeslot_type' => TimeslotOverride::class,
                        'timeslot_id' => $timeslot_override_period1_student->id,
                        'class_attendance_status' => PeriodAttendanceStatus::PRESENT->value,
                        'is_editable' => false,
                        'is_default' => true,
                        'leave_application_id' => $approved_student_application->id,
                        'leave_application_info' => 'school competition (test)',
                        'updated_by_employee' => [],
                    ],
                ]
            ],
            [
                'student_id' => $student3->id,
                'student_number' => $student3->student_number,
                'student_name' => 'b',
                'student_name_translations' => $student3->translations,
                'student_photo' => $student3->photo,
                'seat_no' => null, // timeslot override won't have seat_no
                'school_check_in_datetime' => null,
                'current_class' => $student3->primaryClass->semesterClass->classModel->getFormattedTranslations('name'),
                'class_attendances' => [
                    $timeslot_override_period1_student->period => [
                        'period' => $timeslot_override_period1_student->period,
                        'timeslot_type' => TimeslotOverride::class,
                        'timeslot_id' => $timeslot_override_period1_student3->id,
                        'class_attendance_status' => PeriodAttendanceStatus::ABSENT->value,
                        'is_editable' => false,
                        'is_default' => true,
                        'leave_application_id' => null,
                        'leave_application_info' => null,
                        'updated_by_employee' => [],
                    ],
                ]
            ],
            [
                'student_id' => $student2->id,
                'student_number' => $student2->student_number,
                'student_name' => 'c',
                'student_name_translations' => $student2->translations,
                'student_photo' => $student2->photo,
                'seat_no' => null, // timeslot override won't have seat_no
                'school_check_in_datetime' => null,
                'current_class' => $student2->primaryClass->semesterClass->classModel->getFormattedTranslations('name'),
                'class_attendances' => [
                    $timeslot_override_period1_student2->period => [
                        'period' => $timeslot_override_period1_student2->period,
                        'timeslot_type' => TimeslotOverride::class,
                        'timeslot_id' => $timeslot_override_period1_student2->id,
                        'class_attendance_status' => PeriodAttendanceStatus::ABSENT->value,
                        'is_editable' => false,
                        'is_default' => true,
                        'leave_application_id' => null,
                        'leave_application_info' => null,
                        'updated_by_employee' => [],
                    ],
                ]
            ],
        ]
    ]);

    $payload = [
        'date' => $date,
        'class_attendances' => [
            [
                'timeslot_type' => TimeslotOverride::class,
                'timeslot_id' => $timeslot_override_period1_student->period,
                'period' => $timeslot_override_period1_student->period,
                'students' => [
                    [
                        'student_id' => $student->id,
                        'class_attendance_status' => PeriodAttendanceStatus::ABSENT->value,
                    ],
                    [
                        'student_id' => $student2->id,
                        'class_attendance_status' => PeriodAttendanceStatus::ABSENT->value,
                    ],
                    [
                        'student_id' => $student3->id,
                        'class_attendance_status' => PeriodAttendanceStatus::PRESENT->value,
                    ],
                ],
            ],
        ]
    ];

    Carbon::setTestNow('2024-12-30 07:00:00'); // utc timezone 2024-12-30 07:00:00

    $summary = $this->attendanceService
        ->setPeriod($payload['class_attendances'][0]['period'])
        ->setEmployee($employee)
        ->setDate($payload['date'])
        ->bulkUpdateClassAttendance($payload['class_attendances'][0]['students']);

    expect($summary)->toBe([
        "header" => "Period {$timeslot_override_period1_student->period} (07:00 - 07:30)",
        "body" => [
            [
                "LATE" => 0,
            ],
            [
                "ABSENT" => 2,
            ],
            [
                "PRESENT" => 1,
            ],
        ],
        "footer" => [
            'TOTAL' => 3,
        ]
    ]);

    expect(PeriodAttendance::count())->toBe(4);
    $this->assertDatabaseHas(resolve(PeriodAttendance::class)->getTable(), [
        'student_id' => $student->id,
        'date' => $date,
        'timeslot_type' => TimeslotOverride::class,
        'timeslot_id' => $timeslot_override_period1_student->id,
        'updated_by_employee_id' => $employee->id,
        'attendance_taken_at' => '2024-12-30 07:00:00',
        'status' => PeriodAttendanceStatus::ABSENT->value,
        'leave_application_id' => $approved_student_application->id,
        'period' => $timeslot_override_period1_student->period,
    ]);
    $this->assertDatabaseHas(resolve(PeriodAttendance::class)->getTable(), [
        'student_id' => $student2->id,
        'date' => $date,
        'timeslot_type' => TimeslotOverride::class,
        'timeslot_id' => $timeslot_override_period1_student2->id,
        'updated_by_employee_id' => $employee->id,
        'attendance_taken_at' => '2024-12-30 07:00:00',
        'status' => PeriodAttendanceStatus::ABSENT->value,
        'leave_application_id' => null,
        'period' => $timeslot_override_period1_student2->period,
    ]);
    $this->assertDatabaseHas(resolve(PeriodAttendance::class)->getTable(), [
        'student_id' => $student3->id,
        'date' => $date,
        'timeslot_type' => TimeslotOverride::class,
        'timeslot_id' => $timeslot_override_period1_student3->id,
        'updated_by_employee_id' => $employee->id,
        'attendance_taken_at' => '2024-12-30 07:00:00',
        'status' => PeriodAttendanceStatus::PRESENT->value,
        'leave_application_id' => null,
        'period' => $timeslot_override_period1_student3->period,
    ]);
    // not updated
    $this->assertDatabaseHas(resolve(PeriodAttendance::class)->getTable(), [
        'student_id' => $student4->id,
        'date' => $date,
        'timeslot_type' => TimeslotOverride::class,
        'timeslot_id' => $timeslot_override_period4_student4->id,
        'updated_by_employee_id' => null,
        'status' => PeriodAttendanceStatus::ABSENT->value,
        'leave_application_id' => null,
        'period' => $timeslot_override_period4_student4->period,
    ]);
});

test('getClassAttendanceByTimeslot other class type', function () {
    $employee = Employee::factory()->create();

    $period_group = PeriodGroup::factory()->create();
    $period = Period::factory()->create([
        'period_group_id' => $period_group->id,
        'period' => 2,
        'day' => Day::MONDAY,
    ]);
    $period_label = PeriodLabel::factory()->create([
        'period_group_id' => $period_group->id,
        'period' => 2,
        'name->en' => '第一节',
        'name->zh' => '第一节',
        'is_attendance_required' => true,
    ]);

    $semester_class = SemesterClass::factory()->create();
    $timetable = Timetable::factory()->create(['semester_class_id' => $semester_class->id]);
    $timeslot = Timeslot::factory()->create([
        'timetable_id' => $timetable->id,
        'day' => Day::MONDAY->value, // 2024-12-30 is Monday
        'period_id' => $period->id,
        'attendance_from' => '07:00:00',
        'attendance_to' => '09:00:00',
    ]);

    TimeslotTeacher::factory()->create([
        'employee_id' => $employee->id,
        'timeslot_id' => $timeslot->id,
    ]);

    $date = '2025-04-11';
    $student = Student::factory()->create([
        'name->en' => 'a'
    ]);
    $student2 = Student::factory()->create([
        'name->en' => 'b'
    ]);
    $student3 = Student::factory()->create([
        'name->en' => 'c'
    ]);

    // only student 1 and 3 is_attendance_required = true
    $active_calendar = Calendar::factory()->create([
        'year' => '2025',
        'name->en' => 'calendar 2025',
        'name->zh' => '日历 2025',
        'is_default' => true,
        'is_active' => true,
    ]);
    $active_calendar_11_04_2025 = CalendarSetting::factory()->create([
        'calendar_id' => $active_calendar->id,
        'date' => $date,
        'is_attendance_required' => true,
        'description' => null,
    ]);
    CalendarTarget::factory()->create([
        'calendar_id' => $active_calendar->id,
        'priority' => 5,
        'calendar_targetable_type' => Student::class,
        'calendar_targetable_id' => $student->id,
    ]);
    CalendarTarget::factory()->create([
        'calendar_id' => $active_calendar->id,
        'priority' => 5,
        'calendar_targetable_type' => Student::class,
        'calendar_targetable_id' => $student3->id,
    ]);

    config(['school.timezone' => 'Asia/Kuala_Lumpur']);
    Carbon::setTestNow("{$date} 00:00:00");

    // english class (only 2 active)
    $english_class_student = StudentClass::factory()->create([
        'semester_setting_id' => $semester_class->semester_setting_id,
        'semester_class_id' => $semester_class->id,
        'class_type' => ClassType::ENGLISH->value,
        'student_id' => $student->id,
        'seat_no' => null,
        'is_active' => true,
    ]);
    $english_class_student2 = StudentClass::factory()->create([
        'semester_setting_id' => $semester_class->semester_setting_id,
        'semester_class_id' => $semester_class->id,
        'class_type' => ClassType::ENGLISH->value,
        'student_id' => $student2->id,
        'seat_no' => 1,
        'is_active' => true,
    ]);
    StudentClass::factory()->create([
        'semester_setting_id' => $semester_class->semester_setting_id,
        'semester_class_id' => $semester_class->id,
        'class_type' => ClassType::ENGLISH->value,
        'student_id' => $student3->id,
        'seat_no' => 1,
        'is_active' => false,
    ]);

    // primary class
    StudentClass::factory()->create([
        'class_type' => ClassType::PRIMARY->value,
        'student_id' => $student->id,
        'seat_no' => 1,
    ]);
    StudentClass::factory()->create([
        'class_type' => ClassType::PRIMARY->value,
        'student_id' => $student2->id,
        'seat_no' => 2,
    ]);
    StudentClass::factory()->create([
        'class_type' => ClassType::PRIMARY->value,
        'student_id' => $student3->id,
        'seat_no' => 3,
    ]);

    $attendance = Attendance::factory()->create([
        'attendance_recordable_type' => Student::class,
        'attendance_recordable_id' => $student->id,
        'date' => $date,
        'status' => AttendanceStatus::PRESENT->value,
    ]);

    PeriodAttendance::factory()->create([
        'student_id' => $student->id,
        'timeslot_id' => $timeslot->id,
        'date' => $date,
        'status' => PeriodAttendanceStatus::ABSENT->value,
        'updated_by_employee_id' => null,
        'period' => $timeslot->period->period,
    ]);

    PeriodAttendance::factory()->create([
        'student_id' => $student2->id,
        'timeslot_id' => $timeslot->id,
        'date' => $date,
        'status' => PeriodAttendanceStatus::ABSENT->value,
        'updated_by_employee_id' => null,
        'period' => $timeslot->period->period,
    ]);

    DB::statement('REFRESH MATERIALIZED VIEW latest_primary_class_by_semester_setting_views');

    $data = $this->attendanceService
        ->setTimeslotType(Timeslot::class)
        ->setTimeslotIds([$timeslot->id])
        ->setDate($date)
        ->setEmployee($employee)
        ->getClassAttendanceByTimeslot();

    expect($data)->toMatchArray([
        "periods" => [
            $timeslot->period->period => [
                "period" => $timeslot->period->period,
                "label" => "第一节 (07:00 - 09:00)",
            ],
        ],
        "students" => [
            [
                'student_id' => $student->id,
                'student_number' => $student->student_number,
                'student_name' => 'a',
                'student_name_translations' => $student->translations,
                'student_photo' => $student->photo,
                'seat_no' => $english_class_student->seat_no,
                'school_check_in_datetime' => Carbon::parse($attendance->check_in_datetime)->toISOString(),
                'current_class' => $student->primaryClass->semesterClass->classModel->getFormattedTranslations('name'),
                'class_attendances' => [
                    $timeslot->period->period => [
                        'period' => $timeslot->period->period,
                        'timeslot_type' => Timeslot::class,
                        'timeslot_id' => $timeslot->id,
                        'class_attendance_status' => PeriodAttendanceStatus::ABSENT->value,
                        'is_editable' => true,
                        'is_default' => true,
                        'leave_application_id' => null,
                        'leave_application_info' => null,
                        'updated_by_employee' => [],
                    ]
                ]
            ],
            [
                'student_id' => $student2->id,
                'student_number' => $student2->student_number,
                'student_name' => 'b',
                'student_name_translations' => $student2->translations,
                'student_photo' => $student2->photo,
                'seat_no' => $english_class_student2->seat_no,
                'school_check_in_datetime' => null,
                'current_class' => $student2->primaryClass->semesterClass->classModel->getFormattedTranslations('name'),
                'class_attendances' => [
                    $timeslot->period->period => [
                        'period' => $timeslot->period->period,
                        'timeslot_type' => Timeslot::class,
                        'timeslot_id' => $timeslot->id,
                        'class_attendance_status' => PeriodAttendanceStatus::ABSENT->value,
                        'is_editable' => false, // calendar not found, is_attendance_required = false
                        'is_default' => true,
                        'leave_application_id' => null,
                        'leave_application_info' => null,
                        'updated_by_employee' => [],
                    ]
                ]
            ],
        ]
    ]);
});

test('getClassAttendanceByTimeslot inactive student or inactive class', function () {
    $employee = Employee::factory()->create();

    $period_group = PeriodGroup::factory()->create();
    $period = Period::factory()->create([
        'period_group_id' => $period_group->id,
        'period' => 2,
        'day' => Day::MONDAY,
    ]);
    $period_label = PeriodLabel::factory()->create([
        'period_group_id' => $period_group->id,
        'period' => 2,
        'name->en' => '第一节',
        'name->zh' => '第一节',
        'is_attendance_required' => true,
    ]);

    $semester_class = SemesterClass::factory()->create();
    $timetable = Timetable::factory()->create(['semester_class_id' => $semester_class->id]);
    $timeslot = Timeslot::factory()->create([
        'timetable_id' => $timetable->id,
        'day' => Day::MONDAY->value, // 2024-12-30 is Monday
        'period_id' => $period->id,
        'attendance_from' => '07:00:00',
        'attendance_to' => '09:00:00',
    ]);
    TimeslotTeacher::factory()->create([
        'employee_id' => $employee->id,
        'timeslot_id' => $timeslot->id,
    ]);

    $date = '2025-04-11';
    $student = Student::factory()->create([
        'name->en' => 'a'
    ]);
    $student2 = Student::factory()->create([
        'name->en' => 'b'
    ]);
    $student3 = Student::factory()->create([
        'name->en' => 'c'
    ]);

    // only student 1 and 3 is_attendance_required = true
    $active_calendar = Calendar::factory()->create([
        'year' => '2025',
        'name->en' => 'calendar 2025',
        'name->zh' => '日历 2025',
        'is_default' => true,
        'is_active' => true,
    ]);
    $active_calendar_11_04_2025 = CalendarSetting::factory()->create([
        'calendar_id' => $active_calendar->id,
        'date' => $date,
        'is_attendance_required' => true,
        'description' => null,
    ]);
    CalendarTarget::factory()->create([
        'calendar_id' => $active_calendar->id,
        'priority' => 5,
        'calendar_targetable_type' => Student::class,
        'calendar_targetable_id' => $student->id,
    ]);
    CalendarTarget::factory()->create([
        'calendar_id' => $active_calendar->id,
        'priority' => 5,
        'calendar_targetable_type' => Student::class,
        'calendar_targetable_id' => $student2->id,
    ]);
    CalendarTarget::factory()->create([
        'calendar_id' => $active_calendar->id,
        'priority' => 5,
        'calendar_targetable_type' => Student::class,
        'calendar_targetable_id' => $student3->id,
    ]);

    config(['school.timezone' => 'Asia/Kuala_Lumpur']);
    Carbon::setTestNow("{$date} 00:00:00");

    // primary class
    $student_class_student = StudentClass::factory()->create([
        'semester_class_id' => $semester_class->id,
        'class_type' => ClassType::PRIMARY->value,
        'student_id' => $student->id,
        'seat_no' => 1,
    ]);
    $student_class_student2 = StudentClass::factory()->create([
        'semester_class_id' => $semester_class->id,
        'class_type' => ClassType::PRIMARY->value,
        'student_id' => $student2->id,
        'seat_no' => 2,
    ]);
    $student_class_student3 = StudentClass::factory()->create([
        'semester_class_id' => $semester_class->id,
        'class_type' => ClassType::PRIMARY->value,
        'student_id' => $student3->id,
        'seat_no' => 3,
    ]);

    $attendance_student = Attendance::factory()->create([
        'attendance_recordable_type' => Student::class,
        'attendance_recordable_id' => $student->id,
        'date' => $date,
        'status' => AttendanceStatus::PRESENT->value,
    ]);

    $attendance_student2 = Attendance::factory()->create([
        'attendance_recordable_type' => Student::class,
        'attendance_recordable_id' => $student2->id,
        'date' => $date,
        'status' => AttendanceStatus::PRESENT->value,
    ]);

    // absent
    $attendance_student3 = Attendance::factory()->create([
        'attendance_recordable_type' => Student::class,
        'attendance_recordable_id' => $student3->id,
        'date' => $date,
        'check_in_datetime' => null,
        'check_in_status' => null,
        'check_in_remarks' => null,
        'status' => AttendanceStatus::ABSENT->value,
    ]);

    PeriodAttendance::factory()->create([
        'student_id' => $student->id,
        'timeslot_id' => $timeslot->id,
        'date' => $date,
        'status' => PeriodAttendanceStatus::ABSENT->value,
        'updated_by_employee_id' => null,
        'period' => $timeslot->period->period,
    ]);

    PeriodAttendance::factory()->create([
        'student_id' => $student2->id,
        'timeslot_id' => $timeslot->id,
        'date' => $date,
        'status' => PeriodAttendanceStatus::ABSENT->value,
        'updated_by_employee_id' => null,
        'period' => $timeslot->period->period,
    ]);

    PeriodAttendance::factory()->create([
        'student_id' => $student3->id,
        'timeslot_id' => $timeslot->id,
        'date' => $date,
        'status' => PeriodAttendanceStatus::ABSENT->value,
        'updated_by_employee_id' => null,
        'period' => $timeslot->period->period,
    ]);

    // deactivate $student
    $student->update(['is_active' => false]);

    DB::statement('REFRESH MATERIALIZED VIEW latest_primary_class_by_semester_setting_views');

    $data = $this->attendanceService
        ->setTimeslotType(Timeslot::class)
        ->setTimeslotIds([$timeslot->id])
        ->setDate($date)
        ->setEmployee($employee)
        ->getClassAttendanceByTimeslot();

    expect($data)->toMatchArray([
        "periods" => [
            $timeslot->period->period => [
                "period" => $timeslot->period->period,
                "label" => "第一节 (07:00 - 09:00)",
            ],
        ],
        "students" => [
            [
                'student_id' => $student2->id,
                'student_number' => $student2->student_number,
                'student_name' => 'b',
                'student_name_translations' => $student2->translations,
                'student_photo' => $student2->photo,
                'seat_no' => 2,
                'school_check_in_datetime' => Carbon::parse($attendance_student2->check_in_datetime)->toISOString(),
                'current_class' => $student2->primaryClass->semesterClass->classModel->getFormattedTranslations('name'),
                'class_attendances' => [
                    $timeslot->period->period => [
                        'period' => $timeslot->period->period,
                        'timeslot_type' => Timeslot::class,
                        'timeslot_id' => $timeslot->id,
                        'class_attendance_status' => PeriodAttendanceStatus::ABSENT->value,
                        'is_editable' => true,
                        'is_default' => true,
                        'leave_application_id' => null,
                        'leave_application_info' => null,
                        'updated_by_employee' => [],
                    ]
                ]
            ],
            [
                'student_id' => $student3->id,
                'student_number' => $student3->student_number,
                'student_name' => 'c',
                'student_name_translations' => $student3->translations,
                'student_photo' => $student3->photo,
                'seat_no' => 3,
                'school_check_in_datetime' => null,
                'current_class' => $student3->primaryClass->semesterClass->classModel->getFormattedTranslations('name'),
                'class_attendances' => [
                    $timeslot->period->period => [
                        'period' => $timeslot->period->period,
                        'timeslot_type' => Timeslot::class,
                        'timeslot_id' => $timeslot->id,
                        'class_attendance_status' => PeriodAttendanceStatus::ABSENT->value,
                        'is_editable' => false, // absent school attendance cannot edit
                        'is_default' => true,
                        'leave_application_id' => null,
                        'leave_application_info' => null,
                        'updated_by_employee' => [],
                    ]
                ]
            ],
        ]
    ]);

    // deactivate $student_class_student2
    $student_class_student2->update(['is_active' => false]);

    DB::statement('REFRESH MATERIALIZED VIEW latest_primary_class_by_semester_setting_views');

    $data = $this->attendanceService
        ->setTimeslotType(Timeslot::class)
        ->setTimeslotIds([$timeslot->id])
        ->setDate($date)
        ->setEmployee($employee)
        ->getClassAttendanceByTimeslot();

    expect($data)->toMatchArray([
        "periods" => [
            $timeslot->period->period => [
                "period" => $timeslot->period->period,
                "label" => "第一节 (07:00 - 09:00)",
            ],
        ],
        "students" => [
            [
                'student_id' => $student3->id,
                'student_number' => $student3->student_number,
                'student_name' => 'c',
                'student_name_translations' => $student3->translations,
                'student_photo' => $student3->photo,
                'seat_no' => 3,
                'school_check_in_datetime' => null,
                'current_class' => $student3->primaryClass->semesterClass->classModel->getFormattedTranslations('name'),
                'class_attendances' => [
                    $timeslot->period->period => [
                        'period' => $timeslot->period->period,
                        'timeslot_type' => Timeslot::class,
                        'timeslot_id' => $timeslot->id,
                        'class_attendance_status' => PeriodAttendanceStatus::ABSENT->value,
                        'is_editable' => false, // absent school attendance cannot edit
                        'is_default' => true,
                        'leave_application_id' => null,
                        'leave_application_info' => null,
                        'updated_by_employee' => [],
                    ]
                ]
            ],
        ]
    ]);
});

test('getPeriodOptionIsDisabledStatus', function () {
    config(['school.timezone' => 'Asia/Kuala_Lumpur']);
    Carbon::setTestNow('2025-04-12 00:00:00'); // malaysia timezone 2025-04-12 08:00:00

    // date = 2025-04-11, attendance from = 08:00:00, expected is_disabled = false
    expect($this->attendanceService->getPeriodOptionIsDisabledStatus(Carbon::parse('2025-04-11 08:00:00', config('school.timezone'))))->toBeFalse();
    // date = 2025-04-11, attendance from = 09:00:00, expected is_disabled = false
    expect($this->attendanceService->getPeriodOptionIsDisabledStatus(Carbon::parse('2025-04-11 09:00:00', config('school.timezone'))))->toBeFalse();
    // date = 2025-04-12, attendance from = 08:00:00, expected is_disabled = false
    expect($this->attendanceService->getPeriodOptionIsDisabledStatus(Carbon::parse('2025-04-12 08:00:00', config('school.timezone'))))->toBeFalse();
    // date = 2025-04-12, attendance from = 09:00:00, expected is_disabled = true
    expect($this->attendanceService->getPeriodOptionIsDisabledStatus(Carbon::parse('2025-04-12 09:00:00', config('school.timezone'))))->toBeTrue();
    // date = 2025-04-12, attendance from = 08:05:00, expected is_disabled = false (5 mins earlier, class starts at 08:05 but visits class attendance page at 08:00)
    expect($this->attendanceService->getPeriodOptionIsDisabledStatus(Carbon::parse('2025-04-12 08:05:00', config('school.timezone'))))->toBeFalse();
    // date = 2025-04-12, attendance from = 08:06:00, expected is_disabled = true (6 mins earlier, class starts at 08:05 but visits class attendance page at 08:01)
    expect($this->attendanceService->getPeriodOptionIsDisabledStatus(Carbon::parse('2025-04-12 08:06:00', config('school.timezone'))))->toBeTrue();
});

test('getStudentClassAttendances', function () {
    $date = '2025-04-14';
    $date_sub_1_week = '2025-04-07';

    $student = Student::factory()->create();
    $period_group = PeriodGroup::factory()->create();
    $period = Period::factory()->create([
        'period_group_id' => $period_group->id,
        'period' => 1,
    ]);
    $period_label = PeriodLabel::factory()->create([
        'period_group_id' => $period_group->id,
        'period' => 1,
        'name->en' => '第一节',
        'name->zh' => '第一节',
        'is_attendance_required' => true,
    ]);
    $period2 = Period::factory()->create([
        'period_group_id' => $period_group->id,
        'period' => 2,
    ]);
    $period_label2 = PeriodLabel::factory()->create([
        'period_group_id' => $period_group->id,
        'period' => 2,
        'name->en' => '第二节',
        'name->zh' => '第二节',
        'is_attendance_required' => true,
    ]);

    $timeslot = Timeslot::factory()->create([
        'day' => Day::MONDAY->value,
        'period_id' => $period->id,
        'attendance_from' => '08:00:00',
        'attendance_to' => '08:30:00',
    ]);
    $timeslot2 = Timeslot::factory()->create([
        'day' => Day::MONDAY->value,
        'period_id' => $period2->id,
        'attendance_from' => '08:30:00',
        'attendance_to' => '09:00:00',
    ]);
    $period_attendance_student = PeriodAttendance::factory()->create([
        'student_id' => $student->id,
        'timeslot_type' => Timeslot::class,
        'timeslot_id' => $timeslot->id,
        'date' => $date,
        'status' => PeriodAttendanceStatus::PRESENT->value,
        'leave_application_id' => null,
        'period' => 1,
    ]);
    $period_attendance2_student = PeriodAttendance::factory()->create([
        'student_id' => $student->id,
        'timeslot_type' => Timeslot::class,
        'timeslot_id' => $timeslot2->id,
        'date' => $date,
        'status' => PeriodAttendanceStatus::PRESENT->value,
        'leave_application_id' => null,
        'period' => 2,
    ]);

    config(['school.timezone' => 'Asia/Kuala_Lumpur']);
    Carbon::setTestNow("{$date} 00:00:00"); // malaysia timezone 2025-04-14 08:00:00
    // not today = cannot edit
    // with leave application = cannot edit
    // school attendance absent = cannot edit
    // first period (first 班务) = cannot edit

    $this->attendanceService->setDate($date_sub_1_week);
    $data = $this->attendanceService->getStudentClassAttendances($student->periodAttendances, true, AttendanceStatus::PRESENT);
    // not today date
    expect($data)->toMatchArray([
        '1' => [
            'period' => 1,
            'timeslot_type' => Timeslot::class,
            'timeslot_id' => $timeslot->id,
            'class_attendance_status' => $period_attendance_student->status,
            'is_editable' => false, // not today date cannot edit
            'is_default' => true,
            'leave_application_id' => null,
            'leave_application_info' => null,
            'updated_by_employee' => [],
        ],
        '2' => [
            'period' => 2,
            'timeslot_type' => Timeslot::class,
            'timeslot_id' => $timeslot2->id,
            'class_attendance_status' => $period_attendance2_student->status,
            'is_editable' => false, // not today date cannot edit
            'is_default' => true,
            'leave_application_id' => null,
            'leave_application_info' => null,
            'updated_by_employee' => [],
        ]
    ]);

    $this->attendanceService->setDate($date);

    $data = $this->attendanceService->getStudentClassAttendances($student->periodAttendances, true, AttendanceStatus::ABSENT);
    // today date + absent school attendance
    expect($data)->toMatchArray([
        '1' => [
            'period' => 1,
            'timeslot_type' => Timeslot::class,
            'timeslot_id' => $timeslot->id,
            'class_attendance_status' => $period_attendance_student->status,
            'is_editable' => false, // first periodm cannot edit
            'is_default' => true,
            'leave_application_id' => null,
            'leave_application_info' => null,
            'updated_by_employee' => [],
        ],
        '2' => [
            'period' => 2,
            'timeslot_type' => Timeslot::class,
            'timeslot_id' => $timeslot2->id,
            'class_attendance_status' => $period_attendance2_student->status,
            'is_editable' => false, // absent school attendance cannot edit
            'is_default' => true,
            'leave_application_id' => null,
            'leave_application_info' => null,
            'updated_by_employee' => [],
        ]
    ]);

    $data = $this->attendanceService->getStudentClassAttendances($student->periodAttendances, true, AttendanceStatus::PRESENT);
    // today date
    expect($data)->toMatchArray([
        '1' => [
            'period' => 1,
            'timeslot_type' => Timeslot::class,
            'timeslot_id' => $timeslot->id,
            'class_attendance_status' => $period_attendance_student->status,
            'is_editable' => false, // first periodm cannot edit
            'is_default' => true,
            'leave_application_id' => null,
            'leave_application_info' => null,
            'updated_by_employee' => [],
        ],
        '2' => [
            'period' => 2,
            'timeslot_type' => Timeslot::class,
            'timeslot_id' => $timeslot2->id,
            'class_attendance_status' => $period_attendance2_student->status,
            'is_editable' => true,
            'is_default' => true,
            'leave_application_id' => null,
            'leave_application_info' => null,
            'updated_by_employee' => [],
        ]
    ]);

    $period_attendance2_student->update(['leave_application_id' => LeaveApplication::factory()->create()->id]);
    $student->refresh();
    $data = $this->attendanceService->getStudentClassAttendances($student->periodAttendances, true, AttendanceStatus::PRESENT);
    // today date + with leave application
    expect($data)->toMatchArray([
        '1' => [
            'period' => 1,
            'timeslot_type' => Timeslot::class,
            'timeslot_id' => $timeslot->id,
            'class_attendance_status' => $period_attendance_student->status,
            'is_editable' => false, // first periodm cannot edit
            'is_default' => true,
            'leave_application_id' => null,
            'leave_application_info' => null,
            'updated_by_employee' => [],
        ],
        '2' => [
            'period' => 2,
            'timeslot_type' => Timeslot::class,
            'timeslot_id' => $timeslot2->id,
            'class_attendance_status' => $period_attendance2_student->status,
            'is_editable' => false, // with leave application, cannot edit
            'is_default' => true,
            'leave_application_id' => $period_attendance2_student->leave_application_id,
            'leave_application_info' => $period_attendance2_student->leaveApplication->remarks ? $period_attendance2_student->leaveApplication->reason . " ({$period_attendance2_student->leaveApplication->remarks})" : $period_attendance2_student->leaveApplication->reason,
            'updated_by_employee' => [],
        ]
    ]);
});
