<?php

use App\Enums\AttendanceCheckInStatus;
use App\Enums\AttendanceCheckOutStatus;
use App\Enums\AttendanceStatus;
use App\Enums\ClassType;
use App\Enums\Day;
use App\Enums\LeaveApplicationStatus;
use App\Enums\PeriodAttendanceStatus;
use App\Models\Attendance;
use App\Models\ClassModel;
use App\Models\ClassSubject;
use App\Models\ClassSubjectContractor;
use App\Models\Contractor;
use App\Models\Employee;
use App\Models\LatestPrimaryClassBySemesterSettingView;
use App\Models\LeaveApplication;
use App\Models\LeaveApplicationPeriod;
use App\Models\LeaveApplicationType;
use App\Models\PeriodAttendance;
use App\Models\PeriodGroup;
use App\Models\SemesterClass;
use App\Models\SemesterSetting;
use App\Models\Student;
use App\Models\StudentClass;
use App\Models\Subject;
use App\Models\Timeslot;
use App\Models\Timetable;
use App\Repositories\AttendanceRepository;
use Database\Seeders\InternationalizationSeeder;
use Database\Seeders\PeriodSeeder;
use Illuminate\Database\Eloquent\Factories\Sequence;

beforeEach(function () {
    $this->seed([
        InternationalizationSeeder::class,
    ]);
    $this->attendanceRepository = resolve(AttendanceRepository::class);
    app()->setLocale('en');
    $this->table = resolve(Attendance::class)->getTable();
});

test('getModelClass()', function () {
    $model_class = $this->attendanceRepository->getModelClass();
    expect($model_class)->toEqual(Attendance::class);
});

test('getAllPaginated getAll', function (int $expected_count, array $filters, array $expected_models) {
    $student = Student::factory()->create();
    $student->id = 1;
    $student->save();

    $student_attendance_late = Attendance::factory()->create([
        'attendance_recordable_type' => Student::class,
        'attendance_recordable_id' => $student->id,
        'date' => '2024-12-01',
        'check_in_datetime' => '2024-12-01 08:00:00',
        'check_in_status' => AttendanceCheckInStatus::LATE->value,
        'check_out_datetime' => null,
        'check_out_status' => null,
        'status' => AttendanceStatus::PRESENT->value,
        'card_id' => null,
    ]);

    $student_attendance_on_time = Attendance::factory()->create([
        'attendance_recordable_type' => Student::class,
        'attendance_recordable_id' => Student::factory(),
        'date' => '2024-12-02',
        'check_in_datetime' => '2024-12-01 07:00:00',
        'check_in_status' => AttendanceCheckInStatus::ON_TIME->value,
        'check_out_datetime' => null,
        'check_out_status' => null,
        'status' => AttendanceStatus::PRESENT->value,
        'card_id' => null,
    ]);

    $student_attendance_absent = Attendance::factory()->create([
        'attendance_recordable_type' => Student::class,
        'attendance_recordable_id' => Student::factory(),
        'date' => '2024-12-02',
        'check_in_datetime' => null,
        'check_in_status' => null,
        'check_out_datetime' => null,
        'check_out_status' => null,
        'status' => AttendanceStatus::ABSENT->value,
        'card_id' => null,
    ]);


    $teacher_attendance_on_time = Attendance::factory()->create([
        'attendance_recordable_type' => Employee::class,
        'attendance_recordable_id' => Employee::factory(),
        'date' => '2024-12-03',
        'check_in_datetime' => '2024-12-01 07:00:00',
        'check_in_status' => AttendanceCheckInStatus::ON_TIME->value,
        'check_out_datetime' => '2024-12-01 15:00:00',
        'check_out_status' => AttendanceCheckOutStatus::ON_TIME->value,
        'status' => AttendanceStatus::PRESENT->value,
        'card_id' => null,
    ]);

    $semester_setting1 = SemesterSetting::factory()->create(['id' => 100]);
    $semester_setting1_semester_class = SemesterClass::factory()->create([
        'id' => 100,
        'semester_setting_id' => $semester_setting1->id,
    ]);
    $semester_setting1_semester_class2 = SemesterClass::factory()->create([
        'id' => 200,
        'semester_setting_id' => $semester_setting1->id,
    ]);

    $semester_setting2 = SemesterSetting::factory()->create(['id' => 200]);
    $semester_setting2_semester_class = SemesterClass::factory()->create([
        'id' => 300,
        'semester_setting_id' => $semester_setting2->id,
    ]);

    StudentClass::factory()->create([
        'semester_setting_id' => $semester_setting1->id,
        'semester_class_id' => $semester_setting1_semester_class->id,
        'student_id' => $student->id,
        'is_active' => true,
    ]);
    StudentClass::factory()->create([
        'semester_setting_id' => $semester_setting1->id,
        'semester_class_id' => $semester_setting1_semester_class2->id,
        'student_id' => $student_attendance_on_time->attendance_recordable_id,
        'is_active' => true,
    ]);
    StudentClass::factory()->create([
        'semester_setting_id' => $semester_setting2->id,
        'semester_class_id' => $semester_setting2_semester_class->id,
        'student_id' => $student_attendance_absent->attendance_recordable_id,
        'is_active' => true,
    ]);

    $attendances = [
        'student_attendance_late' => $student_attendance_late,
        'student_attendance_on_time' => $student_attendance_on_time,
        'student_attendance_absent' => $student_attendance_absent,
        'teacher_attendance_on_time' => $teacher_attendance_on_time,
    ];

    LatestPrimaryClassBySemesterSettingView::refreshViewTable(false);

    $filters['order_by']['id'] ??= 'asc';

    $result_paginated = $this->attendanceRepository->getAllPaginated($filters)->toArray();
    expect($result_paginated['data'])->toHaveCount($expected_count);
    foreach ($expected_models as $key => $model) {
        $expected_data = $attendances[$model]->toArray();
        expect($result_paginated['data'][$key])->toEqual($expected_data);
    }
    $result = $this->attendanceRepository->getAll($filters)->toArray();
    expect($result)->toHaveCount($expected_count);
    foreach ($expected_models as $key => $model) {
        $expected_data = $attendances[$model]->toArray();
        expect($result[$key])->toEqual($expected_data);
    }
})->with([
    'no filter' => [4, [], ['student_attendance_late', 'student_attendance_on_time', 'student_attendance_absent', 'teacher_attendance_on_time']],
    'filter by date' => [2, ['date' => '2024-12-02'], ['student_attendance_on_time', 'student_attendance_absent']],
    'filter by date_from' => [3, ['date_from' => '2024-12-02'], ['student_attendance_on_time', 'student_attendance_absent', 'teacher_attendance_on_time']],
    'filter by date_to' => [3, ['date_to' => '2024-12-02'], ['student_attendance_late', 'student_attendance_on_time', 'student_attendance_absent']],
    'filter by date_from date_to' => [2, ['date_from' => '2024-12-02', 'date_to' => '2024-12-02'], ['student_attendance_on_time', 'student_attendance_absent']],
    'filter by status' => [3, ['status' => AttendanceStatus::PRESENT], ['student_attendance_late', 'student_attendance_on_time', 'teacher_attendance_on_time']],
    'filter by check_in_status' => [1, ['check_in_status' => AttendanceCheckInStatus::LATE], ['student_attendance_late']],
    'filter by check_out_status' => [1, ['check_out_status' => AttendanceCheckOutStatus::ON_TIME], ['teacher_attendance_on_time']],
    'filter by attendance_recordable_type' => [3, ['attendance_recordable_type' => Student::class], ['student_attendance_late', 'student_attendance_on_time', 'student_attendance_absent']],
    'filter by attendance_recordable_type attendance_recordable_id' => [1, ['attendance_recordable_type' => Student::class, 'attendance_recordable_id' => 1], ['student_attendance_late']],
    'filter by semester_setting_id' => [2, ['semester_setting_id' => 100], ['student_attendance_late', 'student_attendance_on_time']],
    'filter by semester_class_id' => [1, ['semester_class_id' => 200], ['student_attendance_on_time']],
    'filter by latest_semester_setting_id' => [2, ['latest_semester_setting_id' => 100], ['student_attendance_late', 'student_attendance_on_time']],
    'filter by latest_semester_class_id' => [1, ['latest_semester_class_id' => 200], ['student_attendance_on_time']],
    'filter by semester_setting_id and semester_class_id' => [1, ['semester_setting_id' => 100, 'semester_class_id' => 200], ['student_attendance_on_time']],
    'sort by id asc' => [4, ['order_by' => ['id' => 'asc']], ['student_attendance_late', 'student_attendance_on_time', 'student_attendance_absent', 'teacher_attendance_on_time']],
    'sort by id desc' => [4, ['order_by' => ['id' => 'desc']], ['teacher_attendance_on_time', 'student_attendance_absent', 'student_attendance_on_time', 'student_attendance_late']],
]);

test('getAttendanceSummaryData()', function () {
    $semester_setting = SemesterSetting::factory()->create();

    $j11_class = ClassModel::factory()->create([
        'type' => ClassType::PRIMARY,
        'name->en' => 'J11',
    ]);
    $j22_class = ClassModel::factory()->create([
        'type' => ClassType::PRIMARY,
        'name->en' => 'J22',
    ]);

    $semester_classes = SemesterClass::factory(2)->state(new Sequence(
        [
            'semester_setting_id' => $semester_setting->id,
            'class_id' => $j11_class->id,
        ],
        [
            'semester_setting_id' => $semester_setting->id,
            'class_id' => $j22_class->id,
        ],
    ))->create();

    $students = Student::factory(4)->state(new Sequence(
        [
            'name->en' => 'Albert' // J11
        ],
        [
            'name->en' => 'Bob' // J11
        ],
        [
            'name->en' => 'Charlie', // J22
        ],
        [
            'name->en' => 'David', // J22
        ],
    ))->create();

    $student_classes = StudentClass::factory(4)->state(new Sequence(
        [
            'semester_setting_id' => $semester_setting->id,
            'semester_class_id' => $semester_classes[0]->id,
            'student_id' => $students[0]->id,
        ],
        [
            'semester_setting_id' => $semester_setting->id,
            'semester_class_id' => $semester_classes[0]->id,
            'student_id' => $students[1]->id,
        ],
        [
            'semester_setting_id' => $semester_setting->id,
            'semester_class_id' => $semester_classes[1]->id,
            'student_id' => $students[2]->id,
        ],
        [
            'semester_setting_id' => $semester_setting->id,
            'semester_class_id' => $semester_classes[1]->id,
            'student_id' => $students[3]->id,
        ],
    ))->create();

    LatestPrimaryClassBySemesterSettingView::refreshViewTable();


    $attendances = Attendance::factory(16)->state(new Sequence(
    /**
     * 2024-06-01
     */

    // 2024-06-01
        [
            'date' => '2024-06-01',
            'attendance_recordable_id' => $students[0]->id, // Albert J11 PRESENT
            'attendance_recordable_type' => Student::class,
            'check_in_status' => null,
            'status' => AttendanceStatus::PRESENT->value,
        ],
        // 2024-06-01
        [
            'date' => '2024-06-01',
            'attendance_recordable_id' => $students[1]->id, // Bob J11 ABSENT
            'attendance_recordable_type' => Student::class,
            'check_in_status' => null,
            'status' => AttendanceStatus::ABSENT->value,
        ],
        // 2024-06-01
        [
            'date' => '2024-06-01',
            'attendance_recordable_id' => $students[2]->id, // Charlie J22 LATE
            'attendance_recordable_type' => Student::class,
            'check_in_status' => AttendanceCheckInStatus::LATE->value,
            'status' => AttendanceStatus::PRESENT->value,
        ],
        // 2024-06-01
        [
            'date' => '2024-06-01',
            'attendance_recordable_id' => $students[3]->id, // David J22 PRESENT
            'attendance_recordable_type' => Student::class,
            'check_in_status' => AttendanceCheckInStatus::ON_TIME->value,
            'status' => AttendanceStatus::PRESENT->value,
        ],


        /**
         * 2024-06-02
         */

        // 2024-06-02
        [
            'date' => '2024-06-02',
            'attendance_recordable_id' => $students[0]->id, // Albert J11 PRESENT
            'attendance_recordable_type' => Student::class,
            'check_in_status' => null,
            'status' => AttendanceStatus::PRESENT->value,
        ],
        // 2024-06-02
        [
            'date' => '2024-06-02',
            'attendance_recordable_id' => $students[1]->id, // Bob J11 ABSENT
            'attendance_recordable_type' => Student::class,
            'check_in_status' => null,
            'status' => AttendanceStatus::ABSENT->value,
        ],
        // 2024-06-02
        [
            'date' => '2024-06-02',
            'attendance_recordable_id' => $students[2]->id, // Charlie J22 LATE
            'attendance_recordable_type' => Student::class,
            'check_in_status' => AttendanceCheckInStatus::LATE->value,
            'status' => AttendanceStatus::PRESENT->value,
        ],
        // 2024-06-02
        [
            'date' => '2024-06-02',
            'attendance_recordable_id' => $students[3]->id, // David J22 PRESENT
            'attendance_recordable_type' => Student::class,
            'check_in_status' => AttendanceCheckInStatus::ON_TIME->value,
            'status' => AttendanceStatus::PRESENT->value,
        ],

        /**
         * 2024-06-03
         */

        // 2024-06-03
        [
            'date' => '2024-06-03',
            'attendance_recordable_id' => $students[0]->id, // Albert J11 ABSENT
            'attendance_recordable_type' => Student::class,
            'check_in_status' => null,
            'status' => AttendanceStatus::ABSENT->value,
        ],
        // 2024-06-03
        [
            'date' => '2024-06-03',
            'attendance_recordable_id' => $students[1]->id, // Bob J11 ABSENT
            'attendance_recordable_type' => Student::class,
            'check_in_status' => null,
            'status' => AttendanceStatus::ABSENT->value,
        ],
        // 2024-06-03
        [
            'date' => '2024-06-03',
            'attendance_recordable_id' => $students[2]->id, // Charlie J22 LATE
            'attendance_recordable_type' => Student::class,
            'check_in_status' => AttendanceCheckInStatus::LATE->value,
            'status' => AttendanceStatus::PRESENT->value,
        ],
        // 2024-06-03
        [
            'date' => '2024-06-03',
            'attendance_recordable_id' => $students[3]->id, // David J22 LATE
            'attendance_recordable_type' => Student::class,
            'check_in_status' => AttendanceCheckInStatus::LATE->value,
            'status' => AttendanceStatus::PRESENT->value,
        ],


        /**
         * 2024-06-04
         */

        // 2024-06-04
        [
            'date' => '2024-06-04',
            'attendance_recordable_id' => $students[0]->id, // Albert J11 ABSENT
            'attendance_recordable_type' => Student::class,
            'check_in_status' => null,
            'status' => AttendanceStatus::ABSENT->value,
        ],
        // 2024-06-04
        [
            'date' => '2024-06-04',
            'attendance_recordable_id' => $students[1]->id, // Bob J11 ABSENT
            'attendance_recordable_type' => Student::class,
            'check_in_status' => null,
            'status' => AttendanceStatus::ABSENT->value,
        ],
        // 2024-06-04
        [
            'date' => '2024-06-04',
            'attendance_recordable_id' => $students[2]->id, // Charlie J22 LATE
            'attendance_recordable_type' => Student::class,
            'check_in_status' => AttendanceCheckInStatus::LATE->value,
            'status' => AttendanceStatus::PRESENT->value,
        ],
        // 2024-06-04
        [
            'date' => '2024-06-04',
            'attendance_recordable_id' => $students[3]->id, // David J22 LATE
            'attendance_recordable_type' => Student::class,
            'check_in_status' => AttendanceCheckInStatus::LATE->value,
            'status' => AttendanceStatus::PRESENT->value,
        ],
    ))->create();

    LatestPrimaryClassBySemesterSettingView::refreshViewTable(false);


    $result = $this->attendanceRepository->getAttendanceSummaryData([
        'semester_class_ids' => [
            $semester_classes[0]->id,
            $semester_classes[1]->id,
        ],
        'date_from' => '2024-06-01',
        'date_to' => '2024-06-04',
    ]);

    expect($result)->toHaveCount(4)
        ->toEqual([
            '2024-06-01' => [
                [
                    'date' => '2024-06-01',
                    'id' => $semester_classes[0]->class_id,
                    'name' => $semester_classes[0]->classModel->getTranslations('name'),
                    'records' => [
                        'LATE' => 0,
                        'PRESENT' => 1, // Albert
                        'ABSENT' => 1, // Bob
                    ],
                ],
                [
                    'date' => '2024-06-01',
                    'id' => $semester_classes[1]->class_id,
                    'name' => $semester_classes[1]->classModel->getTranslations('name'),
                    'records' => [
                        'LATE' => 1, // Charlie
                        'PRESENT' => 1, // David
                        'ABSENT' => 0,
                    ],
                ],
            ],
            '2024-06-02' => [
                [
                    'date' => '2024-06-02',
                    'id' => $semester_classes[0]->class_id,
                    'name' => $semester_classes[0]->classModel->getTranslations('name'),
                    'records' => [
                        'LATE' => 0,
                        'PRESENT' => 1, // Albert
                        'ABSENT' => 1, // Bob
                    ],
                ],
                [
                    'date' => '2024-06-02',
                    'id' => $semester_classes[1]->class_id,
                    'name' => $semester_classes[1]->classModel->getTranslations('name'),
                    'records' => [
                        'LATE' => 1, // Charlie
                        'PRESENT' => 1, // David
                        'ABSENT' => 0,
                    ],
                ],
            ],
            '2024-06-03' => [
                [
                    'date' => '2024-06-03',
                    'id' => $semester_classes[0]->class_id,
                    'name' => $semester_classes[0]->classModel->getTranslations('name'),
                    'records' => [
                        'LATE' => 0,
                        'PRESENT' => 0,
                        'ABSENT' => 2, // Albert, Bob
                    ],
                ],
                [
                    'date' => '2024-06-03',
                    'id' => $semester_classes[1]->class_id,
                    'name' => $semester_classes[1]->classModel->getTranslations('name'),
                    'records' => [
                        'LATE' => 2, // Charlie, David
                        'PRESENT' => 0,
                        'ABSENT' => 0,
                    ],
                ],
            ],
            '2024-06-04' => [
                [
                    'date' => '2024-06-04',
                    'id' => $semester_classes[0]->class_id,
                    'name' => $semester_classes[0]->classModel->getTranslations('name'),
                    'records' => [
                        'LATE' => 0,
                        'PRESENT' => 0,
                        'ABSENT' => 2, // Albert, Bob
                    ],
                ],
                [
                    'date' => '2024-06-04',
                    'id' => $semester_classes[1]->class_id,
                    'name' => $semester_classes[1]->classModel->getTranslations('name'),
                    'records' => [
                        'LATE' => 2, // Charlie, David
                        'PRESENT' => 0,
                        'ABSENT' => 0,
                    ],
                ],
            ],
        ]);
});

test('getClassAttendanceTakingData()', function () {
    $semester_setting = SemesterSetting::factory()->create();

    $j11_class = ClassModel::factory()->create([
        'type' => ClassType::PRIMARY,
        'name->en' => 'J11',
    ]);

    $semester_class = SemesterClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'class_id' => $j11_class->id,
    ]);

    $students = Student::factory(4)->state(new Sequence(
        [
            'name->en' => 'Albert' // J11
        ],
        [
            'name->en' => 'Bob' // J11
        ],
        [
            'name->en' => 'Charlie', // J11
        ],
        [
            'name->en' => 'David', // J11
        ],
    ))->create();

    $student_classes = StudentClass::factory(4)->state(new Sequence(
        [
            'semester_setting_id' => $semester_setting->id,
            'semester_class_id' => $semester_class->id,
            'student_id' => $students[0]->id,
        ],
        [
            'semester_setting_id' => $semester_setting->id,
            'semester_class_id' => $semester_class->id,
            'student_id' => $students[1]->id,
        ],
        [
            'semester_setting_id' => $semester_setting->id,
            'semester_class_id' => $semester_class->id,
            'student_id' => $students[2]->id,
        ],
        [
            'semester_setting_id' => $semester_setting->id,
            'semester_class_id' => $semester_class->id,
            'student_id' => $students[3]->id,
        ],
    ))->create();

    Attendance::factory(4)->state(new Sequence(
        [
            'attendance_recordable_id' => $students[0]->id,
            'status' => AttendanceStatus::PRESENT->value,
            'date' => '2025-04-10',
        ],
        [
            'attendance_recordable_id' => $students[1]->id,
            'status' => AttendanceStatus::PRESENT->value,
            'date' => '2025-04-10',
        ],
        [
            'attendance_recordable_id' => $students[2]->id,
            'status' => AttendanceStatus::PRESENT->value,
            'date' => '2025-04-10',
        ],
        [
            'attendance_recordable_id' => $students[3]->id,
            'status' => AttendanceStatus::ABSENT->value,
            'date' => '2025-04-10',
        ]
    ))->create();

    LatestPrimaryClassBySemesterSettingView::refreshViewTable();

    /**
     *
     * no TimeTable yet
     *
     */
    $result = $this->attendanceRepository->getClassAttendanceTakingData([
        'semester_class_id' => $semester_class->id,
        'date' => '2025-04-10', // THURSDAY
    ]);

    expect($result)->toEqual([
        'class' => null,
        'headers' => [],
        'attendance_data' => [],
    ]);


    /**
     *
     *  setup period attendances
     *
     */

    $this->seed([PeriodSeeder::class]);

    $period_group = PeriodGroup::first();


    // make period 10, 11, 12  dont require attendance, these periods will not appear in headers and attendance data
    $period_group->periodLabels()
        ->whereIn('period', [10, 11, 12])
        ->update(['is_attendance_required' => false]);


    $time_table = Timetable::factory()->create([
        'name' => 'J11 Time Table',
        'semester_class_id' => $semester_class->id,
        'period_group_id' => $period_group->id, // default period group
        'is_active' => true,
    ]);

    $timeslots = collect();

    $thursday_periods = $period_group->periods()->where('day', Day::THURSDAY)->orderBy('period', 'asc')->get();

    foreach ($thursday_periods as $period) {
        $time_slot = Timeslot::factory()->create([
            'timetable_id' => $time_table->id,
            'day' => Day::THURSDAY,
            'period_id' => $period->id,
            'class_subject_id' => null,
            'placeholder' => null,
            'attendance_from' => $period->from_time,
            'attendance_to' => $period->to_time,
        ]);

        $timeslots->push($time_slot);
    }


    $period_attendances = collect();

    // All student PRESENT at all period
    foreach ($timeslots as $timeslot) {

        /**
         * Albert
         */
        $period_attendance_1 = PeriodAttendance::factory()->create([
            'student_id' => $students[0]->id,
            'date' => '2025-04-10',
            'timeslot_id' => $timeslot->id,
            'status' => PeriodAttendanceStatus::PRESENT->value,
        ]);

        $period_attendances->push($period_attendance_1);


        /**
         * Bob
         */
        $period_attendance_2 = PeriodAttendance::factory()->create([
            'student_id' => $students[1]->id,
            'date' => '2025-04-10',
            'timeslot_id' => $timeslot->id,
            'status' => PeriodAttendanceStatus::PRESENT->value,
        ]);

        $period_attendances->push($period_attendance_2);


        /**
         * Charlie
         */
        $period_attendance_3 = PeriodAttendance::factory()->create([
            'student_id' => $students[2]->id,
            'date' => '2025-04-10',
            'timeslot_id' => $timeslot->id,
            'status' => PeriodAttendanceStatus::PRESENT->value,
        ]);

        $period_attendances->push($period_attendance_3);


        /**
         * David
         */
        $period_attendance_4 = PeriodAttendance::factory()->create([
            'student_id' => $students[3]->id,
            'date' => '2025-04-10',
            'timeslot_id' => $timeslot->id,
            'status' => PeriodAttendanceStatus::PRESENT->value,
        ]);

        $period_attendances->push($period_attendance_4);
    }


    $period_labels = $period_group->refresh()->periodLabels;


    /**
     *
     *  All student PRESENT at all period on THURSDAY
     *
     */
    $result = $this->attendanceRepository->getClassAttendanceTakingData([
        'semester_class_id' => $semester_class->id,
        'date' => '2025-04-10', // THURSDAY
    ]);

    expect($result['class']->toArray())->toEqual($semester_class->classModel->toArray());

    expect($result['headers']->toArray())->toEqual([
        [
            'from_time' => $thursday_periods[0]->from_time,
            'to_time' => $thursday_periods[0]->to_time,
            'period' => $thursday_periods[0]->period,
            'name' => $period_labels[0]->getTranslations('name'),
        ],
        [
            'from_time' => $thursday_periods[1]->from_time,
            'to_time' => $thursday_periods[1]->to_time,
            'period' => $thursday_periods[1]->period,
            'name' => $period_labels[1]->getTranslations('name'),
        ],
        [
            'from_time' => $thursday_periods[2]->from_time,
            'to_time' => $thursday_periods[2]->to_time,
            'period' => $thursday_periods[2]->period,
            'name' => $period_labels[2]->getTranslations('name'),
        ],
        [
            'from_time' => $thursday_periods[3]->from_time,
            'to_time' => $thursday_periods[3]->to_time,
            'period' => $thursday_periods[3]->period,
            'name' => $period_labels[3]->getTranslations('name'),
        ],
        [
            'from_time' => $thursday_periods[4]->from_time,
            'to_time' => $thursday_periods[4]->to_time,
            'period' => $thursday_periods[4]->period,
            'name' => $period_labels[4]->getTranslations('name'),
        ],
        [
            'from_time' => $thursday_periods[5]->from_time,
            'to_time' => $thursday_periods[5]->to_time,
            'period' => $thursday_periods[5]->period,
            'name' => $period_labels[5]->getTranslations('name'),
        ],
        [
            'from_time' => $thursday_periods[6]->from_time,
            'to_time' => $thursday_periods[6]->to_time,
            'period' => $thursday_periods[6]->period,
            'name' => $period_labels[6]->getTranslations('name'),
        ],
        [
            'from_time' => $thursday_periods[7]->from_time,
            'to_time' => $thursday_periods[7]->to_time,
            'period' => $thursday_periods[7]->period,
            'name' => $period_labels[7]->getTranslations('name'),
        ],
        [
            'from_time' => $thursday_periods[8]->from_time,
            'to_time' => $thursday_periods[8]->to_time,
            'period' => $thursday_periods[8]->period,
            'name' => $period_labels[8]->getTranslations('name'),
        ],
        // periods 10, 11, 12  are not required attendance, will not appear in headers
    ]);

    expect($result['attendance_data'])->toHaveCount(4)
        ->toEqual([
            [
                'student_primary_class' => $semester_class->classModel->name,
                'student_id' => $students[0]->id,
                'student_name' => $students[0]->getTranslations('name'),
                'student_number' => $students[0]->student_number,
                'school_attendance_status' => 'PRESENT',
                'period_attendances' => [
                    1 => PeriodAttendanceStatus::PRESENT->value,
                    2 => PeriodAttendanceStatus::PRESENT->value,
                    3 => PeriodAttendanceStatus::PRESENT->value,
                    4 => PeriodAttendanceStatus::PRESENT->value,
                    5 => PeriodAttendanceStatus::PRESENT->value,
                    6 => PeriodAttendanceStatus::PRESENT->value,
                    7 => PeriodAttendanceStatus::PRESENT->value,
                    8 => PeriodAttendanceStatus::PRESENT->value,
                    9 => PeriodAttendanceStatus::PRESENT->value,
                ],
            ],
            [
                'student_primary_class' => $semester_class->classModel->name,
                'student_id' => $students[1]->id,
                'student_name' => $students[1]->getTranslations('name'),
                'student_number' => $students[1]->student_number,
                'school_attendance_status' => 'PRESENT',
                'period_attendances' => [
                    1 => PeriodAttendanceStatus::PRESENT->value,
                    2 => PeriodAttendanceStatus::PRESENT->value,
                    3 => PeriodAttendanceStatus::PRESENT->value,
                    4 => PeriodAttendanceStatus::PRESENT->value,
                    5 => PeriodAttendanceStatus::PRESENT->value,
                    6 => PeriodAttendanceStatus::PRESENT->value,
                    7 => PeriodAttendanceStatus::PRESENT->value,
                    8 => PeriodAttendanceStatus::PRESENT->value,
                    9 => PeriodAttendanceStatus::PRESENT->value,
                ],
            ],
            [
                'student_primary_class' => $semester_class->classModel->name,
                'student_id' => $students[2]->id,
                'student_name' => $students[2]->getTranslations('name'),
                'student_number' => $students[2]->student_number,
                'school_attendance_status' => 'PRESENT',
                'period_attendances' => [
                    1 => PeriodAttendanceStatus::PRESENT->value,
                    2 => PeriodAttendanceStatus::PRESENT->value,
                    3 => PeriodAttendanceStatus::PRESENT->value,
                    4 => PeriodAttendanceStatus::PRESENT->value,
                    5 => PeriodAttendanceStatus::PRESENT->value,
                    6 => PeriodAttendanceStatus::PRESENT->value,
                    7 => PeriodAttendanceStatus::PRESENT->value,
                    8 => PeriodAttendanceStatus::PRESENT->value,
                    9 => PeriodAttendanceStatus::PRESENT->value,
                ],
            ],
            [
                'student_primary_class' => $semester_class->classModel->name,
                'student_id' => $students[3]->id,
                'student_name' => $students[3]->getTranslations('name'),
                'student_number' => $students[3]->student_number,
                'school_attendance_status' => 'ABSENT',
                'period_attendances' => [
                    1 => PeriodAttendanceStatus::PRESENT->value,
                    2 => PeriodAttendanceStatus::PRESENT->value,
                    3 => PeriodAttendanceStatus::PRESENT->value,
                    4 => PeriodAttendanceStatus::PRESENT->value,
                    5 => PeriodAttendanceStatus::PRESENT->value,
                    6 => PeriodAttendanceStatus::PRESENT->value,
                    7 => PeriodAttendanceStatus::PRESENT->value,
                    8 => PeriodAttendanceStatus::PRESENT->value,
                    9 => PeriodAttendanceStatus::PRESENT->value,
                ],
            ],
        ]);


    /**
     *
     * All student LATE at all period on THURSDAY
     *
     */

    $period_attendances->each(function ($period_attendance) {
        $period_attendance->update([
            'status' => PeriodAttendanceStatus::LATE->value,
        ]);
    });

    $result = $this->attendanceRepository->getClassAttendanceTakingData([
        'semester_class_id' => $semester_class->id,
        'date' => '2025-04-10', // THURSDAY
    ]);

    expect($result['attendance_data'])->toHaveCount(4)
        ->toEqual([
            [
                'student_primary_class' => $semester_class->classModel->name,
                'student_id' => $students[0]->id,
                'student_name' => $students[0]->getTranslations('name'),
                'student_number' => $students[0]->student_number,
                'school_attendance_status' => 'PRESENT',
                'period_attendances' => [
                    1 => PeriodAttendanceStatus::LATE->value,
                    2 => PeriodAttendanceStatus::LATE->value,
                    3 => PeriodAttendanceStatus::LATE->value,
                    4 => PeriodAttendanceStatus::LATE->value,
                    5 => PeriodAttendanceStatus::LATE->value,
                    6 => PeriodAttendanceStatus::LATE->value,
                    7 => PeriodAttendanceStatus::LATE->value,
                    8 => PeriodAttendanceStatus::LATE->value,
                    9 => PeriodAttendanceStatus::LATE->value,
                ],
            ],
            [
                'student_primary_class' => $semester_class->classModel->name,
                'student_id' => $students[1]->id,
                'student_name' => $students[1]->getTranslations('name'),
                'student_number' => $students[1]->student_number,
                'school_attendance_status' => 'PRESENT',
                'period_attendances' => [
                    1 => PeriodAttendanceStatus::LATE->value,
                    2 => PeriodAttendanceStatus::LATE->value,
                    3 => PeriodAttendanceStatus::LATE->value,
                    4 => PeriodAttendanceStatus::LATE->value,
                    5 => PeriodAttendanceStatus::LATE->value,
                    6 => PeriodAttendanceStatus::LATE->value,
                    7 => PeriodAttendanceStatus::LATE->value,
                    8 => PeriodAttendanceStatus::LATE->value,
                    9 => PeriodAttendanceStatus::LATE->value,
                ],
            ],
            [
                'student_primary_class' => $semester_class->classModel->name,
                'student_id' => $students[2]->id,
                'student_name' => $students[2]->getTranslations('name'),
                'student_number' => $students[2]->student_number,
                'school_attendance_status' => 'PRESENT',
                'period_attendances' => [
                    1 => PeriodAttendanceStatus::LATE->value,
                    2 => PeriodAttendanceStatus::LATE->value,
                    3 => PeriodAttendanceStatus::LATE->value,
                    4 => PeriodAttendanceStatus::LATE->value,
                    5 => PeriodAttendanceStatus::LATE->value,
                    6 => PeriodAttendanceStatus::LATE->value,
                    7 => PeriodAttendanceStatus::LATE->value,
                    8 => PeriodAttendanceStatus::LATE->value,
                    9 => PeriodAttendanceStatus::LATE->value,
                ],
            ],
            [
                'student_primary_class' => $semester_class->classModel->name,
                'student_id' => $students[3]->id,
                'student_name' => $students[3]->getTranslations('name'),
                'student_number' => $students[3]->student_number,
                'school_attendance_status' => 'ABSENT',
                'period_attendances' => [
                    1 => PeriodAttendanceStatus::LATE->value,
                    2 => PeriodAttendanceStatus::LATE->value,
                    3 => PeriodAttendanceStatus::LATE->value,
                    4 => PeriodAttendanceStatus::LATE->value,
                    5 => PeriodAttendanceStatus::LATE->value,
                    6 => PeriodAttendanceStatus::LATE->value,
                    7 => PeriodAttendanceStatus::LATE->value,
                    8 => PeriodAttendanceStatus::LATE->value,
                    9 => PeriodAttendanceStatus::LATE->value,
                ],
            ],
        ]);


    /**
     * only Albert absent on all period on THURSDAY
     */

    $period_attendances
        ->where('student_id', $students[0]->id)
        ->each(function ($period_attendance) {
            $period_attendance->update([
                'status' => PeriodAttendanceStatus::ABSENT->value,
            ]);
        });


    $result = $this->attendanceRepository->getClassAttendanceTakingData([
        'semester_class_id' => $semester_class->id,
        'date' => '2025-04-10', // THURSDAY
    ]);

    expect($result['attendance_data'])->toHaveCount(4)
        ->toEqual([
            [
                'student_primary_class' => $semester_class->classModel->name,
                'student_id' => $students[0]->id, // Albert
                'student_name' => $students[0]->getTranslations('name'),
                'student_number' => $students[0]->student_number,
                'school_attendance_status' => 'PRESENT',
                'period_attendances' => [
                    1 => PeriodAttendanceStatus::ABSENT->value,
                    2 => PeriodAttendanceStatus::ABSENT->value,
                    3 => PeriodAttendanceStatus::ABSENT->value,
                    4 => PeriodAttendanceStatus::ABSENT->value,
                    5 => PeriodAttendanceStatus::ABSENT->value,
                    6 => PeriodAttendanceStatus::ABSENT->value,
                    7 => PeriodAttendanceStatus::ABSENT->value,
                    8 => PeriodAttendanceStatus::ABSENT->value,
                    9 => PeriodAttendanceStatus::ABSENT->value,
                ],
            ],
            [
                'student_primary_class' => $semester_class->classModel->name,
                'student_id' => $students[1]->id,
                'student_name' => $students[1]->getTranslations('name'),
                'student_number' => $students[1]->student_number,
                'school_attendance_status' => 'PRESENT',
                'period_attendances' => [
                    1 => PeriodAttendanceStatus::LATE->value,
                    2 => PeriodAttendanceStatus::LATE->value,
                    3 => PeriodAttendanceStatus::LATE->value,
                    4 => PeriodAttendanceStatus::LATE->value,
                    5 => PeriodAttendanceStatus::LATE->value,
                    6 => PeriodAttendanceStatus::LATE->value,
                    7 => PeriodAttendanceStatus::LATE->value,
                    8 => PeriodAttendanceStatus::LATE->value,
                    9 => PeriodAttendanceStatus::LATE->value,
                ],
            ],
            [
                'student_primary_class' => $semester_class->classModel->name,
                'student_id' => $students[2]->id,
                'student_name' => $students[2]->getTranslations('name'),
                'student_number' => $students[2]->student_number,
                'school_attendance_status' => 'PRESENT',
                'period_attendances' => [
                    1 => PeriodAttendanceStatus::LATE->value,
                    2 => PeriodAttendanceStatus::LATE->value,
                    3 => PeriodAttendanceStatus::LATE->value,
                    4 => PeriodAttendanceStatus::LATE->value,
                    5 => PeriodAttendanceStatus::LATE->value,
                    6 => PeriodAttendanceStatus::LATE->value,
                    7 => PeriodAttendanceStatus::LATE->value,
                    8 => PeriodAttendanceStatus::LATE->value,
                    9 => PeriodAttendanceStatus::LATE->value,
                ],
            ],
            [
                'student_primary_class' => $semester_class->classModel->name,
                'student_id' => $students[3]->id,
                'student_name' => $students[3]->getTranslations('name'),
                'student_number' => $students[3]->student_number,
                'school_attendance_status' => 'ABSENT',
                'period_attendances' => [
                    1 => PeriodAttendanceStatus::LATE->value,
                    2 => PeriodAttendanceStatus::LATE->value,
                    3 => PeriodAttendanceStatus::LATE->value,
                    4 => PeriodAttendanceStatus::LATE->value,
                    5 => PeriodAttendanceStatus::LATE->value,
                    6 => PeriodAttendanceStatus::LATE->value,
                    7 => PeriodAttendanceStatus::LATE->value,
                    8 => PeriodAttendanceStatus::LATE->value,
                    9 => PeriodAttendanceStatus::LATE->value,
                ],
            ],
        ]);
});

test('getAllForContractorsDailyAttendanceReport', function (int $expected_count, $language, array $filters, array $expected_data) {

    $contractor1 = Contractor::factory()->create([
        'name' => ['en' => 'Alan', 'zh' => '阿伦'],
        'contractor_number' => '120',
    ]);
    $contractor2 = Contractor::factory()->create([
        'name' => ['en' => 'Zapp', 'zh' => '德考尼斯'],
        'contractor_number' => '121',
    ]);
    $contractor3 = Contractor::factory()->create([
        'name' => ['en' => 'Good', 'zh' => '狗哥'],
        'contractor_number' => '122',
    ]);

    //2025-04-13
    $attendance1 = Attendance::factory()->create([
        'attendance_recordable_type' => Contractor::class,
        'attendance_recordable_id' => $contractor1->id,
        'date' => '2025-04-13',
        'check_in_datetime' => '2025-04-13 02:00:00',
        'check_in_status' => AttendanceCheckInStatus::ON_TIME->value,
        'check_out_datetime' => null,
        'check_out_status' => null,
        'status' => AttendanceStatus::PRESENT->value,
        'card_id' => 220,
    ]);

    $attendance2 = Attendance::factory()->create([
        'attendance_recordable_type' => Contractor::class,
        'attendance_recordable_id' => $contractor2->id,
        'date' => '2025-04-13',
        'check_in_datetime' => '2025-04-13 01:00:00',
        'check_in_status' => AttendanceCheckInStatus::ON_TIME->value,
        'check_out_datetime' => '2025-04-13 04:00:00',
        'check_out_status' => AttendanceCheckInStatus::ON_TIME->value,
        'status' => AttendanceStatus::PRESENT->value,
        'card_id' => 230,
    ]);

    //2025-04-15
    Attendance::factory()->create([
        'attendance_recordable_type' => Contractor::class,
        'attendance_recordable_id' => $contractor1->id,
        'date' => '2025-04-15',
        'check_in_datetime' => '2025-04-15 04:00:00',
        'check_in_status' => AttendanceCheckInStatus::ON_TIME->value,
        'check_out_datetime' => null,
        'check_out_status' => null,
        'status' => AttendanceStatus::PRESENT->value,
        'card_id' => 220,
    ]);
    Attendance::factory()->create([
        'attendance_recordable_type' => Contractor::class,
        'attendance_recordable_id' => $contractor3->id,
        'date' => '2025-04-15',
        'check_in_datetime' => '2025-04-15 01:30:00',
        'check_in_status' => AttendanceCheckInStatus::ON_TIME->value,
        'check_out_datetime' => null,
        'check_out_status' => null,
        'status' => AttendanceStatus::PRESENT->value,
        'card_id' => 240,
    ]);

    $semester_setting1 = SemesterSetting::factory()->create([
        'id' => 100,
    ]);
    $subject = Subject::factory()->create(['code' => 'COCURRICULUM']);

    $classes = ClassModel::factory(3)->state(new Sequence(
        [
            'name' => ['en' => 'Cocu Class 1', 'zh' => 'Cocu Class 1A'],
            'type' => ClassType::SOCIETY->value,
        ],
        [
            'name' => ['en' => 'Cocu Class 2', 'zh' => 'Cocu Class 2A'],
            'type' => ClassType::SOCIETY->value,
        ],
        [
            'name' => ['en' => 'Cocu Class 3', 'zh' => 'Cocu Class 3A'],
            'type' => ClassType::SOCIETY->value,
        ],
    ))->create();

    $class_subjects = [];

    foreach ($classes as $class) {
        $semester_class = SemesterClass::factory()->create([
            'semester_setting_id' => $semester_setting1->id,
            'class_id' => $class->id,
        ]);

        $class_subjects[] = ClassSubject::factory()->create([
            'semester_class_id' => $semester_class->id,
            'subject_id' => $subject->id,
        ]);
    }

    foreach ([$contractor1, $contractor2, $contractor3] as $index => $contractor) {
        ClassSubjectContractor::factory()->create([
            'contractor_id' => $contractor->id,
            'class_subject_id' => $class_subjects[$index]->id,
        ]);
    }

    app()->setLocale($language);

    $results = $this->attendanceRepository->getAllForContractorsDailyAttendanceReport($filters)->toArray();

    expect($results)->toHaveCount($expected_count)
        ->and($results)->tomatchArray($expected_data);


})->with([
    'filter by date 2025-04-13 attend_only' => [
        2, 'en', ['date' => '2025-04-13', 'report_language' => 'en', 'type' => 'attend_only', 'semester_setting_id' => 100], [
            [
                'contractor_number' => 120,
                'contractor_name' => 'Alan',
                'class_name' => 'Cocu Class 1',
                'check_in_datetime' => '2025-04-13T02:00:00.000000Z',
                'check_out_datetime' => null,
                'attendance_status' => AttendanceStatus::PRESENT->value,
                'card_id' => 220,
            ],
            [
                'contractor_number' => 121,
                'contractor_name' => 'Zapp',
                'class_name' => 'Cocu Class 2',
                'check_in_datetime' => '2025-04-13T01:00:00.000000Z',
                'check_out_datetime' => '2025-04-13T04:00:00.000000Z',
                'attendance_status' => AttendanceStatus::PRESENT->value,
                'card_id' => 230,
            ]
        ]
    ],
    'filter by date 2025-04-15 attend_only' => [
        2, 'en', ['date' => '2025-04-15', 'report_language' => 'en', 'type' => 'attend_only', 'semester_setting_id' => 100], [
            [
                'contractor_number' => 120,
                'contractor_name' => 'Alan',
                'class_name' => 'Cocu Class 1',
                'check_in_datetime' => '2025-04-15T04:00:00.000000Z',
                'check_out_datetime' => null,
                'attendance_status' => AttendanceStatus::PRESENT->value,
                'card_id' => 220,
            ],
            [
                'contractor_number' => 122,
                'contractor_name' => 'Good',
                'class_name' => 'Cocu Class 3',
                'check_in_datetime' => '2025-04-15T01:30:00.000000Z',
                'check_out_datetime' => null,
                'attendance_status' => AttendanceStatus::PRESENT->value,
                'card_id' => 240,
            ]
        ]
    ],
    'filter by date 2025-04-13 all' => [
        3, 'en', ['date' => '2025-04-13', 'report_language' => 'en', 'type' => 'all', 'semester_setting_id' => 100], [
            [
                'contractor_number' => 120,
                'contractor_name' => 'Alan',
                'class_name' => 'Cocu Class 1',
                'check_in_datetime' => '2025-04-13 02:00:00',
                'check_out_datetime' => null,
                'attendance_status' => AttendanceStatus::PRESENT->value,
                'card_id' => 220,
            ],
            [
                'contractor_number' => 122,
                'contractor_name' => 'Good',
                'class_name' => 'Cocu Class 3',
                'check_in_datetime' => null,
                'check_out_datetime' => null,
                'attendance_status' => AttendanceStatus::ABSENT->value,
                'card_id' => null,
            ],
            [
                'contractor_number' => 121,
                'contractor_name' => 'Zapp',
                'class_name' => 'Cocu Class 2',
                'check_in_datetime' => '2025-04-13 01:00:00',
                'check_out_datetime' => '2025-04-13 04:00:00',
                'attendance_status' => AttendanceStatus::PRESENT->value,
                'card_id' => 230,
            ],
        ]
    ],
    'filter by date 2025-04-15 all zh' => [
        3, 'zh', ['date' => '2025-04-15', 'report_language' => 'en', 'type' => 'all', 'semester_setting_id' => 100], [
            [
                'contractor_number' => 120,
                'contractor_name' => '阿伦',
                'class_name' => 'Cocu Class 1A',
                'check_in_datetime' => '2025-04-15 04:00:00',
                'check_out_datetime' => null,
                'attendance_status' => AttendanceStatus::PRESENT->value,
                'card_id' => 220,
            ],
            [
                'contractor_number' => 121,
                'contractor_name' => '德考尼斯',
                'class_name' => 'Cocu Class 2A',
                'check_in_datetime' => null,
                'check_out_datetime' => null,
                'attendance_status' => AttendanceStatus::ABSENT->value,
                'card_id' => null,
            ],
            [
                'contractor_number' => 122,
                'contractor_name' => '狗哥',
                'class_name' => 'Cocu Class 3A',
                'check_in_datetime' => '2025-04-15 01:30:00',
                'check_out_datetime' => null,
                'attendance_status' => AttendanceStatus::PRESENT->value,
                'card_id' => 240,
            ],
        ]
    ],
]);


test('getStudentAttendanceMarkDeductionData()', function () {
    app()->setLocale('en');

    $semester_setting = SemesterSetting::factory()->create();

    $j11_class = ClassModel::factory()->create([
        'type' => ClassType::PRIMARY,
        'name->en' => 'J11',
    ]);

    $j22_class = ClassModel::factory()->create([
        'type' => ClassType::PRIMARY,
        'name->en' => 'J22',
    ]);

    $semester_class_1 = SemesterClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'class_id' => $j11_class->id,
    ]);

    $semester_class_2 = SemesterClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'class_id' => $j22_class->id,
    ]);

    $students = Student::factory(5)->state(new Sequence(
        [
            'name->en' => 'Albert' // J11
        ],
        [
            'name->en' => 'Bob' // J11
        ],
        [
            'name->en' => 'Charlie' // J22
        ],
        [
            'name->en' => 'David' // J22
        ],
        [
            'name->en' => 'Inactive Student',
            'is_active' => false,
        ],
    ))->create();

    $student_classes = StudentClass::factory(5)->state(new Sequence(
        [
            'semester_setting_id' => $semester_setting->id,
            'semester_class_id' => $semester_class_1->id,
            'student_id' => $students[0]->id,
        ],
        [
            'semester_setting_id' => $semester_setting->id,
            'semester_class_id' => $semester_class_1->id,
            'student_id' => $students[1]->id,
        ],
        [
            'semester_setting_id' => $semester_setting->id,
            'semester_class_id' => $semester_class_2->id,
            'student_id' => $students[2]->id,
        ],
        [
            'semester_setting_id' => $semester_setting->id,
            'semester_class_id' => $semester_class_2->id,
            'student_id' => $students[3]->id,
        ],
        [
            'semester_setting_id' => $semester_setting->id,
            'semester_class_id' => $semester_class_2->id,
            'student_id' => $students[4]->id,
        ],
    ))->create();

    LatestPrimaryClassBySemesterSettingView::refreshViewTable(false);

    /**
     * no data
     */
    $result = $this->attendanceRepository->getStudentAttendanceMarkDeductionData([
        'semester_class_ids' => [
            $semester_class_1->id,
            $semester_class_2->id,
        ],
        'date_from' => '2025-04-10',
        'date_to' => '2025-04-11',
    ]);

    expect($result)->toHaveCount(0);


    /**
     *
     *  setup period attendances
     *
     */

    $this->seed([PeriodSeeder::class]);

    $mc_leave_type = LeaveApplicationType::factory()->create([
        'name->en' => 'Medical Certificate',
        'average_point_deduction' => 0,
    ]);

    $time_off_type = LeaveApplicationType::factory()->create([
        'name->en' => 'Time Off',
        'average_point_deduction' => 0.05,
    ]);

    $mc_leave_application = LeaveApplication::factory()->create([
        'leave_applicable_type' => Student::class,
        'leave_applicable_id' => $students[2]->id, // Charlie MC
        'status' => LeaveApplicationStatus::APPROVED->value,
        'leave_application_type_id' => $mc_leave_type->id,
        'reason' => 'Fever',
        'average_point_deduction' => 0,
    ]);

    $time_off_leave_application = LeaveApplication::factory()->create([
        'leave_applicable_type' => Student::class,
        'leave_applicable_id' => $students[3]->id, // David Time Off
        'status' => LeaveApplicationStatus::APPROVED->value,
        'leave_application_type_id' => $time_off_type->id,
        'reason' => 'Planned Vacation',
        'average_point_deduction' => 0.05,
    ]);

    $period_group = PeriodGroup::first();

    $J11_time_table = Timetable::factory()->create([
        'name' => 'J11 Time Table',
        'semester_class_id' => $semester_class_1->id,
        'period_group_id' => $period_group->id,
        'is_active' => true,
    ]);

    $J22_time_table = Timetable::factory()->create([
        'name' => 'J22 Time Table',
        'semester_class_id' => $semester_class_2->id,
        'period_group_id' => $period_group->id,
        'is_active' => true,
    ]);

    // J11 timeslots
    $J11_timeslots = collect();

    $thursday_periods = $period_group->periods()->where('day', Day::THURSDAY)->orderBy('period', 'asc')->get();

    foreach ($thursday_periods as $period) {
        $time_slot = Timeslot::factory()->create([
            'timetable_id' => $J11_time_table->id,
            'day' => Day::THURSDAY,
            'period_id' => $period->id,
            'class_subject_id' => null,
            'placeholder' => null,
            'attendance_from' => $period->from_time,
            'attendance_to' => $period->to_time,
        ]);

        $J11_timeslots->push($time_slot);
    }

    $friday_periods = $period_group->periods()->where('day', Day::FRIDAY)->orderBy('period', 'asc')->get();

    foreach ($friday_periods as $period) {
        $time_slot = Timeslot::factory()->create([
            'timetable_id' => $J11_time_table->id,
            'day' => Day::FRIDAY,
            'period_id' => $period->id,
            'class_subject_id' => null,
            'placeholder' => null,
            'attendance_from' => $period->from_time,
            'attendance_to' => $period->to_time,
        ]);

        $J11_timeslots->push($time_slot);
    }

    $J11_period_attendances = collect();

    foreach ($J11_timeslots as $timeslot) {
        // last timeslot on (FRIDAY) has no mark deduction
        $last_timeslot = $J11_timeslots->last();

        /**
         * Albert is LATE for all period, THURSDAY and FRIDAY
         */
        $period_attendance_1 = PeriodAttendance::factory()->create([
            'student_id' => $students[0]->id,
            'date' => $timeslot->day->value === 'THURSDAY' ? '2025-04-10' : '2025-04-11',
            'timeslot_id' => $timeslot->id,
            'period' => $timeslot->period->period,
            'status' => PeriodAttendanceStatus::LATE->value,
            'has_mark_deduction' => $timeslot->id !== $last_timeslot->id ? true : false, // only last timeslot has no mark deduction
        ]);

        $J11_period_attendances->push($period_attendance_1);


        /**
         * Bob is ABSENT for all period, THURSDAY and FRIDAY
         */
        $period_attendance_2 = PeriodAttendance::factory()->create([
            'student_id' => $students[1]->id,
            'date' => $timeslot->day->value === 'THURSDAY' ? '2025-04-10' : '2025-04-11',
            'timeslot_id' => $timeslot->id,
            'period' => $timeslot->period->period,
            'status' => PeriodAttendanceStatus::ABSENT->value,
            'has_mark_deduction' => $timeslot->id !== $last_timeslot->id ? true : false, // only last timeslot has no mark deduction
        ]);

        $J11_period_attendances->push($period_attendance_2);
    }

    // J22 timeslots
    $J22_timeslots = collect();

    $thursday_periods = $period_group->periods()->where('day', Day::THURSDAY)->orderBy('period', 'asc')->get();

    foreach ($thursday_periods as $period) {
        $time_slot = Timeslot::factory()->create([
            'timetable_id' => $J22_time_table->id,
            'day' => Day::THURSDAY,
            'period_id' => $period->id,
            'class_subject_id' => null,
            'placeholder' => null,
            'attendance_from' => $period->from_time,
            'attendance_to' => $period->to_time,
        ]);

        $J22_timeslots->push($time_slot);
    }

    $J22_period_attendances = collect();

    foreach ($J22_timeslots as $timeslot) {
        /**
         * Charlie MC for all period, THURSDAY
         */
        $period_attendance_1 = PeriodAttendance::factory()->create([
            'student_id' => $students[2]->id,
            'date' => '2025-04-10',
            'timeslot_id' => $timeslot->id,
            'period' => $timeslot->period->period,
            'status' => PeriodAttendanceStatus::ABSENT->value,
            'leave_application_id' => $mc_leave_application->id,
        ]);

        LeaveApplicationPeriod::factory()->create([
            'leave_application_id' => $mc_leave_application->id,
            'period' => $timeslot->period->period,
            'date' => '2025-04-10',
        ]);

        $J22_period_attendances->push($period_attendance_1);

        /**
         * David Time Off for all period, THURSDAY
         */
        $period_attendance_2 = PeriodAttendance::factory()->create([
            'student_id' => $students[3]->id,
            'date' => '2025-04-10',
            'timeslot_id' => $timeslot->id,
            'period' => $timeslot->period->period,
            'status' => PeriodAttendanceStatus::ABSENT->value,
            'leave_application_id' => $time_off_leave_application->id,
        ]);

        LeaveApplicationPeriod::factory()->create([
            'leave_application_id' => $time_off_leave_application->id,
            'period' => $timeslot->period->period,
            'date' => '2025-04-10',
        ]);

        $J22_period_attendances->push($period_attendance_2);

        /**
         * Inactive Student, THURSDAY
         */
        $period_attendance_3 = PeriodAttendance::factory()->create([
            'student_id' => $students[4]->id,
            'date' => '2025-04-10',
            'timeslot_id' => $timeslot->id,
            'period' => $timeslot->period->period,
            'status' => PeriodAttendanceStatus::ABSENT->value,
            'leave_application_id' => null,
        ]);

        $J22_period_attendances->push($period_attendance_3);
    }


    /**
     * Albert is late on THURSDAY and FRIDAY
     * Bob is Absent on THURSDAY and FRIDAY
     * Charlie is MC on THURSDAY , will not be included in the data because no mark deduction
     * David is Time Off on THURSDAY
     *
     */
    $result = $this->attendanceRepository->getStudentAttendanceMarkDeductionData([
        'semester_class_ids' => [
            $semester_class_1->id,
            $semester_class_2->id,
        ],
        'date_from' => '2025-04-10',
        'date_to' => '2025-04-11',
    ])->toArray();

    expect(count($result))->toBe(2); // J111 and J22
    expect(count($result[0]['students']))->toBe(2); // J111 2 students
    expect(count($result[1]['students']))->toBe(1); // J22 2 students

    // $result is sorted by class_name, 1st class is J11
    expect($result[0]['class_name'])->toEqual('J11')
        // ALBERT
        ->and($result[0]['students'][0]['student_name'])->toEqual($students[0]->getTranslations('name'))
        ->and($result[0]['students'][0]['student_number'])->toEqual($students[0]->student_number)
        ->and($result[0]['students'][0]['class'])->toEqual($j11_class->getTranslations('name'))
        ->and($result[0]['students'][0]['attendances'])->toHaveCount(2) // THURSDAY and FRIDAY
        // ALBERT , 2025-04-10, LATE
        ->and($result[0]['students'][0]['attendances'][0])->toEqual([
            'date' => '2025-04-10',
            'type' => PeriodAttendanceStatus::getTranslatedPeriodAttendance(PeriodAttendanceStatus::LATE->value),
            'base_deduct_average_point' => 0.02,
            'periods' => 12, // full day
            'total_deduct_average_point' => 0.02 * 12,
            'reason' => null,
        ])
        // ALBERT , 2025-04-11, LATE (FRIDAY)
        ->and($result[0]['students'][0]['attendances'][1])->toEqual([
            'date' => '2025-04-11',
            'type' => PeriodAttendanceStatus::getTranslatedPeriodAttendance(PeriodAttendanceStatus::LATE->value),
            'base_deduct_average_point' => 0.02,
            'periods' => 11, // last timeslot has no mark deduction so only 11 periods
            'total_deduct_average_point' => 0.02 * 11,
            'reason' => null,
        ])

        // BOB
        ->and($result[0]['students'][1]['student_name'])->toEqual($students[1]->getTranslations('name'))
        ->and($result[0]['students'][1]['student_number'])->toEqual($students[1]->student_number)
        ->and($result[0]['students'][1]['class'])->toEqual($j11_class->getTranslations('name'))
        ->and($result[0]['students'][1]['attendances'])->toHaveCount(2) // THURSDAY and FRIDAY
        // BOB , 2025-04-10, ABSENT
        ->and($result[0]['students'][1]['attendances'][0])->toEqual([
            'date' => '2025-04-10',
            'type' => PeriodAttendanceStatus::getTranslatedPeriodAttendance(PeriodAttendanceStatus::ABSENT->value),
            'base_deduct_average_point' => 0.04,
            'periods' => 12, // full day
            'total_deduct_average_point' => 0.04 * 12,
            'reason' => null,
        ])
        // BOB , 2025-04-11, ABSENT (FRIDAY)
        ->and($result[0]['students'][1]['attendances'][1])->toEqual([
            'date' => '2025-04-11',
            'type' => PeriodAttendanceStatus::getTranslatedPeriodAttendance(PeriodAttendanceStatus::ABSENT->value),
            'base_deduct_average_point' => 0.04,
            'periods' => 11, // last timeslot has no mark deduction so only 11 periods
            'total_deduct_average_point' => 0.04 * 11,
            'reason' => null,
        ]);

    // 2nd class is J22
    expect($result[1]['class_name'])->toEqual('J22')
        // CHARLIE Record Excluded Because MC -> No Mark Deduction

        // DAVID
        ->and($result[1]['students'][0]['student_name'])->toEqual($students[3]->getTranslations('name'))
        ->and($result[1]['students'][0]['student_number'])->toEqual($students[3]->student_number)
        ->and($result[1]['students'][0]['class'])->toEqual($j22_class->getTranslations('name'))
        ->and($result[1]['students'][0]['attendances'])->toHaveCount(1) // THURSDAY
        // DAVID , 2025-04-10, Time Off
        ->and($result[1]['students'][0]['attendances'][0])->toEqual([
            'date' => '2025-04-10',
            'type' => $time_off_type->name,
            'base_deduct_average_point' => $time_off_leave_application->average_point_deduction,
            'periods' => 12, // full day
            'total_deduct_average_point' => $time_off_leave_application->average_point_deduction * 12,
            'reason' => $time_off_leave_application->reason,
        ]);


    /**
     * *******  Updating attendance data for THURSDAY J11 ->
     *
     * ALBERT is LATE for period 1, 2, 3
     * ALBERT is ABSENT for period 4, 5
     * ALBERT is MC for period 6, 7, 8, 9, 10, 11, 12 -> this will be excluded from data
     *
     * BOB attendances remained
     *
     */

    //  updating LATE attendances for period 1, 2, 3
    $J11_period_attendances
        ->where('date', '2025-04-10')
        ->where('student_id', $students[0]->id)
        ->whereIn('period', [1, 2, 3])
        ->each(function ($period_attendance) {
            $period_attendance->update([
                'status' => PeriodAttendanceStatus::LATE->value,
            ]);
        });

    // updating ABSENT attendances for period 4, 5
    $J11_period_attendances
        ->where('date', '2025-04-10')
        ->where('student_id', $students[0]->id)
        ->whereIn('period', [4, 5])
        ->each(function ($period_attendance) {
            $period_attendance->update([
                'status' => PeriodAttendanceStatus::ABSENT->value,
            ]);
        });

    $albert_leave_application = LeaveApplication::factory()->create([
        'leave_applicable_type' => Student::class,
        'leave_applicable_id' => $students[0]->id, // Albert
        'status' => LeaveApplicationStatus::APPROVED->value,
        'leave_application_type_id' => $mc_leave_type->id,
        'reason' => 'Stomachache',
    ]);

    // updating MC attendances for period 6, 7, 8, 9, 10, 11, 12
    $J11_period_attendances
        ->where('date', '2025-04-10')
        ->where('student_id', $students[0]->id)
        ->whereIn('period', [6, 7, 8, 9, 10, 11, 12])
        ->each(function ($period_attendance) use ($albert_leave_application) {

            LeaveApplicationPeriod::factory()->create([
                'leave_application_id' => $albert_leave_application->id,
                'period' => $period_attendance->period,
                'date' => '2025-04-10',
            ]);

            $period_attendance->update([
                'status' => PeriodAttendanceStatus::ABSENT->value,
                'leave_application_id' => $albert_leave_application->id,
            ]);
        });

    /**
     *
     * get data for only THURSDAY for class J11
     *
     *
     * ALBERT is LATE for period 1, 2, 3
     * ALBERT is ABSENT for period 4, 5
     * ALBERT is MC for period 6, 7, 8, 9, 10, 11, 12 -> this will be excluded from data
     *
     *
     */
    $result = $this->attendanceRepository->getStudentAttendanceMarkDeductionData([
        'semester_class_ids' => [
            $semester_class_1->id, // J11
        ],
        'date_from' => '2025-04-10', // only for THURSDAY
        'date_to' => '2025-04-10',
    ])->toArray();

    expect($result[0])->toEqual([
        'class_name' => 'J11',
        'students' => [
            // Albert data is updated
            [
                'student_name' => $students[0]->getTranslations('name'),
                'student_number' => $students[0]->student_number,
                'class' => $j11_class->getTranslations('name'),
                'attendances' => [
                    [
                        'date' => '2025-04-10',
                        'type' => PeriodAttendanceStatus::getTranslatedPeriodAttendance(PeriodAttendanceStatus::LATE->value),
                        'base_deduct_average_point' => 0.02,
                        'periods' => 3, // period 1, 2, 3
                        'total_deduct_average_point' => 0.02 * 3,
                        'reason' => null,
                    ],
                    [
                        'date' => '2025-04-10',
                        'type' => PeriodAttendanceStatus::getTranslatedPeriodAttendance(PeriodAttendanceStatus::ABSENT->value),
                        'base_deduct_average_point' => 0.04,
                        'periods' => 2, // period 4, 5
                        'total_deduct_average_point' => 0.04 * 2,
                        'reason' => null,
                    ],
                    // Exclude MC from data because no mark deduction
                ],
            ],
            // Bob data remained
            [
                'student_name' => $students[1]->getTranslations('name'),
                'student_number' => $students[1]->student_number,
                'class' => $j11_class->getTranslations('name'),
                'attendances' => [
                    [
                        'date' => '2025-04-10',
                        'type' => PeriodAttendanceStatus::getTranslatedPeriodAttendance(PeriodAttendanceStatus::ABSENT->value),
                        'base_deduct_average_point' => 0.04,
                        'periods' => 12, // full day
                        'total_deduct_average_point' => 0.04 * 12,
                        'reason' => null,
                    ],
                ],
            ],
        ],
    ]);
});

test('getStudentAttendanceMarkDeductionData(), get future data with approved leave application', function () {
    app()->setLocale('en');

    $semester_setting = SemesterSetting::factory()->create();

    $j11_class = ClassModel::factory()->create([
        'type' => ClassType::PRIMARY,
        'name->en' => 'J11',
    ]);

    $semester_class_1 = SemesterClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'class_id' => $j11_class->id,
    ]);

    $albert = Student::factory()->create([
        'name->en' => 'Albert' // J11
    ]);

    $student_class = StudentClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'semester_class_id' => $semester_class_1->id,
        'student_id' => $albert->id,
    ]);

    LatestPrimaryClassBySemesterSettingView::refreshViewTable(false);

    /**
     *
     *  Albert is MC for MONDAY, TUESDAY, WEDNESDAY
     *  Albert is on Time Off for THURSDAY, FRIDAY
     *
     */

    $this->seed([PeriodSeeder::class]);

    $mc_leave_type = LeaveApplicationType::factory()->create([
        'name->en' => 'Medical Certificate',
        'average_point_deduction' => 0,
    ]);

    $time_off_type = LeaveApplicationType::factory()->create([
        'name->en' => 'Time Off',
        'average_point_deduction' => 0.05,
    ]);

    $mc_leave_application = LeaveApplication::factory()->create([
        'leave_applicable_type' => Student::class,
        'leave_applicable_id' => $albert->id, // Albert MC
        'status' => LeaveApplicationStatus::APPROVED->value,
        'leave_application_type_id' => $mc_leave_type->id,
        'reason' => 'Fever',
        'average_point_deduction' => 0,
    ]);

    $time_off_leave_application = LeaveApplication::factory()->create([
        'leave_applicable_type' => Student::class,
        'leave_applicable_id' => $albert->id, // Albert MC
        'status' => LeaveApplicationStatus::APPROVED->value,
        'leave_application_type_id' => $time_off_type->id,
        'reason' => 'Planned Vacation',
        'average_point_deduction' => 0.05,
    ]);

    $period_group = PeriodGroup::first();

    $J11_time_table = Timetable::factory()->create([
        'name' => 'J11 Time Table',
        'semester_class_id' => $semester_class_1->id,
        'period_group_id' => $period_group->id,
        'is_active' => true,
    ]);

    // J11 timeslots
    $J11_timeslots = collect();

    // full days
    $days = [Day::MONDAY, Day::TUESDAY, Day::WEDNESDAY, Day::THURSDAY, Day::FRIDAY];

    foreach ($days as $day) {
        $day_periods = $period_group->periods()->where('day', $day)->orderBy('period', 'asc')->get();

        foreach ($day_periods as $period) {
            $time_slot = Timeslot::factory()->create([
                'timetable_id' => $J11_time_table->id,
                'day' => $day,
                'period_id' => $period->id,
                'class_subject_id' => null,
                'placeholder' => null,
                'attendance_from' => $period->from_time,
                'attendance_to' => $period->to_time,
            ]);

            $J11_timeslots->push($time_slot);
        }
    }

    $J11_period_attendances = collect();

    foreach ($J11_timeslots as $timeslot) {
        switch ($timeslot->day->value) {
            case 'MONDAY':
                $period_attendance = PeriodAttendance::factory()->create([
                    'student_id' => $albert->id,
                    'date' => '2025-04-21',
                    'timeslot_id' => $timeslot->id,
                    'period' => $timeslot->period->period,
                    'status' => PeriodAttendanceStatus::ABSENT->value,
                    'leave_application_id' => $mc_leave_application->id,
                ]);

                LeaveApplicationPeriod::factory()->create([
                    'leave_application_id' => $mc_leave_application->id,
                    'period' => $timeslot->period->period,
                    'date' => '2025-04-21',
                ]);
                break;

            case 'TUESDAY':
                $period_attendance = PeriodAttendance::factory()->create([
                    'student_id' => $albert->id,
                    'date' => '2025-04-22',
                    'timeslot_id' => $timeslot->id,
                    'period' => $timeslot->period->period,
                    'status' => PeriodAttendanceStatus::ABSENT->value,
                    'leave_application_id' => $mc_leave_application->id,
                ]);

                LeaveApplicationPeriod::factory()->create([
                    'leave_application_id' => $mc_leave_application->id,
                    'period' => $timeslot->period->period,
                    'date' => '2025-04-22',
                ]);
                break;

            case 'WEDNESDAY':
                if ($timeslot->period->period <= 6) { // assume period_attendances is up to WEDNESDAY PERIOD 6
                    $period_attendance = PeriodAttendance::factory()->create([
                        'student_id' => $albert->id,
                        'date' => '2025-04-23',
                        'timeslot_id' => $timeslot->id,
                        'period' => $timeslot->period->period,
                        'status' => PeriodAttendanceStatus::ABSENT->value,
                        'leave_application_id' => $mc_leave_application->id,
                    ]);
                }

                LeaveApplicationPeriod::factory()->create([
                    'leave_application_id' => $mc_leave_application->id,
                    'period' => $timeslot->period->period,
                    'date' => '2025-04-23',
                ]);
                break;

            case 'THURSDAY' || 'FRIDAY':
                // no attendance yet, only LeaveApplicationPeriod

                LeaveApplicationPeriod::factory()->create([
                    'leave_application_id' => $time_off_leave_application->id,
                    'period' => $timeslot->period->period,
                    'date' => $timeslot->day->value === 'THURSDAY' ? '2025-04-24' : '2025-04-25',
                ]);
                break;
        }

        $J11_period_attendances->push($period_attendance);
    }

    /**
     *  Albert is MC for MONDAY, TUESDAY, WEDNESDAY , MC has no mark deduction
     *  Albert is on Time Off for THURSDAY, FRIDAY
     *
     */
    $result = $this->attendanceRepository->getStudentAttendanceMarkDeductionData([
        'semester_class_ids' => [
            $semester_class_1->id,
        ],
        'date_from' => '2025-04-21',
        'date_to' => '2025-04-25',
    ])->toArray();

    expect($result[0])->toEqual([
        'class_name' => 'J11',
        'students' => [
            [
                'student_name' => $albert->getTranslations('name'),
                'student_number' => $albert->student_number,
                'class' => $j11_class->getTranslations('name'),
                'attendances' => [
                    // MC has no mark deduction, MONDAY, TUESDAY, WEDNESDAY is excluded
                    [
                        'date' => '2025-04-24',
                        'type' => $time_off_type->name,
                        'base_deduct_average_point' => $time_off_leave_application->average_point_deduction,
                        'periods' => 12, // full period
                        'total_deduct_average_point' => $time_off_leave_application->average_point_deduction * 12,
                        'reason' => $time_off_leave_application->reason,
                    ],
                    [
                        'date' => '2025-04-25',
                        'type' => $time_off_type->name,
                        'base_deduct_average_point' => $time_off_leave_application->average_point_deduction,
                        'periods' => 12, // full period
                        'total_deduct_average_point' => $time_off_leave_application->average_point_deduction * 12,
                        'reason' => $time_off_leave_application->reason,
                    ],
                ],
            ],
        ],
    ]);
});

test('getStudentAttendanceMarkDeductionData(), filter by leave_application types', function () {
    app()->setLocale('en');

    $semester_setting = SemesterSetting::factory()->create();

    $j11_class = ClassModel::factory()->create([
        'type' => ClassType::PRIMARY,
        'name->en' => 'J11',
    ]);

    $semester_class_1 = SemesterClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'class_id' => $j11_class->id,
    ]);

    $albert = Student::factory()->create([
        'name->en' => 'Albert' // J11
    ]);

    $student_class = StudentClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'semester_class_id' => $semester_class_1->id,
        'student_id' => $albert->id,
    ]);

    LatestPrimaryClassBySemesterSettingView::refreshViewTable(false);

    /**
     *
     *  Albert is MC for MONDAY
     *  Albert is on Time OFF for TUESDAY
     *  Albert is on State Competition event for WEDNESDAY
     *
     */

    $this->seed([PeriodSeeder::class]);

    $mc_leave_type = LeaveApplicationType::factory()->create([
        'name->en' => 'Medical Certificate',
    ]);

    $time_off_type = LeaveApplicationType::factory()->create([
        'name->en' => 'Time Off',
    ]);

    $state_competition_type = LeaveApplicationType::factory()->create([
        'name->en' => 'State Competition',
    ]);

    $mc_leave_application = LeaveApplication::factory()->create([
        'leave_applicable_type' => Student::class,
        'leave_applicable_id' => $albert->id, // Albert MC
        'status' => LeaveApplicationStatus::APPROVED->value,
        'leave_application_type_id' => $mc_leave_type->id,
        'reason' => 'Fever',
        'average_point_deduction' => 0.02,
    ]);

    $time_off_leave_application = LeaveApplication::factory()->create([
        'leave_applicable_type' => Student::class,
        'leave_applicable_id' => $albert->id, // Albert Time Off
        'status' => LeaveApplicationStatus::APPROVED->value,
        'leave_application_type_id' => $time_off_type->id,
        'reason' => 'Planned Vacation',
        'average_point_deduction' => 0.05,
    ]);

    $state_competition_leave_application = LeaveApplication::factory()->create([
        'leave_applicable_type' => Student::class,
        'leave_applicable_id' => $albert->id, // Albert State Competition
        'status' => LeaveApplicationStatus::APPROVED->value,
        'leave_application_type_id' => $state_competition_type->id,
        'reason' => 'State Competition',
        'average_point_deduction' => 0.01,
    ]);

    $period_group = PeriodGroup::first();

    $J11_time_table = Timetable::factory()->create([
        'name' => 'J11 Time Table',
        'semester_class_id' => $semester_class_1->id,
        'period_group_id' => $period_group->id,
        'is_active' => true,
    ]);

    // J11 timeslots
    $J11_timeslots = collect();

    $days = [Day::MONDAY, Day::TUESDAY, Day::WEDNESDAY];

    foreach ($days as $day) {
        $day_periods = $period_group->periods()->where('day', $day)->orderBy('period', 'asc')->get();

        foreach ($day_periods as $period) {
            $time_slot = Timeslot::factory()->create([
                'timetable_id' => $J11_time_table->id,
                'day' => $day,
                'period_id' => $period->id,
                'class_subject_id' => null,
                'placeholder' => null,
                'attendance_from' => $period->from_time,
                'attendance_to' => $period->to_time,
            ]);

            $J11_timeslots->push($time_slot);
        }
    }

    $J11_period_attendances = collect();

    foreach ($J11_timeslots as $timeslot) {
        switch ($timeslot->day->value) {
            case 'MONDAY':
                $period_attendance = PeriodAttendance::factory()->create([
                    'student_id' => $albert->id,
                    'date' => '2025-04-21',
                    'timeslot_id' => $timeslot->id,
                    'period' => $timeslot->period->period,
                    'status' => PeriodAttendanceStatus::ABSENT->value,
                    'leave_application_id' => $mc_leave_application->id,
                ]);

                LeaveApplicationPeriod::factory()->create([
                    'leave_application_id' => $mc_leave_application->id,
                    'period' => $timeslot->period->period,
                    'date' => '2025-04-21',
                ]);
                break;

            case 'TUESDAY':
                $period_attendance = PeriodAttendance::factory()->create([
                    'student_id' => $albert->id,
                    'date' => '2025-04-22',
                    'timeslot_id' => $timeslot->id,
                    'period' => $timeslot->period->period,
                    'status' => PeriodAttendanceStatus::ABSENT->value,
                    'leave_application_id' => $time_off_leave_application->id,
                ]);

                LeaveApplicationPeriod::factory()->create([
                    'leave_application_id' => $time_off_leave_application->id,
                    'period' => $timeslot->period->period,
                    'date' => '2025-04-22',
                ]);
                break;

            case 'WEDNESDAY':
                $period_attendance = PeriodAttendance::factory()->create([
                    'student_id' => $albert->id,
                    'date' => '2025-04-23',
                    'timeslot_id' => $timeslot->id,
                    'period' => $timeslot->period->period,
                    'status' => PeriodAttendanceStatus::ABSENT->value,
                    'leave_application_id' => $state_competition_leave_application->id,
                ]);

                LeaveApplicationPeriod::factory()->create([
                    'leave_application_id' => $state_competition_leave_application->id,
                    'period' => $timeslot->period->period,
                    'date' => '2025-04-23',
                ]);
                break;
        }

        $J11_period_attendances->push($period_attendance);
    }

    /**
     *
     *  Albert is MC for MONDAY
     *  Albert is on Time OFF for TUESDAY
     *  Albert is on State Competition event for WEDNESDAY
     *
     */

    $result = $this->attendanceRepository->getStudentAttendanceMarkDeductionData([
        'semester_class_ids' => [
            $semester_class_1->id,
        ],
        'leave_application_type_ids' => [
            $mc_leave_type->id, // ONLY WANTED MC
        ],
        'date_from' => '2025-04-21',
        'date_to' => '2025-04-25',
    ])->toArray();

    expect($result[0])->toEqual([
        'class_name' => 'J11',
        'students' => [
            [
                'student_name' => $albert->getTranslations('name'),
                'student_number' => $albert->student_number,
                'class' => $j11_class->getTranslations('name'),
                'attendances' => [
                    [
                        'date' => '2025-04-21',
                        'type' => $mc_leave_type->name,
                        'base_deduct_average_point' => $mc_leave_application->average_point_deduction,
                        'periods' => 12, // full period
                        'total_deduct_average_point' => $mc_leave_application->average_point_deduction * 12,
                        'reason' => $mc_leave_application->reason,
                    ],
                ],
            ],
        ],
    ]);

    /**
     *
     *  Albert is MC for MONDAY
     *  Albert is on Time OFF for TUESDAY
     *  Albert is on State Competition event for WEDNESDAY
     *
     */

    $result = $this->attendanceRepository->getStudentAttendanceMarkDeductionData([
        'semester_class_ids' => [
            $semester_class_1->id,
        ],
        'leave_application_type_ids' => [
            $time_off_type->id, // ONLY WANTED TIME OFF
        ],
        'date_from' => '2025-04-21',
        'date_to' => '2025-04-25',
    ])->toArray();

    expect($result[0])->toEqual([
        'class_name' => 'J11',
        'students' => [
            [
                'student_name' => $albert->getTranslations('name'),
                'student_number' => $albert->student_number,
                'class' => $j11_class->getTranslations('name'),
                'attendances' => [
                    [
                        'date' => '2025-04-22',
                        'type' => $time_off_type->name,
                        'base_deduct_average_point' => $time_off_leave_application->average_point_deduction,
                        'periods' => 12, // full period
                        'total_deduct_average_point' => $time_off_leave_application->average_point_deduction * 12,
                        'reason' => $time_off_leave_application->reason,
                    ],
                ],
            ],
        ],
    ]);

    /**
     *
     *  Albert is MC for MONDAY
     *  Albert is on Time OFF for TUESDAY
     *  Albert is on State Competition event for WEDNESDAY
     *
     */

    $result = $this->attendanceRepository->getStudentAttendanceMarkDeductionData([
        'semester_class_ids' => [
            $semester_class_1->id,
        ],
        'leave_application_type_ids' => [
            $mc_leave_type->id, // ONLY WANTED MC and STATE COMPETITION
            $state_competition_type->id,
        ],
        'date_from' => '2025-04-21',
        'date_to' => '2025-04-25',
    ])->toArray();

    expect($result[0])->toEqual([
        'class_name' => 'J11',
        'students' => [
            [
                'student_name' => $albert->getTranslations('name'),
                'student_number' => $albert->student_number,
                'class' => $j11_class->getTranslations('name'),
                'attendances' => [
                    [
                        'date' => '2025-04-21',
                        'type' => $mc_leave_type->name,
                        'base_deduct_average_point' => $mc_leave_application->average_point_deduction,
                        'periods' => 12, // full period
                        'total_deduct_average_point' => $mc_leave_application->average_point_deduction * 12,
                        'reason' => $mc_leave_application->reason,
                    ],
                    [
                        'date' => '2025-04-23',
                        'type' => $state_competition_type->name,
                        'base_deduct_average_point' => $state_competition_leave_application->average_point_deduction,
                        'periods' => 12, // full period
                        'total_deduct_average_point' => $state_competition_leave_application->average_point_deduction * 12,
                        'reason' => $state_competition_leave_application->reason,
                    ],
                ],
            ],
        ],
    ]);
});
