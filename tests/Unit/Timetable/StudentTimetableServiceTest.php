<?php

use App\Enums\CardStatus;
use App\Enums\CardType;
use App\Enums\ClassType;
use App\Enums\Day;
use App\Enums\PeriodAttendanceStatus;
use App\Enums\SubjectType;
use App\Models\AttendancePeriodOverride;
use App\Models\Card;
use App\Models\ClassModel;
use App\Models\ClassSubject;
use App\Models\Course;
use App\Models\Employee;
use App\Models\Grade;
use App\Models\Period;
use App\Models\PeriodGroup;
use App\Models\PeriodLabel;
use App\Models\SchoolAttendancePeriodOverride;
use App\Models\SemesterClass;
use App\Models\SemesterSetting;
use App\Models\Student;
use App\Models\StudentClass;
use App\Models\StudentTimetable;
use App\Models\Subject;
use App\Models\Timeslot;
use App\Models\TimeslotOverride;
use App\Models\TimeslotTeacher;
use App\Models\Timetable;
use App\Services\Timetable\StudentTimetableService;
use Database\Seeders\InternationalizationSeeder;

beforeEach(function () {
    $this->seed(InternationalizationSeeder::class);

    app()->setLocale('en');

    $this->testLocale = app()->getLocale();

});

test('getDefaultTimetable()', function () {

    $employee1 = Employee::factory()->create([
        'name->en' => 'Teacher A',
    ]);
    $employee2 = Employee::factory()->create([
        'name->en' => 'Teacher B',
    ]);
    $employee3 = Employee::factory()->create([
        'name->en' => 'Teacher C',
    ]);

    $grade1 = Grade::factory()->create([
        'name->en' => 'Grade J1'
    ]);

    $course = Course::factory()->uec()->create();
    $semester_setting = SemesterSetting::factory()->create([
        'course_id' => $course->id,
    ]);

    $main_class_j111 = ClassModel::factory()->create([
        'grade_id' => $grade1->id,
        'name->en' => 'J111',
        'name->zh' => '初一11',
    ]);
    $main_class_j122 = ClassModel::factory()->create([
        'grade_id' => $grade1->id,
        'name->en' => 'J122',
        'name->zh' => '初一22',
    ]);
    $english_class = ClassModel::factory()->create([
        'name->en' => 'ENG1',
        'type' => ClassType::ELECTIVE,
    ]);
    $cocu_class_1 = ClassModel::factory()->create([
        'name->en' => 'COMPUTER CLUB',
        'type' => ClassType::SOCIETY,
    ]);
    $cocu_class_2 = ClassModel::factory()->create([
        'name->en' => 'CHESS CLUB',
        'type' => ClassType::SOCIETY,
    ]);

    $semester_class1 = SemesterClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'class_id' => $main_class_j111->id,
    ]);
    $semester_class2 = SemesterClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'class_id' => $english_class->id,
    ]);
    $semester_class3 = SemesterClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'class_id' => $cocu_class_1->id,
    ]);
    $semester_class4 = SemesterClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'class_id' => $cocu_class_2->id,
    ]);
    $semester_class5 = SemesterClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'class_id' => $main_class_j122->id,
    ]);

    $period_group1 = PeriodGroup::factory()->create();
    $period_group2 = PeriodGroup::factory()->create();
    $period_group3 = PeriodGroup::factory()->create();

    $main_j111_timetable = Timetable::factory()->create([
        'name' => 'J111 timetable',
        'is_active' => true,
        'period_group_id' => $period_group1->id,
        'semester_class_id' => $semester_class1,
    ]);
    $eng_timetable = Timetable::factory()->create([
        'name' => 'EN timetable',
        'is_active' => true,
        'period_group_id' => $period_group2->id,
        'semester_class_id' => $semester_class2,
    ]);
    $cocu_1_timetable = Timetable::factory()->create([
        'name' => 'Cocu timetable 1',
        'is_active' => true,
        'period_group_id' => $period_group2->id,
        'semester_class_id' => $semester_class3,
    ]);
    $cocu_2_timetable = Timetable::factory()->create([
        'name' => 'Cocu timetable 2',
        'is_active' => true,
        'period_group_id' => $period_group3->id,
        'semester_class_id' => $semester_class4,
    ]);
    $main_j122_timetable = Timetable::factory()->create([
        'name' => 'J122 timetable',
        'is_active' => true,
        'period_group_id' => $period_group1->id,
        'semester_class_id' => $semester_class5,
    ]);


    $period1 = Period::factory()->create([
        'period_group_id' => $period_group1->id,
        'period' => 1,
        'from_time' => '08:00:00',
        'to_time' => '08:30:00',
        'day' => Day::MONDAY,
        'display_group' => 1,
    ]);
    PeriodLabel::factory()->create([
        'period_group_id' => $period_group1->id,
        'period' => 1,
        'is_attendance_required' => true,
    ]);
    $period2 = Period::factory()->create([
        'period_group_id' => $period_group1->id,
        'period' => 2,
        'from_time' => '08:30:00',
        'to_time' => '09:00:00',
        'day' => Day::MONDAY,
        'display_group' => 1,
    ]);
    PeriodLabel::factory()->create([
        'period_group_id' => $period_group1->id,
        'period' => 2,
        'is_attendance_required' => true,
    ]);
    $period3 = Period::factory()->create([
        'period_group_id' => $period_group1->id,
        'period' => 3,
        'from_time' => '09:00:00',
        'to_time' => '10:00:00',
        'day' => Day::MONDAY,
        'display_group' => 1,
    ]);
    PeriodLabel::factory()->create([
        'period_group_id' => $period_group1->id,
        'period' => 3,
        'is_attendance_required' => true,
    ]);
    $period4 = Period::factory()->create([
        'period_group_id' => $period_group1->id,
        'period' => 4,
        'from_time' => '10:00:00',
        'to_time' => '10:30:00',
        'day' => Day::MONDAY,
        'display_group' => 1,
    ]);
    PeriodLabel::factory()->create([
        'period_group_id' => $period_group1->id,
        'period' => 4,
        'is_attendance_required' => true,
    ]);
    $period5 = Period::factory()->create([
        'period_group_id' => $period_group1->id,
        'period' => 5,
        'from_time' => '10:30:00',
        'to_time' => '12:00:00',
        'day' => Day::MONDAY,
        'display_group' => 1,
    ]);
    PeriodLabel::factory()->create([
        'period_group_id' => $period_group1->id,
        'period' => 5,
        'is_attendance_required' => true,
    ]);


    $period_eng_1 = Period::factory()->create([
        'period_group_id' => $period_group2->id,
        'period' => 1,
        'from_time' => '09:00:00',
        'to_time' => '10:00:00',
        'day' => Day::MONDAY,
        'display_group' => 1,
    ]);
    PeriodLabel::factory()->create([
        'period_group_id' => $period_group2->id,
        'period' => 1,
        'is_attendance_required' => true,
    ]);
    $period_eng_2 = Period::factory()->create([
        'period_group_id' => $period_group2->id,
        'period' => 2,
        'from_time' => '11:00:00',
        'to_time' => '12:00:00',
        'day' => Day::MONDAY,
        'display_group' => 1,
    ]);
    PeriodLabel::factory()->create([
        'period_group_id' => $period_group2->id,
        'period' => 2,
        'is_attendance_required' => true,
    ]);
    $period_cocu = Period::factory()->create([
        'period_group_id' => $period_group2->id,
        'period' => 5,
        'from_time' => '10:30:00',
        'to_time' => '12:00:00',
        'day' => Day::MONDAY,
        'display_group' => 1,
    ]);
    PeriodLabel::factory()->create([
        'period_group_id' => $period_group2->id,
        'period' => 5,
        'is_attendance_required' => true,
    ]);
    $period_cocu2 = Period::factory()->create([
        'period_group_id' => $period_group3->id,
        'period' => 5,
        'from_time' => '10:30:00',
        'to_time' => '12:00:00',
        'day' => Day::MONDAY,
        'display_group' => 1,
    ]);
    PeriodLabel::factory()->create([
        'period_group_id' => $period_group3->id,
        'period' => 5,
        'is_attendance_required' => true,
    ]);


    $subject_math = Subject::factory()->create([
        'name->en' => 'Math',
        'type' => SubjectType::MAJOR,
    ]);
    $subject_science = Subject::factory()->create([
        'name->en' => 'Science',
        'type' => SubjectType::MAJOR,
    ]);
    $subject_bm = Subject::factory()->create([
        'name->en' => 'BM',
        'type' => SubjectType::MAJOR,
    ]);
    $subject_english = Subject::factory()->create([
        'name->en' => 'English',
        'type' => SubjectType::ELECTIVE,
    ]);
    $subject_computer_club = Subject::factory()->create([
        'name->en' => 'Computer Club Activity',
        'type' => SubjectType::COCURRICULUM,
    ]);
    $subject_chess_club = Subject::factory()->create([
        'name->en' => 'Chess Club Activity',
        'type' => SubjectType::COCURRICULUM,
    ]);

    $class_subject_j111_math = ClassSubject::factory()->create([
        'semester_class_id' => $semester_class1->id,
        'subject_id' => $subject_math->id,
    ]);
    $class_subject_j111_science = ClassSubject::factory()->create([
        'semester_class_id' => $semester_class1->id,
        'subject_id' => $subject_science->id,
    ]);
    $class_subject_english = ClassSubject::factory()->create([
        'semester_class_id' => $semester_class2->id,
        'subject_id' => $subject_english->id,
    ]);
    $class_subject_j122_math = ClassSubject::factory()->create([
        'semester_class_id' => $semester_class5->id,
        'subject_id' => $subject_math->id,
    ]);
    $class_subject_j122_bm = ClassSubject::factory()->create([
        'semester_class_id' => $semester_class5->id,
        'subject_id' => $subject_bm->id,
    ]);
    $class_subject_computer_club = ClassSubject::factory()->create([
        'semester_class_id' => $semester_class3->id,
        'subject_id' => $subject_computer_club->id,
    ]);
    $class_subject_chess_club = ClassSubject::factory()->create([
        'semester_class_id' => $semester_class3->id,
        'subject_id' => $subject_chess_club->id,
    ]);

    // assume following periods for student1  (attendance time from 8am to 11:30am)
    // period 1 8am - 8:30am = J111 Math
    // period 2 8:30am - 9am = J111 Math
    // period 3 9am - 10am = <Placeholder for EN> & ENG1
    // period 4 10am - 10:30am = J111 Science
    // period 5 10:30am - 12pm = <Placeholder for Cocu>  & Cocu 1 (ends at 11:30am)

    $student1 = Student::factory()->create();

    StudentClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'semester_class_id' => $semester_class1->id,
        'student_id' => $student1->id,
        'class_type' => ClassType::PRIMARY
    ]);
    StudentClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'semester_class_id' => $semester_class2->id,
        'student_id' => $student1->id,
        'class_type' => ClassType::ELECTIVE,
    ]);
    StudentClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'semester_class_id' => $semester_class3->id,
        'student_id' => $student1->id,
        'class_type' => ClassType::SOCIETY,
    ]);

    $timeslot1 = Timeslot::factory()->create([
        'timetable_id' => $main_j111_timetable->id,
        'class_subject_id' => $class_subject_j111_math->id,
        'period_id' => $period1->id,
        'placeholder' => null,
        'attendance_from' => '08:00',
        'attendance_to' => '08:30',
    ]);
    $timeslot2 = Timeslot::factory()->create([
        'timetable_id' => $main_j111_timetable->id,
        'class_subject_id' => $class_subject_j111_math->id,
        'period_id' => $period2->id,
        'placeholder' => null,
        'attendance_from' => '08:30',
        'attendance_to' => '09:00',
    ]);
    $timeslot3 = Timeslot::factory()->create([
        'timetable_id' => $main_j111_timetable->id,
        'class_subject_id' => null,
        'period_id' => $period3->id,
        'placeholder' => 'ENGLISH',
        'attendance_from' => '09:00',
        'attendance_to' => '10:00',
    ]);
    $timeslot4 = Timeslot::factory()->create([
        'timetable_id' => $main_j111_timetable->id,
        'class_subject_id' => $class_subject_j111_science->id,
        'period_id' => $period4->id,
        'placeholder' => null,
        'attendance_from' => '10:00',
        'attendance_to' => '10:30',
    ]);
    $timeslot5 = Timeslot::factory()->create([
        'timetable_id' => $main_j111_timetable->id,
        'class_subject_id' => null,
        'period_id' => $period5->id,
        'placeholder' => 'COCU',
        'attendance_from' => '10:30',
        'attendance_to' => '12:00',
    ]);

    $eng_timeslot = Timeslot::factory()->create([
        'timetable_id' => $eng_timetable->id,
        'class_subject_id' => $class_subject_english->id,
        'period_id' => $period_eng_1->id,
        'placeholder' => null,
        'attendance_from' => '09:00',
        'attendance_to' => '10:00',
    ]);

    $timeslot7 = Timeslot::factory()->create([
        'timetable_id' => $cocu_1_timetable->id,
        'class_subject_id' => $class_subject_computer_club->id,
        'period_id' => $period_cocu->id,
        'placeholder' => null,
        'attendance_from' => '10:30',
        'attendance_to' => '11:30',
    ]);

    TimeslotTeacher::factory()->create([
        'timeslot_id' => $timeslot1->id,
        'employee_id' => $employee1->id,
    ]);
    TimeslotTeacher::factory()->create([
        'timeslot_id' => $timeslot2->id,
        'employee_id' => $employee1->id,
    ]);
    TimeslotTeacher::factory()->create([
        'timeslot_id' => $timeslot4->id,
        'employee_id' => $employee1->id,
    ]);
    TimeslotTeacher::factory()->create([
        'timeslot_id' => $eng_timeslot->id,
        'employee_id' => $employee2->id,
    ]);
    TimeslotTeacher::factory()->create([
        'timeslot_id' => $eng_timeslot->id,
        'employee_id' => $employee3->id,
    ]);
    TimeslotTeacher::factory()->create([
        'timeslot_id' => $timeslot7->id,
        'employee_id' => $employee2->id,
    ]);

    StudentTimetable::refreshViewTable(false);

    $timetable = app()->make(StudentTimetableService::class)
        ->setStudent($student1)
        ->getDefaultTimetable();

    $summary = app()->make(StudentTimetableService::class)
        ->getAttendancePeriodForStudentAndDay($timetable);

    expect($timetable)->toHaveCount(5);

    // period 1 8am - 8:30am = J111 Math
    expect($timetable[0])->toMatchArray([
        'student_id' => $student1->id,
        'timetable_name' => $main_j111_timetable->name,
        'period_group_name' => [
            'en' => $period_group1->getTranslation('name', 'en'),
            'zh' => $period_group1->getTranslation('name', 'zh'),
        ],
        'timeslot_id' => $timeslot1->id,
        'day' => Day::MONDAY->value,
        'from_time' => '08:00:00',
        'to_time' => '08:30:00',
        'period' => 1,
        'attendance_from' => '08:00:00',
        'attendance_to' => '08:30:00',
        'placeholder' => null,
        'subject_type' => SubjectType::MAJOR->value,
        'subject_code' => $subject_math->code,
        'subject_name' => [
            'en' => $subject_math->getTranslation('name', 'en'),
            'zh' => $subject_math->getTranslation('name', 'zh'),
        ],
        'class_code' => $main_class_j111->code,
        'class_name' => [
            'en' => $main_class_j111->getTranslation('name', 'en'),
            'zh' => $main_class_j111->getTranslation('name', 'zh'),
        ],
        'class_type' => ClassType::PRIMARY->value,
    ])
        ->and($timetable[0]['employees'])->toHaveCount(1)
        ->and($timetable[0]['employees'][0])->toMatchArray([
            'employee_number' => $employee1->employee_number,
            'employee_name' => [
                'en' => $employee1->getTranslation('name', 'en'),
                'zh' => $employee1->getTranslation('name', 'zh'),
            ],
        ]);


    // period 2 8:30am - 9am = J111 Math
    expect($timetable[1])->toMatchArray([
        'student_id' => $student1->id,
        'timetable_name' => $main_j111_timetable->name,
        'period_group_name' => [
            'en' => $period_group1->getTranslation('name', 'en'),
            'zh' => $period_group1->getTranslation('name', 'zh'),
        ],
        'timeslot_id' => $timeslot2->id,
        'day' => Day::MONDAY->value,
        'from_time' => '08:30:00',
        'to_time' => '09:00:00',
        'period' => 2,
        'attendance_from' => '08:30:00',
        'attendance_to' => '09:00:00',
        'placeholder' => null,
        'subject_type' => SubjectType::MAJOR->value,
        'subject_code' => $subject_math->code,
        'subject_name' => [
            'en' => $subject_math->getTranslation('name', 'en'),
            'zh' => $subject_math->getTranslation('name', 'zh'),
        ],
        'class_code' => $main_class_j111->code,
        'class_name' => [
            'en' => $main_class_j111->getTranslation('name', 'en'),
            'zh' => $main_class_j111->getTranslation('name', 'zh'),
        ],
        'class_type' => ClassType::PRIMARY->value,
    ])
        ->and($timetable[1]['employees'])->toHaveCount(1)
        ->and($timetable[1]['employees'][0])->toMatchArray([
            'employee_number' => $employee1->employee_number,
            'employee_name' => [
                'en' => $employee1->getTranslation('name', 'en'),
                'zh' => $employee1->getTranslation('name', 'zh'),
            ],
        ]);

    // period 3 9am - 10am = <Placeholder for EN> & ENG1
    $timetable[2]['employees'] = collect($timetable[2]['employees'])->sortBy('employee_name.en')->values();

    expect($timetable[2])->toMatchArray([
        'student_id' => $student1->id,
        'timetable_name' => $eng_timetable->name,
        'period_group_name' => [
            'en' => $period_group2->getTranslation('name', 'en'),
            'zh' => $period_group2->getTranslation('name', 'zh'),
        ],
        'timeslot_id' => $eng_timeslot->id,
        'day' => Day::MONDAY->value,
        'from_time' => '09:00:00',
        'to_time' => '10:00:00',
        'period' => 1,
        'attendance_from' => '09:00:00',
        'attendance_to' => '10:00:00',
        'placeholder' => null,
        'subject_type' => SubjectType::ELECTIVE->value,
        'subject_code' => $subject_english->code,
        'subject_name' => [
            'en' => $subject_english->getTranslation('name', 'en'),
            'zh' => $subject_english->getTranslation('name', 'zh'),
        ],
        'class_code' => $english_class->code,
        'class_name' => [
            'en' => $english_class->getTranslation('name', 'en'),
            'zh' => $english_class->getTranslation('name', 'zh'),
        ],
        'class_type' => ClassType::ELECTIVE->value,
    ])
        ->and($timetable[2]['employees'])->toHaveCount(2)
        ->and($timetable[2]['employees'][0])->toMatchArray([
            'employee_number' => $employee2->employee_number,
            'employee_name' => [
                'en' => $employee2->getTranslation('name', 'en'),
                'zh' => $employee2->getTranslation('name', 'zh'),
            ],
        ])
        ->and($timetable[2]['employees'][1])->toMatchArray([
            'employee_number' => $employee3->employee_number,
            'employee_name' => [
                'en' => $employee3->getTranslation('name', 'en'),
                'zh' => $employee3->getTranslation('name', 'zh'),
            ],
        ]);

    // period 4 10am - 10:30am = J111 Science
    expect($timetable[3])->toMatchArray([
        'student_id' => $student1->id,
        'timetable_name' => $main_j111_timetable->name,
        'period_group_name' => [
            'en' => $period_group1->getTranslation('name', 'en'),
            'zh' => $period_group1->getTranslation('name', 'zh'),
        ],
        'timeslot_id' => $timeslot4->id,
        'day' => Day::MONDAY->value,
        'from_time' => '10:00:00',
        'to_time' => '10:30:00',
        'period' => 4,
        'attendance_from' => '10:00:00',
        'attendance_to' => '10:30:00',
        'placeholder' => null,
        'subject_type' => SubjectType::MAJOR->value,
        'subject_code' => $subject_science->code,
        'subject_name' => [
            'en' => $subject_science->getTranslation('name', 'en'),
            'zh' => $subject_science->getTranslation('name', 'zh'),
        ],
        'class_code' => $main_class_j111->code,
        'class_name' => [
            'en' => $main_class_j111->getTranslation('name', 'en'),
            'zh' => $main_class_j111->getTranslation('name', 'zh'),
        ],
        'class_type' => ClassType::PRIMARY->value,
    ])
        ->and($timetable[3]['employees'])->toHaveCount(1)
        ->and($timetable[3]['employees'][0])->toMatchArray([
            'employee_number' => $employee1->employee_number,
            'employee_name' => [
                'en' => $employee1->getTranslation('name', 'en'),
                'zh' => $employee1->getTranslation('name', 'zh'),
            ],
        ]);

    // period 5 10:30am - 12pm = <Placeholder for Cocu>  & Cocu 1 (ends at 11:30am)
    expect($timetable[4])->toMatchArray([
        'student_id' => $student1->id,
        'timetable_name' => $cocu_1_timetable->name,
        'period_group_name' => [
            'en' => $period_group2->getTranslation('name', 'en'),
            'zh' => $period_group2->getTranslation('name', 'zh'),
        ],
        'timeslot_id' => $timeslot7->id,
        'day' => Day::MONDAY->value,
        'from_time' => '10:30:00',
        'to_time' => '12:00:00',
        'period' => 5,
        'attendance_from' => '10:30:00',
        'attendance_to' => '11:30:00',
        'placeholder' => null,
        'subject_type' => SubjectType::COCURRICULUM->value,
        'subject_code' => $subject_computer_club->code,
        'subject_name' => [
            'en' => $subject_computer_club->getTranslation('name', 'en'),
            'zh' => $subject_computer_club->getTranslation('name', 'zh'),
        ],
        'class_code' => $cocu_class_1->code,
        'class_name' => [
            'en' => $cocu_class_1->getTranslation('name', 'en'),
            'zh' => $cocu_class_1->getTranslation('name', 'zh'),
        ],
        'class_type' => ClassType::SOCIETY->value,
    ])
        ->and($timetable[4]['employees'])->toHaveCount(1)
        ->and($timetable[4]['employees'][0])->toMatchArray([
            'employee_number' => $employee2->employee_number,
            'employee_name' => [
                'en' => $employee2->getTranslation('name', 'en'),
                'zh' => $employee2->getTranslation('name', 'zh'),
            ],
        ]);


    expect($summary)->toHaveCount(2)
        ->and($summary['from'])->toBe('08:00:00')
        ->and($summary['to'])->toBe('11:30:00');


    // assume following periods for student2 (attendance time from 7:30am to 12:30pm)
    // period 1 7:30am - 8:30am = J122 BM
    // period 2 8:30am - 9am = J122 BM
    // period 3 9am - 10am = <Placeholder for EN> & ENG1
    // period 4 10am - 10:30am = J122 Math
    // period 5 10:30am - 12pm = <Placeholder for Cocu>  & Cocu 2 (ends at 12:30pm)

    $student2 = Student::factory()->create();

    StudentClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'semester_class_id' => $semester_class5->id,
        'student_id' => $student2->id,
        'class_type' => ClassType::PRIMARY
    ]);
    StudentClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'semester_class_id' => $semester_class2->id,
        'student_id' => $student2->id,
        'class_type' => ClassType::ELECTIVE,
    ]);
    StudentClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'semester_class_id' => $semester_class4->id,
        'student_id' => $student2->id,
        'class_type' => ClassType::SOCIETY,
    ]);

    $timeslot1 = Timeslot::factory()->create([
        'timetable_id' => $main_j122_timetable->id,
        'class_subject_id' => $class_subject_j122_bm->id,
        'period_id' => $period1->id,
        'placeholder' => null,
        'attendance_from' => '07:30',
        'attendance_to' => '08:30',
    ]);
    $timeslot2 = Timeslot::factory()->create([
        'timetable_id' => $main_j122_timetable->id,
        'class_subject_id' => $class_subject_j122_bm->id,
        'period_id' => $period2->id,
        'placeholder' => null,
        'attendance_from' => '08:30',
        'attendance_to' => '09:00',
    ]);
    $timeslot3 = Timeslot::factory()->create([
        'timetable_id' => $main_j122_timetable->id,
        'class_subject_id' => null,
        'period_id' => $period3->id,
        'placeholder' => 'ENGLISH',
        'attendance_from' => '09:00',
        'attendance_to' => '10:00',
    ]);
    $timeslot4 = Timeslot::factory()->create([
        'timetable_id' => $main_j122_timetable->id,
        'class_subject_id' => $class_subject_j122_math->id,
        'period_id' => $period4->id,
        'placeholder' => null,
        'attendance_from' => '10:00',
        'attendance_to' => '10:30',
    ]);
    $timeslot5 = Timeslot::factory()->create([
        'timetable_id' => $main_j122_timetable->id,
        'class_subject_id' => null,
        'period_id' => $period5->id,
        'placeholder' => 'COCU',
        'attendance_from' => '10:30',
        'attendance_to' => '12:00',
    ]);
    $timeslot7 = Timeslot::factory()->create([
        'timetable_id' => $cocu_2_timetable->id,
        'class_subject_id' => $class_subject_chess_club,
        'period_id' => $period_cocu2->id,
        'placeholder' => null,
        'attendance_from' => '10:30',
        'attendance_to' => '12:30',
    ]);

    TimeslotTeacher::factory()->create([
        'timeslot_id' => $timeslot1->id,
        'employee_id' => $employee1->id,
    ]);
    TimeslotTeacher::factory()->create([
        'timeslot_id' => $timeslot2->id,
        'employee_id' => $employee1->id,
    ]);
    TimeslotTeacher::factory()->create([
        'timeslot_id' => $timeslot4->id,
        'employee_id' => $employee1->id,
    ]);
    TimeslotTeacher::factory()->create([
        'timeslot_id' => $timeslot7->id,
        'employee_id' => $employee2->id,
    ]);

    StudentTimetable::refreshViewTable(false);

    $timetable = app()->make(StudentTimetableService::class)
        ->setStudent($student2)
        ->setDayFilter(Day::MONDAY)
        ->getDefaultTimetable();

    $summary = app()->make(StudentTimetableService::class)
        ->getAttendancePeriodForStudentAndDay($timetable);

    expect($timetable)->toHaveCount(5);

    // period 1 7:30am - 8:30am = J122 BM
    expect($timetable[0])->toMatchArray([
        'student_id' => $student2->id,
        'timetable_name' => $main_j122_timetable->name,
        'period_group_name' => [
            'en' => $period_group1->getTranslation('name', 'en'),
            'zh' => $period_group1->getTranslation('name', 'zh'),
        ],
        'timeslot_id' => $timeslot1->id,
        'day' => Day::MONDAY->value,
        'from_time' => '08:00:00',
        'to_time' => '08:30:00',
        'period' => 1,
        'attendance_from' => '07:30:00',
        'attendance_to' => '08:30:00',
        'placeholder' => null,
        'subject_type' => SubjectType::MAJOR->value,
        'subject_code' => $subject_bm->code,
        'subject_name' => [
            'en' => $subject_bm->getTranslation('name', 'en'),
            'zh' => $subject_bm->getTranslation('name', 'zh'),
        ],
        'class_code' => $main_class_j122->code,
        'class_name' => [
            'en' => $main_class_j122->getTranslation('name', 'en'),
            'zh' => $main_class_j122->getTranslation('name', 'zh'),
        ],
        'class_type' => ClassType::PRIMARY->value,
    ])
        ->and($timetable[0]['employees'])->toHaveCount(1)
        ->and($timetable[0]['employees'][0])->toMatchArray([
            'employee_number' => $employee1->employee_number,
            'employee_name' => [
                'en' => $employee1->getTranslation('name', 'en'),
                'zh' => $employee1->getTranslation('name', 'zh'),
            ],
        ]);

    // period 2 8:30am - 9am = J122 BM
    expect($timetable[1])->toMatchArray([
        'student_id' => $student2->id,
        'timetable_name' => $main_j122_timetable->name,
        'period_group_name' => [
            'en' => $period_group1->getTranslation('name', 'en'),
            'zh' => $period_group1->getTranslation('name', 'zh'),
        ],
        'timeslot_id' => $timeslot2->id,
        'day' => Day::MONDAY->value,
        'from_time' => '08:30:00',
        'to_time' => '09:00:00',
        'period' => 2,
        'attendance_from' => '08:30:00',
        'attendance_to' => '09:00:00',
        'placeholder' => null,
        'subject_type' => SubjectType::MAJOR->value,
        'subject_code' => $subject_bm->code,
        'subject_name' => [
            'en' => $subject_bm->getTranslation('name', 'en'),
            'zh' => $subject_bm->getTranslation('name', 'zh'),
        ],
        'class_code' => $main_class_j122->code,
        'class_name' => [
            'en' => $main_class_j122->getTranslation('name', 'en'),
            'zh' => $main_class_j122->getTranslation('name', 'zh'),
        ],
        'class_type' => ClassType::PRIMARY->value,
    ])
        ->and($timetable[1]['employees'])->toHaveCount(1)
        ->and($timetable[1]['employees'][0])->toMatchArray([
            'employee_number' => $employee1->employee_number,
            'employee_name' => [
                'en' => $employee1->getTranslation('name', 'en'),
                'zh' => $employee1->getTranslation('name', 'zh'),
            ],
        ]);

    // period 3 9am - 10am = <Placeholder for EN> & ENG1
    $timetable[2]['employees'] = collect($timetable[2]['employees'])->sortBy('employee_name.en')->values();

    expect($timetable[2])->toMatchArray([
        'student_id' => $student2->id,
        'timetable_name' => $eng_timetable->name,
        'period_group_name' => [
            'en' => $period_group2->getTranslation('name', 'en'),
            'zh' => $period_group2->getTranslation('name', 'zh'),
        ],
        'timeslot_id' => $eng_timeslot->id,
        'day' => Day::MONDAY->value,
        'from_time' => '09:00:00',
        'to_time' => '10:00:00',
        'period' => 1,
        'attendance_from' => '09:00:00',
        'attendance_to' => '10:00:00',
        'placeholder' => null,
        'subject_type' => SubjectType::ELECTIVE->value,
        'subject_code' => $subject_english->code,
        'subject_name' => [
            'en' => $subject_english->getTranslation('name', 'en'),
            'zh' => $subject_english->getTranslation('name', 'zh'),
        ],
        'class_code' => $english_class->code,
        'class_name' => [
            'en' => $english_class->getTranslation('name', 'en'),
            'zh' => $english_class->getTranslation('name', 'zh'),
        ],
        'class_type' => ClassType::ELECTIVE->value,
    ])
        ->and($timetable[2]['employees'])->toHaveCount(2)
        ->and($timetable[2]['employees'][0])->toMatchArray([
            'employee_number' => $employee2->employee_number,
            'employee_name' => [
                'en' => $employee2->getTranslation('name', 'en'),
                'zh' => $employee2->getTranslation('name', 'zh'),
            ],
        ])
        ->and($timetable[2]['employees'][1])->toMatchArray([
            'employee_number' => $employee3->employee_number,
            'employee_name' => [
                'en' => $employee3->getTranslation('name', 'en'),
                'zh' => $employee3->getTranslation('name', 'zh'),
            ],
        ]);

    // period 4 10am - 10:30am = J122 Math
    expect($timetable[3])->toMatchArray([
        'student_id' => $student2->id,
        'timetable_name' => $main_j122_timetable->name,
        'period_group_name' => [
            'en' => $period_group1->getTranslation('name', 'en'),
            'zh' => $period_group1->getTranslation('name', 'zh'),
        ],
        'timeslot_id' => $timeslot4->id,
        'day' => Day::MONDAY->value,
        'from_time' => '10:00:00',
        'to_time' => '10:30:00',
        'period' => 4,
        'attendance_from' => '10:00:00',
        'attendance_to' => '10:30:00',
        'placeholder' => null,
        'subject_type' => SubjectType::MAJOR->value,
        'subject_code' => $subject_math->code,
        'subject_name' => [
            'en' => $subject_math->getTranslation('name', 'en'),
            'zh' => $subject_math->getTranslation('name', 'zh'),
        ],
        'class_code' => $main_class_j122->code,
        'class_name' => [
            'en' => $main_class_j122->getTranslation('name', 'en'),
            'zh' => $main_class_j122->getTranslation('name', 'zh'),
        ],
        'class_type' => ClassType::PRIMARY->value,
    ])
        ->and($timetable[3]['employees'])->toHaveCount(1)
        ->and($timetable[3]['employees'][0])->toMatchArray([
            'employee_number' => $employee1->employee_number,
            'employee_name' => [
                'en' => $employee1->getTranslation('name', 'en'),
                'zh' => $employee1->getTranslation('name', 'zh'),
            ],
        ]);


    // period 5 10:30am - 12pm = <Placeholder for Cocu>  & Cocu 2 (ends at 12:30pm)
    expect($timetable[4])->toMatchArray([
        'student_id' => $student2->id,
        'timetable_name' => $cocu_2_timetable->name,
        'period_group_name' => [
            'en' => $period_group3->getTranslation('name', 'en'),
            'zh' => $period_group3->getTranslation('name', 'zh'),
        ],
        'timeslot_id' => $timeslot7->id,
        'day' => Day::MONDAY->value,
        'from_time' => '10:30:00',
        'to_time' => '12:00:00',
        'period' => 5,
        'attendance_from' => '10:30:00',
        'attendance_to' => '12:30:00',
        'placeholder' => null,
        'subject_type' => SubjectType::COCURRICULUM->value,
        'subject_code' => $subject_chess_club->code,
        'subject_name' => [
            'en' => $subject_chess_club->getTranslation('name', 'en'),
            'zh' => $subject_chess_club->getTranslation('name', 'zh'),
        ],
        'class_code' => $cocu_class_2->code,
        'class_name' => [
            'en' => $cocu_class_2->getTranslation('name', 'en'),
            'zh' => $cocu_class_2->getTranslation('name', 'zh'),
        ],
        'class_type' => ClassType::SOCIETY->value,
    ])
        ->and($timetable[4]['employees'])->toHaveCount(1)
        ->and($timetable[4]['employees'][0])->toMatchArray([
            'employee_number' => $employee2->employee_number,
            'employee_name' => [
                'en' => $employee2->getTranslation('name', 'en'),
                'zh' => $employee2->getTranslation('name', 'zh'),
            ],
        ]);

    expect($summary)->toHaveCount(2)
        ->and($summary['from'])->toBe('07:30:00')
        ->and($summary['to'])->toBe('12:30:00');

    // with day filter (no data)
    $timetable = app()->make(StudentTimetableService::class)
        ->setStudent($student2)
        ->setDayFilter(Day::FRIDAY)
        ->getDefaultTimetable();

    expect($timetable)->toBeEmpty();

    // without any filter (should return data for both student1 and student2)
    $timetable = app()->make(StudentTimetableService::class)
        ->getDefaultTimetable();

    expect($timetable)->toHaveCount(10);

    expect(collect($timetable)->where('student_id', $student1->id)->values())->toHaveCount(5);
    expect(collect($timetable)->where('student_id', $student2->id)->values())->toHaveCount(5);

    $summary = app()->make(StudentTimetableService::class)
        ->getAttendancePeriodForStudentAndDay(collect($timetable)->where('student_id', $student1->id)->values());

    expect($summary)->toHaveCount(2)
        ->and($summary['from'])->toBe('08:00:00')
        ->and($summary['to'])->toBe('11:30:00');

    $summary = app()->make(StudentTimetableService::class)
        ->getAttendancePeriodForStudentAndDay(collect($timetable)->where('student_id', $student2->id)->values());

    expect($summary)->toHaveCount(2)
        ->and($summary['from'])->toBe('07:30:00')
        ->and($summary['to'])->toBe('12:30:00');

});

test('getDefaultTimetable() - with student affairs (班务)', function () {

    $employee1 = Employee::factory()->create([
        'name->en' => 'Teacher A',
    ]);

    $semester_setting = SemesterSetting::factory()->create();

    $main_class_j111 = ClassModel::factory()->create([
        'name->en' => 'J111',
        'name->zh' => '初一11',
    ]);

    $semester_class1 = SemesterClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'class_id' => $main_class_j111->id,
    ]);

    $period_group1 = PeriodGroup::factory()->create();

    $main_j111_timetable = Timetable::factory()->create([
        'name' => 'J111 timetable',
        'is_active' => true,
        'period_group_id' => $period_group1->id,
        'semester_class_id' => $semester_class1,
    ]);

    // 班务
    $period1 = Period::factory()->create([
        'period_group_id' => $period_group1->id,
        'period' => 1,
        'from_time' => '08:00:00',
        'to_time' => '08:30:00',
        'day' => Day::MONDAY,
        'display_group' => 1,
    ]);
    PeriodLabel::factory()->create([
        'period_group_id' => $period_group1->id,
        'period' => 1,
        'is_attendance_required' => true,
    ]);
    $period2 = Period::factory()->create([
        'period_group_id' => $period_group1->id,
        'period' => 2,
        'from_time' => '08:30:00',
        'to_time' => '09:00:00',
        'day' => Day::MONDAY,
        'display_group' => 1,
    ]);
    PeriodLabel::factory()->create([
        'period_group_id' => $period_group1->id,
        'period' => 2,
        'is_attendance_required' => true,
    ]);
    // english class
    $period3 = Period::factory()->create([
        'period_group_id' => $period_group1->id,
        'period' => 3,
        'from_time' => '09:00:00',
        'to_time' => '10:00:00',
        'day' => Day::MONDAY,
        'display_group' => 1,
    ]);
    PeriodLabel::factory()->create([
        'period_group_id' => $period_group1->id,
        'period' => 3,
        'is_attendance_required' => true,
    ]);


    $subject_math = Subject::factory()->create([
        'name->en' => 'Math',
        'type' => SubjectType::MAJOR,
    ]);
    $class_subject_j111_math = ClassSubject::factory()->create([
        'semester_class_id' => $semester_class1->id,
        'subject_id' => $subject_math->id,
    ]);

    // assume following periods for student1  (attendance time from 8am to 9am)
    // period 1 8am - 8:30am = 班务
    // period 2 8:30am - 9am = J111 Math
    // period 3 9am - 10am = english (not in timetable because null class_subject + no teacher)

    $student1 = Student::factory()->create();

    StudentClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'semester_class_id' => $semester_class1->id,
        'student_id' => $student1->id,
        'class_type' => ClassType::PRIMARY
    ]);

    // 班务
    $timeslot1 = Timeslot::factory()->create([
        'timetable_id' => $main_j111_timetable->id,
        'class_subject_id' => null,
        'period_id' => $period1->id,
        'placeholder' => '班务',
        'attendance_from' => '08:00',
        'attendance_to' => '08:30',
    ]);
    $timeslot2 = Timeslot::factory()->create([
        'timetable_id' => $main_j111_timetable->id,
        'class_subject_id' => $class_subject_j111_math->id,
        'period_id' => $period2->id,
        'placeholder' => null,
        'attendance_from' => '08:30',
        'attendance_to' => '09:00',
    ]);
    $timeslot3 = Timeslot::factory()->create([
        'timetable_id' => $main_j111_timetable->id,
        'class_subject_id' => null,
        'period_id' => $period3->id,
        'placeholder' => 'ENGLISH',
        'attendance_from' => '09:00',
        'attendance_to' => '10:00',
    ]);


    TimeslotTeacher::factory()->create([
        'timeslot_id' => $timeslot1->id,
        'employee_id' => $employee1->id,
    ]);
    TimeslotTeacher::factory()->create([
        'timeslot_id' => $timeslot2->id,
        'employee_id' => $employee1->id,
    ]);

    StudentTimetable::refreshViewTable(false);

    $timetable = app()->make(StudentTimetableService::class)
        ->setStudent($student1)
        ->getDefaultTimetable();

    $summary = app()->make(StudentTimetableService::class)
        ->getAttendancePeriodForStudentAndDay($timetable);

    expect($timetable)->toHaveCount(2);

    // period 1 8am - 8:30am = 班务
    expect($timetable[0])->toMatchArray([
        'student_id' => $student1->id,
        'timetable_name' => $main_j111_timetable->name,
        'period_group_name' => [
            'en' => $period_group1->getTranslation('name', 'en'),
            'zh' => $period_group1->getTranslation('name', 'zh'),
        ],
        'timeslot_id' => $timeslot1->id,
        'day' => Day::MONDAY->value,
        'from_time' => '08:00:00',
        'to_time' => '08:30:00',
        'period' => 1,
        'attendance_from' => '08:00:00',
        'attendance_to' => '08:30:00',
        'placeholder' => '班务',
        'subject_type' => null,
        'subject_code' => null,
        'subject_name' => null,
        'class_code' => $main_class_j111->code,
        'class_name' => [
            'en' => $main_class_j111->getTranslation('name', 'en'),
            'zh' => $main_class_j111->getTranslation('name', 'zh'),
        ],
        'class_type' => ClassType::PRIMARY->value,
    ])
        ->and($timetable[0]['employees'])->toHaveCount(1)
        ->and($timetable[0]['employees'][0])->toMatchArray([
            'employee_number' => $employee1->employee_number,
            'employee_name' => [
                'en' => $employee1->getTranslation('name', 'en'),
                'zh' => $employee1->getTranslation('name', 'zh'),
            ],
        ]);


    // period 2 8:30am - 9am = J111 Math
    expect($timetable[1])->toMatchArray([
        'student_id' => $student1->id,
        'timetable_name' => $main_j111_timetable->name,
        'period_group_name' => [
            'en' => $period_group1->getTranslation('name', 'en'),
            'zh' => $period_group1->getTranslation('name', 'zh'),
        ],
        'timeslot_id' => $timeslot2->id,
        'day' => Day::MONDAY->value,
        'from_time' => '08:30:00',
        'to_time' => '09:00:00',
        'period' => 2,
        'attendance_from' => '08:30:00',
        'attendance_to' => '09:00:00',
        'placeholder' => null,
        'subject_type' => SubjectType::MAJOR->value,
        'subject_code' => $subject_math->code,
        'subject_name' => [
            'en' => $subject_math->getTranslation('name', 'en'),
            'zh' => $subject_math->getTranslation('name', 'zh'),
        ],
        'class_code' => $main_class_j111->code,
        'class_name' => [
            'en' => $main_class_j111->getTranslation('name', 'en'),
            'zh' => $main_class_j111->getTranslation('name', 'zh'),
        ],
        'class_type' => ClassType::PRIMARY->value,
    ])
        ->and($timetable[1]['employees'])->toHaveCount(1)
        ->and($timetable[1]['employees'][0])->toMatchArray([
            'employee_number' => $employee1->employee_number,
            'employee_name' => [
                'en' => $employee1->getTranslation('name', 'en'),
                'zh' => $employee1->getTranslation('name', 'zh'),
            ],
        ]);

    expect($summary)->toHaveCount(2)
        ->and($summary['from'])->toBe('08:00:00')
        ->and($summary['to'])->toBe('09:00:00');
});

test('getAttendancePeriods', function () {

    $employee1 = Employee::factory()->create([
        'name->en' => 'Teacher A',
    ]);
    $employee2 = Employee::factory()->create([
        'name->en' => 'Teacher B',
    ]);
    $employee3 = Employee::factory()->create([
        'name->en' => 'Teacher C',
    ]);

    $grade1 = Grade::factory()->create([
        'name->en' => 'Grade J1'
    ]);

    $course = Course::factory()->uec()->create();
    $semester_setting = SemesterSetting::factory()->create([
        'course_id' => $course->id,
    ]);

    $main_class_j111 = ClassModel::factory()->create([
        'grade_id' => $grade1->id,
        'name->en' => 'J111',
        'name->zh' => '初一11',
    ]);
    $main_class_j122 = ClassModel::factory()->create([
        'grade_id' => $grade1->id,
        'name->en' => 'J122',
        'name->zh' => '初一22',
    ]);
    $english_class = ClassModel::factory()->create([
        'name->en' => 'ENG1',
        'type' => ClassType::ELECTIVE,
    ]);
    $cocu_class_1 = ClassModel::factory()->create([
        'name->en' => 'COMPUTER CLUB',
        'type' => ClassType::SOCIETY,
    ]);
    $cocu_class_2 = ClassModel::factory()->create([
        'name->en' => 'CHESS CLUB',
        'type' => ClassType::SOCIETY,
    ]);

    $semester_class1 = SemesterClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'class_id' => $main_class_j111->id,
    ]);
    $semester_class2 = SemesterClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'class_id' => $english_class->id,
    ]);
    $semester_class3 = SemesterClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'class_id' => $cocu_class_1->id,
    ]);
    $semester_class4 = SemesterClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'class_id' => $cocu_class_2->id,
    ]);
    $semester_class5 = SemesterClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'class_id' => $main_class_j122->id,
    ]);

    $period_group1_main = PeriodGroup::factory()->create();
    $period_group2_main = PeriodGroup::factory()->create();
    $period_group_english = PeriodGroup::factory()->create();
    $period_group_cocu1 = PeriodGroup::factory()->create();
    $period_group_cocu2 = PeriodGroup::factory()->create();

    $main_j111_timetable = Timetable::factory()->create([
        'name' => 'J111 timetable',
        'is_active' => true,
        'period_group_id' => $period_group1_main->id,
        'semester_class_id' => $semester_class1,
    ]);
    $eng_timetable = Timetable::factory()->create([
        'name' => 'EN timetable',
        'is_active' => true,
        'period_group_id' => $period_group_english->id,
        'semester_class_id' => $semester_class2,
    ]);
    $cocu_1_timetable = Timetable::factory()->create([
        'name' => 'Cocu timetable 1',
        'is_active' => true,
        'period_group_id' => $period_group_cocu1->id,
        'semester_class_id' => $semester_class3,
    ]);
    $cocu_2_timetable = Timetable::factory()->create([
        'name' => 'Cocu timetable 2',
        'is_active' => true,
        'period_group_id' => $period_group_cocu2->id,
        'semester_class_id' => $semester_class4,
    ]);
    $main_j122_timetable = Timetable::factory()->create([
        'name' => 'J122 timetable',
        'is_active' => true,
        'period_group_id' => $period_group2_main->id,
        'semester_class_id' => $semester_class5,
    ]);


    $subject_math = Subject::factory()->create([
        'name->en' => 'Math',
        'type' => SubjectType::MAJOR,
    ]);
    $subject_science = Subject::factory()->create([
        'name->en' => 'Science',
        'type' => SubjectType::MAJOR,
    ]);
    $subject_bm = Subject::factory()->create([
        'name->en' => 'BM',
        'type' => SubjectType::MAJOR,
    ]);
    $subject_english = Subject::factory()->create([
        'name->en' => 'English',
        'type' => SubjectType::ELECTIVE,
    ]);
    $subject_computer_club = Subject::factory()->create([
        'name->en' => 'Computer Club Activity',
        'type' => SubjectType::COCURRICULUM,
    ]);
    $subject_chess_club = Subject::factory()->create([
        'name->en' => 'Chess Club Activity',
        'type' => SubjectType::COCURRICULUM,
    ]);

    $class_subject_j111_math = ClassSubject::factory()->create([
        'semester_class_id' => $semester_class1->id,
        'subject_id' => $subject_math->id,
    ]);
    $class_subject_j111_science = ClassSubject::factory()->create([
        'semester_class_id' => $semester_class1->id,
        'subject_id' => $subject_science->id,
    ]);
    $class_subject_english = ClassSubject::factory()->create([
        'semester_class_id' => $semester_class2->id,
        'subject_id' => $subject_english->id,
    ]);
    $class_subject_j122_math = ClassSubject::factory()->create([
        'semester_class_id' => $semester_class5->id,
        'subject_id' => $subject_math->id,
    ]);
    $class_subject_j122_bm = ClassSubject::factory()->create([
        'semester_class_id' => $semester_class5->id,
        'subject_id' => $subject_bm->id,
    ]);
    $class_subject_computer_club = ClassSubject::factory()->create([
        'semester_class_id' => $semester_class3->id,
        'subject_id' => $subject_computer_club->id,
    ]);
    $class_subject_chess_club = ClassSubject::factory()->create([
        'semester_class_id' => $semester_class3->id,
        'subject_id' => $subject_chess_club->id,
    ]);

    $student1 = Student::factory()->create();
    $card = Card::factory()->create([
        'userable_type' => Student::class,
        'userable_id' => $student1->id,
        'card_type' => CardType::PROXIMITY->value,
        'status' => CardStatus::ACTIVE->value,
    ]);
    $student2 = Student::factory()->create();
    $card2 = Card::factory()->create([
        'userable_type' => Student::class,
        'userable_id' => $student2->id,
        'card_type' => CardType::PROXIMITY->value,
        'status' => CardStatus::INACTIVE->value,
    ]);


    foreach ([Day::MONDAY, Day::TUESDAY] as $day) {
        $period1_period_group1 = Period::factory()->create([
            'period_group_id' => $period_group1_main->id,
            'period' => 1,
            'from_time' => '08:00:00',
            'to_time' => '08:30:00',
            'day' => $day,
            'display_group' => 1,
        ]);
        $period1_period_group2 = Period::factory()->create([
            'period_group_id' => $period_group2_main->id,
            'period' => 1,
            'from_time' => '08:00:00',
            'to_time' => '08:30:00',
            'day' => $day,
            'display_group' => 1,
        ]);

        $period2_period_group1 = Period::factory()->create([
            'period_group_id' => $period_group1_main->id,
            'period' => 2,
            'from_time' => '08:30:00',
            'to_time' => '09:00:00',
            'day' => $day,
            'display_group' => 1,
        ]);
        $period2_period_group2 = Period::factory()->create([
            'period_group_id' => $period_group2_main->id,
            'period' => 2,
            'from_time' => '08:30:00',
            'to_time' => '09:00:00',
            'day' => $day,
            'display_group' => 1,
        ]);

        $period3_period_group1 = Period::factory()->create([
            'period_group_id' => $period_group1_main->id,
            'period' => 3,
            'from_time' => '09:00:00',
            'to_time' => '10:00:00',
            'day' => $day,
            'display_group' => 1,
        ]);
        $period3_period_group2 = Period::factory()->create([
            'period_group_id' => $period_group2_main->id,
            'period' => 3,
            'from_time' => '09:00:00',
            'to_time' => '10:00:00',
            'day' => $day,
            'display_group' => 1,
        ]);

        $period4_period_group1 = Period::factory()->create([
            'period_group_id' => $period_group1_main->id,
            'period' => 4,
            'from_time' => '10:00:00',
            'to_time' => '10:30:00',
            'day' => $day,
            'display_group' => 1,
        ]);
        $period4_period_group2 = Period::factory()->create([
            'period_group_id' => $period_group2_main->id,
            'period' => 4,
            'from_time' => '10:00:00',
            'to_time' => '10:30:00',
            'day' => $day,
            'display_group' => 1,
        ]);

        $period5_period_group1 = Period::factory()->create([
            'period_group_id' => $period_group1_main->id,
            'period' => 5,
            'from_time' => '10:30:00',
            'to_time' => '12:00:00',
            'day' => $day,
            'display_group' => 1,
        ]);
        $period5_period_group2 = Period::factory()->create([
            'period_group_id' => $period_group2_main->id,
            'period' => 5,
            'from_time' => '10:30:00',
            'to_time' => '12:00:00',
            'day' => $day,
            'display_group' => 1,
        ]);

        $period_eng_1 = Period::factory()->create([
            'period_group_id' => $period_group_english->id,
            'period' => 3,
            'from_time' => '09:00:00',
            'to_time' => '10:00:00',
            'day' => $day,
            'display_group' => 1,
        ]);

        $period_cocu = Period::factory()->create([
            'period_group_id' => $period_group_cocu1->id,
            'period' => 5,
            'from_time' => '10:30:00',
            'to_time' => '12:00:00',
            'day' => $day,
            'display_group' => 1,
        ]);

        $period_cocu2 = Period::factory()->create([
            'period_group_id' => $period_group_cocu2->id,
            'period' => 5,
            'from_time' => '10:30:00',
            'to_time' => '12:00:00',
            'day' => $day,
            'display_group' => 1,
        ]);

        // student 1
        $timeslot1 = Timeslot::factory()->create([
            'timetable_id' => $main_j111_timetable->id,
            'day' => $day,
            'class_subject_id' => $class_subject_j111_math->id,
            'period_id' => $period1_period_group1->id,
            'placeholder' => null,
            'attendance_from' => '08:00',
            'attendance_to' => '08:30',
        ]);
        $timeslot2 = Timeslot::factory()->create([
            'timetable_id' => $main_j111_timetable->id,
            'day' => $day,
            'class_subject_id' => $class_subject_j111_math->id,
            'period_id' => $period2_period_group1->id,
            'placeholder' => null,
            'attendance_from' => '08:30',
            'attendance_to' => '09:00',
        ]);
        $timeslot3 = Timeslot::factory()->create([
            'timetable_id' => $main_j111_timetable->id,
            'day' => $day,
            'class_subject_id' => null,
            'period_id' => $period3_period_group1->id,
            'placeholder' => 'ENGLISH',
            'attendance_from' => '09:00',
            'attendance_to' => '10:00',
        ]);
        $timeslot4 = Timeslot::factory()->create([
            'timetable_id' => $main_j111_timetable->id,
            'day' => $day,
            'class_subject_id' => $class_subject_j111_science->id,
            'period_id' => $period4_period_group1->id,
            'placeholder' => null,
            'attendance_from' => '10:00',
            'attendance_to' => '10:30',
        ]);
        $timeslot5 = Timeslot::factory()->create([
            'timetable_id' => $main_j111_timetable->id,
            'day' => $day,
            'class_subject_id' => null,
            'period_id' => $period5_period_group1->id,
            'placeholder' => 'COCU',
            'attendance_from' => '10:30',
            'attendance_to' => '12:00',
        ]);

        $eng_timeslot = Timeslot::factory()->create([
            'timetable_id' => $eng_timetable->id,
            'day' => $day,
            'class_subject_id' => $class_subject_english->id,
            'period_id' => $period_eng_1->id,
            'placeholder' => null,
            'attendance_from' => '09:00',
            'attendance_to' => '10:00',
        ]);

        $timeslot7 = Timeslot::factory()->create([
            'timetable_id' => $cocu_1_timetable->id,
            'day' => $day,
            'class_subject_id' => $class_subject_computer_club->id,
            'period_id' => $period_cocu->id,
            'placeholder' => null,
            'attendance_from' => '10:30',
            'attendance_to' => '11:30',
        ]);

        TimeslotTeacher::factory()->create([
            'timeslot_id' => $timeslot1->id,
            'employee_id' => $employee1->id,
        ]);
        TimeslotTeacher::factory()->create([
            'timeslot_id' => $timeslot2->id,
            'employee_id' => $employee1->id,
        ]);
        TimeslotTeacher::factory()->create([
            'timeslot_id' => $timeslot4->id,
            'employee_id' => $employee1->id,
        ]);
        TimeslotTeacher::factory()->create([
            'timeslot_id' => $eng_timeslot->id,
            'employee_id' => $employee2->id,
        ]);
        TimeslotTeacher::factory()->create([
            'timeslot_id' => $eng_timeslot->id,
            'employee_id' => $employee3->id,
        ]);
        TimeslotTeacher::factory()->create([
            'timeslot_id' => $timeslot7->id,
            'employee_id' => $employee2->id,
        ]);

        // student 2
        $timeslot1 = Timeslot::factory()->create([
            'timetable_id' => $main_j122_timetable->id,
            'day' => $day,
            'class_subject_id' => $class_subject_j122_bm->id,
            'period_id' => $period1_period_group2->id,
            'placeholder' => null,
            'attendance_from' => '07:30',
            'attendance_to' => '08:30',
        ]);
        $timeslot2 = Timeslot::factory()->create([
            'timetable_id' => $main_j122_timetable->id,
            'day' => $day,
            'class_subject_id' => $class_subject_j122_bm->id,
            'period_id' => $period2_period_group2->id,
            'placeholder' => null,
            'attendance_from' => '08:30',
            'attendance_to' => '09:00',
        ]);
        $timeslot3 = Timeslot::factory()->create([
            'timetable_id' => $main_j122_timetable->id,
            'day' => $day,
            'class_subject_id' => null,
            'period_id' => $period3_period_group2->id,
            'placeholder' => 'ENGLISH',
            'attendance_from' => '09:00',
            'attendance_to' => '10:00',
        ]);
        $timeslot4 = Timeslot::factory()->create([
            'timetable_id' => $main_j122_timetable->id,
            'day' => $day,
            'class_subject_id' => $class_subject_j122_math->id,
            'period_id' => $period4_period_group2->id,
            'placeholder' => null,
            'attendance_from' => '10:00',
            'attendance_to' => '10:30',
        ]);
        $timeslot5 = Timeslot::factory()->create([
            'timetable_id' => $main_j122_timetable->id,
            'day' => $day,
            'class_subject_id' => null,
            'period_id' => $period5_period_group2->id,
            'placeholder' => 'COCU',
            'attendance_from' => '10:30',
            'attendance_to' => '12:00',
        ]);
        $timeslot7 = Timeslot::factory()->create([
            'timetable_id' => $cocu_2_timetable->id,
            'day' => $day,
            'class_subject_id' => $class_subject_chess_club,
            'period_id' => $period_cocu2->id,
            'placeholder' => null,
            'attendance_from' => '10:30',
            'attendance_to' => '12:30',
        ]);

        TimeslotTeacher::factory()->create([
            'timeslot_id' => $timeslot1->id,
            'employee_id' => $employee1->id,
        ]);
        TimeslotTeacher::factory()->create([
            'timeslot_id' => $timeslot2->id,
            'employee_id' => $employee1->id,
        ]);
        TimeslotTeacher::factory()->create([
            'timeslot_id' => $timeslot4->id,
            'employee_id' => $employee1->id,
        ]);
        TimeslotTeacher::factory()->create([
            'timeslot_id' => $timeslot7->id,
            'employee_id' => $employee2->id,
        ]);

    }

    PeriodLabel::factory()->create([
        'period_group_id' => $period_group1_main->id,
        'period' => 1,
        'is_attendance_required' => true,
    ]);
    PeriodLabel::factory()->create([
        'period_group_id' => $period_group2_main->id,
        'period' => 1,
        'is_attendance_required' => true,
    ]);
    PeriodLabel::factory()->create([
        'period_group_id' => $period_group1_main->id,
        'period' => 2,
        'is_attendance_required' => true,
    ]);
    PeriodLabel::factory()->create([
        'period_group_id' => $period_group2_main->id,
        'period' => 2,
        'is_attendance_required' => true,
    ]);
    PeriodLabel::factory()->create([
        'period_group_id' => $period_group1_main->id,
        'period' => 3,
        'is_attendance_required' => true,
    ]);
    PeriodLabel::factory()->create([
        'period_group_id' => $period_group2_main->id,
        'period' => 3,
        'is_attendance_required' => true,
    ]);
    PeriodLabel::factory()->create([
        'period_group_id' => $period_group1_main->id,
        'period' => 4,
        'is_attendance_required' => true,
    ]);
    PeriodLabel::factory()->create([
        'period_group_id' => $period_group2_main->id,
        'period' => 4,
        'is_attendance_required' => true,
    ]);
    PeriodLabel::factory()->create([
        'period_group_id' => $period_group1_main->id,
        'period' => 5,
        'is_attendance_required' => true,
    ]);
    PeriodLabel::factory()->create([
        'period_group_id' => $period_group2_main->id,
        'period' => 5,
        'is_attendance_required' => true,
    ]);
    PeriodLabel::factory()->create([
        'period_group_id' => $period_group_english->id,
        'period' => 3,
        'is_attendance_required' => true,
    ]);
    PeriodLabel::factory()->create([
        'period_group_id' => $period_group_cocu1->id,
        'period' => 5,
        'is_attendance_required' => true,
    ]);
    PeriodLabel::factory()->create([
        'period_group_id' => $period_group_cocu2->id,
        'period' => 5,
        'is_attendance_required' => true,
    ]);

    // assume following periods for student1  (attendance time from 8am to 11:30am)
    // period 1 8am - 8:30am = J111 Math
    // period 2 8:30am - 9am = J111 Math
    // period 3 9am - 10am = <Placeholder for EN> & ENG1
    // period 4 10am - 10:30am = J111 Science
    // period 5 10:30am - 12pm = <Placeholder for Cocu>  & Cocu 1 (ends at 11:30am)

    StudentClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'semester_class_id' => $semester_class1->id,
        'student_id' => $student1->id,
        'class_type' => ClassType::PRIMARY
    ]);
    StudentClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'semester_class_id' => $semester_class2->id,
        'student_id' => $student1->id,
        'class_type' => ClassType::ELECTIVE,
    ]);
    StudentClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'semester_class_id' => $semester_class3->id,
        'student_id' => $student1->id,
        'class_type' => ClassType::SOCIETY,
    ]);

    // assume following periods for student2 (attendance time from 7:30am to 12:30pm)
    // period 1 7:30am - 8:30am = J122 BM
    // period 2 8:30am - 9am = J122 BM
    // period 3 9am - 10am = <Placeholder for EN> & ENG1
    // period 4 10am - 10:30am = J122 Math
    // period 5 10:30am - 12pm = <Placeholder for Cocu>  & Cocu 2 (ends at 12:30pm)
    StudentClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'semester_class_id' => $semester_class5->id,
        'student_id' => $student2->id,
        'class_type' => ClassType::PRIMARY
    ]);
    StudentClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'semester_class_id' => $semester_class2->id,
        'student_id' => $student2->id,
        'class_type' => ClassType::ELECTIVE,
    ]);
    StudentClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'semester_class_id' => $semester_class4->id,
        'student_id' => $student2->id,
        'class_type' => ClassType::SOCIETY,
    ]);

    StudentTimetable::refreshViewTable(false);

    // without override
    // data in mon and tue only.
    $data = app()->make(StudentTimetableService::class)
        ->setSimpleOutput(true)
        ->getAttendancePeriods('2025-01-27', '2025-01-29');

    expect($data)->toHaveCount(6)
        ->and($data->toArray())->toContain(
            [
                'period' => '2025-01-27',
                'day' => Day::MONDAY->value,
                'userable_id' => $student1->id,
                'userable_type' => Student::class,
                'userable_name' => $student1->getFormattedTranslations('name'),
                'userable_number' => $student1->student_number,
                'card_number' => (string) $card->card_number,
                '_default_attendance_from' => '08:00:00',
                '_default_attendance_to' => '11:30:00',
                'attendance_from' => '08:00:00',
                'attendance_to' => '11:30:00',
                '_attendance_from_overwritten_by' => null,
                '_attendance_to_overwritten_by' => null,
                'img_url' => null,
            ],
            [
                'period' => '2025-01-28',
                'day' => Day::TUESDAY->value,
                'userable_id' => $student1->id,
                'userable_type' => Student::class,
                'userable_name' => $student1->getFormattedTranslations('name'),
                'userable_number' => $student1->student_number,
                'card_number' => (string) $card->card_number,
                '_default_attendance_from' => '08:00:00',
                '_default_attendance_to' => '11:30:00',
                'attendance_from' => '08:00:00',
                'attendance_to' => '11:30:00',
                '_attendance_from_overwritten_by' => null,
                '_attendance_to_overwritten_by' => null,
                'img_url' => null,
            ],
            [
                'period' => '2025-01-27',
                'day' => Day::MONDAY->value,
                'userable_id' => $student2->id,
                'userable_type' => Student::class,
                'userable_name' => $student2->getFormattedTranslations('name'),
                'userable_number' => $student2->student_number,
                'card_number' => null,
                '_default_attendance_from' => '07:30:00',
                '_default_attendance_to' => '12:30:00',
                'attendance_from' => '07:30:00',
                'attendance_to' => '12:30:00',
                '_attendance_from_overwritten_by' => null,
                '_attendance_to_overwritten_by' => null,
                'img_url' => null,
            ],
            [
                'period' => '2025-01-28',
                'day' => Day::TUESDAY->value,
                'userable_id' => $student2->id,
                'userable_type' => Student::class,
                'userable_name' => $student2->getFormattedTranslations('name'),
                'userable_number' => $student2->student_number,
                'card_number' => null,
                '_default_attendance_from' => '07:30:00',
                '_default_attendance_to' => '12:30:00',
                'attendance_from' => '07:30:00',
                'attendance_to' => '12:30:00',
                '_attendance_from_overwritten_by' => null,
                '_attendance_to_overwritten_by' => null,
                'img_url' => null,
            ],
            // only created periods for Monday and Tuesday
            [
                'period' => '2025-01-29',
                'day' => Day::WEDNESDAY->value,
                'userable_id' => $student1->id,
                'userable_type' => Student::class,
                'userable_name' => $student1->getFormattedTranslations('name'),
                'userable_number' => $student1->student_number,
                'card_number' => (string) $card->card_number,
                '_default_attendance_from' => null,
                '_default_attendance_to' => null,
                'attendance_from' => null,
                'attendance_to' => null,
                '_attendance_from_overwritten_by' => null,
                '_attendance_to_overwritten_by' => null,
                'img_url' => null,
            ],
            [
                'period' => '2025-01-29',
                'day' => Day::WEDNESDAY->value,
                'userable_id' => $student2->id,
                'userable_type' => Student::class,
                'userable_name' => $student2->getFormattedTranslations('name'),
                'userable_number' => $student2->student_number,
                'card_number' => null,
                '_default_attendance_from' => null,
                '_default_attendance_to' => null,
                'attendance_from' => null,
                'attendance_to' => null,
                '_attendance_from_overwritten_by' => null,
                '_attendance_to_overwritten_by' => null,
                'img_url' => null,
            ],
        );

    // with school override
    // data in mon only.
    $school_override_attendance_from = SchoolAttendancePeriodOverride::factory()->create([
        'from' => '2025-01-27',
        'to' => '2025-01-27',
        'remarks' => 'test 01-27',
        'priority' => 1,
        'attendance_from' => '10:00:00',
        'attendance_to' => null,
    ]);

    $school_override_attendance_to = SchoolAttendancePeriodOverride::factory()->create([
        'from' => '2025-01-28',
        'to' => '2025-01-28',
        'remarks' => 'test 01-28 priority 1',
        'priority' => 1,
        'attendance_from' => null,
        'attendance_to' => '15:00:00',
    ]);

    SchoolAttendancePeriodOverride::factory()->create([
        'from' => '2025-01-28',
        'to' => '2025-01-28',
        'remarks' => 'test 01-28 priority 2',
        'priority' => 2,
        'attendance_from' => '11:00:00',
        'attendance_to' => '16:00:00',
    ]);

    $data = app()->make(StudentTimetableService::class)
        ->setSimpleOutput(true)
        ->getAttendancePeriods('2025-01-27', '2025-01-28');

    expect($data)->toHaveCount(4)
        ->and($data->toArray())->toMatchArray([
            [
                'period' => '2025-01-27',
                'day' => Day::MONDAY->value,
                'userable_id' => $student1->id,
                'userable_type' => Student::class,
                '_default_attendance_from' => '08:00:00',
                '_default_attendance_to' => '11:30:00',
                'attendance_from' => '10:00:00',
                'attendance_to' => '11:30:00',
                '_attendance_from_overwritten_by' => $school_override_attendance_from->fresh(),
                '_attendance_to_overwritten_by' => null,
                'userable_name' => $student1->getFormattedTranslations('name'),
                'userable_number' => $student1->student_number,
                'card_number' => (string) $card->card_number,
                'img_url' => null,
            ],
            [
                'period' => '2025-01-27',
                'day' => Day::MONDAY->value,
                'userable_id' => $student2->id,
                'userable_type' => Student::class,
                '_default_attendance_from' => '07:30:00',
                '_default_attendance_to' => '12:30:00',
                'attendance_from' => '10:00:00',
                'attendance_to' => '12:30:00',
                '_attendance_from_overwritten_by' => $school_override_attendance_from->fresh(),
                '_attendance_to_overwritten_by' => null,
                'userable_name' => $student2->getFormattedTranslations('name'),
                'userable_number' => $student2->student_number,
                'card_number' => null,
                'img_url' => null,
            ],
            [
                'period' => '2025-01-28',
                'day' => Day::TUESDAY->value,
                'userable_id' => $student1->id,
                'userable_type' => Student::class,
                '_default_attendance_from' => '08:00:00',
                '_default_attendance_to' => '11:30:00',
                'attendance_from' => '08:00:00',
                'attendance_to' => '15:00:00',
                '_attendance_from_overwritten_by' => null,
                '_attendance_to_overwritten_by' => $school_override_attendance_to->fresh(),
                'userable_name' => $student1->getFormattedTranslations('name'),
                'userable_number' => $student1->student_number,
                'card_number' => (string) $card->card_number,
                'img_url' => null,
            ],
            [
                'period' => '2025-01-28',
                'day' => Day::TUESDAY->value,
                'userable_id' => $student2->id,
                'userable_type' => Student::class,
                '_default_attendance_from' => '07:30:00',
                '_default_attendance_to' => '12:30:00',
                'attendance_from' => '07:30:00',
                'attendance_to' => '15:00:00',
                '_attendance_from_overwritten_by' => null,
                '_attendance_to_overwritten_by' => $school_override_attendance_to->fresh(),
                'userable_name' => $student2->getFormattedTranslations('name'),
                'userable_number' => $student2->student_number,
                'card_number' => null,
                'img_url' => null,
            ],
        ]);


    // with override (2025-01-27 student1 09:00 to 10:00,
    // 2025-01-27 student2 09:00 to 10:00,
    // 2025-01-28 student1 09:00 to 11:00
    // 2025-02-04 student1 10:00 to 14:00)
    // data in mon and tue only.

    $override1 = AttendancePeriodOverride::factory()->create([
        'attendance_recordable_type' => Student::class,
        'attendance_recordable_id' => $student1->id,
        'period' => '2025-01-27',
        'attendance_from' => '09:00:00',
        'attendance_to' => '10:00:00',
    ]);
    $override2 = AttendancePeriodOverride::factory()->create([
        'attendance_recordable_type' => Student::class,
        'attendance_recordable_id' => $student2->id,
        'period' => '2025-01-27',
        'attendance_from' => '09:00:00',
        'attendance_to' => '10:00:00',
    ]);
    $override3 = AttendancePeriodOverride::factory()->create([
        'attendance_recordable_type' => Student::class,
        'attendance_recordable_id' => $student1->id,
        'period' => '2025-01-28',
        'attendance_from' => '09:00:00',
        'attendance_to' => '11:00:00',
    ]);
    $override4 = AttendancePeriodOverride::factory()->create([
        'attendance_recordable_type' => Student::class,
        'attendance_recordable_id' => $student1->id,
        'period' => '2025-02-04',
        'attendance_from' => '10:00:00',
        'attendance_to' => '14:00:00',
    ]);

    // get 2 weeks of data (mon, tue, mon, tue)
    // only first set mon, tue has override
    $data = app()->make(StudentTimetableService::class)
        ->setSimpleOutput(true)
        ->getAttendancePeriods('2025-01-27', '2025-02-05');

    expect($data)->toHaveCount(20)
        ->and($data->toArray())->toMatchArray([
            [
                'period' => '2025-01-27',
                'day' => Day::MONDAY->value,
                'userable_id' => $student1->id,
                'userable_type' => Student::class,
                '_default_attendance_from' => '08:00:00',
                '_default_attendance_to' => '11:30:00',
                'attendance_from' => '09:00:00',
                'attendance_to' => '10:00:00',
                '_attendance_from_overwritten_by' => $override1->fresh(),
                '_attendance_to_overwritten_by' => $override1->fresh(),
                'userable_name' => $student1->getFormattedTranslations('name'),
                'userable_number' => $student1->student_number,
                'card_number' => (string) $card->card_number,
                'img_url' => null,
            ],
            [
                'period' => '2025-01-27',
                'day' => Day::MONDAY->value,
                'userable_id' => $student2->id,
                'userable_type' => Student::class,
                '_default_attendance_from' => '07:30:00',
                '_default_attendance_to' => '12:30:00',
                'attendance_from' => '09:00:00',
                'attendance_to' => '10:00:00',
                '_attendance_from_overwritten_by' => $override2->fresh(),
                '_attendance_to_overwritten_by' => $override2->fresh(),
                'userable_name' => $student2->getFormattedTranslations('name'),
                'userable_number' => $student2->student_number,
                'card_number' => null,
                'img_url' => null,
            ],
            [
                'period' => '2025-01-28',
                'day' => Day::TUESDAY->value,
                'userable_id' => $student1->id,
                'userable_type' => Student::class,
                '_default_attendance_from' => '08:00:00',
                '_default_attendance_to' => '11:30:00',
                'attendance_from' => '09:00:00',
                'attendance_to' => '11:00:00',
                '_attendance_from_overwritten_by' => $override3->fresh(),
                '_attendance_to_overwritten_by' => $override3->fresh(),
                'userable_name' => $student1->getFormattedTranslations('name'),
                'userable_number' => $student1->student_number,
                'card_number' => (string) $card->card_number,
                'img_url' => null,
            ],
            [
                'period' => '2025-01-28',
                'day' => Day::TUESDAY->value,
                'userable_id' => $student2->id,
                'userable_type' => Student::class,
                '_default_attendance_from' => '07:30:00',
                '_default_attendance_to' => '12:30:00',
                'attendance_from' => '07:30:00',
                'attendance_to' => '15:00:00',
                '_attendance_from_overwritten_by' => null,
                '_attendance_to_overwritten_by' => $school_override_attendance_to->fresh(),
                'userable_name' => $student2->getFormattedTranslations('name'),
                'userable_number' => $student2->student_number,
                'card_number' => null,
                'img_url' => null,
            ],
            [
                'period' => '2025-01-29',
                'day' => Day::WEDNESDAY->value,
                'userable_id' => $student1->id,
                'userable_type' => Student::class,
                '_default_attendance_from' => null,
                '_default_attendance_to' => null,
                'attendance_from' => null,
                'attendance_to' => null,
                '_attendance_from_overwritten_by' => null,
                '_attendance_to_overwritten_by' => null,
                'userable_name' => $student1->getFormattedTranslations('name'),
                'userable_number' => $student1->student_number,
                'card_number' => (string) $card->card_number,
                'img_url' => null,
            ],
            [
                'period' => '2025-01-29',
                'day' => Day::WEDNESDAY->value,
                'userable_id' => $student2->id,
                'userable_type' => Student::class,
                '_default_attendance_from' => null,
                '_default_attendance_to' => null,
                'attendance_from' => null,
                'attendance_to' => null,
                '_attendance_from_overwritten_by' => null,
                '_attendance_to_overwritten_by' => null,
                'userable_name' => $student2->getFormattedTranslations('name'),
                'userable_number' => $student2->student_number,
                'card_number' => null,
                'img_url' => null,
            ],
            [
                'period' => '2025-01-30',
                'day' => Day::THURSDAY->value,
                'userable_id' => $student1->id,
                'userable_type' => Student::class,
                '_default_attendance_from' => null,
                '_default_attendance_to' => null,
                'attendance_from' => null,
                'attendance_to' => null,
                '_attendance_from_overwritten_by' => null,
                '_attendance_to_overwritten_by' => null,
                'userable_name' => $student1->getFormattedTranslations('name'),
                'userable_number' => $student1->student_number,
                'card_number' => (string) $card->card_number,
                'img_url' => null,
            ],
            [
                'period' => '2025-01-30',
                'day' => Day::THURSDAY->value,
                'userable_id' => $student2->id,
                'userable_type' => Student::class,
                '_default_attendance_from' => null,
                '_default_attendance_to' => null,
                'attendance_from' => null,
                'attendance_to' => null,
                '_attendance_from_overwritten_by' => null,
                '_attendance_to_overwritten_by' => null,
                'userable_name' => $student2->getFormattedTranslations('name'),
                'userable_number' => $student2->student_number,
                'card_number' => null,
                'img_url' => null,
            ],
            [
                'period' => '2025-01-31',
                'day' => Day::FRIDAY->value,
                'userable_id' => $student1->id,
                'userable_type' => Student::class,
                '_default_attendance_from' => null,
                '_default_attendance_to' => null,
                'attendance_from' => null,
                'attendance_to' => null,
                '_attendance_from_overwritten_by' => null,
                '_attendance_to_overwritten_by' => null,
                'userable_name' => $student1->getFormattedTranslations('name'),
                'userable_number' => $student1->student_number,
                'card_number' => (string) $card->card_number,
                'img_url' => null,
            ],
            [
                'period' => '2025-01-31',
                'day' => Day::FRIDAY->value,
                'userable_id' => $student2->id,
                'userable_type' => Student::class,
                '_default_attendance_from' => null,
                '_default_attendance_to' => null,
                'attendance_from' => null,
                'attendance_to' => null,
                '_attendance_from_overwritten_by' => null,
                '_attendance_to_overwritten_by' => null,
                'userable_name' => $student2->getFormattedTranslations('name'),
                'userable_number' => $student2->student_number,
                'card_number' => null,
                'img_url' => null,
            ],
            [
                'period' => '2025-02-01',
                'day' => Day::SATURDAY->value,
                'userable_id' => $student1->id,
                'userable_type' => Student::class,
                '_default_attendance_from' => null,
                '_default_attendance_to' => null,
                'attendance_from' => null,
                'attendance_to' => null,
                '_attendance_from_overwritten_by' => null,
                '_attendance_to_overwritten_by' => null,
                'userable_name' => $student1->getFormattedTranslations('name'),
                'userable_number' => $student1->student_number,
                'card_number' => (string) $card->card_number,
                'img_url' => null,
            ],
            [
                'period' => '2025-02-01',
                'day' => Day::SATURDAY->value,
                'userable_id' => $student2->id,
                'userable_type' => Student::class,
                '_default_attendance_from' => null,
                '_default_attendance_to' => null,
                'attendance_from' => null,
                'attendance_to' => null,
                '_attendance_from_overwritten_by' => null,
                '_attendance_to_overwritten_by' => null,
                'userable_name' => $student2->getFormattedTranslations('name'),
                'userable_number' => $student2->student_number,
                'card_number' => null,
                'img_url' => null,
            ],
            [
                'period' => '2025-02-02',
                'day' => Day::SUNDAY->value,
                'userable_id' => $student1->id,
                'userable_type' => Student::class,
                '_default_attendance_from' => null,
                '_default_attendance_to' => null,
                'attendance_from' => null,
                'attendance_to' => null,
                '_attendance_from_overwritten_by' => null,
                '_attendance_to_overwritten_by' => null,
                'userable_name' => $student1->getFormattedTranslations('name'),
                'userable_number' => $student1->student_number,
                'card_number' => (string) $card->card_number,
                'img_url' => null,
            ],
            [
                'period' => '2025-02-02',
                'day' => Day::SUNDAY->value,
                'userable_id' => $student2->id,
                'userable_type' => Student::class,
                '_default_attendance_from' => null,
                '_default_attendance_to' => null,
                'attendance_from' => null,
                'attendance_to' => null,
                '_attendance_from_overwritten_by' => null,
                '_attendance_to_overwritten_by' => null,
                'userable_name' => $student2->getFormattedTranslations('name'),
                'userable_number' => $student2->student_number,
                'card_number' => null,
                'img_url' => null,
            ],
            [
                'period' => '2025-02-03',
                'day' => Day::MONDAY->value,
                'userable_id' => $student1->id,
                'userable_type' => Student::class,
                '_default_attendance_from' => '08:00:00',
                '_default_attendance_to' => '11:30:00',
                'attendance_from' => '08:00:00',
                'attendance_to' => '11:30:00',
                '_attendance_from_overwritten_by' => null,
                '_attendance_to_overwritten_by' => null,
                'userable_name' => $student1->getFormattedTranslations('name'),
                'userable_number' => $student1->student_number,
                'card_number' => (string) $card->card_number,
                'img_url' => null,
            ],
            [
                'period' => '2025-02-03',
                'day' => Day::MONDAY->value,
                'userable_id' => $student2->id,
                'userable_type' => Student::class,
                '_default_attendance_from' => '07:30:00',
                '_default_attendance_to' => '12:30:00',
                'attendance_from' => '07:30:00',
                'attendance_to' => '12:30:00',
                '_attendance_from_overwritten_by' => null,
                '_attendance_to_overwritten_by' => null,
                'userable_name' => $student2->getFormattedTranslations('name'),
                'userable_number' => $student2->student_number,
                'card_number' => null,
                'img_url' => null,
            ],
            [
                'period' => '2025-02-04',
                'day' => Day::TUESDAY->value,
                'userable_id' => $student1->id,
                'userable_type' => Student::class,
                '_default_attendance_from' => '08:00:00',
                '_default_attendance_to' => '11:30:00',
                'attendance_from' => '10:00:00',
                'attendance_to' => '14:00:00',
                '_attendance_from_overwritten_by' => $override4->fresh(),
                '_attendance_to_overwritten_by' => $override4->fresh(),
                'userable_name' => $student1->getFormattedTranslations('name'),
                'userable_number' => $student1->student_number,
                'card_number' => (string) $card->card_number,
                'img_url' => null,
            ],
            [
                'period' => '2025-02-04',
                'day' => Day::TUESDAY->value,
                'userable_id' => $student2->id,
                'userable_type' => Student::class,
                '_default_attendance_from' => '07:30:00',
                '_default_attendance_to' => '12:30:00',
                'attendance_from' => '07:30:00',
                'attendance_to' => '12:30:00',
                '_attendance_from_overwritten_by' => null,
                '_attendance_to_overwritten_by' => null,
                'userable_name' => $student2->getFormattedTranslations('name'),
                'userable_number' => $student2->student_number,
                'card_number' => null,
                'img_url' => null,
            ],
            [
                'period' => '2025-02-05',
                'day' => Day::WEDNESDAY->value,
                'userable_id' => $student1->id,
                'userable_type' => Student::class,
                '_default_attendance_from' => null,
                '_default_attendance_to' => null,
                'attendance_from' => null,
                'attendance_to' => null,
                '_attendance_from_overwritten_by' => null,
                '_attendance_to_overwritten_by' => null,
                'userable_name' => $student1->getFormattedTranslations('name'),
                'userable_number' => $student1->student_number,
                'card_number' => (string) $card->card_number,
                'img_url' => null,
            ],
            [
                'period' => '2025-02-05',
                'day' => Day::WEDNESDAY->value,
                'userable_id' => $student2->id,
                'userable_type' => Student::class,
                '_default_attendance_from' => null,
                '_default_attendance_to' => null,
                'attendance_from' => null,
                'attendance_to' => null,
                '_attendance_from_overwritten_by' => null,
                '_attendance_to_overwritten_by' => null,
                'userable_name' => $student2->getFormattedTranslations('name'),
                'userable_number' => $student2->student_number,
                'card_number' => null,
                'img_url' => null,
            ],
        ]);

    // with timeslot override
    AttendancePeriodOverride::truncate();
    SchoolAttendancePeriodOverride::truncate();

    // no class no timeslot
    $student3 = Student::factory()->create();
    $card3 = Card::factory()->create([
        'userable_type' => Student::class,
        'userable_id' => $student3->id,
        'card_type' => CardType::PROXIMITY->value,
        'status' => CardStatus::ACTIVE->value,
    ]);
    TimeslotOverride::factory()->create([
        'student_id' => $student3->id,
        'date' => '2025-01-27',
        'period' => 1,
        'attendance_from' => '09:00:00',
        'attendance_to' => '11:00:00',
        'is_empty' => false,
    ]);
    TimeslotOverride::factory()->create([
        'student_id' => $student3->id,
        'date' => '2025-01-27',
        'period' => 2,
        'attendance_from' => '11:00:00',
        'attendance_to' => '12:00:00',
        'is_empty' => false,
    ]);

    $data = app()->make(StudentTimetableService::class)
        ->setStudentIds([$student1->id, $student3->id])
        ->setSimpleOutput(true)
        ->getAttendancePeriods('2025-01-27', '2025-01-27');

    expect($data)->toHaveCount(2)
        ->and($data->toArray())->toMatchArray([
            [
                'period' => '2025-01-27',
                'day' => Day::MONDAY->value,
                'userable_id' => $student1->id,
                'userable_type' => Student::class,
                'userable_name' => $student1->getFormattedTranslations('name'),
                'userable_number' => $student1->student_number,
                'card_number' => (string) $card->card_number,
                'img_url' => null,
                '_default_attendance_from' => '08:00:00',
                '_default_attendance_to' => '11:30:00',
                'attendance_from' => '08:00:00',
                'attendance_to' => '11:30:00',
                '_attendance_from_overwritten_by' => null,
                '_attendance_to_overwritten_by' => null,
            ],
            [
                'period' => '2025-01-27',
                'day' => Day::MONDAY->value,
                'userable_id' => $student3->id,
                'userable_type' => Student::class,
                'userable_name' => $student3->getFormattedTranslations('name'),
                'userable_number' => $student3->student_number,
                'card_number' => (string) $card3->card_number,
                'img_url' => null,
                '_default_attendance_from' => '09:00:00',
                '_default_attendance_to' => '12:00:00',
                'attendance_from' => '09:00:00',
                'attendance_to' => '12:00:00',
                '_attendance_from_overwritten_by' => null,
                '_attendance_to_overwritten_by' => null,
            ],
        ]);

    $student5 = Student::factory()->create();
    $timeslot_override = TimeslotOverride::factory()->create([
        'student_id' => $student5->id,
        'date' => '2025-01-27',
        'period' => 1,
        'placeholder' => 'empty',
        'inherit_from_school_attendance' => false,
        'class_attendance_required' => false,
        'is_empty' => true,
    ]);
    $data = app()->make(StudentTimetableService::class)
        ->setStudentIds([$student5->id])
        ->setSimpleOutput(true)
        ->getAttendancePeriods('2025-01-27', '2025-01-27');

    expect($data)->toHaveCount(1)
        ->and($data->toArray())->toMatchArray([
            [
                'period' => '2025-01-27',
                'day' => Day::MONDAY->value,
                'userable_id' => $student5->id,
                'userable_type' => Student::class,
                'userable_name' => $student5->getFormattedTranslations('name'),
                'userable_number' => $student5->student_number,
                'card_number' => null,
                'img_url' => null,
                '_default_attendance_from' => null,
                '_default_attendance_to' => null,
                'attendance_from' => null,
                'attendance_to' => null,
                '_attendance_from_overwritten_by' => null,
                '_attendance_to_overwritten_by' => null,
            ],
        ]);
});


test('getOverride', function () {

    $student1 = Student::factory()->create();
    $student2 = Student::factory()->create();

    AttendancePeriodOverride::factory()->create([
        'attendance_recordable_type' => Student::class,
        'attendance_recordable_id' => $student1->id,
        'period' => '2025-01-25',
        'attendance_from' => '07:00:00',
        'attendance_to' => '12:00:00',
    ]);
    AttendancePeriodOverride::factory()->create([
        'attendance_recordable_type' => Student::class,
        'attendance_recordable_id' => $student1->id,
        'period' => '2025-01-26',
        'attendance_from' => '08:00:00',
        'attendance_to' => '10:00:00',
    ]);
    AttendancePeriodOverride::factory()->create([
        'attendance_recordable_type' => Student::class,
        'attendance_recordable_id' => $student2->id,
        'period' => '2025-01-25',
        'attendance_from' => '07:00:00',
        'attendance_to' => '12:30:00',
    ]);

    $data = app()->make(StudentTimetableService::class)
        ->getOverride('2025-01-01', '2025-01-10');

    expect($data)->toBeEmpty();


    $data = app()->make(StudentTimetableService::class)
        ->getOverride('2025-01-20', '2025-01-25');

    expect($data)->toHaveCount(1)
        ->and($data['2025-01-25'])->toHaveCount(2)
        ->and($data['2025-01-25'][$student1->id]->toArray())->toMatchArray([
            'attendance_recordable_type' => Student::class,
            'attendance_recordable_id' => $student1->id,
            'period' => '2025-01-25',
            'attendance_from' => '07:00:00',
            'attendance_to' => '12:00:00',
        ])
        ->and($data['2025-01-25'][$student2->id]->toArray())->toMatchArray([
            'attendance_recordable_type' => Student::class,
            'attendance_recordable_id' => $student2->id,
            'period' => '2025-01-25',
            'attendance_from' => '07:00:00',
            'attendance_to' => '12:30:00',
        ]);


    $data = app()->make(StudentTimetableService::class)
        ->getOverride('2025-01-25', '2025-01-26');

    expect($data)->toHaveCount(2)
        ->and($data['2025-01-25'])->toHaveCount(2)
        ->and($data['2025-01-25'][$student1->id]->toArray())->toMatchArray([
            'attendance_recordable_type' => Student::class,
            'attendance_recordable_id' => $student1->id,
            'period' => '2025-01-25',
            'attendance_from' => '07:00:00',
            'attendance_to' => '12:00:00',
        ])
        ->and($data['2025-01-25'][$student2->id]->toArray())->toMatchArray([
            'attendance_recordable_type' => Student::class,
            'attendance_recordable_id' => $student2->id,
            'period' => '2025-01-25',
            'attendance_from' => '07:00:00',
            'attendance_to' => '12:30:00',
        ])
        ->and($data['2025-01-26'])->toHaveCount(1)
        ->and($data['2025-01-26'][$student1->id]->toArray())->toMatchArray([
            'attendance_recordable_type' => Student::class,
            'attendance_recordable_id' => $student1->id,
            'period' => '2025-01-26',
            'attendance_from' => '08:00:00',
            'attendance_to' => '10:00:00',
        ]);

});

test('hasIndividualOverrideOn', function () {

    $student1 = Student::factory()->create();
    $student2 = Student::factory()->create();

    AttendancePeriodOverride::factory()->create([
        'attendance_recordable_type' => Student::class,
        'attendance_recordable_id' => $student1->id,
        'period' => '2025-01-25',
        'attendance_from' => '07:00:00',
        'attendance_to' => '12:00:00',
    ]);
    AttendancePeriodOverride::factory()->create([
        'attendance_recordable_type' => Student::class,
        'attendance_recordable_id' => $student1->id,
        'period' => '2025-01-26',
        'attendance_from' => '08:00:00',
        'attendance_to' => '10:00:00',
    ]);
    AttendancePeriodOverride::factory()->create([
        'attendance_recordable_type' => Student::class,
        'attendance_recordable_id' => $student2->id,
        'period' => '2025-01-25',
        'attendance_from' => '07:00:00',
        'attendance_to' => '12:30:00',
    ]);

    $data = app()->make(StudentTimetableService::class)
        ->hasIndividualOverrideOn($student1, '2025-01-01');

    expect($data)->toBeFalse();


    $data = app()->make(StudentTimetableService::class)
        ->hasIndividualOverrideOn($student1, '2025-01-27');

    expect($data)->toBeFalse();


    $data = app()->make(StudentTimetableService::class)
        ->hasIndividualOverrideOn($student1, '2025-01-25');

    expect($data)->toBeTrue();

    $data = app()->make(StudentTimetableService::class)
        ->hasIndividualOverrideOn($student1, '2025-01-26');

    expect($data)->toBeTrue();
});

test('getTimetableWithTimeslotOverrideByDate', function () {
    $employee1 = Employee::factory()->create([
        'name->en' => 'Teacher A',
    ]);

    $semester_setting = SemesterSetting::factory()->create();

    $main_class_j111 = ClassModel::factory()->create([
        'name->en' => 'J111',
        'name->zh' => '初一11',
    ]);

    $semester_class1 = SemesterClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'class_id' => $main_class_j111->id,
    ]);

    $period_group1 = PeriodGroup::factory()->create();

    $main_j111_timetable = Timetable::factory()->create([
        'name' => 'J111 timetable',
        'is_active' => true,
        'period_group_id' => $period_group1->id,
        'semester_class_id' => $semester_class1,
    ]);

    // 班务
    $period1 = Period::factory()->create([
        'period_group_id' => $period_group1->id,
        'period' => 1,
        'from_time' => '08:00:00',
        'to_time' => '08:30:00',
        'day' => Day::MONDAY,
        'display_group' => 1,
    ]);
    $period_label = PeriodLabel::factory()->create([
        'period_group_id' => $period_group1->id,
        'period' => 1,
        'is_attendance_required' => true,
    ]);
    $period2 = Period::factory()->create([
        'period_group_id' => $period_group1->id,
        'period' => 2,
        'from_time' => '08:30:00',
        'to_time' => '09:00:00',
        'day' => Day::MONDAY,
        'display_group' => 1,
    ]);
    $period_label2 = PeriodLabel::factory()->create([
        'period_group_id' => $period_group1->id,
        'period' => 2,
        'is_attendance_required' => true,
    ]);
    // english class
    $period3 = Period::factory()->create([
        'period_group_id' => $period_group1->id,
        'period' => 3,
        'from_time' => '09:00:00',
        'to_time' => '10:00:00',
        'day' => Day::MONDAY,
        'display_group' => 1,
    ]);
    PeriodLabel::factory()->create([
        'period_group_id' => $period_group1->id,
        'period' => 3,
        'is_attendance_required' => true,
    ]);


    $subject_math = Subject::factory()->create([
        'name->en' => 'Math',
        'type' => SubjectType::MAJOR,
    ]);
    $class_subject_j111_math = ClassSubject::factory()->create([
        'semester_class_id' => $semester_class1->id,
        'subject_id' => $subject_math->id,
    ]);

    // assume following periods for student1  (attendance time from 8am to 9am)
    // period 1 8am - 8:30am = 班务
    // period 2 8:30am - 9am = J111 Math
    // period 3 9am - 10am = english (not in timetable because null class_subject + no teacher)

    // 班务
    $timeslot1 = Timeslot::factory()->create([
        'timetable_id' => $main_j111_timetable->id,
        'class_subject_id' => null,
        'period_id' => $period1->id,
        'placeholder' => '班务',
        'attendance_from' => '08:00',
        'attendance_to' => '08:30',
        'default_init_status' => PeriodAttendanceStatus::PRESENT,
        'has_mark_deduction' => false,
    ]);
    $timeslot2 = Timeslot::factory()->create([
        'timetable_id' => $main_j111_timetable->id,
        'class_subject_id' => $class_subject_j111_math->id,
        'period_id' => $period2->id,
        'placeholder' => null,
        'attendance_from' => '08:30',
        'attendance_to' => '09:00',
        'default_init_status' => PeriodAttendanceStatus::ABSENT,
        'has_mark_deduction' => true,
    ]);
    $timeslot3 = Timeslot::factory()->create([
        'timetable_id' => $main_j111_timetable->id,
        'class_subject_id' => null,
        'period_id' => $period3->id,
        'placeholder' => 'ENGLISH',
        'attendance_from' => '09:00',
        'attendance_to' => '10:00',
        'has_mark_deduction' => true,
    ]);


    TimeslotTeacher::factory()->create([
        'timeslot_id' => $timeslot1->id,
        'employee_id' => $employee1->id,
    ]);
    TimeslotTeacher::factory()->create([
        'timeslot_id' => $timeslot2->id,
        'employee_id' => $employee1->id,
    ]);

    $student1 = Student::factory()->create();
    StudentClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'semester_class_id' => $semester_class1->id,
        'student_id' => $student1->id,
        'class_type' => ClassType::PRIMARY
    ]);
    StudentTimetable::refreshViewTable(false);

    $timetable = app()->make(StudentTimetableService::class)
        ->setStudent($student1)
        ->getTimetableWithTimeslotOverrideByDate(['2025-01-27']);

    expect($timetable->toArray())->toMatchArray([
        "2025-01-27" => [
            $student1->id => [
                [
                    "student_id" => $student1->id,
                    "timetable_name" => $main_j111_timetable->name,
                    "period_group_name" => $period_group1->getTranslations('name'),
                    "timeslot_type" => Timeslot::class,
                    "timeslot_id" => $timeslot1->id,
                    "day" => "MONDAY",
                    "date" => "2025-01-27",
                    "from_time" => "08:00:00",
                    "to_time" => "08:30:00",
                    "period" => 1,
                    "attendance_from" => "08:00:00",
                    "attendance_to" => "08:30:00",
                    "placeholder" => "班务",
                    "inherit_from_school_attendance" => true,
                    "class_attendance_required" => true,
                    "is_empty" => false,
                    "default_init_status" => PeriodAttendanceStatus::PRESENT->value,
                    "period_label_id" => $period_label->id,
                    'has_mark_deduction' => false, // $timeslot1's has_mark_deduction = false
                ],
                [
                    "student_id" => $student1->id,
                    "timetable_name" => $main_j111_timetable->name,
                    "period_group_name" => $period_group1->getTranslations('name'),
                    "timeslot_type" => Timeslot::class,
                    "timeslot_id" => $timeslot2->id,
                    "day" => "MONDAY",
                    "date" => "2025-01-27",
                    "from_time" => "08:30:00",
                    "to_time" => "09:00:00",
                    "period" => 2,
                    "attendance_from" => "08:30:00",
                    "attendance_to" => "09:00:00",
                    "placeholder" => null,
                    "inherit_from_school_attendance" => true,
                    "class_attendance_required" => true,
                    "is_empty" => false,
                    "default_init_status" => PeriodAttendanceStatus::ABSENT->value,
                    "period_label_id" => $period_label2->id,
                    'has_mark_deduction' => true,
                ],
            ],
        ],
    ]);

    $summary = app()->make(StudentTimetableService::class)
        ->getAttendancePeriodForStudentAndDay($timetable['2025-01-27'][$student1->id]);

    expect($summary)->toMatchArray([
        'from' => '08:00:00',
        'to' => '09:00:00',
    ]);

    $student2 = Student::factory()->create();
    StudentClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'semester_class_id' => $semester_class1->id,
        'student_id' => $student2->id,
        'class_type' => ClassType::PRIMARY
    ]);
    $timeslot_override_student2_period1 = TimeslotOverride::factory()->create([
        'student_id' => $student2->id,
        'date' => '2025-01-27',
        'period' => 1,
        'placeholder' => 'is_empty = true',
        'attendance_from' => '08:00:00',
        'attendance_to' => '08:30:00',
        'employee_id' => null,
        'inherit_from_school_attendance' => false,
        'class_attendance_required' => false,
        'is_empty' => true,
    ]);
    $timeslot_override_student2_period3 = TimeslotOverride::factory()->create([
        'student_id' => $student2->id,
        'date' => '2025-01-27',
        'period' => 3,
        'placeholder' => 'period 3',
        'attendance_from' => '09:00:00',
        'attendance_to' => '09:30:00',
        'employee_id' => $employee1->id,
        'inherit_from_school_attendance' => false,
        'class_attendance_required' => true,
        'is_empty' => false,
    ]);

    // without timetable
    $student3 = Student::factory()->create();
    $timeslot_override_student3_period3 = TimeslotOverride::factory()->create([
        'student_id' => $student3->id,
        'date' => '2025-01-27',
        'period' => 3,
        'placeholder' => 'period 3',
        'attendance_from' => '09:00:00',
        'attendance_to' => '09:30:00',
        'employee_id' => $employee1->id,
        'inherit_from_school_attendance' => false,
        'class_attendance_required' => true,
        'is_empty' => false,
    ]);
    StudentTimetable::refreshViewTable(false);

    $timetable = app()->make(StudentTimetableService::class)
        ->setStudentIds([$student1->id, $student2->id, $student3->id])
        ->getTimetableWithTimeslotOverrideByDate(['2025-01-27', '2025-02-03']);

    expect($timetable->toArray())->toMatchArray([
        "2025-01-27" => [
            $student1->id => [
                [
                    "student_id" => $student1->id,
                    "timetable_name" => $main_j111_timetable->name,
                    "period_group_name" => $period_group1->getTranslations('name'),
                    "timeslot_type" => Timeslot::class,
                    "timeslot_id" => $timeslot1->id,
                    "day" => "MONDAY",
                    "date" => "2025-01-27",
                    "from_time" => "08:00:00",
                    "to_time" => "08:30:00",
                    "period" => 1,
                    "attendance_from" => "08:00:00",
                    "attendance_to" => "08:30:00",
                    "placeholder" => "班务",
                    "inherit_from_school_attendance" => true,
                    "class_attendance_required" => true,
                    "is_empty" => false,
                    "default_init_status" => PeriodAttendanceStatus::PRESENT->value,
                    "period_label_id" => $period_label->id,
                    'has_mark_deduction' => false, // $timeslot1's has_mark_deduction = false
                ],
                [
                    "student_id" => $student1->id,
                    "timetable_name" => $main_j111_timetable->name,
                    "period_group_name" => $period_group1->getTranslations('name'),
                    "timeslot_type" => Timeslot::class,
                    "timeslot_id" => $timeslot2->id,
                    "day" => "MONDAY",
                    "date" => "2025-01-27",
                    "from_time" => "08:30:00",
                    "to_time" => "09:00:00",
                    "period" => 2,
                    "attendance_from" => "08:30:00",
                    "attendance_to" => "09:00:00",
                    "placeholder" => null,
                    "inherit_from_school_attendance" => true,
                    "class_attendance_required" => true,
                    "is_empty" => false,
                    "default_init_status" => PeriodAttendanceStatus::ABSENT->value,
                    "period_label_id" => $period_label2->id,
                    'has_mark_deduction' => true,
                ],
            ],
            $student2->id => [
                [
                    "student_id" => $student2->id,
                    "timetable_name" => null,
                    "period_group_name" => null,
                    "timeslot_type" => TimeslotOverride::class,
                    "timeslot_id" => $timeslot_override_student2_period1->id,
                    "day" => "MONDAY",
                    "date" => "2025-01-27",
                    "from_time" => null,
                    "to_time" => null,
                    "period" => 1,
                    "attendance_from" => "08:00:00",
                    "attendance_to" => "08:30:00",
                    "placeholder" => $timeslot_override_student2_period1->placeholder,
                    "inherit_from_school_attendance" => false,
                    "class_attendance_required" => false,
                    "is_empty" => true,
                    "default_init_status" => PeriodAttendanceStatus::ABSENT->value,
                    "period_label_id" => null,
                    'has_mark_deduction' => true,
                ],
                [
                    "student_id" => $student2->id,
                    "timetable_name" => $main_j111_timetable->name,
                    "period_group_name" => $period_group1->getTranslations('name'),
                    "timeslot_type" => Timeslot::class,
                    "timeslot_id" => $timeslot2->id,
                    "day" => "MONDAY",
                    "date" => "2025-01-27",
                    "from_time" => "08:30:00",
                    "to_time" => "09:00:00",
                    "period" => 2,
                    "attendance_from" => "08:30:00",
                    "attendance_to" => "09:00:00",
                    "placeholder" => null,
                    "inherit_from_school_attendance" => true,
                    "class_attendance_required" => true,
                    "is_empty" => false,
                    "default_init_status" => PeriodAttendanceStatus::ABSENT->value,
                    "period_label_id" => $period_label2->id,
                    'has_mark_deduction' => true,
                ],
                [
                    "student_id" => $student2->id,
                    "timetable_name" => null,
                    "period_group_name" => null,
                    "timeslot_type" => TimeslotOverride::class,
                    "timeslot_id" => $timeslot_override_student2_period3->id,
                    "day" => "MONDAY",
                    "date" => "2025-01-27",
                    "from_time" => null,
                    "to_time" => null,
                    "period" => 3,
                    "attendance_from" => "09:00:00",
                    "attendance_to" => "09:30:00",
                    "placeholder" => $timeslot_override_student2_period3->placeholder,
                    "inherit_from_school_attendance" => false,
                    "class_attendance_required" => true,
                    "is_empty" => false,
                    "default_init_status" => PeriodAttendanceStatus::ABSENT->value,
                    "period_label_id" => null,
                    'has_mark_deduction' => true,
                ],
            ],
            $student3->id => [
                [
                    "student_id" => $student3->id,
                    "timetable_name" => null,
                    "period_group_name" => null,
                    "timeslot_type" => TimeslotOverride::class,
                    "timeslot_id" => $timeslot_override_student3_period3->id,
                    "day" => "MONDAY",
                    "date" => "2025-01-27",
                    "from_time" => null,
                    "to_time" => null,
                    "period" => 3,
                    "attendance_from" => "09:00:00",
                    "attendance_to" => "09:30:00",
                    "placeholder" => $timeslot_override_student3_period3->placeholder,
                    "inherit_from_school_attendance" => false,
                    "class_attendance_required" => true,
                    "is_empty" => false,
                    "default_init_status" => PeriodAttendanceStatus::ABSENT->value,
                    "period_label_id" => null,
                    'has_mark_deduction' => true,
                ],
            ],
        ],
        "2025-02-03" => [
            $student1->id => [
                [
                    "student_id" => $student1->id,
                    "timetable_name" => $main_j111_timetable->name,
                    "period_group_name" => $period_group1->getTranslations('name'),
                    "timeslot_type" => Timeslot::class,
                    "timeslot_id" => $timeslot1->id,
                    "day" => "MONDAY",
                    "date" => "2025-02-03",
                    "from_time" => "08:00:00",
                    "to_time" => "08:30:00",
                    "period" => 1,
                    "attendance_from" => "08:00:00",
                    "attendance_to" => "08:30:00",
                    "placeholder" => "班务",
                    "inherit_from_school_attendance" => true,
                    "class_attendance_required" => true,
                    "is_empty" => false,
                    "default_init_status" => PeriodAttendanceStatus::PRESENT->value,
                    "period_label_id" => $period_label->id,
                    'has_mark_deduction' => false, // $timeslot1's has_mark_deduction = false
                ],
                [
                    "student_id" => $student1->id,
                    "timetable_name" => $main_j111_timetable->name,
                    "period_group_name" => $period_group1->getTranslations('name'),
                    "timeslot_type" => Timeslot::class,
                    "timeslot_id" => $timeslot2->id,
                    "day" => "MONDAY",
                    "date" => "2025-02-03",
                    "from_time" => "08:30:00",
                    "to_time" => "09:00:00",
                    "period" => 2,
                    "attendance_from" => "08:30:00",
                    "attendance_to" => "09:00:00",
                    "placeholder" => null,
                    "inherit_from_school_attendance" => true,
                    "class_attendance_required" => true,
                    "is_empty" => false,
                    "default_init_status" => PeriodAttendanceStatus::ABSENT->value,
                    "period_label_id" => $period_label2->id,
                    'has_mark_deduction' => true,
                ],
            ],
            $student2->id => [
                [
                    "student_id" => $student2->id,
                    "timetable_name" => $main_j111_timetable->name,
                    "period_group_name" => $period_group1->getTranslations('name'),
                    "timeslot_type" => Timeslot::class,
                    "timeslot_id" => $timeslot1->id,
                    "day" => "MONDAY",
                    "date" => "2025-02-03",
                    "from_time" => "08:00:00",
                    "to_time" => "08:30:00",
                    "period" => 1,
                    "attendance_from" => "08:00:00",
                    "attendance_to" => "08:30:00",
                    "placeholder" => "班务",
                    "inherit_from_school_attendance" => true,
                    "class_attendance_required" => true,
                    "is_empty" => false,
                    "default_init_status" => PeriodAttendanceStatus::PRESENT->value,
                    "period_label_id" => $period_label->id,
                    'has_mark_deduction' => false, // $timeslot1's has_mark_deduction = false
                ],
                [
                    "student_id" => $student2->id,
                    "timetable_name" => $main_j111_timetable->name,
                    "period_group_name" => $period_group1->getTranslations('name'),
                    "timeslot_type" => Timeslot::class,
                    "timeslot_id" => $timeslot2->id,
                    "day" => "MONDAY",
                    "date" => "2025-02-03",
                    "from_time" => "08:30:00",
                    "to_time" => "09:00:00",
                    "period" => 2,
                    "attendance_from" => "08:30:00",
                    "attendance_to" => "09:00:00",
                    "placeholder" => null,
                    "inherit_from_school_attendance" => true,
                    "class_attendance_required" => true,
                    "is_empty" => false,
                    "default_init_status" => PeriodAttendanceStatus::ABSENT->value,
                    "period_label_id" => $period_label2->id,
                    'has_mark_deduction' => true,
                ],
            ],
        ],
    ]);

    $summary = app()->make(StudentTimetableService::class)
        ->setSimpleOutput(true)
        ->getAttendancePeriods('2025-01-27', '2025-01-27')
        ->keyBy('userable_id')
        ->toArray();

    expect($summary[$student1->id])->toMatchArray([
        'period' => '2025-01-27',
        'day' => 'MONDAY',
        'userable_id' => $student1->id,
        'userable_type' => Student::class,
        'userable_number' => $student1->student_number,
        'card_number' => null,
        '_default_attendance_from' => '08:30:00', // $timeslot2's attendance_from, $timeslot1's has_mark_deduction = false
        '_default_attendance_to' => '09:00:00', // $timeslot2's attendance_to, $timeslot1's has_mark_deduction = false
        'attendance_from' => '08:30:00',
        'attendance_to' => '09:00:00',
        '_attendance_from_overwritten_by' => null,
        '_attendance_to_overwritten_by' => null,
        'img_url' => null,
    ]);

    expect($summary[$student2->id])->toMatchArray([
        'period' => '2025-01-27',
        'day' => 'MONDAY',
        'userable_id' => $student2->id,
        'userable_type' => Student::class,
        'userable_number' => $student2->student_number,
        'card_number' => null,
        '_default_attendance_from' => '08:30:00', // $timeslot2's attendance_from, $timeslot1's has_mark_deduction = false
        '_default_attendance_to' => '09:30:00', // $timeslot_override_student2_period3's attendance_to
        'attendance_from' => '08:30:00',
        'attendance_to' => '09:30:00',
        '_attendance_from_overwritten_by' => null,
        '_attendance_to_overwritten_by' => null,
        'img_url' => null,
    ]);

    expect($summary[$student3->id])->toMatchArray([
        'period' => '2025-01-27',
        'day' => 'MONDAY',
        'userable_id' => $student3->id, // this student has no timetable
        'userable_type' => Student::class,
        'userable_number' => $student3->student_number,
        'card_number' => null,
        '_default_attendance_from' => '09:00:00', // $timeslot_override_student3_period3's attendance_from
        '_default_attendance_to' => '09:30:00', // $timeslot_override_student3_period3's attendance_to
        'attendance_from' => '09:00:00',
        'attendance_to' => '09:30:00',
        '_attendance_from_overwritten_by' => null,
        '_attendance_to_overwritten_by' => null,
        'img_url' => null,
    ]);

    $summary = app()->make(StudentTimetableService::class)
        ->setSimpleOutput(true)
        ->getAttendancePeriods('2025-02-03', '2025-02-03')
        ->keyBy('userable_id')
        ->toArray();

    expect($summary[$student1->id])->toMatchArray([
        'period' => '2025-02-03',
        'day' => 'MONDAY',
        'userable_id' => $student1->id,
        'userable_type' => Student::class,
        'userable_number' => $student1->student_number,
        'card_number' => null,
        '_default_attendance_from' => '08:30:00', // $timeslot2's attendance_from, $timeslot1's has_mark_deduction = false
        '_default_attendance_to' => '09:00:00', // $timeslot2's attendance_to, $timeslot1's has_mark_deduction = false
        'attendance_from' => '08:30:00',
        'attendance_to' => '09:00:00',
        '_attendance_from_overwritten_by' => null,
        '_attendance_to_overwritten_by' => null,
        'img_url' => null,
    ]);

    expect($summary[$student2->id])->toMatchArray([
        'period' => '2025-02-03',
        'day' => 'MONDAY',
        'userable_id' => $student2->id,
        'userable_type' => Student::class,
        'userable_number' => $student2->student_number,
        'card_number' => null,
        '_default_attendance_from' => '08:30:00', // $timeslot2's attendance_from, $timeslot1's has_mark_deduction = false
        '_default_attendance_to' => '09:00:00', // $timeslot2's attendance_to, $timeslot1's has_mark_deduction = false
        'attendance_from' => '08:30:00',
        'attendance_to' => '09:00:00',
        '_attendance_from_overwritten_by' => null,
        '_attendance_to_overwritten_by' => null,
        'img_url' => null,
    ]);
});
