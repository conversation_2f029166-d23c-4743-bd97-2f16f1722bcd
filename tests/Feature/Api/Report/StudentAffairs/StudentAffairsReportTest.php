<?php

use App\Enums\CompetitionBonusType;
use App\Enums\ExportType;
use App\Models\Award;
use App\Models\ClassModel;
use App\Models\Competition;
use App\Models\CompetitionRecord;
use App\Models\Department;
use App\Models\SemesterClass;
use App\Models\SemesterSetting;
use App\Models\Student;
use App\Models\StudentClass;
use App\Models\User;
use App\Services\ReportPrintService;
use Barryvdh\Snappy\Facades\SnappyPdf;
use Database\Seeders\InternationalizationSeeder;
use Database\Seeders\PermissionSeeder;
use Illuminate\Support\Facades\Hash;
use Laravel\Sanctum\Sanctum;
use Maatwebsite\Excel\Facades\Excel;
use Mockery\MockInterface;

beforeEach(function () {
    $this->seed([
        InternationalizationSeeder::class,
        PermissionSeeder::class
    ]);
    app()->setLocale('en');
    $this->withHeaders([
        'Accept-Language' => 'en'
    ]);

    $user = User::factory()->create([
        'password' => Hash::make('123456'),
    ]);
    $user->assignRole('Super Admin');
    Sanctum::actingAs($user);

    $this->routeNamePrefix = 'reports.student-affairs';

    SnappyPdf::fake();
    Excel::fake();
});

test('studentPerformanceReportByDateRange return excel', function () {
    $department = Department::factory()->create([
        'name->en' => 'department A',
        'name->zh' => '部门 A',
    ]);
    $department2 = Department::factory()->create([
        'name->en' => 'department B',
        'name->zh' => '部门 B',
    ]);

    $award2 = Award::factory()->create([
        'name->en' => 'second place',
        'name->zh' => '第二名',
        'sequence' => 1,
    ]);
    $award = Award::factory()->create([
        'name->en' => 'first place',
        'name->zh' => '第一名',
        'sequence' => 0,
    ]);

    $semester_setting = SemesterSetting::factory()->create();

    $semester_class = SemesterClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'class_id' => ClassModel::factory()->create([
            'name->en' => 'A class',
            'name->zh' => '班级 A',
        ])->id,
    ]);

    $semester_class2 = SemesterClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'class_id' => ClassModel::factory()->create([
            'name->en' => 'B class',
            'name->zh' => '班级 B',
        ])->id,
    ]);

    $student = Student::factory()->create([
        'name->en' => 'student A',
        'name->zh' => '学生 A',
        'student_number' => '0001',
    ]);
    StudentClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'semester_class_id' => $semester_class->id,
        'student_id' => $student->id,
    ]);

    $student2 = Student::factory()->create([
        'name->en' => 'student B',
        'name->zh' => '学生 B',
        'student_number' => '0002',
    ]);
    StudentClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'semester_class_id' => $semester_class2->id,
        'student_id' => $student2->id,
    ]);

    // competition with 1 record
    $first_competition = Competition::factory()->create([
        'name' => 'Lompat Galah',
        'department_id' => $department->id,
        'date' => '2025-01-08',
    ]);
    $first_competition_record = CompetitionRecord::factory()->create([
        'competition_id' => $first_competition->id,
        'award_id' => $award->id,
        'student_id' => $student->id,
        'semester_class_id' => $semester_class->id,
        'type_of_bonus' => CompetitionBonusType::PERFORMANCE->value,
        'mark' => 4,
    ]);
    // competition with 2 records
    $second_competition = Competition::factory()->create([
        'name' => 'Driving',
        'department_id' => $department2->id,
        'date' => '2025-01-09',
    ]);
    $second_competition_record = CompetitionRecord::factory()->create([
        'competition_id' => $second_competition->id,
        'award_id' => $award2->id,
        'student_id' => $student->id,
        'semester_class_id' => $semester_class->id,
        'type_of_bonus' => CompetitionBonusType::OFF_CAMPUS->value,
        'mark' => 5,
    ]);
    $second_competition_record2 = CompetitionRecord::factory()->create([
        'competition_id' => $second_competition->id,
        'award_id' => $award->id,
        'student_id' => $student2->id,
        'semester_class_id' => $semester_class2->id,
        'type_of_bonus' => CompetitionBonusType::OFF_CAMPUS->value,
        'mark' => 6,
    ]);
    
    $filters = [
        'report_language' => 'zh',
        'from_date' => '2025-01-08',
        'to_date' => '2025-01-09',
        'export_type' => ExportType::EXCEL->value
    ];
    $filename = 'student-affairs-student-performance-report-by-date-range';
    $extension = '.xlsx';

    $this->partialMock(ReportPrintService::class, function (MockInterface $mock) use ($filename, $extension) {
        $mock->shouldReceive('upload')->once()->andReturnSelf();
        $mock->shouldReceive('getFileUrl')->once()->andReturn($filename . $extension);
    });

    $response = $this->getJson(
        route($this->routeNamePrefix . '.student-performance-by-date-range', $filters)
    )->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data']['url'])->toMatch("/$filename/")
        ->toEndWith($extension);
});