<?php

use App\Enums\CardStatus;
use App\Enums\PaymentProvider;
use App\Enums\PaymentStatus;
use App\Enums\PaymentType;
use App\Enums\TaxType;
use App\Enums\WalletTransactionStatus;
use App\Enums\WalletTransactionType;
use App\Helpers\ConfigHelper;
use App\Http\Resources\CurrencyResource;
use App\Http\Resources\EnumResource;
use App\Http\Resources\SimpleUserableViewResource;
use App\Http\Resources\UserResource;
use App\Interfaces\Userable;
use App\Models\BillingDocument;
use App\Models\Card;
use App\Models\Config;
use App\Models\Currency;
use App\Models\Employee;
use App\Models\Guardian;
use App\Models\GuardianStudent;
use App\Models\PaymentMethod;
use App\Models\Student;
use App\Models\User;
use App\Models\Wallet;
use App\Models\WalletTransaction;
use App\Services\WalletService;
use Database\Seeders\GlAccountSeeder;
use Database\Seeders\InternationalizationSeeder;
use Database\Seeders\LegalEntitySeeder;
use Database\Seeders\PaymentMethodSeeder;
use Database\Seeders\PaymentTermsSeeder;
use Database\Seeders\PermissionSeeder;
use Database\Seeders\ProductSeeder;
use Database\Seeders\TaxSeeder;
use Database\Seeders\UomSeeder;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Http;
use Laravel\Sanctum\Sanctum;
use Mockery\MockInterface;

beforeEach(function () {
    $this->seed([
        InternationalizationSeeder::class,
        PermissionSeeder::class,
        ProductSeeder::class,
        TaxSeeder::class,
        UomSeeder::class,
        GlAccountSeeder::class,
        PaymentMethodSeeder::class,
        PaymentTermsSeeder::class,
        LegalEntitySeeder::class,
    ]);

    app()->setLocale('en');

    $this->withHeaders([
        'Accept-Language' => 'en'
    ]);

    $this->user = User::factory()->create([
        'password' => Hash::make('123456'),
    ]);

    $this->sender_guardian = Guardian::factory()->create([
        'user_id' => $this->user->id
    ]);

    $this->guardian_diff_user = Guardian::factory()->create();

    $this->user->assignRole('Super Admin');

    Sanctum::actingAs($this->user);

    $this->routeNamePrefix = 'users.wallets';

    $this->table = resolve(Wallet::class)->getTable();

    $this->currency = Currency::factory()->malaysiaCurrency()->create();

    $this->wallet = Wallet::factory()->create([
        'user_id' => $this->user->id,
        'currency_id' => $this->currency->id,
        'balance' => 10
    ]);
});

test('index-admin without any params', function () {
    Wallet::truncate();

    $employee = Employee::factory()->create();
    $user = $employee->user;
    $user->syncPermissions('wallet-admin-view');

    Sanctum::actingAs($user);

    $first_wallet = Wallet::factory()->withEmployee()->create();
    $second_wallet = Wallet::factory()->withStudent()->create();

    $response = $this->getJson(route('admin.wallets.index', [
        'includes' => ['userables', 'user'],
        'order_by' => ['id']
    ]));

    $response->assertStatus(200);

    expect($response->json())->toHaveSuccessGeneralResponse()
        ->and($response->json()['data'])->toHaveCount(2)
        ->sequence(
            fn($response) => $response->toEqual([
                'id' => $first_wallet->id,
                'user' => resourceToArray(new UserResource($first_wallet->user)),
                'userables' => resourceToArray(SimpleUserableViewResource::collection($first_wallet->userables)),
                'balance' => $first_wallet->balance,
                'currency' => resourceToArray(new CurrencyResource($first_wallet->currency))
            ]),
            fn($response) => $response->toEqual([
                'id' => $second_wallet->id,
                'user' => resourceToArray(new UserResource($second_wallet->user)),
                'userables' => resourceToArray(SimpleUserableViewResource::collection($second_wallet->userables)),
                'balance' => $second_wallet->balance,
                'currency' => resourceToArray(new CurrencyResource($second_wallet->currency))
            ]),
        );
});

test('index-admin: test service accepting params', function () {
    Wallet::truncate();

    $employee = Employee::factory()->create();
    $user = $employee->user;
    $user->syncPermissions('wallet-admin-view');

    Sanctum::actingAs($user);

    $first_wallet = Wallet::factory()->withEmployee()->create();
    Wallet::factory()->withEmployee()->create();

    $first_user = $first_wallet->user;
    $first_userables = $first_user->userables->where('userable_type', Employee::class)->first();

    $filters = [
        'user_name' => ['en' => $first_userables->name],
        'user_email' => $first_userables->email,
        'user_phone_number' => $first_userables->phone_number,
        'user_type' => Student::class,
        'user_number' => $first_userables->number,
        'includes' => ['userables', 'user', 'currency'],
        'order_by' => ['id' => 'asc'],
    ];

    $this->mock(WalletService::class, function (MockInterface $mock) use ($filters, $first_wallet) {
        $mock->shouldReceive('getPaginatedWallets')
            ->once()
            ->with($filters)
            ->andReturn(new LengthAwarePaginator([$first_wallet->loadMissing('userables')], 1, 1));
    });

    $response = $this->getJson(route('admin.wallets.index', $filters))->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toHaveCount(1)
        ->and($response['data'][0])->toEqual([
            'id' => $first_wallet->id,
            'user' => resourceToArray(new UserResource($first_wallet->user)),
            'userables' => resourceToArray(SimpleUserableViewResource::collection($first_wallet->userables)),
            'balance' => $first_wallet->balance,
            'currency' => resourceToArray(new CurrencyResource($first_wallet->currency))
        ]);
});

test('index-admin: test guardian_name param', function () {
    Wallet::truncate();

    $employee = Employee::factory()->create();
    $user = $employee->user;
    $user->syncPermissions('wallet-admin-view');

    Sanctum::actingAs($user);

    $student_A1 = Student::factory()->create([
        'name->en' => 'Student A1',
        'email' => '<EMAIL>',
        'phone_number' => '+60128889999',
        'student_number' => 'STUDENT-A001',
    ]);

    $guardian_A = Guardian::factory()->create([
        'name->en' => 'Father A',
        'email' => '<EMAIL>',
        'phone_number' => '+60175558888',
    ]);

    GuardianStudent::factory()->create([
        'guardian_id' => $guardian_A->id,
        'studenable_id' => $student_A1->id,
        'studenable_type' => Student::class,
        'is_direct_dependant' => true
    ]);

    $student_B = Student::factory()->create([
        'name->en' => 'Student B',
        'email' => '<EMAIL>',
        'phone_number' => '+60128889999',
        'student_number' => 'STUDENT-B001',
    ]);

    $guardian_B = Guardian::factory()->create([
        'name->en' => 'Father B',
        'email' => '<EMAIL>',
        'phone_number' => '+60175558889',
    ]);

    // B1 is not direct dependant, so it should not be shown
    $guardian_B1 = Guardian::factory()->create([
        'name->en' => 'Father B',
        'email' => '<EMAIL>',
        'phone_number' => null,
    ]);

    GuardianStudent::factory()->create([
        'guardian_id' => $guardian_B->id,
        'studenable_id' => $student_B->id,
        'studenable_type' => Student::class,
        'is_direct_dependant' => true
    ]);

    GuardianStudent::factory()->create([
        'guardian_id' => $guardian_B1->id,
        'studenable_id' => $student_B->id,
        'studenable_type' => Student::class,
        'is_direct_dependant' => false
    ]);

    $wallet_student_A1 = Wallet::factory()->create(['user_id' => $student_A1->user_id]);
    $wallet_guardian_A = Wallet::factory()->create(['user_id' => $guardian_A->user_id]);
    $wallet_student_B = Wallet::factory()->create(['user_id' => $student_B->user_id]);
    $wallet_guardian_B = Wallet::factory()->create(['user_id' => $guardian_B->user_id]);


    $filters = [
        'user_name' => [
            'en' => 'Father A'
        ],
        'includes' => ['userables', 'user', 'currency'],
        'order_by' => ['id' => 'asc'],
    ];

    $response = $this->getJson(route('admin.wallets.index', $filters))->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toHaveCount(2)
        ->and($response['data'][0])->toEqual([
            'id' => $wallet_student_A1->id,
            'user' => resourceToArray(new UserResource($wallet_student_A1->user)),
            'userables' => resourceToArray(SimpleUserableViewResource::collection($wallet_student_A1->userables)),
            'balance' => $wallet_student_A1->balance,
            'currency' => resourceToArray(new CurrencyResource($wallet_student_A1->currency))
        ])
        ->and($response['data'][1])->toEqual([
            'id' => $wallet_guardian_A->id,
            'user' => resourceToArray(new UserResource($wallet_guardian_A->user)),
            'userables' => resourceToArray(SimpleUserableViewResource::collection($wallet_guardian_A->userables)),
            'balance' => $wallet_guardian_A->balance,
            'currency' => resourceToArray(new CurrencyResource($wallet_guardian_A->currency))
        ]);

    // Test search by email
    $filters = [
        'user_email' => '<EMAIL>',
        'includes' => ['userables', 'user', 'currency'],
        'order_by' => ['id' => 'asc'],
    ];

    $response = $this->getJson(route('admin.wallets.index', $filters))->json();
    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toHaveCount(2)
        ->sequence(
            fn($item) => $item->toEqual([
                'id' => $wallet_student_B->id,
                'user' => resourceToArray(new UserResource($wallet_student_B->user)),
                'userables' => resourceToArray(SimpleUserableViewResource::collection($wallet_student_B->userables)),
                'balance' => $wallet_student_B->balance,
                'currency' => resourceToArray(new CurrencyResource($wallet_student_B->currency))
            ]),
            fn($item) => $item->toEqual([
                'id' => $wallet_guardian_B->id,
                'user' => resourceToArray(new UserResource($wallet_guardian_B->user)),
                'userables' => resourceToArray(SimpleUserableViewResource::collection($wallet_guardian_B->userables)),
                'balance' => $wallet_guardian_B->balance,
                'currency' => resourceToArray(new CurrencyResource($wallet_guardian_B->currency))
            ]),
        );

    // Test search by phone number
    $filters = [
        'user_phone_number' => '+60175558889',
        'includes' => ['userables', 'user', 'currency'],
        'order_by' => ['id' => 'asc'],
    ];

    $response = $this->getJson(route('admin.wallets.index', $filters))->json();
    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toHaveCount(2)
        ->sequence(
            fn($item) => $item->toEqual([
                'id' => $wallet_student_B->id,
                'user' => resourceToArray(new UserResource($wallet_student_B->user)),
                'userables' => resourceToArray(SimpleUserableViewResource::collection($wallet_student_B->userables)),
                'balance' => $wallet_student_B->balance,
                'currency' => resourceToArray(new CurrencyResource($wallet_student_B->currency))
            ]),
            fn($item) => $item->toEqual([
                'id' => $wallet_guardian_B->id,
                'user' => resourceToArray(new UserResource($wallet_guardian_B->user)),
                'userables' => resourceToArray(SimpleUserableViewResource::collection($wallet_guardian_B->userables)),
                'balance' => $wallet_guardian_B->balance,
                'currency' => resourceToArray(new CurrencyResource($wallet_guardian_B->currency))
            ]),
        );
});

test('get balance', function () {
    $student = Student::factory()->create([
        'user_id' => $this->user->id,
    ]);

    $card = Card::factory()->create([
        'name' => 'Card 1',
        'card_number' => '123456',
        'userable_type' => Student::class,
        'userable_id' => $student->id
    ]);

    $second_currency = Currency::factory()->create();

    $second_wallet = Wallet::factory()->create([
        'user_id' => $this->user->id,
        'currency_id' => $second_currency->id,
        'balance' => 15,
    ]);

    $response = $this->getJson(route($this->routeNamePrefix . '.get-balance', [
        'card_number' => '123456',
        'currency' => 'MYR'
    ]))->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toEqual([
            'user_id' => $card->userable->getUserNumber(),
            'name' => $card->userable->getUserName(),
            'photo' => $card->userable->getProfilePicture(),
            'balance' => 10,
            'currency' => 'MYR',
            'user_type_description' => Userable::USER_LABELS[Student::class],
        ]);

    $response = $this->getJson(route($this->routeNamePrefix . '.get-balance', [
        'card_number' => '123456',
        'currency' => $second_currency->code
    ]))->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toEqual([
            'user_id' => $card->userable->getUserNumber(),
            'name' => $card->userable->getUserName(),
            'photo' => $card->userable->getProfilePicture(),
            'balance' => 15,
            'currency' => $second_currency->code,
            'user_type_description' => Userable::USER_LABELS[Student::class],
        ]);
});

test('get balance failed with inactive card', function () {
    $student = Student::factory()->create([
        'user_id' => $this->user->id,
    ]);

    Card::factory()->create([
        'name' => 'Card 1',
        'card_number' => '123456',
        'userable_type' => Student::class,
        'userable_id' => $student->id,
        'status' => CardStatus::INACTIVE
    ]);

    $response = $this->getJson(route($this->routeNamePrefix . '.get-balance', [
        'card_number' => '123456',
        'currency' => 'MYR'
    ]))->json();

    expect($response)->toHaveFailedGeneralResponse()
        ->and($response['error'])
        ->toHaveKey('card_number.0', 'The selected card number is invalid.');
});

test("transfer from other people's wallet failed", function () {
    $other_guardian = Guardian::factory()->create();

    $other_user = $other_guardian->user;
    $other_user->assignRole('Super Admin');

    $other_wallet = Wallet::factory()->create([
        'user_id' => $other_user->id,
        'currency_id' => $this->currency->id,
        'balance' => 20
    ]);

    $payload = [
        'wallet_id' => $other_wallet->id,
        'userable_id' => $other_guardian->id,
        'userable_type' => Guardian::class,
        'receivers' => [
            [
                'userable_id' => $this->sender_guardian->id,
                'userable_type' => Guardian::class,
                'wallet_id' => $this->wallet->id,
                'amount' => 5,
                'description' => 'Test Transfer'
            ]
        ]
    ];

    $response = $this->actingAs($this->user)->postJson(route($this->routeNamePrefix . '.transfer'), $payload)->json();

    expect($response)->toHaveFailedGeneralResponse()
        ->and($response['error']['wallet_id'][0])->toBe('The sender wallet does not belong to the user.');

    $response = $this->actingAs($other_user)->postJson(route($this->routeNamePrefix . '.transfer'), $payload)->json();

    expect($response)->toHaveSuccessGeneralResponse();
});

test('transfer to single wallet', function () {
    $this->assertDatabaseCount('wallet_transactions', 0);

    $receiver = User::factory()->create();

    $receiver_userable = Guardian::factory()->create([
        'user_id' => $receiver->id
    ]);

    $receiver_wallet = Wallet::factory()->create([
        'user_id' => $receiver->id,
        'currency_id' => $this->currency->id,
        'balance' => 15
    ]);

    $transfer_amount = 2.05;

    // Error - Transfer using userable that doesn't belong to user
    $payload = [
        'wallet_id' => $this->wallet->id,
        'userable_id' => $this->guardian_diff_user->id,
        'userable_type' => Guardian::class,
        'receivers' => [
            [
                'wallet_id' => 999,
                'amount' => $transfer_amount,
                'description' => 'Lunch'
            ],
        ]
    ];

    $response = $this->postJson(route($this->routeNamePrefix . '.transfer'), $payload)->json();
    expect($response['error']['userable_id'])->toBe(['The selected user is invalid.']);

    //receiver id not exist
    $payload = [
        'wallet_id' => $this->wallet->id,
        'userable_id' => $this->sender_guardian->id,
        'userable_type' => Guardian::class,
        'receivers' => [
            [
                'wallet_id' => 999,
                'amount' => $transfer_amount,
                'description' => 'Lunch'
            ]
        ]
    ];

    $response = $this->postJson(route($this->routeNamePrefix . '.transfer'), $payload)->json();

    expect($response)->toHaveFailedGeneralResponse()
        ->and($response['error']['receivers.0.wallet_id'][0])->toBe('The selected receiver wallet is invalid.');

    $payload = [
        'wallet_id' => $this->wallet->id,
        'userable_id' => $this->sender_guardian->id,
        'userable_type' => Guardian::class,
        'receivers' => [
            [
                'userable_id' => $receiver_userable->id,
                'userable_type' => Guardian::class,
                'wallet_id' => $receiver_wallet->id,
                'amount' => $transfer_amount,
                'description' => 'Lunch'
            ]
        ]
    ];

    $response = $this->postJson(route($this->routeNamePrefix . '.transfer'), $payload)->json();

    $expected_currency = $this->currency->only(['id', 'code', 'name', 'symbol', 'is_active']);

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toBe([
            'sender' => [
                'name' => $this->sender_guardian->name,
                'wallet_id' => $this->wallet->id,
                'balance_before' => 10,
                'balance_after' => 7.95,
                'total_amount' => $transfer_amount * -1,
                'currency' => $expected_currency
            ],
            'receivers' => [
                [
                    'name' => $receiver_userable->name,
                    'wallet_id' => $receiver_wallet->id,
                    'balance_before' => 15,
                    'balance_after' => 17.05,
                    'total_amount' => $transfer_amount,
                    'currency' => $expected_currency
                ]
            ],
        ]);

    $this->assertDatabaseCount('wallet_transactions', 2);

    //sender record
    $sender_after_balance = 10 - $transfer_amount;
    $this->assertDatabaseHas('wallet_transactions', [
        'wallet_id' => $this->wallet->id,
        'type' => WalletTransactionType::TRANSFER,
        'status' => WalletTransactionStatus::SUCCESS,
        'wallet_transactable_type' => get_class($this->wallet),
        'wallet_transactable_id' => $receiver_wallet->id,
        'userable_type' => Guardian::class,
        'userable_id' => $this->sender_guardian->id,
        'total_amount' => $transfer_amount * -1,
        'amount_before_tax' => $transfer_amount * -1,
        'amount_after_tax' => $transfer_amount * -1,
        'tax_type' => TaxType::FLAT_AMOUNT,
        'tax_value' => 0,
        'balance_before' => 10,
        'balance_after' => $sender_after_balance,
        'description' => 'Lunch',
    ]);

    $this->assertDatabaseHas($this->table, [
        'user_id' => $this->user->id,
        'balance' => $sender_after_balance
    ]);

    //receiver record
    $receiver_after_balance = 15 + $transfer_amount;
    $this->assertDatabaseHas('wallet_transactions', [
        'wallet_id' => $receiver_wallet->id,
        'type' => WalletTransactionType::TRANSFER,
        'status' => WalletTransactionStatus::SUCCESS,
        'wallet_transactable_type' => get_class($receiver_wallet),
        'wallet_transactable_id' => $this->wallet->id,
        'userable_type' => Guardian::class,
        'userable_id' => $this->sender_guardian->id,
        'total_amount' => $transfer_amount,
        'amount_before_tax' => $transfer_amount,
        'amount_after_tax' => $transfer_amount,
        'tax_type' => TaxType::FLAT_AMOUNT,
        'tax_value' => 0,
        'balance_before' => 15,
        'balance_after' => $receiver_after_balance,
        'description' => 'Lunch',
    ]);

    $this->assertDatabaseHas($this->table, [
        'user_id' => $receiver->id,
        'balance' => $receiver_after_balance
    ]);

    //transfer to receiver that exceed sender balance
    $payload = [
        'wallet_id' => $this->wallet->id,
        'userable_id' => $this->sender_guardian->id,
        'userable_type' => Guardian::class,
        'receivers' => [
            [
                'userable_id' => $receiver_userable->id,
                'userable_type' => Guardian::class,
                'wallet_id' => $receiver_wallet->id,
                'amount' => 100,
                'description' => 'Dinner'
            ]
        ]
    ];

    $response = $this->postJson(route($this->routeNamePrefix . '.transfer'), $payload)->json();

    expect($response)->toHaveFailedGeneralResponse()
        ->and($response['error'])->toBe('User has insufficient balance.');
});

test('transfer to multiple wallets', function () {
    $this->assertDatabaseCount('wallet_transactions', 0);

    $first_receiver = User::factory()->create();

    $first_receiver_userable = Guardian::factory()->create([
        'user_id' => $first_receiver->id
    ]);

    $first_receiver_wallet = Wallet::factory()->create([
        'user_id' => $first_receiver->id,
        'currency_id' => $this->currency->id,
        'balance' => 15
    ]);

    $second_receiver = User::factory()->create();

    $second_receiver_userable = Guardian::factory()->create([
        'user_id' => $second_receiver->id
    ]);

    $second_receiver_wallet = Wallet::factory()->create([
        'user_id' => $second_receiver->id,
        'currency_id' => $this->currency->id,
        'balance' => 15
    ]);

    $first_transfer_amount = 2.05;
    $second_transfer_amount = 3.63;

    $payload = [
        'userable_id' => $this->sender_guardian->id,
        'userable_type' => Guardian::class,
        'wallet_id' => $this->wallet->id,
        'receivers' => [
            [
                'userable_id' => $first_receiver_userable->id,
                'userable_type' => Guardian::class,
                'wallet_id' => $first_receiver_wallet->id,
                'amount' => $first_transfer_amount,
                'description' => 'Lunch'
            ],
            [
                'userable_id' => $second_receiver_userable->id,
                'userable_type' => Guardian::class,
                'wallet_id' => $second_receiver_wallet->id,
                'amount' => $second_transfer_amount,
                'description' => 'Dinner'
            ]
        ]
    ];

    $response = $this->postJson(route($this->routeNamePrefix . '.transfer'), $payload)->json();

    $expected_currency = $this->currency->only(['id', 'code', 'name', 'symbol', 'is_active']);

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toBe([
            'sender' => [
                'name' => $this->sender_guardian->name,
                'wallet_id' => $this->wallet->id,
                'balance_before' => 10,
                'balance_after' => 4.32,
                'total_amount' => ($first_transfer_amount * -1) + ($second_transfer_amount * -1),
                'currency' => $expected_currency
            ],
            'receivers' => [
                [
                    'name' => $first_receiver_userable->name,
                    'wallet_id' => $first_receiver_wallet->id,
                    'balance_before' => 15,
                    'balance_after' => 15 + $first_transfer_amount,
                    'total_amount' => $first_transfer_amount,
                    'currency' => $expected_currency
                ],
                [
                    'name' => $second_receiver_userable->name,
                    'wallet_id' => $second_receiver_wallet->id,
                    'balance_before' => 15,
                    'balance_after' => 15 + $second_transfer_amount,
                    'total_amount' => $second_transfer_amount,
                    'currency' => $expected_currency
                ]
            ],
        ]);

    // 2 Sender records + 2 Receiver records
    $this->assertDatabaseCount('wallet_transactions', 4);

    //sender record
    $this->assertDatabaseHas('wallet_transactions', [
        'wallet_id' => $this->wallet->id,
        'type' => WalletTransactionType::TRANSFER,
        'status' => WalletTransactionStatus::SUCCESS,
        'wallet_transactable_type' => get_class($this->wallet),
        'wallet_transactable_id' => $first_receiver_wallet->id,
        'userable_type' => Guardian::class,
        'userable_id' => $this->sender_guardian->id,
        'total_amount' => $first_transfer_amount * -1,
        'amount_before_tax' => $first_transfer_amount * -1,
        'amount_after_tax' => $first_transfer_amount * -1,
        'tax_type' => TaxType::FLAT_AMOUNT,
        'tax_value' => 0,
        'balance_before' => 10,
        'balance_after' => 10 - $first_transfer_amount,
        'description' => 'Lunch',
    ]);

    $this->assertDatabaseHas('wallet_transactions', [
        'wallet_id' => $this->wallet->id,
        'type' => WalletTransactionType::TRANSFER,
        'status' => WalletTransactionStatus::SUCCESS,
        'wallet_transactable_type' => get_class($this->wallet),
        'wallet_transactable_id' => $second_receiver_wallet->id,
        'userable_type' => Guardian::class,
        'userable_id' => $this->sender_guardian->id,
        'total_amount' => $second_transfer_amount * -1,
        'amount_before_tax' => $second_transfer_amount * -1,
        'amount_after_tax' => $second_transfer_amount * -1,
        'tax_type' => TaxType::FLAT_AMOUNT,
        'tax_value' => 0,
        'balance_before' => 10 - $first_transfer_amount,
        'balance_after' => (10 - $first_transfer_amount) - $second_transfer_amount,
        'description' => 'Dinner',
    ]);

    $sender_after_balance = 10 - $first_transfer_amount - $second_transfer_amount;

    $this->assertDatabaseHas($this->table, [
        'id' => $this->wallet->id,
        'user_id' => $this->user->id,
        'balance' => $sender_after_balance
    ]);

    //receiver record
    $first_receiver_after_balance = 15 + $first_transfer_amount;
    $this->assertDatabaseHas('wallet_transactions', [
        'wallet_id' => $first_receiver_wallet->id,
        'type' => WalletTransactionType::TRANSFER,
        'status' => WalletTransactionStatus::SUCCESS,
        'wallet_transactable_type' => get_class($this->wallet),
        'wallet_transactable_id' => $this->wallet->id,
        'userable_type' => Guardian::class,
        'userable_id' => $this->sender_guardian->id,
        'total_amount' => $first_transfer_amount,
        'amount_before_tax' => $first_transfer_amount,
        'amount_after_tax' => $first_transfer_amount,
        'tax_type' => TaxType::FLAT_AMOUNT,
        'tax_value' => 0,
        'balance_before' => 15,
        'balance_after' => $first_receiver_after_balance,
        'description' => 'Lunch',
    ]);

    $this->assertDatabaseHas($this->table, [
        'id' => $first_receiver_wallet->id,
        'user_id' => $first_receiver->id,
        'balance' => $first_receiver_after_balance
    ]);

    //receiver record
    $second_receiver_after_balance = 15 + $second_transfer_amount;
    $this->assertDatabaseHas('wallet_transactions', [
        'wallet_id' => $second_receiver_wallet->id,
        'type' => WalletTransactionType::TRANSFER,
        'status' => WalletTransactionStatus::SUCCESS,
        'wallet_transactable_type' => get_class($this->wallet),
        'wallet_transactable_id' => $this->wallet->id,
        'userable_type' => Guardian::class,
        'userable_id' => $this->sender_guardian->id,
        'total_amount' => $second_transfer_amount,
        'amount_before_tax' => $second_transfer_amount,
        'amount_after_tax' => $second_transfer_amount,
        'tax_type' => TaxType::FLAT_AMOUNT,
        'tax_value' => 0,
        'balance_before' => 15,
        'balance_after' => $second_receiver_after_balance,
        'description' => 'Dinner',
    ]);

    $this->assertDatabaseHas($this->table, [
        'id' => $second_receiver_wallet->id,
        'user_id' => $second_receiver->id,
        'balance' => $second_receiver_after_balance
    ]);
});

test('transfer to wallet with different currency fail', function () {
    $usd_currency = Currency::factory()->usdCurrency()->create();

    $first_receiver = User::factory()->create();

    $first_receiver_wallet = Wallet::factory()->create([
        'user_id' => $first_receiver->id,
        'currency_id' => $usd_currency->id,
        'balance' => 15
    ]);

    $transfer_amount = 2.05;

    $payload = [
        'wallet_id' => $this->wallet->id,
        'receivers' => [
            [
                'wallet_id' => $first_receiver_wallet->id,
                'amount' => $transfer_amount,
                'description' => 'Lunch'
            ],
        ]
    ];

    $response = $this->postJson(route($this->routeNamePrefix . '.transfer'), $payload)->json();

    $expected_currency = $this->currency->only(['id', 'code', 'name', 'symbol', 'is_active']);

    expect($response)->toHaveFailedGeneralResponse()
        ->and($response['error'])->toHaveKey('receivers.0.wallet_id',
            ['The receiver wallet must be the same currency as the sender wallet.']);
});

test('wallet withdraw not enough balance', function () {
    $user = User::factory()->create();

    Student::factory()->create([
        'user_id' => $user->id,
    ]);

    $wallet = Wallet::factory()->create([
        'user_id' => $user->id,
        'currency_id' => $this->currency->id,
        'balance' => 15
    ]);

    $amount = 15.05;

    $payload = [
        'amount' => $amount,
        'remark' => 'Test'
    ];

    $response = $this->postJson(route('admin.wallets.withdraw', ['wallet' => $wallet->id]), $payload)->json();

    expect($response['error'])->toBe('User has insufficient balance.');
});

test('wallet withdraw', function () {
    $this->assertDatabaseCount('wallet_transactions', 0);

    $user = User::factory()->create();

    $student = Student::factory()->create([
        'user_id' => $user->id,
    ]);

    $wallet = Wallet::factory()->create([
        'user_id' => $user->id,
        'currency_id' => $this->currency->id,
        'balance' => 15
    ]);

    $amount = 10.05;

    $payload = [
        'amount' => $amount,
        'remark' => 'Test'
    ];

    $response = $this->postJson(route('admin.wallets.withdraw', ['wallet' => $wallet->id]), $payload)->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toMatchArray([
            'type' => resourceToArray(new EnumResource(WalletTransactionType::WITHDRAW)),
            'status' => resourceToArray(new EnumResource(WalletTransactionStatus::SUCCESS)),
            'total_amount' => -abs($amount),
            'balance_before' => 15.00,
            'balance_after' => 4.95,
            'description' => 'Balance Withdraw',
            'remark' => 'Test'
        ])
        ->and($response['data']['reference_no'])->toContain(WalletTransactionType::getReferenceNoPrefix(WalletTransactionType::WITHDRAW));

    $this->assertDatabaseCount('wallet_transactions', 1);

    $this->assertDatabaseHas('wallet_transactions', [
        'wallet_id' => $wallet->id,
        'type' => WalletTransactionType::WITHDRAW,
        'status' => WalletTransactionStatus::SUCCESS,
        'wallet_transactable_type' => Wallet::class,
        'wallet_transactable_id' => $wallet->id,
        'total_amount' => -abs($amount),
        'amount_before_tax' => -abs($amount),
        'amount_after_tax' => -abs($amount),
        'tax_type' => TaxType::FLAT_AMOUNT,
        'tax_value' => 0,
        'balance_before' => 15,
        'balance_after' => 4.95,
        'description' => 'Balance Withdraw',
        'remark' => 'Test'
    ]);

    $this->assertDatabaseHas($this->table, [
        'user_id' => $user->id,
        'balance' => 4.95
    ]);
});

test('wallet adjustment positive amount', function () {
    $this->assertDatabaseCount('wallet_transactions', 0);

    $user = User::factory()->create();

    Student::factory()->create([
        'user_id' => $user->id,
    ]);

    $wallet = Wallet::factory()->create([
        'user_id' => $user->id,
        'currency_id' => $this->currency->id,
        'balance' => 15
    ]);

    $amount = 10.05;
    $after_amount = $wallet->balance + $amount;

    $payload = [
        'amount' => $amount,
        'remark' => 'Test'
    ];

    $response = $this->postJson(route('admin.wallets.adjustment', ['wallet' => $wallet->id]), $payload)->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toMatchArray([
            'type' => resourceToArray(new EnumResource(WalletTransactionType::ADJUSTMENT)),
            'status' => resourceToArray(new EnumResource(WalletTransactionStatus::SUCCESS)),
            'total_amount' => $amount,
            'balance_before' => 15.00,
            'balance_after' => $after_amount,
            'description' => 'Balance Adjustment',
            'remark' => 'Test'
        ])
        ->and($response['data']['reference_no'])->toContain(WalletTransactionType::getReferenceNoPrefix(WalletTransactionType::ADJUSTMENT));

    $this->assertDatabaseCount('wallet_transactions', 1);

    $this->assertDatabaseHas('wallet_transactions', [
        'wallet_id' => $wallet->id,
        'type' => WalletTransactionType::ADJUSTMENT,
        'status' => WalletTransactionStatus::SUCCESS,
        'wallet_transactable_type' => Wallet::class,
        'wallet_transactable_id' => $wallet->id,
        'total_amount' => $amount,
        'amount_before_tax' => $amount,
        'amount_after_tax' => $amount,
        'tax_type' => TaxType::FLAT_AMOUNT,
        'tax_value' => 0,
        'balance_before' => 15,
        'balance_after' => $after_amount,
        'description' => 'Balance Adjustment',
        'remark' => 'Test'
    ]);

    $this->assertDatabaseHas($this->table, [
        'user_id' => $user->id,
        'balance' => $after_amount
    ]);
});


test('wallet adjustment negative amount', function () {
    $this->assertDatabaseCount('wallet_transactions', 0);

    $user = User::factory()->create();

    Student::factory()->create([
        'user_id' => $user->id,
    ]);

    $wallet = Wallet::factory()->create([
        'user_id' => $user->id,
        'currency_id' => $this->currency->id,
        'balance' => 15
    ]);

    $amount = -5.05;
    $after_amount = $wallet->balance + $amount;

    $payload = [
        'amount' => $amount,
        'remark' => 'Test'
    ];

    $response = $this->postJson(route('admin.wallets.adjustment', ['wallet' => $wallet->id]), $payload)->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toMatchArray([
            'type' => resourceToArray(new EnumResource(WalletTransactionType::ADJUSTMENT)),
            'status' => resourceToArray(new EnumResource(WalletTransactionStatus::SUCCESS)),
            'total_amount' => $amount,
            'balance_before' => 15.00,
            'balance_after' => $after_amount,
            'description' => 'Balance Adjustment',
            'remark' => 'Test'
        ])
        ->and($response['data']['reference_no'])->toContain(WalletTransactionType::getReferenceNoPrefix(WalletTransactionType::ADJUSTMENT));

    $this->assertDatabaseCount('wallet_transactions', 1);

    $this->assertDatabaseHas('wallet_transactions', [
        'wallet_id' => $wallet->id,
        'type' => WalletTransactionType::ADJUSTMENT,
        'status' => WalletTransactionStatus::SUCCESS,
        'wallet_transactable_type' => Wallet::class,
        'wallet_transactable_id' => $wallet->id,
        'total_amount' => $amount,
        'amount_before_tax' => $amount,
        'amount_after_tax' => $amount,
        'tax_type' => TaxType::FLAT_AMOUNT,
        'tax_value' => 0,
        'balance_before' => 15,
        'balance_after' => $after_amount,
        'description' => 'Balance Adjustment',
        'remark' => 'Test'
    ]);

    $this->assertDatabaseHas($this->table, [
        'user_id' => $user->id,
        'balance' => $after_amount
    ]);
});


test('depositRequest', function () {
    $student = Student::factory()->create([
        'user_id' => $this->user->id
    ]);

    $payex_base_url = config('services.payment_gateway.payex.base_url');
    $payex_payment_url = $payex_base_url . '/' . config('services.payment_gateway.payex.payment_url');
    $payex_auth_url = $payex_base_url . '/' . config('services.payment_gateway.payex.auth_url');

    Config::factory()->create([
        'key' => Config::PAYEX_MERCHANT_EMAIL,
        'value' => '<EMAIL>'
    ]);

    Config::factory()->create([
        'key' => Config::PAYEX_MERCHANT_SECRET,
        'value' => '123456'
    ]);

    Http::fake([
        $payex_auth_url => Http::response(
            [
                'token' => 'VALID_TOKEN',
                'expiration' => '2030-05-29T09:55:45.499Z',
            ],
            200,
            ['Content-Type' => 'application/json']
        ),
        $payex_payment_url => Http::response(
            [
                'request_id' => 'd9e48d61-df21-422c-82f3-19eaf66ee5f8',
                'status' => '00',
                'result' => [
                    [
                        'status' => '00',
                        'key' => '7e3afcc5957b4555909d085832550147',
                        'url' => 'VALID_PAYMENT_URL',
                        'error' => null
                    ]
                ],
            ],
            200,
            ['Content-Type' => 'application/json']
        )
    ]);

    // Error - topping up as another userable
    $payload = [
        'userable_type' => Guardian::class,
        'userable_id' => $this->guardian_diff_user->id,
        'customer_name' => "Testing User",
        'customer_email' => "<EMAIL>",
        'amount' => 10,
        'wallet_id' => $this->wallet->id,
        'payment_type' => PaymentMethod::CODE_FPX,
    ];
    $response = $this->postJson(route($this->routeNamePrefix . '.deposit-request'), $payload)->json();
    expect($response)->toMatchArray([
        'error' => [
            'userable_id' => ['The selected user is invalid.']
        ]
    ]);

    //amount less than 10
    $payload = [
        'userable_type' => Student::class,
        'userable_id' => $student->id,
        'customer_name' => "Testing User",
        'customer_email' => "<EMAIL>",
        'amount' => 9,
        'wallet_id' => $this->wallet->id,
        'payment_type' => PaymentMethod::CODE_FPX,
    ];

    $response = $this->postJson(route($this->routeNamePrefix . '.deposit-request'), $payload)->json();

    expect($response)->toHaveFailedGeneralResponse()
        ->and($response['error']['amount'][0])
        ->toBe("The amount field must be at least 10.");

    $this->assertDatabaseCount('wallet_transactions', 0);
    $this->assertDatabaseCount('payment_gateway_logs', 0);

    // Test if set config max amount to 0
    ConfigHelper::put(Config::WALLET_DEPOSIT_MAX_AMOUNT, 0, Config::CATEGORY_GENERAL);

    //amount more than 300
    $payload = [
        'userable_type' => Student::class,
        'userable_id' => $student->id,
        'customer_name' => "Testing User",
        'customer_email' => "<EMAIL>",
        'amount' => 9999,
        'wallet_id' => $this->wallet->id,
        'payment_type' => PaymentMethod::CODE_FPX,
    ];

    $response = $this->postJson(route($this->routeNamePrefix . '.deposit-request'), $payload)->json();

    expect($response)->toHaveSuccessGeneralResponse();

    $this->assertDatabaseCount('wallet_transactions', 1);
    $this->assertDatabaseCount('payment_gateway_logs', 1);
    $this->assertDatabaseCount('billing_documents', 1);
    $this->assertDatabaseCount('billing_document_line_items', 1);

    // Test if set config max amount to 300
    ConfigHelper::put(Config::WALLET_DEPOSIT_MAX_AMOUNT, 300, Config::CATEGORY_GENERAL);

    //amount more than 300
    $payload = [
        'userable_type' => Student::class,
        'userable_id' => $student->id,
        'customer_name' => "Testing User",
        'customer_email' => "<EMAIL>",
        'amount' => 400,
        'wallet_id' => $this->wallet->id,
        'payment_type' => PaymentMethod::CODE_FPX,
    ];

    $response = $this->postJson(route($this->routeNamePrefix . '.deposit-request'), $payload)->json();

    expect($response)->toHaveFailedGeneralResponse()
        ->and($response['error']['amount'][0])
        ->toBe("The amount field must not be greater than 300.");

    $this->assertDatabaseCount('wallet_transactions', 1);
    $this->assertDatabaseCount('payment_gateway_logs', 1);
    $this->assertDatabaseCount('billing_documents', 1);
    $this->assertDatabaseCount('billing_document_line_items', 1);

    //amount = 15
    $payload = [
        'userable_type' => Student::class,
        'userable_id' => $student->id,
        'customer_name' => "Testing User",
        'customer_email' => "<EMAIL>",
        'amount' => 15.00,
        'wallet_id' => $this->wallet->id,
        'payment_type' => PaymentMethod::CODE_FPX,
        'return_url' => url('api.skribble-learn.skribble.test/payment-gateway')
    ];

    $response = $this->postJson(route($this->routeNamePrefix . '.deposit-request'), $payload)->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toMatchArray([
            'currency' => $this->currency->code,
            'amount' => 15,
            'payment_url' => 'VALID_PAYMENT_URL',
        ])->toHaveKey('order_id');

    $this->assertDatabaseCount('wallet_transactions', 2);
    $this->assertDatabaseCount('payment_gateway_logs', 2);
    $this->assertDatabaseCount('billing_documents', 2);
    $this->assertDatabaseCount('billing_document_line_items', 2);

    $after_balance = 10 + $payload['amount'];
    $this->assertDatabaseHas('wallet_transactions', [
        'wallet_id' => $this->wallet->id,
        'type' => WalletTransactionType::DEPOSIT,
        'status' => WalletTransactionStatus::PENDING,
        'wallet_transactable_type' => get_class($this->wallet),
        'wallet_transactable_id' => $this->wallet->id,
        'userable_type' => Student::class,
        'userable_id' => $student->id,
        'total_amount' => $payload['amount'],
        'amount_before_tax' => $payload['amount'],
        'amount_after_tax' => $payload['amount'],
        'tax_type' => TaxType::FLAT_AMOUNT,
        'tax_value' => 0,
        'balance_before' => 10,
        'balance_after' => $after_balance,
        'description' => 'Deposit from payment gateway'
    ]);

    $this->assertDatabaseHas('payment_gateway_logs', [
        'requested_by_id' => $this->user->id,
        'requested_by_type' => get_class($this->user),
        'type' => PaymentType::WALLET_DEPOSIT,
        'provider' => PaymentProvider::PAYEX,
        'transaction_loggable_type' => WalletTransaction::class,
        'currency_id' => $this->currency->id,
        'currency_code' => $this->currency->code,
        'currency_name' => $this->currency->name,
        'amount' => $payload['amount'],
        'status' => PaymentStatus::PENDING,
        'description' => 'Deposit Wallet',
        'payment_url' => 'VALID_PAYMENT_URL'
    ]);

    $this->assertDatabaseHas(BillingDocument::class, [
        'amount_before_tax' => 15,
        'amount_after_tax' => 15,
        'currency_code' => $this->currency->code,
        'sub_type' => BillingDocument::SUB_TYPE_WALLET_TOPUP,
        'type' => BillingDocument::TYPE_INVOICE,
    ]);
});

test('get transferable wallets', function () {
    $student = Student::factory()
        ->forUser([
            'email' => '<EMAIL>'
        ])->create([
            'name->en' => 'Jayden',
            'email' => '<EMAIL>',
            'student_number' => '12345'
        ]);

    $student_user = $student->user;

    $student_wallet = Wallet::factory()->create([
        'user_id' => $student_user->id,
        'currency_id' => $this->currency->id,
        'balance' => 50
    ]);

    $employee = Employee::factory()
        ->forUser([
            'email' => '<EMAIL>'
        ])
        ->create([
            'name->en' => 'Jordan',
            'email' => '<EMAIL>',
            'employee_number' => '67890'
        ]);

    $employee_user = $employee->user;

    $employee_wallet = Wallet::factory()->create([
        'user_id' => $employee_user->id,
        'currency_id' => $this->currency->id,
        'balance' => 50
    ]);

    GuardianStudent::factory()->create([
        'studenable_id' => $student->id,
        'studenable_type' => Student::class,
        'guardian_id' => $this->sender_guardian->id,
    ]);

    $response = $this->getJson(route($this->routeNamePrefix . '.getTransferableWallets',
        [
            'userable_type' => Guardian::class,
            'userable_id' => $this->sender_guardian->id,
            'currency_code' => $this->currency->code,
            'order_by' => 'id'
        ]))->json();
    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toHaveCount(1) // Only show 1 wallet because guardian can only see student linked to him
        ->sequence(
            fn($item) => $item->toHaveKey('id', $student_wallet->id)
        );

    $expected_currency = $this->currency->only(['id', 'code', 'name', 'symbol', 'is_active']);

    $response = $this->getJson(route($this->routeNamePrefix . '.getTransferableWallets',
        [
            'userable_type' => Guardian::class,
            'userable_id' => $this->sender_guardian->id,
            'search' => 'jayden',
            'currency_code' => $this->currency->code
        ]))->json();
    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toHaveCount(1)
        ->and($response['data'][0])->toBe([
            'id' => $student_wallet->id,
            'user' => resourceToArray(new UserResource($student_user)),
            'userables' => [
                [
                    'userable_id' => $student->id,
                    'userable_type' => Student::class,
                    'user_type_description' => 'Student',
                    'name' => $student->name,
                    'email' => $student->email,
                    'phone_number' => $student->phone_number,
                    'number' => $student->student_number,
                    'translations' => $student->translations,
                ]
            ],
            'balance' => $student_wallet->balance,
            'currency' => $expected_currency
        ]);
});

test('get own wallets', function () {
    $currency_usd = Currency::factory()->usdCurrency()->create();

    $student = Student::factory()
        ->forUser([
            'email' => '<EMAIL>'
        ])->create([
            'name->en' => 'Jayden',
            'email' => '<EMAIL>',
            'student_number' => '12345'
        ]);

    $student_user = $student->user;

    $student_user->assignRole('Super Admin');

    $student_wallet_myr = Wallet::factory()->create([
        'user_id' => $student_user->id,
        'currency_id' => $this->currency->id,
        'balance' => 50
    ]);

    $student_wallet_usd = Wallet::factory()->create([
        'user_id' => $student_user->id,
        'currency_id' => $currency_usd->id,
        'balance' => 50
    ]);

    $employee = Employee::factory()
        ->forUser([
            'email' => '<EMAIL>'
        ])
        ->create([
            'name->en' => 'Jordan',
            'email' => '<EMAIL>',
            'employee_number' => '67890'
        ]);

    $employee_user = $employee->user;

    $employee_wallet_myr = Wallet::factory()->create([
        'user_id' => $employee_user->id,
        'currency_id' => $this->currency->id,
        'balance' => 50
    ]);

    $response = $this->actingAs($student_user)->getJson(route($this->routeNamePrefix . '.index',
        ['currency_code' => 'MYR']))->json();
    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toHaveCount(1)
        ->and($response['data'][0])->toHaveKey('id', $student_wallet_myr->id);

    $response = $this->actingAs($student_user)->getJson(route($this->routeNamePrefix . '.index',
        ['currency_code' => 'USD']))->json();
    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toHaveCount(1)
        ->and($response['data'][0])->toHaveKey('id', $student_wallet_usd->id);

    $expected_currency = $this->currency->only(['id', 'code', 'name', 'symbol', 'is_active']);

    $response = $this->actingAs($student_user)->getJson(route($this->routeNamePrefix . '.index', [
        'currency_code' => 'MYR',
        'includes' => ['userables', 'user']
    ]))->json();
    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toHaveCount(1)
        ->and($response['data'][0])->toBe([
            'id' => $student_wallet_myr->id,
            'user' => resourceToArray(new UserResource(User::find($student_user->id))),
            'userables' => resourceToArray(SimpleUserableViewResource::collection($student_wallet_myr->userables)),
            'balance' => 50,
            'currency' => $expected_currency
        ]);
});

test('show requested wallet', function () {
    $first_userable = Guardian::factory()->create();
    $first_user = $first_userable->user;
    $first_user->assignRole('Super Admin');
    $first_wallet = Wallet::factory()->create(['user_id' => $first_user->id]);
    $first_wallet_transactions = WalletTransaction::factory()->count(3)->create([
        'wallet_id' => $first_wallet->id,
        'created_at' => '2025-01-03 20:00:00', // Malaysia Time - 2025-01-04 04:00:00
        'updated_at' => '2025-01-03 20:00:00', // Malaysia Time - 2025-01-04 04:00:00
    ])->load('wallet.currency');

    $first_wallet_transactions = $first_wallet_transactions->merge(WalletTransaction::factory()->count(2)->create([
        'wallet_id' => $first_wallet->id,
        'created_at' => '2025-01-03 13:00:00', // Malaysia Time - 2025-01-03 21:00:00
        'updated_at' => '2025-01-03 13:00:00', // Malaysia Time - 2025-01-03 21:00:00
    ])->load('wallet.currency'));

    $first_wallet_transactions = $first_wallet_transactions->merge(WalletTransaction::factory()->count(1)->create([
        'wallet_id' => $first_wallet->id,
        'created_at' => '2025-01-04 01:00:00', // Malaysia Time - 2025-01-04 09:00:00
        'updated_at' => '2025-01-04 01:00:00', // Malaysia Time - 2025-01-04 09:00:00
    ])->load('wallet.currency'));

    $second_userable = Guardian::factory()->create();
    $second_user = $second_userable->user;
    $second_user->assignRole('Super Admin');
    $second_wallet = Wallet::factory()->create(['user_id' => $second_user->id]);
    $second_wallet_transactions = WalletTransaction::factory()->count(5)->create(['wallet_id' => $second_wallet->id])->load('wallet.currency');

    $response = $this->actingAs($first_user)->getJson(route($this->routeNamePrefix . '.transactions.show',
        ['wallet' => $first_wallet->id, 'order_by' => 'id']))->json();
    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toHaveCount(2)
        ->and($response['data']['2025-01-04'])->toHaveCount(4)
        ->and($response['data']['2025-01-03'])->toHaveCount(2)
        ->and($response['data']['2025-01-04'][0])->toMatchArray([
            'id' => $first_wallet_transactions[0]->id,
            'card' => null,
            'currency' => [
                'id' => $first_wallet->currency->id,
                'code' => $first_wallet->currency->code,
                'name' => $first_wallet->currency->name,
                'symbol' => $first_wallet->currency->symbol,
                'is_active' => $first_wallet->currency->is_active,
            ],
            'reference_no' => $first_wallet_transactions[0]->reference_no,
            'type' => [
                'value' => $first_wallet_transactions[0]->type->value,
                'label' => $first_wallet_transactions[0]->type->label(),
            ],
            'status' => [
                'value' => $first_wallet_transactions[0]->status->value,
                'label' => $first_wallet_transactions[0]->status->label(),
            ],
            'total_amount' => $first_wallet_transactions[0]->total_amount,
            'amount_before_tax' => $first_wallet_transactions[0]->amount_before_tax,
            'amount_after_tax' => $first_wallet_transactions[0]->amount_after_tax,
            'balance_before' => $first_wallet_transactions[0]->balance_before,
            'balance_after' => $first_wallet_transactions[0]->balance_after,
            'description' => $first_wallet_transactions[0]->description,
            'remark' => $first_wallet_transactions[0]->remark,
            'created_at' => $first_wallet_transactions[0]->created_at->toISOString(),
        ]);

    $response = $this->actingAs($second_user)->getJson(route($this->routeNamePrefix . '.transactions.show',
        ['wallet' => $second_wallet->id, 'order_by' => 'id']))->json();
    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'][now()->tz(config('school.timezone'))->toDateString()])
        ->sequence(
            fn($item) => $item->toHaveKey('id', $second_wallet_transactions[0]->id),
            fn($item) => $item->toHaveKey('id', $second_wallet_transactions[1]->id),
            fn($item) => $item->toHaveKey('id', $second_wallet_transactions[2]->id),
            fn($item) => $item->toHaveKey('id', $second_wallet_transactions[3]->id),
            fn($item) => $item->toHaveKey('id', $second_wallet_transactions[4]->id)
        );
});
