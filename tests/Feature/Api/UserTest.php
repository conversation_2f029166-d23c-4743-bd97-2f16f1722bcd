<?php

use App\Enums\GuardianType;
use App\Http\Resources\PermissionResource;
use App\Http\Resources\RoleResource;
use App\Http\Resources\SimpleUserableViewResource;
use App\Http\Resources\UserableViewResource;
use App\Interfaces\Userable;
use App\Models\Employee;
use App\Models\Guardian;
use App\Models\GuardianStudent;
use App\Models\Role;
use App\Models\Student;
use App\Models\User;
use App\Models\UserInbox;
use App\Models\Wallet;
use App\Services\UserService;
use Database\Seeders\InternationalizationSeeder;
use Database\Seeders\PermissionSeeder;
use Illuminate\Database\Eloquent\Factories\Sequence;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\Hash;
use Laravel\Sanctum\Sanctum;
use Mockery\MockInterface;

beforeEach(function () {
    $this->seed([
        InternationalizationSeeder::class,
        PermissionSeeder::class,
    ]);

    app()->setLocale('en');

    $this->withHeaders([
        'Accept-Language' => 'en'
    ]);

    $this->user = User::factory()->create([
        'password' => Hash::make('123456'),
    ]);

    $this->user->assignRole('Super Admin');

    Sanctum::actingAs($this->user);

    $this->routeNamePrefix = 'users.';
});

test('get profile', function () {
    $student = Student::factory()->create([
        'user_id' => $this->user->id,
    ]);

    $employee = Employee::factory()->create([
        'user_id' => $this->user->id,
    ]);

    $guardian = Guardian::factory()->create([
        'user_id' => $this->user->id,
    ]);

    // Create 2 read user inboxes and 3 unread user inboxes
    UserInbox::factory(2)->read()->create([
        'user_id' => $this->user->id,
    ]);

    UserInbox::factory(3)->create([
        'user_id' => $this->user->id,
    ]);

    $response = $this->getJson(route($this->routeNamePrefix . 'get-profile'))->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toMatchArray([
            'id' => $this->user->id,
            'email' => $this->user->email,
            'phone_number' => $this->user->phone_number,
            'is_active' => $this->user->is_active,
            'last_login_at' => $this->user->last_login_at->toDateTimestring(),
        ]);

    $response = $this->getJson(route($this->routeNamePrefix . 'get-profile', [
        'includes' => ['userables.wallets', "guardian.students.userable.wallets"]
    ]))->json();

    Wallet::factory()->create([
        'user_id' => $this->user->id,
    ]);

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toMatchArray([
            'id' => $this->user->id,
            'email' => $this->user->email,
            'phone_number' => $this->user->phone_number,
            'is_active' => $this->user->is_active,
            'last_login_at' => $this->user->last_login_at->toDateTimestring(),
            'user_inboxes_unread_count' => 3, // 3 unread user inboxes
        ])->and($response['data']['userables'])->toMatchArray(resourceToArray(UserableViewResource::collection($this->user->getAllUserables())));
});

test('change password', function () {
    $payload = [
        'password' => '654321',
        'password_confirmation' => '654321',
    ];

    $response = $this->postJson(route($this->routeNamePrefix . 'change-password'), $payload)->json();

    expect($response)->toHaveSuccessGeneralResponse();

    $login_with_new_password = auth('api')->once([
        'email' => $this->user->email,
        'password' => $payload['password']
    ]);

    expect($login_with_new_password)->toBeTrue();

    $login_with_old_password = auth('api')->once([
        'email' => $this->user->email,
        'password' => '123456'
    ]);

    expect($login_with_old_password)->toBeFalse();
});

test('show', function () {
    $user = User::factory()->withStudent()->create();
    $user->assignRole('Super Admin');

    $response = $this->getJson(route($this->routeNamePrefix . 'show', ['user' => $user->id]))->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toMatchArray([
            'id' => $user->id,
            'email' => $user->email,
            'phone_number' => $user->phone_number,
            'is_active' => $user->is_active,
            'last_login_at' => $user->last_login_at->toDateTimestring(),
            'direct_userables' => resourceToArray(SimpleUserableViewResource::collection($user->userables)),
            'accessible_student_userables' => [],
            'userables' => resourceToArray(SimpleUserableViewResource::collection($user->userables)),
            'roles' => resourceToArray(RoleResource::collection($user->roles->loadMissing('permissions'))),
            'permissions' => resourceToArray(PermissionResource::collection($user->getAllPermissions())),
        ]);

    /**
     *
     *  show Employee that is also Guardian and have 2 Students under him
     *
     */
    $name = 'Kevin';

    $user_2 = User::factory()->create();
    $user_2->assignRole('Super Admin');

    $employee = Employee::factory()->create([
        'user_id' => $user_2->id,
        'email' => $user_2->email,
        'phone_number' => $user_2->phone_number,
        'name->en' => $name,
    ]);

    $guardian = Guardian::factory()->create([
        'user_id' => $user_2->id,
        'email' => $user_2->email,
        'phone_number' => $user_2->phone_number,
        'name->en' => $name,
    ]);

    $students = Student::factory(2)->state(new Sequence(
        [
            'name->en' => 'David',
        ],
        [
            'name->en' => 'Simon'
        ],
    ))->create();

    $guardian_students = GuardianStudent::factory(2)->state(new Sequence(
        [
            'guardian_id' => $guardian->id,
            'type' => GuardianType::FATHER->value,
            'studenable_type' => Student::class,
            'studenable_id' => $students[0]->id, // Father to David
        ],
        [
            'guardian_id' => $guardian->id,
            'type' => GuardianType::FATHER->value,
            'studenable_type' => Student::class,
            'studenable_id' => $students[1]->id, // Father to Simon
        ],
    ))->create();

    expect($user_2->userables)->toHaveCount(2)
        ->and($user_2->guardian->students)->toHaveCount(2)
        ->and($user_2->getStudentUserables())->toHaveCount(2)
        ->and($user_2->getAllUserables())->toHaveCount(4);

    $response = $this->getJson(route($this->routeNamePrefix . 'show', ['user' => $user_2->id]))->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toMatchArray([
            'id' => $user_2->id,
            'email' => $user_2->email,
            'phone_number' => $user_2->phone_number,
            'is_active' => $user_2->is_active,
            'direct_userables' => resourceToArray(SimpleUserableViewResource::collection($user_2->userables)),
            'accessible_student_userables' => resourceToArray(SimpleUserableViewResource::collection($user_2->getStudentUserables())),
            'userables' => resourceToArray(SimpleUserableViewResource::collection($user_2->getAllUserables())),
            'roles' => resourceToArray(RoleResource::collection($user_2->roles->loadMissing('permissions'))),
            'permissions' => resourceToArray(PermissionResource::collection($user_2->getAllPermissions())),
        ])
        ->and($response['data']['direct_userables'])->toHaveCount(2)
        ->and($response['data']['accessible_student_userables'])->toHaveCount(2)
        ->and($response['data']['userables'])->toHaveCount(4);
});

test('show invalid user', function () {
    $response = $this->getJson(route($this->routeNamePrefix . 'show', ['user' => 11]));

    $response->assertStatus(404);

    expect($response->json())->toHaveModelResourceNotFoundResponse();
});

test('index without any params', function () {
    $first_user = User::factory()->withStudent()->create();
    $second_user = User::factory()->withEmployee()->create();

    $response = $this->getJson(route($this->routeNamePrefix . 'index', [
        'includes' => ['userables', 'roles', 'permissions'],
        'order_by' => ['id']
    ]));

    $response->assertStatus(200);

    expect($response->json())->toHaveSuccessGeneralResponse()
        ->and($response->json()['data'])->toHaveCount(3)
        ->sequence(
            fn($response) => $response->toEqual([
                'id' => $this->user->id,                    // default user created for the test
                'email' => $this->user->email,
                'phone_number' => $this->user->phone_number,
                'is_active' => $this->user->is_active,
                'last_login_at' => $this->user->last_login_at->toDateTimestring(),
                'direct_userables' => resourceToArray(SimpleUserableViewResource::collection($this->user->userables)),
                'accessible_student_userables' => [],
                'userables' => resourceToArray(SimpleUserableViewResource::collection($this->user->userables)),
                'roles' => resourceToArray(RoleResource::collection($this->user->roles->loadMissing('permissions'))),
                'permissions' => resourceToArray(PermissionResource::collection($this->user->getAllPermissions())),
            ]),
            fn($response) => $response->toEqual([
                'id' => $first_user->id,
                'email' => $first_user->email,
                'phone_number' => $first_user->phone_number,
                'is_active' => $first_user->is_active,
                'last_login_at' => $first_user->last_login_at->toDateTimestring(),
                'direct_userables' => resourceToArray(SimpleUserableViewResource::collection($first_user->userables)),
                'accessible_student_userables' => [],
                'userables' => resourceToArray(SimpleUserableViewResource::collection($first_user->userables)),
                'roles' => resourceToArray(RoleResource::collection($first_user->roles->loadMissing('permissions'))),
                'permissions' => resourceToArray(PermissionResource::collection($first_user->getAllPermissions())),
            ]),
            fn($response) => $response->toEqual([
                'id' => $second_user->id,
                'email' => $second_user->email,
                'phone_number' => $second_user->phone_number,
                'is_active' => $second_user->is_active,
                'last_login_at' => $second_user->last_login_at->toDateTimestring(),
                'direct_userables' => resourceToArray(SimpleUserableViewResource::collection($second_user->userables)),
                'accessible_student_userables' => [],
                'userables' => resourceToArray(SimpleUserableViewResource::collection($second_user->userables)),
                'roles' => resourceToArray(RoleResource::collection($second_user->roles->loadMissing('permissions'))),
                'permissions' => resourceToArray(PermissionResource::collection($second_user->getAllPermissions())),
            ]),
        );
});

test('index: test service accepting params', function () {
    $first_user = User::factory()->withStudent()->create([
        'is_password_reset_required' => true
    ]);
    $second_user = User::factory()->withEmployee()->create([
        'is_password_reset_required' => true
    ]);

    $first_user_student = $first_user->userables->first()->toArray();

    $role = Role::factory()->create();

    $first_user->syncRoles($role);
    $second_user->syncRoles($role);

    // Test if all values passes the validation and passes into service
    $filters = [
        'email' => $first_user->email,
        'phone_number' => $first_user->phone_number,
        'is_active' => $first_user->is_active,
        'role_id' => $role->id,
        'user_type' => Userable::USER_TYPE_MAPPING[Student::class],
        'user_number' => $first_user_student['number'],
        'user_name' => [
            'en' => $first_user_student['name']['en'],
            'zh' => $first_user_student['name']['zh'],
        ],
        'includes' => ['roles', 'permissions'],
        'order_by' => ['id' => 'asc'],
    ];

    $mock_filters = $filters;
    $mock_filters['includes'][] = 'userables';
    $mock_filters['includes'][] = 'guardian.directDependants.userable';     // this is loaded when userables is loaded.
    $mock_filters['user_type'] = Userable::USERABLE_MAPPING[$mock_filters['user_type']];

    $this->mock(UserService::class, function (MockInterface $mock) use ($mock_filters, $first_user) {
        $mock->shouldReceive('getAllPaginatedUsers')
            ->once()
            ->with($mock_filters)
            ->andReturn(new LengthAwarePaginator([$first_user->loadMissing(['userables', 'roles', 'permissions', 'guardian.directDependants.userable'])], 1, 1));
    });

    $response = $this->getJson(route($this->routeNamePrefix . 'index', $filters))->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toHaveCount(1)
        ->and($response['data'][0])->toEqual([
            'id' => $first_user->id,
            'email' => $first_user->email,
            'phone_number' => $first_user->phone_number,
            'is_active' => $first_user->is_active,
            'last_login_at' => $first_user->last_login_at->toDateTimestring(),
            'direct_userables' => resourceToArray(SimpleUserableViewResource::collection($first_user->userables)),
            'accessible_student_userables' => [],
            'userables' => resourceToArray(SimpleUserableViewResource::collection($first_user->userables)),
            'roles' => resourceToArray(RoleResource::collection($first_user->roles->loadMissing('permissions'))),
            'permissions' => resourceToArray(PermissionResource::collection($first_user->getAllPermissions())),
        ]);
});

test('update success', function () {
    $user = User::factory()->withStudent()->create([
        'is_active' => false,
        'password' => Hash::make('123456'),
        'email' => '<EMAIL>',
        'phone_number' => '+60123456789'
    ]);

    $role = Role::factory()->create();

    expect($user->roles)->toHaveCount(0);

    $this->assertDatabaseCount('users', 2);

    $payload = [
        'is_active' => true,
        'password' => 'new_password',
        'password_confirmation' => 'new_password',
        'role_ids' => [$role->id],
        'email' => '<EMAIL>',
        'phone_number' => '+60128884321'
    ];

    $response = $this->putJson(route($this->routeNamePrefix . 'update', $user->id), $payload);

    $response->assertStatus(200);

    $this->assertDatabaseCount('users', 2);

    $user->refresh();

    expect($response->json())->toHaveSuccessGeneralResponse()
        ->and($response->json()['data'])
        ->toBe([
            'id' => $user->id,
            'email' => $payload['email'],
            'phone_number' => $payload['phone_number'],
            'is_active' => $payload['is_active'],
            'last_login_at' => $user->last_login_at->toDateTimestring(),
            'direct_userables' => resourceToArray(SimpleUserableViewResource::collection($user->userables)),
            'accessible_student_userables' => [],
            'userables' => resourceToArray(SimpleUserableViewResource::collection($user->userables)),
            'roles' => resourceToArray(RoleResource::collection($user->roles->loadMissing('permissions'))),
            'permissions' => resourceToArray(PermissionResource::collection($user->getAllPermissions())),
        ]);

    expect($user->roles)->toHaveCount(1)
        ->and($user->hasRole($role))->toBeTruthy();

    $this->assertDatabaseHas('users', [
        'id' => $user->id,
        'email' => $user->email,
        'phone_number' => $user->phone_number,
        'is_active' => $payload['is_active'],
    ]);

    // test new password
    $login_with_new_password = auth('api')->once([
        'email' => $user->email,
        'password' => $payload['password']
    ]);

    expect($login_with_new_password)->toBeTrue();

    $login_with_old_password = auth('api')->once([
        'email' => $user->email,
        'password' => '123456'
    ]);

    expect($login_with_old_password)->toBeFalse();
});

test('update validation error', function () {
    $this->assertDatabaseCount('users', 1);

    $response = $this->putJson(route($this->routeNamePrefix . 'update', $this->user->id), []);

    $response->assertStatus(422);

    expect(User::count())->toBe(1)
        ->and($response->json())->toMatchArray([
            'error' => [
                'is_active' => [
                    'The is active field is required.'
                ],
            ],
            'data' => null
        ]);
        
    $user = User::factory()->create([
        'is_active' => false,
        'password' => Hash::make('123456'),
        'email' => '<EMAIL>',
        'phone_number' => '+60123456789'
    ]);

    $user_2 = User::factory()->create([
        'is_active' => false,
        'password' => Hash::make('123456'),
        'email' => '<EMAIL>',
        'phone_number' => '+60128889991'
    ]);

    $payload = [
        'is_active' => true,
        'email' => '<EMAIL>',
        'phone_number' => '+60128889991'     
    ];

    $response = $this->putJson(route($this->routeNamePrefix . 'update', $user->id), $payload)->json();

    expect($response)->toHaveFailedGeneralResponse()
        ->and($response['error'])->toMatchArray([
            'email' => ['The email has already been taken.'],
            'phone_number' => ['The phone number has already been taken.']
        ]);

});

test('unlinkGuardian failed', function () {
    $another_guardian = Guardian::factory()->create();

    $payload = ['guardian_id' => $another_guardian->id];

    $response = $this->postJson(route($this->routeNamePrefix . 'unlink-guardian', ['user' => $this->user->id]), $payload)->json();

    expect($response)->toHaveFailedGeneralResponse()
        ->and($response['error'])->toBe('This guardian does not belongs to the user.');
});

test('unlinkGuardian success', function () {
    $another_guardian = Guardian::factory()->create();
    $guardian = Guardian::factory()->create([
        'user_id' => $this->user->id,
    ]);
    $payload = ['guardian_id' => $guardian->id];
    $response = $this->postJson(route($this->routeNamePrefix . 'unlink-guardian', ['user' => $this->user->id]), $payload)->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data']['userables'])->toBe([]);
});
