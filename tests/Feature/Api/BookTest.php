<?php

use App\Enums\BookBinding;
use App\Enums\BookCondition;
use App\Enums\BookLoanSettingType;
use App\Enums\BookStatus;
use App\Enums\LibraryBookLoanStatus;
use App\Http\Resources\AuthorResource;
use App\Http\Resources\BookCategoryResource;
use App\Http\Resources\BookClassificationResource;
use App\Http\Resources\BookLanguageResource;
use App\Http\Resources\BookLoanSettingResource;
use App\Http\Resources\BookSourceResource;
use App\Http\Resources\BookSubClassificationResource;
use App\Http\Resources\LibraryBookLoanWithMemberResource;
use App\Models\Author;
use App\Models\Book;
use App\Models\BookCategory;
use App\Models\BookClassification;
use App\Models\BookLoanSetting;
use App\Models\BookSource;
use App\Models\BookSubClassification;
use App\Models\LibraryBookLoan;
use App\Models\User;
use Database\Seeders\InternationalizationSeeder;
use Database\Seeders\PermissionSeeder;
use Illuminate\Database\Eloquent\Factories\Sequence;
use Illuminate\Support\Facades\Hash;
use Laravel\Sanctum\Sanctum;

beforeEach(function () {
    $this->seed([
        InternationalizationSeeder::class,
        PermissionSeeder::class,
    ]);

    app()->setLocale('en');

    $this->withHeaders([
        'Accept-Language' => 'en'
    ]);

    $user = User::factory()->create([
        'password' => Hash::make('123456'),
    ]);

    $user->assignRole('Super Admin');

    Sanctum::actingAs($user);

    $this->table = resolve(Book::class)->getTable();
    $this->bookLoanSettingTable = resolve(BookLoanSetting::class)->getTable();
    $this->authorBookTable = 'author_book';
    $this->routeNamePrefix = 'libraries.books';
});

test('index', function () {
    $book_sub_classifications = BookSubClassification::factory(2)->create();
    $authors = Author::factory(2)
        ->state(new Sequence(
            [
                'name' => 'David',
            ],
            [
                'name' => 'Simon'
            ]
        ))
        ->create();
    $books = Book::factory(4)->state(new Sequence(
        [
            'book_no' => '12345',
            'call_no' => '12345',
            'book_sub_classification_id' => $book_sub_classifications[0]->id,
            'title' => 'English',
            'status' => BookStatus::AVAILABLE,
            'isbn' => '123456789'
        ],
        [
            'book_no' => '12346',
            'call_no' => '12346',
            'book_sub_classification_id' => $book_sub_classifications[0]->id,
            'title' => 'English 2',
            'status' => BookStatus::AVAILABLE,
            'isbn' => '123456788'
        ],
        [
            'book_no' => '12347',
            'call_no' => '12347',
            'book_sub_classification_id' => $book_sub_classifications[1]->id,
            'title' => 'Tamil',
            'status' => BookStatus::LOST,
            'isbn' => '123456787'
        ],
        [
            'book_no' => '12348',
            'call_no' => '12348',
            'book_sub_classification_id' => $book_sub_classifications[1]->id,
            'title' => '全新思维',
            'isbn' => '123456786',
        ],
    ))->create();

    $books[0]->authors()->sync([$authors[0]->id]);
    $books[1]->authors()->sync([$authors[0]->id]);
    $books[2]->authors()->sync([$authors[1]->id]);
    $books[3]->authors()->sync([$authors[1]->id]);

    LibraryBookLoan::factory(2)->state(new Sequence(
        [
            'book_id' => $books[2]->id,
            'loan_status' => LibraryBookLoanStatus::RETURNED
        ],
        [
            'book_id' => $books[2]->id,
            'loan_status' => LibraryBookLoanStatus::BORROWED
        ]
    ))->create();

    foreach ($books as $book) {
        BookLoanSetting::factory(4)
            ->state(new Sequence(
                [
                    'book_id' => $book->id,
                    'type' => BookLoanSettingType::STUDENT->value,
                ],
                [
                    'book_id' => $book->id,
                    'type' => BookLoanSettingType::EMPLOYEE->value,
                ],
                [
                    'book_id' => $book->id,
                    'type' => BookLoanSettingType::LIBRARIAN->value,
                ],
                [
                    'book_id' => $book->id,
                    'type' => BookLoanSettingType::OTHERS->value,
                ],
            ))
            ->create();
    }

    //Filter by title = English 2
    $payload = [
        'title' => 'English 2'
    ];
    $response = $this->getJson(route("$this->routeNamePrefix.index", $payload))->json();

    expect($response)
        ->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toHaveCount(1)
        ->sequence(
            fn($data) => $data->toHaveKey('title', 'English 2')
        );

    //Filter by partial title = English
    $payload = [
        'title' => 'English'
    ];
    $response = $this->getJson(route("$this->routeNamePrefix.index", $payload))->json();

    expect($response)
        ->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toHaveCount(2)
        ->sequence(
            fn($data) => $data->toHaveKey('title', 'English'),
            fn($data) => $data->toHaveKey('title', 'English 2')
        );

    //Filter by keywords - wildcard
    $payload = [
        'title' => 'glis'
    ];
    $response = $this->getJson(route("$this->routeNamePrefix.index", $payload))->json();

    expect($response)
        ->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toHaveCount(2)
        ->sequence(
            fn($data) => $data->toHaveKey('title', 'English'),
            fn($data) => $data->toHaveKey('title', 'English 2')
        );

    $payload = [
        'title' => '新思'
    ];
    $response = $this->getJson(route("$this->routeNamePrefix.index", $payload))->json();

    expect($response)
        ->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toHaveCount(1)
        ->sequence(
            fn($data) => $data->toHaveKey('title', '全新思维')
        );

    $payload = [
        'title' => '全新思'
    ];
    $response = $this->getJson(route("$this->routeNamePrefix.index", $payload))->json();

    expect($response)
        ->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toHaveCount(1)
        ->sequence(
            fn($data) => $data->toHaveKey('title', '全新思维')
        );

    $payload = [
        'title' => '思维'
    ];
    $response = $this->getJson(route("$this->routeNamePrefix.index", $payload))->json();

    expect($response)
        ->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toHaveCount(1)
        ->sequence(
            fn($data) => $data->toHaveKey('title', '全新思维')
        );

    //Filter non-existing title = No Exist
    $payload = [
        'title' => 'No Exist'
    ];
    $response = $this->getJson(route("$this->routeNamePrefix.index", $payload))->json();

    expect($response)
        ->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toHaveCount(0);

    //Filter by author ids
    $payload = [
        'author_ids' => [$authors[0]->id]
    ];
    $response = $this->getJson(route("$this->routeNamePrefix.index", $payload))->json();

    expect($response)
        ->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toHaveCount(2)
        ->sequence(
            fn($data) => $data->toHaveKey('authors.0.id', $authors[0]->id),
            fn($data) => $data->toHaveKey('authors.0.id', $authors[0]->id)
        );

    //Filter non-existing author_ids
    $payload = [
        'author_ids' => [9999]
    ];
    $response = $this->getJson(route("$this->routeNamePrefix.index", $payload))->json();

    expect($response)
        ->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toHaveCount(0);

    //Filter by isbn
    $payload = [
        'isbn' => '123456789'
    ];
    $response = $this->getJson(route("$this->routeNamePrefix.index", $payload))->json();

    expect($response)
        ->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toHaveCount(1)
        ->sequence(
            fn($data) => $data->toHaveKey('isbn', '123456789')
        );

    //Filter non-existing isbn
    $payload = [
        'isbn' => 99999
    ];
    $response = $this->getJson(route("$this->routeNamePrefix.index", $payload))->json();

    expect($response)
        ->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toHaveCount(0);

    //Filter by book sub classification id = 1
    $payload = [
        'book_sub_classification_id' => $book_sub_classifications[1]->id
    ];
    $response = $this->getJson(route("$this->routeNamePrefix.index", $payload))->json();

    expect($response)
        ->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toHaveCount(2)
        ->sequence(
            fn($data) => $data->toHaveKey('book_sub_classification.id', $book_sub_classifications[1]->id)
        );

    //Filter non-existing book sub classification id = 99999
    $payload = [
        'book_sub_classification_id' => 99999
    ];
    $response = $this->getJson(route("$this->routeNamePrefix.index", $payload))->json();

    expect($response)
        ->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toHaveCount(0);

    //Filter by book_no = 12345
    $payload = [
        'book_no' => '12345'
    ];
    $response = $this->getJson(route("$this->routeNamePrefix.index", $payload))->json();

    expect($response)
        ->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toHaveCount(1)
        ->sequence(
            fn($data) => $data->toHaveKey('book_no', '12345')
        );

    //Filter by book_no_wildcard = 345
    $payload = [
        'book_no_wildcard' => '345'
    ];
    $response = $this->getJson(route("$this->routeNamePrefix.index", $payload))->json();

    expect($response)
        ->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toHaveCount(1)
        ->sequence(
            fn($data) => $data->toHaveKey('book_no', '12345')
        );

    //Filter non-existing book_no = No Exist
    $payload = [
        'book_no' => 'No Exist'
    ];
    $response = $this->getJson(route("$this->routeNamePrefix.index", $payload))->json();

    expect($response)
        ->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toHaveCount(0);

    //Filter by call_no = 12345
    $payload = [
        'call_no' => '12345'
    ];
    $response = $this->getJson(route("$this->routeNamePrefix.index", $payload))->json();

    expect($response)
        ->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toHaveCount(1)
        ->sequence(
            fn($data) => $data->toHaveKey('call_no', '12345')
        );

    //Filter by call_no_wildcard = 345
    $payload = [
        'call_no_wildcard' => '345'
    ];
    $response = $this->getJson(route("$this->routeNamePrefix.index", $payload))->json();

    expect($response)
        ->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toHaveCount(1)
        ->sequence(
            fn($data) => $data->toHaveKey('call_no', '12345')
        );

    //Filter non-existing call_no = No Exist
    $payload = [
        'call_no' => 'No Exist'
    ];
    $response = $this->getJson(route("$this->routeNamePrefix.index", $payload))->json();

    expect($response)
        ->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toHaveCount(0);


    //sort by title asc
    $payload = [
        'order_by' => ['title' => 'asc'],
    ];

    $response = $this->getJson(route("$this->routeNamePrefix.index", $payload))->json();

    expect($response)
        ->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toHaveCount(4)
        ->sequence(
            fn($data) => $data->toHaveKey('title', 'English'),
            fn($data) => $data->toHaveKey('title', 'English 2'),
            fn($data) => $data->toHaveKey('title', 'Tamil'),
            fn($data) => $data->toHaveKey('title', '全新思维'),
        );

    //sort by title desc
    $payload = [
        'order_by' => ['title' => 'desc'],
    ];

    $response = $this->getJson(route("$this->routeNamePrefix.index", $payload))->json();

    expect($response)
        ->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toHaveCount(4)
        ->sequence(
            fn($data) => $data->toHaveKey('title', '全新思维'),
            fn($data) => $data->toHaveKey('title', 'Tamil'),
            fn($data) => $data->toHaveKey('title', 'English 2'),
            fn($data) => $data->toHaveKey('title', 'English'),
        );

    //sort by id asc
    $payload = [
        'order_by' => ['id' => 'asc'],
    ];
    $response = $this->getJson(route("$this->routeNamePrefix.index", $payload))->json();

    expect($response)
        ->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toHaveCount(4)
        ->sequence(
            fn($data) => $data->toHaveKey('id', $books[0]->id),
            fn($data) => $data->toHaveKey('id', $books[1]->id),
            fn($data) => $data->toHaveKey('id', $books[2]->id),
            fn($data) => $data->toHaveKey('id', $books[3]->id),
        );

    //sort by id desc
    $payload = [
        'order_by' => ['id' => 'desc'],
    ];
    $response = $this->getJson(route("$this->routeNamePrefix.index", $payload))->json();

    expect($response)
        ->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toHaveCount(4)
        ->sequence(
            fn($data) => $data->toHaveKey('id', $books[3]->id),
            fn($data) => $data->toHaveKey('id', $books[2]->id),
            fn($data) => $data->toHaveKey('id', $books[1]->id),
            fn($data) => $data->toHaveKey('id', $books[0]->id),
        );

//    Test pattern
    $payload = [
        'title' => 'Tamil'
    ];
    $response = $this->getJson(route("$this->routeNamePrefix.index", $payload))->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'][0])->toEqual([
            'id' => $books[2]->id,
            'book_no' => $books[2]->book_no,
            'call_no' => $books[2]->call_no,
            'book_category' => resourceToArray(new BookCategoryResource($books[2]->bookCategory)),
            'book_classification' => resourceToArray(new BookClassificationResource($books[2]->bookClassification)),
            'book_sub_classification' => resourceToArray(new BookSubClassificationResource($books[2]->bookSubClassification)),
            'book_source' => resourceToArray(new BookSourceResource($books[2]->bookSource)),
            'authors' => resourceToArray(AuthorResource::collection($books[2]->authors)),
            'title' => $books[2]->title,
            'series' => $books[2]->series,
            'edition' => $books[2]->edition,
            'remark' => $books[2]->remark,
            'topic' => $books[2]->topic,
            'location_1' => $books[2]->location_1,
            'location_2' => $books[2]->location_2,
            'isbn' => $books[2]->isbn,
            'binding' => $books[2]->binding->value,
            'publisher' => $books[2]->publisher,
            'publisher_place' => $books[2]->publisher_place,
            'published_date' => $books[2]->published_date->format('Y-m-d'),
            'book_page' => $books[2]->book_page,
            'words' => $books[2]->words,
            'book_size' => $books[2]->book_size,
            'cdrom' => $books[2]->cdrom,
            'purchase_value' => $books[2]->purchase_value,
            'lost_penalty_value' => $books[2]->lost_penalty_value,
            'loan_settings' => resourceToArray(BookLoanSettingResource::collection($books[2]->loanSettings)),
            'book_loans' => resourceToArray(LibraryBookLoanWithMemberResource::collection($books[2]->bookLoans)),
            'active_book_loan' => resourceToArray(new LibraryBookLoanWithMemberResource($books[2]->activeBookLoan)),
            'condition' => $books[2]->condition->value,
            'status' => $books[2]->status->value,
            "entry_date" => $books[2]->entry_date,
            'book_language' => resourceToArray(new BookLanguageResource($books[2]->bookLanguage)),
        ]);
});

test('show', function () {
    $book = Book::factory()->create();
    BookLoanSetting::factory(4)
        ->state(new Sequence(
            [
                'book_id' => $book->id,
                'type' => BookLoanSettingType::STUDENT->value,
            ],
            [
                'book_id' => $book->id,
                'type' => BookLoanSettingType::EMPLOYEE->value,
            ],
            [
                'book_id' => $book->id,
                'type' => BookLoanSettingType::LIBRARIAN->value,
            ],
            [
                'book_id' => $book->id,
                'type' => BookLoanSettingType::OTHERS->value,
            ],
        ))
        ->create();
    LibraryBookLoan::factory(2)->state(new Sequence(
        [
            'book_id' => $book->id,
            'loan_status' => LibraryBookLoanStatus::RETURNED
        ],
        [
            'book_id' => $book->id,
            'loan_status' => LibraryBookLoanStatus::BORROWED
        ]
    ))->create();

    //test with id exist
    $response = $this->getJson(route("$this->routeNamePrefix.show", ['book' => $book->id]))->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toEqual([
            'id' => $book->id,
            'book_no' => $book->book_no,
            'call_no' => $book->call_no,
            'book_category' => resourceToArray(new BookCategoryResource($book->bookCategory)),
            'book_classification' => resourceToArray(new BookClassificationResource($book->bookClassification)),
            'book_sub_classification' => resourceToArray(new BookSubClassificationResource($book->bookSubClassification)),
            'book_source' => resourceToArray(new BookSourceResource($book->bookSource)),
            'authors' => resourceToArray(AuthorResource::collection($book->authors)),
            'title' => $book->title,
            'series' => $book->series,
            'edition' => $book->edition,
            'remark' => $book->remark,
            'topic' => $book->topic,
            'location_1' => $book->location_1,
            'location_2' => $book->location_2,
            'isbn' => $book->isbn,
            'binding' => $book->binding->value,
            'publisher' => $book->publisher,
            'publisher_place' => $book->publisher_place,
            'published_date' => $book->published_date->format('Y-m-d'),
            'book_page' => $book->book_page,
            'words' => $book->words,
            'book_size' => $book->book_size,
            'cdrom' => $book->cdrom,
            'purchase_value' => $book->purchase_value,
            'lost_penalty_value' => $book->lost_penalty_value,
            'loan_settings' => resourceToArray(BookLoanSettingResource::collection($book->loanSettings)),
            'book_loans' => resourceToArray(LibraryBookLoanWithMemberResource::collection($book->bookLoans)),
            'active_book_loan' => resourceToArray(new LibraryBookLoanWithMemberResource($book->activeBookLoan)),
            'condition' => $book->condition->value,
            'status' => $book->status->value,
            "entry_date" => $book->entry_date,
            'book_language' => resourceToArray(new BookLanguageResource($book->bookLanguage)),
        ]);

    //test with id not exist
    $response = $this->getJson(route("$this->routeNamePrefix.show", ['book' => 9999]))->json();

    expect($response)->toHaveModelResourceNotFoundResponse();
});

test('store', function () {
    //validation required failed
    $payload = [];
    $response = $this->postJson(route("$this->routeNamePrefix.create"), $payload)->json();

    expect($response)->toHaveFailedGeneralResponse()
        ->and($response['error'])->toBe([
            "book_no" => [
                "The book no field is required."
            ],
            "call_no" => [
                "The call no field is required."
            ],
            "book_category_id" => [
                "The book category id field is required."
            ],
            "book_classification_id" => [
                "The book classification id field is required."
            ],
            "book_sub_classification_id" => [
                "The book sub classification id field is required."
            ],
            "title" => [
                "The title field is required."
            ],
            "book_source_id" => [
                "The book source id field is required."
            ],
            "binding" => [
                "The binding field is required."
            ],
            "cdrom" => [
                "The cdrom field is required."
            ],
            "purchase_value" => [
                "The purchase value field is required."
            ],
            "lost_penalty_value" => [
                "The lost penalty value field is required."
            ],
            "authors" => [
                "The authors field is required."
            ],
            'loan_settings.EMPLOYEE.loan_period_day' => [
                'The loan settings.EMPLOYEE.loan period day field is required.',
            ],
            'loan_settings.EMPLOYEE.can_borrow' => [
                'The loan settings.EMPLOYEE.can borrow field is required.',
            ],
            'loan_settings.STUDENT.loan_period_day' => [
                'The loan settings.STUDENT.loan period day field is required.',
            ],
            'loan_settings.STUDENT.can_borrow' => [
                'The loan settings.STUDENT.can borrow field is required.',
            ],
            'loan_settings.LIBRARIAN.loan_period_day' => [
                'The loan settings.LIBRARIAN.loan period day field is required.',
            ],
            'loan_settings.LIBRARIAN.can_borrow' => [
                'The loan settings.LIBRARIAN.can borrow field is required.',
            ],
            'loan_settings.OTHERS.loan_period_day' => [
                'The loan settings.OTHERS.loan period day field is required.',
            ],
            'loan_settings.OTHERS.can_borrow' => [
                'The loan settings.OTHERS.can borrow field is required.',
            ],
        ]);
    //Validation valid failed
    $payload = [
        'book_no' => uniqid(),
        'call_no' => fake()->sentence(),
        'book_category_id' => 9999,
        'book_classification_id' => 9999,
        'book_sub_classification_id' => 9999,
        'book_source_id' => 9999,
        'title' => fake()->title,
        'series' => fake()->sentence(),
        'edition' => fake()->sentence(),
        'remark' => fake()->sentence(),
        'topic' => fake()->title(),
        'location_1' => fake()->sentence(),
        'location_2' => fake()->sentence(),
        'isbn' => fake()->sentence(),
        'binding' => 'non exist binding',
        'publisher' => fake()->sentence(),
        'publisher_place' => fake()->address(),
        'published_date' => fake()->date(),
        'book_page' => 100,
        'words' => 1000,
        'book_size' => '12 inch',
        'cdrom' => false,
        'purchase_value' => 20,
        'lost_penalty_value' => 40,
        'authors' => [
            'Simon',
            'David'
        ],
        'loan_settings' => [
            BookLoanSettingType::EMPLOYEE->value => [
                'loan_period_day' => 5,
                'can_borrow' => true
            ],
            BookLoanSettingType::STUDENT->value => [
                'loan_period_day' => 10,
                'can_borrow' => true
            ],
            BookLoanSettingType::LIBRARIAN->value => [
                'loan_period_day' => 15,
                'can_borrow' => true
            ],
            BookLoanSettingType::OTHERS->value => [
                'loan_period_day' => 20,
                'can_borrow' => false
            ],
        ]
    ];

    $response = $this->postJson(route("$this->routeNamePrefix.create"), $payload)->json();

    expect($response)->toHaveFailedGeneralResponse()
        ->and($response['error'])->toEqual([
            "book_category_id" => [
                "The selected book category id is invalid.",
            ],
            "book_classification_id" => [
                "The selected book classification id is invalid.",
            ],
            "book_sub_classification_id" => [
                "The selected book sub classification id is invalid.",
            ],
            "book_source_id" => [
                "The selected book source id is invalid.",
            ],
            "binding" => [
                "The selected binding is invalid.",
            ],
        ]);

    //Test unique failed
    Book::factory()->create([
        'book_no' => '123456',
    ]);
    $book_classification = BookClassification::factory()->create();
    $book_sub_classification = BookSubClassification::factory()->create([
        'book_classification_id' => $book_classification->id,
    ]);
    $book_category = BookCategory::factory()->create();
    $book_source = BookSource::factory()->create();

    $payload = [
        'book_no' => '123456',
        'call_no' => fake()->sentence(),
        'book_category_id' => $book_category->id,
        'book_classification_id' => $book_classification->id,
        'book_sub_classification_id' => $book_sub_classification->id,
        'book_source_id' => $book_source->id,
        'title' => fake()->title,
        'series' => fake()->sentence(),
        'edition' => fake()->sentence(),
        'remark' => fake()->sentence(),
        'topic' => fake()->title(),
        'location_1' => fake()->sentence(),
        'location_2' => fake()->sentence(),
        'isbn' => fake()->sentence(),
        'binding' => BookBinding::HARD_COVER->value,
        'publisher' => fake()->sentence(),
        'publisher_place' => fake()->address(),
        'published_date' => fake()->date(),
        'book_page' => 100,
        'words' => 1000,
        'book_size' => '12 inch',
        'cdrom' => false,
        'authors' => [
            'David',
            'Simon',
        ],
        'loan_settings' => [
            BookLoanSettingType::EMPLOYEE->value => [
                'loan_period_day' => 5,
                'can_borrow' => true
            ],
            BookLoanSettingType::STUDENT->value => [
                'loan_period_day' => 10,
                'can_borrow' => true
            ],
            BookLoanSettingType::LIBRARIAN->value => [
                'loan_period_day' => 15,
                'can_borrow' => true
            ],
            BookLoanSettingType::OTHERS->value => [
                'loan_period_day' => 20,
                'can_borrow' => false
            ],
        ]
    ];

    $response = $this->postJson(route("$this->routeNamePrefix.create"), $payload)->json();

    expect($response)->toHaveFailedGeneralResponse()
        ->and($response['error'])
        ->toHaveKey('book_no.0', 'The book no has already been taken.');

    //store success
    $this->assertDatabaseCount($this->table, 1);

    $payload = [
        'book_no' => '123457',
        'call_no' => fake()->sentence(),
        'book_category_id' => $book_category->id,
        'book_classification_id' => $book_classification->id,
        'book_sub_classification_id' => $book_sub_classification->id,
        'book_source_id' => $book_source->id,
        'title' => fake()->title,
        'series' => fake()->sentence(),
        'edition' => fake()->sentence(),
        'remark' => fake()->sentence(),
        'topic' => fake()->title(),
        'location_1' => fake()->sentence(),
        'location_2' => fake()->sentence(),
        'isbn' => fake()->sentence(),
        'binding' => BookBinding::HARD_COVER->value,
        'publisher' => fake()->sentence(),
        'publisher_place' => fake()->address(),
        'published_date' => fake()->date(),
        'book_page' => 100,
        'words' => 1000,
        'book_size' => '12 inch',
        'cdrom' => false,
        'purchase_value' => 20,
        'lost_penalty_value' => 40,
        'authors' => [
            'David',
            'Simon',
        ],
        'entry_date' => '1960-01-01',
        'loan_settings' => [
            BookLoanSettingType::EMPLOYEE->value => [
                'loan_period_day' => 5,
                'can_borrow' => true
            ],
            BookLoanSettingType::STUDENT->value => [
                'loan_period_day' => 10,
                'can_borrow' => true
            ],
            BookLoanSettingType::LIBRARIAN->value => [
                'loan_period_day' => 15,
                'can_borrow' => true
            ],
            BookLoanSettingType::OTHERS->value => [
                'loan_period_day' => 20,
                'can_borrow' => false
            ],
        ]
    ];

    $response = $this->postJson(route("$this->routeNamePrefix.create"), $payload)->json();

    $mapping_loan_settings = [
        array_merge(['type' => BookLoanSettingType::EMPLOYEE->value], $payload['loan_settings'][BookLoanSettingType::EMPLOYEE->value]),
        array_merge(['type' => BookLoanSettingType::STUDENT->value], $payload['loan_settings'][BookLoanSettingType::STUDENT->value]),
        array_merge(['type' => BookLoanSettingType::LIBRARIAN->value], $payload['loan_settings'][BookLoanSettingType::LIBRARIAN->value]),
        array_merge(['type' => BookLoanSettingType::OTHERS->value], $payload['loan_settings'][BookLoanSettingType::OTHERS->value]),
    ];

    $this->assertDatabaseCount(BookLoanSetting::class, 4);

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toMatchArray([
            'book_no' => $payload['book_no'],
            'call_no' => $payload['call_no'],
            'book_category' => resourceToArray(new BookCategoryResource($book_category)),
            'book_classification' => resourceToArray(new BookClassificationResource($book_classification)),
            'book_sub_classification' => resourceToArray(new BookSubClassificationResource($book_sub_classification)),
            'book_source' => resourceToArray(new BookSourceResource($book_source)),
            'title' => $payload['title'],
            'series' => $payload['series'],
            'edition' => $payload['edition'],
            'remark' => $payload['remark'],
            'topic' => $payload['topic'],
            'location_1' => $payload['location_1'],
            'location_2' => $payload['location_2'],
            'isbn' => $payload['isbn'],
            'binding' => $payload['binding'],
            'publisher' => $payload['publisher'],
            'publisher_place' => $payload['publisher_place'],
            'published_date' => $payload['published_date'],
            'book_page' => $payload['book_page'],
            'words' => $payload['words'],
            'book_size' => $payload['book_size'],
            'cdrom' => $payload['cdrom'],
            'purchase_value' => $payload['purchase_value'],
            'lost_penalty_value' => $payload['lost_penalty_value'],
            'entry_date' => $payload['entry_date'],
        ])->toHaveKey('authors', [
            [
                'name' => 'David',
                'id' => Author::where('name', 'David')->first()->id
            ],
            [
                'name' => 'Simon',
                'id' => Author::where('name', 'Simon')->first()->id
            ]
        ]);

    foreach ($response['data']['loan_settings'] as $setting) {
        expect($mapping_loan_settings)->toContain($setting);
    }

    $this->assertDatabaseCount($this->table, 2);

    $this->assertDatabaseHas($this->table, [
        'book_no' => $payload['book_no'],
        'call_no' => $payload['call_no'],
        'book_category_id' => $payload['book_category_id'],
        'book_classification_id' => $payload['book_classification_id'],
        'book_sub_classification_id' => $payload['book_sub_classification_id'],
        'book_source_id' => $payload['book_source_id'],
        'title' => $payload['title'],
        'series' => $payload['series'],
        'edition' => $payload['edition'],
        'remark' => $payload['remark'],
        'topic' => $payload['topic'],
        'location_1' => $payload['location_1'],
        'location_2' => $payload['location_2'],
        'isbn' => $payload['isbn'],
        'binding' => $payload['binding'],
        'publisher' => $payload['publisher'],
        'publisher_place' => $payload['publisher_place'],
        'published_date' => $payload['published_date'],
        'book_page' => $payload['book_page'],
        'words' => $payload['words'],
        'book_size' => $payload['book_size'],
        'cdrom' => $payload['cdrom'],
        'purchase_value' => $payload['purchase_value'],
        'lost_penalty_value' => $payload['lost_penalty_value'],
        'entry_date' => $payload['entry_date'],
    ]);

    //author_book table inserted 2 records
    $this->assertDatabaseCount($this->authorBookTable, 2);

    foreach (['David', 'Simon'] as $author) {
        $this->assertDatabaseHas('master_authors', [
            'name' => $author
        ]);

        $this->assertDatabaseHas($this->authorBookTable, [
            'book_id' => $response['data']['id'],
            'author_id' => Author::where('name', $author)->first()->id
        ]);
    }

    //book_loan_settings table inserted 4 records
    $this->assertDatabaseCount($this->bookLoanSettingTable, 4);

    foreach ($payload['loan_settings'] as $type => $setting) {
        $this->assertDatabaseHas($this->bookLoanSettingTable, [
            'book_id' => $response['data']['id'],
            'type' => $type,
            'loan_period_day' => $setting['loan_period_day'],
            'can_borrow' => $setting['can_borrow'],
        ]);
    }
});

test('update', function () {
    $book_classification = BookClassification::factory()->create();
    $book_sub_classification = BookSubClassification::factory()->create([
        'book_classification_id' => $book_classification->id,
    ]);
    $book_category = BookCategory::factory()->create();
    $book_source = BookSource::factory()->create();
    $authors = Author::factory()->create([
        'name' => 'David'
    ]);

    $book = Book::factory()->create([
        'book_no' => '123457',
        'call_no' => fake()->sentence(),
        'book_category_id' => $book_category->id,
        'book_classification_id' => $book_classification->id,
        'book_sub_classification_id' => $book_sub_classification->id,
        'book_source_id' => $book_source->id,
        'title' => fake()->title,
        'series' => fake()->sentence(),
        'edition' => fake()->sentence(),
        'remark' => fake()->sentence(),
        'topic' => fake()->title(),
        'location_1' => fake()->sentence(),
        'location_2' => fake()->sentence(),
        'isbn' => fake()->sentence(),
        'binding' => BookBinding::HARD_COVER->value,
        'publisher' => fake()->sentence(),
        'publisher_place' => fake()->address(),
        'published_date' => fake()->date(),
        'book_page' => 100,
        'words' => 1000,
        'book_size' => '12 inch',
        'cdrom' => false,
    ]);
    $book->authors()->sync($authors->pluck('id'));

    BookLoanSetting::factory(4)
        ->state(new Sequence(
            [
                'book_id' => $book->id,
                'type' => BookLoanSettingType::EMPLOYEE->value,
                'loan_period_day' => 0,
                'can_borrow' => false
            ],
            [
                'book_id' => $book->id,
                'type' => BookLoanSettingType::STUDENT->value,
                'loan_period_day' => 0,
                'can_borrow' => false
            ],
            [
                'book_id' => $book->id,
                'type' => BookLoanSettingType::LIBRARIAN->value,
                'loan_period_day' => 0,
                'can_borrow' => false
            ],
            [
                'book_id' => $book->id,
                'type' => BookLoanSettingType::OTHERS->value,
                'loan_period_day' => 0,
                'can_borrow' => false
            ],
        ))
        ->create();

    //validation required to be failed
    $payload = [];
    $response = $this->putJson(route("$this->routeNamePrefix.update", ['book' => $book->id]), $payload)->json();

    expect($response)->toHaveFailedGeneralResponse()
        ->and($response['error'])->toBe([
            "book_no" => [
                "The book no field is required."
            ],
            "call_no" => [
                "The call no field is required."
            ],
            "book_category_id" => [
                "The book category id field is required."
            ],
            "book_classification_id" => [
                "The book classification id field is required."
            ],
            "book_sub_classification_id" => [
                "The book sub classification id field is required."
            ],
            "title" => [
                "The title field is required."
            ],
            "book_source_id" => [
                "The book source id field is required."
            ],
            "binding" => [
                "The binding field is required."
            ],
            "cdrom" => [
                "The cdrom field is required."
            ],
            "purchase_value" => [
                "The purchase value field is required."
            ],
            "lost_penalty_value" => [
                "The lost penalty value field is required."
            ],
            "authors" => [
                "The authors field is required."
            ],
            'loan_settings.EMPLOYEE.loan_period_day' => [
                'The loan settings.EMPLOYEE.loan period day field is required.',
            ],
            'loan_settings.EMPLOYEE.can_borrow' => [
                'The loan settings.EMPLOYEE.can borrow field is required.',
            ],
            'loan_settings.STUDENT.loan_period_day' => [
                'The loan settings.STUDENT.loan period day field is required.',
            ],
            'loan_settings.STUDENT.can_borrow' => [
                'The loan settings.STUDENT.can borrow field is required.',
            ],
            'loan_settings.LIBRARIAN.loan_period_day' => [
                'The loan settings.LIBRARIAN.loan period day field is required.',
            ],
            'loan_settings.LIBRARIAN.can_borrow' => [
                'The loan settings.LIBRARIAN.can borrow field is required.',
            ],
            'loan_settings.OTHERS.loan_period_day' => [
                'The loan settings.OTHERS.loan period day field is required.',
            ],
            'loan_settings.OTHERS.can_borrow' => [
                'The loan settings.OTHERS.can borrow field is required.',
            ],
        ]);

    //Validation valid failed
    $payload = [
        'book_no' => uniqid(),
        'call_no' => fake()->sentence(),
        'book_category_id' => 9999,
        'book_classification_id' => 9999,
        'book_sub_classification_id' => 9999,
        'book_source_id' => 9999,
        'title' => fake()->title,
        'series' => fake()->sentence(),
        'edition' => fake()->sentence(),
        'remark' => fake()->sentence(),
        'topic' => fake()->title(),
        'location_1' => fake()->sentence(),
        'location_2' => fake()->sentence(),
        'isbn' => fake()->sentence(),
        'binding' => 'non exist binding',
        'publisher' => fake()->sentence(),
        'publisher_place' => fake()->address(),
        'published_date' => fake()->date(),
        'book_page' => 100,
        'words' => 1000,
        'book_size' => '12 inch',
        'cdrom' => false,
        'authors' => [
            'David',
            'Simon'
        ],
        'loan_settings' => [
            BookLoanSettingType::EMPLOYEE->value => [
                'loan_period_day' => 5,
                'can_borrow' => true
            ],
            BookLoanSettingType::STUDENT->value => [
                'loan_period_day' => 10,
                'can_borrow' => true
            ],
            BookLoanSettingType::LIBRARIAN->value => [
                'loan_period_day' => 15,
                'can_borrow' => true
            ],
            BookLoanSettingType::OTHERS->value => [
                'loan_period_day' => 20,
                'can_borrow' => false
            ],
        ]
    ];

    $response = $this->putJson(route("$this->routeNamePrefix.update", ['book' => $book->id]), $payload)->json();

    expect($response)->toHaveFailedGeneralResponse()
        ->and($response['error'])->toBe([
            "book_category_id" => [
                "The selected book category id is invalid."
            ],
            "book_classification_id" => [
                "The selected book classification id is invalid."
            ],
            "book_sub_classification_id" => [
                "The selected book sub classification id is invalid."
            ],
            "book_source_id" => [
                "The selected book source id is invalid."
            ],
            "binding" => [
                "The selected binding is invalid."
            ],
            "purchase_value" => [
                "The purchase value field is required."
            ],
            "lost_penalty_value" => [
                "The lost penalty value field is required."
            ],
        ]);

    //Test unique failed
    Book::factory()->create([
        'book_no' => '123456',
    ]);
    $book_classification2 = BookClassification::factory()->create();
    $book_sub_classification2 = BookSubClassification::factory()->create([
        'book_classification_id' => $book_classification2->id,
    ]);
    $book_category2 = BookCategory::factory()->create();
    $book_source2 = BookSource::factory()->create();
    $authors2 = Author::factory(2)->create();

    $payload = [
        'book_no' => '123456',
        'call_no' => fake()->sentence(),
        'book_category_id' => $book_category2->id,
        'book_classification_id' => $book_classification2->id,
        'book_sub_classification_id' => $book_sub_classification2->id,
        'book_source_id' => $book_source2->id,
        'title' => fake()->title,
        'series' => fake()->sentence(),
        'edition' => fake()->sentence(),
        'remark' => fake()->sentence(),
        'topic' => fake()->title(),
        'location_1' => fake()->sentence(),
        'location_2' => fake()->sentence(),
        'isbn' => fake()->sentence(),
        'binding' => BookBinding::HARD_COVER->value,
        'publisher' => fake()->sentence(),
        'publisher_place' => fake()->address(),
        'published_date' => fake()->date(),
        'book_page' => 100,
        'words' => 1000,
        'book_size' => '12 inch',
        'cdrom' => false,
        'authors' => [
            'David',
            'Simon'
        ],
        'loan_settings' => [
            BookLoanSettingType::EMPLOYEE->value => [
                'loan_period_day' => 5,
                'can_borrow' => true
            ],
            BookLoanSettingType::STUDENT->value => [
                'loan_period_day' => 10,
                'can_borrow' => true
            ],
            BookLoanSettingType::LIBRARIAN->value => [
                'loan_period_day' => 15,
                'can_borrow' => true
            ],
            BookLoanSettingType::OTHERS->value => [
                'loan_period_day' => 20,
                'can_borrow' => false
            ],
        ]
    ];

    $response = $this->putJson(route("$this->routeNamePrefix.update", ['book' => $book->id]), $payload)->json();

    expect($response)->toHaveFailedGeneralResponse()
        ->and($response['error'])
        ->toHaveKey('book_no.0', 'The book no has already been taken.');

    //update success
    $this->assertDatabaseCount($this->table, 2);

    $payload = [
        'book_no' => '123458',
        'call_no' => fake()->sentence(),
        'book_category_id' => $book_category2->id,
        'book_classification_id' => $book_classification2->id,
        'book_sub_classification_id' => $book_sub_classification2->id,
        'book_source_id' => $book_source2->id,
        'title' => 'test 1',
        'series' => fake()->sentence(),
        'edition' => fake()->sentence(),
        'remark' => fake()->sentence(),
        'topic' => fake()->title(),
        'location_1' => fake()->sentence(),
        'location_2' => fake()->sentence(),
        'isbn' => fake()->sentence(),
        'binding' => BookBinding::HARD_COVER->value,
        'publisher' => fake()->sentence(),
        'publisher_place' => fake()->address(),
        'published_date' => fake()->date(),
        'book_page' => 100,
        'words' => 1000,
        'book_size' => '12 inch',
        'cdrom' => false,
        'purchase_value' => 10,
        'lost_penalty_value' => 20,
        'entry_date' => '1960-01-01',
        'condition' => BookCondition::GOOD->value, // Same as the book's current condition
        'authors' => [
            'David',
            'Simon'
        ],
        'loan_settings' => [
            BookLoanSettingType::EMPLOYEE->value => [
                'loan_period_day' => 5,
                'can_borrow' => true
            ],
            BookLoanSettingType::STUDENT->value => [
                'loan_period_day' => 10,
                'can_borrow' => true
            ],
            BookLoanSettingType::LIBRARIAN->value => [
                'loan_period_day' => 15,
                'can_borrow' => true
            ],
            BookLoanSettingType::OTHERS->value => [
                'loan_period_day' => 30,
                'can_borrow' => false
            ],
        ]
    ];

    $this->assertDatabaseCount(BookLoanSetting::class, 4);

    $response = $this->putJson(route("$this->routeNamePrefix.update", ['book' => $book->id]), $payload)->json();

    $this->assertDatabaseCount(BookLoanSetting::class, 4);

    $mapping_loan_settings = [
        array_merge(['type' => BookLoanSettingType::EMPLOYEE->value], $payload['loan_settings'][BookLoanSettingType::EMPLOYEE->value]),
        array_merge(['type' => BookLoanSettingType::STUDENT->value], $payload['loan_settings'][BookLoanSettingType::STUDENT->value]),
        array_merge(['type' => BookLoanSettingType::LIBRARIAN->value], $payload['loan_settings'][BookLoanSettingType::LIBRARIAN->value]),
        array_merge(['type' => BookLoanSettingType::OTHERS->value], $payload['loan_settings'][BookLoanSettingType::OTHERS->value]),
    ];

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toMatchArray([
            'book_no' => $payload['book_no'],
            'call_no' => $payload['call_no'],
            'book_category' => resourceToArray(new BookCategoryResource($book_category2)),
            'book_classification' => resourceToArray(new BookClassificationResource($book_classification2)),
            'book_sub_classification' => resourceToArray(new BookSubClassificationResource($book_sub_classification2)),
            'book_source' => resourceToArray(new BookSourceResource($book_source2)),
            'title' => $payload['title'],
            'series' => $payload['series'],
            'edition' => $payload['edition'],
            'remark' => $payload['remark'],
            'topic' => $payload['topic'],
            'location_1' => $payload['location_1'],
            'location_2' => $payload['location_2'],
            'isbn' => $payload['isbn'],
            'binding' => $payload['binding'],
            'publisher' => $payload['publisher'],
            'publisher_place' => $payload['publisher_place'],
            'published_date' => $payload['published_date'],
            'book_page' => $payload['book_page'],
            'words' => $payload['words'],
            'book_size' => $payload['book_size'],
            'cdrom' => $payload['cdrom'],
            'purchase_value' => $payload['purchase_value'],
            'lost_penalty_value' => $payload['lost_penalty_value'],
            'entry_date' => $payload['entry_date'],
        ])->and($response['data']['authors'])->toHaveCount(2)
        ->sequence(
            fn($data) => $data->toHaveKey('name', 'David'),
            fn($data) => $data->toHaveKey('name', 'Simon')
        );

    foreach ($response['data']['loan_settings'] as $setting) {
        expect($mapping_loan_settings)->toContain($setting);
    }

    $this->assertDatabaseCount($this->table, 2);

    $this->assertDatabaseHas($this->table, [
        'book_no' => $payload['book_no'],
        'call_no' => $payload['call_no'],
        'book_category_id' => $payload['book_category_id'],
        'book_classification_id' => $payload['book_classification_id'],
        'book_sub_classification_id' => $payload['book_sub_classification_id'],
        'book_source_id' => $payload['book_source_id'],
        'title' => $payload['title'],
        'series' => $payload['series'],
        'edition' => $payload['edition'],
        'remark' => $payload['remark'],
        'topic' => $payload['topic'],
        'location_1' => $payload['location_1'],
        'location_2' => $payload['location_2'],
        'isbn' => $payload['isbn'],
        'binding' => $payload['binding'],
        'publisher' => $payload['publisher'],
        'publisher_place' => $payload['publisher_place'],
        'published_date' => $payload['published_date'],
        'book_page' => $payload['book_page'],
        'words' => $payload['words'],
        'book_size' => $payload['book_size'],
        'cdrom' => $payload['cdrom'],
        'purchase_value' => $payload['purchase_value'],
        'lost_penalty_value' => $payload['lost_penalty_value'],
        'entry_date' => $payload['entry_date'],
    ]);

    //author_book table inserted 2 records
    $this->assertDatabaseCount($this->authorBookTable, 2);

    foreach (['David', 'Simon'] as $author) {
        $this->assertDatabaseHas($this->authorBookTable, [
            'book_id' => $response['data']['id'],
            'author_id' => Author::where('name', $author)->first()->id
        ]);

        $this->assertDatabaseHas($this->authorBookTable, [
            'book_id' => $response['data']['id'],
            'author_id' => Author::where('name', $author)->first()->id
        ]);
    }

    //book_loan_settings table updated 4 records
    $this->assertDatabaseCount($this->bookLoanSettingTable, 4);

    foreach ($payload['loan_settings'] as $type => $setting) {
        $this->assertDatabaseHas($this->bookLoanSettingTable, [
            'book_id' => $response['data']['id'],
            'type' => $type,
            'loan_period_day' => $setting['loan_period_day'],
            'can_borrow' => $setting['can_borrow'],
        ]);
    }

    //update with id not exist
    $payload['title'] = 'test 3';

    $response = $this->putJson(route("$this->routeNamePrefix.update", ['book' => 9999]), $payload)->json();

    expect($response)->toHaveModelResourceNotFoundResponse();

    // Assert nothing updated
    $this->assertDatabaseCount($this->table, 2);
    $this->assertDatabaseHas($this->table, [
        'title' => 'test 1'
    ]);
});

test('store - automatically sets status based on condition', function () {
    $book_classification = BookClassification::factory()->create();
    $book_sub_classification = BookSubClassification::factory()->create([
        'book_classification_id' => $book_classification->id,
    ]);
    $book_category = BookCategory::factory()->create();
    $book_source = BookSource::factory()->create();

    $base_payload = [
        'call_no' => fake()->sentence(),
        'book_category_id' => $book_category->id,
        'book_classification_id' => $book_classification->id,
        'book_sub_classification_id' => $book_sub_classification->id,
        'book_source_id' => $book_source->id,
        'title' => fake()->title,
        'binding' => BookBinding::HARD_COVER->value,
        'purchase_value' => 10,
        'lost_penalty_value' => 20,
        'authors' => ['Test Author'],
        'cdrom' => false,
        'loan_settings' => [
            BookLoanSettingType::STUDENT->value => [
                'loan_period_day' => 10,
                'can_borrow' => true
            ],
            BookLoanSettingType::EMPLOYEE->value => [
                'loan_period_day' => 5,
                'can_borrow' => true
            ],
            BookLoanSettingType::LIBRARIAN->value => [
                'loan_period_day' => 15,
                'can_borrow' => true
            ],
            BookLoanSettingType::OTHERS->value => [
                'loan_period_day' => 20,
                'can_borrow' => false
            ],
        ]
    ];

    // Test WRITE_OFF condition sets status to NOT_AVAILABLE
    $payload = array_merge($base_payload, [
        'book_no' => '123458',
        'condition' => BookCondition::WRITE_OFF->value
    ]);

    $response = $this->postJson(route("$this->routeNamePrefix.create"), $payload)->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data']['status'])->toBe(BookStatus::NOT_AVAILABLE->value)
        ->and($response['data']['condition'])->toBe(BookCondition::WRITE_OFF->value);

    // Test MISSING condition sets status to LOST
    $payload = array_merge($base_payload, [
        'book_no' => '123459',
        'condition' => BookCondition::MISSING->value
    ]);

    $response = $this->postJson(route("$this->routeNamePrefix.create"), $payload)->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data']['status'])->toBe(BookStatus::LOST->value)
        ->and($response['data']['condition'])->toBe(BookCondition::MISSING->value);

    // Test GOOD condition sets status to AVAILABLE
    $payload = array_merge($base_payload, [
        'book_no' => '123460',
        'condition' => BookCondition::GOOD->value
    ]);

    $response = $this->postJson(route("$this->routeNamePrefix.create"), $payload)->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data']['status'])->toBe(BookStatus::AVAILABLE->value)
        ->and($response['data']['condition'])->toBe(BookCondition::GOOD->value);
});

test('destroy', function () {
    $authors = Author::factory(2)->create();
    $book = Book::factory()->create();
    $other_books = Book::factory(3)->create();

    $book->authors()->sync($authors->pluck('id'));

    $this->assertDatabaseCount($this->table, 4);

    //id not exist
    $response = $this->deleteJson(route("$this->routeNamePrefix.destroy", ['book' => 9999]))->json();
    expect($response)->toHaveModelResourceNotFoundResponse();

    $this->assertDatabaseCount($this->table, 4);

    //delete success
    $response = $this->deleteJson(route("$this->routeNamePrefix.destroy", ['book' => $book->id]))->json();
    expect($response)->toHaveSuccessGeneralResponse();

    $this->assertDatabaseCount($this->table, 3);
    $this->assertDatabaseMissing($this->table, ['id' => $book->id]);

    foreach ($other_books as $other_book) {
        $this->assertDatabaseHas($this->table, ['id' => $other_book->id]);
    }
});

test('recover', function () {
    $books = Book::factory(3)
        ->state(new Sequence(
            [
                'status' => BookStatus::AVAILABLE
            ],
            [
                'status' => BookStatus::BORROWED
            ],
            [
                'status' => BookStatus::LOST
            ],
        ))
        ->create();


    //validation book id not found
    $response = $this->postJson(route("$this->routeNamePrefix.recover-lost-book", ['book' => 9999]))->json();

    expect($response)->toHaveModelResourceNotFoundResponse();

    //validation book status must be lost
    $response = $this->postJson(route("$this->routeNamePrefix.recover-lost-book", ['book' => $books[1]->id]))->json();

    expect($response)->toHaveFailedGeneralResponse()
        ->and($response['error'])->toBe('Only lost book can be recovered.');

    //Success
    $response = $this->postJson(route("$this->routeNamePrefix.recover-lost-book", ['book' => $books[2]->id]))->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toMatchArray([
            'id' => $books[2]->id,
            'status' => BookStatus::AVAILABLE->value
        ]);

    $this->assertDatabaseHas($this->table, [
        'id' => $books[2]->id,
        'status' => BookStatus::AVAILABLE->value
    ]);
});

test('update should block condition change when book is borrowed', function () {
    $book_classification = BookClassification::factory()->create();
    $book_sub_classification = BookSubClassification::factory()->create([
        'book_classification_id' => $book_classification->id,
    ]);
    $book_category = BookCategory::factory()->create();
    $book_source = BookSource::factory()->create();
    $authors = Author::factory()->create([
        'name' => 'David'
    ]);

    // Create a book with GOOD condition and BORROWED status
    $book = Book::factory()->create([
        'book_no' => '123459',
        'call_no' => fake()->sentence(),
        'book_category_id' => $book_category->id,
        'book_classification_id' => $book_classification->id,
        'book_sub_classification_id' => $book_sub_classification->id,
        'book_source_id' => $book_source->id,
        'title' => fake()->title,
        'series' => fake()->sentence(),
        'edition' => fake()->sentence(),
        'remark' => fake()->sentence(),
        'topic' => fake()->title(),
        'location_1' => fake()->sentence(),
        'location_2' => fake()->sentence(),
        'isbn' => fake()->sentence(),
        'binding' => BookBinding::HARD_COVER->value,
        'publisher' => fake()->sentence(),
        'publisher_place' => fake()->address(),
        'published_date' => fake()->date(),
        'book_page' => 100,
        'words' => 1000,
        'book_size' => '12 inch',
        'cdrom' => false,
        'condition' => BookCondition::GOOD,
        'status' => BookStatus::BORROWED,
    ]);
    $book->authors()->sync($authors->pluck('id'));

    BookLoanSetting::factory(4)
        ->state(new Sequence(
            [
                'book_id' => $book->id,
                'type' => BookLoanSettingType::EMPLOYEE->value,
                'loan_period_day' => 5,
                'can_borrow' => true
            ],
            [
                'book_id' => $book->id,
                'type' => BookLoanSettingType::STUDENT->value,
                'loan_period_day' => 10,
                'can_borrow' => true
            ],
            [
                'book_id' => $book->id,
                'type' => BookLoanSettingType::LIBRARIAN->value,
                'loan_period_day' => 15,
                'can_borrow' => true
            ],
            [
                'book_id' => $book->id,
                'type' => BookLoanSettingType::OTHERS->value,
                'loan_period_day' => 20,
                'can_borrow' => false
            ],
        ))
        ->create();

    // Attempt to update the book with a different condition (DAMAGE) while it's borrowed
    $payload = [
        'book_no' => $book->book_no,
        'call_no' => $book->call_no,
        'book_category_id' => $book->book_category_id,
        'book_classification_id' => $book->book_classification_id,
        'book_sub_classification_id' => $book->book_sub_classification_id,
        'book_source_id' => $book->book_source_id,
        'title' => $book->title,
        'series' => $book->series,
        'edition' => $book->edition,
        'remark' => $book->remark,
        'topic' => $book->topic,
        'location_1' => $book->location_1,
        'location_2' => $book->location_2,
        'isbn' => $book->isbn,
        'binding' => $book->binding->value,
        'publisher' => $book->publisher,
        'publisher_place' => $book->publisher_place,
        'published_date' => $book->published_date->format('Y-m-d'),
        'book_page' => $book->book_page,
        'words' => $book->words,
        'book_size' => $book->book_size,
        'cdrom' => $book->cdrom,
        'purchase_value' => 20,
        'lost_penalty_value' => 40,
        'condition' => BookCondition::DAMAGE->value, // Different condition from current (GOOD)
        'authors' => [
            'David'
        ],
        'loan_settings' => [
            BookLoanSettingType::EMPLOYEE->value => [
                'loan_period_day' => 5,
                'can_borrow' => true
            ],
            BookLoanSettingType::STUDENT->value => [
                'loan_period_day' => 10,
                'can_borrow' => true
            ],
            BookLoanSettingType::LIBRARIAN->value => [
                'loan_period_day' => 15,
                'can_borrow' => true
            ],
            BookLoanSettingType::OTHERS->value => [
                'loan_period_day' => 20,
                'can_borrow' => false
            ],
        ]
    ];

    $response = $this->putJson(route("$this->routeNamePrefix.update", ['book' => $book->id]), $payload)->json();

    // Should fail with validation error
    expect($response)->toHaveFailedGeneralResponse()
        ->and($response['error'])->toHaveKey('condition.0', 'Cannot update book condition when book is borrowed.');

    // Verify the book condition remains unchanged
    $book->refresh();
    expect($book->condition)->toBe(BookCondition::GOOD);
});

test('update should allow condition change when book is not borrowed', function () {
    $book_classification = BookClassification::factory()->create();
    $book_sub_classification = BookSubClassification::factory()->create([
        'book_classification_id' => $book_classification->id,
    ]);
    $book_category = BookCategory::factory()->create();
    $book_source = BookSource::factory()->create();
    $authors = Author::factory()->create([
        'name' => 'David'
    ]);

    // Create a book with GOOD condition and AVAILABLE status
    $book = Book::factory()->create([
        'book_no' => '123460',
        'call_no' => fake()->sentence(),
        'book_category_id' => $book_category->id,
        'book_classification_id' => $book_classification->id,
        'book_sub_classification_id' => $book_sub_classification->id,
        'book_source_id' => $book_source->id,
        'title' => fake()->title,
        'series' => fake()->sentence(),
        'edition' => fake()->sentence(),
        'remark' => fake()->sentence(),
        'topic' => fake()->title(),
        'location_1' => fake()->sentence(),
        'location_2' => fake()->sentence(),
        'isbn' => fake()->sentence(),
        'binding' => BookBinding::HARD_COVER->value,
        'publisher' => fake()->sentence(),
        'publisher_place' => fake()->address(),
        'published_date' => fake()->date(),
        'book_page' => 100,
        'words' => 1000,
        'book_size' => '12 inch',
        'cdrom' => false,
        'condition' => BookCondition::GOOD,
        'status' => BookStatus::AVAILABLE, // Not borrowed
    ]);
    $book->authors()->sync($authors->pluck('id'));

    BookLoanSetting::factory(4)
        ->state(new Sequence(
            [
                'book_id' => $book->id,
                'type' => BookLoanSettingType::EMPLOYEE->value,
                'loan_period_day' => 5,
                'can_borrow' => true
            ],
            [
                'book_id' => $book->id,
                'type' => BookLoanSettingType::STUDENT->value,
                'loan_period_day' => 10,
                'can_borrow' => true
            ],
            [
                'book_id' => $book->id,
                'type' => BookLoanSettingType::LIBRARIAN->value,
                'loan_period_day' => 15,
                'can_borrow' => true
            ],
            [
                'book_id' => $book->id,
                'type' => BookLoanSettingType::OTHERS->value,
                'loan_period_day' => 20,
                'can_borrow' => false
            ],
        ))
        ->create();

    // Attempt to update the book with a different condition (DAMAGE) while it's available
    $payload = [
        'book_no' => $book->book_no,
        'call_no' => $book->call_no,
        'book_category_id' => $book->book_category_id,
        'book_classification_id' => $book->book_classification_id,
        'book_sub_classification_id' => $book->book_sub_classification_id,
        'book_source_id' => $book->book_source_id,
        'title' => $book->title,
        'series' => $book->series,
        'edition' => $book->edition,
        'remark' => $book->remark,
        'topic' => $book->topic,
        'location_1' => $book->location_1,
        'location_2' => $book->location_2,
        'isbn' => $book->isbn,
        'binding' => $book->binding->value,
        'publisher' => $book->publisher,
        'publisher_place' => $book->publisher_place,
        'published_date' => $book->published_date->format('Y-m-d'),
        'book_page' => $book->book_page,
        'words' => $book->words,
        'book_size' => $book->book_size,
        'cdrom' => $book->cdrom,
        'purchase_value' => 20,
        'lost_penalty_value' => 40,
        'condition' => BookCondition::DAMAGE->value, // Different condition from current (GOOD)
        'authors' => [
            'David'
        ],
        'loan_settings' => [
            BookLoanSettingType::EMPLOYEE->value => [
                'loan_period_day' => 5,
                'can_borrow' => true
            ],
            BookLoanSettingType::STUDENT->value => [
                'loan_period_day' => 10,
                'can_borrow' => true
            ],
            BookLoanSettingType::LIBRARIAN->value => [
                'loan_period_day' => 15,
                'can_borrow' => true
            ],
            BookLoanSettingType::OTHERS->value => [
                'loan_period_day' => 20,
                'can_borrow' => false
            ],
        ]
    ];

    $response = $this->putJson(route("$this->routeNamePrefix.update", ['book' => $book->id]), $payload)->json();

    // Should succeed
    expect($response)->toHaveSuccessGeneralResponse();

    // Verify the book condition was updated
    $book->refresh();
    expect($book->condition)->toBe(BookCondition::DAMAGE);
});

