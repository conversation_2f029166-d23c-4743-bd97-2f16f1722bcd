<?php

use App\Enums\ClassStream;
use App\Enums\ClassType;
use App\Enums\EnglishLevel;
use App\Http\Resources\GradeResource;
use App\Models\ClassModel;
use App\Models\ClassSubject;
use App\Models\Course;
use App\Models\Employee;
use App\Models\Grade;
use App\Models\SemesterClass;
use App\Models\SemesterSetting;
use App\Models\Student;
use App\Models\StudentClass;
use App\Models\Subject;
use App\Models\User;
use App\Services\ClassService;
use Database\Seeders\InternationalizationSeeder;
use Database\Seeders\PermissionSeeder;
use Illuminate\Database\Eloquent\Factories\Sequence;
use Illuminate\Pagination\LengthAwarePaginator;
use Laravel\Sanctum\Sanctum;
use Mockery\MockInterface;

beforeEach(function () {
    $this->seed([
        InternationalizationSeeder::class,
        PermissionSeeder::class
    ]);

    $this->user = User::factory()->create();
    $this->user->assignRole('Super Admin');

    Sanctum::actingAs($this->user);

    $this->base_url = 'classes.';
    $this->class_table_name = resolve(ClassModel::class)->getTable();
});

test('index without any params', function () {
    $first_class = ClassModel::factory()->create();
    $second_class = ClassModel::factory()->english()->create();

    $response = $this->getJson(route($this->base_url . 'index', ['order_by' => 'id']))->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])
        ->toHaveCount(2)
        ->toEqual([
            [
                'id' => $first_class->id,
                'name' => $first_class->name,
                'code' => $first_class->code,
                'stream' => $first_class->stream->value,
                'type' => $first_class->type->value,
                'english_level' => null,
                'grade' => resourceToArray(new GradeResource($first_class->grade)),
                'is_active' => $first_class->is_active,
                'translations' => $first_class->translations,
            ],
            [
                'id' => $second_class->id,
                'name' => $second_class->name,
                'code' => $second_class->code,
                'stream' => $first_class->stream->value,
                'type' => $second_class->type->value,
                'english_level' => $second_class->english_level->value,
                'grade' => resourceToArray(new GradeResource($second_class->grade)),
                'is_active' => $second_class->is_active,
                'translations' => $second_class->translations,
            ],
        ]);
});

test('index with params', function () {

    $first_class = ClassModel::factory()->create([
        'name->en' => 'Class 1',
        'code' => 'class-1'
    ]);

    $second_class = ClassModel::factory()->create([
        'name->en' => 'Class 2',
        'code' => 'class-2'
    ]);

    $first_semester_class = SemesterClass::factory()->create([
        'class_id' => $first_class->id
    ]);

    $payload = [
        'name' => [
            'en' => 'Class 1'
        ],
        'type' => 'PRIMARY',
        'code' => 'class-1',
        'grade_id' => $first_class->grade_id,
        'stream' => $first_class->stream->value,
        'semester_setting_id' => $first_semester_class->semester_setting_id
    ];
    // Filter by name = Class 1
    $response = $this->getJson(route($this->base_url . 'index', $payload));

    $this->mock(ClassService::class, function (MockInterface $mock) use ($payload, $first_class) {
        $mock->shouldReceive('getAllPaginatedClasses')->with($payload)->andReturn(new LengthAwarePaginator([$first_class], 1, 1));
    });

    $response->assertStatus(200);

    expect($response->json())->toHaveSuccessGeneralResponse()
        ->and($response->json()['data'])->toEqual([
            [
                'id' => $first_class->id,
                'name' => $first_class->name,
                'code' => $first_class->code,
                'type' => $first_class->type->value,
                'stream' => $first_class->stream->value,
                'english_level' => null,
                'grade' => resourceToArray(new GradeResource($first_class->grade)),
                'is_active' => $first_class->is_active,
                'translations' => $first_class->translations,
            ],
        ]);
});

test('index - getAll', function () {
    $first_class = ClassModel::factory()->create();
    $second_class = ClassModel::factory()->english()->create();

    $payload = [
        'order_by' => 'id',
        'per_page' => -1
    ];

    $response = $this->getJson(route($this->base_url . 'index', $payload))->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])
        ->toHaveCount(2)
        ->toEqual([
            [
                'id' => $first_class->id,
                'name' => $first_class->name,
                'code' => $first_class->code,
                'type' => $first_class->type->value,
                'english_level' => null,
                'grade' => resourceToArray(new GradeResource($first_class->grade)),
                'is_active' => $first_class->is_active,
                'translations' => $first_class->translations,
                'stream' => $first_class->stream->value,
            ],
            [
                'id' => $second_class->id,
                'name' => $second_class->name,
                'code' => $second_class->code,
                'type' => $second_class->type->value,
                'english_level' => $second_class->english_level->value,
                'grade' => resourceToArray(new GradeResource($second_class->grade)),
                'is_active' => $second_class->is_active,
                'translations' => $second_class->translations,
                'stream' => $second_class->stream->value,
            ],
        ]);
});

test('index determine getPaginated per_page is not -1', function () {
    $this->mock(ClassService::class, function (MockInterface $mock) {
        $class = ClassModel::factory()->create();

        $mock->shouldReceive('getAllPaginatedClasses')
            ->once()
            ->andReturn(new LengthAwarePaginator([$class], 1, 1));
    });

    $response = $this->getJson(route($this->base_url . 'index', [
        'per_page' => 10,
        'page' => 1,
    ]));

    $response->assertStatus(200);

    expect($response->json())->toHaveKey('pagination');
});


test('index determine getAll per_page is -1', function () {

    $this->mock(ClassService::class, function (MockInterface $mock) {
        $classes = ClassModel::factory(2)->create();

        $mock->shouldReceive('getAllClasses')->once()->andReturn($classes);
    });

    $response = $this->getJson(route($this->base_url . 'index', [
        'per_page' => -1,
        'page' => 1,
    ]));

    $response->assertStatus(200);

    expect($response->json())->not()->toHaveKey('pagination');
});


test('create success', function () {
    expect(ClassModel::count())->toBe(0);

    $grade = Grade::factory()->create();

    $payload = [
        'name' => [
            'en' => 'Class 1',
            'zh' => '初一',
        ],
        'type' => 'PRIMARY',
        'code' => 'class-1',
        'stream' => ClassStream::COMMERCE->value,
        'grade_id' => $grade->id,
        'is_active' => true,
    ];

    $response = $this->postJson(route($this->base_url . 'create'), $payload);

    $response->assertStatus(200);

    // Expect only one record is created
    expect(ClassModel::count())->toBe(1);

    $created_data = ClassModel::first();

    expect($response->json())->toHaveSuccessGeneralResponse()
        ->and($response->json()['data'])->toBe([
            'id' => $created_data->id,
            'name' => $payload['name']['en'],
            'code' => strtoupper($payload['code']),
            'stream' => $payload['stream'],
            'type' => $payload['type'],
            'english_level' => null,
            'grade' => resourceToArray(new GradeResource($grade)),
            'is_active' => $payload['is_active'],
            'translations' => ['name' => $payload['name']],
        ]);

    // create english class
    $payload = [
        'name' => [
            'en' => 'Class 2',
            'zh' => '初一',
        ],
        'type' => ClassType::ENGLISH->value,
        'stream' => ClassStream::COMMERCE->value,
        'code' => 'class-2',
        'english_level' => EnglishLevel::ADVANCED_1->value,
        'grade_id' => $grade->id,
        'is_active' => true,
    ];

    $response = $this->postJson(route($this->base_url . 'create'), $payload);

    $response->assertStatus(200);

    expect(ClassModel::count())->toBe(2)
        ->and($response->json())->toHaveSuccessGeneralResponse()
        ->and($response->json()['data'])->toMatchArray([
            'name' => $payload['name']['en'],
            'code' => strtoupper($payload['code']),
            'type' => $payload['type'],
            'stream' => $payload['stream'],
            'english_level' => $payload['english_level'],
            'grade' => resourceToArray(new GradeResource($grade)),
            'is_active' => $payload['is_active'],
            'translations' => ['name' => $payload['name']],
        ]);

});

test('create validation error: no data', function () {
    expect(ClassModel::count())->toBe(0);

    $response = $this->postJson(route($this->base_url . 'create'), []);

    $response->assertStatus(422);

    // Expect nothing is created and return validation error
    $this->assertDatabaseCount($this->class_table_name, 0);

    expect(ClassModel::count())->toBe(0)
        ->and($response->json())->toHaveFailedGeneralResponse()
        ->toMatchArray([
            'error' => [
                'name' => [
                    'The name field is required.'
                ],
                'code' => [
                    'The code field is required.'
                ],
                'type' => [
                    'The type field is required.'
                ],
                'stream' => [
                    'The stream field is required.'
                ],
                'is_active' => [
                    'The is active field is required.'
                ],
            ],
            'data' => null
        ]);

    // create english but dont provide level
    $grade = Grade::factory()->create();

    $payload = [
        'name' => [
            'en' => 'Class 2',
            'zh' => '初一',
        ],
        'type' => ClassType::ENGLISH->value,
        'stream' => ClassStream::COMMERCE->value,
        'code' => 'class-1',
        'english_level' => null,
        'grade_id' => $grade->id,
        'is_active' => true,
    ];

    $response = $this->postJson(route($this->base_url . 'create'), $payload);

    $this->assertDatabaseCount($this->class_table_name, 0);

    expect($response->json())->toHaveFailedGeneralResponse()
        ->toMatchArray([
            'error' => [
                'english_level' => [
                    'The english level field is required when type is ENGLISH.'
                ],
            ],
            'data' => null
        ]);
});

test('create validation error: unique', function () {
    $grade = Grade::factory()->create();

    ClassModel::factory()->create([
        'code' => 'CLASS-1'
    ]);

    $payload = [
        'name' => [
            'en' => 'Class 1',
            'zh' => '初一',
        ],
        'type' => 'PRIMARY',
        'stream' => ClassStream::COMMERCE->value,
        'code' => 'class-1',
        'grade_id' => $grade->id,
        'is_active' => true,
    ];

    $this->assertDatabaseCount($this->class_table_name, 1);

    $response = $this->postJson(route($this->base_url . 'create'), $payload);

    $response->assertStatus(422);

    // Expect nothing is created and return validation error
    $this->assertDatabaseCount($this->class_table_name, 1);

    expect($response->json())->toHaveFailedGeneralResponse()
        ->toMatchArray([
            'error' => [
                'code' => [
                    'The code has already been taken.'
                ]
            ],
            'data' => null
        ]);
});

test('update success', function () {
    $class = ClassModel::factory()->create();

    $grade = Grade::factory()->create();

    expect(ClassModel::count())->toBe(1);

    $payload = [
        'name' => [
            'en' => 'Class 1',
            'zh' => '初一',
        ],
        'stream' => ClassStream::SCIENCE->value,
        'code' => 'class-1',
        'type' => 'PRIMARY',
        'grade_id' => $grade->id,
        'is_active' => false,
    ];

    $response = $this->putJson(route($this->base_url . 'update', $class->id), $payload);

    $response->assertStatus(200);

    $this->assertDatabaseCount($this->class_table_name, 1);

    expect(ClassModel::count())->toBe(1)
        ->and($response->json())->toHaveSuccessGeneralResponse()
        ->and($response->json()['data'])
        ->toMatchArray([
            'id' => $class->id,
            'name' => $payload['name']['en'],
            'type' => $payload['type'],
            'stream' => $payload['stream'],
            'code' => strtoupper($payload['code']),
            'grade' => resourceToArray(new GradeResource($grade)),
            'is_active' => $payload['is_active'],
            'translations' => ['name' => $payload['name']],
        ]);

    $this->assertDatabaseHas($this->class_table_name, [
        'id' => $class->id,
        'name->en' => $payload['name']['en'],
        'name->zh' => $payload['name']['zh'],
        'type' => $payload['type'],
        'stream' => $payload['stream'],
        'code' => strtoupper($payload['code']),
        'grade_id' => $grade->id,
        'is_active' => $payload['is_active'],
    ]);


    // update class to ENGLISH
    $payload = [
        'name' => [
            'en' => 'Class 1',
            'zh' => '初一',
        ],
        'code' => 'class-1',
        'type' => ClassType::ENGLISH->value,
        'stream' => ClassStream::SCIENCE->value,
        'english_level' => EnglishLevel::ADVANCED_1->value,
        'grade_id' => $grade->id,
        'is_active' => true,
    ];

    $response = $this->putJson(route($this->base_url . 'update', $class->id), $payload);

    $response->assertStatus(200);

    $this->assertDatabaseCount($this->class_table_name, 1);

    expect(ClassModel::count())->toBe(1)
        ->and($response->json())->toHaveSuccessGeneralResponse()
        ->and($response->json()['data'])
        ->toMatchArray([
            'id' => $class->id,
            'name' => $payload['name']['en'],
            'type' => $payload['type'],
            'stream' => $payload['stream'],
            'english_level' => $payload['english_level'],
            'grade' => resourceToArray(new GradeResource($grade)),
            'is_active' => $payload['is_active'],
            'translations' => ['name' => $payload['name']],
        ]);

    $this->assertDatabaseHas($this->class_table_name, [
        'id' => $class->id,
        'name->en' => $payload['name']['en'],
        'name->zh' => $payload['name']['zh'],
        'type' => $payload['type'],
        'stream' => $payload['stream'],
        'english_level' => $payload['english_level'],
        'grade_id' => $grade->id,
        'is_active' => $payload['is_active'],
    ]);

});

test('update validation error', function () {
    $class = ClassModel::factory()->create();

    expect(ClassModel::count())->toBe(1);

    $response = $this->putJson(route($this->base_url . 'update', $class->id), []);

    $response->assertStatus(422);

    expect(ClassModel::count())->toBe(1)
        ->and($response->json())->toMatchArray([
            'error' => [
                'name' => [
                    'The name field is required.'
                ],
                'code' => [
                    'The code field is required.'
                ],
                'type' => [
                    'The type field is required.'
                ],
                'stream' => [
                    'The stream field is required.'
                ],
                'is_active' => [
                    'The is active field is required.'
                ],
            ],
            'data' => null,
        ]);

    $this->assertDatabaseHas($this->class_table_name, [
        'id' => $class->id,
        'name->en' => $class->getTranslation('name', 'en'),
        'name->zh' => $class->getTranslation('name', 'zh'),
        'code' => $class->code,
        'type' => $class->type,
        'stream' => $class->stream,
        'grade_id' => $class->grade_id,
        'is_active' => $class->is_active,
    ]);

    // update to english but dont provide level
    $grade = Grade::factory()->create();

    $payload = [
        'name' => [
            'en' => 'Class 2',
            'zh' => '初一',
        ],
        'code' => 'code-1',
        'type' => ClassType::ENGLISH->value,
        'stream' => ClassStream::COMMERCE->value,
        'english_level' => null,
        'grade_id' => $grade->id,
        'is_active' => true,
    ];

    $response = $this->putJson(route($this->base_url . 'update', $class->id), $payload);

    $this->assertDatabaseCount($this->class_table_name, 1);

    expect($response->json())->toHaveFailedGeneralResponse()
        ->toMatchArray([
            'error' => [
                'english_level' => [
                    'The english level field is required when type is ENGLISH.'
                ],
            ],
            'data' => null
        ]);
});

test('update validation error - unique', function () {
    $grade = Grade::factory()->create();
    $class = ClassModel::factory()->create([
        'code' => 'CLASS-1'
    ]);

    ClassModel::factory()->create([
        'code' => 'CLASS-2'
    ]);

    $payload = [
        'name' => [
            'en' => 'Class 1',
            'zh' => '初一',
        ],
        'code' => 'class-2',
        'type' => 'PRIMARY',
        'stream' => ClassStream::COMMERCE->value,
        'grade_id' => $grade->id,
        'is_active' => false,
    ];


    $response = $this->putJson(route($this->base_url . 'update', $class->id), $payload);

    $response->assertStatus(422);

    expect($response->json())->toMatchArray([
        'error' => [
            'code' => [
                'The code has already been taken.'
            ]
        ],
        'data' => null,
    ]);
});

test('show single record success', function () {
    $first_class = ClassModel::factory()->create();
    $second_class = ClassModel::factory()->english()->create();

    $response = $this->getJson(route($this->base_url . 'show', $first_class->id));

    $response->assertStatus(200);

    expect($response->json())->toHaveSuccessGeneralResponse()
        ->and($response->json()['data'])
        ->toBe([
            'id' => $first_class->id,
            'name' => $first_class->name,
            'code' => $first_class->code,
            'stream' => $first_class->stream->value,
            'type' => $first_class->type->value,
            'english_level' => null,
            'grade' => resourceToArray(new GradeResource($first_class->grade)),
            'is_active' => $first_class->is_active,
            'translations' => $first_class->translations,
        ]);

    $response = $this->getJson(route($this->base_url . 'show', $second_class->id));

    $response->assertStatus(200);

    expect($response->json())->toHaveSuccessGeneralResponse()
        ->and($response->json()['data'])
        ->toMatchArray([
            'id' => $second_class->id,
            'name' => $second_class->name,
            'stream' => $second_class->stream->value,
            'type' => $second_class->type->value,
            'code' => $second_class->code,
            'english_level' => $second_class->english_level->value,
            'grade' => resourceToArray(new GradeResource($second_class->grade)),
            'is_active' => $second_class->is_active,
            'translations' => $second_class->translations,
        ]);
});

test('show not existing record error', function () {
    expect(ClassModel::count())->toBe(0);

    $response = $this->getJson(route($this->base_url . 'show', ['class' => 1]));

    $response->assertStatus(404);

    expect($response->json())->toHaveModelResourceNotFoundResponse();
});

// Don't allow delete
//test('delete success', function () {
//    $first_class = ClassModel::factory()->create();
//    $other_classes = ClassModel::factory()->count(3)->create();
//
//    $this->assertDatabaseCount($this->class_table_name, 4);
//
//    //id not exist
//    $response = $this->deleteJson(route($this->base_url . 'destroy', ['class' => 9999]))->json();
//    expect($response)->toHaveModelResourceNotFoundResponse();
//
//    $this->assertDatabaseCount($this->class_table_name, 4);
//
//    //delete success
//    $response = $this->deleteJson(route($this->base_url . 'destroy', ['class' => $first_class->id]))->json();
//    expect($response)->toHaveSuccessGeneralResponse();
//
//    // Soft delete doesn't affect count
//    $this->assertDatabaseCount($this->class_table_name, 4);
//    expect($first_class->refresh()->deleted_at)->not->toBeNull();
//
//    foreach ($other_classes as $other) {
//        $this->assertDatabaseHas($this->class_table_name, [
//            'id' => $other->id,
//            'deleted_at' => null
//        ]);
//    }
//});

test('assign class to semester', function () {
    $course = Course::factory()->uec()->create();
    $semester_setting = SemesterSetting::factory()->create([
        'course_id' => $course->id,
    ]);
    $first_class = ClassModel::factory()->create();
    $first_homeroom_teacher = Employee::factory()->create();

    $second_class = ClassModel::factory()->create();
    $second_homeroom_teacher = Employee::factory()->create();

    expect(SemesterClass::count())->toBe(0);

    $payload = [
        'semester_setting_id' => $semester_setting->id,
        'classes' => [
            [
                'id' => $first_class->id,
                'homeroom_teacher_id' => $first_homeroom_teacher->id,
                'is_active' => true,
            ],
            [
                'id' => $second_class->id,
                'homeroom_teacher_id' => $second_homeroom_teacher->id,
                'is_active' => true,
            ]
        ]
    ];

    $response = $this->putJson(route($this->base_url . 'assign-class-to-semester', $payload));

    $response->assertStatus(200);

    expect($response->json())->toHaveSuccessGeneralResponse()
        ->and(SemesterClass::count())->toBe(2);

    foreach ($payload['classes'] as $class) {
        $this->assertDatabaseHas('semester_classes', [
            'semester_setting_id' => $payload['semester_setting_id'],
            'class_id' => $class['id'],
            'homeroom_teacher_id' => $class['homeroom_teacher_id'],
            'is_active' => $class['is_active'],
        ]);
    }

    // Try to assign again, should just change the homeroom_teacher_id and is_active
    $payload = [
        'semester_setting_id' => $semester_setting->id,
        'classes' => [
            [
                'id' => $first_class->id,
                'homeroom_teacher_id' => $second_homeroom_teacher->id,
                'is_active' => false,
            ],
            [
                'id' => $second_class->id,
                'homeroom_teacher_id' => $first_homeroom_teacher->id,
                'is_active' => false,
            ]
        ]
    ];

    $response = $this->putJson(route($this->base_url . 'assign-class-to-semester', $payload));
    $response->assertStatus(200);

    expect($response->json())->toHaveSuccessGeneralResponse()
        ->and(SemesterClass::count())->toBe(2);

    foreach ($payload['classes'] as $class) {
        $this->assertDatabaseHas('semester_classes', [
            'semester_setting_id' => $payload['semester_setting_id'],
            'class_id' => $class['id'],
            'homeroom_teacher_id' => $class['homeroom_teacher_id'],
            'is_active' => $class['is_active'],
        ]);
    }
});

test('assign class to student: new assign', function () {
    $course = Course::factory()->uec()->create();
    $semester_setting = SemesterSetting::factory()->create([
        'course_id' => $course->id,
    ]);
    $first_class = ClassModel::factory()->create([
        'type' => 'PRIMARY',
    ]);
    $first_homeroom_teacher = Employee::factory()->create();

    $second_class = ClassModel::factory()->create([
        'type' => 'PRIMARY',
    ]);
    $second_homeroom_teacher = Employee::factory()->create();

    $first_semester_class = SemesterClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'class_id' => $first_class->id,
        'homeroom_teacher_id' => $first_homeroom_teacher->id,
        'is_active' => true,
    ]);

    $second_semester_class = SemesterClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'class_id' => $second_class->id,
        'homeroom_teacher_id' => $second_homeroom_teacher->id,
        'is_active' => true,
    ]);

    $first_student = Student::factory()->create();
    $second_student = Student::factory()->create();

    expect(StudentClass::count())->toBe(0);

    $payload = [
        'semester_setting_id' => $semester_setting->id,
        'semester_class_id' => $first_semester_class->id,
        'students' => [
            [
                'id' => $first_student->id,
//                'seat_no' => 1,
                'class_enter_date' => '2024-07-01',
                'is_active' => true,
            ],
            [
                'id' => $second_student->id,
//                'seat_no' => 2,
                'class_enter_date' => '2024-07-01',
                'is_active' => true,
            ]
        ]
    ];

    $response = $this->putJson(route($this->base_url . 'assign-class-to-student', $payload));
    expect($response->json())->toHaveSuccessGeneralResponse();

    $this->assertDatabaseCount('student_classes', 2);
    foreach ($payload['students'] as $student) {
        $this->assertDatabaseHas('student_classes', [
            'semester_setting_id' => $payload['semester_setting_id'],
            'semester_class_id' => $payload['semester_class_id'],
            'student_id' => $student['id'],
            'seat_no' => null, //$student['seat_no'],
            'class_enter_date' => $student['class_enter_date'],
            'is_active' => $student['is_active'],
            'is_latest_class_in_semester' => true
        ]);
    }
});

test('assign class to student: existing assign (different class_enter_date)', function () {
    $course = Course::factory()->uec()->create();
    $semester_setting = SemesterSetting::factory()->create([
        'course_id' => $course->id,
    ]);
    $first_class = ClassModel::factory()->create([
        'type' => 'PRIMARY',
    ]);
    $first_homeroom_teacher = Employee::factory()->create();

    $second_class = ClassModel::factory()->create([
        'type' => 'PRIMARY',
    ]);
    $second_homeroom_teacher = Employee::factory()->create();

    $first_semester_class = SemesterClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'class_id' => $first_class->id,
        'homeroom_teacher_id' => $first_homeroom_teacher->id,
        'is_active' => true,
    ]);

    $second_semester_class = SemesterClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'class_id' => $second_class->id,
        'homeroom_teacher_id' => $second_homeroom_teacher->id,
        'is_active' => true,
    ]);

    $first_student = Student::factory()->create();
    $second_student = Student::factory()->create();

    expect(StudentClass::count())->toBe(0);

    $payload = [
        'semester_setting_id' => $semester_setting->id,
        'semester_class_id' => $first_semester_class->id,
        'students' => [
            [
                'id' => $first_student->id,
//                'seat_no' => 1,
                'class_enter_date' => '2024-07-01',
                'is_active' => true,
            ],
            [
                'id' => $second_student->id,
//                'seat_no' => 2,
                'class_enter_date' => '2024-07-01',
                'is_active' => true,
            ]
        ]
    ];

    $response = $this->putJson(route($this->base_url . 'assign-class-to-student', $payload));
    expect($response->json())->toHaveSuccessGeneralResponse();

    $this->assertDatabaseCount('student_classes', 2);
    foreach ($payload['students'] as $student) {
        $this->assertDatabaseHas('student_classes', [
            'semester_setting_id' => $payload['semester_setting_id'],
            'semester_class_id' => $payload['semester_class_id'],
            'student_id' => $student['id'],
            'seat_no' => null, //$student['seat_no'],
            'class_enter_date' => $student['class_enter_date'],
            'is_active' => $student['is_active'],
            'is_latest_class_in_semester' => true
        ]);
    }

    // Test if change class_enter_date of same class, old record will be is_active = false and new record will be created
    $payload = [
        'semester_setting_id' => $semester_setting->id,
        'semester_class_id' => $first_semester_class->id,
        'students' => [
            [
                'id' => $first_student->id,
//                'seat_no' => 1,
                'class_enter_date' => '2024-07-02',
                'is_active' => true,
            ],
            [
                'id' => $second_student->id,
//                'seat_no' => 2,
                'class_enter_date' => '2024-07-01',
                'is_active' => true,
            ]
        ]
    ];

    $response = $this->putJson(route($this->base_url . 'assign-class-to-student', $payload));
    expect($response->json())->toHaveSuccessGeneralResponse();

    $this->assertDatabaseCount('student_classes', 3);
    foreach ($payload['students'] as $student) {
        $this->assertDatabaseHas('student_classes', [
            'semester_setting_id' => $payload['semester_setting_id'],
            'semester_class_id' => $payload['semester_class_id'],
            'student_id' => $student['id'],
            'seat_no' => null, //$student['seat_no'],
            'class_enter_date' => $student['class_enter_date'],
            'is_active' => $student['is_active'],
            'is_latest_class_in_semester' => true
        ]);
    }

    $this->assertDatabaseHas('student_classes', [
        'semester_setting_id' => $payload['semester_setting_id'],
        'semester_class_id' => $payload['semester_class_id'],
        'student_id' => $first_student->id,
        'seat_no' => null, //1,
        'class_enter_date' => '2024-07-01',
        'is_active' => false,
        'is_latest_class_in_semester' => false
    ]);
});

test('assign class to student: existing assign (same class_enter_date, different seat_no and is_active)', function () {
    $course = Course::factory()->uec()->create();
    $semester_setting = SemesterSetting::factory()->create([
        'course_id' => $course->id,
    ]);
    $first_class = ClassModel::factory()->create([
        'type' => 'PRIMARY',
    ]);
    $first_homeroom_teacher = Employee::factory()->create();

    $second_class = ClassModel::factory()->create([
        'type' => 'PRIMARY',
    ]);
    $second_homeroom_teacher = Employee::factory()->create();

    $first_semester_class = SemesterClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'class_id' => $first_class->id,
        'homeroom_teacher_id' => $first_homeroom_teacher->id,
        'is_active' => true,
    ]);

    $second_semester_class = SemesterClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'class_id' => $second_class->id,
        'homeroom_teacher_id' => $second_homeroom_teacher->id,
        'is_active' => true,
    ]);

    $first_student = Student::factory()->create();
    $second_student = Student::factory()->create();

    expect(StudentClass::count())->toBe(0);

    $payload = [
        'semester_setting_id' => $semester_setting->id,
        'semester_class_id' => $first_semester_class->id,
        'students' => [
            [
                'id' => $first_student->id,
//                'seat_no' => 1,
                'class_enter_date' => '2024-07-01',
                'is_active' => true,
            ],
            [
                'id' => $second_student->id,
//                'seat_no' => 2,
                'class_enter_date' => '2024-07-01',
                'is_active' => true,
            ]
        ]
    ];

    $response = $this->putJson(route($this->base_url . 'assign-class-to-student', $payload));
    expect($response->json())->toHaveSuccessGeneralResponse();

    $this->assertDatabaseCount('student_classes', 2);
    foreach ($payload['students'] as $student) {
        $this->assertDatabaseHas('student_classes', [
            'semester_setting_id' => $payload['semester_setting_id'],
            'semester_class_id' => $payload['semester_class_id'],
            'student_id' => $student['id'],
            'seat_no' => null, //$student['seat_no'],
            'class_enter_date' => $student['class_enter_date'],
            'is_active' => $student['is_active'],
            'is_latest_class_in_semester' => true
        ]);
    }

    // Test if change seat_no and is_active of same class, will just update old record instead creating new record
    $payload = [
        'semester_setting_id' => $semester_setting->id,
        'semester_class_id' => $first_semester_class->id,
        'students' => [
            [
                'id' => $first_student->id,
//                'seat_no' => 4,
                'class_enter_date' => '2024-07-01',
            ],
            [
                'id' => $second_student->id,
//                'seat_no' => 5,
                'class_enter_date' => '2024-07-01',
            ]
        ]
    ];

    $response = $this->putJson(route($this->base_url . 'assign-class-to-student', $payload));
    expect($response->json())->toHaveSuccessGeneralResponse();

    $this->assertDatabaseCount('student_classes', 2);
    foreach ($payload['students'] as $student) {
        $this->assertDatabaseHas('student_classes', [
            'semester_setting_id' => $payload['semester_setting_id'],
            'semester_class_id' => $payload['semester_class_id'],
            'student_id' => $student['id'],
            'seat_no' => null, // $student['seat_no'],
            'class_enter_date' => $student['class_enter_date'],
            'is_active' => true,
            'is_latest_class_in_semester' => true
        ]);
    }
});

test('assign class to student: new assignment and also link students to 2 class_subjects', function () {
    $semester_setting = SemesterSetting::factory()->create();
    $class = ClassModel::factory()->create();
    $homeroom_teacher = Employee::factory()->create();

    $semester_class = SemesterClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'class_id' => $class->id,
        'homeroom_teacher_id' => $homeroom_teacher->id,
        'is_active' => true,
    ]);

    $student_1 = Student::factory()->create();
    $student_2 = Student::factory()->create();

    $subject_1 = Subject::factory()->create();
    $subject_2 = Subject::factory()->create();

    // Link 2 subject to $semester_class
    $class_subject_1 = ClassSubject::create(['semester_class_id' => $semester_class->id, 'subject_id' => $subject_1->id]);
    $class_subject_2 = ClassSubject::create(['semester_class_id' => $semester_class->id, 'subject_id' => $subject_2->id]);

    $payload = [
        'semester_setting_id' => $semester_setting->id,
        'semester_class_id' => $semester_class->id,
        'class_subject_ids' => [$class_subject_1->id, $class_subject_2->id], // populate students under $semester_class into this class_subject
        'students' => [
            [
                'id' => $student_1->id,
//                'seat_no' => 1,
                'class_enter_date' => '2024-07-01',
                'is_active' => true,
            ],
            [
                'id' => $student_2->id,
//                'seat_no' => 2,
                'class_enter_date' => '2024-07-01',
                'is_active' => true,
            ]
        ]
    ];

    $response = $this->putJson(route($this->base_url . 'assign-class-to-student', $payload));

    expect($response->json())->toHaveSuccessGeneralResponse();

    $this->assertDatabaseCount('student_classes', 2);

    foreach ($payload['students'] as $student) {
        $this->assertDatabaseHas('student_classes', [
            'semester_setting_id' => $payload['semester_setting_id'],
            'semester_class_id' => $payload['semester_class_id'],
            'student_id' => $student['id'],
            'seat_no' => null, //$student['seat_no'],
            'class_enter_date' => $student['class_enter_date'],
            'is_active' => $student['is_active'],
            'is_latest_class_in_semester' => true
        ]);
    }

    $this->assertDatabaseCount('class_subject_student', 4); // 2 students under each class_subject

    // assert both student is also under each $class_subject
    foreach ($payload['students'] as $student) {
        $this->assertDatabaseHas('class_subject_student', [
            'class_subject_id' => $class_subject_1->id,
            'student_id' => $student['id'],
        ]);

        $this->assertDatabaseHas('class_subject_student', [
            'class_subject_id' => $class_subject_2->id,
            'student_id' => $student['id'],
        ]);
    }
});

test('assign class to student: assign first then remove student from class', function () {
    $semester_setting = SemesterSetting::factory()->create();
    $class = ClassModel::factory()->create();
    $homeroom_teacher = Employee::factory()->create();

    $semester_class = SemesterClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'class_id' => $class->id,
        'homeroom_teacher_id' => $homeroom_teacher->id,
        'is_active' => true,
    ]);

    $student_1 = Student::factory()->create();
    $student_2 = Student::factory()->create();

    $this->assertDatabaseCount('student_classes', 0);


    /** ASSIGN 2 STUDENTS TO THE SEMESTER_CLASS */
    $payload = [
        'semester_setting_id' => $semester_setting->id,
        'semester_class_id' => $semester_class->id,
        'class_subject_ids' => [], // empty
        'students' => [
            [
                'id' => $student_1->id,
//                'seat_no' => 1,
                'class_enter_date' => '2024-07-01',
                'is_active' => true,
            ],
            [
                'id' => $student_2->id,
//                'seat_no' => 2,
                'class_enter_date' => '2024-07-01',
                'is_active' => true,
            ]
        ]
    ];

    $response = $this->putJson(route($this->base_url . 'assign-class-to-student', $payload));

    expect($response->json())->toHaveSuccessGeneralResponse();

    $this->assertDatabaseCount('student_classes', 2);

    foreach ($payload['students'] as $student) {
        $this->assertDatabaseHas('student_classes', [
            'semester_setting_id' => $payload['semester_setting_id'],
            'semester_class_id' => $payload['semester_class_id'],
            'student_id' => $student['id'],
            'seat_no' => null, //$student['seat_no'],
            'class_enter_date' => $student['class_enter_date'],
            'is_active' => $student['is_active'],
            'is_latest_class_in_semester' => true
        ]);
    }


    /** REMOVE 1 STUDENT FROM THE SEMESTER_CLASS -> expect 1 student_class is_active = false */
    $payload = [
        'semester_setting_id' => $semester_setting->id,
        'semester_class_id' => $semester_class->id,
        'class_subject_ids' => [], // empty
        'students' => [
            [
                'id' => $student_1->id,
//                'seat_no' => 1,
                'class_enter_date' => '2024-07-01',
                'is_active' => true,
            ],
        ]
    ];

    $response = $this->putJson(route($this->base_url . 'assign-class-to-student', $payload));

    expect($response->json())->toHaveSuccessGeneralResponse();

    $this->assertDatabaseCount('student_classes', 2);

    $this->assertDatabaseHas('student_classes', [
        'semester_setting_id' => $payload['semester_setting_id'],
        'semester_class_id' => $payload['semester_class_id'],
        'student_id' => $payload['students'][0]['id'],
        'seat_no' => null, //$payload['students'][0]['seat_no'],
        'class_enter_date' => $payload['students'][0]['class_enter_date'],
        'is_active' => $payload['students'][0]['is_active'],
        'is_latest_class_in_semester' => true
    ]);

    // assert student 2 to be is_active = false
    $this->assertDatabaseHas('student_classes', [
        'semester_setting_id' => $payload['semester_setting_id'],
        'semester_class_id' => $payload['semester_class_id'],
        'student_id' => $student_2->id,
        'seat_no' => null, //2,
        'class_enter_date' => '2024-07-01',
        'class_leave_date' => now()->toDateString(),
        'is_active' => false,
        'is_latest_class_in_semester' => false
    ]);


    /** REASSIGN STUDENT $student_2->id TO THE SEMESTER_CLASS -> expect 3 student_class, for $student_2->id : 1 is_active = true, 1 is_active = false  */
    $payload = [
        'semester_setting_id' => $semester_setting->id,
        'semester_class_id' => $semester_class->id,
        'class_subject_ids' => [], // empty
        'students' => [
            [
                'id' => $student_1->id,
//                'seat_no' => 1,
                'class_enter_date' => '2024-07-01',
                'is_active' => true,
            ],
            [
                'id' => $student_2->id, // re-add $student_2 to this semester_class->id
//                'seat_no' => 2,
                'class_enter_date' => '2024-07-02',
                'is_active' => true,
            ]
        ]
    ];

    $response = $this->putJson(route($this->base_url . 'assign-class-to-student', $payload));

    expect($response->json())->toHaveSuccessGeneralResponse();


    $this->assertDatabaseCount('student_classes', 3);

    $this->assertDatabaseHas('student_classes', [
        'semester_setting_id' => $payload['semester_setting_id'],
        'semester_class_id' => $payload['semester_class_id'],
        'student_id' => $payload['students'][0]['id'],
        'seat_no' => null, //$payload['students'][0]['seat_no'],
        'class_enter_date' => $payload['students'][0]['class_enter_date'],
        'is_active' => $payload['students'][0]['is_active'],
        'is_latest_class_in_semester' => true
    ]);

    // assert student 2 previous record to be is_active = false
    $this->assertDatabaseHas('student_classes', [
        'semester_setting_id' => $payload['semester_setting_id'],
        'semester_class_id' => $payload['semester_class_id'],
        'student_id' => $student_2->id,
        'seat_no' => null, //2,
        'class_enter_date' => '2024-07-01',
        'is_active' => false,
        'is_latest_class_in_semester' => false
    ]);

    // assert student 2 new record to be is_active = true
    $this->assertDatabaseHas('student_classes', [
        'semester_setting_id' => $payload['semester_setting_id'],
        'semester_class_id' => $payload['semester_class_id'],
        'student_id' => $student_2->id,
        'seat_no' => null, //$payload['students'][1]['seat_no'],
        'class_enter_date' => $payload['students'][1]['class_enter_date'],
        'is_active' => true,
        'is_latest_class_in_semester' => true
    ]);
});

test('assign multiple semesters to class success', function () {
    $class = ClassModel::factory()->create();

    $semester_setting_1 = SemesterSetting::factory()->create();
    $semester_setting_2 = SemesterSetting::factory()->create();
    $semester_setting_3 = SemesterSetting::factory()->create();

    $homeroom_teacher_1 = Employee::factory()->create();
    $homeroom_teacher_2 = Employee::factory()->create();
    $homeroom_teacher_3 = Employee::factory()->create();

    // initially assign only one semester_class and homeroom_teacher_id null
    $payload = [
        'class_id' => $class->id,
        'semester_settings' => [
            [
                'id' => $semester_setting_1->id,
                'homeroom_teacher_id' => null,
                'is_active' => true,
            ],
        ],
    ];

    $response = $this->putJson(route($this->base_url . 'assign-semester-to-class', $payload));

    expect($response->json())->toHaveSuccessGeneralResponse();

    $this->assertDatabaseCount('semester_classes', 1);

    $this->assertDatabaseHas('semester_classes', [
        'semester_setting_id' => $payload['semester_settings'][0]['id'],
        'class_id' => $payload['class_id'],
        'homeroom_teacher_id' => $payload['semester_settings'][0]['homeroom_teacher_id'],
        'is_active' => $payload['semester_settings'][0]['is_active'],
    ]);

    // assign 2 more semester_class and update old homeroom_teacher_id to be not null
    $payload = [
        'class_id' => $class->id,
        'semester_settings' => [
            [
                'id' => $semester_setting_1->id,
                'homeroom_teacher_id' => $homeroom_teacher_1->id, // add teacher now
                'is_active' => true,
            ],
            [
                'id' => $semester_setting_2->id,
                'homeroom_teacher_id' => $homeroom_teacher_2->id,
                'is_active' => false, // non active semester
            ],
            [
                'id' => $semester_setting_3->id,
                'homeroom_teacher_id' => $homeroom_teacher_3->id,
                'is_active' => true,
            ],
        ],
    ];

    $response = $this->putJson(route($this->base_url . 'assign-semester-to-class', $payload));

    expect($response->json())->toHaveSuccessGeneralResponse();

    $this->assertDatabaseCount('semester_classes', 3);

    foreach ($payload['semester_settings'] as $semester_setting_payload) {
        $this->assertDatabaseHas('semester_classes', [
            'semester_setting_id' => $semester_setting_payload['id'],
            'class_id' => $payload['class_id'],
            'homeroom_teacher_id' => $semester_setting_payload['homeroom_teacher_id'],
            'is_active' => $semester_setting_payload['is_active'],
        ]);
    }

    // semester_class can only be removed from destroy API, even if dont provide here, still no effect to existing class->semesterClasses
    $payload = [
        'class_id' => $class->id,
        'semester_settings' => [
            [
                'id' => $semester_setting_1->id,
                'homeroom_teacher_id' => $homeroom_teacher_1->id, // add teacher now
                'is_active' => true,
            ],
        ],
    ];

    $response = $this->putJson(route($this->base_url . 'assign-semester-to-class', $payload));

    expect($response->json())->toHaveSuccessGeneralResponse();

    $this->assertDatabaseCount('semester_classes', 3);

    $this->assertDatabaseHas('semester_classes', [ // this will be updated from payload
        'semester_setting_id' => $semester_setting_1->id,
        'class_id' => $payload['class_id'],
        'homeroom_teacher_id' => $homeroom_teacher_1->id,
        'is_active' => true,
    ]);

    // Make sure the other 2 semester_class is still remained same as previous API call
    $this->assertDatabaseHas('semester_classes', [
        'semester_setting_id' => $semester_setting_2->id,
        'homeroom_teacher_id' => $homeroom_teacher_2->id,
        'is_active' => false, // non active semester
    ]);

    $this->assertDatabaseHas('semester_classes', [
        'semester_setting_id' => $semester_setting_3->id,
        'homeroom_teacher_id' => $homeroom_teacher_3->id,
        'is_active' => true,
    ]);
});

test('assign multiple semesters to class failed : validation error', function () {
    $class = ClassModel::factory()->create();
    $semester_setting_1 = SemesterSetting::factory()->create();

    // invalid class_id
    $payload = [
        'class_id' => 11122,
        'semester_settings' => [
            [
                'id' => $semester_setting_1->id,
                'homeroom_teacher_id' => null,
                'is_active' => true,
            ],
        ],
    ];

    $response = $this->putJson(route($this->base_url . 'assign-semester-to-class', $payload));

    expect($response->json())->toHaveFailedGeneralResponse()->toMatchArray([
        'error' => [
            'class_id' => [
                'The selected class id is invalid.'
            ],
        ],
        'data' => null,
    ]);

    // invalid semester_setting_id
    $payload = [
        'class_id' => $class->id,
        'semester_settings' => [
            [
                'id' => 11122,
                'homeroom_teacher_id' => null,
                'is_active' => true,
            ],
        ],
    ];

    $response = $this->putJson(route($this->base_url . 'assign-semester-to-class', $payload));

    expect($response->json())->toHaveFailedGeneralResponse()->toMatchArray([
        'error' => [
            'semester_settings.0.id' => [
                'The selected semester_settings.0.id is invalid.'
            ],
        ],
        'data' => null,
    ]);

    // invalid homeroom_teacher_id
    $payload = [
        'class_id' => $class->id,
        'semester_settings' => [
            [
                'id' => $semester_setting_1->id,
                'homeroom_teacher_id' => 11122,
                'is_active' => true,
            ],
        ],
    ];

    $response = $this->putJson(route($this->base_url . 'assign-semester-to-class', $payload));

    expect($response->json())->toHaveFailedGeneralResponse()->toMatchArray([
        'error' => [
            'semester_settings.0.homeroom_teacher_id' => [
                'The selected semester_settings.0.homeroom_teacher_id is invalid.'
            ],
        ],
        'data' => null,
    ]);
});


test('getStudentsBySemesterClass', function () {
    /**
     * fail
     */

    $response = $this->getJson(route($this->base_url . 'seat-assignment.get-students', 1))->json();

    expect($response)->toHaveFailedGeneralResponse()->toMatchArray([
        'error' => 'Resource not found',
        'data' => null,
    ]);

    /**
     * success
     */

    $students = Student::factory(4)->state(new Sequence(
        [
            'name->en' => 'Jon',
        ],
        [
            'name->en' => 'Jack',
        ],
        [
            'name->en' => 'Koko',
        ],
        [
            'name->en' => 'Charlie',
        ],
    ))->create();

    $semester_setting = SemesterSetting::factory()->create([
        'name' => '2024 Semester 1',
        'is_current_semester' => true,
    ]);

    $semester_classes = SemesterClass::factory(2)->state(new Sequence(
        [
            'semester_setting_id' => $semester_setting->id,
            'is_active' => true,
        ],
        [
            'semester_setting_id' => $semester_setting->id,
            'is_active' => true,
        ],
    ))->create();

    /**
     *
     * no students for semester_class[0] and semester_class[1]
     *
     */

    // semester_classes[0]
    $response = $this->getJson(route($this->base_url . 'seat-assignment.get-students', $semester_classes[0]->id))->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toHaveCount(0);


    // semester_classes[1]
    $response = $this->getJson(route($this->base_url . 'seat-assignment.get-students', $semester_classes[1]->id))->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toHaveCount(0);


    /**
     *
     * Jon active in $semester_classes[0] , seat_no = 1
     * Jack active in $semester_classes[0], seat_no = null
     * Koko inactive in $semester_classes[0], seat_no = 2   -- still assigned seat
     * Charlie inactive in $semester_classes[0], seat_no = null     -- no seat
     *
     *
     * Koko active in $semester_classes[1], seat_no = 1
     * Charlie active in $semester_classes[1], seat_no = 2
     * Jon inactive in $semester_classes[1], seat_no = null         -- no seat
     *
     */

    $student_classes = StudentClass::factory(7)->state(new Sequence(
    // semester class 1
        [
            'semester_setting_id' => $semester_setting->id,
            'semester_class_id' => $semester_classes[0]->id,
            'class_type' => ClassType::PRIMARY,
            'student_id' => $students[0]->id,
            'seat_no' => 1,
            'is_active' => true, // Jon
        ],
        [
            'semester_setting_id' => $semester_setting->id,
            'semester_class_id' => $semester_classes[0]->id,
            'class_type' => ClassType::PRIMARY,
            'student_id' => $students[1]->id,
            'seat_no' => null,
            'is_active' => true, // Jack seat_no = null
        ],
        [
            'semester_setting_id' => $semester_setting->id,
            'semester_class_id' => $semester_classes[0]->id,
            'class_type' => ClassType::PRIMARY,
            'student_id' => $students[2]->id,
            'seat_no' => 2,
            'is_active' => false, // Koko inactive but still assigned seat, expect to be included
        ],
        [
            'semester_setting_id' => $semester_setting->id,
            'semester_class_id' => $semester_classes[0]->id,
            'class_type' => ClassType::PRIMARY,
            'student_id' => $students[3]->id,
            'seat_no' => null,
            'is_active' => false, // Charlie inactive but seat_no = null, expect to be excluded
        ],


        // semester class 2
        [
            'semester_setting_id' => $semester_setting->id,
            'semester_class_id' => $semester_classes[1]->id,
            'class_type' => ClassType::PRIMARY,
            'student_id' => $students[2]->id,
            'seat_no' => 1,
            'is_active' => true, // Koko
        ],
        [
            'semester_setting_id' => $semester_setting->id,
            'semester_class_id' => $semester_classes[1]->id,
            'class_type' => ClassType::PRIMARY,
            'student_id' => $students[3]->id,
            'seat_no' => null,
            'is_active' => true, // Charlie
        ],
        [
            'semester_setting_id' => $semester_setting->id,
            'semester_class_id' => $semester_classes[1]->id,
            'class_type' => ClassType::PRIMARY,
            'student_id' => $students[0]->id,
            'seat_no' => null,
            'is_active' => false, // Jon old record but no seat - expected to be excluded
        ],
    ))->create();

    // semester_classes[0]
    $response = $this->getJson(route($this->base_url . 'seat-assignment.get-students', $semester_classes[0]->id))->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toHaveCount(3)
        ->toHaveKey('0.student_class_id', $student_classes[0]->id)
        ->toHaveKey('1.student_class_id', $student_classes[2]->id)
        ->toHaveKey('2.student_class_id', $student_classes[1]->id)
        ->and($response['data'][0])->toEqual([
            'student_class_id' => $student_classes[0]->id,
            'student_id' => $students[0]->id,
            'student_number' => $students[0]->student_number,
            'student_name' => $students[0]->getTranslations('name'),
            'seat_no' => $student_classes[0]->seat_no,
            'is_active' => $student_classes[0]->is_active,
        ]);

    // semester_classes[1]
    $response = $this->getJson(route($this->base_url . 'seat-assignment.get-students', $semester_classes[1]->id))->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toHaveCount(2)
        ->toHaveKey('0.student_class_id', $student_classes[4]->id)
        ->toHaveKey('1.student_class_id', $student_classes[5]->id)
        ->and($response['data'][0])->toEqual([
            'student_class_id' => $student_classes[4]->id,
            'student_id' => $students[2]->id,
            'student_number' => $students[2]->student_number,
            'student_name' => $students[2]->getTranslations('name'),
            'seat_no' => $student_classes[4]->seat_no,
            'is_active' => $student_classes[4]->is_active,
        ]);
});


test('assignSeatsToStudents failed because validation error', function () {
    /**
     * failed because student_class_id repeated
     */

    $semester_class = SemesterClass::factory()->create();

    $payload = [
        'seats' => [
            [
                'student_class_id' => 1,
                'seat_no' => 1,
            ],
            [
                'student_class_id' => 1,
                'seat_no' => 2,
            ],
        ],
    ];

    $response = $this->putJson(route($this->base_url . 'seat-assignment.assign-seats', ['semester_class' => $semester_class->id]), $payload)->json();

    expect($response)->toHaveFailedGeneralResponse()
        ->toMatchArray([
            'error' => [
                'seats.0.student_class_id' => [
                    'The student field has a duplicate value.',
                ],
                'seats.1.student_class_id' => [
                    'The student field has a duplicate value.',
                ],
            ],
            'data' => null
        ]);

    /**
     * failed because seat_no repeated
     */

    $semester_class = SemesterClass::factory()->create();

    $payload = [
        'seats' => [
            [
                'student_class_id' => 1,
                'seat_no' => 100,
            ],
            [
                'student_class_id' => 2,
                'seat_no' => 100,
            ],
        ],
    ];

    $response = $this->putJson(route($this->base_url . 'seat-assignment.assign-seats', ['semester_class' => $semester_class->id]), $payload)->json();

    expect($response)->toHaveFailedGeneralResponse()
        ->toMatchArray([
            'error' => [
                'seats.0.seat_no' => [
                    'The seat number field has a duplicate value.',
                ],
                'seats.1.seat_no' => [
                    'The seat number field has a duplicate value.',
                ],
            ],
            'data' => null
        ]);
});

test('assignSeatsToStudents success', function () {
    $students = Student::factory(4)->state(new Sequence(
        [
            'name->en' => 'Jon',
        ],
        [
            'name->en' => 'Jack',
        ],
        [
            'name->en' => 'Koko',
        ],
        [
            'name->en' => 'Charlie',
        ],
    ))->create();

    $semester_setting = SemesterSetting::factory()->create([
        'name' => '2024 Semester 1',
        'is_current_semester' => true,
    ]);

    $semester_classes = SemesterClass::factory(2)->state(new Sequence(
        [
            'semester_setting_id' => $semester_setting->id,
            'is_active' => true,
        ],
        [
            'semester_setting_id' => $semester_setting->id,
            'is_active' => true,
        ],
    ))->create();


    $student_classes = StudentClass::factory(7)->state(new Sequence(
    // semester class 1
        [
            'semester_setting_id' => $semester_setting->id,
            'semester_class_id' => $semester_classes[0]->id,
            'class_type' => ClassType::PRIMARY,
            'student_id' => $students[0]->id,
            'seat_no' => 1,
            'is_active' => true, // Jon
        ],
        [
            'semester_setting_id' => $semester_setting->id,
            'semester_class_id' => $semester_classes[0]->id,
            'class_type' => ClassType::PRIMARY,
            'student_id' => $students[1]->id,
            'seat_no' => null,
            'is_active' => true, // Jack seat_no = null
        ],
        [
            'semester_setting_id' => $semester_setting->id,
            'semester_class_id' => $semester_classes[0]->id,
            'class_type' => ClassType::PRIMARY,
            'student_id' => $students[2]->id,
            'seat_no' => 2,
            'is_active' => false, // Koko inactive but still assigned seat
        ],
        [
            'semester_setting_id' => $semester_setting->id,
            'semester_class_id' => $semester_classes[0]->id,
            'class_type' => ClassType::PRIMARY,
            'student_id' => $students[3]->id,
            'seat_no' => null,
            'is_active' => false, // Charlie inactive but seat_no = null
        ],


        // semester class 2
        [
            'semester_setting_id' => $semester_setting->id,
            'semester_class_id' => $semester_classes[1]->id,
            'class_type' => ClassType::PRIMARY,
            'student_id' => $students[2]->id,
            'seat_no' => 1,
            'is_active' => true, // Koko
        ],
        [
            'semester_setting_id' => $semester_setting->id,
            'semester_class_id' => $semester_classes[1]->id,
            'class_type' => ClassType::PRIMARY,
            'student_id' => $students[3]->id,
            'seat_no' => null,
            'is_active' => true, // Charlie
        ],
        [
            'semester_setting_id' => $semester_setting->id,
            'semester_class_id' => $semester_classes[1]->id,
            'class_type' => ClassType::PRIMARY,
            'student_id' => $students[0]->id,
            'seat_no' => null,
            'is_active' => false, // Jon old record but no seat
        ],
    ))->create();


    /**
     *
     * reassign seats for $semester_classes[0]
     *
     */
    $payload = [
        'seats' => [
            [
                'student_class_id' => $student_classes[2]->id, // Koko
                'seat_no' => 1,
            ],
            [
                'student_class_id' => $student_classes[1]->id, // Jack
                'seat_no' => 2,
            ],
            [
                'student_class_id' => $student_classes[0]->id, // Jon
                'seat_no' => 3,
            ],
        ],
    ];

    $response = $this->putJson(route($this->base_url . 'seat-assignment.assign-seats', ['semester_class' => $semester_classes[0]->id]), $payload)->json();

    expect($response)->toHaveSuccessGeneralResponse();

    /**
     * validate $semester_classes[0]
     */

    // Koko semester_class_1, seat_no, 1
    $student_classes[2]->refresh();

    expect($student_classes[2]->seat_no)->toEqual(1)
        ->and($student_classes[2]->semester_class_id)->toEqual($semester_classes[0]->id)
        ->and($student_classes[2]->student->getTranslation('name', 'en'))->toEqual('Koko');

    // Jack semester_class_1, seat_no, 2
    $student_classes[1]->refresh();

    expect($student_classes[1]->seat_no)->toEqual(2)
        ->and($student_classes[1]->semester_class_id)->toEqual($semester_classes[0]->id)
        ->and($student_classes[1]->student->getTranslation('name', 'en'))->toEqual('Jack');

    // Jon semester_class_1, seat_no, 3
    $student_classes[0]->refresh();

    expect($student_classes[0]->seat_no)->toEqual(3)
        ->and($student_classes[0]->semester_class_id)->toEqual($semester_classes[0]->id)
        ->and($student_classes[0]->student->getTranslation('name', 'en'))->toEqual('Jon');


    /**
     *
     * unassign seat Koko (inactive) for $semester_classes[0]
     *
     */
    $payload = [
        'seats' => [
            [
                'student_class_id' => $student_classes[2]->id, // Koko
                'seat_no' => null,
            ],
            [
                'student_class_id' => $student_classes[1]->id, // Jack
                'seat_no' => 1,
            ],
            [
                'student_class_id' => $student_classes[0]->id, // Jon
                'seat_no' => 2,
            ],
        ],
    ];

    $response = $this->putJson(route($this->base_url . 'seat-assignment.assign-seats', ['semester_class' => $semester_classes[0]->id]), $payload)->json();

    expect($response)->toHaveSuccessGeneralResponse();

    /**
     * validate $semester_classes[0]
     */

    // Koko semester_class_1, seat_no, null
    $student_classes[2]->refresh();

    expect($student_classes[2]->seat_no)->toEqual(null)
        ->and($student_classes[2]->semester_class_id)->toEqual($semester_classes[0]->id)
        ->and($student_classes[2]->student->getTranslation('name', 'en'))->toEqual('Koko');

    // Jack semester_class_1, seat_no, 1
    $student_classes[1]->refresh();

    expect($student_classes[1]->seat_no)->toEqual(1)
        ->and($student_classes[1]->semester_class_id)->toEqual($semester_classes[0]->id)
        ->and($student_classes[1]->student->getTranslation('name', 'en'))->toEqual('Jack');

    // Jon semester_class_1, seat_no, 2
    $student_classes[0]->refresh();

    expect($student_classes[0]->seat_no)->toEqual(2)
        ->and($student_classes[0]->semester_class_id)->toEqual($semester_classes[0]->id)
        ->and($student_classes[0]->student->getTranslation('name', 'en'))->toEqual('Jon');


    /**
     *
     * unassign seats for $semester_classes[1]
     *
     */
    $payload = [
        'seats' => [
            [
                'student_class_id' => $student_classes[4]->id, // Koko
                'seat_no' => null,
            ],
            [
                'student_class_id' => $student_classes[5]->id, // Charlie
                'seat_no' => null,
            ],
        ],
    ];

    $response = $this->putJson(route($this->base_url . 'seat-assignment.assign-seats', ['semester_class' => $semester_classes[1]->id]), $payload)->json();

    expect($response)->toHaveSuccessGeneralResponse();

    /**
     * validate $semester_classes[1]
     */

    // Koko semester_class_2, seat_no, null
    $student_classes[4]->refresh();

    expect($student_classes[4]->seat_no)->toEqual(null)
        ->and($student_classes[4]->semester_class_id)->toEqual($semester_classes[1]->id)
        ->and($student_classes[4]->student->getTranslation('name', 'en'))->toEqual('Koko');

    // Charlie semester_class_2, seat_no, null
    $student_classes[5]->refresh();

    expect($student_classes[5]->seat_no)->toEqual(null)
        ->and($student_classes[5]->semester_class_id)->toEqual($semester_classes[1]->id)
        ->and($student_classes[5]->student->getTranslation('name', 'en'))->toEqual('Charlie');

    // Jon semester_class_2, old record remained
    $student_classes[6]->refresh();

    expect($student_classes[6]->seat_no)->toEqual(null)
        ->and($student_classes[6]->is_active)->toEqual(false)
        ->and($student_classes[6]->semester_class_id)->toEqual($semester_classes[1]->id)
        ->and($student_classes[6]->student->getTranslation('name', 'en'))->toEqual('Jon');


    /**
     *
     * reassign seats for $semester_classes[1]
     *
     */
    $payload = [
        'seats' => [
            [
                'student_class_id' => $student_classes[4]->id, // Koko
                'seat_no' => 10,
            ],
            [
                'student_class_id' => $student_classes[5]->id, // Charlie
                'seat_no' => 9,
            ],
        ],
    ];

    $response = $this->putJson(route($this->base_url . 'seat-assignment.assign-seats', ['semester_class' => $semester_classes[1]->id]), $payload)->json();

    expect($response)->toHaveSuccessGeneralResponse();

    /**
     * validate $semester_classes[1]
     */

    // Koko semester_class_2, seat_no, 10
    $student_classes[4]->refresh();

    expect($student_classes[4]->seat_no)->toEqual(10)
        ->and($student_classes[4]->semester_class_id)->toEqual($semester_classes[1]->id)
        ->and($student_classes[4]->student->getTranslation('name', 'en'))->toEqual('Koko');

    // Charlie semester_class_2, seat_no, 9
    $student_classes[5]->refresh();

    expect($student_classes[5]->seat_no)->toEqual(9)
        ->and($student_classes[5]->semester_class_id)->toEqual($semester_classes[1]->id)
        ->and($student_classes[5]->student->getTranslation('name', 'en'))->toEqual('Charlie');
});

test('autoAssignSeatsBySemesterSetting', function () {
    $students = Student::factory(3)->state(new Sequence(
        [
            'name->en' => 'Serena', // Serena is in 2 class
        ],
        [
            'name->en' => 'David',
        ],
        [
            'name->en' => 'Cavill',
        ],
    ))->create();

    $semester_setting = SemesterSetting::factory()->create([
        'name' => '2024 Semester 1',
        'is_current_semester' => true,
    ]);

    $semester_classes = SemesterClass::factory(2)->state(new Sequence(
        [
            'semester_setting_id' => $semester_setting->id,
            'is_active' => true,
        ],
        [
            'semester_setting_id' => $semester_setting->id,
            'is_active' => true,
        ],
    ))->create();

    $student_classes = StudentClass::factory(4)->state(new Sequence(
    // semester class 1
        [
            'semester_setting_id' => $semester_setting->id,
            'semester_class_id' => $semester_classes[0]->id,
            'class_type' => ClassType::PRIMARY,
            'student_id' => $students[0]->id,
            'seat_no' => null,
            'is_active' => true, // Serena
        ],
        [
            'semester_setting_id' => $semester_setting->id,
            'semester_class_id' => $semester_classes[0]->id,
            'class_type' => ClassType::PRIMARY,
            'student_id' => $students[1]->id,
            'seat_no' => null,
            'is_active' => true, // David
        ],
        [
            'semester_setting_id' => $semester_setting->id,
            'semester_class_id' => $semester_classes[0]->id,
            'class_type' => ClassType::PRIMARY,
            'student_id' => $students[2]->id,
            'seat_no' => null,
            'is_active' => true, // Cavill
        ],

        // semester class 2
        [
            'semester_setting_id' => $semester_setting->id,
            'semester_class_id' => $semester_classes[1]->id,
            'class_type' => ClassType::PRIMARY,
            'student_id' => $students[0]->id,
            'seat_no' => null,
            'is_active' => true, // Serena
        ],
    ))->create();

    $response = $this->postJson(route($this->base_url . 'seat-assignment.auto-assign', $semester_setting->id))->json();

    expect($response)->toHaveSuccessGeneralResponse();

    // Cavill semester_class_1, seat_no, 1
    $student_classes[2]->refresh();

    expect($student_classes[2]->seat_no)->toEqual(1)
        ->and($student_classes[2]->semester_class_id)->toEqual($semester_classes[0]->id)
        ->and($student_classes[2]->student->getTranslation('name', 'en'))->toEqual('Cavill');

    // David semester_class_1, seat_no, 2
    $student_classes[1]->refresh();

    expect($student_classes[1]->seat_no)->toEqual(2)
        ->and($student_classes[1]->semester_class_id)->toEqual($semester_classes[0]->id)
        ->and($student_classes[1]->student->getTranslation('name', 'en'))->toEqual('David');

    // Serena semester_class_1, seat_no, 3
    $student_classes[0]->refresh();

    expect($student_classes[0]->seat_no)->toEqual(3)
        ->and($student_classes[0]->semester_class_id)->toEqual($semester_classes[0]->id)
        ->and($student_classes[0]->student->getTranslation('name', 'en'))->toEqual('Serena');


    // Serena semester_class_2, seat_no, 1
    $student_classes[3]->refresh();

    expect($student_classes[3]->seat_no)->toEqual(1)
        ->and($student_classes[3]->semester_class_id)->toEqual($semester_classes[1]->id)
        ->and($student_classes[3]->student->getTranslation('name', 'en'))->toEqual('Serena');
});

test('autoAssignSeatsBySemesterSetting failed because of validation', function () {
    /**
     * fail because invalid id
     */

    $response = $this->postJson(route($this->base_url . 'seat-assignment.auto-assign', 1))->json();

    expect($response)->toHaveFailedGeneralResponse()->toMatchArray([
        'error' => 'Resource not found',
        'data' => null,
    ]);

    /**
     * fail because some class already have an assigned seat in the semester_setting
     */

    $semester_setting = SemesterSetting::factory()->create([
        'name' => '2024 Semester 1',
        'is_current_semester' => true,
    ]);

    $student_classes = StudentClass::factory(2)->state(new Sequence(
        [
            'semester_setting_id' => $semester_setting->id,
            'class_type' => ClassType::PRIMARY,
            'seat_no' => 1,
            'is_active' => true,
        ],
        [
            'semester_setting_id' => $semester_setting->id,
            'class_type' => ClassType::PRIMARY,
            'seat_no' => 2,
            'is_active' => true,
        ],

    ))->create();

    $response = $this->postJson(route($this->base_url . 'seat-assignment.auto-assign', $semester_setting->id))->json();

    expect($response)->toHaveFailedGeneralResponse()->toMatchArray([
        'error' => __('system_error.6006'),
        'code' => 6006,
        'data' => null,
    ]);
});

test('assign multiple classes to multiple semesters success', function () {
    $class_1 = ClassModel::factory()->create();
    $class_2 = ClassModel::factory()->create();

    $semester_setting_1 = SemesterSetting::factory()->create();
    $semester_setting_2 = SemesterSetting::factory()->create();

    $existing_semester_class = SemesterClass::factory()->create([
        'semester_setting_id' => $semester_setting_1->id,
        'class_id' => $class_1->id,
    ]);

    $payload = [
        'semester_setting_ids' => [
            $semester_setting_1->id,
            $semester_setting_2->id,
        ],
        'class_ids' => [
            $class_1->id,
            $class_2->id,
        ],
    ];

    $this->assertDatabaseCount('semester_classes', 1);

    $this->assertDatabaseHas('semester_classes', [
        'id' => $existing_semester_class->id,
        'class_id' => $class_1->id,
        'semester_setting_id' => $semester_setting_1->id,
    ]);

    /**
     *
     * expect existing combination in payload is ignored
     * expect to create 3 new semester_classes
     *
     */

    $response = $this->putJson(route($this->base_url . 'assign-classes-to-semesters'), $payload);

    expect($response->json())->toHaveSuccessGeneralResponse();

    $this->assertDatabaseCount('semester_classes', 4);

    // old record remained
    $this->assertDatabaseHas('semester_classes', [
        'id' => $existing_semester_class->id,
        'semester_setting_id' => $existing_semester_class->semester_setting_id,
        'class_id' => $existing_semester_class->class_id,
    ]);

    // new record 1
    $this->assertDatabaseHas('semester_classes', [
        'class_id' => $class_1->id,
        'semester_setting_id' => $semester_setting_2->id,
    ]);

    // new record 2
    $this->assertDatabaseHas('semester_classes', [
        'class_id' => $class_2->id,
        'semester_setting_id' => $semester_setting_1->id,
    ]);

    // new record 3
    $this->assertDatabaseHas('semester_classes', [
        'class_id' => $class_2->id,
        'semester_setting_id' => $semester_setting_2->id,
    ]);
});

test('assign multiple classes to multiple semesters failed : validation error', function () {
    /**
     *
     *  invalid class_ids, semester_setting_ids
     *
     */

    $payload = [
        'semester_setting_ids' => [
            1,
            2,
        ],
        'class_ids' => [
            1,
            2,
        ],
    ];

    $response = $this->putJson(route($this->base_url . 'assign-classes-to-semesters'), $payload);

    expect($response->json())->toHaveFailedGeneralResponse()->toMatchArray([
        'error' => [
            'semester_setting_ids' => [
                'The selected semester setting ids is invalid.'
            ],
            'class_ids' => [
                'The selected class ids is invalid.'
            ],
        ],
        'data' => null,
    ]);


    /**
     *
     * duplicated ids provided
     *
     */
    $class_1 = ClassModel::factory()->create();
    $semester_setting_1 = SemesterSetting::factory()->create();

    $payload = [
        'class_ids' => [
            $class_1->id,
            $class_1->id,
        ],
        'semester_setting_ids' => [
            $semester_setting_1->id,
            $semester_setting_1->id,
        ],
    ];

    $response = $this->putJson(route($this->base_url . 'assign-classes-to-semesters'), $payload);

    expect($response->json())->toHaveFailedGeneralResponse()->toMatchArray([
        'error' => [
            'semester_setting_ids' => [
                'The semester setting field has a duplicate value.',
            ],
            'class_ids' => [
                'The class field has a duplicate value.',
            ],
        ],
        'data' => null,
    ]);
});
