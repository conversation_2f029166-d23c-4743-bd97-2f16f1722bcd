<?php

use App\Enums\HostelInOutType;
use App\Http\Resources\GuardianResource;
use App\Http\Resources\HostelRoomBedResource;
use App\Http\Resources\StudentResource;
use App\Http\Resources\UserResource;
use App\Models\Guardian;
use App\Models\HostelBedAssignment;
use App\Models\HostelInOutRecord;
use App\Models\HostelRoomBed;
use App\Models\Student;
use App\Models\User;
use App\Services\HostelInOutRecordService;
use Database\Seeders\PermissionSeeder;
use Laravel\Sanctum\Sanctum;

beforeEach(function () {
    $this->seed([
        PermissionSeeder::class
    ]);

    $this->admin = User::factory()->create();

    $this->admin->assignRole('Super Admin');

    Sanctum::actingAs($this->admin);

    $this->baseUrl = 'hostel-in-out-records.';

    $this->hostelInOutRecordTableName = resolve(HostelInOutRecord::class)->getTable();
});

test('index without any params', function () {
    $person_in_charge = User::factory()->withEmployee()->create();

    $first_hostel_in_out_record = HostelInOutRecord::factory()->create([
        'check_out_by' => $person_in_charge->id,
        'check_in_by' => $person_in_charge->id,
    ]);
    $second_hostel_in_out_record = HostelInOutRecord::factory()->create([
        'check_out_by' => $person_in_charge->id,
        'check_in_by' => $person_in_charge->id,
    ]);

    $response = $this->getJson(route($this->baseUrl . 'index', ['order_by' => ['id']]));

    $response->assertStatus(200);

    expect($response->json())->toHaveSuccessGeneralResponse()
        ->and($response->json()['data'])->toHaveCount(2)
        ->sequence(
            fn($response) => $response->toEqual([
                'id' => $first_hostel_in_out_record->id,
                'type' => $first_hostel_in_out_record->type,
                'student' => resourceToArray(new StudentResource($first_hostel_in_out_record->student)),
                'guardian' => resourceToArray(new GuardianResource($first_hostel_in_out_record->guardian)),
//                'hostel_room_bed' => resourceToArray(new HostelRoomBedResource($first_hostel_in_out_record->bed)),
                'check_out_datetime' => $first_hostel_in_out_record->check_out_datetime,
                'check_in_datetime' => $first_hostel_in_out_record->check_in_datetime,
                'card_no' => $first_hostel_in_out_record->card_no,
                'reason' => $first_hostel_in_out_record->reason,
            ]),
            fn($response) => $response->toEqual([
                'id' => $second_hostel_in_out_record->id,
                'type' => $second_hostel_in_out_record->type,
                'student' => resourceToArray(new StudentResource($second_hostel_in_out_record->student)),
                'guardian' => resourceToArray(new GuardianResource($second_hostel_in_out_record->guardian)),
//                'hostel_room_bed' => resourceToArray(new HostelRoomBedResource($second_hostel_in_out_record->bed)),
                'check_out_datetime' => $second_hostel_in_out_record->check_out_datetime,
                'check_in_datetime' => $second_hostel_in_out_record->check_in_datetime,
                'card_no' => $second_hostel_in_out_record->card_no,
                'reason' => $second_hostel_in_out_record->reason,
            ]),
        );

    $response = $this->getJson(route(
        $this->baseUrl . 'index',
        [
            'order_by' => ['id'],
            'includes' => [
                'bed.hostelRoom.hostelBlock',
                'checkOutBy.userables',
                'checkInBy.userables',
            ]
        ]
    ));

    $response->assertStatus(200);

    $first_hostel_in_out_record = $first_hostel_in_out_record->load('bed.hostelRoom.hostelBlock');
    $second_hostel_in_out_record = $second_hostel_in_out_record->load('bed.hostelRoom.hostelBlock');

    $person_in_charge->loadMissing('userables');

    expect($response->json())->toHaveSuccessGeneralResponse()
        ->and($response->json()['data'])->toHaveCount(2)
        ->sequence(
            fn($response) => $response->toMatchArray([
                'id' => $first_hostel_in_out_record->id,
                'type' => $first_hostel_in_out_record->type,
                'student' => resourceToArray(new StudentResource($first_hostel_in_out_record->student)),
                'guardian' => resourceToArray(new GuardianResource($first_hostel_in_out_record->guardian)),
                'hostel_room_bed' => resourceToArray(new HostelRoomBedResource($first_hostel_in_out_record->bed)),
                'check_out_datetime' => $first_hostel_in_out_record->check_out_datetime,
                'check_out_by' => resourceToArray(new UserResource($person_in_charge)),
                'check_in_datetime' => $first_hostel_in_out_record->check_in_datetime,
                'check_in_by' => resourceToArray(new UserResource($person_in_charge)),
                'card_no' => $first_hostel_in_out_record->card_no,
                'reason' => $first_hostel_in_out_record->reason,
            ]),
            fn($response) => $response->toMatchArray([
                'id' => $second_hostel_in_out_record->id,
                'type' => $second_hostel_in_out_record->type,
                'student' => resourceToArray(new StudentResource($second_hostel_in_out_record->student)),
                'guardian' => resourceToArray(new GuardianResource($second_hostel_in_out_record->guardian)),
                'hostel_room_bed' => resourceToArray(new HostelRoomBedResource($second_hostel_in_out_record->bed)),
                'check_out_datetime' => $second_hostel_in_out_record->check_out_datetime,
                'check_out_by' => resourceToArray(new UserResource($person_in_charge)),
                'check_in_datetime' => $second_hostel_in_out_record->check_in_datetime,
                'check_in_by' => resourceToArray(new UserResource($person_in_charge)),
                'card_no' => $second_hostel_in_out_record->card_no,
                'reason' => $second_hostel_in_out_record->reason,
            ]),
        );
});

test('index with params filter by type', function () {
    HostelInOutRecord::factory(2)->create([
        'type' => HostelInOutType::HOME->value,
    ]);

    HostelInOutRecord::factory(1)->create([
        'type' => HostelInOutType::OUTING->value,
    ]);

    expect(HostelInOutRecord::count())->toBe(3);

    // filter by type HOME
    $response = $this->getJson(route($this->baseUrl . 'index', [
        'type' => HostelInOutType::HOME->value,
    ]));

    $response->assertStatus(200);

    expect($response->json())->toHaveSuccessGeneralResponse()
        ->and($response->json()['data'])->toHaveCount(2)
        ->toHaveKey('0.type', HostelInOutType::HOME->value)
        ->toHaveKey('1.type', HostelInOutType::HOME->value);

    // filter by type OUTING
    $response = $this->getJson(route($this->baseUrl . 'index', [
        'type' => HostelInOutType::OUTING->value,
    ]));

    $response->assertStatus(200);

    expect($response->json())->toHaveSuccessGeneralResponse()
        ->and($response->json()['data'])->toHaveCount(1)
        ->toHaveKey('0.type', HostelInOutType::OUTING->value);
});

test('index with params filter by student_id', function () {
    $student = Student::factory()->create();

    // two records for going out for $student
    HostelInOutRecord::factory(2)->create([
        'student_id' => $student->id,
    ]);

    HostelInOutRecord::factory()->create();

    expect(HostelInOutRecord::count())->toBe(3);

    // filter by student_id HOME
    $response = $this->getJson(route($this->baseUrl . 'index', [
        'student_id' => $student->id,
    ]));

    $response->assertStatus(200);

    expect($response->json())->toHaveSuccessGeneralResponse()
        ->and($response->json()['data'])->toHaveCount(2)
        ->toHaveKey('0.student.id', $student->id)
        ->toHaveKey('1.student.id', $student->id);
});

test('index with params order by id', function () {
    $first_hostel_in_out_record = HostelInOutRecord::factory()->create();
    $second_hostel_in_out_record = HostelInOutRecord::factory()->create();

    expect(HostelInOutRecord::count())->toBe(2);

    // Sort by id asc
    $response = $this->getJson(route($this->baseUrl . 'index', [
        'order_by' => ['id' => 'asc'],
    ]));

    $response->assertStatus(200);

    expect($response->json())->toHaveSuccessGeneralResponse()
        ->and($response->json()['data'])
        ->toHaveKey('0.id', $first_hostel_in_out_record->id)
        ->toHaveKey('1.id', $second_hostel_in_out_record->id);

    // Sort by id desc
    $response = $this->getJson(route($this->baseUrl . 'index', [
        'order_by' => ['id' => 'desc'],
    ]));

    $response->assertStatus(200);

    expect($response->json())->toHaveSuccessGeneralResponse()
        ->and($response->json()['data'])
        ->toHaveKey('0.id', $second_hostel_in_out_record->id)
        ->toHaveKey('1.id', $first_hostel_in_out_record->id);
});

test('index with default includes', function () {
    $first_hostel_in_out_record = HostelInOutRecord::factory()->create();
    $second_hostel_in_out_record = HostelInOutRecord::factory()->create();

    expect(HostelInOutRecord::count())->toBe(2);

    $response = $this->getJson(route($this->baseUrl . 'index', [
        'order_by' => ['id'],
    ]));

    $response->assertStatus(200);

    expect($response->json())->toHaveSuccessGeneralResponse()
        ->and($response->json()['data'])->toHaveCount(2)
        ->sequence(
            fn($response) => $response->toHaveKey('student', resourceToArray(new StudentResource($first_hostel_in_out_record->student)))
                ->toHaveKey('guardian', resourceToArray(new GuardianResource($first_hostel_in_out_record->guardian))),

            fn($response) => $response->toHaveKey('student', resourceToArray(new StudentResource($second_hostel_in_out_record->student)))
                ->toHaveKey('guardian', resourceToArray(new GuardianResource($second_hostel_in_out_record->guardian))),
        );
});

test('create success', function () {
    expect(HostelInOutRecord::count())->toBe(0);

    $student = Student::factory()->create();

    $guardian = Guardian::factory()->create();

    $bed = HostelRoomBed::factory()->create();

    HostelBedAssignment::factory()->create([
        'assignable_type' => Student::class,
        'assignable_id' => $student->id,
        'hostel_room_bed_id' => $bed->id,
    ]);

    $payload = [
        'students' => [
            [
                'type' => HostelInOutType::OUTING->value,
                'id' => $student->id,
                'guardian_id' => $guardian->id,
                'hostel_room_bed_id' => $bed->id,
                'check_out_datetime' => '2024-12-25 08:30:00',
                'card_no' => 'PL11000',
                'reason' => 'student go to supermarket',
            ]
        ]
    ];

    $expected_params = $payload;
    $expected_params['check_out_by'] = $this->admin->id;

    $this->mock(HostelInOutRecordService::class)
        ->shouldReceive('createHostelInOutRecords')
        ->once()
        ->with($expected_params)
        ->andReturnNull();

    $response = $this->postJson(route($this->baseUrl . 'create'), $payload);

    $response->assertStatus(200);

    expect($response->json())->toHaveSuccessGeneralResponse();
});

test('create success: with guardian', function () {
    expect(HostelInOutRecord::count())->toBe(0);

    $student1 = Student::factory()->create();
    $student2 = Student::factory()->create();

    $guardian1 = Guardian::factory()->create();
    $guardian2 = Guardian::factory()->create();

    $bed1 = HostelRoomBed::factory()->create();
    $bed2 = HostelRoomBed::factory()->create();

    HostelBedAssignment::factory()->create([
        'assignable_type' => Student::class,
        'assignable_id' => $student1->id,
        'hostel_room_bed_id' => $bed1->id,
    ]);

    HostelBedAssignment::factory()->create([
        'assignable_type' => Student::class,
        'assignable_id' => $student2->id,
        'hostel_room_bed_id' => $bed2->id,
    ]);

    $payload = [
        'students' => [
            [
                'type' => HostelInOutType::OUTING->value,
                'id' => $student1->id,
                'guardian_id' => $guardian1->id,
                'hostel_room_bed_id' => $bed1->id,
                'check_out_datetime' => '2024-12-25 08:30:00',
                'card_no' => 'PL11000',
                'reason' => 'student1 go to supermarket',
            ],
            [
                'type' => HostelInOutType::OUTING->value,
                'id' => $student2->id,
                'guardian_id' => $guardian2->id, // guardian is a required field
                'hostel_room_bed_id' => $bed2->id,
                'check_out_datetime' => '2024-12-25 08:30:00',
                'card_no' => 'PL11001',
                'reason' => 'student2 go to supermarket',
            ],
        ]
    ];

    $response = $this->postJson(route($this->baseUrl . 'create'), $payload);

    $response->assertStatus(200);

    expect(HostelInOutRecord::count())->toBe(2)
        ->and($response->json())->toHaveSuccessGeneralResponse();

    $this->assertDatabaseHas($this->hostelInOutRecordTableName, [
        'type' => $payload['students'][0]['type'],
        'student_id' => $payload['students'][0]['id'],
        'guardian_id' => $payload['students'][0]['guardian_id'],
        'hostel_room_bed_id' => $payload['students'][0]['hostel_room_bed_id'],
        'check_out_datetime' => $payload['students'][0]['check_out_datetime'],
        'check_out_by' => $this->admin->id,
        'check_in_datetime' => null,
        'check_in_by' => null,
        'card_no' => $payload['students'][0]['card_no'],
        'reason' => $payload['students'][0]['reason'],
    ]);

    $this->assertDatabaseHas($this->hostelInOutRecordTableName, [
        'type' => $payload['students'][1]['type'],
        'student_id' => $payload['students'][1]['id'],
        'guardian_id' => $payload['students'][1]['guardian_id'],
        'hostel_room_bed_id' => $payload['students'][1]['hostel_room_bed_id'],
        'check_out_datetime' => $payload['students'][1]['check_out_datetime'],
        'check_out_by' => $this->admin->id,
        'check_in_datetime' => null,
        'check_in_by' => null,
        'card_no' => $payload['students'][1]['card_no'],
        'reason' => $payload['students'][1]['reason'],
    ]);
});

test('create validation error', function () {
    expect(HostelInOutRecord::count())->toBe(0);

    $response = $this->postJson(route($this->baseUrl . 'create'));

    $response->assertStatus(422);

    // Expect nothing is created and return validation error
    $this->assertDatabaseCount($this->hostelInOutRecordTableName, 0);

    expect(HostelInOutRecord::count())->toBe(0)
        ->and($response->json())->toHaveFailedGeneralResponse()
        ->toMatchArray([
            'error' => [
                "students" => [
                    "The students field is required."
                ]
            ],
            'data' => null
        ]);

    $response = $this->postJson(route($this->baseUrl . 'create'), [
        'students' => [
            []
        ]
    ]);

    $response->assertStatus(422);

    // Expect nothing is created and return validation error
    $this->assertDatabaseCount($this->hostelInOutRecordTableName, 0);

    expect(HostelInOutRecord::count())->toBe(0)
        ->and($response->json())->toHaveFailedGeneralResponse()
        ->toMatchArray([
            'error' => [
                "students.0.type" => [
                    "The type field is required."
                ],
                "students.0.id" => [
                    "The student field is required."
                ],
                "students.0.hostel_room_bed_id" => [
                    "The hostel room bed field is required."
                ],
                "students.0.check_out_datetime" => [
                    "The check out datetime field is required."
                ],
            ],
            'data' => null
        ]);

    // When student already checked out without check in and try to create again
    $student = Student::factory()->create();
    $guardian = Guardian::factory()->create();
    $bed = HostelRoomBed::factory()->create();

    HostelBedAssignment::factory()->create([
        'assignable_type' => Student::class,
        'assignable_id' => $student->id,
        'hostel_room_bed_id' => $bed->id,
    ]);

    HostelInOutRecord::factory()->onlyCheckout()->create([
        'student_id' => $student->id,
        'guardian_id' => $guardian->id,
        'hostel_room_bed_id' => $bed->id,
    ]);

    $response = $this->postJson(route($this->baseUrl . 'create'), [
        'students' => [
            [
                'type' => HostelInOutType::OUTING->value,
                'id' => $student->id,
                'guardian_id' => $guardian->id,
                'hostel_room_bed_id' => $bed->id,
                'check_out_datetime' => '2024-12-25 08:30:00',
                'reason' => 'student go to supermarket',
            ]
        ]
    ]);

    $response->assertStatus(422);

    $this->assertDatabaseCount($this->hostelInOutRecordTableName, 1);

    expect(HostelInOutRecord::count())->toBe(1)
        ->and($response->json())->toHaveFailedGeneralResponse()
        ->toMatchArray([
            'error' => [
                "students.0.id" => [
                    "The student has already checked out from the hostel."
                ],
                'students.0.card_no' => [
                    "The card number field is required when type is OUTING."
                ],
            ],
            'data' => null
        ]);
});

test('check in success', function () {
    $hostel_in_out_record = HostelInOutRecord::factory()->onlyCheckout()->create();

    expect(HostelInOutRecord::count())->toBe(1);
    $check_in_datetime = now()->addHour()->toDateTimeString();

    $check_in_datetime = now()->addHour()->toDateTimeString();

    $payload = [
        'records' => [
            [
                'id' => $hostel_in_out_record->id,
                'check_in_datetime' => $check_in_datetime,
                'reason' => 'updated reason',
            ]
        ]
    ];

    $response = $this->patchJson(route($this->baseUrl . 'check-in'), $payload);

    $response->assertStatus(200);

    $this->assertDatabaseCount($this->hostelInOutRecordTableName, 1);

    expect(HostelInOutRecord::count())->toBe(1)
        ->and($response->json())->toHaveSuccessGeneralResponse();

    $this->assertDatabaseHas($this->hostelInOutRecordTableName, [
        'id' => $hostel_in_out_record->id,
        'check_in_datetime' => $check_in_datetime,
        'check_in_by' => $this->admin->id,
        'reason' => 'updated reason',
    ]);
});

test('check in validation error', function () {
    $hostel_in_out_record = HostelInOutRecord::factory()->onlyCheckout()->create();

    expect(HostelInOutRecord::count())->toBe(1);

    $response = $this->patchJson(route($this->baseUrl . 'check-in'), []);

    $response->assertStatus(422);

    // Expect nothing is created and return validation error
    expect(HostelInOutRecord::count())->toBe(1)
        ->and($response->json())->toMatchArray([
            'error' => [
                'records' => [
                    'The records field is required.'
                ],
            ],
            'data' => null
        ]);

    $this->assertDatabaseHas($this->hostelInOutRecordTableName, [
        'id' => $hostel_in_out_record->id,
        'check_in_datetime' => null,
        'check_in_by' => null,
    ]);

    $response = $this->patchJson(route($this->baseUrl . 'check-in'), [
        'records' => [
            []
        ]
    ]);

    $response->assertStatus(422);

    // Expect nothing is created and return validation error
    expect(HostelInOutRecord::count())->toBe(1)
        ->and($response->json())->toMatchArray([
            'error' => [
                "records.0.id" => [
                    "The records.0.id field is required."
                ],
                "records.0.check_in_datetime" => [
                    "The records.0.check_in_datetime field is required."
                ]
            ],
            'data' => null
        ]);

    $this->assertDatabaseHas($this->hostelInOutRecordTableName, [
        'id' => $hostel_in_out_record->id,
        'check_in_datetime' => null,
        'check_in_by' => null,
    ]);
});

test('update success: without check_in_datetime', function () {
    $hostel_in_out_record = HostelInOutRecord::factory()->onlyCheckout()->create();

    expect(HostelInOutRecord::count())->toBe(1);

    $payload = [
        'check_in_datetime' => null,
        'reason' => 'updated reason',
        'card_no' => 'P111',
    ];

    $response = $this->putJson(route($this->baseUrl . 'update', $hostel_in_out_record->id), $payload);

    $response->assertStatus(200);

    expect($response->json())->toHaveSuccessGeneralResponse();

    $this->assertDatabaseHas($this->hostelInOutRecordTableName, [
        'id' => $hostel_in_out_record->id,
        'type' => HostelInOutType::HOME->value,
        'check_in_datetime' => null,
        'check_in_by' => null,
        'card_no' => 'P111',
        'reason' => 'updated reason',
    ]);
});

test('update success: with check_in_datetime', function () {
    $hostel_in_out_record = HostelInOutRecord::factory()->onlyCheckout()->create();

    expect(HostelInOutRecord::count())->toBe(1);

    $payload = [
        'check_in_datetime' => '2024-12-25 08:30:00',
        'reason' => 'updated reason',
    ];

    $response = $this->putJson(route($this->baseUrl . 'update', $hostel_in_out_record->id), $payload);

    $response->assertStatus(200);

    expect($response->json())->toHaveSuccessGeneralResponse();

    $this->assertDatabaseHas($this->hostelInOutRecordTableName, [
        'id' => $hostel_in_out_record->id,
        'type' => HostelInOutType::HOME->value,
        'check_in_datetime' => '2024-12-25 08:30:00',
        'check_in_by' => $this->admin->id,
        'reason' => 'updated reason',
    ]);
});

test('show single record success', function () {
    $person_in_charge = User::factory()->withEmployee()->create();

    $first_hostel_in_out_record = HostelInOutRecord::factory()->create([
        'check_out_by' => $person_in_charge->id,
        'check_in_by' => $person_in_charge->id,
    ]);
    $second_hostel_in_out_record = HostelInOutRecord::factory()->create([
        'check_out_by' => $person_in_charge->id,
        'check_in_by' => $person_in_charge->id,
    ]);

    $response = $this->getJson(route($this->baseUrl . 'show', $first_hostel_in_out_record->id));

    $response->assertStatus(200);

    $first_hostel_in_out_record = $first_hostel_in_out_record->load('bed.hostelRoom.hostelBlock');

    $person_in_charge->loadMissing('userables');

    expect($response->json())->toHaveSuccessGeneralResponse()
        ->and($response->json()['data'])
        ->toMatchArray([
            'id' => $first_hostel_in_out_record->id,
            'type' => $first_hostel_in_out_record->type,
            'student' => resourceToArray(new StudentResource($first_hostel_in_out_record->student)),
            'guardian' => resourceToArray(new GuardianResource($first_hostel_in_out_record->guardian)),
            'hostel_room_bed' => resourceToArray(new HostelRoomBedResource($first_hostel_in_out_record->bed)),
            'check_out_datetime' => $first_hostel_in_out_record->check_out_datetime,
            'check_out_by' => resourceToArray(new UserResource($person_in_charge)),
            'check_in_datetime' => $first_hostel_in_out_record->check_in_datetime,
            'check_in_by' => resourceToArray(new UserResource($person_in_charge)),
            'card_no' => $first_hostel_in_out_record->card_no,
            'reason' => $first_hostel_in_out_record->reason,
        ]);

    $response = $this->getJson(route($this->baseUrl . 'show', $second_hostel_in_out_record->id));

    $response->assertStatus(200);

    $second_hostel_in_out_record = $second_hostel_in_out_record->load('bed.hostelRoom.hostelBlock');

    expect($response->json())->toHaveSuccessGeneralResponse()
        ->and($response->json()['data'])
        ->toMatchArray([
            'id' => $second_hostel_in_out_record->id,
            'type' => $second_hostel_in_out_record->type,
            'student' => resourceToArray(new StudentResource($second_hostel_in_out_record->student)),
            'guardian' => resourceToArray(new GuardianResource($second_hostel_in_out_record->guardian)),
            'hostel_room_bed' => resourceToArray(new HostelRoomBedResource($second_hostel_in_out_record->bed)),
            'check_out_datetime' => $second_hostel_in_out_record->check_out_datetime,
            'check_out_by' => resourceToArray(new UserResource($person_in_charge)),
            'check_in_datetime' => $second_hostel_in_out_record->check_in_datetime,
            'check_in_by' => resourceToArray(new UserResource($person_in_charge)),
            'card_no' => $second_hostel_in_out_record->card_no,
            'reason' => $second_hostel_in_out_record->reason,
        ]);
});

test('show not existing record error', function () {
    expect(HostelInOutRecord::count())->toBe(0);

    $response = $this->getJson(route($this->baseUrl . 'show', 1));

    $response->assertStatus(404);

    expect($response->json())->toHaveModelResourceNotFoundResponse();
});

test('delete success', function () {
    $first_hostel_in_out_record = HostelInOutRecord::factory()->create();
    $other_hostel_in_out_records = HostelInOutRecord::factory()->count(3)->create();

    $this->assertDatabaseCount($this->hostelInOutRecordTableName, 4);

    //id not exist
    $response = $this->deleteJson(route($this->baseUrl . 'destroy', ['hostel_in_out_record' => 9999]))->json();
    expect($response)->toHaveModelResourceNotFoundResponse();

    $this->assertDatabaseCount($this->hostelInOutRecordTableName, 4);

    //delete success
    $response = $this->deleteJson(route($this->baseUrl . 'destroy',
        ['hostel_in_out_record' => $first_hostel_in_out_record->id]))->json();
    expect($response)->toHaveSuccessGeneralResponse();

    $this->assertDatabaseCount($this->hostelInOutRecordTableName, 3);
    $this->assertDatabaseMissing($this->hostelInOutRecordTableName, ['id' => $first_hostel_in_out_record->id]);

    foreach ($other_hostel_in_out_records as $other_hostel_in_out_record) {
        $this->assertDatabaseHas($this->hostelInOutRecordTableName, ['id' => $other_hostel_in_out_record->id]);
    }
});
