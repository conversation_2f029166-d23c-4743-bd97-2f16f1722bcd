<?php

use App\Enums\MerchantType;
use App\Http\Resources\EcommerceProductGroupWithoutProductResource;
use App\Http\Resources\EcommerceProductSubCategoryWithoutProductResource;
use App\Http\Resources\EcommerceProductTagWithoutProductResource;
use App\Http\Resources\MerchantResource;
use App\Models\Currency;
use App\Models\EcommerceProduct;
use App\Models\EcommerceProductAvailableDate;
use App\Models\EcommerceProductCategoryAssignment;
use App\Models\EcommerceProductDeliveryDate;
use App\Models\EcommerceProductGroup;
use App\Models\EcommerceProductGroupAssignment;
use App\Models\EcommerceProductSubCategory;
use App\Models\EcommerceProductTag;
use App\Models\EcommerceProductTagAssignment;
use App\Models\Employee;
use App\Models\Merchant;
use App\Models\ModelHasDefaultRole;
use App\Models\Role;
use App\Models\Tax;
use App\Models\User;
use App\Services\EcommerceProductService;
use Database\Seeders\InternationalizationSeeder;
use Database\Seeders\PermissionSeeder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Factories\Sequence;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\UploadedFile;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\Hash;
use Laravel\Sanctum\Sanctum;
use Mockery\MockInterface;

uses(RefreshDatabase::class);

beforeEach(function () {
    $this->seed([
        InternationalizationSeeder::class,
        PermissionSeeder::class,
    ]);

    app()->setLocale('en');

    $this->withHeaders([
        'Accept-Language' => 'en'
    ]);

    $this->user = User::factory()->create([
        'password' => Hash::make('123456'),
    ]);

    $this->employee = Employee::factory()->create([
        'user_id' => $this->user->id,
    ]);

    $this->user->assignRole('Super Admin');

    $this->tax = Tax::factory()->create([
        'percentage' => 0,
        'name' => 'Tax Exempt',
    ]);
    $this->tax2 = Tax::factory()->create([
        'percentage' => 8,
        'name' => 'SST 8%',
    ]);

    Sanctum::actingAs($this->user);

    $this->table = resolve(EcommerceProduct::class)->getTable();
    $this->product_available_date_table = resolve(EcommerceProductAvailableDate::class)->getTable();
    $this->product_delivery_date_table = resolve(EcommerceProductDeliveryDate::class)->getTable();
    $this->product_group_assignment_table = resolve(EcommerceProductGroupAssignment::class)->getTable();
    $this->product_category_assignment_table = resolve(EcommerceProductCategoryAssignment::class)->getTable();
    $this->product_tag_assignment_table = resolve(EcommerceProductTagAssignment::class)->getTable();

    $this->routeNamePrefix = 'products';
});

test('getByDeliveryDates()', function () {
    $merchant = Merchant::factory()->create([
        'type' => MerchantType::CANTEEN
    ]);

    $products = EcommerceProduct::factory(3)->create([
        'merchant_id' => $merchant->id,
    ]);
    $product_group = EcommerceProductGroup::factory()->create();

    EcommerceProductGroupAssignment::factory()->create([
        'product_id' => $products[0]->id,
        'product_group_id' => $product_group->id,
    ]);

    EcommerceProductDeliveryDate::factory(4)
        ->state(new Sequence(
            [
                'product_id' => $products[0]->id,
                'delivery_date' => '2021-01-01',
            ],
            [
                'product_id' => $products[2]->id,
                'delivery_date' => '2021-01-01'
            ],
            [
                'product_id' => $products[0]->id,
                'delivery_date' => '2021-01-02',
                'product_group_id' => $product_group->id,
            ],
            [
                'product_id' => $products[2]->id,
                'delivery_date' => '2021-01-03'
            ],
        ))
        ->create();

    $payload = [
        'delivery_date_from' => '2021-01-01',
        'delivery_date_to' => '2021-01-02',
        'merchant_type' => MerchantType::CANTEEN,
    ];

    $response = $this->getJson(route($this->routeNamePrefix . '.get-by-delivery-date', $payload))->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toHaveCount(2)
        ->sequence(
            fn($data) => $data->toMatchArray([
                'delivery_date' => '2021-01-01',
                'products' => [
                    [
                        'id' => $products[0]->id,
                        'name' => $products[0]->name,
                    ],
                    [
                        'id' => $products[2]->id,
                        'name' => $products[2]->name,
                    ],
                ],
                'product_groups' => []
            ]),
            fn($data) => $data->toMatchArray([
                'delivery_date' => '2021-01-02',
                'products' => [],
                'product_groups' => [
                    [
                        'id' => $product_group->id,
                        'name' => $product_group->name,
                        'products' => $product_group->products->select('id', 'name')->toArray(),
                    ]
                ]
            ]),
        );
});

test('bulkUpdateDeliveryDate', function () {
    $products = EcommerceProduct::factory(3)->create();
    $product_group = EcommerceProductGroup::factory()->create();

    EcommerceProductGroupAssignment::factory()->create([
        'product_id' => $products[0]->id,
        'product_group_id' => $product_group->id,
    ]);

    EcommerceProductDeliveryDate::factory(2)
        ->state(new Sequence(
            [
                'product_id' => $products[0]->id,
                'delivery_date' => '2021-03-01'
            ],
            [
                'product_id' => $products[2]->id,
                'delivery_date' => '2021-01-01'
            ]
        ))
        ->create();

    $this->assertDatabaseCount($this->product_delivery_date_table, 2);

    $payload = [
        'dates' => [
            [
                'delivery_date' => '2021-01-01',
                'product_ids' => [$products[1]->id],
                'product_group_ids' => [$product_group->id],
            ],
            [
                'delivery_date' => '2021-01-02',
                'product_ids' => [$products[2]->id],
                'product_group_ids' => [],
            ],
        ]
    ];

    $response = $this->postJson(route($this->routeNamePrefix . '.product-delivery-date-bulk-update'), $payload)->json();

    expect($response)->toHaveSuccessGeneralResponse();

    //check db record
    $this->assertDatabaseCount($this->product_delivery_date_table, 4);

    //$products[2] delivery date 2021-01-01 will be deleted as the id dint appear in payload delivery_date=2021-01-01
    $this->assertDatabaseMissing($this->product_delivery_date_table, [
        'product_id' => $products[2]->id,
        'delivery_date' => '2021-01-01'
    ]);

    //check $products[0] old delivery date 2021-03-01 that not in payload will not deleted in db
    $this->assertDatabaseHas($this->product_delivery_date_table, [
        'product_id' => $products[0]->id,
        'delivery_date' => '2021-03-01'
    ]);

    //check product group products will create new delivery date with product group id
    $this->assertDatabaseHas($this->product_delivery_date_table, [
        'product_id' => $products[0]->id,
        'delivery_date' => '2021-01-01',
        'product_group_id' => $product_group->id
    ]);

    //check products will create new delivery date
    $this->assertDatabaseHas($this->product_delivery_date_table, [
        'product_id' => $products[1]->id,
        'delivery_date' => '2021-01-01'
    ]);

    $this->assertDatabaseHas($this->product_delivery_date_table, [
        'product_id' => $products[2]->id,
        'delivery_date' => '2021-01-02'
    ]);
});

test('getByAvailableDates()', function () {
    $merchant = Merchant::factory()->create([
        'type' => MerchantType::CANTEEN
    ]);

    $products = EcommerceProduct::factory(3)->create([
        'merchant_id' => $merchant->id,
    ]);
    $product_group = EcommerceProductGroup::factory()->create();

    EcommerceProductGroupAssignment::factory()->create([
        'product_id' => $products[0]->id,
        'product_group_id' => $product_group->id,
    ]);

    EcommerceProductAvailableDate::factory(4)
        ->state(new Sequence(
            [
                'product_id' => $products[0]->id,
                'available_date' => '2021-01-01',
            ],
            [
                'product_id' => $products[2]->id,
                'available_date' => '2021-01-01'
            ],
            [
                'product_id' => $products[0]->id,
                'available_date' => '2021-01-02',
                'product_group_id' => $product_group->id,
            ],
            [
                'product_id' => $products[2]->id,
                'available_date' => '2021-01-03'
            ],
        ))
        ->create();

    $payload = [
        'available_date_from' => '2021-01-01',
        'available_date_to' => '2021-01-02',
        'merchant_type' => MerchantType::CANTEEN,
    ];

    $response = $this->getJson(route($this->routeNamePrefix . '.get-by-available-date', $payload))->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toHaveCount(2)
        ->sequence(
            fn($data) => $data->toMatchArray([
                'available_date' => '2021-01-01',
                'products' => [
                    [
                        'id' => $products[0]->id,
                        'name' => $products[0]->name,
                    ],
                    [
                        'id' => $products[2]->id,
                        'name' => $products[2]->name,
                    ],
                ],
                'product_groups' => []
            ]),
            fn($data) => $data->toMatchArray([
                'available_date' => '2021-01-02',
                'products' => [],
                'product_groups' => [
                    [
                        'id' => $product_group->id,
                        'name' => $product_group->name,
                        'products' => $product_group->products->select('id', 'name')->toArray(),
                    ]
                ]
            ]),
        );
});

test('bulkUpdateAvailableDate', function () {
    $products = EcommerceProduct::factory(3)->create();
    $product_group = EcommerceProductGroup::factory()->create();

    EcommerceProductGroupAssignment::factory()->create([
        'product_id' => $products[0]->id,
        'product_group_id' => $product_group->id,
    ]);

    EcommerceProductAvailableDate::factory(2)
        ->state(new Sequence(
            [
                'product_id' => $products[0]->id,
                'available_date' => '2021-03-01'
            ],
            [
                'product_id' => $products[2]->id,
                'available_date' => '2021-01-01'
            ]
        ))
        ->create();

    $this->assertDatabaseCount($this->product_available_date_table, 2);

    $payload = [
        'dates' => [
            [
                'available_date' => '2021-01-01',
                'product_ids' => [$products[1]->id],
                'product_group_ids' => [$product_group->id],
            ],
            [
                'available_date' => '2021-01-02',
                'product_ids' => [$products[2]->id],
                'product_group_ids' => [],
            ],
        ]
    ];

    $response = $this->postJson(route($this->routeNamePrefix . '.product-available-date-bulk-update'), $payload)->json();

    expect($response)->toHaveSuccessGeneralResponse();

    //check db record
    $this->assertDatabaseCount($this->product_available_date_table, 4);

    //$products[2] available date 2021-01-01 will be deleted as the id dint appear in payload available_date=2021-01-01
    $this->assertDatabaseMissing($this->product_available_date_table, [
        'product_id' => $products[2]->id,
        'available_date' => '2021-01-01'
    ]);

    //check $products[0] old available date 2021-03-01 that not in payload will not deleted in db
    $this->assertDatabaseHas($this->product_available_date_table, [
        'product_id' => $products[0]->id,
        'available_date' => '2021-03-01'
    ]);

    //check product group products will create new available date with product group id
    $this->assertDatabaseHas($this->product_available_date_table, [
        'product_id' => $products[0]->id,
        'available_date' => '2021-01-01',
        'product_group_id' => $product_group->id
    ]);

    //check products will create new available date
    $this->assertDatabaseHas($this->product_available_date_table, [
        'product_id' => $products[1]->id,
        'available_date' => '2021-01-01'
    ]);

    $this->assertDatabaseHas($this->product_available_date_table, [
        'product_id' => $products[2]->id,
        'available_date' => '2021-01-02'
    ]);
});

test('indexByMerchantType: test service accepting params', function () {
    // Test if all values passes the validation and passes into service
    // Individual filters testing in EcommerceProductRepositoryTest
    $merchants = Merchant::factory(2)->create();
    $products = EcommerceProduct::factory(3)->state(new Sequence(
        [
            'name' => 'Test 1',
            'code' => 'test1',
            'merchant_id' => $merchants[0]->id,
            'is_active' => true,
            'description' => 'Test 1',
            'price_before_tax' => 10,
            'currency_code' => 'TWD'
        ],
        [
            'name' => 'Test 2',
            'code' => 'test2',
            'merchant_id' => $merchants[0]->id,
            'is_active' => true,
            'description' => 'Test 2',
            'price_before_tax' => 11,
            'currency_code' => 'TWD'
        ],
        [
            'name' => 'wee',
            'code' => 'wee',
            'merchant_id' => $merchants[1]->id,
            'is_active' => false,
            'description' => 'Test 3',
            'price_before_tax' => 13,
            'currency_code' => 'TWD'
        ]
    ))->create();

    EcommerceProductAvailableDate::factory()->create([
        'product_id' => $products[2]->id,
        'available_date' => '2021-01-01'
    ]);

    EcommerceProductDeliveryDate::factory()->create([
        'product_id' => $products[2]->id,
        'delivery_date' => '2021-01-01'
    ]);

    $groups = EcommerceProductGroup::factory(2)->create();

    EcommerceProductGroupAssignment::factory(2)->state(new Sequence(
        [
            'product_group_id' => $groups[0]->id,
            'product_id' => $products[2]->id,
        ],
        [
            'product_group_id' => $groups[1]->id,
            'product_id' => $products[2]->id,
        ],
    ))->create();

    $product_sub_category = EcommerceProductSubCategory::factory()->create();

    EcommerceProductCategoryAssignment::factory()->create([
        'product_sub_category_id' => $product_sub_category->id,
        'product_id' => $products[2]->id,
    ]);

    $tags = EcommerceProductTag::factory(2)->create();

    EcommerceProductTagAssignment::factory(2)->state(new Sequence(
        [
            'product_id' => $products[2]->id,
            'tag_id' => $tags[0]->id,
        ],
        [
            'product_id' => $products[2]->id,
            'tag_id' => $tags[1]->id,
        ]
    ))->create();

    $filters = [
        'userable_type' => Employee::class,
        'userable_id' => $this->employee->id,
        'name' => 'wee',
        'available_date' => '2021-01-01',
        'delivery_date' => '2021-01-01',
        'merchant_type' => $merchants[1]->type->value,
        'is_active' => true,
        'category_id' => $product_sub_category->product_category_id,
        'sub_category_id' => $product_sub_category->id,
        'group_id' => $groups[1]->id,
        'tag_id' => $tags[1]->id,
    ];

    $this->mock(EcommerceProductService::class, function (MockInterface $mock) use ($filters, $products) {
        $product = $products[2];
        $product->load(["merchant", "availableDates", "deliveryDates", "groups", "subCategories", "tags"]);
        $mock->shouldReceive('getAllPaginatedEcommerceProducts')->once()->with(array_merge($filters, ['includes' => ['merchant', 'availableDates', 'deliveryDates', 'groups', 'subCategories', 'tags', 'media']]))->once()->andReturn(new LengthAwarePaginator([$product], 1, 1));
    });

    $response = $this->getJson(
        route($this->routeNamePrefix . '.index-by-merchant-type', $filters)
    )->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toHaveCount(1)
        ->and($response['data'][0])->toMatchArray([
            'name' => 'wee',
            'code' => 'wee',
            'merchant' => resourceToArray(new MerchantResource($merchants[1])),
            'is_active' => false,
            'description' => 'Test 3',
            'price_before_tax' => 13,
            'currency_code' => 'TWD',
            'available_dates' => [
                [
                    'date' => '2021-01-01',
                ]
            ],
            'delivery_dates' => [
                [
                    'date' => '2021-01-01',
                ]
            ],
            'groups' => resourceToArray(EcommerceProductGroupWithoutProductResource::collection($groups)),
            'sub_categories' => resourceToArray(EcommerceProductSubCategoryWithoutProductResource::collection([$product_sub_category])),
            'tags' => resourceToArray(EcommerceProductTagWithoutProductResource::collection($tags)),
        ]);
});

test('index: test service accepting params', function () {
    // Test if all values passes the validation and passes into service
    // Individual filters testing in EcommerceProductRepositoryTest
    $merchants = Merchant::factory(2)->create();
    $products = EcommerceProduct::factory(3)->state(new Sequence(
        [
            'name' => 'Test 1',
            'code' => 'test1',
            'merchant_id' => $merchants[0]->id,
            'is_active' => true,
            'description' => 'Test 1',
            'price_before_tax' => 10,
            'currency_code' => 'TWD'
        ],
        [
            'name' => 'Test 2',
            'code' => 'test2',
            'merchant_id' => $merchants[0]->id,
            'is_active' => true,
            'description' => 'Test 2',
            'price_before_tax' => 11,
            'currency_code' => 'TWD'
        ],
        [
            'name' => 'wee',
            'code' => 'wee',
            'merchant_id' => $merchants[1]->id,
            'is_active' => false,
            'description' => 'Test 3',
            'price_before_tax' => 13,
            'currency_code' => 'TWD'
        ]
    ))->create();

    EcommerceProductAvailableDate::factory()->create([
        'product_id' => $products[2]->id,
        'available_date' => '2021-01-01'
    ]);

    EcommerceProductDeliveryDate::factory()->create([
        'product_id' => $products[2]->id,
        'delivery_date' => '2021-01-01'
    ]);

    $groups = EcommerceProductGroup::factory(2)->create();

    EcommerceProductGroupAssignment::factory(2)->state(new Sequence(
        [
            'product_group_id' => $groups[0]->id,
            'product_id' => $products[2]->id,
        ],
        [
            'product_group_id' => $groups[1]->id,
            'product_id' => $products[2]->id,
        ],
    ))->create();

    $product_sub_category = EcommerceProductSubCategory::factory()->create();

    EcommerceProductCategoryAssignment::factory()->create([
        'product_sub_category_id' => $product_sub_category->id,
        'product_id' => $products[2]->id,
    ]);

    $tags = EcommerceProductTag::factory(2)->create();

    EcommerceProductTagAssignment::factory(2)->state(new Sequence(
        [
            'product_id' => $products[2]->id,
            'tag_id' => $tags[0]->id,
        ],
        [
            'product_id' => $products[2]->id,
            'tag_id' => $tags[1]->id,
        ]
    ))->create();

    $filters = [
        'userable_type' => Employee::class,
        'userable_id' => $this->employee->id,
        'name' => 'wee',
        'code' => strtoupper('wee'),
        'merchant_id' => $merchants[1]->id,
        'is_active' => false,
        'category_id' => $product_sub_category->product_category_id,
        'sub_category_id' => $product_sub_category->id,
        'group_id' => $groups[1]->id,
        'tag_id' => $tags[1]->id,
        'includes' => ['merchant', 'availableDates', 'deliveryDates', 'groups', 'subCategories', 'tags', 'media'],
    ];

    $this->mock(EcommerceProductService::class, function (MockInterface $mock) use ($filters, $products) {
        $mock->shouldReceive('setMerchantIdByPermission')->andReturn($filters);
        $mock->shouldReceive('getAllPaginatedEcommerceProducts')->with($filters)->once()->andReturn(new LengthAwarePaginator([
            $products[2]->loadMissing(['merchant', 'availableDates', 'deliveryDates', 'groups', 'subCategories', 'tags', 'media'])
        ], 1, 1));
    });

    $response = $this->getJson(
        route($this->routeNamePrefix . '.index', $filters)
    )->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toHaveCount(1)
        ->and($response['data'][0])->toMatchArray([
            'name' => 'wee',
            'code' => 'wee',
            'merchant' => resourceToArray(new MerchantResource($merchants[1])),
            'is_active' => false,
            'photo' => $products[2]->photo,
            'description' => 'Test 3',
            'price_before_tax' => 13,
            'currency_code' => 'TWD',
            'available_dates' => [
                [
                    'date' => '2021-01-01',
                ]
            ],
            'delivery_dates' => [
                [
                    'date' => '2021-01-01',
                ]
            ],
            'groups' => resourceToArray(EcommerceProductGroupWithoutProductResource::collection($groups)),
            'sub_categories' => resourceToArray(EcommerceProductSubCategoryWithoutProductResource::collection([$product_sub_category])),
            'tags' => resourceToArray(EcommerceProductTagWithoutProductResource::collection($tags)),
        ]);
});

test('index: test login as merchant user account', function () {
    // create merchant via merchant create api
    Currency::factory()->malaysiaCurrency()->create();

    $role = Role::firstOrCreate([
        'name' => 'Merchant',
        'guard_name' => 'api'
    ]);
    $role->syncPermissions("product-view");
    ModelHasDefaultRole::create(['role_id' => $role->id, 'model' => Merchant::class]);

    $payload = [
        'name' => [
            'en' => fake()->name,
            'zh' => fake('zh_CN')->name,
        ],
        'label' => fake()->name,
        'type' => MerchantType::CANTEEN->value,
        'is_active' => true,
        'email' => '<EMAIL>',
        'phone_number' => '+***********',
        'password' => 'testing',
        'password_confirmation' => 'testing',
        'with_user_account' => true,
    ];

    $response = $this->postJson(route("merchants.create"), $payload)->json();
    expect($response)->toHaveSuccessGeneralResponse();

    $merchant = Merchant::find($response['data']['id']);
    Sanctum::actingAs($merchant->user);

    $merchant2 = Merchant::factory()->create();

    $products = EcommerceProduct::factory(3)->state(new Sequence(
        [
            'name' => 'Test 1',
            'code' => 'test1',
            'merchant_id' => $merchant->id,
            'is_active' => true,
            'description' => 'Test 1',
            'price_before_tax' => 10,
            'currency_code' => 'TWD'
        ],
        [
            'name' => 'Test 2',
            'code' => 'test2',
            'merchant_id' => $merchant->id,
            'is_active' => true,
            'description' => 'Test 2',
            'price_before_tax' => 11,
            'currency_code' => 'TWD'
        ],
        [
            'name' => 'wee',
            'code' => 'wee',
            'merchant_id' => $merchant2->id,
            'is_active' => true,
            'description' => 'Test 3',
            'price_before_tax' => 13,
            'currency_code' => 'TWD'
        ]
    ))->create();

    EcommerceProductAvailableDate::factory()->create([
        'product_id' => $products[0]->id,
        'available_date' => '2021-01-01'
    ]);

    EcommerceProductDeliveryDate::factory()->create([
        'product_id' => $products[0]->id,
        'delivery_date' => '2021-01-01'
    ]);

    EcommerceProductAvailableDate::factory()->create([
        'product_id' => $products[1]->id,
        'available_date' => '2021-01-02'
    ]);

    EcommerceProductDeliveryDate::factory()->create([
        'product_id' => $products[1]->id,
        'delivery_date' => '2021-01-02'
    ]);

    $groups = EcommerceProductGroup::factory(2)->create();

    EcommerceProductGroupAssignment::factory(2)->state(new Sequence(
        [
            'product_group_id' => $groups[0]->id,
            'product_id' => $products[0]->id,
        ],
        [
            'product_group_id' => $groups[1]->id,
            'product_id' => $products[1]->id,
        ],
    ))->create();

    $product_sub_category = EcommerceProductSubCategory::factory()->create();

    EcommerceProductCategoryAssignment::factory(2)->state(new Sequence(
        [
            'product_sub_category_id' => $product_sub_category->id,
            'product_id' => $products[0]->id,
        ],
        [
            'product_sub_category_id' => $product_sub_category->id,
            'product_id' => $products[1]->id,
        ],
    ))->create();

    $tags = EcommerceProductTag::factory(2)->create();

    EcommerceProductTagAssignment::factory(2)->state(new Sequence(
        [
            'product_id' => $products[0]->id,
            'tag_id' => $tags[0]->id,
        ],
        [
            'product_id' => $products[1]->id,
            'tag_id' => $tags[1]->id,
        ]
    ))->create();


    $response = $this->getJson(
        route($this->routeNamePrefix . '.index', ['includes' => ["merchant", "availableDates", "deliveryDates", "groups", "subCategories"]])
    )->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toHaveCount(2)
        ->and($response['data'])->toMatchArray([
            [
                'id' => $products[0]->id,
                'name' => 'Test 1',
                'code' => 'test1',
                'merchant' => resourceToArray(new MerchantResource($merchant->unsetRelations())),
                'is_active' => true,
                'photo' => $products[0]->photo,
                'description' => 'Test 1',
                'price_before_tax' => 10,
                'currency_code' => 'TWD',
                'available_dates' => [
                    [
                        'date' => '2021-01-01',
                    ]
                ],
                'delivery_dates' => [
                    [
                        'date' => '2021-01-01',
                    ]
                ],
                'groups' => resourceToArray(EcommerceProductGroupWithoutProductResource::collection($products[0]->groups)),
                'sub_categories' => resourceToArray(EcommerceProductSubCategoryWithoutProductResource::collection($products[0]->subCategories)),
                'tags' => resourceToArray(EcommerceProductTagWithoutProductResource::collection($products[0]->tags)),
                'sequence' => 0,
            ],
            [
                'id' => $products[1]->id,
                'name' => 'Test 2',
                'code' => 'test2',
                'merchant' => resourceToArray(new MerchantResource($merchant->unsetRelations())),
                'is_active' => true,
                'photo' => $products[1]->photo,
                'description' => 'Test 2',
                'price_before_tax' => 11,
                'currency_code' => 'TWD',
                'available_dates' => [
                    [
                        'date' => '2021-01-02',
                    ]
                ],
                'delivery_dates' => [
                    [
                        'date' => '2021-01-02',
                    ]
                ],
                'groups' => resourceToArray(EcommerceProductGroupWithoutProductResource::collection($products[1]->groups)),
                'sub_categories' => resourceToArray(EcommerceProductSubCategoryWithoutProductResource::collection($products[1]->subCategories)),
                'tags' => resourceToArray(EcommerceProductTagWithoutProductResource::collection($products[1]->tags)),
                'sequence' => 0,
            ],
        ]);

    $merchant->user->givePermissionTo('view-all-merchant');

    // Re-authenticate
    Sanctum::actingAs($merchant->user);

    $response = $this->getJson(
        route($this->routeNamePrefix . '.index', [])
    )->json();

    // Can see all merchants' products
    expect($response['data'])->toHaveCount(3);
});

test('index determine getPaginated per_page is not -1', function () {

    $this->mock(EcommerceProductService::class, function (MockInterface $mock) {
        $product1 = EcommerceProduct::factory()->create();
        $product2 = EcommerceProduct::factory()->create();

        $mock->shouldReceive('getAllPaginatedEcommerceProducts')->once()->andReturn(new LengthAwarePaginator([$product1, $product2], 2, 1));
    });

    $response = $this->getJson(route($this->routeNamePrefix . '.index', [
        'per_page' => 10,
        'page' => 1,
    ]));

    $response->assertStatus(200);

    expect($response->json())->toHaveKey('pagination');

});


test('index determine getAll per_page is -1', function () {

    $input = [
        'per_page' => -1,
        'page' => 1,
    ];

    $this->mock(EcommerceProductService::class, function (MockInterface $mock) use ($input) {
        $product1 = EcommerceProduct::factory()->create();
        $product2 = EcommerceProduct::factory()->create();

        $mock->shouldReceive('getAllEcommerceProducts')->once()->andReturn(Collection::make([$product1, $product2]));
    });

    $response = $this->getJson(route($this->routeNamePrefix . '.index', $input));

    $response->assertStatus(200);

    expect($response->json())->not()->toHaveKey('pagination');
});

test('show', function () {
    $merchant = Merchant::factory()->create();
    $product = EcommerceProduct::factory()->create([
        'name' => 'wee',
        'code' => 'wee',
        'merchant_id' => $merchant->id,
        'is_active' => false,
        'description' => 'Test 3',
        'price_before_tax' => 13,
        'currency_code' => 'TWD'
    ]);

    //test with id exist
    $response = $this->getJson(route("$this->routeNamePrefix.show", ['product' => $product->id]))->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toMatchArray([
            'id' => $product->id,
            'name' => $product->name,
            'code' => $product->code,
            'merchant' => resourceToArray(new MerchantResource($merchant)),
            'is_active' => $product->is_active,
            'photo' => $product->photo,
            'description' => $product->description,
            'price_before_tax' => $product->price_before_tax,
            'currency_code' => $product->currency_code,
            'groups' => [],
            'available_dates' => [],
            'delivery_dates' => [],
            'sub_categories' => [],
            'tags' => []
        ]);

    //test with id not exist
    $response = $this->getJson(route("$this->routeNamePrefix.show", ['product' => 9999]))->json();

    expect($response)->toHaveModelResourceNotFoundResponse();
});

test('store', function () {
    $merchant = Merchant::factory()->create();
    //store success
    $this->assertDatabaseCount($this->table, 0);
    $currency = Currency::factory()->create([
        'code' => 'TWD',
    ]);
    $groups = EcommerceProductGroup::factory(2)->create();
    $tags = EcommerceProductTag::factory(2)->create();

    $sub_categories = EcommerceProductSubCategory::factory(2)->create();
    $payload = [
        'name' => 'wee',
        'code' => 'wee',
        'merchant_id' => $merchant->id,
        'merchant_type' => $merchant->type,
        'is_active' => false,
        'description' => 'Test 3',
        'price_before_tax' => 13,
        'tax_id' => $this->tax->id,
        'currency_code' => $currency->code,
        'available_dates' => ['2021-01-01', '2021-01-02', '2021-01-03'],
        'delivery_dates' => ['2021-02-01', '2021-02-02', '2021-02-03'],
        'product_group_ids' => $groups->pluck('id')->toArray(),
        'product_sub_category_ids' => $sub_categories->pluck('id')->toArray(),
        'product_tag_ids' => $tags->pluck('id')->toArray(),
        'photo' => UploadedFile::fake()->create('first_file.png', 500),
        'sequence' => 5,
    ];

    $this->assertDatabaseCount($this->table, 0);
    $this->assertDatabaseCount($this->product_available_date_table, 0);
    $this->assertDatabaseCount($this->product_delivery_date_table, 0);
    $this->assertDatabaseCount($this->product_group_assignment_table, 0);
    $this->assertDatabaseCount($this->product_category_assignment_table, 0);
    $this->assertDatabaseCount($this->product_tag_assignment_table, 0);

    $response = $this->postJson(route("$this->routeNamePrefix.create"), $payload)->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toMatchArray([
            'name' => $payload['name'],
            'code' => strtoupper($payload['code']),
            'is_active' => $payload['is_active'],
            'description' => $payload['description'],
            'price_before_tax' => $payload['price_before_tax'],
            'currency_code' => $payload['currency_code'],
            'photo' => $response['data']['photo'],
            'sequence' => 5,
        ]);

    //check photo
    expect($response['data']['photo'])->not->toBeNull();
    $this->assertDatabaseHas('media', [
        'model_type' => EcommerceProduct::class,
        'model_id' => $response['data']['id'],
        'file_name' => 'first_file.png',
        'collection_name' => 'photo'
    ]);

    $this->assertDatabaseCount($this->table, 1);

    $this->assertDatabaseHas($this->table, [
        'name' => $payload['name'],
        'code' => strtoupper($payload['code']),
        'merchant_id' => $merchant->id,
        'is_active' => $payload['is_active'],
        'description' => $payload['description'],
        'price_before_tax' => $payload['price_before_tax'],
        'currency_code' => $payload['currency_code'],
    ]);

    $this->assertDatabaseCount($this->product_available_date_table, 3);

    foreach ($payload['available_dates'] as $date) {
        $this->assertDatabaseHas($this->product_available_date_table, [
            'product_id' => $response['data']['id'],
            'available_date' => $date,
        ]);
    }

    $this->assertDatabaseCount($this->product_delivery_date_table, 3);

    foreach ($payload['delivery_dates'] as $date) {
        $this->assertDatabaseHas($this->product_delivery_date_table, [
            'product_id' => $response['data']['id'],
            'delivery_date' => $date,
        ]);
    }

    $this->assertDatabaseCount($this->product_group_assignment_table, 2);

    foreach ($payload['product_group_ids'] as $group_id) {
        $this->assertDatabaseHas($this->product_group_assignment_table, [
            'product_id' => $response['data']['id'],
            'product_group_id' => $group_id,
        ]);
    }

    $this->assertDatabaseCount($this->product_category_assignment_table, 2);

    foreach ($payload['product_sub_category_ids'] as $product_sub_category_id) {
        $this->assertDatabaseHas($this->product_category_assignment_table, [
            'product_id' => $response['data']['id'],
            'product_sub_category_id' => $product_sub_category_id,
        ]);
    }

    $this->assertDatabaseCount($this->product_tag_assignment_table, 2);

    foreach ($payload['product_tag_ids'] as $product_tag_id) {
        $this->assertDatabaseHas($this->product_tag_assignment_table, [
            'product_id' => $response['data']['id'],
            'tag_id' => $product_tag_id,
        ]);
    }
});

test('update', function () {
    $currency = Currency::factory()->create([
        'code' => 'TWD',
    ]);

    $merchants = Merchant::factory(2)->create([
        'type' => MerchantType::CANTEEN
    ]);

    $product = EcommerceProduct::factory()->create([
        'name' => 'wee',
        'code' => 'wee',
        'merchant_id' => $merchants[0]->id,
        'is_active' => false,
        'description' => 'Test 3',
        'price_before_tax' => 13,
        'currency_code' => 'TWD',
        'sequence' => 5,
    ]);

    $available_date = EcommerceProductAvailableDate::factory()->create([
        'product_id' => $product->id,
        'available_date' => '2022-01-01',
    ]);

    $delivery_date = EcommerceProductDeliveryDate::factory()->create([
        'product_id' => $product->id,
        'delivery_date' => '2022-01-01',
    ]);

    $groups = EcommerceProductGroup::factory(2)->create();

    EcommerceProductGroupAssignment::factory(2)->state(new Sequence(
        [
            'product_group_id' => $groups[0]->id,
            'product_id' => $product->id,
        ],
        [
            'product_group_id' => $groups[1]->id,
            'product_id' => $product->id,
        ],
    ))->create();

    $old_product_sub_categories = EcommerceProductSubCategory::factory(2)->create();
    EcommerceProductCategoryAssignment::factory(2)->state(new Sequence(
        [
            'product_id' => $product->id,
            'product_sub_category_id' => $old_product_sub_categories[0]->id,
        ],
        [
            'product_id' => $product->id,
            'product_sub_category_id' => $old_product_sub_categories[1]->id,
        ],
    ))->create();

    $old_product_tags = EcommerceProductTag::factory(2)->create();
    EcommerceProductTagAssignment::factory(2)->state(new Sequence(
        [
            'product_id' => $product->id,
            'tag_id' => $old_product_tags[0]->id,
        ],
        [
            'product_id' => $product->id,
            'tag_id' => $old_product_tags[1]->id,
        ],
    ))->create();

    //update with id exist
    $this->assertDatabaseCount($this->table, 1);
    $this->assertDatabaseCount($this->product_available_date_table, 1);
    $this->assertDatabaseCount($this->product_delivery_date_table, 1);
    $this->assertDatabaseCount($this->product_group_assignment_table, 2);
    $this->assertDatabaseCount($this->product_category_assignment_table, 2);
    $this->assertDatabaseCount($this->product_tag_assignment_table, 2);

    $updated_group = EcommerceProductGroup::factory()->create();
    $updated_product_sub_category = EcommerceProductSubCategory::factory()->create();
    $updated_product_tag = EcommerceProductTag::factory()->create();

    $payload = [
        'name' => 'wee',
        'code' => 'wee',
        'merchant_id' => $merchants[1]->id,
        'merchant_type' => $merchants[1]->type,
        'is_active' => false,
        'description' => 'Test 3',
        'price_before_tax' => 13,
        'tax_id' => $this->tax->id,
        'currency_code' => $currency->code,
        'available_dates' => ['2021-01-01', '2021-01-02', '2021-01-03'],
        'delivery_dates' => ['2021-01-01', '2021-01-02', '2021-01-03'],
        'product_group_ids' => [$updated_group->id],
        'product_sub_category_ids' => [$updated_product_sub_category->id],
        'product_tag_ids' => [$updated_product_tag->id],
        'photo' => UploadedFile::fake()->create('first_file.png', 500),
        'sequence' => 8,
    ];

    $response = $this->putJson(route("$this->routeNamePrefix.update", ['product' => $product->id]), $payload)->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toMatchArray([
            'name' => $payload['name'],
            'code' => strtoupper($payload['code']),
            'is_active' => $payload['is_active'],
            'description' => $payload['description'],
            'price_before_tax' => $payload['price_before_tax'],
            'currency_code' => $payload['currency_code'],
            'photo' => $response['data']['photo'],
            'sequence' => 8,
        ])
        ->and($response['data']['photo'])->not->toBeNull();

    //check media
    $this->assertDatabaseHas('media', [
        'model_type' => EcommerceProduct::class,
        'model_id' => $response['data']['id'],
        'file_name' => 'first_file.png',
        'collection_name' => 'photo'
    ]);

    $this->assertDatabaseCount($this->table, 1);
    $this->assertDatabaseHas($this->table, [
        'id' => $product->id,
        'name' => $payload['name'],
        'code' => strtoupper($payload['code']),
        'merchant_id' => $merchants[1]->id,
        'is_active' => $payload['is_active'],
        'description' => $payload['description'],
        'price_before_tax' => $payload['price_before_tax'],
        'currency_code' => $payload['currency_code'],
        'sequence' => 8,
    ]);

    $this->assertDatabaseCount($this->product_available_date_table, 3);

    foreach ($payload['available_dates'] as $date) {
        $this->assertDatabaseHas($this->product_available_date_table, [
            'product_id' => $response['data']['id'],
            'available_date' => $date,
        ]);
    }

    $this->assertDatabaseMissing($this->product_available_date_table, [
        'id' => $available_date->id,
    ]);

    $this->assertDatabaseCount($this->product_delivery_date_table, 3);

    foreach ($payload['delivery_dates'] as $date) {
        $this->assertDatabaseHas($this->product_delivery_date_table, [
            'product_id' => $response['data']['id'],
            'delivery_date' => $date,
        ]);
    }

    $this->assertDatabaseMissing($this->product_delivery_date_table, [
        'id' => $delivery_date->id,
    ]);

    $this->assertDatabaseCount($this->product_group_assignment_table, 1);

    foreach ($payload['product_group_ids'] as $group_id) {
        $this->assertDatabaseHas($this->product_group_assignment_table, [
            'product_id' => $response['data']['id'],
            'product_group_id' => $group_id,
        ]);
    }

    foreach ($groups as $group) {
        $this->assertDatabaseMissing($this->product_group_assignment_table, [
            'product_id' => $response['data']['id'],
            'product_group_id' => $group->id,
        ]);
    }

    $this->assertDatabaseCount($this->product_category_assignment_table, 1);

    foreach ($payload['product_sub_category_ids'] as $product_sub_category_id) {
        $this->assertDatabaseHas($this->product_category_assignment_table, [
            'product_id' => $response['data']['id'],
            'product_sub_category_id' => $product_sub_category_id,
        ]);
    }

    foreach ($old_product_sub_categories as $product_sub_category) {
        $this->assertDatabaseMissing($this->product_category_assignment_table, [
            'product_id' => $response['data']['id'],
            'product_sub_category_id' => $product_sub_category->id,
        ]);
    }

    $this->assertDatabaseCount($this->product_tag_assignment_table, 1);

    foreach ($payload['product_tag_ids'] as $product_tag_id) {
        $this->assertDatabaseHas($this->product_tag_assignment_table, [
            'product_id' => $response['data']['id'],
            'tag_id' => $product_tag_id,
        ]);
    }

    foreach ($old_product_tags as $product_tag) {
        $this->assertDatabaseMissing($this->product_tag_assignment_table, [
            'product_id' => $response['data']['id'],
            'tag_id' => $product_tag->id,
        ]);
    }

    //update with id not exist
    $payload = [
        'name' => 'wee2',
        'code' => 'wee2',
        'merchant_id' => $merchants[1]->id,
        'merchant_type' => $merchants[1]->type,
        'is_active' => false,
        'description' => 'Test 3',
        'price_before_tax' => 13,
        'currency_code' => $currency->code,
        'available_dates' => ['2021-01-01', '2021-01-02', '2021-01-03'],
        'delivery_dates' => ['2021-01-01', '2021-01-02', '2021-01-03'],
        'product_group_ids' => [$updated_group->id],
    ];

    $response = $this->json('PUT', route("$this->routeNamePrefix.update", ['product' => 9999]), $payload)->json();

    expect($response)->toHaveModelResourceNotFoundResponse();

    // Assert nothing updated
    $this->assertDatabaseCount($this->table, 1);
    $this->assertDatabaseHas($this->table, [
        'id' => $product->id,
        'name' => 'wee',
        'code' => 'WEE',
    ]);
});

test('destroy', function () {
    Currency::factory()->create();
    $product = EcommerceProduct::factory()->create();
    EcommerceProduct::factory(3)->create();

    $this->assertDatabaseCount($this->table, 4);

    //id not exist
    $response = $this->json('DELETE', route("$this->routeNamePrefix.destroy", ['product' => 9999]))->json();
    expect($response)->toHaveModelResourceNotFoundResponse();

    $this->assertDatabaseCount($this->table, 4);

    //delete success
    $response = $this->json('DELETE', route("$this->routeNamePrefix.destroy", ['product' => $product->id]))->json();
    expect($response)->toHaveSuccessGeneralResponse();

    $this->assertDatabaseMissing($this->table, ['id' => $product->id]);
});
