<?php

use App\Enums\SubjectType;
use App\Http\Resources\ClubResource;
use App\Models\Club;
use App\Models\Subject;
use App\Models\User;
use App\Services\SubjectService;
use Database\Seeders\InternationalizationSeeder;
use Database\Seeders\PermissionSeeder;
use Illuminate\Pagination\LengthAwarePaginator;
use Laravel\Sanctum\Sanctum;
use Mockery\MockInterface;

beforeEach(function () {
    $this->seed([
        InternationalizationSeeder::class,
        PermissionSeeder::class
    ]);

    $this->user = User::factory()->create();

    $this->user->assignRole('Super Admin');

    Sanctum::actingAs($this->user);

    $this->baseUrl = 'subjects.';

    $this->tableName = resolve(Subject::class)->getTable();
});

test('index without any params', function () {
    $club = Club::factory()->create();

    $first = Subject::factory()->create(['club_id' => $club->id]);
    $second = Subject::factory()->create();

    $response = $this->getJson(route($this->baseUrl . 'index'));

    $response->assertStatus(200);

    expect($response->json())->toHaveSuccessGeneralResponse()
        ->and($response->json()['data'])
        ->toHaveCount(2)
        ->toEqual([
            [
                'id' => $first->id,
                'type' => $first->type,
                'club' => resourceToArray(new ClubResource($club)),
                'code' => $first->code,
                'name' => $first->name,
                'translations' => $first->translations,
            ],
            [
                'id' => $second->id,
                'type' => $second->type,
                'club' => null,
                'code' => $second->code,
                'name' => $second->name,
                'translations' => $second->translations,
            ],
        ]);
});

test('index : test service accepting params', function () {
    $club = Club::factory()->create();
    $subject = Subject::factory()->create(['club_id' => $club->id]);

    $payload = [
        'types' => [$subject->type],
        'club_id' => $club->id,
        'code' => $subject->code,
        'name' => $subject->name,
        'order_by' => [
            'id' => 'asc'
        ],
    ];

    $payload2 = $payload;
    $payload2['code'] = strtoupper($payload2['code']);

    $this->mock(SubjectService::class)
        ->shouldReceive('getAllPaginatedSubjects')
        ->once()
        ->with($payload2)
        ->andReturn(new LengthAwarePaginator([$subject], 1, 1));

    $response = $this->getJson(route($this->baseUrl . 'index', $payload));

    $response->assertStatus(200);

    expect($response->json())->toHaveSuccessGeneralResponse()
        ->and($response->json()['data'])->toHaveCount(1)
        ->toEqual([
            [
                'id' => $subject->id,
                'type' => $subject->type,
                'club' => resourceToArray(new ClubResource($club)),
                'code' => $subject->code,
                'name' => $subject->name,
                'translations' => $subject->translations,
            ]
        ]);
});

test('index - getAll', function () {
    $club = Club::factory()->create();

    $first = Subject::factory()->create(['club_id' => $club->id]);
    $second = Subject::factory()->create();

    $response = $this->getJson(route($this->baseUrl . 'index', ['per_page' => -1]));

    $response->assertStatus(200);

    expect($response->json())->toHaveSuccessGeneralResponse()
        ->and($response->json()['data'])
        ->toHaveCount(2)
        ->toEqual([
            [
                'id' => $first->id,
                'type' => $first->type,
                'club' => resourceToArray(new ClubResource($club)),
                'code' => $first->code,
                'name' => $first->name,
                'translations' => $first->translations,
            ],
            [
                'id' => $second->id,
                'type' => $second->type,
                'club' => null,
                'code' => $second->code,
                'name' => $second->name,
                'translations' => $second->translations,
            ],
        ]);
});

test('index determine getPaginated per_page is not -1', function () {
    $this->mock(SubjectService::class, function (MockInterface $mock) {
        $subject = Subject::factory()->create();

        $mock->shouldReceive('getAllPaginatedSubjects')
            ->once()
            ->andReturn(new LengthAwarePaginator([$subject], 1, 1));
    });

    $response = $this->getJson(route($this->baseUrl . 'index', [
        'per_page' => 10,
        'page' => 1,
    ]));

    $response->assertStatus(200);

    expect($response->json())->toHaveKey('pagination');
});


test('index determine getAll per_page is -1', function () {

    $this->mock(SubjectService::class, function (MockInterface $mock) {
        $subjects = Subject::factory(2)->create();

        $mock->shouldReceive('getAllSubjects')->once()->andReturn($subjects);
    });

    $response = $this->getJson(route($this->baseUrl . 'index', [
        'per_page' => -1,
        'page' => 1,
    ]));

    $response->assertStatus(200);

    expect($response->json())->not()->toHaveKey('pagination');
});

test('create success', function () {
    // type == COCURRICULUM
    $this->assertDatabaseCount($this->tableName, 0);

    $club = Club::factory()->create();

    $payload = [
        'type' => SubjectType::COCURRICULUM->value,
        'club_id' => $club->id,
        'code' => '001a',
        'name' => [
            'en' => 'new English',
            'zh' => 'zh new English',
        ],
    ];

    $response = $this->postJson(route($this->baseUrl . 'create'), $payload);

    $response->assertStatus(200);

    // Expect only one record is created
    $this->assertDatabaseCount($this->tableName, 1);

    $cocu_subject = Subject::first();

    expect($response->json())->toHaveSuccessGeneralResponse()
        ->and($response->json()['data'])->toEqual([
            'id' => $cocu_subject->id,
            'type' => $cocu_subject->type,
            'club' => resourceToArray(new ClubResource($cocu_subject->club)),
            'code' => strtoupper($cocu_subject->code),
            'name' => $cocu_subject->name,
            'translations' => $cocu_subject->translations,
        ]);

    // type == MAJOR, no need club_id
    $this->assertDatabaseCount($this->tableName, 1);

    $payload = [
        'type' => SubjectType::MAJOR->value,
        'club_id' => null,
        'code' => '002',
        'name' => [
            'en' => 'new Math',
            'zh' => 'zh new Math',
        ],
    ];

    $response = $this->postJson(route($this->baseUrl . 'create'), $payload);

    $response->assertStatus(200);

    $this->assertDatabaseCount($this->tableName, 2);

    $major_subject = Subject::where('type', SubjectType::MAJOR->value)->first();

    expect($response->json())->toHaveSuccessGeneralResponse()
        ->and($response->json()['data'])->toEqual([
            'id' => $major_subject->id,
            'type' => $major_subject->type,
            'club' => null,
            'code' => $major_subject->code,
            'name' => $major_subject->name,
            'translations' => $major_subject->translations,
        ]);
});

test('create validation error: no data, club_id null when type is COCURRICULUM', function () {
    $this->assertDatabaseCount($this->tableName, 0);

    $response = $this->postJson(route($this->baseUrl . 'create'), []);

    $response->assertStatus(422);

    // Expect nothing is created and return validation error
    $this->assertDatabaseCount($this->tableName, 0);

    expect($response->json())->toHaveFailedGeneralResponse()
        ->toMatchArray([
            'error' => [
                'type' => [
                    'The type field is required.'
                ],
                'code' => [
                    'The code field is required.'
                ],
                'name' => [
                    'The name field is required.'
                ],
            ],
            'data' => null
        ]);

    $payload = [
        'type' => SubjectType::COCURRICULUM->value,
        'club_id' => null,
        'code' => '002',
        'name' => [
            'en' => 'new Math',
            'zh' => 'zh new Math',
        ],
    ];

    $response = $this->postJson(route($this->baseUrl . 'create'), $payload);

    // Expect nothing is created and return validation error
    $this->assertDatabaseCount($this->tableName, 0);

    expect($response->json())->toHaveFailedGeneralResponse()
        ->toMatchArray([
            'error' => [
                'club_id' => [
                    'The club id field is required when type is COCURRICULUM.'
                ],
            ],
            'data' => null
        ]);
});

test('update success', function () {
    $subject = Subject::factory()->create();

    $this->assertDatabaseCount($this->tableName, 1);

    // update MAJOR to COCURRICULUM
    $club = Club::factory()->create();

    $payload = [
        'type' => SubjectType::COCURRICULUM->value,
        'club_id' => $club->id,
        'code' => '001b',
        'name' => [
            'en' => 'updated Eng',
            'zh' => 'zh updated Eng',
        ],
    ];

    $response = $this->putJson(route($this->baseUrl . 'update', $subject->id), $payload);

    $response->assertStatus(200);

    $this->assertDatabaseCount($this->tableName, 1);

    $subject->refresh();

    expect($response->json())->toHaveSuccessGeneralResponse()
        ->and($response->json()['data'])
        ->toEqual([
            'id' => $subject->id,
            'type' => $subject->type,
            'club' => resourceToArray(new ClubResource($subject->club)),
            'code' => $subject->code,
            'name' => $subject->name,
            'translations' => $subject->translations,
        ]);

    $this->assertDatabaseHas($this->tableName, [
        'id' => $subject->id,
        'type' => $payload['type'],
        'club_id' => $payload['club_id'],
        'code' => strtoupper($payload['code']),
        'name->en' => $payload['name']['en'],
        'name->zh' => $payload['name']['zh'],
    ]);

    // update to MAJOR, no need club_id
    $major_subject = Subject::factory()->create();

    $payload = [
        'type' => SubjectType::MAJOR->value,
        'club_id' => null,
        'code' => '002',
        'name' => [
            'en' => 'updated Math',
            'zh' => 'zh updated Math',
        ],
    ];

    $response = $this->putJson(route($this->baseUrl . 'update', $major_subject->id), $payload);

    $response->assertStatus(200);

    $this->assertDatabaseCount($this->tableName, 2);

    $major_subject->refresh();

    expect($response->json())->toHaveSuccessGeneralResponse()
        ->and($response->json()['data'])
        ->toEqual([
            'id' => $major_subject->id,
            'type' => $major_subject->type,
            'club' => null,
            'code' => $major_subject->code,
            'name' => $major_subject->name,
            'translations' => $major_subject->translations,
        ]);

    $this->assertDatabaseHas($this->tableName, [
        'id' => $major_subject->id,
        'type' => $payload['type'],
        'club_id' => null,
        'code' => $payload['code'],
        'name->en' => $payload['name']['en'],
        'name->zh' => $payload['name']['zh'],
    ]);
});

test('update validation error', function () {
    $subject = Subject::factory()->create();

    $this->assertDatabaseCount($this->tableName, 1);

    $response = $this->putJson(route($this->baseUrl . 'update', $subject->id), []);

    $response->assertStatus(422);

    expect($response->json())->toMatchArray([
        'error' => [
            'type' => [
                'The type field is required.'
            ],
            'code' => [
                'The code field is required.'
            ],
            'name' => [
                'The name field is required.'
            ],
        ],
        'data' => null,
    ]);

    $this->assertDatabaseHas($this->tableName, [
        'id' => $subject->id,
        'type' => $subject->type,
        'club_id' => $subject->club_id,
        'code' => $subject->code,
        'name->en' => $subject->getTranslation('name', 'en'),
        'name->zh' => $subject->getTranslation('name', 'zh'),
    ]);

    // club id null when type is COCURRICULUM
    $payload = [
        'type' => SubjectType::COCURRICULUM->value,
        'club_id' => null,
        'code' => '002',
        'name' => [
            'en' => 'new Math',
            'zh' => 'zh new Math',
        ],
    ];

    $response = $this->putJson(route($this->baseUrl . 'update', $subject->id), $payload);

    $response->assertStatus(422);

    expect($response->json())->toHaveFailedGeneralResponse()
        ->toMatchArray([
            'error' => [
                'club_id' => [
                    'The club id field is required when type is COCURRICULUM.'
                ],
            ],
            'data' => null
        ]);

    $this->assertDatabaseMissing($this->tableName, [
        'type' => 'COCURRICULUM',
    ]);
});

test('show single record success', function () {
    $first = Subject::factory()->club()->create();
    $second = Subject::factory()->create();

    $response = $this->getJson(route($this->baseUrl . 'show', $first->id));

    $response->assertStatus(200);

    expect($response->json())->toHaveSuccessGeneralResponse()
        ->and($response->json()['data'])
        ->toEqual([
            'id' => $first->id,
            'type' => $first->type,
            'club' => resourceToArray(new ClubResource($first->club)),
            'code' => $first->code,
            'name' => $first->name,
            'translations' => $first->translations,
            'semester_class_ids' => [],
        ]);

    $response = $this->getJson(route($this->baseUrl . 'show', $second->id));

    $response->assertStatus(200);

    expect($response->json())->toHaveSuccessGeneralResponse()
        ->and($response->json()['data'])
        ->toEqual([
            'id' => $second->id,
            'type' => $second->type,
            'club' => null,
            'code' => $second->code,
            'name' => $second->name,
            'translations' => $second->translations,
            'semester_class_ids' => [],
        ]);
});

test('show not existing record error', function () {
    $this->assertDatabaseCount($this->tableName, 0);

    $response = $this->getJson(route($this->baseUrl . 'show', 1));

    $response->assertStatus(404);

    expect($response->json())->toHaveModelResourceNotFoundResponse();
});

test('delete success', function () {
    $first = Subject::factory()->create();
    $other_subjects = Subject::factory()->count(3)->create();

    $this->assertDatabaseCount($this->tableName, 4);

    //delete success
    $response = $this->deleteJson(route($this->baseUrl . 'destroy', ['subject' => $first->id]))->json();

    expect($response)->toHaveSuccessGeneralResponse();

    $this->assertDatabaseCount($this->tableName, 3);

    $this->assertDatabaseMissing($this->tableName, ['id' => $first->id]);

    foreach ($other_subjects as $other) {
        $this->assertDatabaseHas($this->tableName, ['id' => $other->id]);
    }
});
