<?php

use App\Enums\ClassType;
use App\Enums\ExportType;
use App\Enums\ProductCategory;
use App\Events\InvoicePaidEvent;
use App\Exports\GenericExcelExportViaView;
use App\Factories\ExportAdapterFactory;
use App\Http\Resources\BillingDocumentLineItemResource;
use App\Http\Resources\ProductResource;
use App\Http\Resources\SimpleEmployeeResource;
use App\Http\Resources\SimpleGuardianResource;
use App\Http\Resources\SimpleStudentResource;
use App\Interfaces\Userable;
use App\Listeners\MarkBillableItemAsPaid;
use App\Models\BillingDocument;
use App\Models\BillingDocumentAdvanceTransaction;
use App\Models\BillingDocumentLineItem;
use App\Models\ClassModel;
use App\Models\Currency;
use App\Models\DiscountSetting;
use App\Models\Employee;
use App\Models\GlAccount;
use App\Models\LegalEntity;
use App\Models\PaymentMethod;
use App\Models\PaymentRequest;
use App\Models\PaymentTerm;
use App\Models\Product;
use App\Models\RecurringSetting;
use App\Models\Scholarship;
use App\Models\ScholarshipAward;
use App\Models\SemesterClass;
use App\Models\SemesterSetting;
use App\Models\Student;
use App\Models\StudentClass;
use App\Models\Tax;
use App\Models\UnpaidItem;
use App\Models\UnpaidItemAssignment;
use App\Models\UnpaidItemAssignmentStudent;
use App\Models\User;
use App\Services\Billing\AccountingService;
use App\Services\Billing\BillingDocumentPostingService;
use App\Services\Billing\BillingDocumentService;
use App\Services\Billing\PaymentRequestService;
use App\Services\Billing\PaymentService;
use App\Services\Billing\UnpaidItemAssignmentService;
use App\Services\Billing\UnpaidItemService;
use App\Services\DocumentPrintService;
use App\Services\ReportPrintService;
use Barryvdh\Snappy\Facades\SnappyPdf;
use Carbon\Carbon;
use Database\Seeders\CurrencySeeder;
use Database\Seeders\EmployeeSeeder;
use Database\Seeders\GlAccountSeeder;
use Database\Seeders\InternationalizationSeeder;
use Database\Seeders\LegalEntitySeeder;
use Database\Seeders\PaymentMethodSeeder;
use Database\Seeders\PaymentTermsSeeder;
use Database\Seeders\PermissionSeeder;
use Database\Seeders\TaxSeeder;
use Database\Seeders\UomSeeder;
use Illuminate\Database\Eloquent\Factories\Sequence;
use Illuminate\Support\Facades\Hash;
use Laravel\Sanctum\Sanctum;
use Mockery\MockInterface;

beforeEach(function () {
    $this->seed([
        CurrencySeeder::class,
        TaxSeeder::class,
        EmployeeSeeder::class,
        UomSeeder::class,
        GlAccountSeeder::class,
        PaymentMethodSeeder::class,
        PaymentTermsSeeder::class,
        LegalEntitySeeder::class,
        InternationalizationSeeder::class,
        PermissionSeeder::class,
    ]);

    app()->setLocale('en');

    $this->withHeaders([
        'Accept-Language' => 'en'
    ]);

    $this->user = User::factory()->create([
        'password' => Hash::make('123456'),
    ]);

    $this->user->assignRole('Super Admin');

    Sanctum::actingAs($this->user);

    $this->routeNamePrefix = 'admin.accounting';

    $this->currencyCode = Currency::first()->code;

    $this->legalEntity = LegalEntity::factory()->create();

    $this->tax = Tax::factory()->create([
        'percentage' => 0,
        'name' => 'Tax Exempt',
    ]);

    $this->paymentTerm = PaymentTerm::factory()->create([
        'due_date_days' => 10,
    ]);

    $this->reportPrintService = app(ReportPrintService::class);
});

test('create and assign fees to students success - non recurring fee for multiple students', function () {
    $this->assertDatabaseCount(UnpaidItemAssignment::class, 0);
    $this->assertDatabaseCount(UnpaidItemAssignmentStudent::class, 0);

    $students = Student::factory(2)->state(new Sequence(
        [
            'name->en' => 'David',
        ],
        [
            'name->en' => 'Simon'
        ]
    ))->create();

    $product = Product::factory()->create([
        'name->en' => 'School Fees',
        'category' => ProductCategory::SCHOOL_FEES->value,
    ]);

    $payload = [
        'student_ids' => [$students[0]->id, $students[1]->id],
        'fees' => [
            [
                'name' => 'School Fees 2025',
                'product_id' => $product->id,
                'currency_code' => $this->currencyCode,
                'unit_price' => '500.50',
                'quantity' => 1,
                'apply_at' => '2025-01-01 09:30:00', // 9.30 am, 1st January
                'recurring_settings' => [
                    [
                        'billing_date' => '2025-01-01',
                        'description' => 'School Fees 2025',
                    ],
                ],
            ],
        ]
    ];

    $this->mock(UnpaidItemAssignmentService::class, function (MockInterface $mock) use ($payload, $product, $students) {
        $mock->shouldReceive('setName')->once()->with($payload['fees'][0]['name'])->andReturnSelf();
        $mock->shouldReceive('setProduct')->once()->withArgs(function ($value) use (&$product) {
            return $value->id === $product->id;
        })->andReturnSelf();
        $mock->shouldReceive('setQuantity')->once()->with($payload['fees'][0]['quantity'])->andReturnSelf();
        $mock->shouldReceive('setUnitPrice')->once()->with($payload['fees'][0]['unit_price'])->andReturnSelf();
        $mock->shouldReceive('setCurrency')->once()->with($payload['fees'][0]['currency_code'])->andReturnSelf();
        $mock->shouldReceive('calculateAmountBeforeTax')->once()->andReturnSelf();
        $mock->shouldReceive('setApplyAt')->once()->withArgs(function ($value) use (&$payload) {
            return $value->eq(\Carbon\Carbon::parse($payload['fees'][0]['apply_at']));
        })->andReturnSelf();
        $mock->shouldReceive('setStatus')->once()->with(UnpaidItemAssignment::STATUS_NEW)->andReturnSelf();
        $mock->shouldReceive('addRecurringSetting')->once()->withArgs(function ($value) use (&$payload) {
            $setting = RecurringSetting::create(
                $payload['fees'][0]['recurring_settings'][0]['billing_date'],
                $payload['fees'][0]['recurring_settings'][0]['description']
            );
            return $value == $setting;
        })->andReturnSelf();
        $mock->shouldReceive('create')->once()->andReturnSelf();
        $mock->shouldReceive('assignToStudents')->once()->withArgs(function ($value) use (&$students) {
            return $value->pluck('id')->contains($students[0]->id) && $value->pluck('id')->contains($students[1]->id);
        })->andReturnSelf();
    });

    $response = $this->postJson(route($this->routeNamePrefix . '.fees.create-and-assign'), $payload)->json();

    expect($response)->toHaveSuccessGeneralResponse();
});

test('create and assign fees to students success - recurring fee for multiple students', function () {
    $this->assertDatabaseCount(UnpaidItemAssignment::class, 0);
    $this->assertDatabaseCount(UnpaidItemAssignmentStudent::class, 0);

    $students = Student::factory(2)->state(new Sequence(
        [
            'name->en' => 'David',
        ],
        [
            'name->en' => 'Simon'
        ]
    ))->create();

    $product = Product::factory()->create([
        'name->en' => 'School Fees Reccuring',
        'category' => ProductCategory::SCHOOL_FEES->value,
    ]);

    $payload = [
        'student_ids' => [$students[0]->id, $students[1]->id],
        'fees' => [
            [
                'name' => 'School Fees (3 Months) 2025',
                'product_id' => $product->id,
                'currency_code' => $this->currencyCode,
                'unit_price' => '500.50',
                'quantity' => 1,
                'apply_at' => '2025-01-01 09:30:00', // 9.30 am, 1st January
                'recurring_settings' => [
                    [
                        'billing_date' => '2025-01-01',
                        'description' => 'School Fees 2025 Jan',
                    ],
                    [
                        'billing_date' => '2025-02-01',
                        'description' => 'School Fees 2025 Feb',
                    ],
                    [
                        'billing_date' => '2025-03-01',
                        'description' => 'School Fees 2025 March',
                    ],
                ],
            ],
        ]
    ];

    $this->mock(UnpaidItemAssignmentService::class, function (MockInterface $mock) use ($payload, $product, $students) {
        $mock->shouldReceive('setName')->once()->with($payload['fees'][0]['name'])->andReturnSelf();
        $mock->shouldReceive('setProduct')->once()->withArgs(function ($value) use (&$product) {
            return $value->id === $product->id;
        })->andReturnSelf();
        $mock->shouldReceive('setQuantity')->once()->with($payload['fees'][0]['quantity'])->andReturnSelf();
        $mock->shouldReceive('setUnitPrice')->once()->with($payload['fees'][0]['unit_price'])->andReturnSelf();
        $mock->shouldReceive('setCurrency')->once()->with($payload['fees'][0]['currency_code'])->andReturnSelf();
        $mock->shouldReceive('calculateAmountBeforeTax')->once()->andReturnSelf();
        $mock->shouldReceive('setApplyAt')->once()->withArgs(function ($value) use (&$payload) {
            return $value->eq(\Carbon\Carbon::parse($payload['fees'][0]['apply_at']));
        })->andReturnSelf();
        $mock->shouldReceive('setStatus')->once()->with(UnpaidItemAssignment::STATUS_NEW)->andReturnSelf();
        $mock->shouldReceive('addRecurringSetting')->times(3)->andReturnSelf();
        $mock->shouldReceive('create')->once()->andReturnSelf();
        $mock->shouldReceive('assignToStudents')->once()->withArgs(function ($value) use (&$students) {
            return $value->pluck('id')->contains($students[0]->id) && $value->pluck('id')->contains($students[1]->id);
        })->andReturnSelf();
    });

    $response = $this->postJson(route($this->routeNamePrefix . '.fees.create-and-assign'), $payload)->json();

    expect($response)->toHaveSuccessGeneralResponse();
});

test('create and assign fees to students success - multiple fees with multiple recurring fee for multiple students', function () {
    $this->assertDatabaseCount(UnpaidItemAssignment::class, 0);
    $this->assertDatabaseCount(UnpaidItemAssignmentStudent::class, 0);

    $students = Student::factory(2)->state(new Sequence(
        [
            'name->en' => 'David',
        ],
        [
            'name->en' => 'Simon'
        ]
    ))->create();

    $product1 = Product::factory()->create([
        'name->en' => 'School Fees Reccuring',
        'category' => ProductCategory::SCHOOL_FEES->value,
    ]);

    $product2 = Product::factory()->create([
        'name->en' => 'Hostel Fees Reccuring',
        'category' => ProductCategory::HOSTEL_FEES->value,
    ]);

    $payload = [
        'student_ids' => [$students[0]->id, $students[1]->id],
        'fees' => [
            [
                'name' => 'School Fees (2 Months) 2025',
                'product_id' => $product1->id,
                'currency_code' => $this->currencyCode,
                'unit_price' => '500.50',
                'quantity' => 1,
                'apply_at' => '2025-01-01 09:30:00', // 9.30 am, 1st January
                'recurring_settings' => [
                    [
                        'billing_date' => '2025-01-01',
                        'description' => 'School Fees 2025 Jan',
                    ],
                    [
                        'billing_date' => '2025-02-01',
                        'description' => 'School Fees 2025 Feb',
                    ],
                ],
            ],
            [
                'name' => 'Hostel Fees (2 Months) 2025',
                'product_id' => $product2->id,
                'currency_code' => $this->currencyCode,
                'unit_price' => '501.50',
                'quantity' => 1,
                'apply_at' => '2025-01-01 09:30:00', // 9.30 am, 1st January
                'recurring_settings' => [
                    [
                        'billing_date' => '2025-01-01',
                        'description' => 'Hostel Fees 2025 Jan',
                    ],
                    [
                        'billing_date' => '2025-02-01',
                        'description' => 'Hostel Fees 2025 Feb',
                    ],
                ],
            ],
        ]
    ];

    $response = $this->postJson(route($this->routeNamePrefix . '.fees.create-and-assign'), $payload)->json();

    expect($response)->toHaveSuccessGeneralResponse();

    $this->assertDatabaseCount(UnpaidItemAssignment::class, 2);
    $this->assertDatabaseCount(UnpaidItemAssignmentStudent::class, 4);

    // School Fees
    $this->assertDatabaseHas(UnpaidItemAssignment::class, [
        'name' => $payload['fees'][0]['name'],
        'product_id' => $payload['fees'][0]['product_id'],
        'status' => UnpaidItemAssignment::STATUS_NEW,
        'currency_code' => $payload['fees'][0]['currency_code'],
        'unit_price' => $payload['fees'][0]['unit_price'],
        'quantity' => $payload['fees'][0]['quantity'],
        'amount_before_tax' => $payload['fees'][0]['unit_price'],
        'apply_at' => $payload['fees'][0]['apply_at'],
        'applied_at' => null,
    ]);

    $school_fees_unpaid_item_assignment = UnpaidItemAssignment::where('name', $payload['fees'][0]['name'])->first();

    expect($school_fees_unpaid_item_assignment->recurringSettings())->toHaveCount(2)
        ->and($school_fees_unpaid_item_assignment->recurringSettings()[0]->getBillingDate())
        ->toBe($payload['fees'][0]['recurring_settings'][0]['billing_date'])
        ->and($school_fees_unpaid_item_assignment->recurringSettings()[0]->getDescription())
        ->toBe($payload['fees'][0]['recurring_settings'][0]['description'])
        ->and($school_fees_unpaid_item_assignment->recurringSettings()[1]->getBillingDate())
        ->toBe($payload['fees'][0]['recurring_settings'][1]['billing_date'])
        ->and($school_fees_unpaid_item_assignment->recurringSettings()[1]->getDescription())
        ->toBe($payload['fees'][0]['recurring_settings'][1]['description']);

    $this->assertDatabaseHas(UnpaidItemAssignmentStudent::class, [
        'unpaid_item_assignment_id' => $school_fees_unpaid_item_assignment->id,
        'student_id' => $students[0]->id,
        'status' => UnpaidItemAssignmentStudent::STATUS_NEW,
        'applied_at' => null,
    ]);

    $this->assertDatabaseHas(UnpaidItemAssignmentStudent::class, [
        'unpaid_item_assignment_id' => $school_fees_unpaid_item_assignment->id,
        'student_id' => $students[1]->id,
        'status' => UnpaidItemAssignmentStudent::STATUS_NEW,
        'applied_at' => null,
    ]);


    // Hostel Fees
    $this->assertDatabaseHas(UnpaidItemAssignment::class, [
        'name' => $payload['fees'][1]['name'],
        'product_id' => $payload['fees'][1]['product_id'],
        'status' => UnpaidItemAssignment::STATUS_NEW,
        'currency_code' => $payload['fees'][1]['currency_code'],
        'unit_price' => $payload['fees'][1]['unit_price'],
        'quantity' => $payload['fees'][1]['quantity'],
        'amount_before_tax' => $payload['fees'][1]['unit_price'],
        'apply_at' => $payload['fees'][1]['apply_at'],
        'applied_at' => null,
    ]);

    $hostel_fees_unpaid_item_assignment = UnpaidItemAssignment::where('name', $payload['fees'][1]['name'])->first();

    expect($hostel_fees_unpaid_item_assignment->recurringSettings())->toHaveCount(2)
        ->and($hostel_fees_unpaid_item_assignment->recurringSettings()[0]->getBillingDate())
        ->toBe($payload['fees'][1]['recurring_settings'][0]['billing_date'])
        ->and($hostel_fees_unpaid_item_assignment->recurringSettings()[0]->getDescription())
        ->toBe($payload['fees'][1]['recurring_settings'][0]['description'])
        ->and($hostel_fees_unpaid_item_assignment->recurringSettings()[1]->getBillingDate())
        ->toBe($payload['fees'][1]['recurring_settings'][1]['billing_date'])
        ->and($hostel_fees_unpaid_item_assignment->recurringSettings()[1]->getDescription())
        ->toBe($payload['fees'][1]['recurring_settings'][1]['description']);

    $this->assertDatabaseHas(UnpaidItemAssignmentStudent::class, [
        'unpaid_item_assignment_id' => $hostel_fees_unpaid_item_assignment->id,
        'student_id' => $students[0]->id,
        'status' => UnpaidItemAssignmentStudent::STATUS_NEW,
        'applied_at' => null,
    ]);

    $this->assertDatabaseHas(UnpaidItemAssignmentStudent::class, [
        'unpaid_item_assignment_id' => $hostel_fees_unpaid_item_assignment->id,
        'student_id' => $students[1]->id,
        'status' => UnpaidItemAssignmentStudent::STATUS_NEW,
        'applied_at' => null,
    ]);
});

test('create and assign fees to students success - validation error', function () {
    $this->assertDatabaseCount(UnpaidItemAssignment::class, 0);
    $this->assertDatabaseCount(UnpaidItemAssignmentStudent::class, 0);


    /**
     * missing required fiels
     */

    $response = $this->postJson(route($this->routeNamePrefix . '.fees.create-and-assign'), []);

    expect(UnpaidItemAssignment::count())->toBe(0)
        ->and(UnpaidItemAssignmentStudent::count())->toBe(0)
        ->and($response->json())->toHaveFailedGeneralResponse()
        ->toMatchArray([
            'error' => [
                "student_ids" => [
                    "The student ids field is required."
                ],
                "fees" => [
                    "The fees field is required."
                ],
            ],
            'data' => null
        ]);


    /**
     * student ids and product ids is invalid
     */

    $payload = [
        'student_ids' => [1, 2],
        'fees' => [
            [
                'name' => 'School Fees 2025',
                'product_id' => 1,
                'currency_code' => $this->currencyCode,
                'unit_price' => '500.50',
                'quantity' => 1,
                'apply_at' => '2025-01-01 09:30:00', // 9.30 am, 1st January
                'recurring_settings' => [
                    [
                        'billing_date' => '2025-01-01',
                        'description' => 'School Fees 2025',
                    ],
                ],
            ],
        ]
    ];

    $response = $this->postJson(route($this->routeNamePrefix . '.fees.create-and-assign'), $payload);

    expect(UnpaidItemAssignment::count())->toBe(0)
        ->and(UnpaidItemAssignmentStudent::count())->toBe(0)
        ->and($response->json())->toHaveFailedGeneralResponse()
        ->toMatchArray([
            'error' => [
                "student_ids" => [
                    "The selected student ids is invalid."
                ],
                "fees.0.product_id" => [
                    "The selected fees product is invalid."
                ],
            ],
            'data' => null
        ]);


    /**
     * billing_date month is before apply_at_date month
     */

    $payload = [
        'student_ids' => [Student::factory()->create()->id],
        'fees' => [
            [
                'name' => 'School Fees 2025',
                'product_id' => Product::factory()->create(['category' => ProductCategory::SCHOOL_FEES])->id,
                'currency_code' => $this->currencyCode,
                'unit_price' => '500.50',
                'quantity' => 1,
                'apply_at' => '2025-02-25 09:30:00',
                'recurring_settings' => [
                    [
                        'billing_date' => '2025-01-01', // previous month from apply_at date
                        'description' => 'School Fees 2025',
                    ],
                ],
            ],
        ]
    ];

    $response = $this->postJson(route($this->routeNamePrefix . '.fees.create-and-assign'), $payload);

    expect(UnpaidItemAssignment::count())->toBe(0)
        ->and(UnpaidItemAssignmentStudent::count())->toBe(0)
        ->and($response->json())->toHaveFailedGeneralResponse()
        ->toMatchArray([
            'error' => [
                "fees.0.recurring_settings.0.billing_date" => [
                    "The billing date must be on or after the apply at date."
                ],
            ],
            'data' => null
        ]);


    /**
     * same month billing date with same month apply_at date
     *
     * willl not trigger validation error
     */

    $payload = [
        'student_ids' => [Student::factory()->create()->id],
        'fees' => [
            [
                'name' => 'School Fees 2025',
                'product_id' => Product::factory()->create(['category' => ProductCategory::SCHOOL_FEES])->id,
                'currency_code' => $this->currencyCode,
                'unit_price' => '500.50',
                'quantity' => 1,
                'apply_at' => '2025-01-02 09:30:00',
                'recurring_settings' => [
                    [
                        'billing_date' => '2025-01-01', // same month from apply_at date
                        'description' => 'School Fees 2025',
                    ],
                ],
            ],
        ]
    ];

    $response = $this->postJson(route($this->routeNamePrefix . '.fees.create-and-assign'), $payload)->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and(UnpaidItemAssignment::count())->toBe(1)
        ->and(UnpaidItemAssignmentStudent::count())->toBe(1);
});

test('index', function () {
    UnpaidItem::factory(2)->student()->create();

    $student = Student::factory()->create([
        'user_id' => $this->user->id
    ]);

    $unpaid_items = UnpaidItem::factory(2)->student()->create([
        'period' => '2024-10-01',
        'bill_to_type' => get_class($student),
        'bill_to_id' => $student->id,
        'status' => UnpaidItem::STATUS_UNPAID,
    ]);

    $filters = [
        'bill_to_type' => Userable::USER_TYPE_MAPPING[get_class($student)],
        'bill_to_id' => $student->id,
        'status' => UnpaidItem::STATUS_UNPAID,
        'period_from' => '2024-10-01',
        'period_to' => '2024-10-01',
        'includes' => [
            'billTo',
            'product',
            'createdByEmployee',
            'billingDocument',
        ],
    ];

    $response = $this->getJson(
        route($this->routeNamePrefix . '.fees.index-unpaid-item', $filters)
    )->json();

    // Unpaid Items with Discount scenario already tested in indexUnpaidItem for FE
    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toHaveCount(2)
        ->and($response['data'])->toEqual([
            [
                'id' => $unpaid_items[0]->id,
                'bill_to' => resourceToArray(new SimpleStudentResource($unpaid_items[0]->billTo)),
                'bill_to_type' => $unpaid_items[0]->bill_to_type,
                'bill_to_id' => $unpaid_items[0]->bill_to_id,
                'status' => $unpaid_items[0]->status,
                'description' => $unpaid_items[0]->description,
                'product' => resourceToArray(new ProductResource($unpaid_items[0]->product)),
                'gl_account_code' => $unpaid_items[0]->gl_account_code,
                'period' => $unpaid_items[0]->period,
                'currency_code' => $unpaid_items[0]->currency_code,
                'unit_price' => $unpaid_items[0]->unit_price,
                'quantity' => $unpaid_items[0]->quantity,
                'amount_before_tax' => $unpaid_items[0]->amount_before_tax,
                'amount_before_tax_after_discount' => $unpaid_items[0]->amount_before_tax,
                'created_by_employee' => resourceToArray(new SimpleEmployeeResource($unpaid_items[0]->createdByEmployee)),
                'billing_document' => $unpaid_items[0]->billing_document,
                'paid_at' => $unpaid_items[0]->paid_at,
                'discounts' => null,
            ],
            [
                'id' => $unpaid_items[1]->id,
                'bill_to' => resourceToArray(new SimpleStudentResource($unpaid_items[1]->billTo)),
                'bill_to_type' => $unpaid_items[1]->bill_to_type,
                'bill_to_id' => $unpaid_items[1]->bill_to_id,
                'status' => $unpaid_items[1]->status,
                'description' => $unpaid_items[1]->description,
                'product' => resourceToArray(new ProductResource($unpaid_items[1]->product)),
                'gl_account_code' => $unpaid_items[1]->gl_account_code,
                'period' => $unpaid_items[1]->period,
                'currency_code' => $unpaid_items[1]->currency_code,
                'unit_price' => $unpaid_items[1]->unit_price,
                'quantity' => $unpaid_items[1]->quantity,
                'amount_before_tax' => $unpaid_items[1]->amount_before_tax,
                'amount_before_tax_after_discount' => $unpaid_items[1]->amount_before_tax,
                'created_by_employee' => resourceToArray(new SimpleEmployeeResource($unpaid_items[1]->createdByEmployee)),
                'billing_document' => $unpaid_items[1]->billing_document,
                'paid_at' => $unpaid_items[1]->paid_at,
                'discounts' => null,
            ],
        ]);
});

test('index : see all unpaid_items\' discounts (UNPAID & PAID), with discount + scholarship', function () {
    UnpaidItem::factory(2)->student()->create();

    $student = Student::factory()->create([
        'user_id' => $this->user->id
    ]);

    $invoice_1 = BillingDocument::factory()->create([
        'document_date' => Carbon::now()->toDateString(),
        'type' => BillingDocument::TYPE_INVOICE,
        'status' => BillingDocument::STATUS_CONFIRMED,
        'payment_status' => BillingDocument::PAYMENT_STATUS_PAID,
        'amount_before_tax' => 400,
        'amount_after_tax' => 400,
        'bill_to_type' => $student->getBillToType(),
        'bill_to_id' => $student->getBillToId(),
        'paid_at' => now()->toDateTimeString(),
    ]);

    $paid_item_no_discount = UnpaidItem::factory()->student()->create([
        'period' => '2023-10-01',
        'bill_to_type' => get_class($student),
        'bill_to_id' => $student->id,
        'status' => UnpaidItem::STATUS_PAID,
        'gl_account_code' => GlAccount::CODE_SCHOOL_FEES,
        'billing_document_id' => $invoice_1->id,
        'paid_at' => now()->toDateTimeString(),
    ]);

    $line_item_1 = BillingDocumentLineItem::factory()->create([
        'billing_document_id' => $invoice_1,
        'amount_before_tax' => 400,
        'gl_account_code' => GlAccount::CODE_SCHOOL_FEES,
        'currency_code' => 'MYR',
        'billable_item_id' => $paid_item_no_discount->id,
        'billable_item_type' => get_class($paid_item_no_discount),
        'description' => 'School Fees Jan 2024',
    ]);

    ////////////////////////////////////////////////////////////////////
    $scholarship = Scholarship::factory()->create([
        'name->en' => 'A+ Scholarship',
    ]);

    $scholarship_award = ScholarshipAward::factory()->create([
        'student_id' => $student->id,
        'scholarship_id' => $scholarship->id,
        'effective_from' => '2024-01-01',
        'effective_to' => '2026-12-31',
    ]);

    $discount_100 = DiscountSetting::factory()->create([
        'userable_id' => $student->id,
        'userable_type' => get_class($student),
        'gl_account_codes' => json_encode([GlAccount::CODE_SCHOOL_FEES]),
        'effective_from' => '2024-01-01',
        'effective_to' => '2026-12-31',
        'is_active' => 1,
        'max_amount' => 500,
        'used_amount' => 300,
        'basis' => DiscountSetting::BASIS_FIXED_AMOUNT,
        'basis_amount' => 100,
        'source_type' => get_class($scholarship_award),
        'source_id' => $scholarship_award->id,
    ]);

    $invoice_2 = BillingDocument::factory()->create([
        'document_date' => Carbon::now()->toDateString(),
        'type' => BillingDocument::TYPE_INVOICE,
        'status' => BillingDocument::STATUS_CONFIRMED,
        'payment_status' => BillingDocument::PAYMENT_STATUS_PAID,
        'amount_before_tax' => 400,
        'amount_after_tax' => 400,
        'bill_to_type' => $student->getBillToType(),
        'bill_to_id' => $student->getBillToId(),
        'paid_at' => now()->toDateTimeString(),
    ]);

    $paid_item_with_discount = UnpaidItem::factory()->student()->create([
        'period' => '2024-10-02',
        'bill_to_type' => get_class($student),
        'bill_to_id' => $student->id,
        'status' => UnpaidItem::STATUS_PAID,
        'gl_account_code' => GlAccount::CODE_SCHOOL_FEES,
        'billing_document_id' => $invoice_2->id,
        'paid_at' => now()->toDateTimeString(),
    ]);

    $line_item_2 = BillingDocumentLineItem::factory()->create([
        'billing_document_id' => $invoice_2,
        'amount_before_tax' => 400,
        'gl_account_code' => GlAccount::CODE_SCHOOL_FEES,
        'currency_code' => 'MYR',
        'billable_item_id' => $paid_item_with_discount->id,
        'billable_item_type' => get_class($paid_item_with_discount),
        'description' => 'School Fees Jan 2024',
    ]);

    $line_item_2_discount = BillingDocumentLineItem::factory()->create([
        'billing_document_id' => $invoice_2,
        'amount_before_tax' => -400,
        'gl_account_code' => GlAccount::CODE_SCHOOL_FEES,
        'currency_code' => 'MYR',
        'description' => 'Discount For School Fees Jan 2024',
        'discount_id' => $discount_100->id,
        'is_discount' => true,
        'discount_original_line_item_id' => $line_item_2->id,
    ]);


    ////////////////////////////////////////////////////////////////////
    $unpaid_item_no_discount = UnpaidItem::factory()->student()->create([
        'period' => '2024-10-01',
        'bill_to_type' => get_class($student),
        'bill_to_id' => $student->id,
        'status' => UnpaidItem::STATUS_UNPAID,
        'gl_account_code' => GlAccount::CODE_HOSTEL_FEES,
        'billing_document_id' => null,
        'paid_at' => null,
    ]);

    ////////////////////////////////////////////////////////////////////
    $unpaid_item_with_discount = UnpaidItem::factory()->student()->create([
        'period' => '2024-10-02',
        'bill_to_type' => get_class($student),
        'bill_to_id' => $student->id,
        'status' => UnpaidItem::STATUS_UNPAID,
        'gl_account_code' => GlAccount::CODE_SCHOOL_FEES, // this has discount
        'billing_document_id' => null,
        'paid_at' => null,
    ]);

    $filters = [
        'bill_to_type' => Userable::USER_TYPE_MAPPING[get_class($student)],
        'bill_to_id' => $student->id,
        'period_from' => '2023-10-01',
        'period_to' => '2024-10-02',
    ];

    $response = $this->getJson(
        route($this->routeNamePrefix . '.fees.index-unpaid-item', $filters)
    )->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toHaveCount(4)
        ->and($response['data'][0]['id'])->toEqual($paid_item_no_discount->id)
        ->and($response['data'][0]['discounts'])->toEqual(null)
        ->and($response['data'][1]['id'])->toEqual($paid_item_with_discount->id)
        ->and($response['data'][1]['discounts'])->toEqual([
            $scholarship->name,
        ])
        ->and($response['data'][2]['id'])->toEqual($unpaid_item_no_discount->id)
        ->and($response['data'][2]['discounts'])->toEqual(null)
        ->and($response['data'][3]['id'])->toEqual($unpaid_item_with_discount->id)
        ->and($response['data'][3]['discounts'])->toEqual([
            $scholarship->name,
        ]);
});

test('index by other bill_to_type', function () {
    /**
     * guardian
     */

    UnpaidItem::factory(2)->student()->create();

    $unpaid_item1 = UnpaidItem::factory()->guardian()->create([
        'period' => '2024-10-01'
    ]);

    $filters = [
        'bill_to_type' => Userable::USER_TYPE_MAPPING[$unpaid_item1->bill_to_type],
        'bill_to_id' => $unpaid_item1->bill_to_id,
        'status' => $unpaid_item1->status,
        'period_from' => '2024-10-01',
        'period_to' => '2024-10-01',
        'includes' => [
            'billTo',
            'product',
            'createdByEmployee',
            'billingDocument',
        ],
    ];

    $response = $this->getJson(
        route($this->routeNamePrefix . '.fees.index-unpaid-item', $filters)
    )->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toHaveCount(1)
        ->and($response['data'])->toHaveKey('0.bill_to', resourceToArray(new SimpleGuardianResource($unpaid_item1->billTo)));


    /**
     * employee
     */

    $unpaid_item2 = UnpaidItem::factory()->employee()->create([
        'period' => '2024-10-01'
    ]);

    $filters = [
        'bill_to_type' => Userable::USER_TYPE_MAPPING[$unpaid_item2->bill_to_type],
        'bill_to_id' => $unpaid_item2->bill_to_id,
        'status' => $unpaid_item2->status,
        'period_from' => '2024-10-01',
        'period_to' => '2024-10-01',
        'includes' => [
            'billTo',
            'product',
            'createdByEmployee',
            'billingDocument',
        ],
    ];

    $response = $this->getJson(
        route($this->routeNamePrefix . '.fees.index-unpaid-item', $filters)
    )->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toHaveCount(1)
        ->and($response['data'])->toHaveKey('0.bill_to', resourceToArray(new SimpleEmployeeResource($unpaid_item2->billTo)));
});

test('index non-admin - no discount available', function () {
    UnpaidItem::factory(2)->student()->create();

    $student = Student::factory()->create([
        'user_id' => $this->user->id
    ]);
    $student_no_access = Student::factory()->create([
        'user_id' => User::factory()->create(),
    ]);

    // create discount under another student
    DiscountSetting::factory()->create([
        'userable_id' => $student_no_access->id,
        'userable_type' => get_class($student_no_access),
        'gl_account_codes' => json_encode([GlAccount::CODE_SCHOOL_FEES]),
        'effective_from' => '2024-01-01',
        'effective_to' => '2024-12-31',
        'is_active' => 1,
        'max_amount' => 100,
        'used_amount' => 0,
        'basis' => DiscountSetting::BASIS_PERCENT,
        'basis_amount' => 50
    ]);

    $unpaid_item2 = UnpaidItem::factory()->student()->create([
        'period' => '2024-11-01',
        'amount_before_tax' => 100,
        'bill_to_type' => get_class($student),
        'bill_to_id' => $student->id,
        'gl_account_code' => GlAccount::CODE_SCHOOL_FEES,
    ]);
    $unpaid_item1 = UnpaidItem::factory()->student()->create([
        'period' => '2024-10-01',
        'amount_before_tax' => 100,
        'bill_to_type' => get_class($student),
        'bill_to_id' => $student->id,
        'gl_account_code' => GlAccount::CODE_SCHOOL_FEES,
    ]);
    $unpaid_item3 = UnpaidItem::factory()->student()->create([
        'period' => '2024-12-01',
        'amount_before_tax' => 100,
        'bill_to_type' => get_class($student),
        'bill_to_id' => $student->id,
        'gl_account_code' => GlAccount::CODE_SCHOOL_FEES,
    ]);

    $filters = [
        'bill_to_type' => Userable::USER_TYPE_MAPPING[$unpaid_item1->bill_to_type],
        'bill_to_id' => $unpaid_item1->bill_to_id,
        'status' => UnpaidItem::STATUS_UNPAID,
        'period_from' => '2024-10-01',
        'period_to' => '2024-12-01',
        'includes' => [
            'billTo',
            'product',
            'createdByEmployee',
            'billingDocument',
        ],
        'order_by' => [
            'period' => 'ASC'
        ]
    ];

    $response = $this->getJson(
        route('accounting.fees.index-unpaid-item', $filters)
    )->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toHaveCount(3)
        ->and($response['data'][0])->toEqual([
            'id' => $unpaid_item1->id,
            'bill_to' => resourceToArray(new SimpleStudentResource($unpaid_item1->billTo)),
            'bill_to_type' => $unpaid_item1->bill_to_type,
            'bill_to_id' => $unpaid_item1->bill_to_id,
            'status' => $unpaid_item1->status,
            'description' => $unpaid_item1->description,
            'product' => resourceToArray(new ProductResource($unpaid_item1->product)),
            'gl_account_code' => $unpaid_item1->gl_account_code,
            'period' => $unpaid_item1->period,
            'currency_code' => $unpaid_item1->currency_code,
            'unit_price' => $unpaid_item1->unit_price,
            'quantity' => $unpaid_item1->quantity,
            'amount_before_tax' => $unpaid_item1->amount_before_tax,
            'amount_before_tax_after_discount' => $unpaid_item1->amount_before_tax,
            'created_by_employee' => resourceToArray(new SimpleEmployeeResource($unpaid_item1->createdByEmployee)),
            'billing_document' => $unpaid_item1->billing_document,
            'paid_at' => $unpaid_item1->paid_at,
            'discounts' => null,
        ])
        ->and($response['data'][1])->toEqual([
            'id' => $unpaid_item2->id,
            'bill_to' => resourceToArray(new SimpleStudentResource($unpaid_item2->billTo)),
            'bill_to_type' => $unpaid_item2->bill_to_type,
            'bill_to_id' => $unpaid_item2->bill_to_id,
            'status' => $unpaid_item2->status,
            'description' => $unpaid_item2->description,
            'product' => resourceToArray(new ProductResource($unpaid_item2->product)),
            'gl_account_code' => $unpaid_item2->gl_account_code,
            'period' => $unpaid_item2->period,
            'currency_code' => $unpaid_item2->currency_code,
            'unit_price' => $unpaid_item2->unit_price,
            'quantity' => $unpaid_item2->quantity,
            'amount_before_tax' => $unpaid_item2->amount_before_tax,
            'amount_before_tax_after_discount' => $unpaid_item2->amount_before_tax,
            'created_by_employee' => resourceToArray(new SimpleEmployeeResource($unpaid_item2->createdByEmployee)),
            'billing_document' => $unpaid_item2->billing_document,
            'paid_at' => $unpaid_item2->paid_at,
            'discounts' => null,
        ])
        ->and($response['data'][2])->toEqual([
            'id' => $unpaid_item3->id,
            'bill_to' => resourceToArray(new SimpleStudentResource($unpaid_item3->billTo)),
            'bill_to_type' => $unpaid_item3->bill_to_type,
            'bill_to_id' => $unpaid_item3->bill_to_id,
            'status' => $unpaid_item3->status,
            'description' => $unpaid_item3->description,
            'product' => resourceToArray(new ProductResource($unpaid_item3->product)),
            'gl_account_code' => $unpaid_item3->gl_account_code,
            'period' => $unpaid_item3->period,
            'currency_code' => $unpaid_item3->currency_code,
            'unit_price' => $unpaid_item3->unit_price,
            'quantity' => $unpaid_item3->quantity,
            'amount_before_tax' => $unpaid_item3->amount_before_tax,
            'amount_before_tax_after_discount' => $unpaid_item3->amount_before_tax,
            'created_by_employee' => resourceToArray(new SimpleEmployeeResource($unpaid_item3->createdByEmployee)),
            'billing_document' => $unpaid_item3->billing_document,
            'paid_at' => $unpaid_item3->paid_at,
            'discounts' => null,
        ]);

    // try to query other unrelated ppl data, should get 403
    $response = $this->getJson(
        route('accounting.fees.index-unpaid-item', [
            'bill_to_type' => Userable::USER_TYPE_MAPPING[get_class($student_no_access)],
            'bill_to_id' => $student_no_access->id,
            'period_from' => '2024-01-01',
            'period_to' => '2025-01-01',
        ])
    )->json();

    expect($response)->toHaveFailedGeneralResponse()
        ->and($response['code'])->toBe(403);
});


test('index non-admin - amount greater than discount max_amount', function () {
    UnpaidItem::factory(2)->student()->create();

    $student = Student::factory()->create([
        'user_id' => $this->user->id
    ]);
    $student_no_access = Student::factory()->create([
        'user_id' => User::factory()->create(),
    ]);

    $discount = DiscountSetting::factory()->create([
        'userable_id' => $student->id,
        'userable_type' => get_class($student),
        'gl_account_codes' => json_encode([GlAccount::CODE_SCHOOL_FEES]),
        'effective_from' => '2024-01-01',
        'effective_to' => '2024-12-31',
        'is_active' => 1,
        'max_amount' => 100,
        'used_amount' => 0,
        'basis' => DiscountSetting::BASIS_PERCENT,
        'basis_amount' => 70
    ]);

    $unpaid_item2 = UnpaidItem::factory()->student()->create([
        'period' => '2024-11-01',
        'amount_before_tax' => 100,
        'bill_to_type' => get_class($student),
        'bill_to_id' => $student->id,
        'gl_account_code' => GlAccount::CODE_SCHOOL_FEES,
    ]);
    $unpaid_item1 = UnpaidItem::factory()->student()->create([
        'period' => '2024-10-01',
        'amount_before_tax' => 100,
        'bill_to_type' => get_class($student),
        'bill_to_id' => $student->id,
        'gl_account_code' => GlAccount::CODE_SCHOOL_FEES,
    ]);
    $unpaid_item3 = UnpaidItem::factory()->student()->create([
        'period' => '2024-12-01',
        'amount_before_tax' => 100,
        'bill_to_type' => get_class($student),
        'bill_to_id' => $student->id,
        'gl_account_code' => GlAccount::CODE_SCHOOL_FEES,
    ]);
    // irrelevant
    $unpaid_item4 = UnpaidItem::factory()->student()->create([
        'period' => '2024-09-01',
        'amount_before_tax' => 100,
        'bill_to_type' => get_class($student),
        'bill_to_id' => $student->id,
        'gl_account_code' => GlAccount::CODE_SCHOOL_FEES,
    ]);


    $filters = [
        'bill_to_type' => Userable::USER_TYPE_MAPPING[$unpaid_item1->bill_to_type],
        'bill_to_id' => $unpaid_item1->bill_to_id,
        'status' => UnpaidItem::STATUS_UNPAID,
        'period_from' => '2024-10-01',
        'period_to' => '2024-12-01',
        'includes' => [
            'billTo',
            'product',
            'createdByEmployee',
            'billingDocument',
        ],
        'order_by' => [
            'period' => 'ASC'
        ]
    ];

    $response = $this->getJson(
        route('accounting.fees.index-unpaid-item', $filters)
    )->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toHaveCount(3)
        ->and($response['data'][0])->toEqual([
            'id' => $unpaid_item1->id,
            'bill_to' => resourceToArray(new SimpleStudentResource($unpaid_item1->billTo)),
            'bill_to_type' => $unpaid_item1->bill_to_type,
            'bill_to_id' => $unpaid_item1->bill_to_id,
            'status' => $unpaid_item1->status,
            'description' => $unpaid_item1->description,
            'product' => resourceToArray(new ProductResource($unpaid_item1->product)),
            'gl_account_code' => $unpaid_item1->gl_account_code,
            'period' => $unpaid_item1->period,
            'currency_code' => $unpaid_item1->currency_code,
            'unit_price' => $unpaid_item1->unit_price,
            'quantity' => $unpaid_item1->quantity,
            'amount_before_tax' => $unpaid_item1->amount_before_tax,
            'amount_before_tax_after_discount' => 30,
            'created_by_employee' => resourceToArray(new SimpleEmployeeResource($unpaid_item1->createdByEmployee)),
            'billing_document' => $unpaid_item1->billing_document,
            'paid_at' => $unpaid_item1->paid_at,
            'discounts' => [
                'Discount (' . $discount->basis . '): ' . number_format($discount->basis_amount, 2),
            ],
        ])
        ->and($response['data'][1])->toEqual([
            'id' => $unpaid_item2->id,
            'bill_to' => resourceToArray(new SimpleStudentResource($unpaid_item2->billTo)),
            'bill_to_type' => $unpaid_item2->bill_to_type,
            'bill_to_id' => $unpaid_item2->bill_to_id,
            'status' => $unpaid_item2->status,
            'description' => $unpaid_item2->description,
            'product' => resourceToArray(new ProductResource($unpaid_item2->product)),
            'gl_account_code' => $unpaid_item2->gl_account_code,
            'period' => $unpaid_item2->period,
            'currency_code' => $unpaid_item2->currency_code,
            'unit_price' => $unpaid_item2->unit_price,
            'quantity' => $unpaid_item2->quantity,
            'amount_before_tax' => $unpaid_item2->amount_before_tax,
            'amount_before_tax_after_discount' => 70,
            'created_by_employee' => resourceToArray(new SimpleEmployeeResource($unpaid_item2->createdByEmployee)),
            'billing_document' => $unpaid_item2->billing_document,
            'paid_at' => $unpaid_item2->paid_at,
            'discounts' => [
                'Discount (' . $discount->basis . '): ' . number_format($discount->basis_amount, 2),
            ],
        ])
        ->and($response['data'][2])->toEqual([
            'id' => $unpaid_item3->id,
            'bill_to' => resourceToArray(new SimpleStudentResource($unpaid_item3->billTo)),
            'bill_to_type' => $unpaid_item3->bill_to_type,
            'bill_to_id' => $unpaid_item3->bill_to_id,
            'status' => $unpaid_item3->status,
            'description' => $unpaid_item3->description,
            'product' => resourceToArray(new ProductResource($unpaid_item3->product)),
            'gl_account_code' => $unpaid_item3->gl_account_code,
            'period' => $unpaid_item3->period,
            'currency_code' => $unpaid_item3->currency_code,
            'unit_price' => $unpaid_item3->unit_price,
            'quantity' => $unpaid_item3->quantity,
            'amount_before_tax' => $unpaid_item3->amount_before_tax,
            'amount_before_tax_after_discount' => $unpaid_item3->amount_before_tax, // no discount because exhauseted discount max_amount already
            'created_by_employee' => resourceToArray(new SimpleEmployeeResource($unpaid_item3->createdByEmployee)),
            'billing_document' => $unpaid_item3->billing_document,
            'paid_at' => $unpaid_item3->paid_at,
            'discounts' => null,
        ]);

    // make sure no actual billing document/line items created
    $this->assertDatabaseCount(BillingDocumentLineItem::class, 0);
    $this->assertDatabaseCount(BillingDocument::class, 0);

    // make sure discount is not actually used
    $this->assertDatabaseHas(DiscountSetting::class, [
        'id' => $discount->id,
        'userable_id' => $student->id,
        'userable_type' => get_class($student),
        'gl_account_codes' => json_encode([GlAccount::CODE_SCHOOL_FEES]),
        'effective_from' => '2024-01-01',
        'effective_to' => '2024-12-31',
        'is_active' => 1,
        'max_amount' => 100,
        'used_amount' => 0,
        'basis' => DiscountSetting::BASIS_PERCENT,
        'basis_amount' => 70
    ]);
});


test('index non-admin - amount lesser than discount max_amount', function () {
    UnpaidItem::factory(2)->student()->create();

    $student = Student::factory()->create([
        'user_id' => $this->user->id
    ]);
    $student_no_access = Student::factory()->create([
        'user_id' => User::factory()->create(),
    ]);

    $discount = DiscountSetting::factory()->create([
        'userable_id' => $student->id,
        'userable_type' => get_class($student),
        'gl_account_codes' => json_encode([GlAccount::CODE_SCHOOL_FEES]),
        'effective_from' => '2024-01-01',
        'effective_to' => '2024-12-31',
        'is_active' => 1,
        'max_amount' => 1000,
        'used_amount' => 0,
        'basis' => DiscountSetting::BASIS_PERCENT,
        'basis_amount' => 70
    ]);

    $unpaid_item2 = UnpaidItem::factory()->student()->create([
        'period' => '2024-11-01',
        'amount_before_tax' => 100,
        'bill_to_type' => get_class($student),
        'bill_to_id' => $student->id,
        'gl_account_code' => GlAccount::CODE_SCHOOL_FEES,
    ]);
    $unpaid_item1 = UnpaidItem::factory()->student()->create([
        'period' => '2024-10-01',
        'amount_before_tax' => 100,
        'bill_to_type' => get_class($student),
        'bill_to_id' => $student->id,
        'gl_account_code' => GlAccount::CODE_SCHOOL_FEES,
    ]);
    $unpaid_item3 = UnpaidItem::factory()->student()->create([
        'period' => '2024-12-01',
        'amount_before_tax' => 100,
        'bill_to_type' => get_class($student),
        'bill_to_id' => $student->id,
        'gl_account_code' => GlAccount::CODE_SCHOOL_FEES,
    ]);
    // irrelevant
    $unpaid_item4 = UnpaidItem::factory()->student()->create([
        'period' => '2024-09-01',
        'amount_before_tax' => 100,
        'bill_to_type' => get_class($student),
        'bill_to_id' => $student->id,
        'gl_account_code' => GlAccount::CODE_SCHOOL_FEES,
    ]);


    $filters = [
        'bill_to_type' => Userable::USER_TYPE_MAPPING[$unpaid_item1->bill_to_type],
        'bill_to_id' => $unpaid_item1->bill_to_id,
        'status' => UnpaidItem::STATUS_UNPAID,
        'period_from' => '2024-10-01',
        'period_to' => '2024-12-01',
        'includes' => [
            'billTo',
            'product',
            'createdByEmployee',
            'billingDocument',
        ],
        'order_by' => [
            'period' => 'ASC'
        ]
    ];

    $response = $this->getJson(
        route('accounting.fees.index-unpaid-item', $filters)
    )->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toHaveCount(3)
        ->and($response['data'][0])->toEqual([
            'id' => $unpaid_item1->id,
            'bill_to' => resourceToArray(new SimpleStudentResource($unpaid_item1->billTo)),
            'bill_to_type' => $unpaid_item1->bill_to_type,
            'bill_to_id' => $unpaid_item1->bill_to_id,
            'status' => $unpaid_item1->status,
            'description' => $unpaid_item1->description,
            'product' => resourceToArray(new ProductResource($unpaid_item1->product)),
            'gl_account_code' => $unpaid_item1->gl_account_code,
            'period' => $unpaid_item1->period,
            'currency_code' => $unpaid_item1->currency_code,
            'unit_price' => $unpaid_item1->unit_price,
            'quantity' => $unpaid_item1->quantity,
            'amount_before_tax' => $unpaid_item1->amount_before_tax,
            'amount_before_tax_after_discount' => 30,
            'created_by_employee' => resourceToArray(new SimpleEmployeeResource($unpaid_item1->createdByEmployee)),
            'billing_document' => $unpaid_item1->billing_document,
            'paid_at' => $unpaid_item1->paid_at,
            'discounts' => [
                'Discount (' . $discount->basis . '): ' . number_format($discount->basis_amount, 2),
            ],
        ])
        ->and($response['data'][1])->toEqual([
            'id' => $unpaid_item2->id,
            'bill_to' => resourceToArray(new SimpleStudentResource($unpaid_item2->billTo)),
            'bill_to_type' => $unpaid_item2->bill_to_type,
            'bill_to_id' => $unpaid_item2->bill_to_id,
            'status' => $unpaid_item2->status,
            'description' => $unpaid_item2->description,
            'product' => resourceToArray(new ProductResource($unpaid_item2->product)),
            'gl_account_code' => $unpaid_item2->gl_account_code,
            'period' => $unpaid_item2->period,
            'currency_code' => $unpaid_item2->currency_code,
            'unit_price' => $unpaid_item2->unit_price,
            'quantity' => $unpaid_item2->quantity,
            'amount_before_tax' => $unpaid_item2->amount_before_tax,
            'amount_before_tax_after_discount' => 30,
            'created_by_employee' => resourceToArray(new SimpleEmployeeResource($unpaid_item2->createdByEmployee)),
            'billing_document' => $unpaid_item2->billing_document,
            'paid_at' => $unpaid_item2->paid_at,
            'discounts' => [
                'Discount (' . $discount->basis . '): ' . number_format($discount->basis_amount, 2),
            ],
        ])
        ->and($response['data'][2])->toEqual([
            'id' => $unpaid_item3->id,
            'bill_to' => resourceToArray(new SimpleStudentResource($unpaid_item3->billTo)),
            'bill_to_type' => $unpaid_item3->bill_to_type,
            'bill_to_id' => $unpaid_item3->bill_to_id,
            'status' => $unpaid_item3->status,
            'description' => $unpaid_item3->description,
            'product' => resourceToArray(new ProductResource($unpaid_item3->product)),
            'gl_account_code' => $unpaid_item3->gl_account_code,
            'period' => $unpaid_item3->period,
            'currency_code' => $unpaid_item3->currency_code,
            'unit_price' => $unpaid_item3->unit_price,
            'quantity' => $unpaid_item3->quantity,
            'amount_before_tax' => $unpaid_item3->amount_before_tax,
            'amount_before_tax_after_discount' => 30,
            'created_by_employee' => resourceToArray(new SimpleEmployeeResource($unpaid_item3->createdByEmployee)),
            'billing_document' => $unpaid_item3->billing_document,
            'paid_at' => $unpaid_item3->paid_at,
            'discounts' => [
                'Discount (' . $discount->basis . '): ' . number_format($discount->basis_amount, 2),
            ],
        ]);

    // make sure no actual billing document/line items created
    $this->assertDatabaseCount(BillingDocumentLineItem::class, 0);
    $this->assertDatabaseCount(BillingDocument::class, 0);

    // make sure discount is not actually used
    $this->assertDatabaseHas(DiscountSetting::class, [
        'id' => $discount->id,
        'userable_id' => $student->id,
        'userable_type' => get_class($student),
        'gl_account_codes' => json_encode([GlAccount::CODE_SCHOOL_FEES]),
        'effective_from' => '2024-01-01',
        'effective_to' => '2024-12-31',
        'is_active' => 1,
        'max_amount' => 1000,
        'used_amount' => 0,
        'basis' => DiscountSetting::BASIS_PERCENT,
        'basis_amount' => 70
    ]);
});


test('index non-admin - unpaid items with different GL accounts calculate discounted amount', function () {
    UnpaidItem::factory(2)->student()->create();

    $student = Student::factory()->create([
        'user_id' => $this->user->id
    ]);
    $student_no_access = Student::factory()->create([
        'user_id' => User::factory()->create(),
    ]);

    $discount = DiscountSetting::factory()->create([
        'userable_id' => $student->id,
        'userable_type' => get_class($student),
        'gl_account_codes' => json_encode([GlAccount::CODE_SCHOOL_FEES]),
        'effective_from' => '2024-01-01',
        'effective_to' => '2024-12-31',
        'is_active' => 1,
        'max_amount' => 1000,
        'used_amount' => 0,
        'basis' => DiscountSetting::BASIS_PERCENT,
        'basis_amount' => 100
    ]);

    $unpaid_item2 = UnpaidItem::factory()->student()->create([
        'period' => '2024-11-01',
        'amount_before_tax' => 100,
        'bill_to_type' => get_class($student),
        'bill_to_id' => $student->id,
        'gl_account_code' => GlAccount::CODE_SCHOOL_FEES,
    ]);
    $unpaid_item1 = UnpaidItem::factory()->student()->create([
        'period' => '2024-10-01',
        'amount_before_tax' => 100,
        'bill_to_type' => get_class($student),
        'bill_to_id' => $student->id,
        'gl_account_code' => GlAccount::CODE_SCHOOL_FEES,
    ]);
    $unpaid_item3 = UnpaidItem::factory()->student()->create([
        'period' => '2024-12-01',
        'amount_before_tax' => 100,
        'bill_to_type' => get_class($student),
        'bill_to_id' => $student->id,
        'gl_account_code' => GlAccount::CODE_HOSTEL_FEES,
    ]);
    // irrelevant
    $unpaid_item4 = UnpaidItem::factory()->student()->create([
        'period' => '2024-09-01',
        'amount_before_tax' => 100,
        'bill_to_type' => get_class($student),
        'bill_to_id' => $student->id,
        'gl_account_code' => GlAccount::CODE_SCHOOL_FEES,
    ]);


    $filters = [
        'bill_to_type' => Userable::USER_TYPE_MAPPING[$unpaid_item1->bill_to_type],
        'bill_to_id' => $unpaid_item1->bill_to_id,
        'status' => UnpaidItem::STATUS_UNPAID,
        'period_from' => '2024-10-01',
        'period_to' => '2024-12-01',
        'includes' => [
            'billTo',
            'product',
            'createdByEmployee',
            'billingDocument',
        ],
        'order_by' => [
            'period' => 'ASC'
        ]
    ];

    $response = $this->getJson(
        route('accounting.fees.index-unpaid-item', $filters)
    )->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toHaveCount(3)
        ->and($response['data'][0])->toEqual([
            'id' => $unpaid_item1->id,
            'bill_to' => resourceToArray(new SimpleStudentResource($unpaid_item1->billTo)),
            'bill_to_type' => $unpaid_item1->bill_to_type,
            'bill_to_id' => $unpaid_item1->bill_to_id,
            'status' => $unpaid_item1->status,
            'description' => $unpaid_item1->description,
            'product' => resourceToArray(new ProductResource($unpaid_item1->product)),
            'gl_account_code' => $unpaid_item1->gl_account_code,
            'period' => $unpaid_item1->period,
            'currency_code' => $unpaid_item1->currency_code,
            'unit_price' => $unpaid_item1->unit_price,
            'quantity' => $unpaid_item1->quantity,
            'amount_before_tax' => $unpaid_item1->amount_before_tax,
            'amount_before_tax_after_discount' => 0,
            'created_by_employee' => resourceToArray(new SimpleEmployeeResource($unpaid_item1->createdByEmployee)),
            'billing_document' => $unpaid_item1->billing_document,
            'paid_at' => $unpaid_item1->paid_at,
            'discounts' => [
                'Discount (' . $discount->basis . '): ' . number_format($discount->basis_amount, 2),
            ],
        ])
        ->and($response['data'][1])->toEqual([
            'id' => $unpaid_item2->id,
            'bill_to' => resourceToArray(new SimpleStudentResource($unpaid_item2->billTo)),
            'bill_to_type' => $unpaid_item2->bill_to_type,
            'bill_to_id' => $unpaid_item2->bill_to_id,
            'status' => $unpaid_item2->status,
            'description' => $unpaid_item2->description,
            'product' => resourceToArray(new ProductResource($unpaid_item2->product)),
            'gl_account_code' => $unpaid_item2->gl_account_code,
            'period' => $unpaid_item2->period,
            'currency_code' => $unpaid_item2->currency_code,
            'unit_price' => $unpaid_item2->unit_price,
            'quantity' => $unpaid_item2->quantity,
            'amount_before_tax' => $unpaid_item2->amount_before_tax,
            'amount_before_tax_after_discount' => 0,
            'created_by_employee' => resourceToArray(new SimpleEmployeeResource($unpaid_item2->createdByEmployee)),
            'billing_document' => $unpaid_item2->billing_document,
            'paid_at' => $unpaid_item2->paid_at,
            'discounts' => [
                'Discount (' . $discount->basis . '): ' . number_format($discount->basis_amount, 2),
            ],
        ])
        ->and($response['data'][2])->toEqual([
            'id' => $unpaid_item3->id,
            'bill_to' => resourceToArray(new SimpleStudentResource($unpaid_item3->billTo)),
            'bill_to_type' => $unpaid_item3->bill_to_type,
            'bill_to_id' => $unpaid_item3->bill_to_id,
            'status' => $unpaid_item3->status,
            'description' => $unpaid_item3->description,
            'product' => resourceToArray(new ProductResource($unpaid_item3->product)),
            'gl_account_code' => $unpaid_item3->gl_account_code,
            'period' => $unpaid_item3->period,
            'currency_code' => $unpaid_item3->currency_code,
            'unit_price' => $unpaid_item3->unit_price,
            'quantity' => $unpaid_item3->quantity,
            'amount_before_tax' => $unpaid_item3->amount_before_tax,
            'amount_before_tax_after_discount' => $unpaid_item3->amount_before_tax,     // discount not applied due to different gl account
            'created_by_employee' => resourceToArray(new SimpleEmployeeResource($unpaid_item3->createdByEmployee)),
            'billing_document' => $unpaid_item3->billing_document,
            'paid_at' => $unpaid_item3->paid_at,
            'discounts' => null,
        ]);

    // make sure no actual billing document/line items created
    $this->assertDatabaseCount(BillingDocumentLineItem::class, 0);
    $this->assertDatabaseCount(BillingDocument::class, 0);

    // make sure discount is not actually used
    $this->assertDatabaseHas(DiscountSetting::class, [
        'id' => $discount->id,
        'userable_id' => $student->id,
        'userable_type' => get_class($student),
        'gl_account_codes' => json_encode([GlAccount::CODE_SCHOOL_FEES]),
        'effective_from' => '2024-01-01',
        'effective_to' => '2024-12-31',
        'is_active' => 1,
        'max_amount' => 1000,
        'used_amount' => 0,
        'basis' => DiscountSetting::BASIS_PERCENT,
        'basis_amount' => 100
    ]);
});

test('index non-admin - unpaid items with same GL accounts and multiple discounts', function () {
    UnpaidItem::factory(2)->student()->create();

    $student = Student::factory()->create([
        'user_id' => $this->user->id
    ]);

    $scholarship = Scholarship::factory()->create([
        'name->en' => 'A+ Scholarship',
    ]);

    $scholarship_award = ScholarshipAward::factory()->create([
        'student_id' => $student->id,
        'scholarship_id' => $scholarship->id,
        'effective_from' => '2024-01-01',
        'effective_to' => '2026-12-31',
    ]);

    $discount_100 = DiscountSetting::factory()->create([
        'userable_id' => $student->id,
        'userable_type' => get_class($student),
        'gl_account_codes' => json_encode([GlAccount::CODE_SCHOOL_FEES]),
        'effective_from' => '2025-01-01',
        'effective_to' => '2025-12-31',
        'is_active' => 1,
        'max_amount' => 500,
        'used_amount' => 300,
        'basis' => DiscountSetting::BASIS_FIXED_AMOUNT,
        'basis_amount' => 100,
        'source_type' => get_class($scholarship_award),
        'source_id' => $scholarship_award->id,
    ]);

    $discount_75 = DiscountSetting::factory()->create([
        'userable_id' => $student->id,
        'userable_type' => get_class($student),
        'gl_account_codes' => json_encode([GlAccount::CODE_SCHOOL_FEES]),
        'effective_from' => '2025-01-01',
        'effective_to' => '2025-12-31',
        'is_active' => 1,
        'max_amount' => 500,
        'used_amount' => 225,
        'basis' => DiscountSetting::BASIS_FIXED_AMOUNT,
        'basis_amount' => 75
    ]);

    $unpaid_item_sept = UnpaidItem::factory()->student()->create([
        'description' => 'School Fees Sep 2025',
        'period' => '2025-09-01',
        'amount_before_tax' => 500,
        'bill_to_type' => get_class($student),
        'bill_to_id' => $student->id,
        'gl_account_code' => GlAccount::CODE_SCHOOL_FEES,
        'status' => UnpaidItem::STATUS_UNPAID,
    ]);

    $unpaid_item_oct = UnpaidItem::factory()->student()->create([
        'description' => 'School Fees Oct 2025',
        'period' => '2025-10-01',
        'amount_before_tax' => 500,
        'bill_to_type' => get_class($student),
        'bill_to_id' => $student->id,
        'gl_account_code' => GlAccount::CODE_SCHOOL_FEES,
        'status' => UnpaidItem::STATUS_UNPAID,
    ]);

    $unpaid_item_nov = UnpaidItem::factory()->student()->create([
        'description' => 'School Fees Nov 2025',
        'period' => '2025-11-01',
        'amount_before_tax' => 500,
        'bill_to_type' => get_class($student),
        'bill_to_id' => $student->id,
        'gl_account_code' => GlAccount::CODE_SCHOOL_FEES,
        'status' => UnpaidItem::STATUS_UNPAID,
    ]);

    $unpaid_item_dec = UnpaidItem::factory()->student()->create([
        'description' => 'School Fees Dec 2025',
        'period' => '2025-12-01',
        'amount_before_tax' => 500,
        'bill_to_type' => get_class($student),
        'bill_to_id' => $student->id,
        'gl_account_code' => GlAccount::CODE_SCHOOL_FEES,
        'status' => UnpaidItem::STATUS_UNPAID,
    ]);


    $filters = [
        'bill_to_type' => Userable::USER_TYPE_MAPPING[get_class($student)],
        'bill_to_id' => $student->id,
        'status' => UnpaidItem::STATUS_UNPAID,
        'period_from' => '2025-01-01',
        'period_to' => '2025-12-01',
        'order_by' => [
            'period' => 'ASC'
        ]
    ];

    $response = $this->getJson(
        route('accounting.fees.index-unpaid-item', $filters)
    )->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toHaveCount(4)
        ->and($response['data'][0]['description'])->toEqual('School Fees Sep 2025')
        ->and($response['data'][0]['amount_before_tax'])->toEqual(500) // original amount
        ->and($response['data'][0]['amount_before_tax_after_discount'])->toEqual(325) // total discount  = 100 (discount_1) + 75 (dicsount_2) = 175 , total amount after discount = 500 - 175 = 325
        ->and($response['data'][0]['discounts'])->toEqual([
            'A+ Scholarship',
            'Discount (' . $discount_75->basis . '): ' . number_format($discount_75->basis_amount, 2),
        ])
        ->and($response['data'][1]['description'])->toEqual('School Fees Oct 2025')
        ->and($response['data'][1]['amount_before_tax'])->toEqual(500) // original amount
        ->and($response['data'][1]['amount_before_tax_after_discount'])->toEqual(325) // total discount  = 100 (discount_1) + 75 (dicsount_2) = 175 , total amount after discount = 500 - 175 = 325
        ->and($response['data'][1]['discounts'])->toEqual([
            'A+ Scholarship',
            'Discount (' . $discount_75->basis . '): ' . number_format($discount_75->basis_amount, 2),
        ])
        ->and($response['data'][2]['description'])->toEqual('School Fees Nov 2025')
        ->and($response['data'][2]['amount_before_tax'])->toEqual(500) // original amount
        ->and($response['data'][2]['amount_before_tax_after_discount'])->toEqual(425) // total discount  = 75 (dicsount_2) = 75 , total amount after discount = 500 - 75 = 425
        ->and($response['data'][2]['discounts'])->toEqual([
            'Discount (' . $discount_75->basis . '): ' . number_format($discount_75->basis_amount, 2),
        ])
        ->and($response['data'][3]['description'])->toEqual('School Fees Dec 2025')
        ->and($response['data'][3]['amount_before_tax'])->toEqual(500) // original amount
        ->and($response['data'][3]['amount_before_tax_after_discount'])->toEqual(450) // total discount = 50 (discount_2) = 50 , total amount after discount = 500 - 50 = 450
        ->and($response['data'][3]['discounts'])->toEqual([
            'Discount (' . $discount_75->basis . '): ' . number_format($discount_75->basis_amount, 2),
        ]);

    // make sure no actual billing document/line items created
    $this->assertDatabaseCount(BillingDocumentLineItem::class, 0);
    $this->assertDatabaseCount(BillingDocument::class, 0);

    // make sure discount is not actually used
    $this->assertDatabaseHas(DiscountSetting::class, [
        'id' => $discount_100->id,
        'userable_id' => $student->id,
        'userable_type' => get_class($student),
        'gl_account_codes' => json_encode([GlAccount::CODE_SCHOOL_FEES]),
        'effective_from' => '2025-01-01',
        'effective_to' => '2025-12-31',
        'is_active' => 1,
        'max_amount' => 500,
        'used_amount' => 300,
        'basis' => DiscountSetting::BASIS_FIXED_AMOUNT,
        'basis_amount' => 100
    ]);

    $this->assertDatabaseHas(DiscountSetting::class, [
        'id' => $discount_75->id,
        'userable_id' => $student->id,
        'userable_type' => get_class($student),
        'gl_account_codes' => json_encode([GlAccount::CODE_SCHOOL_FEES]),
        'effective_from' => '2025-01-01',
        'effective_to' => '2025-12-31',
        'is_active' => 1,
        'max_amount' => 500,
        'used_amount' => 225,
        'basis' => DiscountSetting::BASIS_FIXED_AMOUNT,
        'basis_amount' => 75
    ]);
});

test('index non-admin - PENDING or PAID items with correct discounts', function () {
    UnpaidItem::factory(2)->student()->create();

    $student = Student::factory()->create([
        'user_id' => $this->user->id
    ]);

    $school_discount_1 = DiscountSetting::factory()->create([
        'userable_id' => $student->id,
        'userable_type' => get_class($student),
        'gl_account_codes' => json_encode([GlAccount::CODE_SCHOOL_FEES]),
        'effective_from' => '2025-01-01',
        'effective_to' => '2025-12-31',
        'is_active' => 1,
        'max_amount' => null,
        'used_amount' => 0,
        'basis' => DiscountSetting::BASIS_FIXED_AMOUNT,
        'basis_amount' => 50
    ]);

    $school_discount_2 = DiscountSetting::factory()->create([
        'userable_id' => $student->id,
        'userable_type' => get_class($student),
        'gl_account_codes' => json_encode([GlAccount::CODE_SCHOOL_FEES]),
        'effective_from' => '2025-01-01',
        'effective_to' => '2025-12-31',
        'is_active' => 1,
        'max_amount' => null,
        'used_amount' => 0,
        'basis' => DiscountSetting::BASIS_FIXED_AMOUNT,
        'basis_amount' => 25
    ]);

    $hostel_discount = DiscountSetting::factory()->create([
        'userable_id' => $student->id,
        'userable_type' => get_class($student),
        'gl_account_codes' => json_encode([GlAccount::CODE_HOSTEL_FEES]),
        'effective_from' => '2025-01-01',
        'effective_to' => '2025-12-31',
        'is_active' => 1,
        'max_amount' => null,
        'used_amount' => 0,
        'basis' => DiscountSetting::BASIS_FIXED_AMOUNT,
        'basis_amount' => 65
    ]);

    $unpaid_item_SCHOOL_JAN = UnpaidItem::factory()->student()->create([
        'description' => 'School Fees Jan 2025',
        'period' => '2025-01-01',
        'amount_before_tax' => 500,
        'bill_to_type' => get_class($student),
        'bill_to_id' => $student->id,
        'gl_account_code' => GlAccount::CODE_SCHOOL_FEES,
        'status' => UnpaidItem::STATUS_UNPAID,
    ]);

    $unpaid_item_SCHOOL_FEB = UnpaidItem::factory()->student()->create([
        'description' => 'School Fees Feb 2025',
        'period' => '2025-02-01',
        'amount_before_tax' => 500,
        'bill_to_type' => get_class($student),
        'bill_to_id' => $student->id,
        'gl_account_code' => GlAccount::CODE_SCHOOL_FEES,
        'status' => UnpaidItem::STATUS_UNPAID,
    ]);

    $unpaid_item_HOSTEL_JAN = UnpaidItem::factory()->student()->create([
        'description' => 'Hostel Fees Jan 2025',
        'period' => '2025-01-01',
        'amount_before_tax' => 250,
        'bill_to_type' => get_class($student),
        'bill_to_id' => $student->id,
        'gl_account_code' => GlAccount::CODE_HOSTEL_FEES,
        'status' => UnpaidItem::STATUS_UNPAID,
    ]);

    $unpaid_item_HOSTEL_FEB = UnpaidItem::factory()->student()->create([
        'description' => 'Hostel Fees Feb 2025',
        'period' => '2025-02-01',
        'amount_before_tax' => 250,
        'bill_to_type' => get_class($student),
        'bill_to_id' => $student->id,
        'gl_account_code' => GlAccount::CODE_HOSTEL_FEES,
        'status' => UnpaidItem::STATUS_UNPAID,
    ]);

    $line_item_SCHOOL_JAN = BillingDocumentLineItem::factory()->create([
        'description' => 'School Fees Jan 2025',
        'amount_before_tax' => 500,
        'billable_item_type' => get_class($unpaid_item_SCHOOL_JAN),
        'billable_item_id' => $unpaid_item_SCHOOL_JAN->id,
        'gl_account_code' => GlAccount::CODE_SCHOOL_FEES,
    ]);

    $line_item_HOSTEL_JAN = BillingDocumentLineItem::factory()->create([
        'description' => 'Hostel Fees Jan 2025',
        'amount_before_tax' => 250,
        'billable_item_type' => get_class($unpaid_item_HOSTEL_JAN),
        'billable_item_id' => $unpaid_item_HOSTEL_JAN->id,
        'gl_account_code' => GlAccount::CODE_HOSTEL_FEES,
    ]);

    /**
     * create CONFIRMED invoice with discount for the UnpaidItems
     */

    /** @var BillingDocumentService */
    $service = app()->make(BillingDocumentService::class);

    $service
        ->init()
        ->setType(\App\Models\BillingDocument::TYPE_INVOICE)
        ->setSubType(BillingDocument::SUB_TYPE_FEES)
        ->setCurrency('MYR')
        ->setStatus(BillingDocument::STATUS_CONFIRMED)
        ->setDocumentDate(\Carbon\Carbon::parse('2025-07-01'))
        ->setLegalEntity($this->legalEntity)
        ->setBillToParty($student)
        ->setPaymentTerm($this->paymentTerm)
        ->calculatePaymentDueDate()
        ->generateReferenceNumber()
        ->addLineItem($line_item_SCHOOL_JAN)
        ->addLineItem($line_item_HOSTEL_JAN)
//        ->addLineItem($line_item_SCHOOL_FEB)
//        ->addLineItem($line_item_HOSTEL_FEB)
        ->calculateAmountBeforeTax()
        ->applyTax($this->tax)
        ->create()
        ->calculateEligibleDiscounts()
        ->addDiscountLineItems()
        ->applyAdvanceOffset()
        ->createAdvanceOffsetTransactions()
        ->recalculateAndUpdateAfterDiscount();

    $created_billing_document = $service->getBillingDocument();

    $collection_unpaid_items = collect([$unpaid_item_SCHOOL_JAN, $unpaid_item_HOSTEL_JAN]);

    app()->make(UnpaidItemService::class)
        ->setUnpaidItems($collection_unpaid_items)
        ->assignBillingDocument($created_billing_document)
        ->save();


    $filters = [
        'bill_to_type' => Userable::USER_TYPE_MAPPING[get_class($student)],
        'bill_to_id' => $student->id,
//        'status' => UnpaidItem::STATUS_PENDING,
        'period_from' => '2025-01-01',
        'period_to' => '2025-12-01',
        'order_by' => [
            'period' => 'ASC',
            'description' => 'ASC'
        ]
    ];

    $response = $this->getJson(
        route('accounting.fees.index-unpaid-item', $filters)
    )->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toHaveCount(4)
        ->and($response['data'][0]['description'])->toEqual('Hostel Fees Jan 2025')
        ->and($response['data'][0]['status'])->toEqual(UnpaidItem::STATUS_PENDING)
        ->and($response['data'][0]['amount_before_tax'])->toEqual(250) // original amount
        ->and($response['data'][0]['amount_before_tax_after_discount'])->toEqual(185) // total discount  = 65 (hostel_discount_1) = 65 , total amount after discount = 250 - 65 = 185

        ->and($response['data'][1]['description'])->toEqual('School Fees Jan 2025')
        ->and($response['data'][1]['status'])->toEqual(UnpaidItem::STATUS_PENDING)
        ->and($response['data'][1]['amount_before_tax'])->toEqual(500) // original amount
        ->and($response['data'][1]['amount_before_tax_after_discount'])->toEqual(425) // total discount  = 50 (school_discount_1) + 25 (school_discount_2) = 75 , total amount after discount = 500 - 75 = 425

        ->and($response['data'][2]['description'])->toEqual('Hostel Fees Feb 2025')
        ->and($response['data'][2]['status'])->toEqual(UnpaidItem::STATUS_UNPAID)
        ->and($response['data'][2]['amount_before_tax'])->toEqual(250) // original amount
        ->and($response['data'][2]['amount_before_tax_after_discount'])->toEqual(185) // total discount  = 65 (hostel_discount_1) = 65 , total amount after discount = 250 - 65 = 185

        ->and($response['data'][3]['description'])->toEqual('School Fees Feb 2025')
        ->and($response['data'][3]['status'])->toEqual(UnpaidItem::STATUS_UNPAID)
        ->and($response['data'][3]['amount_before_tax'])->toEqual(500) // original amount
        ->and($response['data'][3]['amount_before_tax_after_discount'])->toEqual(425) // total discount  = 50 (school_discount_1) + 25 (school_discount_2) = 75 , total amount after discount = 500 - 75 = 425
    ;


    /**
     * pay the billing document
     *
     * total billing document amount = 185 + 425 + 185 + 425 = 1220
     */


    $created_billing_document->refresh();

    /** @var PaymentRequestService */
    $payment_request_service = app()->make(PaymentRequestService::class);

    $payment_request = $payment_request_service
        ->setUserable($student)
        ->setBillingDocument($created_billing_document)
        ->setPaymentMethod(PaymentMethod::first())
        ->setStatus(PaymentRequest::STATUS_APPROVED)
        ->setAmount($created_billing_document->refresh()->amount_after_tax)
        ->setBankId(null)
        ->setPaymentReferenceNo($created_billing_document->reference_no)
        ->setApprovedAt(now())
        ->setApprovedByEmployee(Employee::first())
        ->create()
        ->getPaymentRequest();


    /** @var PaymentService */
    $payment_service = app()->make(PaymentService::class);

    $payment_service
        ->setBillingDocument($created_billing_document->refresh())
        ->setPaymentRequestAndPopulateData($payment_request)
        ->setRemarks('PAID')
        ->create()
        ->triggerPostPaymentProcesses();

    $created_billing_document->refresh();

    // trigger event listener
    $event = new InvoicePaidEvent($created_billing_document);
    $listener = new MarkBillableItemAsPaid();

    $listener->handle($event);


    expect($created_billing_document->refresh()->payment_status)->toEqual(BillingDocument::PAYMENT_STATUS_PAID);
    expect($unpaid_item_SCHOOL_JAN->refresh()->status)->toEqual(UnpaidItem::STATUS_PAID);
    expect($unpaid_item_SCHOOL_FEB->refresh()->status)->toEqual(UnpaidItem::STATUS_UNPAID);
    expect($unpaid_item_HOSTEL_JAN->refresh()->status)->toEqual(UnpaidItem::STATUS_PAID);
    expect($unpaid_item_HOSTEL_FEB->refresh()->status)->toEqual(UnpaidItem::STATUS_UNPAID);


    /**
     *
     * filter UNPAID ITEM for PAID status
     *
     */


    $payload = [
        'bill_to_type' => Userable::USER_TYPE_MAPPING[get_class($student)],
        'bill_to_id' => $student->id,
        'period_from' => '2025-01-01',
        'period_to' => '2025-12-01',
        'order_by' => [
            'period' => 'ASC',
            'name' => 'ASC'
        ]
    ];

    $response = $this->getJson(
        route('accounting.fees.index-unpaid-item', $payload)
    )->json();


    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toHaveCount(4)
        ->and($response['data'][0]['description'])->toEqual('Hostel Fees Jan 2025')
        ->and($response['data'][0]['status'])->toEqual(UnpaidItem::STATUS_PAID)
        ->and($response['data'][0]['amount_before_tax'])->toEqual(250) // original amount
        ->and($response['data'][0]['amount_before_tax_after_discount'])->toEqual(185) // total discount  = 65 (hostel_discount_1) = 65 , total amount after discount = 250 - 65 = 185

        ->and($response['data'][1]['description'])->toEqual('School Fees Jan 2025')
        ->and($response['data'][1]['status'])->toEqual(UnpaidItem::STATUS_PAID)
        ->and($response['data'][1]['amount_before_tax'])->toEqual(500) // original amount
        ->and($response['data'][1]['amount_before_tax_after_discount'])->toEqual(425) // total discount  = 50 (school_discount_1) + 25 (school_discount_2) = 75 , total amount after discount = 500 - 75 = 425

        ->and($response['data'][2]['description'])->toEqual('Hostel Fees Feb 2025')
        ->and($response['data'][2]['status'])->toEqual(UnpaidItem::STATUS_UNPAID)
        ->and($response['data'][2]['amount_before_tax'])->toEqual(250) // original amount
        ->and($response['data'][2]['amount_before_tax_after_discount'])->toEqual(185) // total discount  = 65 (hostel_discount_1) = 65 , total amount after discount = 250 - 65 = 185

        ->and($response['data'][3]['description'])->toEqual('School Fees Feb 2025')
        ->and($response['data'][3]['status'])->toEqual(UnpaidItem::STATUS_UNPAID)
        ->and($response['data'][3]['amount_before_tax'])->toEqual(500) // original amount
        ->and($response['data'][3]['amount_before_tax_after_discount'])->toEqual(425) // total discount  = 50 (school_discount_1) + 25 (school_discount_2) = 75 , total amount after discount = 500 - 75 = 425
    ;
});


test('createBillingDocument success', function () {
    $student = Student::factory()->create();
    $user = $student->user;

    $user->syncPermissions(['fees-unpaid-item-pay']);

    Sanctum::actingAs($user);

    $unpaid_items = UnpaidItem::factory(2)->create([
        'bill_to_type' => Student::class,
        'bill_to_id' => $student->id,
        'status' => UnpaidItem::STATUS_UNPAID,
        'gl_account_code' => GlAccount::CODE_SCHOOL_FEES,
        'amount_before_tax' => 100, // total for 2 unpaid items is 200
    ]);

    $this->assertDatabaseCount('billing_documents', 0);
    $this->assertDatabaseCount('billing_document_line_items', 0);

    $payload = [
        'unpaid_item_ids' => [
            $unpaid_items[0]->id,
            $unpaid_items[1]->id,
        ],
    ];

    $expected_billing_document = BillingDocument::factory()->create([
        'bill_to_type' => Student::class,
        'bill_to_id' => $student->id,
        'currency_code' => $this->currencyCode,
        'amount_before_tax' => 200,
        'amount_before_tax_after_less_advance' => 200,
        'amount_after_tax' => 200,
        'tax_amount' => 0,
        'status' => BillingDocument::STATUS_CONFIRMED,
        'payment_status' => BillingDocument::PAYMENT_STATUS_UNPAID,
    ]);

    BillingDocumentLineItem::factory()->create([
        'billing_document_id' => $expected_billing_document->id,
        'gl_account_code' => $unpaid_items[0]->gl_account_code,
        'description' => $unpaid_items[0]->description,
        'amount_before_tax' => $unpaid_items[0]->amount_before_tax,
        'product_id' => $unpaid_items[0]->product_id,
        'currency_code' => $unpaid_items[0]->currency_code,
        'quantity' => $unpaid_items[0]->quantity,
        'unit_price' => $unpaid_items[0]->unit_price,
        'billable_item_type' => get_class($unpaid_items[0]),
        'billable_item_id' => $unpaid_items[0]->id,
        'is_discount' => false,
        'offset_billing_document_id' => null,
    ]);

    BillingDocumentLineItem::factory()->create([
        'billing_document_id' => $expected_billing_document->id,
        'gl_account_code' => $unpaid_items[1]->gl_account_code,
        'description' => $unpaid_items[1]->description,
        'amount_before_tax' => $unpaid_items[1]->amount_before_tax,
        'product_id' => $unpaid_items[1]->product_id,
        'currency_code' => $unpaid_items[1]->currency_code,
        'quantity' => $unpaid_items[1]->quantity,
        'unit_price' => $unpaid_items[1]->unit_price,
        'billable_item_type' => get_class($unpaid_items[1]),
        'billable_item_id' => $unpaid_items[1]->id,
        'is_discount' => false,
        'offset_billing_document_id' => null,
    ]);

    $this->mock(AccountingService::class, function (MockInterface $mock) use ($user, $payload, $expected_billing_document) {
        $mock->shouldReceive('setUser')->once()->withArgs(function ($value) use (&$user) {
            return $value->id == $user->id;
        })->andReturnSelf();
        $mock->shouldReceive('setUnpaidItems')->once()->andReturnSelf();
        $mock->shouldReceive('validateUserCreateBillingDocument')->once()->andReturnSelf();
        $mock->shouldReceive('createBillingDocumentFromUnpaidItem')->once()->andReturn($expected_billing_document->loadMissing('lineItems'));
    });

    $response = $this->postJson(route('accounting.fees.create-billing-document-from-unpaid-item'), $payload)->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toEqual([
            'id' => $expected_billing_document->id,
            'reference_number' => $expected_billing_document->reference_no,
            'document_date' => $expected_billing_document->document_date,
            'paid_at' => $expected_billing_document->paid_at,
            'type' => $expected_billing_document->type,
            'sub_type' => $expected_billing_document->sub_type,
            'status' => $expected_billing_document->status,
            'payment_status' => $expected_billing_document->payment_status,
            'legal_entity_name' => $expected_billing_document->legal_entity_name,
            'legal_entity_address' => $expected_billing_document->legal_entity_address,
            'bill_to_name' => $expected_billing_document->bill_to_name,
            'bill_to_address' => $expected_billing_document->bill_to_address,
            'bill_to_reference_number' => $expected_billing_document->bill_to_reference_number,
            'tax_code' => $expected_billing_document->tax_code,
            'tax_description' => $expected_billing_document->tax_description,
            'tax_percentage' => $expected_billing_document->tax_percentage,
            'payment_due_date' => $expected_billing_document->payment_due_date,
            'remit_to_account_number' => $expected_billing_document->remit_to_account_number,
            'remit_to_account_name' => $expected_billing_document->remit_to_account_name,
            'remit_to_bank_name' => $expected_billing_document->remit_to_bank_name,
            'remit_to_bank_address' => $expected_billing_document->remit_to_bank_address,
            'remit_to_swift_code' => $expected_billing_document->remit_to_swift_code,
            'currency_code' => $expected_billing_document->currency_code,
            'amount_before_tax' => $expected_billing_document->amount_before_tax,
            'amount_before_tax_after_less_advance' => $expected_billing_document->amount_before_tax_after_less_advance,
            'tax_amount' => $expected_billing_document->tax_amount,
            'amount_after_tax' => $expected_billing_document->amount_after_tax,
            'receipt_url' => $expected_billing_document->receipt_url,
            'line_items' => resourceToArray(BillingDocumentLineItemResource::collection($expected_billing_document->lineItems)),
        ]);
});

test('createBillingDocument with discount from scholarship success', function () {
    Carbon::setTestNow('2024-10-05');

    $currency = Currency::where('code', config('school.currency_code'))->first();

    $student = Student::factory()->create();
    $user = $student->user;

    $user->syncPermissions(['fees-unpaid-item-pay']);

    Sanctum::actingAs($user);

    $unpaid_items = UnpaidItem::factory(2)->create([
        'bill_to_type' => Student::class,
        'bill_to_id' => $student->id,
        'status' => UnpaidItem::STATUS_UNPAID,
        'gl_account_code' => GlAccount::CODE_SCHOOL_FEES,
        'amount_before_tax' => 100, // total for 2 unpaid items is 200
        'period' => '2024-10-01'
    ]);

    $scholarship = Scholarship::factory()->create([
        'description' => 'Pin Hwa High School High Achievers Scholarship',
    ]);

    $scholarship_award1 = ScholarshipAward::factory()->create([
        'scholarship_id' => $scholarship->id,
        'student_id' => $student->id,
    ]);

    $setting1 = DiscountSetting::factory()->create([
        'userable_id' => $student->id,
        'userable_type' => get_class($student),
        'source_id' => $scholarship_award1->id,
        'source_type' => get_class($scholarship_award1),
        'gl_account_codes' => json_encode([GlAccount::CODE_SCHOOL_FEES]),
        'effective_from' => '2024-01-01',
        'effective_to' => '2024-12-31',
        'is_active' => 1,
        'max_amount' => null,
        'used_amount' => 0,
        'basis' => DiscountSetting::BASIS_PERCENT,
        'basis_amount' => 60 // 60% discount
    ]);

    $this->assertDatabaseCount('billing_documents', 0);
    $this->assertDatabaseCount('billing_document_line_items', 0);

    $payload = [
        'unpaid_item_ids' => [
            $unpaid_items[0]->id, // 100
            $unpaid_items[1]->id, // 100
        ],
    ];

    $response = $this->postJson(route('accounting.fees.create-billing-document-from-unpaid-item'), $payload)->json();

    $this->assertDatabaseCount('payments', 0);
    $this->assertDatabaseCount('billing_documents', 1);
    $this->assertDatabaseCount('billing_document_line_items', 4); // 2 unpaid items, 2 discount

    expect($response)->toHaveSuccessGeneralResponse();

    // total billing document = 200
    // total discount = 200 * 60% = 120
    // total after discount = 200 - 120 = 80

    expect($response['data']['amount_after_tax'])->toBe(80)
        ->and($response['data']['line_items'])->toHaveCount(4)
        ->and($response['data']['line_items'][0]['amount_before_tax'])->toEqual(100) // first unpaid item
        ->and($response['data']['line_items'][1]['amount_before_tax'])->toEqual(100) // second unpaid item
        ->and($response['data']['line_items'][2]['amount_before_tax'])->toEqual(-60) // first discount
        ->and($response['data']['line_items'][3]['amount_before_tax'])->toEqual(-60) // second discount
        ->and($response['data']['payment_status'])->toEqual(BillingDocument::PAYMENT_STATUS_UNPAID);
});

test('createBillingDocument with ADVANCE success', function () {
    $currency = Currency::where('code', config('school.currency_code'))->first();

    $student = Student::factory()->create();
    $user = $student->user;

    $user->syncPermissions(['fees-unpaid-item-pay']);

    Sanctum::actingAs($user);

    $unpaid_items = UnpaidItem::factory(2)->create([
        'bill_to_type' => Student::class,
        'bill_to_id' => $student->id,
        'status' => UnpaidItem::STATUS_UNPAID,
        'gl_account_code' => GlAccount::CODE_SCHOOL_FEES,
        'amount_before_tax' => 100, // total for 2 unpaid items is 200
    ]);

    $advance_invoice_1 = BillingDocument::factory()->create([
        'document_date' => '2024-06-01',
        'reference_no' => 'ADVINV0001',
        'type' => BillingDocument::TYPE_ADVANCE_INVOICE,
        'status' => BillingDocument::STATUS_POSTED,
        'amount_before_tax' => 100, // only 100
        'bill_to_type' => $student->getBillToType(),
        'bill_to_id' => $student->getBillToId(),
        'bill_to_name' => $student->getBillToName(),
        'bill_to_address' => $student->getBillToAddress(),
    ]);

    BillingDocumentAdvanceTransaction::factory()->create([
        'advance_invoice_id' => $advance_invoice_1->id,
        'amount_before_tax' => $advance_invoice_1->amount_before_tax,
        'billable_type' => $advance_invoice_1->billTo->getBillToType(),
        'billable_id' => $advance_invoice_1->billTo->getBillToId(),
        'currency_code' => $advance_invoice_1->currency_code,
        'gl_account_code' => GlAccount::CODE_SCHOOL_FEES,
    ]);

    $payload = [
        'unpaid_item_ids' => [
            $unpaid_items[0]->id, // 100
            $unpaid_items[1]->id, // 100
        ],
    ];

    $response = $this->postJson(route('accounting.fees.create-billing-document-from-unpaid-item'), $payload)->json();

    $this->assertDatabaseCount('payments', 0);
    $this->assertDatabaseCount('billing_documents', 2);
    $this->assertDatabaseCount('billing_document_line_items', 3); // 2 unpaid items, 1 advance

    expect($response)->toHaveSuccessGeneralResponse();

    // total billing document = 200
    // total advance = 100
    // total after advance = 200 - 100 = 100

    expect($response['data']['amount_after_tax'])->toBe(100)
        ->and($response['data']['line_items'])->toHaveCount(3)
        ->and($response['data']['line_items'][0]['amount_before_tax'])->toEqual(100) // first unpaid item
        ->and($response['data']['line_items'][1]['amount_before_tax'])->toEqual(100) // second unpaid item
        ->and($response['data']['line_items'][2]['amount_before_tax'])->toEqual(-100) // advance
        ->and($response['data']['payment_status'])->toEqual(BillingDocument::PAYMENT_STATUS_UNPAID);
});

test('createBillingDocument with ADVANCE + scholarship discount success', function () {
    Carbon::setTestNow('2024-07-01');

    $currency = Currency::where('code', config('school.currency_code'))->first();

    $student = Student::factory()->create();
    $user = $student->user;

    $user->syncPermissions(['fees-unpaid-item-pay']);

    Sanctum::actingAs($user);

    $unpaid_items = UnpaidItem::factory(2)->create([
        'bill_to_type' => Student::class,
        'bill_to_id' => $student->id,
        'status' => UnpaidItem::STATUS_UNPAID,
        'gl_account_code' => GlAccount::CODE_SCHOOL_FEES,
        'amount_before_tax' => 500, // total for 2 unpaid items is 1000
        'period' => '2024-06-01',
    ]);

    // ADVANCE
    $advance_invoice_1 = BillingDocument::factory()->create([
        'document_date' => '2024-06-01',
        'reference_no' => 'ADVINV0001',
        'type' => BillingDocument::TYPE_ADVANCE_INVOICE,
        'status' => BillingDocument::STATUS_POSTED,
        'amount_before_tax' => 200, // only 200
        'bill_to_type' => $student->getBillToType(),
        'bill_to_id' => $student->getBillToId(),
        'bill_to_name' => $student->getBillToName(),
        'bill_to_address' => $student->getBillToAddress(),
    ]);

    BillingDocumentAdvanceTransaction::factory()->create([
        'advance_invoice_id' => $advance_invoice_1->id,
        'amount_before_tax' => $advance_invoice_1->amount_before_tax,
        'billable_type' => $advance_invoice_1->billTo->getBillToType(),
        'billable_id' => $advance_invoice_1->billTo->getBillToId(),
        'currency_code' => $advance_invoice_1->currency_code,
        'gl_account_code' => GlAccount::CODE_SCHOOL_FEES,
    ]);

    // SCHOLARSHIP
    $scholarship = Scholarship::factory()->create([
        'description' => 'Pin Hwa High School High Achievers Scholarship',
    ]);

    $scholarship_award1 = ScholarshipAward::factory()->create([
        'scholarship_id' => $scholarship->id,
        'student_id' => $student->id,
    ]);

    $setting1 = DiscountSetting::factory()->create([
        'userable_id' => $student->id,
        'userable_type' => get_class($student),
        'source_id' => $scholarship_award1->id,
        'source_type' => get_class($scholarship_award1),
        'gl_account_codes' => json_encode([GlAccount::CODE_SCHOOL_FEES]),
        'effective_from' => '2024-01-01',
        'effective_to' => '2024-12-31',
        'is_active' => 1,
        'max_amount' => null,
        'used_amount' => 0,
        'basis' => DiscountSetting::BASIS_PERCENT,
        'basis_amount' => 60 // 60% discount
    ]);

    $payload = [
        'unpaid_item_ids' => [
            $unpaid_items[0]->id, // 500
            $unpaid_items[1]->id, // 500
        ],
    ];

    $response = $this->postJson(route('accounting.fees.create-billing-document-from-unpaid-item'), $payload)->json();

    // total 1000
    // discounted_1 amount = line item 1 = 500 * 60% = 300
    // discounted_2 amount = line item 2 = 500 * 60% = 300
    // advance 200
    // amount : 1000 (total) - 300 (discounted_1) - 300 (discounted_2) - 200 (advance) = 200

    $this->assertDatabaseCount('payments', 0);
    $this->assertDatabaseCount('billing_documents', 2); // 1 advance, 1 newly created
    $this->assertDatabaseCount('billing_document_line_items', 5); // 2 unpaid items, 2 discount, 1 advance

    expect($response)->toHaveSuccessGeneralResponse();

    expect($response['data']['amount_after_tax'])->toBe(200)
        ->and($response['data']['line_items'])->toHaveCount(5)
        ->and($response['data']['line_items'][0]['amount_before_tax'])->toEqual(500) // first unpaid item
        ->and($response['data']['line_items'][1]['amount_before_tax'])->toEqual(500) // second unpaid item
        ->and($response['data']['line_items'][2]['amount_before_tax'])->toEqual(-300) // first discount
        ->and($response['data']['line_items'][3]['amount_before_tax'])->toEqual(-300) // second discount
        ->and($response['data']['line_items'][4]['amount_before_tax'])->toEqual(-200) // advance
        ->and($response['data']['payment_status'])->toEqual(BillingDocument::PAYMENT_STATUS_UNPAID);
});

test('createBillingDocument with full ADVANCE success', function () {
    Carbon::setTestNow('2024-07-01');

    $currency = Currency::where('code', config('school.currency_code'))->first();

    $student = Student::factory()->create();
    $user = $student->user;

    $user->syncPermissions(['fees-unpaid-item-pay']);

    Sanctum::actingAs($user);

    $unpaid_items = UnpaidItem::factory(2)->create([
        'bill_to_type' => Student::class,
        'bill_to_id' => $student->id,
        'status' => UnpaidItem::STATUS_UNPAID,
        'gl_account_code' => GlAccount::CODE_SCHOOL_FEES,
        'amount_before_tax' => 500, // total for 2 unpaid items is 1000
        'period' => '2024-06-01',
    ]);

    // ADVANCE
    $advance_invoice_1 = BillingDocument::factory()->create([
        'document_date' => '2024-06-01',
        'reference_no' => 'ADVINV0001',
        'type' => BillingDocument::TYPE_ADVANCE_INVOICE,
        'status' => BillingDocument::STATUS_POSTED,
        'amount_before_tax' => 1000, // full amount
        'bill_to_type' => $student->getBillToType(),
        'bill_to_id' => $student->getBillToId(),
        'bill_to_name' => $student->getBillToName(),
        'bill_to_address' => $student->getBillToAddress(),
    ]);

    BillingDocumentAdvanceTransaction::factory()->create([
        'advance_invoice_id' => $advance_invoice_1->id,
        'amount_before_tax' => $advance_invoice_1->amount_before_tax,
        'billable_type' => $advance_invoice_1->billTo->getBillToType(),
        'billable_id' => $advance_invoice_1->billTo->getBillToId(),
        'currency_code' => $advance_invoice_1->currency_code,
        'gl_account_code' => GlAccount::CODE_SCHOOL_FEES,
    ]);

    $payload = [
        'unpaid_item_ids' => [
            $unpaid_items[0]->id, // 500
            $unpaid_items[1]->id, // 500
        ],
    ];

    $response = $this->postJson(route('accounting.fees.create-billing-document-from-unpaid-item'), $payload)->json();

    // total 1000
    // advance 1000
    // amount : 1000 (total) - 1000 (advance) = 0

    $this->assertDatabaseCount('payments', 1); // amount after tax is 0, it will be auto paid
    $this->assertDatabaseCount('billing_documents', 2); // 1 advance, 1 newly created
    $this->assertDatabaseCount('billing_document_line_items', 3); // 2 unpaid items, 1 advance

    expect($response)->toHaveSuccessGeneralResponse();

    expect($response['data']['amount_after_tax'])->toBe(0)
        ->and($response['data']['line_items'])->toHaveCount(3)
        ->and($response['data']['line_items'][0]['amount_before_tax'])->toEqual(500) // first unpaid item
        ->and($response['data']['line_items'][1]['amount_before_tax'])->toEqual(500) // second unpaid item
        ->and($response['data']['line_items'][2]['amount_before_tax'])->toEqual(-1000) // advance
        ->and($response['data']['payment_status'])->toEqual(BillingDocument::PAYMENT_STATUS_PAID);
});

test('createBillingDocument with amount after tax is 0, full discount + advance', function () {
    Carbon::setTestNow('2024-07-01');

    $currency = Currency::where('code', config('school.currency_code'))->first();

    $student = Student::factory()->create();
    $user = $student->user;

    $user->syncPermissions(['fees-unpaid-item-pay']);

    Sanctum::actingAs($user);

    $unpaid_items = UnpaidItem::factory(2)->create([
        'bill_to_type' => Student::class,
        'bill_to_id' => $student->id,
        'status' => UnpaidItem::STATUS_UNPAID,
        'gl_account_code' => GlAccount::CODE_SCHOOL_FEES,
        'amount_before_tax' => 500, // total for 2 unpaid items is 1000
        'period' => '2024-06-01',
    ]);

    // ADVANCE
    $advance_invoice_1 = BillingDocument::factory()->create([
        'document_date' => '2024-06-01',
        'reference_no' => 'ADVINV0001',
        'type' => BillingDocument::TYPE_ADVANCE_INVOICE,
        'status' => BillingDocument::STATUS_POSTED,
        'amount_before_tax' => 800, // 800
        'bill_to_type' => $student->getBillToType(),
        'bill_to_id' => $student->getBillToId(),
        'bill_to_name' => $student->getBillToName(),
        'bill_to_address' => $student->getBillToAddress(),
    ]);

    BillingDocumentAdvanceTransaction::factory()->create([
        'advance_invoice_id' => $advance_invoice_1->id,
        'amount_before_tax' => $advance_invoice_1->amount_before_tax,
        'billable_type' => $advance_invoice_1->billTo->getBillToType(),
        'billable_id' => $advance_invoice_1->billTo->getBillToId(),
        'currency_code' => $advance_invoice_1->currency_code,
        'gl_account_code' => GlAccount::CODE_SCHOOL_FEES,
    ]);


    $scholarship = Scholarship::factory()->create([
        'description' => 'Pin Hwa High School High Achievers Scholarship',
    ]);

    $scholarship_award1 = ScholarshipAward::factory()->create([
        'scholarship_id' => $scholarship->id,
        'student_id' => $student->id,
    ]);

    $setting1 = DiscountSetting::factory()->create([
        'userable_id' => $student->id,
        'userable_type' => get_class($student),
        'source_id' => $scholarship_award1->id,
        'source_type' => get_class($scholarship_award1),
        'gl_account_codes' => json_encode([GlAccount::CODE_SCHOOL_FEES]),
        'effective_from' => '2024-01-01',
        'effective_to' => '2024-12-31',
        'is_active' => 1,
        'max_amount' => null,
        'used_amount' => 0,
        'basis' => DiscountSetting::BASIS_FIXED_AMOUNT,
        'basis_amount' => 100 // 100 discount
    ]);

    $payload = [
        'unpaid_item_ids' => [
            $unpaid_items[0]->id, // 500
            $unpaid_items[1]->id, // 500
        ],
    ];

    $response = $this->postJson(route('accounting.fees.create-billing-document-from-unpaid-item'), $payload)->json();

    // total 1000
    // discounted_1 amount = line item 1 = 100
    // discounted_2 amount = line item 2 = 100
    // advance 800
    // amount_after_tax = 0

    $this->assertDatabaseCount('payments', 1); // auto paid when amount after tax is 0
    $this->assertDatabaseCount('billing_documents', 2); // 1 advance, 1 newly created
    $this->assertDatabaseCount('billing_document_line_items', 5); // 2 unpaid items, 2 discounts, 1 advance

    expect($response)->toHaveSuccessGeneralResponse();

    expect($response['data']['amount_after_tax'])->toBe(0)
        ->and($response['data']['line_items'])->toHaveCount(5)
        ->and($response['data']['line_items'][0]['amount_before_tax'])->toEqual(500) // first unpaid item
        ->and($response['data']['line_items'][1]['amount_before_tax'])->toEqual(500) // second unpaid item
        ->and($response['data']['line_items'][2]['amount_before_tax'])->toEqual(-100) // first discount
        ->and($response['data']['line_items'][3]['amount_before_tax'])->toEqual(-100) // second discount
        ->and($response['data']['line_items'][4]['amount_before_tax'])->toEqual(-800) // advance
        ->and($response['data']['payment_status'])->toEqual(BillingDocument::PAYMENT_STATUS_PAID);
});

test('createBillingDocument failed because of validation', function () {
    /**
     * empty payload
     */
    $response = $this->postJson(route('accounting.fees.create-billing-document-from-unpaid-item'), [])->json();

    expect($response)->toHaveFailedGeneralResponse()
        ->and($response['error'])->toMatchArray([
            'unpaid_item_ids' => [
                'The unpaid item ids field is required.',
            ],
        ]);

    /**
     * invalid unpaid_items id
     */
    $response = $this->postJson(route('accounting.fees.create-billing-document-from-unpaid-item'), [
        'unpaid_item_ids' => [1, 2, 3]
    ])->json();

    expect($response)->toHaveFailedGeneralResponse()
        ->and($response['error'])->toMatchArray([
            'unpaid_item_ids' => [
                'The selected unpaid item ids is invalid.',
            ],
        ]);

    $this->assertDatabaseCount('payment_gateway_logs', 0);
    $this->assertDatabaseCount('billing_documents', 0);
    $this->assertDatabaseCount('billing_document_line_items', 0);
});

test('createBillingDocumentAdmin', function () {
    Carbon::setTestNow('2024-07-01');

    // $this->user is now Employee
    Employee::factory()->create([
        'user_id' => $this->user->id,
    ]);

    $student = Student::factory()->create();
    $user = $student->user;

    $unpaid_items = UnpaidItem::factory(2)->create([
        'bill_to_type' => Student::class,
        'bill_to_id' => $student->id,
        'status' => UnpaidItem::STATUS_UNPAID,
        'gl_account_code' => GlAccount::CODE_SCHOOL_FEES,
        'amount_before_tax' => 100, // total for 2 unpaid items is 200
        'period' => '2024-06-01',
    ]);

    $expected_billing_document = BillingDocument::factory()->create([
        'bill_to_type' => Student::class,
        'bill_to_id' => $student->id,
        'currency_code' => $this->currencyCode,
        'amount_before_tax' => 200,
        'amount_before_tax_after_less_advance' => 200,
        'amount_after_tax' => 200,
        'tax_amount' => 0,
        'status' => BillingDocument::STATUS_CONFIRMED,
        'payment_status' => BillingDocument::PAYMENT_STATUS_UNPAID,
    ]);

    BillingDocumentLineItem::factory()->create([
        'billing_document_id' => $expected_billing_document->id,
        'gl_account_code' => $unpaid_items[0]->gl_account_code,
        'description' => $unpaid_items[0]->description,
        'amount_before_tax' => $unpaid_items[0]->amount_before_tax,
        'product_id' => $unpaid_items[0]->product_id,
        'currency_code' => $unpaid_items[0]->currency_code,
        'quantity' => $unpaid_items[0]->quantity,
        'unit_price' => $unpaid_items[0]->unit_price,
        'billable_item_type' => get_class($unpaid_items[0]),
        'billable_item_id' => $unpaid_items[0]->id,
        'is_discount' => false,
        'offset_billing_document_id' => null,
    ]);

    BillingDocumentLineItem::factory()->create([
        'billing_document_id' => $expected_billing_document->id,
        'gl_account_code' => $unpaid_items[1]->gl_account_code,
        'description' => $unpaid_items[1]->description,
        'amount_before_tax' => $unpaid_items[1]->amount_before_tax,
        'product_id' => $unpaid_items[1]->product_id,
        'currency_code' => $unpaid_items[1]->currency_code,
        'quantity' => $unpaid_items[1]->quantity,
        'unit_price' => $unpaid_items[1]->unit_price,
        'billable_item_type' => get_class($unpaid_items[1]),
        'billable_item_id' => $unpaid_items[1]->id,
        'is_discount' => false,
        'offset_billing_document_id' => null,
    ]);

    $payload = [
        'unpaid_item_ids' => [
            $unpaid_items[0]->id, // 100
            $unpaid_items[1]->id, // 100
        ],
    ];

    $this->mock(AccountingService::class, function (MockInterface $mock) use ($payload, $expected_billing_document) {
        $mock->shouldReceive('setUser')->once()->withArgs(function ($value) {
            return $value->id == $this->user->id;
        })->andReturnSelf();
        $mock->shouldReceive('setUnpaidItems')->once()->andReturnSelf();
        $mock->shouldReceive('validateAdminCreateBillingDocument')->once()->andReturnSelf();
        $mock->shouldReceive('createBillingDocumentFromUnpaidItem')->once()->andReturn($expected_billing_document->loadMissing('lineItems'));
    });

    $response = $this->postJson(route('admin.accounting.fees.create-billing-document-admin'), $payload)->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toEqual([
            'id' => $expected_billing_document->id,
            'reference_number' => $expected_billing_document->reference_no,
            'document_date' => $expected_billing_document->document_date,
            'paid_at' => $expected_billing_document->paid_at,
            'type' => $expected_billing_document->type,
            'sub_type' => $expected_billing_document->sub_type,
            'status' => $expected_billing_document->status,
            'payment_status' => $expected_billing_document->payment_status,
            'legal_entity_name' => $expected_billing_document->legal_entity_name,
            'legal_entity_address' => $expected_billing_document->legal_entity_address,
            'bill_to_name' => $expected_billing_document->bill_to_name,
            'bill_to_address' => $expected_billing_document->bill_to_address,
            'bill_to_reference_number' => $expected_billing_document->bill_to_reference_number,
            'tax_code' => $expected_billing_document->tax_code,
            'tax_description' => $expected_billing_document->tax_description,
            'tax_percentage' => $expected_billing_document->tax_percentage,
            'payment_due_date' => $expected_billing_document->payment_due_date,
            'remit_to_account_number' => $expected_billing_document->remit_to_account_number,
            'remit_to_account_name' => $expected_billing_document->remit_to_account_name,
            'remit_to_bank_name' => $expected_billing_document->remit_to_bank_name,
            'remit_to_bank_address' => $expected_billing_document->remit_to_bank_address,
            'remit_to_swift_code' => $expected_billing_document->remit_to_swift_code,
            'currency_code' => $expected_billing_document->currency_code,
            'amount_before_tax' => $expected_billing_document->amount_before_tax,
            'amount_before_tax_after_less_advance' => $expected_billing_document->amount_before_tax_after_less_advance,
            'tax_amount' => $expected_billing_document->tax_amount,
            'amount_after_tax' => $expected_billing_document->amount_after_tax,
            'receipt_url' => $expected_billing_document->receipt_url,
            'line_items' => resourceToArray(BillingDocumentLineItemResource::collection($expected_billing_document->lineItems)),
        ]);
});

test('printUnpaidItems : test pdf content, no discount', function () {
    $student = Student::factory()->create([
        'name->en' => 'Jon',
        'name->zh' => '张三',
        'student_number' => '0001',
        'nric' => '*********',
    ]);

    $semester_setting = SemesterSetting::factory()->create([
        'name' => 'Semester 1',
        'is_current_semester' => true,
    ]);

    $class = ClassModel::factory()->create([
        'name->en' => 'J111',
        'name->zh' => '一年1班',
        'type' => ClassType::PRIMARY,
    ]);

    $sem_class = SemesterClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'class_id' => $class->id // J111
    ]);

    StudentClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'semester_class_id' => $sem_class->id, // J111
        'student_id' => $student->id,
        'class_enter_date' => now()->toDateString()
    ]);

    $unpaid_items = UnpaidItem::factory(5)->create([
        'bill_to_type' => Student::class,
        'bill_to_id' => $student->id,
        'status' => UnpaidItem::STATUS_UNPAID,
        'amount_before_tax' => 100,
        'description' => 'School Fees ' . fake()->uuid(),
    ]);

    $payload = [
        'bill_to_type' => Student::class,
        'bill_to_id' => $student->id,
        'status' => UnpaidItem::STATUS_UNPAID,
    ];

    // get unpaid items data for the student - no discount
    $data = resolve(UnpaidItemService::class)
        ->setBillToParty($student)
        ->getAllUnpaidItemsWithDiscount($payload);

    $report_data = [
        'data' => $data,
        'bill_to' => $student->loadMissing(['currentSemesterPrimaryClass.semesterClass.classModel', 'currentSemesterPrimaryClass.semesterSetting']),
    ];

    $report_view_name = 'templates.unpaid-items-list';
    $file_name = 'unpaid-items-list';

    $export_type = ExportType::PDF;
    $report_view = view($report_view_name, $report_data);

    SnappyPdf::fake();

    $adapter = ExportAdapterFactory::getAdapterFor($export_type);
    $adapter->setReportBuilder(new GenericExcelExportViaView($report_view))
        ->setReportViewName($report_view_name)
        ->setReportData($report_data);

    $this->reportPrintService->setExportFileAdapter($adapter)
        ->setFileName($file_name)
        ->generate(); // generate pdf

    $expected_headers = ['No', 'Name', 'Month', 'Amount (MYR)'];

    SnappyPdf::assertFileNameIs(storage_path('app' . DIRECTORY_SEPARATOR . DocumentPrintService::PRINT_FOLDER . DIRECTORY_SEPARATOR . $file_name . '.pdf'));
    SnappyPdf::assertViewIs($report_view_name);

    foreach ($expected_headers as $header) {
        SnappyPdf::assertSee($header);
    }

    SnappyPdf::assertSee(e($report_data['bill_to']->getTranslation('name', 'en')));
    SnappyPdf::assertSee(e($report_data['bill_to']->getTranslation('name', 'zh')));
    SnappyPdf::assertSee($report_data['bill_to']->student_number);
    SnappyPdf::assertSee($report_data['bill_to']->nric);
    SnappyPdf::assertSee(e($report_data['bill_to']->currentSemesterPrimaryClass->semesterClass->classModel->getTranslation('name', 'en'))); // class name
    SnappyPdf::assertSee(e($report_data['bill_to']->currentSemesterPrimaryClass->semesterSetting->name)); // semester setting name
    SnappyPdf::assertSee('No'); // for hostel

    $i = 1;
    foreach ($data as $unpaid_item) {
        SnappyPdf::assertSee($i); // No.
        SnappyPdf::assertSee($unpaid_item->description);
        SnappyPdf::assertSee(Carbon::parse($unpaid_item->period)->format('F Y'));
        SnappyPdf::assertSee($unpaid_item->amount_before_tax); // orignal amount

        $i++;
    }

    // $html = view($report_view_name, $report_data)->render();
});

test('printUnpaidItems : test pdf content, with discount', function () {
    $student = Student::factory()->create([
        'name->en' => 'Jon',
        'name->zh' => '张三',
        'student_number' => '0001',
        'nric' => '*********',
    ]);

    $semester_setting = SemesterSetting::factory()->create([
        'name' => 'Semester 1',
        'is_current_semester' => true,
    ]);

    $class = ClassModel::factory()->create([
        'name->en' => 'J111',
        'name->zh' => '一年1班',
        'type' => ClassType::PRIMARY,
    ]);

    $sem_class = SemesterClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'class_id' => $class->id // J111
    ]);

    StudentClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'semester_class_id' => $sem_class->id, // J111
        'student_id' => $student->id,
        'class_enter_date' => now()->toDateString()
    ]);

    $unpaid_items = UnpaidItem::factory(5)->create([
        'period' => '2025-10-01',
        'bill_to_type' => Student::class,
        'bill_to_id' => $student->id,
        'status' => UnpaidItem::STATUS_UNPAID,
        'amount_before_tax' => 100,
        'description' => 'School Fees ' . fake()->uuid(),
        'gl_account_code' => GlAccount::CODE_SCHOOL_FEES,
    ]); // expect all unpaid item to have 50 % discount

    $discount = DiscountSetting::factory()->create([
        'userable_id' => $student->id,
        'userable_type' => get_class($student),
        'gl_account_codes' => json_encode([GlAccount::CODE_SCHOOL_FEES]),
        'effective_from' => '2025-01-01',
        'effective_to' => '2025-12-31',
        'is_active' => 1,
        'max_amount' => 1000,
        'used_amount' => 0,
        'basis' => DiscountSetting::BASIS_PERCENT,
        'basis_amount' => 50
    ]);

    $payload = [
        'bill_to_type' => Student::class,
        'bill_to_id' => $student->id,
        'status' => UnpaidItem::STATUS_UNPAID,
    ];


    // get unpaid items data for the student with discount
    $data = resolve(UnpaidItemService::class)
        ->setBillToParty($student)
        ->getAllUnpaidItemsWithDiscount($payload);

    $report_data = [
        'data' => $data,
        'bill_to' => $student->loadMissing(['currentSemesterPrimaryClass.semesterClass.classModel', 'currentSemesterPrimaryClass.semesterSetting']),
    ];

    $report_view_name = 'templates.unpaid-items-list';
    $file_name = 'unpaid-items-list';

    $export_type = ExportType::PDF;
    $report_view = view($report_view_name, $report_data);

    SnappyPdf::fake();

    $adapter = ExportAdapterFactory::getAdapterFor($export_type);
    $adapter->setReportBuilder(new GenericExcelExportViaView($report_view))
        ->setReportViewName($report_view_name)
        ->setReportData($report_data);

    $this->reportPrintService->setExportFileAdapter($adapter)
        ->setFileName($file_name)
        ->generate(); // generate pdf

    SnappyPdf::assertFileNameIs(storage_path('app' . DIRECTORY_SEPARATOR . DocumentPrintService::PRINT_FOLDER . DIRECTORY_SEPARATOR . $file_name . '.pdf'));
    SnappyPdf::assertViewIs($report_view_name);

    $i = 1;
    foreach ($data as $unpaid_item) {
        SnappyPdf::assertSee($i); // No.
        SnappyPdf::assertSee($unpaid_item->description);
        SnappyPdf::assertSee(Carbon::parse($unpaid_item->period)->format('F Y'));
        SnappyPdf::assertSee(50); // all unpaid item is halved

        $i++;
    }

    $html = view($report_view_name, $report_data)->render();
});

test('printUnpaidItems : download success', function () {
    $student = Student::factory()->create([
        'name->en' => 'Jon',
        'name->zh' => '张三',
        'student_number' => '0001',
        'nric' => '*********',
    ]);

    $payload = [
        'bill_to_type' => Userable::USER_TYPE_MAPPING[Student::class],
        'bill_to_id' => $student->id,
    ];

    $file_name = 'unpaid-items-list';
    $extension = '.pdf';

    SnappyPdf::fake();

    $this->partialMock(ReportPrintService::class, function (MockInterface $mock) use ($file_name, $extension) {
        $mock->shouldReceive('upload')->once()->andReturnSelf();
        $mock->shouldReceive('getFileUrl')->once()->andReturn($file_name . $extension);
    });

    $response = $this->postJson(route('admin.accounting.fees.print-unpaid-items'), $payload)->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data']['url'])->toMatch("/$file_name/")
        ->toEndWith($extension);
});

test('postBillingDocumentsToAutoCount success', function () {

    $employee = Employee::factory()->forUser()->create();
    $user = $employee->user;

    $user->syncPermissions(['post-billing-documents-to-autocount']);

    Sanctum::actingAs($user);

    $payload = [
        'payment_date_from' => '2025-01-01',
        'payment_date_to' => '2025-01-07',
        'type' => BillingDocument::TYPE_INVOICE,
        'sub_type' => BillingDocument::SUB_TYPE_FEES,
        'status' => BillingDocument::STATUS_CONFIRMED,
    ];

    $this->mock(BillingDocumentPostingService::class, function (MockInterface $mock) use ($user, $payload) {
        $mock->shouldReceive('setPaymentDateFrom')->with($payload['payment_date_from'])->once()->andReturnSelf();
        $mock->shouldReceive('setPaymentDateTo')->with($payload['payment_date_to'])->once()->andReturnSelf();
        $mock->shouldReceive('setType')->with($payload['type'])->once()->andReturnSelf();
        $mock->shouldReceive('setSubType')->with($payload['sub_type'])->once()->andReturnSelf();
        $mock->shouldReceive('setStatus')->with($payload['status'])->once()->andReturnSelf();
        $mock->shouldReceive('query')->once()->andReturnSelf();
        $mock->shouldReceive('transform')->once()->andReturnSelf();
        $mock->shouldReceive('generateExcelAndGetFileUrl')->once()->andReturn('https://download-file-here.xlsx');
    });

    $response = $this->postJson(route('admin.accounting.post-to-autocount'), $payload)->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['code'])->toBe(200)
        ->and($response['data']['url'])->toBe('https://download-file-here.xlsx');
});

test('postBillingDocumentsToAutoCount empty', function () {

    $employee = Employee::factory()->forUser()->create();
    $user = $employee->user;

    $user->syncPermissions(['post-billing-documents-to-autocount']);

    Sanctum::actingAs($user);

    $payload = [
        'payment_date_from' => '2025-01-01',
        'payment_date_to' => '2025-01-07',
        'sub_type' => BillingDocument::SUB_TYPE_FEES,
        'status' => BillingDocument::STATUS_CONFIRMED,
    ];

    $this->mock(BillingDocumentPostingService::class, function (MockInterface $mock) use ($user, $payload) {
        $mock->shouldReceive('setPaymentDateFrom')->with($payload['payment_date_from'])->once()->andReturnSelf();
        $mock->shouldReceive('setPaymentDateTo')->with($payload['payment_date_to'])->once()->andReturnSelf();
        $mock->shouldReceive('setSubType')->with($payload['sub_type'])->once()->andReturnSelf();
        $mock->shouldReceive('setStatus')->with($payload['status'])->once()->andReturnSelf();
        $mock->shouldReceive('query')->once()->andReturnSelf();
        $mock->shouldReceive('transform')->once()->andReturnSelf();
        $mock->shouldReceive('generateExcelAndGetFileUrl')->once()->andReturnNull();
    });

    $response = $this->postJson(route('admin.accounting.post-to-autocount'), $payload)->json();

    expect($response)->toHaveFailedGeneralResponse()
        ->and($response['code'])->toBe(404)
        ->and($response['data'])->toBeNull()
        ->and($response['status'])->toBe('ERROR');
});
