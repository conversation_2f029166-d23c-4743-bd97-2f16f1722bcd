<?php

use App\Http\Resources\CourseResource;
use App\Http\Resources\SemesterYearSettingResource;
use App\Models\ClassModel;
use App\Models\ClassSubject;
use App\Models\ClassSubjectContractor;
use App\Models\ClassSubjectTeacher;
use App\Models\Contractor;
use App\Models\Course;
use App\Models\Employee;
use App\Models\SemesterClass;
use App\Models\SemesterSetting;
use App\Models\SemesterYearSetting;
use App\Models\Subject;
use App\Models\User;
use App\Services\SemesterSettingService;
use Database\Seeders\InternationalizationSeeder;
use Database\Seeders\PermissionSeeder;
use Illuminate\Database\Eloquent\Factories\Sequence;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\Hash;
use Laravel\Sanctum\Sanctum;
use Mockery\MockInterface;

beforeEach(function () {
    $this->seed([InternationalizationSeeder::class, PermissionSeeder::class]);

    app()->setLocale('en');

    $this->withHeaders([
        'Accept-Language' => 'en',
    ]);

    $user = User::factory()->create([
        'password' => Hash::make('123456'),
    ]);
    $user->assignRole('Super Admin');

    Sanctum::actingAs($user);

    $this->table = resolve(SemesterSetting::class)->getTable();
    $this->semester_class_table = resolve(SemesterClass::class)->getTable();
    $this->class_subject_table = resolve(ClassSubject::class)->getTable();
    $this->class_subject_contractor_table = resolve(ClassSubjectContractor::class)->getTable();
    $this->class_subject_teacher_table = resolve(ClassSubjectTeacher::class)->getTable();
    $this->routeNamePrefix = 'master-data.semester-settings.';

    $this->course_uec = Course::factory()->create();
    $this->course_igcse = Course::factory()->igcse()->create();

    $this->semester_year_setting_2024 = SemesterYearSetting::create(['year' => 2024]);
    $this->semester_year_setting_2025 = SemesterYearSetting::create(['year' => 2025]);

    $this->uec_first_semester = SemesterSetting::factory()->create([
        'course_id' => $this->course_uec->id,
        'semester_year_setting_id' => $this->semester_year_setting_2024->id,
        'name' => '2024 Semester 1',
        'from' => '2024-01-01',
        'to' => '2024-06-30',
        'is_current_semester' => true
    ]);

    $this->uec_second_semester = SemesterSetting::factory()->create([
        'course_id' => $this->course_uec->id,
        'semester_year_setting_id' => $this->semester_year_setting_2024->id,
        'name' => '2024 Semester 2',
        'from' => '2024-07-01',
        'to' => '2024-12-31',
        'is_current_semester' => false
    ]);

    $this->igcse_first_semester = SemesterSetting::factory()->create([
        'course_id' => $this->course_igcse->id,
        'semester_year_setting_id' => $this->semester_year_setting_2025->id,
        'name' => '2025 Semester 1',
        'from' => '2025-01-01',
        'to' => '2025-06-30',
        'is_current_semester' => true
    ]);

    $this->subjects = Subject::factory(2)->create();
    $this->teacher = Employee::factory()->create();
    $this->contractor = Contractor::factory()->create();
    $this->classes = ClassModel::factory(2)->create();
    $this->igcse_first_semester_classes = SemesterClass::factory(2)
        ->state(new Sequence(
            [
                'semester_setting_id' => $this->igcse_first_semester->id,
                'class_id' => $this->classes[0]->id,
            ],
            [
                'semester_setting_id' => $this->igcse_first_semester->id,
                'class_id' => $this->classes[1]->id,
            ],
        ))->create();

    $this->igcse_first_class_subjects = ClassSubject::factory(2)
        ->state(new Sequence(
            [
                'subject_id' => $this->subjects[1]->id,
                'semester_class_id' => $this->igcse_first_semester_classes[0]->id
            ],
            [
                'subject_id' => $this->subjects[0]->id,
                'semester_class_id' => $this->igcse_first_semester_classes[1]->id
            ],
        ))->create();

    $this->igcse_first_class_subject_contractors = ClassSubjectContractor::factory(2)
        ->state(new Sequence(
            [
                'class_subject_id' => $this->igcse_first_class_subjects[0]->id,
                'contractor_id' => $this->contractor->id,
            ],
            [
                'class_subject_id' => $this->igcse_first_class_subjects[1]->id,
                'contractor_id' => $this->contractor->id,
            ],
        ))->create();

    $this->igcse_first_class_subject_teachers = ClassSubjectTeacher::factory(2)
        ->state(new Sequence(
            [
                'class_subject_id' => $this->igcse_first_class_subjects[0]->id,
                'employee_id' => $this->teacher->id,
            ],
            [
                'class_subject_id' => $this->igcse_first_class_subjects[1]->id,
                'employee_id' => $this->teacher->id,
            ],
        ))->create();
});

test('index: without any params', function () {
    $response = $this->getJson(route($this->routeNamePrefix . 'index'))->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->sequence(
            fn($item) => $item->toHaveKey('id', $this->uec_first_semester->id),
            fn($item) => $item->toHaveKey('id', $this->uec_second_semester->id),
            fn($item) => $item->toHaveKey('id', $this->igcse_first_semester->id),
        );
});

test('index: test service accepting params', function () {
    // Test if all values passes the validation and passes into service
    // Individual filters testing in SemesterSettingRepositoryTest
    $filters = [
        'semester_years' => [$this->semester_year_setting_2024->id],
        'courses' => [$this->course_uec->id],
        'name' => '2024 Semester 1',
        'from' => '2024-01-01',
        'to' => '2024-06-30',
        'is_current_semester' => true,
        'order_by' => [
            'semester_year' => 'asc',
            'course' => [
                'en' => 'asc'
            ],
            'name' => 'asc',
            'from' => 'asc',
            'to' => 'asc',
        ],
        'includes' => ['course', 'semesterYearSetting'],
    ];

    $this->mock(SemesterSettingService::class, function (MockInterface $mock) use ($filters) {
        $mock->shouldReceive('getAllPaginatedSemesterSettings')->with($filters)->once()->andReturn(new LengthAwarePaginator([
            $this->uec_first_semester->load('course', 'semesterYearSetting'),
        ], 1, 1));
    });

    $response = $this->getJson(
        route($this->routeNamePrefix . 'index', $filters)
    )->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toHaveCount(1)
        ->and($response['data'][0])->toMatchArray([
            'id' => $this->uec_first_semester->id,
            'course' => resourceToArray(new CourseResource($this->uec_first_semester->course)),
            'semester_year_setting' => resourceToArray(new SemesterYearSettingResource($this->uec_first_semester->semesterYearSetting)),
            'name' => $this->uec_first_semester->name,
            'from' => $this->uec_first_semester->from,
            'to' => $this->uec_first_semester->to,
            'is_current_semester' => $this->uec_first_semester->is_current_semester,
        ]);
});

test('index - getAll', function () {
    $filters = [
        'per_page' => -1,
        'page' => 1,
    ];

    $response = $this->getJson(route($this->routeNamePrefix . 'index', $filters))->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->sequence(
            fn($item) => $item->toHaveKey('id', $this->uec_first_semester->id),
            fn($item) => $item->toHaveKey('id', $this->uec_second_semester->id),
            fn($item) => $item->toHaveKey('id', $this->igcse_first_semester->id),
        );
});

test('index - getPaginated when per_page is set', function () {
    $this->mock(SemesterSettingService::class, function (MockInterface $mock) {
        $semester_setting = SemesterSetting::factory()->create();

        $mock->shouldReceive('getAllPaginatedSemesterSettings')
            ->once()
            ->andReturn(new LengthAwarePaginator([$semester_setting], 1, 1));
    });

    $response = $this->getJson(route($this->routeNamePrefix . 'index', [
        'per_page' => 10,
        'page' => 1,
    ]));

    $response->assertStatus(200);

    expect($response->json())->toHaveKey('pagination');
});

test('index - getAll when per_page = -1 ', function () {

    $this->mock(SemesterSettingService::class, function (MockInterface $mock) {
        $semester_setting = SemesterSetting::factory(2)->create();

        $mock->shouldReceive('getAllSemesterSettings')->once()->andReturn($semester_setting);
    });

    $response = $this->getJson(route($this->routeNamePrefix . 'index', [
        'per_page' => -1,
        'page' => 1,
    ]));

    $response->assertStatus(200);

    expect($response->json())->not()->toHaveKey('pagination');
});

test('show', function () {
    //test with id exist
    $response = $this->getJson(
        route($this->routeNamePrefix . 'show', [
            'master_semester_setting' => $this->uec_first_semester->id,
        ])
    )->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toEqual([
            'id' => $this->uec_first_semester->id,
            'course' => resourceToArray(new CourseResource($this->uec_first_semester->course)),
            'semester_year_setting' => resourceToArray(new SemesterYearSettingResource($this->uec_first_semester->semesterYearSetting)),
            'name' => $this->uec_first_semester->name,
            'from' => $this->uec_first_semester->from,
            'to' => $this->uec_first_semester->to,
            'is_current_semester' => $this->uec_first_semester->is_current_semester,
        ]);

    //test with id not exist
    $response = $this->getJson(
        route($this->routeNamePrefix . 'show', [
            'master_semester_setting' => 9999,
        ])
    )->json();

    expect($response)->toHaveModelResourceNotFoundResponse();
});

test('create', function () {
    SemesterSetting::truncate();
    $this->assertDatabaseCount($this->table, 0);

    $payload = [
        'course_id' => $this->course_uec->id,
        'semester_year_setting_id' => $this->semester_year_setting_2024->id,
        'name' => '2024 Semester 1',
        'from' => '2024-01-01',
        'to' => '2024-05-01',
        'is_current_semester' => true,
    ];

    $response = $this->postJson(route($this->routeNamePrefix . 'create'), $payload)->json();

    $this->assertDatabaseCount($this->table, 1);
    $semester_setting = SemesterSetting::first();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toMatchArray([
            'id' => $semester_setting->id,
            'course' => resourceToArray(new CourseResource($semester_setting->course)),
            'semester_year_setting' => resourceToArray(new SemesterYearSettingResource($semester_setting->semesterYearSetting)),
            'name' => $payload['name'],
            'from' => $payload['from'],
            'to' => $payload['to'],
            'is_current_semester' => $payload['is_current_semester'],
        ]);

    $this->assertDatabaseCount($this->table, 1);
    $this->assertDatabaseHas($this->table, $payload);
});

test('copy', function () {
    $payload = [
        'from_semester_setting_id' => $this->igcse_first_semester->id,
        'to_semester_setting_id' => $this->uec_second_semester->id,
    ];

    $this->partialMock(SemesterSettingService::class, function (MockInterface $mock) use ($payload) {
        $mock->shouldReceive('setCopyFromSemesterSetting')->with($payload['from_semester_setting_id'])->once()->andReturnSelf();
        $mock->shouldReceive('setCopyToSemesterSetting')->with($payload['to_semester_setting_id'])->once()->andReturnSelf();
        $mock->shouldReceive('copySemesterSetting')->once();
    });

    $response = $this->postJson(route($this->routeNamePrefix . 'copy'), $payload)->json();

    expect($response)->toHaveSuccessGeneralResponse();
});

test('create validation error', function () {
    SemesterSetting::truncate();

    $this->assertDatabaseCount($this->table, 0);

    /**
     *
     *  failed because missing required fields
     *
     */

    $response = $this->postJson(route($this->routeNamePrefix . 'create'), []);

    $response->assertStatus(422);

    $this->assertDatabaseCount($this->table, 0);

    expect($response->json())->toHaveFailedGeneralResponse()
        ->toMatchArray([
            'error' => [
                'course_id' => [
                    'The course id field is required.',
                ],
                'semester_year_setting_id' => [
                    'The year field is required.',
                ],
                'name' => [
                    'The name field is required.',
                ],
                'from' => [
                    'The from field is required.',
                ],
                'to' => [
                    'The to field is required.',
                ],
                'is_current_semester' => [
                    'The is current semester field is required.',
                ],
            ],
            'data' => null
        ]);
});

test('update', function () {
    SemesterSetting::truncate();

    $first_semester = SemesterSetting::factory()->create([
        'course_id' => $this->course_uec->id,
        'semester_year_setting_id' => $this->semester_year_setting_2024->id,
        'name' => '2024 Semester 1',
        'from' => '2024-01-01',
        'to' => '2024-05-01',
        'is_current_semester' => true,
    ]);

    // first update with id exist
    $this->assertDatabaseCount(($this->table), 1);
    $payload = [
        'course_id' => $this->course_igcse->id,
        'semester_year_setting_id' => $this->semester_year_setting_2025->id,
        'name' => '2025 Semester 1',
        'from' => '2025-01-01',
        'to' => '2025-05-01',
        'is_current_semester' => false,
    ];

    $response = $this->putJson(
        route($this->routeNamePrefix . 'update', [
            'master_semester_setting' => $first_semester->id,
        ]),
        $payload
    )->json();

    // Assert response status
    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toEqual([
            'id' => $first_semester->id,
            'course' => resourceToArray(new CourseResource($this->course_igcse)),
            'semester_year_setting' => resourceToArray(new SemesterYearSettingResource($this->semester_year_setting_2025)),
            'name' => $payload['name'],
            'from' => $payload['from'],
            'to' => $payload['to'],
            'is_current_semester' => $payload['is_current_semester'],
        ]);

    // Assert database state after update
    $this->assertDatabaseCount($this->table, 1);
    $this->assertDatabaseHas($this->table, $payload);

    $response = $this->putJson(
        route($this->routeNamePrefix . 'update', [
            'master_semester_setting' => 9999,
        ]),
        $payload
    )->json();

    // Assert response status for not found
    expect($response)->toHaveModelResourceNotFoundResponse();

    // Assert database state not affected
    $this->assertDatabaseCount($this->table, 1);
    $this->assertDatabaseHas($this->table, $payload);
});

test('update validation error', function () {
    SemesterSetting::truncate();

    $this->assertDatabaseCount($this->table, 0);

    $first_semester = SemesterSetting::factory()->create([
        'course_id' => $this->course_uec->id,
        'semester_year_setting_id' => $this->semester_year_setting_2024->id,
        'name' => '2024 Semester 1',
        'from' => '2024-01-01',
        'to' => '2024-05-01',
        'is_current_semester' => false, // initially is not current semester
    ]);
    /**
     *
     *  failed because missing required fields
     *
     */

    $response = $this->putJson(
        route($this->routeNamePrefix . 'update', [
            'master_semester_setting' => $first_semester->id,
        ]),
        []
    );

    $response->assertStatus(422);

    expect($response->json())->toHaveFailedGeneralResponse()
        ->toMatchArray([
            'error' => [
                'course_id' => [
                    'The course id field is required.',
                ],
                'semester_year_setting_id' => [
                    'The year field is required.',
                ],
                'name' => [
                    'The name field is required.',
                ],
                'from' => [
                    'The from field is required.',
                ],
                'to' => [
                    'The to field is required.',
                ],
                'is_current_semester' => [
                    'The is current semester field is required.',
                ],
            ],
            'data' => null
        ]);
});

test('destroy', function () {
    SemesterSetting::truncate();

    $first_semester = SemesterSetting::factory()->create();
    $other_semesters = SemesterSetting::factory(3)->create();

    $this->assertDatabaseCount(($this->table), 4);

    //id not exist
    $response = $this->deleteJson(
        route($this->routeNamePrefix . 'destroy', [
            'master_semester_setting' => 9999,
        ])
    )->json();
    expect($response)->toHaveModelResourceNotFoundResponse();

    $this->assertDatabaseCount(($this->table), 4);

    //delete success
    $response = $this->deleteJson(
        route($this->routeNamePrefix . 'destroy', [
            'master_semester_setting' => $first_semester->id,
        ])
    )->json();
    expect($response)->toHaveSuccessGeneralResponse();

    $this->assertDatabaseCount($this->table, 3);
    $this->assertDatabaseMissing($this->table, [
        'id' => $first_semester->id,
    ]);

    foreach ($other_semesters as $other_semester) {
        $this->assertDatabaseHas($this->table, [
            'id' => $other_semester->id,
        ]);
    }
});
