<?php

use App\Models\Country;
use App\Models\Media;
use App\Models\SchoolProfile;
use App\Models\State;
use App\Models\User;
use Database\Seeders\InternationalizationSeeder;
use Database\Seeders\PermissionSeeder;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Hash;
use Laravel\Sanctum\Sanctum;

beforeEach(function () {
    $this->seed([
        InternationalizationSeeder::class,
        PermissionSeeder::class,
    ]);

    app()->setLocale('en');

    $this->withHeaders([
        'Accept-Language' => 'en'
    ]);

    $user = User::factory()->create([
        'password' => Hash::make('123456'),
    ]);

    $user->assignRole('Super Admin');

    Sanctum::actingAs($user);

    $this->routeNamePrefix = 'master-data.school-profile';
    $this->table = resolve(SchoolProfile::class)->getTable();

    config(['media-library.disk_name' => 'local']);
});

test('index', function () {
    $first_school_profile = SchoolProfile::factory()->create();
    $file = UploadedFile::fake()->create('file.png', 500);

    $first_school_profile->addMedia($file)->toMediaCollection('logo');

    expect(Media::count())->toBeOne();

    $logo = Media::first();
    $logo_path = getMediaFullUrl($logo);

    //Test pattern
    $response = $this->getJson(route($this->routeNamePrefix.'.index'))->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toEqual([
            'id' => $first_school_profile->id,
            'name' => $first_school_profile->name,
            'principal_name' => $first_school_profile->principal_name,
            'translations' => [
                'name' => [
                    'en' => $first_school_profile->getTranslation('name', 'en'),
                    'zh' => $first_school_profile->getTranslation('name', 'zh'),
                ],
                'principal_name' => [
                    'en' => $first_school_profile->getTranslation('principal_name', 'en'),
                    'zh' => $first_school_profile->getTranslation('principal_name', 'zh'),
                ]
            ],
            'short_name' => $first_school_profile->short_name,
            'code' => $first_school_profile->code,
            'address' => $first_school_profile->address,
            'country_id' => $first_school_profile->country_id,
            'state_id' => $first_school_profile->state_id,
            'city' => $first_school_profile->city,
            'postcode' => $first_school_profile->postcode,
            'phone_1' => $first_school_profile->phone_1,
            'phone_2' => $first_school_profile->phone_2,
            'fax_1' => $first_school_profile->fax_1,
            'fax_2' => $first_school_profile->fax_2,
            'email' => $first_school_profile->email,
            'url' => $first_school_profile->url,
            'logo_url' => $logo_path
        ]);
});

test('create', function () {
    //country id not exist
    $payload = [
        'name' => [
            'en' => fake()->name,
            'zh' => fake()->name,
        ],
        'principal_name' => [
            'en' => fake()->name,
            'zh' => fake()->name,
        ],
        'code' => uniqid(),
        'short_name' => fake()->name,
        'logo' => fake()->imageUrl(),
        'address' => fake()->address,
        'country_id' => 9999,
        'state_id' => State::factory()->create()->id,
        'city' => fake()->city,
        'postcode' => fake()->postcode,
        'phone_1' => fake()->phoneNumber,
        'phone_2' => fake()->phoneNumber,
        'fax_1' => fake()->phoneNumber,
        'fax_2' => fake()->phoneNumber,
        'email' => fake()->email,
        'url' => fake()->url
    ];

    $response = $this->json('POST', route($this->routeNamePrefix.'.update'), $payload)->json();

    expect($response)->toHaveFailedGeneralResponse()
        ->and($response['error'])->toMatchArray([
            'country_id' => [
                'The selected country id is invalid.'
            ]
        ]);

    //state id not exist
    $payload = [
        'name' => [
            'en' => fake()->name,
            'zh' => fake()->name,
        ],
        'principal_name' => [
            'en' => fake()->name,
            'zh' => fake()->name,
        ],
        'code' => uniqid(),
        'short_name' => fake()->name,
        'logo' => fake()->imageUrl(),
        'address' => fake()->address,
        'country_id' => Country::factory()->create()->id,
        'state_id' => 9999,
        'city' => fake()->city,
        'postcode' => fake()->postcode,
        'phone_1' => fake()->phoneNumber,
        'phone_2' => fake()->phoneNumber,
        'fax_1' => fake()->phoneNumber,
        'fax_2' => fake()->phoneNumber,
        'email' => fake()->email,
        'url' => fake()->url
    ];

    $response = $this->json('POST', route($this->routeNamePrefix.'.update'), $payload)->json();

    expect($response)->toHaveFailedGeneralResponse()
        ->and($response['error'])->toMatchArray([
            'state_id' => [
                'The selected state id is invalid.'
            ]
        ]);

    // Create success
    $this->assertDatabaseCount($this->table, 0);

    $payload = [
        'name' => [
            'en' => fake()->name,
            'zh' => fake()->name,
        ],
        'principal_name' => [
            'en' => fake()->name,
            'zh' => fake()->name,
        ],
        'code' => uniqid(),
        'short_name' => fake()->name,
        'address' => fake()->address,
        'country_id' => Country::factory()->create()->id,
        'state_id' => State::factory()->create()->id,
        'city' => fake()->city,
        'postcode' => fake()->postcode,
        'phone_1' => fake()->phoneNumber,
        'phone_2' => fake()->phoneNumber,
        'fax_1' => fake()->phoneNumber,
        'fax_2' => fake()->phoneNumber,
        'email' => fake()->email,
        'url' => fake()->url,
        'logo' => UploadedFile::fake()->create('file.png', 500)
    ];

    $response = $this->json('POST', route($this->routeNamePrefix.'.update'), $payload)->json();

    $this->assertDatabaseCount($this->table, 1);
    $this->assertDatabaseCount('media', 1);

    $logo = Media::first();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toEqual([
            'id' => SchoolProfile::first()->id,
            'name' => $payload['name']['en'],
            'principal_name' => $payload['principal_name']['en'],
            'translations' => [
                'name' => [
                    'en' => $payload['name']['en'],
                    'zh' => $payload['name']['zh'],
                ],
                'principal_name' => [
                    'en' => $payload['principal_name']['en'],
                    'zh' => $payload['principal_name']['zh'],
                ]
            ],
            'code' => strtoupper($payload['code']),
            'short_name' => $payload['short_name'],
            'address' => $payload['address'],
            'city' => $payload['city'],
            'postcode' => $payload['postcode'],
            'state_id' => $payload['state_id'],
            'country_id' => $payload['country_id'],
            'phone_1' => $payload['phone_1'],
            'phone_2' => $payload['phone_2'],
            'fax_1' => $payload['fax_1'],
            'fax_2' => $payload['fax_2'],
            'email' => $payload['email'],
            'url' => $payload['url'],
            'logo_url' => getMediaFullUrl($logo)
        ]);


    $this->assertDatabaseHas(resolve(SchoolProfile::class)->getTable(), [
        'id' => SchoolProfile::first()->id,
        'name->en' => $payload['name']['en'],
        'name->zh' => $payload['name']['zh'],
        'principal_name->en' => $payload['principal_name']['en'],
        'principal_name->zh' => $payload['principal_name']['zh'],
        'code' => strtoupper($payload['code']),
        'short_name' => $payload['short_name'],
        'address' => $payload['address'],
        'city' => $payload['city'],
        'postcode' => $payload['postcode'],
        'state_id' => $payload['state_id'],
        'country_id' => $payload['country_id'],
        'phone_1' => $payload['phone_1'],
        'phone_2' => $payload['phone_2'],
        'fax_1' => $payload['fax_1'],
        'fax_2' => $payload['fax_2'],
        'email' => $payload['email'],
        'url' => $payload['url'],
    ]);
});

test('update', function () {
    $first_school_profile = SchoolProfile::factory()->create([
        'name->en' => 'Test EN',
        'name->zh' => 'Test ZH',
    ]);

    //update with id exist
    $this->assertDatabaseCount($this->table, 1);
    $payload = [
        'name' => [
            'en' => 'Test 2',
            'zh' => 'Test 3',
        ],
        'principal_name' => [
            'en' => fake()->name,
            'zh' => fake()->name,
        ],
        'code' => strtoupper(uniqid()),
        'short_name' => fake()->name,
        'address' => fake()->address,
        'country_id' => Country::factory()->create()->id,
        'state_id' => State::factory()->create()->id,
        'city' => fake()->city,
        'postcode' => fake()->postcode,
        'phone_1' => fake()->phoneNumber,
        'phone_2' => fake()->phoneNumber,
        'fax_1' => fake()->phoneNumber,
        'fax_2' => fake()->phoneNumber,
        'email' => fake()->email,
        'url' => fake()->url,
        'logo' => UploadedFile::fake()->create('new_file.png', 500)
    ];

    $response = $this->postJson(route($this->routeNamePrefix.'.update', ['master_school_profile' => $first_school_profile->id]), $payload)->json();

    $this->assertDatabaseCount(resolve(SchoolProfile::class)->getTable(), 1);
    $this->assertDatabaseCount('media', 1);

    $logo = Media::first();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toMatchArray(collect($payload)->except(['name', 'principal_name', 'logo'])->toArray())
        ->and($response['data']['name'])->toEqual($payload['name']['en'])
        ->and($response['data']['principal_name'])->toEqual($payload['principal_name']['en'])
        ->and($response['data']['translations']['name'])->toEqual($payload['name'])
        ->and($response['data']['translations']['principal_name'])->toEqual($payload['principal_name'])
        ->and($response['data']['logo_url'])->toEqual(getMediaFullUrl($logo, 'new_file.png'));

    $this->assertDatabaseHas(resolve(SchoolProfile::class)->getTable(), [
        'name->en' => $payload['name']['en'],
        'name->zh' => $payload['name']['zh'],
        'principal_name->en' => $payload['principal_name']['en'],
        'principal_name->zh' => $payload['principal_name']['zh'],
        'code' => $payload['code'],
        'short_name' => $payload['short_name'],
        'address' => $payload['address'],
        'city' => $payload['city'],
        'postcode' => $payload['postcode'],
        'state_id' => $payload['state_id'],
        'country_id' => $payload['country_id'],
        'phone_1' => $payload['phone_1'],
        'phone_2' => $payload['phone_2'],
        'fax_1' => $payload['fax_1'],
        'fax_2' => $payload['fax_2'],
        'email' => $payload['email'],
        'url' => $payload['url'],
    ]);
});
