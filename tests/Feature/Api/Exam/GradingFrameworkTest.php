<?php

use App\Http\Controllers\Api\Exam\GradingFrameworkController;
use App\Jobs\ApplyGradingFrameworkJob;
use App\Models\ClassModel;
use App\Models\ClassSubject;
use App\Models\ClassSubjectStudent;
use App\Models\Exam;
use App\Models\GradingFramework;
use App\Models\GradingScheme;
use App\Models\LatestPrimaryClassBySemesterSettingView;
use App\Models\SemesterClass;
use App\Models\SemesterSetting;
use App\Models\Student;
use App\Models\StudentClass;
use App\Models\StudentGradingFramework;
use App\Models\Subject;
use App\Models\User;
use App\Services\Exam\GradingFrameworkService;
use App\Services\Exam\Output\PinHwaReportCardOutputV1Service;
use App\Services\Exam\StudentGradingFrameworkService;
use Carbon\Carbon;
use Database\Seeders\InternationalizationSeeder;
use Database\Seeders\PermissionSeeder;
use Illuminate\Database\Eloquent\Factories\Sequence;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\Hash;
use Laravel\Sanctum\Sanctum;
use Mockery\MockInterface;

beforeEach(function () {
    $this->seed([
        InternationalizationSeeder::class,
        PermissionSeeder::class,
    ]);

    app()->setLocale('en');

    $this->withHeaders([
        'Accept-Language' => 'en'
    ]);

    $user = User::factory()->create([
        'password' => Hash::make('123456'),
    ]);

    $user->assignRole('Super Admin');

    Sanctum::actingAs($user);

    $this->GradingFrameworkController = app(GradingFrameworkController::class);

    $this->table = resolve(GradingFramework::class)->getTable();

    $this->gradingScheme = GradingScheme::factory()->withItems()->create([
        'name' => 'Default',
        'code' => 'DEFAULT',
    ]);

    $this->subjects = Subject::factory(5)->state(new Sequence(
        ['code' => '01'],
        ['code' => '02'],
        ['code' => '03'],
        ['code' => '04'],
        ['code' => '70'],
    ))->create();

    $this->exams = Exam::factory(3)->state(new Sequence(
        ['code' => 'SEM1EXAM'],
        ['code' => 'SEM2EXAM'],
        ['code' => 'FINALEXAM'],
    ))->create();

    $this->config = json_decode(file_get_contents(storage_path('tests/grading-framework-config-1.json')), true);
    $this->routeNamePrefix = 'grading-frameworks';

});

test('index', function () {
    $gradingFrameworks = GradingFramework::factory(3)->state(new Sequence(
        [
            'code' => 'J001',
            'name->en' => 'J1DEFAULT',
            'name->zh' => 'J1号码',
            'is_active' => true
        ],
        [
            'code' => 'J002',
            'name->en' => 'J2DEFAULT',
            'name->zh' => 'J2号码',
            'is_active' => true
        ],
        [
            'code' => 'J003',
            'name->en' => 'J3DEFAULT',
            'name->zh' => 'J3号码',
            'is_active' => false
        ]
    ))->create();

    $filters = [
        'name' => 'J2DEFAULT',
        'code' => 'J002',
    ];

    $this->mock(GradingFrameworkService::class, function (MockInterface $mock) use ($filters, $gradingFrameworks) {
        $mock->shouldReceive('getAllPaginatedGradingFrameworks')
            ->with($filters)
            ->once()
            ->andReturn(new LengthAwarePaginator([$gradingFrameworks[1]], 1, 1));
    });

    $response = $this->getJson(
        route($this->routeNamePrefix . '.index', $filters))->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toHaveCount(1)
        ->and($response['data'][0])->toMatchArray([
            'id' => $gradingFrameworks[1]->id,
            'name' => $gradingFrameworks[1]->name,
            'code' => $gradingFrameworks[1]->code,
            'is_active' => $gradingFrameworks[1]->is_active,
            'configuration' => $gradingFrameworks[1]->configuration
        ]);
});

test('index getPaginatedGradingFramework when page != -1', function () {
    $this->mock(GradingFrameworkService::class, function (MockInterface $mock) {
        $gradingFramework = GradingFramework::factory()->create();

        $mock->shouldReceive('getAllPaginatedGradingFrameworks')
            ->once()
            ->andReturn(new LengthAwarePaginator([$gradingFramework], 1, 1));
    });

    $response = $this->getJson(route($this->routeNamePrefix . '.index', [
        'per_page' => 10,
        'page' => 1,
    ]));

    $response->assertStatus(200);

    expect($response->json())->toHaveKey('pagination');
});

test('index getAllGradingFrameworks when page = -1 ', function () {

    $this->mock(GradingFrameworkService::class, function (MockInterface $mock) {
        $gradingFramework = GradingFramework::factory(2)->create();

        $mock->shouldReceive('getAllGradingFrameworks')->once()->andReturn($gradingFramework);
    });

    $response = $this->getJson(route($this->routeNamePrefix . '.index', [
        'per_page' => -1,
        'page' => 1,
    ]));

    $response->assertStatus(200);

    expect($response->json())->not()->toHaveKey('pagination');
});

test('show', function () {

    $gradingFramework = GradingFramework::factory()->create([
        'code' => 'J001',
        'name->en' => 'J1DEFAULT',
        'name->zh' => 'J1号码',
        'is_active' => true,
        'configuration' => $this->config
    ]);

    //test with id exist
    $response = $this->getJson(route("$this->routeNamePrefix.show",
        ['grading_framework' => $gradingFramework->id]))->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toMatchArray([
            'code' => $gradingFramework->code,
            'name' => $gradingFramework->name,
            'is_active' => $gradingFramework->is_active,
            'configuration' => $this->config
        ]);

    //test with id not exist
    $response = $this->getJson(route("$this->routeNamePrefix.show", ['grading_framework' => 9999]))->json();

    expect($response)->toHaveModelResourceNotFoundResponse();

});

test('store', function () {
    //store success
    $this->assertDatabaseCount($this->table, 0);

    $payload = [
        'name' => [
            'en' => 'J001 Default Framework',
            'zh' => 'J001 考试',
        ],
        'code' => 'J1DEFAULT',
    ];
    $response = $this->postJson(route("$this->routeNamePrefix.create"), $payload)->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toMatchArray([
            'code' => $payload['code'],
            'name' => $payload['name']['en'],
            'is_active' => false,
            'configuration' => null
        ]);

    $this->assertDatabaseCount($this->table, 1);

    $this->assertDatabaseHas($this->table, [
        'code' => $payload['code'],
        'name->en' => $payload['name']['en'],
        'name->zh' => $payload['name']['zh'],
        'is_active' => false,
        'configuration' => null
    ]);

    // store fail - trying to use an existing code
    $response = $this->postJson(route("$this->routeNamePrefix.create"), $payload)->json();
    expect($response)->toHaveFailedGeneralResponse()
        ->and($response['error'])
        ->toMatchArray([
            'code' => ['The code has already been taken.']
        ]);

    $this->assertDatabaseCount($this->table, 1);

    $this->assertDatabaseHas($this->table, [
        'code' => $payload['code'],
        'name->en' => $payload['name']['en'],
        'name->zh' => $payload['name']['zh'],
        'configuration' => null
    ]);

    // store fail - required fields not set
    $payload = [];
    $response = $this->postJson(route("$this->routeNamePrefix.create"), $payload)->json();

    expect($response)->toHaveFailedGeneralResponse()
        ->and($response['error'])
        ->toMatchArray([
            'name' => ['The name field is required.'],
            'code' => ['The code field is required.'],
        ]);

});

test('update - is_active = true', function () {
    $grading_framework = GradingFramework::factory()->create([
        'code' => 'J1DEFAULT',
        'is_active' => false,
        'configuration' => null,
    ]);

    $student_1 = Student::factory()->create();
    $student_2 = Student::factory()->create();

    $semester_setting = SemesterSetting::factory()->create();

    $semester_class = SemesterClass::factory()->create([
        'semester_setting_id' => $semester_setting->id
    ]);

    $student_class = StudentClass::factory(2)->state(new Sequence(
        [
            'student_id' => $student_1->id,
            'semester_class_id' => $semester_class->id,
            'semester_setting_id' => $semester_setting->id,
            'is_active' => true
        ],
        [
            'student_id' => $student_2->id,
            'semester_class_id' => $semester_class->id,
            'semester_setting_id' => $semester_setting->id,
            'is_active' => true
        ]
    ))->create();

    $class_subjects = ClassSubject::factory(4)->state(new Sequence(
        [
            'semester_class_id' => $semester_class->id,
            'subject_id' => $this->subjects[0]->id
        ],
        [
            'semester_class_id' => $semester_class->id,
            'subject_id' => $this->subjects[1]->id
        ],
        [
            'semester_class_id' => $semester_class->id,
            'subject_id' => $this->subjects[2]->id
        ],
        [
            'semester_class_id' => $semester_class->id,
            'subject_id' => $this->subjects[3]->id
        ]
    ))->create();

    ClassSubjectStudent::factory(8)->state(new Sequence(
        [
            'student_id' => $student_1->id,
            'class_subject_id' => $class_subjects[0]->id
        ],
        [
            'student_id' => $student_1->id,
            'class_subject_id' => $class_subjects[1]->id
        ],
        [
            'student_id' => $student_1->id,
            'class_subject_id' => $class_subjects[2]->id
        ],
        [
            'student_id' => $student_1->id,
            'class_subject_id' => $class_subjects[3]->id
        ],
        [
            'student_id' => $student_2->id,
            'class_subject_id' => $class_subjects[0]->id
        ],
        [
            'student_id' => $student_2->id,
            'class_subject_id' => $class_subjects[1]->id
        ],
        [
            'student_id' => $student_2->id,
            'class_subject_id' => $class_subjects[2]->id
        ],
        [
            'student_id' => $student_2->id,
            'class_subject_id' => $class_subjects[3]->id
        ],
    ))->create();

    // existing student grading framework
    $sgf1 = StudentGradingFramework::factory()->create([
        'student_id' => $student_1->id,
        'grading_framework_id' => $grading_framework->id,
        'is_active' => true,
    ]);

    $sgf2 = StudentGradingFramework::factory()->create([
        'student_id' => $student_2->id,
        'grading_framework_id' => $grading_framework->id,
        'is_active' => false,
        'configuration' => [
            'some_json' => 123,
            'other_json' => 'test',
        ]
    ]);

    $payload = [
        'name' => [
            'en' => 'J001 Default Framework',
            'zh' => 'J001 考试',
        ],
        'code' => 'J1DEFAULT',
        'is_active' => true,
        'replace_existing' => true,
        'configuration' => $this->config
    ];

    $response = $this->putJson(route("$this->routeNamePrefix.update",
        ['grading_framework' => $grading_framework->id]), $payload)->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toMatchArray([
            'code' => $payload['code'],
            'name' => $payload['name']['en'],
            'is_active' => $payload['is_active'],
            'configuration' => $payload['configuration']
        ]);

    $this->assertDatabaseCount($this->table, 1);
    $this->assertDatabaseHas($this->table, [
        'name->en' => $payload['name']['en'],
        'name->zh' => $payload['name']['zh'],
        'code' => $payload['code'],
        'is_active' => $payload['is_active'],
        'configuration' => json_encode($this->config)
    ]);

    // check if all related ACTIVE student grading framework are updated
    // only update if student grading framework is active AND replace_existing = true
    $this->assertDatabaseCount('student_grading_frameworks', 2);

    // Student is only taking 01, 02, 03, 04 subjects and not 70, so we check against the mapped configuration
    $sgf1 = StudentGradingFramework::with('gradingFramework')->where('id', $sgf1->id)->first();
    $sgf1_result = $sgf1->toArray();
    $sgf1_config = app()->make(StudentGradingFrameworkService::class)
        ->setStudent($student_1)
        ->setGradingFramework($sgf1->gradingFramework)
        ->setStudentGradingFramework($sgf1)
        ->mapConfigToValidSubjects();

    expect($sgf1_result)->toMatchArray([
        'student_id' => $student_1->id,
        'grading_framework_id' => $grading_framework->id,
        'is_active' => true,
        'configuration' => $sgf1_config,
        'is_active' => $payload['is_active']
    ]);

    $this->assertDatabaseHas('student_grading_frameworks', [
        'student_id' => $student_1->id,
        'grading_framework_id' => $grading_framework->id,
        'is_active' => true,
        'configuration' => json_encode($sgf1_config)
    ]);

    $sgf2_result = StudentGradingFramework::find($sgf2->id)->toArray();
    expect($sgf2_result)->toMatchArray([
        'student_id' => $student_2->id,
        'grading_framework_id' => $grading_framework->id,
        'is_active' => false,
        'configuration' => $sgf2->configuration
    ]);

    $this->assertDatabaseHas('student_grading_frameworks', [
        'student_id' => $student_2->id,
        'grading_framework_id' => $grading_framework->id,
        'is_active' => false,
        'configuration' => json_encode($sgf2->configuration)
    ]);

    // Update fail - code has already been taken
    $grading_framework_2 = GradingFramework::factory()->create([
        'code' => 'J2DEFAULT',
        'is_active' => false,
        'configuration' => null,
    ]);

    $payload = [
        'name' => [
            'en' => 'J001 Default Framework',
            'zh' => 'J001 考试',
        ],
        'code' => 'J2DEFAULT',
        'is_active' => true,
        'configuration' => $this->config
    ];

    $response = $this->putJson(route("$this->routeNamePrefix.update",
        ['grading_framework' => $grading_framework->id]), $payload)->json();

    expect($response)->toHaveFailedGeneralResponse()
        ->and($response['error'])
        ->toMatchArray([
            'code' => ['The code has already been taken.']
        ]);

    $this->assertDatabaseCount($this->table, 2);

    // update fail - required fields not set
    $payload = [];
    $response = $this->putJson(route("$this->routeNamePrefix.update",
        ['grading_framework' => $grading_framework->id]), $payload)->json();

    expect($response)->toHaveFailedGeneralResponse()
        ->and($response['error'])
        ->toMatchArray([
            'name' => ['The name field is required.'],
            'code' => ['The code field is required.'],
            'is_active' => ['The is active field is required.'],
        ]);
});

test('update - is_active = false', function () {
    $grading_framework = GradingFramework::factory()->create([
        'code' => 'J1DEFAULT',
        'is_active' => false,
        'configuration' => null,
    ]);

    $student_1 = Student::factory()->create();
    $student_2 = Student::factory()->create();

    // existing student grading framework
    $sgf1 = StudentGradingFramework::factory()->create([
        'student_id' => $student_1->id,
        'grading_framework_id' => $grading_framework->id,
        'is_active' => true,
    ]);

    $sgf2 = StudentGradingFramework::factory()->create([
        'student_id' => $student_2->id,
        'grading_framework_id' => $grading_framework->id,
        'is_active' => false,
        'configuration' => [
            'some_json' => 123,
            'other_json' => 'test',
        ]
    ]);

    $payload = [
        'name' => [
            'en' => 'J001 Default Framework',
            'zh' => 'J001 考试',
        ],
        'code' => 'J1DEFAULT',
        'is_active' => false,
        'configuration' => $this->config
    ];

    $response = $this->putJson(route("$this->routeNamePrefix.update",
        ['grading_framework' => $grading_framework->id]), $payload)->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toMatchArray([
            'code' => $payload['code'],
            'name' => $payload['name']['en'],
            'is_active' => $payload['is_active'],
            'configuration' => $payload['configuration']
        ]);

    $this->assertDatabaseCount($this->table, 1);
    $this->assertDatabaseHas($this->table, [
        'name->en' => $payload['name']['en'],
        'name->zh' => $payload['name']['zh'],
        'code' => $payload['code'],
        'is_active' => $payload['is_active'],
        'configuration' => json_encode($this->config)
    ]);

    // is_active is set to false during grading framework update, all related student grading framework should NOT be updated
    $this->assertDatabaseCount('student_grading_frameworks', 2);

    $sgf1_result = StudentGradingFramework::find($sgf1->id)->toArray();
    expect($sgf1_result)->toMatchArray([
        'student_id' => $student_1->id,
        'grading_framework_id' => $grading_framework->id,
        'is_active' => true,
        'configuration' => $sgf1->configuration
    ]);

    $this->assertDatabaseHas('student_grading_frameworks', [
        'student_id' => $student_1->id,
        'grading_framework_id' => $grading_framework->id,
        'is_active' => true,
        'configuration' => "[]"
    ]);

    $sgf2_result = StudentGradingFramework::find($sgf2->id)->toArray();
    expect($sgf2_result)->toMatchArray([
        'student_id' => $student_2->id,
        'grading_framework_id' => $grading_framework->id,
        'is_active' => false,
        'configuration' => $sgf2->configuration
    ]);

    $this->assertDatabaseHas('student_grading_frameworks', [
        'student_id' => $student_2->id,
        'grading_framework_id' => $grading_framework->id,
        'is_active' => false,
        'configuration' => json_encode($sgf2->configuration)
    ]);

    // is_active = true but replace_existing = false
    $payload = [
        'name' => [
            'en' => 'J001 Default Framework',
            'zh' => 'J001 考试',
        ],
        'code' => 'J1DEFAULT',
        'is_active' => true,
        'replace_existing' => false,
        'configuration' => $this->config
    ];

    $response = $this->putJson(route("$this->routeNamePrefix.update",
        ['grading_framework' => $grading_framework->id]), $payload)->json();

    $this->assertDatabaseCount('student_grading_frameworks', 2);

    $sgf1_result = StudentGradingFramework::find($sgf1->id)->toArray();
    expect($sgf1_result)->toMatchArray([
        'student_id' => $student_1->id,
        'grading_framework_id' => $grading_framework->id,
        'is_active' => true,
        'configuration' => $sgf1->configuration
    ]);

    $this->assertDatabaseHas('student_grading_frameworks', [
        'student_id' => $student_1->id,
        'grading_framework_id' => $grading_framework->id,
        'is_active' => true,
        'configuration' => "[]"
    ]);

    $sgf2_result = StudentGradingFramework::find($sgf2->id)->toArray();
    expect($sgf2_result)->toMatchArray([
        'student_id' => $student_2->id,
        'grading_framework_id' => $grading_framework->id,
        'is_active' => false,
        'configuration' => $sgf2->configuration
    ]);

    $this->assertDatabaseHas('student_grading_frameworks', [
        'student_id' => $student_2->id,
        'grading_framework_id' => $grading_framework->id,
        'is_active' => false,
        'configuration' => json_encode($sgf2->configuration)
    ]);
});

test('apply', function () {

    $class = ClassModel::factory()->create();
    $grading_framework = GradingFramework::factory()->create([
        'code' => 'J1DEFAULT',
        'is_active' => false,
        'configuration' => $this->config
    ]);

    $students = Student::factory(5)->create();
    $semester_setting = SemesterSetting::factory()->create();

    $semester_class = SemesterClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'class_id' => $class->id
    ]);

    $class_subjects = ClassSubject::factory(4)->state(new Sequence(
        [
            'semester_class_id' => $semester_class->id,
            'subject_id' => $this->subjects[0]->id
        ],
        [
            'semester_class_id' => $semester_class->id,
            'subject_id' => $this->subjects[1]->id
        ],
        [
            'semester_class_id' => $semester_class->id,
            'subject_id' => $this->subjects[2]->id
        ],
        [
            'semester_class_id' => $semester_class->id,
            'subject_id' => $this->subjects[3]->id
        ],
    ))->create();


    foreach ($students as $student){
        StudentClass::factory()->create([
            'student_id' => $student->id,
            'semester_class_id' => $semester_class->id,
            'semester_setting_id' => $semester_setting->id,
            'is_active' => true
        ]);

        ClassSubjectStudent::factory(4)->state(new Sequence(
            [
                'student_id' => $student->id,
                'class_subject_id' => $class_subjects[0]->id
            ],
            [
                'student_id' => $student->id,
                'class_subject_id' => $class_subjects[1]->id
            ],
            [
                'student_id' => $student->id,
                'class_subject_id' => $class_subjects[2]->id
            ],
            [
                'student_id' => $student->id,
                'class_subject_id' => $class_subjects[3]->id
            ],
        ))->create();
    }

    $payload = [
        'student_ids' => $students->pluck('id')->toArray(),
        'grading_framework_id' => $grading_framework->id,
        'effective_from' => '2024-04-20',
        'effective_to' => '2024-12-30',
        'academic_year' => '2024'
    ];
    $response = $this->postJson(route("$this->routeNamePrefix.apply", $payload))->json();
    expect($response)->toHaveSuccessGeneralResponse();

    foreach ($students as $student){
        $sgf = StudentGradingFramework::where('student_id', $student->id)
            ->with('gradingFramework')
            ->first();

        // Student is only taking 01, 02, 03, 04 subjects and not 70, so we check against the mapped configuration
        $config = app()->make(StudentGradingFrameworkService::class)
            ->setStudent($student)
            ->setGradingFramework($sgf->gradingFramework)
            ->setStudentGradingFramework($sgf)
            ->mapConfigToValidSubjects();

        expect($sgf)->toMatchArray([
            'student_id' => $student->id,
            'grading_framework_id' => $grading_framework->id,
            'is_active' => true,
            'configuration' => $config,
            'effective_from' => $payload['effective_from'],
            'effective_to' => $payload['effective_to'],
            'academic_year' => $payload['academic_year']
        ]);
    
    }
    // validation guard
    $payload = [
        'student_ids' => [999],
        'grading_framework_id' => 9999,
        'effective_from' => '2024-04-20',
        'effective_to' => '2023-12-30',
        'academic_year' => '12345'
    ];
    $response = $this->postJson(route("$this->routeNamePrefix.apply", $payload))->json();
    expect($response)->toHaveFailedGeneralResponse();
    expect($response['code'])->toBe(422)
        ->and($response['error'])->toMatchArray([
            'student_ids.0' => ['The selected student_ids.0 is invalid.'],
            'grading_framework_id' => ['The selected grading framework id is invalid.'],
            'effective_to' => ['The effective to field must be a date after or equal to effective from.'],
            'academic_year' => ['The academic year field must match the format Y.']
        ]);
});


test('bulkApply', function () {
    // Testing queue behaviour here - processing behaviour is tested under student grading framework service
    $grading_framework = GradingFramework::factory()->create([
        'code' => 'J1DEFAULT',
        'is_active' => true,
        'configuration' => $this->config,
    ]);

    $semester_setting = SemesterSetting::factory()->create();

    $semester_class1 = SemesterClass::factory()->create(['semester_setting_id' => $semester_setting->id]);
    $semester_class2 = SemesterClass::factory()->create(['semester_setting_id' => $semester_setting->id]);
    $semester_class3 = SemesterClass::factory()->create(['semester_setting_id' => $semester_setting->id]);

    $students = Student::factory(4)->create();
    $student_class = StudentClass::factory(4)->state(new Sequence(
        [
            'semester_class_id' => $semester_class1->id,
            'student_id' => $students[0]->id,
        ],
        [
            'semester_class_id' => $semester_class1->id,
            'student_id' => $students[1]->id,
        ],
        [
            'semester_class_id' => $semester_class2->id,
            'student_id' => $students[2]->id,
        ],
        [
            'semester_class_id' => $semester_class3->id,
            'student_id' => $students[3]->id,
        ]
    ))->create();

    LatestPrimaryClassBySemesterSettingView::refreshViewTable(false);
    Queue::fake();
    $payload = [
        'semester_class_ids' => [$semester_class1->id],
        'grading_framework_id' => $grading_framework->id,
        'effective_from' => '2024-02-02',
        'effective_to' => '2024-06-30',
        'academic_year' => '2024'
    ];

    $response = $this->postJson(route("$this->routeNamePrefix.bulkApply", $payload))->json();
    Queue::assertPushed(ApplyGradingFrameworkJob::class, 2);

    Queue::assertPushed(ApplyGradingFrameworkJob::class, function ($job) use ($students, $grading_framework, $payload) {
        $valid_student = $job->student->id === $students[0]->id;
        $valid_grading_framework = $job->gradingFramework->id === $grading_framework->id;
        $valid_effective_from = $job->input['effective_from'] === $payload['effective_from'];
        $valid_effective_to = $job->input['effective_to'] === $payload['effective_to'];
        $valid_academic_year = $job->input['academic_year'] === $payload['academic_year'];

        return $valid_student && $valid_grading_framework && $valid_effective_from && $valid_effective_to && $valid_academic_year;
    });

    Queue::assertPushed(ApplyGradingFrameworkJob::class, function ($job) use ($students, $grading_framework, $payload) {
        $valid_student = $job->student->id === $students[1]->id;
        $valid_grading_framework = $job->gradingFramework->id === $grading_framework->id;
        $valid_effective_from = $job->input['effective_from'] === $payload['effective_from'];
        $valid_effective_to = $job->input['effective_to'] === $payload['effective_to'];
        $valid_academic_year = $job->input['academic_year'] === $payload['academic_year'];

        return $valid_student && $valid_grading_framework && $valid_effective_from && $valid_effective_to && $valid_academic_year;
    });

    // validation guard
    $payload = [
        'semester_class_ids' => [9999, 9999],
        'grading_framework_id' => 9999,
        'effective_from' => '2024-04-20',
        'effective_to' => '2023-12-30',
        'academic_year' => '20251'
    ];
    $response = $this->postJson(route("$this->routeNamePrefix.bulkApply", $payload))->json();
    expect($response)->toHaveFailedGeneralResponse();
    expect($response['code'])->toBe(422)
        ->and($response['error'])->toMatchArray([
            'grading_framework_id' => ['The selected grading framework id is invalid.'],
            'effective_to' => ['The effective to field must be a date after or equal to effective from.'],
            'academic_year' => ['The academic year field must match the format Y.'],
            'semester_class_ids.0' => ['The selected semester_class_ids.0 is invalid.'],
            'semester_class_ids.1' => ['The selected semester_class_ids.1 is invalid.']
        ]);

    Queue::assertPushed(ApplyGradingFrameworkJob::class, 2);
});

test('getFixedFormulas', function () {
    $grading_framework = GradingFramework::factory()->create([
        'code' => 'J1DEFAULT',
        'is_active' => false,
        'configuration' => $this->config
    ]);

    $response = $this->getJson(
        route($this->routeNamePrefix . '.getFixedFormulas', ['grading_framework' => $grading_framework->id]))->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toHaveCount(17);

    $service = resolve(PinHwaReportCardOutputV1Service::class);

    foreach ($service::FORMULA as $key => $value) {
        expect($response['data'])->toContain(
            [
                'label' => $key,
                'value' => $value['value'],
                'args' => isset($value['args']) ? $value['args'] : []
            ]
        );
    }
});

test('getResultSourceFormulas', function () {
    $grading_framework = GradingFramework::factory()->create([
        'code' => 'J1DEFAULT',
        'is_active' => false,
        'configuration' => $this->config
    ]);

    $payload = [
        'grading_framework' => $grading_framework->id,
        'result_source_code' => 'SEM1EXAM',
        'subject_code' => '70'
    ];

    $response = $this->getJson(
        route($this->routeNamePrefix . '.getResultSourceFormulas', $payload))->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toHaveCount(4)
        ->and($response['data'])->toBe([
            [
                'label' => "SEM1EXAM.70.SCORE",
                'value' => "VAR(\"RESULTSOURCE[SEM1EXAM].SUBJECT[70].SCORE\")"
            ],
            [
                'label' => "SEM1EXAM.70.GRADE",
                'value' => "VAR(\"RESULTSOURCE[SEM1EXAM].SUBJECT[70].GRADE\")"
            ],
            [
                'label' => "SEM1EXAM.70.WEIGHT",
                'value' => "VAR(\"RESULTSOURCE[SEM1EXAM].SUBJECT[70].WEIGHT\")"
            ],
            [
                'label' => "SEM1EXAM.70.WEIGHTEDSCORE",
                'value' => "VAR(\"RESULTSOURCE[SEM1EXAM].SUBJECT[70].WEIGHTEDSCORE\")"
            ],
        ]);

});

test('getOutputFormulas', function () {
    $grading_framework = GradingFramework::factory()->create([
        'code' => 'J1DEFAULT',
        'is_active' => false,
        'configuration' => $this->config
    ]);

    $payload = [
        'grading_framework' => $grading_framework->id,
        'output_code' => 'SEM1RESULT',
        'component_code' => 'GT',
    ];

    $response = $this->getJson(
        route($this->routeNamePrefix . '.getOutputFormulas', $payload))->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toHaveCount(3)
        ->and($response['data'])->toBe([
            [
                'label' => "SEM1RESULT.GT.TOTAL",
                'value' => "VAR(\"OUTPUT[SEM1RESULT].COMPONENT[GT].TOTAL\")"
            ],
            [
                'label' => "SEM1RESULT.GT.LABEL",
                'value' => "VAR(\"OUTPUT[SEM1RESULT].COMPONENT[GT].LABEL\")"
            ],
            [
                'label' => "SEM1RESULT.GT.TOTAL_GRADE",
                'value' => "VAR(\"OUTPUT[SEM1RESULT].COMPONENT[GT].TOTAL_GRADE\")"
            ]
        ]);
});
