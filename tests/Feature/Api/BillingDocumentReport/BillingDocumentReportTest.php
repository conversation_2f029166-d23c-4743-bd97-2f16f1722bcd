<?php

use App\Enums\ExportType;
use App\Models\User;
use App\Services\Reports\BillingDocumentReportService;
use Database\Seeders\InternationalizationSeeder;
use Database\Seeders\PermissionSeeder;
use Illuminate\Support\Facades\Hash;
use Lara<PERSON>\Sanctum\Sanctum;
use Mo<PERSON>y\MockInterface;

beforeEach(function () {
    $this->seed([
        InternationalizationSeeder::class,
        PermissionSeeder::class,
    ]);

    app()->setLocale('en');

    $this->withHeaders([
        'Accept-Language' => 'en'
    ]);

    $user = User::factory()->create([
        'password' => Hash::make('123456'),
    ]);

    $user->assignRole('Super Admin');

    Sanctum::actingAs($user);

    $this->routeNamePrefix = 'reports.billing-documents.';
});

test('reportByDailyCollection, download excel', function () {

    $payload = [
        'report_language' => 'en',
        'payment_date_from' => '2024-01-01',
        'payment_date_to' => '2024-01-31',
    ];

    // Report content already tested in BillingDocumentReportServiceTest
    $this->mock(BillingDocumentReportService::class, function (MockInterface $mock) use ($payload) {
        $mock->shouldReceive('setExportType')
            ->once()
            ->with(ExportType::EXCEL->value)
            ->andReturnSelf();

        $mock->shouldReceive('setReportViewName')
            ->once()
            ->with('reports.billing-documents.by-daily-collection')
            ->andReturnSelf();

        $mock->shouldReceive('setFileName')
            ->once()
            ->with(Mockery::type('string'))
            ->andReturnSelf();

        $mock->shouldReceive('getDailyCollectionReportData')
            ->once()
            ->with($payload)
            ->andReturn(['url' => 'http://localhost:8000/storage/s3-downloads/123456']);
    });

    $response = $this->getJson(route($this->routeNamePrefix . 'by-daily-collection', $payload))->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data']['url'])
        ->toEqual('http://localhost:8000/storage/s3-downloads/123456');
});
