<?php

use App\Enums\RewardPunishmentRecordStatus;
use App\Http\Resources\RewardPunishmentResource;
use App\Http\Resources\StudentResource;
use App\Models\RewardPunishment;
use App\Models\RewardPunishmentRecord;
use App\Models\Student;
use App\Models\User;
use Database\Seeders\InternationalizationSeeder;
use Database\Seeders\PermissionSeeder;
use Laravel\Sanctum\Sanctum;

beforeEach(function () {
    $this->seed([
        InternationalizationSeeder::class,
        PermissionSeeder::class
    ]);

    $this->user = User::factory()->create();

    $this->user->assignRole('Super Admin');

    Sanctum::actingAs($this->user);

    app()->setLocale('en');

    $this->testLocale = app()->getLocale();

    $this->baseUrl = 'reward-punishment-records.';

    $this->tableName = resolve(RewardPunishmentRecord::class)->getTable();
});

test('index without any params', function () {
    $first_reward_punishment_record = RewardPunishmentRecord::factory()->create();
    $second_reward_punishment_record = RewardPunishmentRecord::factory()->create();

    $response = $this->getJson(route($this->baseUrl . 'index', [
        'includes' => ['student', 'rewardPunishment'],
        'order_by' => ['id']
    ]));

    $response->assertStatus(200);

    expect($response->json())->toHaveSuccessGeneralResponse()
        ->and($response->json()['data'])->toHaveCount(2)
        ->sequence(
            fn($response) => $response->toEqual([
                'id' => $first_reward_punishment_record->id,
                'date' => $first_reward_punishment_record->date,
                'student' => resourceToArray(new StudentResource($first_reward_punishment_record->student)),
                'reward_punishment' => resourceToArray(new RewardPunishmentResource($first_reward_punishment_record->rewardPunishment)),
                'average_exam_marks' => $first_reward_punishment_record->average_exam_marks,
                'conduct_marks' => $first_reward_punishment_record->conduct_marks,
                'display_in_report_card' => $first_reward_punishment_record->display_in_report_card,
                'status' => $first_reward_punishment_record->status->value,
                'notification_sent_at' => $first_reward_punishment_record->notification_sent_at,
            ]),
            fn($response) => $response->toEqual([
                'id' => $second_reward_punishment_record->id,
                'date' => $second_reward_punishment_record->date,
                'student' => resourceToArray(new StudentResource($second_reward_punishment_record->student)),
                'reward_punishment' => resourceToArray(new RewardPunishmentResource($second_reward_punishment_record->rewardPunishment)),
                'average_exam_marks' => $second_reward_punishment_record->average_exam_marks,
                'conduct_marks' => $second_reward_punishment_record->conduct_marks,
                'display_in_report_card' => $second_reward_punishment_record->display_in_report_card,
                'status' => $second_reward_punishment_record->status->value,
                'notification_sent_at' => $second_reward_punishment_record->notification_sent_at,
            ]),
        );
});

test('index with params filter by student_id', function () {
    RewardPunishmentRecord::factory()->create();

    $student = Student::factory()->create();
    $first_reward_punishment_record = RewardPunishmentRecord::factory()->create([
        'student_id' => $student->id,
    ]);

    expect(RewardPunishmentRecord::count())->toBe(2);

    $response = $this->getJson(route($this->baseUrl . 'index', [
        'student_id' => $student->id,
    ]));

    $response->assertStatus(200);

    expect($response->json())->toHaveSuccessGeneralResponse()
        ->and($response->json()['data'])->toHaveCount(1)
        ->toHaveKey('0.id', $first_reward_punishment_record->id);
});

test('index with params filter by reward_punishment_id', function () {
    RewardPunishmentRecord::factory()->create();

    $reward_punishment = RewardPunishment::factory()->create();
    $first_reward_punishment_record = RewardPunishmentRecord::factory()->create([
        'reward_punishment_id' => $reward_punishment->id,
    ]);

    expect(RewardPunishmentRecord::count())->toBe(2);

    $response = $this->getJson(route($this->baseUrl . 'index', [
        'reward_punishment_id' => $reward_punishment->id,
    ]));

    $response->assertStatus(200);

    expect($response->json())->toHaveSuccessGeneralResponse()
        ->and($response->json()['data'])->toHaveCount(1)
        ->toHaveKey('0.id', $first_reward_punishment_record->id);
});

test('index with params order by id', function () {
    $first_reward_punishment_record = RewardPunishmentRecord::factory()->create();
    $second_reward_punishment_record = RewardPunishmentRecord::factory()->create();

    expect(RewardPunishmentRecord::count())->toBe(2);

    // Sort by id asc
    $response = $this->getJson(route($this->baseUrl . 'index', [
        'order_by' => ['id' => 'asc'],
    ]));

    $response->assertStatus(200);

    expect($response->json())->toHaveSuccessGeneralResponse()
        ->and($response->json()['data'])
        ->toHaveKey('0.id', $first_reward_punishment_record->id)
        ->toHaveKey('1.id', $second_reward_punishment_record->id);

    // Sort by id desc
    $response = $this->getJson(route($this->baseUrl . 'index', [
        'order_by' => ['id' => 'desc'],
    ]));

    $response->assertStatus(200);

    expect($response->json())->toHaveSuccessGeneralResponse()
        ->and($response->json()['data'])
        ->toHaveKey('0.id', $second_reward_punishment_record->id)
        ->toHaveKey('1.id', $first_reward_punishment_record->id);
});

test('show single record success', function () {
    $reward_punishment_record = RewardPunishmentRecord::factory()->create();

    $response = $this->getJson(route($this->baseUrl . 'show', $reward_punishment_record->id));

    $response->assertStatus(200);

    expect($response->json())->toHaveSuccessGeneralResponse()
        ->and($response->json()['data'])
        ->toEqual([
            'id' => $reward_punishment_record->id,
            'date' => $reward_punishment_record->date,
            'student' => resourceToArray(new StudentResource($reward_punishment_record->student)),
            'reward_punishment' => resourceToArray(new RewardPunishmentResource($reward_punishment_record->rewardPunishment->load('meritDemeritSettings', 'category', 'subCategory'))),
            'average_exam_marks' => $reward_punishment_record->average_exam_marks,
            'conduct_marks' => $reward_punishment_record->conduct_marks,
            'display_in_report_card' => $reward_punishment_record->display_in_report_card,
            'status' => $reward_punishment_record->status->value,
            'notification_sent_at' => $reward_punishment_record->notification_sent_at,
        ]);
});

test('show not existing record error', function () {
    expect(RewardPunishmentRecord::count())->toBe(0);

    $response = $this->getJson(route($this->baseUrl . 'show', 1));

    $response->assertStatus(404);

    expect($response->json())->toHaveModelResourceNotFoundResponse();
});

test('create success : for 1 student', function () {
    expect(RewardPunishmentRecord::count())->toBe(0);

    $student = Student::factory()->create();
    $reward_punishment = RewardPunishment::factory()->create();

    $payload = [
        'date' => '2024-02-01', // 1st Feb 2024
        'student_ids' => [
            $student->id,
        ],
        'reward_punishment_id' => $reward_punishment->id,
        'display_in_report_card' => true,
    ];

    $response = $this->postJson(route($this->baseUrl . 'create'), $payload);

    $response->assertStatus(200);

    expect($response->json())->toHaveSuccessGeneralResponse();

    // Expect only one RewardPunishmentRecord is created
    expect(RewardPunishmentRecord::count())->toBe(1);

    $this->assertDatabaseHas($this->tableName, [
        'date' => $payload['date'],
        'student_id' => $student->id,
        'reward_punishment_id' => $payload['reward_punishment_id'],
        'average_exam_marks' => $reward_punishment->average_exam_marks,
        'conduct_marks' => $reward_punishment->conduct_marks,
        'display_in_report_card' => $payload['display_in_report_card'],
        'status' => RewardPunishmentRecordStatus::POSTED->value,
    ]);
});

test('create success : for 3 students', function () {
    expect(RewardPunishmentRecord::count())->toBe(0);

    $student_1 = Student::factory()->create();
    $student_2 = Student::factory()->create();
    $student_3 = Student::factory()->create();
    $reward_punishment = RewardPunishment::factory()->create();

    $payload = [
        'date' => '2024-02-01', // 1st Feb 2024
        'student_ids' => [
            $student_1->id,
            $student_2->id,
            $student_3->id,
        ],
        'reward_punishment_id' => $reward_punishment->id,
        'display_in_report_card' => true,
    ];

    $response = $this->postJson(route($this->baseUrl . 'create'), $payload);

    $response->assertStatus(200);

    expect($response->json())->toHaveSuccessGeneralResponse();

    // Expect only one RewardPunishmentRecord is created
    expect(RewardPunishmentRecord::count())->toBe(3);

    foreach ($payload['student_ids'] as $student_id) {
        $this->assertDatabaseHas($this->tableName, [
            'date' => $payload['date'],
            'student_id' => $student_id,
            'reward_punishment_id' => $payload['reward_punishment_id'],
            'average_exam_marks' => $reward_punishment->average_exam_marks,
            'conduct_marks' => $reward_punishment->conduct_marks,
            'display_in_report_card' => $payload['display_in_report_card'],
            'status' => RewardPunishmentRecordStatus::POSTED->value,
        ]);
    }
});

test('create validation error', function () {
    expect(RewardPunishmentRecord::count())->toBe(0);

    $response = $this->postJson(route($this->baseUrl . 'create'), []);

    $response->assertStatus(422);

    // Expect nothing is created and return validation error
    $this->assertDatabaseCount($this->tableName, 0);

    expect(RewardPunishmentRecord::count())->toBe(0)
        ->and($response->json())->toHaveFailedGeneralResponse()
        ->toMatchArray([
            'error' => [
                'student_ids' => [
                    'The student ids field is required.'
                ],
                'date' => [
                    'The date field is required.'
                ],
                'reward_punishment_id' => [
                    'The reward punishment id field is required.'
                ],
                'display_in_report_card' => [
                    'The display in report card field is required.'
                ],
            ],
            'data' => null
        ]);
});

test('update success', function () {
    $reward_punishment_record = RewardPunishmentRecord::factory()->create();

    $new_student = Student::factory()->create();

    $payload = [
        'date' => '2025-02-01', // 1st Feb 2025
        'student_id' => $new_student->id,
        'reward_punishment_id' => $reward_punishment_record->reward_punishment_id,
        'display_in_report_card' => true,
        'status' => RewardPunishmentRecordStatus::POSTED->value,
    ];

    $response = $this->putJson(route($this->baseUrl . 'update', $reward_punishment_record->id), $payload);

    $response->assertStatus(200);

    $this->assertDatabaseCount($this->tableName, 1);

    $reward_punishment_record->refresh();
    $reward_punishment_record->load(['student', 'rewardPunishment.meritDemeritSettings', 'rewardPunishment.category', 'rewardPunishment.subCategory']);

    expect(RewardPunishmentRecord::count())->toBe(1)
        ->and($response->json())->toHaveSuccessGeneralResponse()
        ->and($response->json()['data'])
        ->toEqual([
            'id' => $reward_punishment_record->id,
            'date' => $reward_punishment_record->date,
            'student' => resourceToArray(new StudentResource($reward_punishment_record->student)),
            'reward_punishment' => resourceToArray(new RewardPunishmentResource($reward_punishment_record->rewardPunishment)),
            'average_exam_marks' => $reward_punishment_record->average_exam_marks,
            'conduct_marks' => $reward_punishment_record->conduct_marks,
            'display_in_report_card' => $reward_punishment_record->display_in_report_card,
            'status' => $reward_punishment_record->status->value,
            'notification_sent_at' => $reward_punishment_record->notification_sent_at,
        ]);

    $this->assertDatabaseHas($this->tableName, [
        'id' => $reward_punishment_record->id,
        'date' => $payload['date'],
        'student_id' => $payload['student_id'],
        'reward_punishment_id' => $payload['reward_punishment_id'],
        'average_exam_marks' => $reward_punishment_record->rewardPunishment->average_exam_marks,
        'conduct_marks' => $reward_punishment_record->rewardPunishment->conduct_marks,
        'display_in_report_card' => $payload['display_in_report_card'],
        'status' => $payload['status'],
    ]);
});

test('update validation error', function () {
    $reward_punishment_record = RewardPunishmentRecord::factory()->create();

    $copied_reward_punishment_record = $reward_punishment_record->toArray();

    expect(RewardPunishmentRecord::count())->toBe(1);

    $response = $this->putJson(route($this->baseUrl . 'update', $reward_punishment_record->id), []);

    $response->assertStatus(422);

    // Expect return validation error
    expect(RewardPunishmentRecord::count())->toBe(1)
        ->and($response->json())->toMatchArray([
            'error' => [
                'date' => [
                    'The date field is required.'
                ],
                'student_id' => [
                    'The student field is required.'
                ],
                'reward_punishment_id' => [
                    'The reward punishment id field is required.'
                ],
                'display_in_report_card' => [
                    'The display in report card field is required.'
                ],
                'status' => [
                    'The status field is required.'
                ],
            ],
            'data' => null
        ]);

    $this->assertDatabaseHas($this->tableName, [
        'id' => $reward_punishment_record->id,
        'date' => $copied_reward_punishment_record['date'],
        'student_id' => $copied_reward_punishment_record['student_id'],
        'reward_punishment_id' => $copied_reward_punishment_record['reward_punishment_id'],
        'average_exam_marks' => $copied_reward_punishment_record['average_exam_marks'],
        'conduct_marks' => $copied_reward_punishment_record['conduct_marks'],
        'display_in_report_card' => $copied_reward_punishment_record['display_in_report_card'],
        'status' => $copied_reward_punishment_record['status'],
    ]);
});

test('updateStatusInBulk success', function () {
    $reward_punishment_record_1 = RewardPunishmentRecord::factory()->create();
    $reward_punishment_record_2 = RewardPunishmentRecord::factory()->create();
    $reward_punishment_record_3 = RewardPunishmentRecord::factory()->create();

    $payload = [
        'status' => RewardPunishmentRecordStatus::POSTED->value,
        'reward_punishment_record_ids' => [
            $reward_punishment_record_1->id,
            $reward_punishment_record_2->id,
            $reward_punishment_record_3->id,
        ],
    ];

    $response = $this->putJson(route($this->baseUrl . 'bulk-update-status'), $payload);

    $response->assertStatus(200);

    $this->assertDatabaseCount($this->tableName, 3);

    expect($response->json())->toHaveSuccessGeneralResponse();

    $this->assertDatabaseHas($this->tableName, [
        'id' => $reward_punishment_record_1->id,
        'status' => RewardPunishmentRecordStatus::POSTED->value,
    ]);

    $this->assertDatabaseHas($this->tableName, [
        'id' => $reward_punishment_record_2->id,
        'status' => RewardPunishmentRecordStatus::POSTED->value,
    ]);

    $this->assertDatabaseHas($this->tableName, [
        'id' => $reward_punishment_record_3->id,
        'status' => RewardPunishmentRecordStatus::POSTED->value,
    ]);
});

test('updateStatusInBulk validation error', function () {
    expect(RewardPunishmentRecord::count())->toBe(0);

    $response = $this->putJson(route($this->baseUrl . 'bulk-update-status'), []);

    $response->assertStatus(422);

    // Expect return validation error
    expect($response->json())->toMatchArray([
        'error' => [
            'status' => [
                'The status field is required.'
            ],
            'reward_punishment_record_ids' => [
                'The reward punishment record ids field is required.'
            ],
        ],
        'data' => null
    ]);
});

test('delete success', function () {
    $first_reward_punishment_record = RewardPunishmentRecord::factory()->create();
    $other_reward_punishment_records = RewardPunishmentRecord::factory()->count(3)->create();

    $this->assertDatabaseCount($this->tableName, 4);

    //id not exist
    $response = $this->deleteJson(route($this->baseUrl . 'destroy', ['reward_punishment_record' => 9999]))->json();
    expect($response)->toHaveModelResourceNotFoundResponse();

    $this->assertDatabaseCount($this->tableName, 4);

    //delete success
    $response = $this->deleteJson(route($this->baseUrl . 'destroy', ['reward_punishment_record' => $first_reward_punishment_record->id]))->json();

    expect($response)->toHaveSuccessGeneralResponse();

    foreach ($other_reward_punishment_records as $other_reward_punishment_record) {
        $this->assertDatabaseHas($this->tableName, ['id' => $other_reward_punishment_record->id]);
    }
});
