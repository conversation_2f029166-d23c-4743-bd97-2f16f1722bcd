<?php

use App\Enums\AttendanceStatus;
use App\Enums\CardStatus;
use App\Enums\ClassType;
use App\Enums\DietaryRestriction;
use App\Enums\GuardianType;
use App\Enums\LiveStatus;
use App\Enums\MarriedStatus;
use App\Enums\ResponseType;
use App\Enums\SchoolLevel;
use App\Enums\StudentAdmissionType;
use App\Enums\StudentLeaveStatus;
use App\Http\Resources\AttendanceResource;
use App\Http\Resources\CountryResource;
use App\Http\Resources\GradeResource;
use App\Http\Resources\GuardianResource;
use App\Http\Resources\HealthConcernResource;
use App\Http\Resources\HostelBedAssignmentResource;
use App\Http\Resources\HostelRoomBedResource;
use App\Http\Resources\LeadershipPositionResource;
use App\Http\Resources\RaceResource;
use App\Http\Resources\SemesterClassResource;
use App\Http\Resources\StateResource;
use App\Http\Resources\StudentClassResource;
use App\Http\Resources\StudentReportCardResource;
use App\Http\Resources\StudentSocietyPositionResource;
use App\Http\Resources\UserResource;
use App\Models\Attendance;
use App\Models\Card;
use App\Models\ClassModel;
use App\Models\Country;
use App\Models\Education;
use App\Models\Employee;
use App\Models\Grade;
use App\Models\Guardian;
use App\Models\GuardianStudent;
use App\Models\HealthConcern;
use App\Models\HostelBedAssignment;
use App\Models\LatestPrimaryClassBySemesterSettingView;
use App\Models\LeadershipPosition;
use App\Models\LeadershipPositionRecord;
use App\Models\LeaveReason;
use App\Models\Media;
use App\Models\Race;
use App\Models\Religion;
use App\Models\Role;
use App\Models\School;
use App\Models\SemesterClass;
use App\Models\SemesterSetting;
use App\Models\SemesterYearSetting;
use App\Models\SocietyPosition;
use App\Models\State;
use App\Models\Student;
use App\Models\StudentClass;
use App\Models\StudentReportCard;
use App\Models\StudentSocietyPosition;
use App\Models\User;
use App\Services\StudentService;
use Carbon\Carbon;
use Database\Seeders\CurrencySeeder;
use Database\Seeders\InternationalizationSeeder;
use Database\Seeders\PermissionSeeder;
use Database\Seeders\SystemRoleSeeder;
use Illuminate\Database\Eloquent\Factories\Sequence;
use Illuminate\Http\UploadedFile;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Arr;
use Laravel\Sanctum\Sanctum;
use Mockery\MockInterface;

beforeEach(function () {
    $this->seed([
        CurrencySeeder::class,
        InternationalizationSeeder::class,
        PermissionSeeder::class,
        SystemRoleSeeder::class,
    ]);
    $this->user = Employee::factory()->create()->user;
    $this->user->assignRole(Role::SUPERADMIN);
    $this->user->assignRole(Role::STUDENT);

    Sanctum::actingAs($this->user);

    app()->setLocale('en');

    $this->routeNamePrefix = 'students.';

    $this->table = resolve(Student::class)->getTable();
    $this->userTable = resolve(User::class)->getTable();
    $this->guardianTable = resolve(Guardian::class)->getTable();
    $this->guardianStudentTable = resolve(GuardianStudent::class)->getTable();
    $this->mediaTable = resolve(Media::class)->getTable();

    $this->malaysia = Country::factory()->create([
        'name->en' => 'Malaysia'
    ]);

    $this->first_state = State::factory()->create([
        'name->en' => 'Kedah',
        'country_id' => $this->malaysia->id,
    ]);

    $this->second_state = State::factory()->create([
        'name->en' => 'Kelantan',
        'country_id' => $this->malaysia->id,
    ]);

    $this->first_grade = Grade::factory()->create([
        'name->en' => 'Student 1',
        'name->zh' => '初一',
        'sequence' => 1,
    ]);

    $this->second_grade = Grade::factory()->create([
        'name->en' => 'Student 2',
        'name->zh' => '初二',
        'sequence' => 2,
    ]);

    $this->first_religion = Religion::factory()->create([
        'name' => 'Chinese'
    ]);

    $this->second_religion = Religion::factory()->create([
        'name' => 'Chinese (China)'
    ]);

    $this->first_race = Race::factory()->create([
        'name' => 'malay'
    ]);

    $this->second_race = Race::factory()->create([
        'name' => 'chinese'
    ]);
});

test('index without any params', function () {
    $first_student = Student::factory()
        ->has(User::factory())
        ->create();
    $second_student = Student::factory()
        ->has(User::factory())
        ->create();

    $leadership_position = LeadershipPosition::factory()->create();
    LeadershipPositionRecord::factory()->create([
        'student_id' => $first_student->id,
        'leadership_position_id' => $leadership_position->id,
        'created_by' => $this->user->id,
    ]);

    Carbon::setTestNow(Carbon::parse('2024-03-01'));

    $second_semester_setting = SemesterSetting::factory()->create([
        'from' => '2024-01-01',
        'to' => '2024-12-31',
        'is_current_semester' => false
    ]);

    $semester_setting = SemesterSetting::factory()->create([
        'from' => '2024-01-01',
        'to' => '2024-12-31',
        'is_current_semester' => true
    ]);

    $first_student_class = StudentClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'student_id' => $first_student->id,
        'class_type' => ClassType::PRIMARY
    ]);

    StudentClass::factory()->create([
        'semester_setting_id' => $second_semester_setting->id,
        'student_id' => $first_student->id,
        'class_type' => ClassType::PRIMARY
    ]);

    $first_student_class->load([
        'semesterSetting',
        'semesterClass.classModel'
    ]);

    StudentReportCard::factory(3)->state(new Sequence(
        [
            'student_id' => $first_student->id,
            'is_active' => true
        ],
        [
            'student_id' => $first_student->id,
            'is_active' => true
        ],
        [
            'student_id' => $first_student->id,
            'is_active' => false
        ]
    ))->create();

    $response = $this->getJson(route($this->routeNamePrefix . 'index', [
        'includes' => [
            'user',
            'admissionGrade',
            'reportCards',
            'currentSemesterPrimaryClass.semesterSetting',
            'currentSemesterPrimaryClass.semesterClass.classModel',
            'guardians',
            'leadershipPositionRecord.leadershipPosition',
            'primarySchool',
            "nationality",
            "race",
            "religion",
            "state",
            "country",
            "healthConcern",
        ],
        'response' => ResponseType::FULL->value,
        'order_by' => ['id'],
    ]));

    $response->assertStatus(200);

    expect($response->json())->toHaveSuccessGeneralResponse()
        ->and($response->json()['data'])->toHaveCount(2)
        ->and($response->json()['data'][0])
        ->toEqual([
            'id' => $first_student->id,
            'user' => resourceToArray(new UserResource($first_student->user)),
            'name' => $first_student->name,
            'phone_number' => $first_student->phone_number,
            'phone_number_2' => $first_student->phone_number_2,
            'email' => $first_student->email,
            'nric' => $first_student->nric,
            'passport_number' => $first_student->passport_number,
            'admission_year' => $first_student->admission_year,
            'admission_grade' => resourceToArray(new GradeResource($first_student->admissionGrade)),
            'report_cards' => resourceToArray(StudentReportCardResource::collection($first_student->reportCards)),
            'join_date' => $first_student->join_date,
            'leave_date' => $first_student->leave_date,
            'leave_status' => $first_student->leave_status,
            'student_number' => $first_student->student_number,
            'birth_cert_number' => $first_student->birth_cert_number,
            'gender' => $first_student->gender->value,
            'date_of_birth' => $first_student->date_of_birth,
            'birthplace' => $first_student->birthplace,
            'nationality' => resourceToArray(new CountryResource($first_student->nationality)),
            'race' => resourceToArray(new RaceResource($first_student->race)),
            'religion' => resourceToArray(new RaceResource($first_student->religion)),
            'address' => $first_student->address,
            'address_2' => $first_student->address_2,
            'postal_code' => $first_student->postal_code,
            'city' => $first_student->city,
            'state' => resourceToArray(new StateResource($first_student->state)),
            'country' => resourceToArray(new CountryResource($first_student->country)),
            'remarks' => $first_student->remarks,
            'custom_field' => $first_student->custom_field,
            'translations' => $first_student->translations,
            'is_hostel' => $first_student->is_hostel,
            'photo' => $first_student->photo,
            'guardians' => resourceToArray(GuardianResource::collection($first_student->guardians)),
            'leadership_position' => resourceToArray(new LeadershipPositionResource($leadership_position)),
            'current_primary_class' => resourceToArray(new StudentClassResource($first_student_class)),
            'dietary_restriction' => $first_student->dietary_restriction,
            'health_concern' => resourceToArray(new HealthConcernResource($first_student->healthConcern)),
            'primary_school' => null,
            'is_active' => true,
            'admission_type' => $first_student->admission_type->value,
        ]);

    $hostel_bed_assignment = HostelBedAssignment::factory()->create([
        'assignable_type' => Student::class,
        'assignable_id' => $first_student->id,
        'assigned_by' => $this->user->id,
    ]);

    $response = $this->getJson(route($this->routeNamePrefix . 'index', [
        'order_by' => ['id'],
        'includes' => [
            "activeHostelBedAssignments.bed",
            "activeHostelBedAssignments.assignedBy"
        ]

    ]));

    $response->assertStatus(200);

    expect($response->json())->toHaveSuccessGeneralResponse()
        ->and($response->json()['data'])
        ->toHaveCount(2)
        ->toHaveKey('0.active_hostel_bed_assignment', [
            'id' => $hostel_bed_assignment->id,
            'bed' => resourceToArray(new HostelRoomBedResource($hostel_bed_assignment->bed)),
            'assigned_by' => resourceToArray(new UserResource($hostel_bed_assignment->assignedBy)),
            'start_date' => $hostel_bed_assignment->start_date->toISOString(),
            'end_date' => null,
        ]);

    //index with simple response
    $response = $this->getJson(route($this->routeNamePrefix . 'index', [
        'includes' => [
            'user',
            'admissionGrade',
            'currentSemesterPrimaryClass.semesterSetting',
            'currentSemesterPrimaryClass.semesterClass.classModel',
            'guardians',
            'leadershipPositionRecord.leadershipPosition',
            'primarySchool',
            "nationality",
            "race",
            "religion",
            "state",
            "country",
            'healthConcern',
            "activeHostelBedAssignments.bed.hostelRoom.hostelBlock",
            "activeHostelBedAssignments.assignedBy",
        ],
        'order_by' => ['id'],
        'response' => ResponseType::SIMPLE->value,
    ]));

    $response->assertStatus(200);

    $hostel_bed_assignment->load([
        "bed.hostelRoom.hostelBlock",
        "assignedBy"
    ]);

    expect($response->json())->toHaveSuccessGeneralResponse()
        ->and($response->json()['data'])->toHaveCount(2)
        ->and($response->json()['data'][0])
        ->toEqual([
            'id' => $first_student->id,
            'student_number' => $first_student->student_number,
            'userable_reference_number' => $first_student->student_number,
            'name' => $first_student->name,
            'gender' => $first_student->gender->value,
            'translations' => $first_student->translations,
            'leave_status' => $first_student->leave_status,
            'active_hostel_bed_assignment' => resourceToArray(new HostelBedAssignmentResource($hostel_bed_assignment)),
            'is_active' => $first_student->is_active,
        ]);
});

test('index return class based on semester_setting_id', function () {

    $first_student = Student::factory()
        ->has(User::factory())
        ->create();

    $second_student = Student::factory()
        ->has(User::factory())
        ->create();

    $leadership_position = LeadershipPosition::factory()->create();
    LeadershipPositionRecord::factory()->create([
        'student_id' => $first_student->id,
        'leadership_position_id' => $leadership_position->id,
        'created_by' => $this->user->id,
    ]);

    Carbon::setTestNow(Carbon::parse('2024-03-01'));

    $second_semester_setting = SemesterSetting::factory()->create([
        'from' => '2023-01-01',
        'to' => '2023-12-31',
        'is_current_semester' => false
    ]);

    $semester_setting = SemesterSetting::factory()->create([
        'from' => '2024-01-01',
        'to' => '2024-12-31',
        'is_current_semester' => true
    ]);

    $first_student_class = StudentClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'student_id' => $first_student->id,
        'class_type' => ClassType::PRIMARY
    ]);

    $second_student_class = StudentClass::factory()->create([
        'semester_setting_id' => $second_semester_setting->id,
        'student_id' => $first_student->id,
        'class_type' => ClassType::PRIMARY
    ]);

    $first_student_class->load([
        'semesterSetting',
        'semesterClass.classModel'
    ]);

    $second_student_class->load([
        'semesterClass.semesterSetting',
        'semesterClass.classModel'
    ]);

    StudentReportCard::factory(3)->state(new Sequence(
        [
            'student_id' => $first_student->id,
            'is_active' => true
        ],
        [
            'student_id' => $first_student->id,
            'is_active' => true
        ],
        [
            'student_id' => $first_student->id,
            'is_active' => false
        ]
    ))->create();

    LatestPrimaryClassBySemesterSettingView::refreshViewTable(false);

    $response = $this->getJson(route($this->routeNamePrefix . 'index', [
        'includes' => [
            'user',
            'admissionGrade',
            'reportCards',
            'currentSemesterPrimaryClass.semesterSetting',
            'currentSemesterPrimaryClass.semesterClass.classModel',
            'guardians',
            'leadershipPositionRecord.leadershipPosition',
            'primarySchool',
            "nationality",
            "race",
            "religion",
            "state",
            "country",
            "healthConcern",
            "latestPrimaryClassBySemesterSettings.semesterClass.classModel"
        ],
        'response' => ResponseType::FULL->value,
        'order_by' => ['id'],
        'semester_setting_id' => $second_semester_setting->id,
    ]));

    expect($response->json())->toHaveSuccessGeneralResponse()
        ->and($response->json()['data'])->toHaveCount(1)
        ->and($response->json()['data'][0])
        ->toEqual([
            'id' => $first_student->id,
            'user' => resourceToArray(new UserResource($first_student->user)),
            'name' => $first_student->name,
            'phone_number' => $first_student->phone_number,
            'phone_number_2' => $first_student->phone_number_2,
            'email' => $first_student->email,
            'nric' => $first_student->nric,
            'passport_number' => $first_student->passport_number,
            'admission_year' => $first_student->admission_year,
            'admission_grade' => resourceToArray(new GradeResource($first_student->admissionGrade)),
            'report_cards' => resourceToArray(StudentReportCardResource::collection($first_student->reportCards)),
            'join_date' => $first_student->join_date,
            'leave_date' => $first_student->leave_date,
            'student_number' => $first_student->student_number,
            'birth_cert_number' => $first_student->birth_cert_number,
            'gender' => $first_student->gender->value,
            'date_of_birth' => $first_student->date_of_birth,
            'birthplace' => $first_student->birthplace,
            'nationality' => resourceToArray(new CountryResource($first_student->nationality)),
            'race' => resourceToArray(new RaceResource($first_student->race)),
            'religion' => resourceToArray(new RaceResource($first_student->religion)),
            'address' => $first_student->address,
            'address_2' => $first_student->address_2,
            'postal_code' => $first_student->postal_code,
            'city' => $first_student->city,
            'state' => resourceToArray(new StateResource($first_student->state)),
            'country' => resourceToArray(new CountryResource($first_student->country)),
            'remarks' => $first_student->remarks,
            'custom_field' => $first_student->custom_field,
            'translations' => $first_student->translations,
            'is_hostel' => $first_student->is_hostel,
            'photo' => $first_student->photo,
            'guardians' => resourceToArray(GuardianResource::collection($first_student->guardians)),
            'leadership_position' => resourceToArray(new LeadershipPositionResource($leadership_position)),
            'current_primary_class' => resourceToArray(new StudentClassResource($first_student_class)),
            'dietary_restriction' => $first_student->dietary_restriction,
            'health_concern' => resourceToArray(new HealthConcernResource($first_student->healthConcern)),
            'primary_school' => null,
            'selected_semester_class' => resourceToArray(new SemesterClassResource($second_student_class->semesterClass)),
            'is_active' => true,
            'admission_type' => $first_student->admission_type->value,
            'leave_status' => null,
        ]);


});

test('index determine getPaginated per_page is not -1', function () {

    $this->mock(StudentService::class, function (MockInterface $mock) {
        $student1 = Student::factory()->create([]);
        $student2 = Student::factory()->create([]);

        $mock->shouldReceive('getAllPaginatedStudents')->once()->andReturn(new LengthAwarePaginator([$student1, $student2], 2, 1));
    });

    $response = $this->getJson(route($this->routeNamePrefix . 'index', [
        'per_page' => 10,
        'page' => 1,
    ]));

    $response->assertStatus(200);

    expect($response->json())->toHaveKey('pagination');

});


test('index determine getAll per_page is -1', function () {

    $this->mock(StudentService::class, function (MockInterface $mock) {
        $student1 = Student::factory()->create([]);
        $student2 = Student::factory()->create([]);

        $mock->shouldReceive('getAllStudents')->once()->andReturn(collect([$student1, $student2]));
    });

    $response = $this->getJson(route($this->routeNamePrefix . 'index', [
        'per_page' => -1,
        'page' => 1,
    ]));

    $response->assertStatus(200);

    expect($response->json())->not()->toHaveKey('pagination');
});

test('index with params order by name', function () {

    $first_student = Student::factory()->create([
        'name->en' => 'Student 1',
        'name->zh' => '学生 1',
        'phone_number' => '12345678',
        'email' => '<EMAIL>',
        'admission_grade_id' => $this->first_grade->id,
        'admission_year' => '2022',
        'join_date' => '2022-01-01',
        'student_number' => '001',
        'birth_cert_number' => '001',
        'nric' => '001',
        'passport_number' => '001',
        'gender' => 'MALE',
        'date_of_birth' => '2001-01-01',
        'birthplace' => $this->malaysia->name,
        'nationality_id' => $this->malaysia->id,
        'race_id' => $this->first_race->id,
        'religion_id' => $this->first_religion->id,
        'address' => '123 Main Street',
        'postal_code' => '12345',
        'city' => 'Petaling Jaya',
        'state_id' => $this->first_state->id,
        'country_id' => $this->malaysia->id,
        'remarks' => 'This is a student remark for student 1',
        'custom_field' => [
            'key' => 'custom_key',
            'value' => 'custom_value'
        ],
    ]);

    $second_student = Student::factory()->create([
        'name->en' => 'Student 2',
        'name->zh' => '学生 2',
        'phone_number' => '*********',
        'email' => '<EMAIL>',
        'admission_grade_id' => $this->first_grade->id,
        'admission_year' => '2022',
        'join_date' => '2024-11-01',
        'student_number' => '002',
        'birth_cert_number' => '002',
        'nric' => '002',
        'passport_number' => '002',
        'gender' => 'MALE',
        'date_of_birth' => '2002-02-02',
        'birthplace' => $this->malaysia->name,
        'nationality_id' => $this->malaysia->id,
        'race_id' => $this->first_race->id,
        'religion_id' => $this->first_religion->id,
        'address' => '123 Main Street',
        'postal_code' => '12345',
        'city' => 'Damansara',
        'state_id' => $this->first_state->id,
        'country_id' => $this->malaysia->id,
        'remarks' => 'This is a student remark for student 2',
        'custom_field' => [
            'key' => 'custom_key',
            'value' => 'custom_value'
        ],
    ]);

    expect(Student::count())->toBe(2);

    // Sort by name asc
    $response = $this->getJson(route($this->routeNamePrefix . 'index', [
        'order_by' => ['name' => 'asc'],
    ]));

    $response->assertStatus(200);

    expect($response->json())->toHaveSuccessGeneralResponse()
        ->and($response->json()['data'][0]['name'])
        ->toEqual($first_student->name);

    // Sort by name desc
    $response = $this->getJson(route($this->routeNamePrefix . 'index', [
        'order_by' => ['name' => 'desc'],
    ]));

    $response->assertStatus(200);

    expect($response->json())->toHaveSuccessGeneralResponse()
        ->and($response->json()['data'][0]['name'])
        ->toEqual($second_student->name);
});

test('index with params order by id', function () {

    $first_student = Student::factory()->create([
        'name->en' => 'Student 1',
        'name->zh' => '学生 1',
        'phone_number' => '12345678',
        'email' => '<EMAIL>',
        'admission_grade_id' => $this->first_grade->id,
        'admission_year' => '2022',
        'join_date' => '2022-01-01',
        'student_number' => '001',
        'birth_cert_number' => '001',
        'nric' => '001',
        'passport_number' => '001',
        'gender' => 'MALE',
        'date_of_birth' => '2001-01-01',
        'birthplace' => $this->malaysia->name,
        'nationality_id' => $this->malaysia->id,
        'race_id' => $this->first_race->id,
        'religion_id' => $this->first_religion->id,
        'address' => '123 Main Street',
        'postal_code' => '12345',
        'city' => 'Petaling Jaya',
        'state_id' => $this->first_state->id,
        'country_id' => $this->malaysia->id,
        'remarks' => 'This is a student remark for student 1',
        'custom_field' => [
            'key' => 'custom_key',
            'value' => 'custom_value'
        ],
    ]);

    $second_student = Student::factory()->create([
        'name->en' => 'Student 2',
        'name->zh' => '学生 2',
        'phone_number' => '*********',
        'email' => '<EMAIL>',
        'admission_grade_id' => $this->first_grade->id,
        'admission_year' => '2022',
        'join_date' => '2024-11-01',
        'student_number' => '002',
        'birth_cert_number' => '002',
        'nric' => '002',
        'passport_number' => '002',
        'gender' => 'MALE',
        'date_of_birth' => '2002-02-02',
        'birthplace' => $this->malaysia->name,
        'nationality_id' => $this->malaysia->id,
        'race_id' => $this->first_race->id,
        'religion_id' => $this->first_religion->id,
        'address' => '123 Main Street',
        'postal_code' => '12345',
        'city' => 'Damansara',
        'state_id' => $this->first_state->id,
        'country_id' => $this->malaysia->id,
        'remarks' => 'This is a student remark for student 2',
        'custom_field' => [
            'key' => 'custom_key',
            'value' => 'custom_value'
        ],
    ]);

    expect(Student::count())->toBe(2);

    // Sort by id asc
    $response = $this->getJson(route($this->routeNamePrefix . 'index', [
        'order_by' => ['id' => 'asc'],
    ]));

    $response->assertStatus(200);

    expect($response->json())->toHaveSuccessGeneralResponse()
        ->and($response->json()['data'][0]['id'])
        ->toEqual($first_student->id);

    // Sort by sequence desc
    $response = $this->getJson(route($this->routeNamePrefix . 'index', [
        'order_by' => ['id' => 'desc'],
    ]));

    $response->assertStatus(200);

    expect($response->json())->toHaveSuccessGeneralResponse()
        ->and($response->json()['data'][0]['id'])
        ->toEqual($second_student->id);
});

test('index with society positions', function () {
    $students = Student::factory(2)->state(new Sequence(
        [
            'name->en' => 'David',
        ],
        [
            'name->en' => 'Simon',
        ],
    ))->create();

    $positions = SocietyPosition::factory(3)->state(new Sequence(
        [
            'name->en' => 'Treasurer',
        ],
        [
            'name->en' => 'President',
        ],
        [
            'name->en' => 'Secretary',
        ],
    ))->create();

    $semester_setting = SemesterSetting::factory()->create();

    $classes = ClassModel::factory(2)->state(new Sequence(
        [
            'name->en' => 'PBSM',
            'type' => ClassType::SOCIETY->value,
        ],
        [
            'name->en' => 'Photography',
            'type' => ClassType::SOCIETY->value,
        ],
    ))->create();

    $semester_classes = SemesterClass::factory(2)->state(new Sequence(
        [
            'semester_setting_id' => $semester_setting->id,
            'class_id' => $classes[0]->id,
        ],
        [
            'semester_setting_id' => $semester_setting->id,
            'class_id' => $classes[1]->id,
        ],
    ))->create();

    $student_classes = StudentClass::factory(4)->state(new Sequence(
    // PBSM
        [
            'semester_setting_id' => $semester_setting->id,
            'semester_class_id' => $semester_classes[0]->id, // PBSM
            'class_type' => $semester_classes[0]->classModel->type,
            'student_id' => $students[0]->id, // David
        ],
        [
            'semester_setting_id' => $semester_setting->id,
            'semester_class_id' => $semester_classes[0]->id, // PBSM
            'class_type' => $semester_classes[0]->classModel->type,
            'student_id' => $students[1]->id, // Simon
        ],
        // Photography
        [
            'semester_setting_id' => $semester_setting->id,
            'semester_class_id' => $semester_classes[1]->id, // Photography
            'class_type' => $semester_classes[1]->classModel->type,
            'student_id' => $students[0]->id, // David
        ],
        [
            'semester_setting_id' => $semester_setting->id,
            'semester_class_id' => $semester_classes[1]->id, // Photography
            'class_type' => $semester_classes[1]->classModel->type,
            'student_id' => $students[1]->id, // Simon
        ],
    ))->create();

    $student_society_positions = StudentSocietyPosition::factory(6)->state(new Sequence(
    // $semester_classes[0]
        [
            'semester_class_id' => $semester_classes[0]->id,
            'student_id' => $students[0]->id, // David
            'society_position_id' => $positions[0]->id, // Treasurer
        ],
        [
            'semester_class_id' => $semester_classes[0]->id,
            'student_id' => $students[0]->id, // David
            'society_position_id' => $positions[1]->id, // President
        ],
        [
            'semester_class_id' => $semester_classes[0]->id,
            'student_id' => $students[1]->id, // Simon
            'society_position_id' => $positions[1]->id, // President
        ],
        [
            'semester_class_id' => $semester_classes[0]->id,
            'student_id' => $students[1]->id, // Simon
            'society_position_id' => $positions[2]->id, // Secretary
        ],
        // $semester_classes[1]
        [
            'semester_class_id' => $semester_classes[1]->id,
            'student_id' => $students[0]->id, // David
            'society_position_id' => $positions[0]->id, // Treasurer
        ],
        [
            'semester_class_id' => $semester_classes[1]->id,
            'student_id' => $students[1]->id, // Simon
            'society_position_id' => $positions[1]->id, // President
        ],
    ))->create();

    /**
     *
     * filter students positions by $semester_classes[0]->id
     *
     */
    $payload = [
        'includes' => [
            'societyPositions.societyPosition',
        ],
        'response' => ResponseType::SIMPLE->value,
        'semester_class_id' => $semester_classes[0]->id,
        'order_by' => ['id']
    ];

    $response = $this->getJson(route($this->routeNamePrefix . 'index', $payload));

    $response->assertStatus(200);

    expect($response->json())->toHaveSuccessGeneralResponse()
        ->and($response->json()['data'])->toHaveCount(2)
        ->and($response->json()['data'][0]['society_positions'])
        ->toEqual([
            resourceToArray(new StudentSocietyPositionResource($student_society_positions[0]->loadMissing('societyPosition'))), // David Treasurer
            resourceToArray(new StudentSocietyPositionResource($student_society_positions[1]->loadMissing('societyPosition'))), // David President
        ])
        ->and($response->json()['data'][1]['society_positions'])
        ->toEqual([
            resourceToArray(new StudentSocietyPositionResource($student_society_positions[2]->loadMissing('societyPosition'))), // Simon President
            resourceToArray(new StudentSocietyPositionResource($student_society_positions[3]->loadMissing('societyPosition'))), // Simon Secretary
        ]);

    /**
     *
     * filter students positions by $semester_classes[1]->id
     *
     */
    $payload = [
        'includes' => [
            'societyPositions.societyPosition',
        ],
        'response' => ResponseType::SIMPLE->value,
        'semester_class_id' => $semester_classes[1]->id,
        'order_by' => ['id']
    ];

    $response = $this->getJson(route($this->routeNamePrefix . 'index', $payload));

    $response->assertStatus(200);

    expect($response->json())->toHaveSuccessGeneralResponse()
        ->and($response->json()['data'])->toHaveCount(2)
        ->and($response->json()['data'][0]['society_positions'])
        ->toEqual([
            resourceToArray(new StudentSocietyPositionResource($student_society_positions[4]->loadMissing('societyPosition'))), // David Treasurer
        ])
        ->and($response->json()['data'][1]['society_positions'])
        ->toEqual([
            resourceToArray(new StudentSocietyPositionResource($student_society_positions[5]->loadMissing('societyPosition'))), // Simon President
        ]);


    /**
     *
     * get students positions by all classes
     *
     */
    $payload = [
        'includes' => [
            'societyPositions.societyPosition',
        ],
        'response' => ResponseType::SIMPLE->value,
        'order_by' => ['id']
    ];

    $response = $this->getJson(route($this->routeNamePrefix . 'index', $payload));

    $response->assertStatus(200);

    expect($response->json())->toHaveSuccessGeneralResponse()
        ->and($response->json()['data'])->toHaveCount(2)
        ->and($response->json()['data'][0]['society_positions'])
        ->toEqual([
            resourceToArray(new StudentSocietyPositionResource($student_society_positions[0]->loadMissing('societyPosition'))), // David Treasurer
            resourceToArray(new StudentSocietyPositionResource($student_society_positions[1]->loadMissing('societyPosition'))), // David President
            resourceToArray(new StudentSocietyPositionResource($student_society_positions[4]->loadMissing('societyPosition'))), // David Treasurer
        ])
        ->and($response->json()['data'][1]['society_positions'])
        ->toEqual([
            resourceToArray(new StudentSocietyPositionResource($student_society_positions[2]->loadMissing('societyPosition'))), // Simon President
            resourceToArray(new StudentSocietyPositionResource($student_society_positions[3]->loadMissing('societyPosition'))), // Simon Secretary
            resourceToArray(new StudentSocietyPositionResource($student_society_positions[5]->loadMissing('societyPosition'))), // Simon President
        ]);
});

test('index with attendance date', function () {

    $semester_class = SemesterClass::factory()->create();
    $semester_class2 = SemesterClass::factory()->create();

    // student and student2 (active), same class, one with attendance, one without attendance
    $student = Student::factory()->create();
    $student_attendance = Attendance::create([
        'attendance_recordable_type' => Student::class,
        'attendance_recordable_id' => $student->id,
        'date' => "2024-12-01",
        'time_in' => null,
        'time_out' => null,
        'on_time' => false,
        'is_present' => false,
        'status' => AttendanceStatus::ABSENT,
    ]);
    StudentClass::factory()->create([
        'semester_setting_id' => $semester_class->semester_setting_id,
        'semester_class_id' => $semester_class->id,
        'class_type' => $semester_class->classModel->type,
        'student_id' => $student->id,
        'is_active' => true,
    ]);

    $student2 = Student::factory()->create();
    StudentClass::factory()->create([
        'semester_setting_id' => $semester_class->semester_setting_id,
        'semester_class_id' => $semester_class->id,
        'class_type' => $semester_class->classModel->type,
        'student_id' => $student2->id,
        'is_active' => true,
    ]);

    // student3 (deactivated), same class as student and student2
    $student3 = Student::factory()->create();
    StudentClass::factory()->create([
        'semester_setting_id' => $semester_class->semester_setting_id,
        'semester_class_id' => $semester_class->id,
        'class_type' => $semester_class->classModel->type,
        'student_id' => $student3->id,
        'is_active' => false,
    ]);

    // student4 (active), different class
    $student4 = Student::factory()->create();
    StudentClass::factory()->create([
        'semester_setting_id' => $semester_class2->semester_setting_id,
        'semester_class_id' => $semester_class2->id,
        'class_type' => $semester_class2->classModel->type,
        'student_id' => $student4->id,
        'is_active' => true,
    ]);

    $response = $this->getJson(route($this->routeNamePrefix . 'index', [
        'attendance_date' => '2024-12-01',
        'only_active_class' => true,
        'semester_setting_id' => $semester_class->semester_setting_id,
        'semester_class_id' => $semester_class->id,
        'response' => ResponseType::SIMPLE->value,
        'order_by' => ['id' => 'asc'],
    ]))->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toMatchArray([
            [
                'id' => $student->id,
                'student_number' => $student->student_number,
                'name' => $student->name,
                'gender' => $student->gender->value,
                'translations' => $student->translations,
                'is_active' => $student->is_active,
                'attendances' => resourceToArray(new AttendanceResource($student_attendance)),
                'userable_reference_number' => $student->student_number,
                'leave_status' => null,
            ],
            [
                'id' => $student2->id,
                'student_number' => $student2->student_number,
                'name' => $student2->name,
                'gender' => $student2->gender->value,
                'translations' => $student2->translations,
                'is_active' => $student2->is_active,
                'attendances' => null,
                'userable_reference_number' => $student2->student_number,
                'leave_status' => null,
            ],
        ]);
});
test('index with guardian properties', function () {
    $student_A1 = Student::factory()->create([
        'name->en' => 'Student A1',
        'email' => '<EMAIL>',
        'phone_number' => '+60128889999',
        'student_number' => 'STUDENT-A001',
    ]);

    $student_A2 = Student::factory()->create([
        'name->en' => 'Student A2',
        'email' => '<EMAIL>',
        'phone_number' => '+60123334444',
        'student_number' => 'STUDENT-A002',
    ]);

    $guardian_A = Guardian::factory()->create([
        'name->en' => 'Father A',
        'email' => '<EMAIL>',
        'phone_number' => '+60175558888',
    ]);

    $student_B = Student::factory()->create([
        'name->en' => 'Student B2',
        'email' => '<EMAIL>',
        'phone_number' => '+60134569875',
        'student_number' => 'STUDENT-B001',
    ]);


    $guardian_B = Guardian::factory()->create([
        'name->en' => 'Father B',
        'email' => '<EMAIL>',
        'phone_number' => '+60185550000',
    ]);

    GuardianStudent::factory(4)->state(new Sequence(
        [
            'guardian_id' => $guardian_A->id,
            'studenable_id' => $student_A1->id,
            'studenable_type' => Student::class,
            'is_direct_dependant' => true
        ],
        [
            'guardian_id' => $guardian_A->id,
            'studenable_id' => $student_A2->id,
            'studenable_type' => Student::class,
            'is_direct_dependant' => true
        ],
        [
            'guardian_id' => $guardian_B->id,
            'studenable_id' => $student_B->id,
            'studenable_type' => Student::class,
            'is_direct_dependant' => true
        ],
        [
            'guardian_id' => $guardian_B->id,
            'studenable_id' => $student_A2->id,
            'studenable_type' => Student::class,
            'is_direct_dependant' => false
        ]

    ))->create();

    // Guardian Name test - Return student A1 and student A2
    $payload = [
        'guardian_name' => $guardian_A->name,
        'order_by' => ['id']
    ];

    $response = $this->getJson(route($this->routeNamePrefix . 'index', $payload))->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toHaveCount(2)
        ->and($response['data'][0])->toMatchArray([
            'id' => $student_A1->id,
            'student_number' => $student_A1->student_number,
        ])
        ->and($response['data'][1])->toMatchArray([
            'id' => $student_A2->id,
            'student_number' => $student_A2->student_number,
        ]);

    // Guardian Phone Number test - Return student A1 and student A2
    $payload = [
        'guardian_phone_number' => $guardian_A->phone_number,
        'order_by' => ['id']
    ];

    $response = $this->getJson(route($this->routeNamePrefix . 'index', $payload))->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toHaveCount(2)
        ->and($response['data'][0])->toMatchArray([
            'id' => $student_A1->id,
            'student_number' => $student_A1->student_number,
        ])
        ->and($response['data'][1])->toMatchArray([
            'id' => $student_A2->id,
            'student_number' => $student_A2->student_number,
        ]);


    // Guardian Email test - Return student B
    $payload = [
        'guardian_email' => $guardian_B->email,
        'order_by' => ['id']
    ];

    $response = $this->getJson(route($this->routeNamePrefix . 'index', $payload))->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toHaveCount(2)
        ->and($response['data'][0])->toMatchArray([
            'id' => $student_A2->id,
            'student_number' => $student_A2->student_number,
        ])
        ->and($response['data'][1])->toMatchArray([
            'id' => $student_B->id,
            'student_number' => $student_B->student_number,
        ]);

});


test('create success', function () {
    config([
        'school.email_domain' => '@smpinhwa.edu.my'
    ]);
    $guardian = Guardian::factory()->create();
    $education = Education::factory()->create();
    $health_concern = HealthConcern::factory()->create();
    $primary_school = School::factory()->create([
        'level' => SchoolLevel::PRIMARY,
    ]);

    expect(Student::count())->toBe(0)
        ->and(User::count())->toBe(2)
        ->and(Guardian::count())->toBe(1)
        ->and(GuardianStudent::count())->toBe(0);

    $payload = [
        'name' => [
            'en' => 'Student 1',
            'zh' => '学生 1',
        ],
        'phone_number' => '+601122223333',
        'phone_number_2' => '+60*********',
        'nric' => '990101111111',
        'passport_number' => '001',
        'admission_grade_id' => $this->first_grade->id,
        'admission_year' => '2022',
        'join_date' => '2022-01-01',
        // Auto generated by system
//        'student_number' => '001',
        'birth_cert_number' => '001',
        'gender' => 'MALE',
        'date_of_birth' => '2001-01-01',
        'phone_no' => '*********',
        'birthplace' => $this->malaysia->name,
        'nationality_id' => $this->malaysia->id,
        'race_id' => $this->first_race->id,
        'religion_id' => $this->first_religion->id,
        'address' => '123 Main Street',
        'address_2' => '12345 Main Street 2',
        'postal_code' => '12345',
        'city' => 'Petaling Jaya',
        'state_id' => $this->first_state->id,
        'country_id' => $this->malaysia->id,
        'is_hostel' => true,
        'remarks' => 'This is a student remark for student 1',
        'leave_date' => '2024-01-01',
        'photo' => UploadedFile::fake()->create('first_file.png', 500),
        'custom_field' => json_encode([
            'key' => 'custom_key',
            'value' => 'custom_value'
        ]),
        'guardians' => [
            [
                'type' => GuardianType::FATHER->value,
                'name' => [
                    'en' => 'Father 1',
                ],
                'phone_number' => '+60*********',
                'email' => '<EMAIL>',
                'with_user_account' => true,
                'nric' => '1234',
                'is_primary' => true,
                'live_status' => LiveStatus::NORMAL->value,
                'relation_to_student' => 'Papa',
                'is_direct_dependant' => false,
            ],
            [
                'type' => GuardianType::MOTHER->value,
                'name' => [
                    'en' => 'Mother 1',
                ],
                'phone_number' => '+***********',
                'email' => '<EMAIL>',
                'with_user_account' => false,
                'nric' => null,
                'is_primary' => false,
                'live_status' => LiveStatus::SICK->value,
                'relation_to_student' => 'Mama',
                'is_direct_dependant' => false,
            ],
            [
                'id' => $guardian->id,
                'type' => GuardianType::GUARDIAN->value,
                'name' => [
                    'en' => 'Guardian 1',
                ],
                'phone_number' => '+************',
                'email' => '<EMAIL>',
                'with_user_account' => false,
                'nric' => '33333',
                'education_id' => $education->id,
                'married_status' => MarriedStatus::WIDOWED->value,
                'occupation' => 'IT',
                'occupation_description' => 'Fix Computer',
                'is_primary' => false,
                'live_status' => LiveStatus::NORMAL->value,
                'relation_to_student' => 'Grandpa',
                'is_direct_dependant' => true,
            ]
        ],
        'dietary_restriction' => DietaryRestriction::NONE->value,
        'health_concern_id' => $health_concern->id,
        'primary_school_id' => $primary_school->id,
        'admission_type' => StudentAdmissionType::NEW->value,
    ];

    $response = $this->postJson(route($this->routeNamePrefix . 'create'), $payload);

    $response->assertStatus(200);

    // Expect only one record is created
    expect(Student::count())->toBe(1)
        ->and(User::count())->toBe(4)
        ->and(Guardian::count())->toBe(3)
        ->and(GuardianStudent::count())->toBe(3)
        ->and($response->json())->toMatchArray([
            'status' => 'OK',
            'code' => 200,
            'message' => 'Success.',
        ])
        ->and($response->json()['data'])
        ->toMatchArray([
            'name' => $payload['name']['en'],
            'phone_number' => $payload['phone_number'],
            'phone_number_2' => $payload['phone_number_2'],
            'email' => '<EMAIL>',
            'nric' => $payload['nric'],
            'passport_number' => $payload['passport_number'],
            'admission_year' => $payload['admission_year'],
//            'admission_grade' => resourceToArray(new GradeResource($this->first_grade)),
            'join_date' => $payload['join_date'],
            'student_number' => "H2022001", // Auto generated by system,
            'birthplace' => $this->malaysia->name,
//            'nationality' => resourceToArray(new CountryResource($this->malaysia)),
            'date_of_birth' => $payload['date_of_birth'],
            'gender' => $payload['gender'],
            'birth_cert_number' => $payload['birth_cert_number'],
//            'race' => resourceToArray(new RaceResource($this->first_race)),
//            'religion' => resourceToArray(new ReligionResource($this->first_religion)),
            'address' => $payload['address'],
            'address_2' => $payload['address_2'],
            'postal_code' => $payload['postal_code'],
            'city' => $payload['city'],
//            'state' => resourceToArray(new StateResource($this->first_state)),
//            'country' => resourceToArray(new CountryResource($this->malaysia)),
            'remarks' => $payload['remarks'],
            'is_hostel' => $payload['is_hostel'],
            'dietary_restriction' => $payload['dietary_restriction'],
//            'health_concern' => resourceToArray(new HealthConcernResource($health_concern)),
//            'primary_school' => resourceToArray(new SchoolResource($primary_school)),
        ]);

    //student user
    $this->assertDatabaseHas($this->userTable, [
        'phone_number' => $payload['phone_number'],
        'email' => '<EMAIL>',
    ]);

    //guardian user
    $this->assertDatabaseHas($this->userTable, [
        'phone_number' => $payload['guardians'][0]['phone_number'],
        'email' => $payload['guardians'][0]['email'],
    ]);

    foreach ($payload['guardians'] as $guardian_data) {
        $data = [
            'name->en' => $guardian_data['name']['en'],
            'phone_number' => $guardian_data['phone_number'],
            'email' => $guardian_data['email'],
            'nric' => $guardian_data['nric'],
            'education_id' => Arr::get($guardian_data, 'education_id'),
            'married_status' => Arr::get($guardian_data, 'married_status'),
            'occupation' => Arr::get($guardian_data, 'occupation'),
            'occupation_description' => Arr::get($guardian_data, 'occupation_description'),
            'live_status' => $guardian_data['live_status'],
        ];

        if (isset($guardian_data['id'])) {
            $data['id'] = $guardian_data['id'];
        }

        $this->assertDatabaseHas($this->guardianTable, $data);

        $data = [
            'type' => $guardian_data['type'],
            'studenable_type' => Student::class,
            'studenable_id' => $response->json()['data']['id'],
            'is_primary' => $guardian_data['is_primary'],
            'relation_to_student' => $guardian_data['relation_to_student'],
            'is_direct_dependant' => $guardian_data['is_direct_dependant'],
        ];

        if (isset($guardian_data['id'])) {
            $data['guardian_id'] = $guardian_data['id'];
        }

        $this->assertDatabaseHas($this->guardianStudentTable, $data);
    }
});

test('create validation error', function () {
    expect(Student::count())->toBe(0);

    $secondary_school = School::factory()->create([
        'level' => SchoolLevel::SECONDARY,
    ]);

    $input = [
        'primary_school_id' => $secondary_school->id,
    ];

    $response = $this->postJson(route($this->routeNamePrefix . 'create'), $input);

    $response->assertStatus(422);

    // Expect nothing is created and return validation error
    $this->assertDatabaseCount($this->table, 0);

    expect(Student::count())->toBe(0)
        ->and($response->json())->toHaveFailedGeneralResponse()
        ->and($response->json()['error'])->toBe([
            'name' => [
                'The name field is required.'
            ],
            'admission_year' => [
                'The admission year field is required.'
            ],
            'admission_grade_id' => [
                'The admission grade id field is required.'
            ],
            'join_date' => [
                'The join date field is required.'
            ],
            'nationality_id' => [
                'The nationality id field is required.'
            ],
            'date_of_birth' => [
                'The date of birth field is required.'
            ],
            'gender' => [
                'The gender field is required.'
            ],
            'birth_cert_number' => [
                'The birth cert number field is required.'
            ],
            'nric' => [
                'NRIC is needed if the Passport Number field is blank.',
            ],
            'passport_number' => [
                'Passport Number is needed if the NRIC field is blank.',
            ],
            'race_id' => [
                'The race id field is required.'
            ],
            'religion_id' => [
                'The religion id field is required.'
            ],
            'address' => [
                'The address field is required.'
            ],
            'postal_code' => [
                'The postal code field is required.'
            ],
            'city' => [
                'The city field is required.'
            ],
            'state_id' => [
                'The state id field is required.'
            ],
            'country_id' => [
                'The country id field is required.'
            ],
            'is_hostel' => [
                'The is hostel field is required.'
            ],
            'dietary_restriction' => [
                'The dietary restriction field is required.'
            ],
            'health_concern_id' => [
                'The health concern id field is required.'
            ],
            'primary_school_id' => [
                'The selected primary school id is invalid.'
            ],
            'admission_type' => [
                'The admission type field is required.'
            ],
        ]);

    $response = $this->postJson(route($this->routeNamePrefix . 'create'), [
        'nric' => '*********0'
    ]);
    expect(Student::count())->toBe(0)
        ->and($response->json())->toHaveFailedGeneralResponse()
        ->and($response->json()['error'])->toMatchArray([
            'nric' => [
                'The nric field must be 12 digits.'
            ],
        ]);

    // without nric and passport number
    $response = $this->postJson(route($this->routeNamePrefix . 'create'), []);
    expect(Student::count())->toBe(0)
        ->and($response->json())->toHaveFailedGeneralResponse()
        ->and($response->json()['error'])->toMatchArray([
            'nric' => [
                'NRIC is needed if the Passport Number field is blank.'
            ],
            'passport_number' => [
                'Passport Number is needed if the NRIC field is blank.'
            ],
        ]);

    // only nric
    $response = $this->postJson(route($this->routeNamePrefix . 'create'), ['nric' => '12345']);
    // nric field and passport number field shouldn't throw error
    expect(Student::count())->toBe(0)
        ->and($response->json())->toHaveFailedGeneralResponse()
        ->and($response->json()['error'])->not->toMatchArray([
            'nric' => [
                'NRIC is needed if the Passport Number field is blank.'
            ],
            'passport_number' => [
                'Passport Number is needed if the NRIC field is blank.'
            ],
        ]);

    // only passport number
    $response = $this->postJson(route($this->routeNamePrefix . 'create'), ['passport_number' => '12345']);
    // nric field and passport number field shouldn't throw error
    expect(Student::count())->toBe(0)
        ->and($response->json())->toHaveFailedGeneralResponse()
        ->and($response->json()['error'])->not->toMatchArray([
            'nric' => [
                'NRIC is needed if the Passport Number field is blank.'
            ],
            'passport_number' => [
                'Passport Number is needed if the NRIC field is blank.'
            ],
        ]);

    Student::factory()->create(['nric' => '*********012', 'passport_number' => '**********']);
    $response = $this->postJson(route($this->routeNamePrefix . 'create'), ['nric' => '*********012', 'passport_number' => '**********']);

    // nric and passport number must be unique
    expect(Student::count())->toBe(1) // used factory to create 1 student
    ->and($response->json())->toHaveFailedGeneralResponse()
        ->and($response->json()['error'])->toMatchArray([
            'nric' => [
                'The nric has already been taken.'
            ],
            'passport_number' => [
                'The passport number has already been taken.'
            ],
        ]);
});

test('create without exist foreign id', function () {
    expect(Student::count())->toBe(0);

    $payload = [
        'admission_grade_id' => 10,
        'admission_year' => '2022',
        'join_date' => '2022-01-01',
        'name' => [
            'en' => 'Student 1',
            'zh' => '学生 1',
        ],
        'student_number' => '001',
        'birth_cert_number' => '001',
        'nric' => '001',
        'passport_number' => '001',
        'gender' => 'MALE',
        'date_of_birth' => '2001-01-01',
        'email' => '<EMAIL>',
        'phone_no' => '*********',
        'birthplace' => 'Malaysia',
        'nationality_id' => 10,
        'race_id' => 10,
        'religion_id' => 10,
        'address' => '123 Main Street',
        'postal_code' => '12345',
        'city' => 'Petaling Jaya',
        'state_id' => 10,
        'country_id' => 10,
        'remarks' => 'This is a student remark for student 1',
        'custom_field' => json_encode([
            'key' => 'custom_key',
            'value' => 'custom_value'
        ]),
        'guardians' => [
            [
                'type' => GuardianType::FATHER->value,
                'name' => [
                    'en' => 'Father 1',
                ],
                'phone_number' => '+60*********',
                'email' => '<EMAIL>',
                'with_user_account' => true,
            ],
            [
                'type' => GuardianType::MOTHER->value,
                'name' => [
                    'en' => 'Mother 1',
                ],
                'phone_number' => '+***********',
                'email' => '<EMAIL>',
                'with_user_account' => false,
            ],
            [
                'type' => GuardianType::GUARDIAN->value,
                'name' => [
                    'en' => 'Guardian 1',
                ],
                'phone_number' => '+************',
                'email' => '<EMAIL>',
                'with_user_account' => false,
            ]
        ],
        'dietary_restriction' => DietaryRestriction::NONE->value,
        'health_concern_id' => HealthConcern::factory()->create()->id,
        'admission_type' => StudentAdmissionType::NEW->value,
    ];

    $response = $this->postJson(route($this->routeNamePrefix . 'create'), $payload);

    $response->assertStatus(422);

    // Expect nothing is created and return validation error
    $this->assertDatabaseCount($this->table, 0);

    expect(Student::count())->toBe(0)
        ->and($response->json())->toHaveFailedGeneralResponse()
        ->toMatchArray([
            'error' => [
                'admission_grade_id' => [
                    'The selected admission grade id is invalid.'
                ],
                'nationality_id' => [
                    'The selected nationality id is invalid.'
                ],
                'race_id' => [
                    'The selected race id is invalid.'
                ],
                'religion_id' => [
                    'The selected religion id is invalid.'
                ],
                'state_id' => [
                    'The selected state id is invalid.'
                ],
                'country_id' => [
                    'The selected country id is invalid.'
                ],
                'is_hostel' => [
                    'The is hostel field is required.'
                ],
                'nric' => [
                    'The nric field must be 12 digits.'
                ],
            ],
            'data' => null
        ]);
});

test('create with user guardian email exist but phone number not match', function () {
    expect(Student::count())->toBe(0);

    User::factory()->create(['email' => '<EMAIL>']);

    $health_concern = HealthConcern::factory()->create();
    $primary_school = School::factory()->create([
        'level' => SchoolLevel::PRIMARY,
    ]);

    $payload = [
        'name' => [
            'en' => 'Student 1',
            'zh' => '学生 1',
        ],
        'phone_number' => '+601122223333',
        'phone_number_2' => '+60*********',
        'email' => '<EMAIL>',
        'nric' => '990101111111',
        'passport_number' => '001',
        'admission_grade_id' => $this->first_grade->id,
        'admission_year' => '2022',
        'join_date' => '2022-01-01',
        // Auto generated by system
//        'student_number' => '001',
        'birth_cert_number' => '001',
        'gender' => 'MALE',
        'date_of_birth' => '2001-01-01',
        'phone_no' => '*********',
        'birthplace' => $this->malaysia->name,
        'nationality_id' => $this->malaysia->id,
        'race_id' => $this->first_race->id,
        'religion_id' => $this->first_religion->id,
        'address' => '123 Main Street',
        'address_2' => '12345 Main Street 2',
        'postal_code' => '12345',
        'city' => 'Petaling Jaya',
        'state_id' => $this->first_state->id,
        'country_id' => $this->malaysia->id,
        'is_hostel' => true,
        'remarks' => 'This is a student remark for student 1',
        'leave_date' => '2024-01-01',
        'photo' => UploadedFile::fake()->create('first_file.png', 500),
        'custom_field' => json_encode([
            'key' => 'custom_key',
            'value' => 'custom_value'
        ]),
        'guardians' => [
            [
                'type' => GuardianType::FATHER->value,
                'name' => [
                    'en' => 'Father 1',
                ],
                'phone_number' => '+60*********',
                'email' => '<EMAIL>',
                'with_user_account' => true,
                'nric' => '1234',
                'is_primary' => true,
                'live_status' => LiveStatus::NORMAL->value,
                'relation_to_student' => 'Papa',
                'is_direct_dependant' => true,
            ]
        ],
        'dietary_restriction' => DietaryRestriction::NONE->value,
        'health_concern_id' => $health_concern->id,
        'primary_school_id' => $primary_school->id,
        'admission_type' => StudentAdmissionType::NEW->value,
    ];

    $response = $this->postJson(route($this->routeNamePrefix . 'create'), $payload);

    $response->assertStatus(422);

    // Expect nothing is created and return validation error
    $this->assertDatabaseCount($this->table, 0);

    expect(Student::count())->toBe(0)
        ->and($response->json())->toHaveFailedGeneralResponse()
        ->toMatchArray([
            'error' => [
                'guardians.0.email' => [
                    'The selected guardian email is already in use.'
                ]
            ],
            'data' => null
        ]);
});

test('create with user guardian email exist but phone number match', function () {
    expect(Student::count())->toBe(0);

    User::factory()->create(['email' => '<EMAIL>', 'phone_number' => '+60*********']);

    $health_concern = HealthConcern::factory()->create();
    $primary_school = School::factory()->create([
        'level' => SchoolLevel::PRIMARY,
    ]);

    $payload = [
        'name' => [
            'en' => 'Student 1',
            'zh' => '学生 1',
        ],
        'phone_number' => '+601122223333',
        'phone_number_2' => '+60*********',
        'email' => '<EMAIL>',
        'nric' => '990101111111',
        'passport_number' => '001',
        'admission_grade_id' => $this->first_grade->id,
        'admission_year' => '2022',
        'join_date' => '2022-01-01',
        // Auto generated by system
//        'student_number' => '001',
        'birth_cert_number' => '001',
        'gender' => 'MALE',
        'date_of_birth' => '2001-01-01',
        'phone_no' => '*********',
        'birthplace' => $this->malaysia->name,
        'nationality_id' => $this->malaysia->id,
        'race_id' => $this->first_race->id,
        'religion_id' => $this->first_religion->id,
        'address' => '123 Main Street',
        'address_2' => '12345 Main Street 2',
        'postal_code' => '12345',
        'city' => 'Petaling Jaya',
        'state_id' => $this->first_state->id,
        'country_id' => $this->malaysia->id,
        'is_hostel' => true,
        'remarks' => 'This is a student remark for student 1',
        'leave_date' => '2024-01-01',
        'photo' => UploadedFile::fake()->create('first_file.png', 500),
        'custom_field' => json_encode([
            'key' => 'custom_key',
            'value' => 'custom_value'
        ]),
        'guardians' => [
            [
                'type' => GuardianType::FATHER->value,
                'name' => [
                    'en' => 'Father 1',
                ],
                'phone_number' => '+60*********',
                'email' => '<EMAIL>',
                'with_user_account' => true,
                'nric' => '1234',
                'is_primary' => true,
                'live_status' => LiveStatus::NORMAL->value,
                'relation_to_student' => 'Papa',
                'is_direct_dependant' => true
            ]
        ],
        'dietary_restriction' => DietaryRestriction::NONE->value,
        'health_concern_id' => $health_concern->id,
        'primary_school_id' => $primary_school->id,
        'admission_type' => StudentAdmissionType::NEW->value,
    ];

    $response = $this->postJson(route($this->routeNamePrefix . 'create'), $payload);

    expect(Student::count())->toBe(1);
    $response->assertStatus(200);
});

test('create with user guardian nric more than 12 digits', function () {
    expect(Student::count())->toBe(0);

    User::factory()->create(['email' => '<EMAIL>', 'phone_number' => '+60*********']);

    $health_concern = HealthConcern::factory()->create();
    $primary_school = School::factory()->create([
        'level' => SchoolLevel::PRIMARY,
    ]);

    $payload = [
        'name' => [
            'en' => 'Student 1',
            'zh' => '学生 1',
        ],
        'phone_number' => '+601122223333',
        'phone_number_2' => '+60*********',
        'email' => '<EMAIL>',
        'nric' => '990101111111',
        'passport_number' => '001',
        'admission_grade_id' => $this->first_grade->id,
        'admission_year' => '2022',
        'join_date' => '2022-01-01',
        // Auto generated by system
//        'student_number' => '001',
        'birth_cert_number' => '001',
        'gender' => 'MALE',
        'date_of_birth' => '2001-01-01',
        'phone_no' => '*********',
        'birthplace' => $this->malaysia->name,
        'nationality_id' => $this->malaysia->id,
        'race_id' => $this->first_race->id,
        'religion_id' => $this->first_religion->id,
        'address' => '123 Main Street',
        'address_2' => '12345 Main Street 2',
        'postal_code' => '12345',
        'city' => 'Petaling Jaya',
        'state_id' => $this->first_state->id,
        'country_id' => $this->malaysia->id,
        'is_hostel' => true,
        'remarks' => 'This is a student remark for student 1',
        'leave_date' => '2024-01-01',
        'photo' => UploadedFile::fake()->create('first_file.png', 500),
        'custom_field' => json_encode([
            'key' => 'custom_key',
            'value' => 'custom_value'
        ]),
        'guardians' => [
            [
                'type' => GuardianType::FATHER->value,
                'name' => [
                    'en' => 'Father 1',
                ],
                'phone_number' => '+60*********',
                'email' => '<EMAIL>',
                'with_user_account' => true,
                'nric' => '*********012345',
                'is_primary' => true,
                'live_status' => LiveStatus::NORMAL->value,
                'relation_to_student' => 'Papa',
                'is_direct_dependant' => true
            ]
        ],
        'dietary_restriction' => DietaryRestriction::NONE->value,
        'health_concern_id' => $health_concern->id,
        'primary_school_id' => $primary_school->id,
        'admission_type' => StudentAdmissionType::NEW->value,
    ];

    $response = $this->postJson(route($this->routeNamePrefix . 'create'), $payload);

    $response->assertStatus(422);

    // Expect nothing is created and return validation error
    $this->assertDatabaseCount($this->table, 0);

    expect(Student::count())->toBe(0)
        ->and($response->json())->toHaveFailedGeneralResponse()
        ->toMatchArray([
            'error' => [
                'guardians.0.nric' => [
                    'The guardian nric field must not be greater than 12 characters.'
                ]
            ],
            'data' => null
        ]);
});

test('update success', function () {
    $student_user = User::factory()->create();
    $student = Student::factory()->create([
        'user_id' => $student_user->id,
    ]);

    $education = Education::factory()->create();

    $guardian_user = User::factory()->create();
    $guardian = Guardian::factory()->create([
        'user_id' => $guardian_user->id,
        'education_id' => null,
        'married_status' => MarriedStatus::WIDOWED->value,
        'occupation' => 'IT',
        'occupation_description' => 'Fix Computer'
    ]);

    GuardianStudent::factory()->create([
        'guardian_id' => $guardian->id,
        'studenable_id' => $student->id,
        'studenable_type' => Student::class,
    ]);

    expect(Student::count())->toBe(1)
        ->and(User::count())->toBe(3)
        ->and(Guardian::count())->toBe(1)
        ->and(GuardianStudent::count())->toBe(1);

    $other_health_concern = HealthConcern::factory()->create();
    $primary_school = School::factory()->create([
        'level' => SchoolLevel::PRIMARY,
    ]);

    // update success
    $payload = [
        'name' => [
            'en' => 'Student 1',
            'zh' => '学生 1',
        ],
        'phone_number' => '+60123301112',
        'phone_number_2' => '+60123301113',
        'nric' => '990101011111',
        'passport_number' => '001',
        'admission_grade_id' => $this->first_grade->id,
        'admission_year' => '2022',
        'join_date' => '2022-01-01',
        'student_number' => '001',
        'birth_cert_number' => '001',
        'gender' => 'MALE',
        'date_of_birth' => '2001-01-01',
        'birthplace' => $this->malaysia->name,
        'nationality_id' => $this->malaysia->id,
        'race_id' => $this->first_race->id,
        'religion_id' => $this->first_religion->id,
        'address' => '123 Main Street',
        'address_2' => '12345 Main Street 2',
        'leave_date' => '2024-01-01',
        'postal_code' => '12345',
        'city' => 'Petaling Jaya',
        'state_id' => $this->first_state->id,
        'country_id' => $this->malaysia->id,
        'is_hostel' => true,
        'remarks' => 'This is a student remark for student 1',
        'photo' => UploadedFile::fake()->create('first_file.png', 500),
        'custom_field' => json_encode([
            'key' => 'custom_key',
            'value' => 'custom_value'
        ]),
        'guardians' => [
            [
                'type' => GuardianType::FATHER->value,
                'name' => [
                    'en' => 'Father 1',
                ],
                'phone_number' => '+***********',
                'email' => '<EMAIL>',
                'with_user_account' => true,
                'nric' => '1234',
                'is_primary' => true,
                'live_status' => LiveStatus::NORMAL->value,
                'relation_to_student' => 'Papa',
                'is_direct_dependant' => false,
            ],
            [
                'type' => GuardianType::MOTHER->value,
                'name' => [
                    'en' => 'Mother 1',
                ],
                'phone_number' => '+***********',
                'email' => '<EMAIL>',
                'with_user_account' => false,
                'nric' => null,
                'is_primary' => false,
                'live_status' => LiveStatus::SICK->value,
                'relation_to_student' => 'Mama',
                'is_direct_dependant' => false,
            ],
            [
                'id' => $guardian->id,
                'type' => GuardianType::GUARDIAN->value,
                'name' => [
                    'en' => 'Guardian 1',
                ],
                'phone_number' => '+***********',
                'email' => '<EMAIL>',
                'with_user_account' => false,
                'nric' => null,
                'education_id' => $education->id,
                'married_status' => MarriedStatus::SINGLE->value,
                'occupation' => 'Test',
                'occupation_description' => 'Test',
                'is_primary' => false,
                'live_status' => LiveStatus::NORMAL->value,
                'relation_to_student' => 'Grandpa',
                'is_direct_dependant' => true,
            ],
        ],
        'dietary_restriction' => DietaryRestriction::VEGETARIAN->value,
        'health_concern_id' => $other_health_concern->id,
        'primary_school_id' => $primary_school->id,
        'admission_type' => StudentAdmissionType::TRANSFERRED->value,
    ];

    $response = $this->putJson(route($this->routeNamePrefix . 'update', $student->id), $payload);

    $response->assertStatus(200);

    $student->refresh();

    expect(Student::count())->toBe(1)
        ->and(User::count())->toBe(4)
        ->and(Guardian::count())->toBe(3)
        ->and(GuardianStudent::count())->toBe(3)
        ->and($response->json())->toMatchArray([
            'status' => 'OK',
            'code' => 200,
            'message' => 'Success.',
        ])
        ->and($response->json()['data'])
        ->toMatchArray([
            'id' => $student->id,
//            'user' => resourceToArray(new UserResource($student_user)),
            'name' => $payload['name']['en'],
            'phone_number' => $payload['phone_number'],
            'phone_number_2' => $payload['phone_number_2'],
            'nric' => $payload['nric'],
            'admission_year' => $student->admission_year,
//            'admission_grade' => resourceToArray(new GradeResource($student->admissionGrade)),
            'join_date' => $student->join_date,
            'leave_date' => $student->leave_date,
            'student_number' => $student->student_number,
            'birth_cert_number' => $student->birth_cert_number,
            'passport_number' => $student->passport_number,
            'gender' => $student->gender->value,
            'date_of_birth' => $student->date_of_birth,
            'birthplace' => $student->birthplace,
//            'nationality' => resourceToArray(new CountryResource($student->nationality)),
//            'race' => resourceToArray(new RaceResource($student->race)),
//            'religion' => resourceToArray(new RaceResource($student->religion)),
            'address' => $student->address,
            'address_2' => $student->address_2,
            'postal_code' => $student->postal_code,
            'city' => $student->city,
//            'state' => resourceToArray(new StateResource($student->state)),
//            'country' => resourceToArray(new CountryResource($student->country)),
            'is_hostel' => $student->is_hostel,
            'remarks' => $student->remarks,
            'custom_field' => $student->custom_field,
            'translations' => $student->translations,
            'photo' => $student->photo,
//            'guardians' => resourceToArray(GuardianResource::collection($student->guardians)),
            'dietary_restriction' => $payload['dietary_restriction'],
//            'health_concern' => resourceToArray(new HealthConcernResource($other_health_concern)),
//            'primary_school' => resourceToArray(new SchoolResource($primary_school)),
        ])
        ->and($student->photo)->not->toBeNull();

    //check db record
    $this->assertDatabaseHas($this->mediaTable, [
        'model_type' => Student::class,
        'model_id' => $student->id,
        'file_name' => 'first_file.png',
        'collection_name' => 'photo'
    ]);

    $this->assertDatabaseHas($this->table, [
        'id' => $student->id,
        'dietary_restriction' => DietaryRestriction::VEGETARIAN->value,
        'health_concern_id' => $other_health_concern->id,
        'primary_school_id' => $primary_school->id,
    ]);

    $this->assertDatabaseHas('users', [
        'email' => $student->user->email,
        'phone_number' => $student->user->phone_number,
    ]);

    //guardian user
    $this->assertDatabaseHas($this->userTable, [
        'phone_number' => $payload['guardians'][0]['phone_number'],
        'email' => $payload['guardians'][0]['email'],
    ]);

    foreach ($payload['guardians'] as $guardian_data) {
        $data = [
            'name->en' => $guardian_data['name']['en'],
            'phone_number' => $guardian_data['phone_number'],
            'email' => $guardian_data['email'],
            'nric' => $guardian_data['nric'],
            'education_id' => Arr::get($guardian_data, 'education_id'),
            'married_status' => Arr::get($guardian_data, 'married_status'),
            'occupation' => Arr::get($guardian_data, 'occupation'),
            'occupation_description' => Arr::get($guardian_data, 'occupation_description'),
            'live_status' => $guardian_data['live_status'],
        ];

        if (isset($guardian_data['id'])) {
            $data['id'] = $guardian_data['id'];
        }

        $this->assertDatabaseHas($this->guardianTable, $data);

        $data = [
            'type' => $guardian_data['type'],
            'studenable_type' => Student::class,
            'studenable_id' => $response->json()['data']['id'],
            'is_primary' => $guardian_data['is_primary'],
            'relation_to_student' => $guardian_data['relation_to_student'],
            'is_direct_dependant' => $guardian_data['is_direct_dependant'],
        ];

        if (isset($guardian_data['id'])) {
            $data['guardian_id'] = $guardian_data['id'];
        }

        $this->assertDatabaseHas($this->guardianStudentTable, $data);
    }
});

test('update failed - guardian nric more than 12 digits', function () {
    $student_user = User::factory()->create();
    $student = Student::factory()->create([
        'user_id' => $student_user->id,
    ]);

    $education = Education::factory()->create();

    $guardian_user = User::factory()->create();
    $guardian = Guardian::factory()->create([
        'user_id' => $guardian_user->id,
        'education_id' => null,
        'married_status' => MarriedStatus::WIDOWED->value,
        'occupation' => 'IT',
        'occupation_description' => 'Fix Computer'
    ]);

    GuardianStudent::factory()->create([
        'guardian_id' => $guardian->id,
        'studenable_id' => $student->id,
        'studenable_type' => Student::class,
    ]);

    expect(Student::count())->toBe(1)
        ->and(User::count())->toBe(3)
        ->and(Guardian::count())->toBe(1)
        ->and(GuardianStudent::count())->toBe(1);

    $other_health_concern = HealthConcern::factory()->create();
    $primary_school = School::factory()->create([
        'level' => SchoolLevel::PRIMARY,
    ]);

    // update success
    $payload = [
        'name' => [
            'en' => 'Student 1',
            'zh' => '学生 1',
        ],
        'phone_number' => '+60123301112',
        'phone_number_2' => '+60123301113',
        'nric' => '990101011111',
        'passport_number' => '001',
        'admission_grade_id' => $this->first_grade->id,
        'admission_year' => '2022',
        'join_date' => '2022-01-01',
        'student_number' => '001',
        'birth_cert_number' => '001',
        'gender' => 'MALE',
        'date_of_birth' => '2001-01-01',
        'birthplace' => $this->malaysia->name,
        'nationality_id' => $this->malaysia->id,
        'race_id' => $this->first_race->id,
        'religion_id' => $this->first_religion->id,
        'address' => '123 Main Street',
        'address_2' => '12345 Main Street 2',
        'leave_date' => '2024-01-01',
        'postal_code' => '12345',
        'city' => 'Petaling Jaya',
        'state_id' => $this->first_state->id,
        'country_id' => $this->malaysia->id,
        'is_hostel' => true,
        'remarks' => 'This is a student remark for student 1',
        'photo' => UploadedFile::fake()->create('first_file.png', 500),
        'custom_field' => json_encode([
            'key' => 'custom_key',
            'value' => 'custom_value'
        ]),
        'guardians' => [
            [
                'type' => GuardianType::FATHER->value,
                'name' => [
                    'en' => 'Father 1',
                ],
                'phone_number' => '+***********',
                'email' => '<EMAIL>',
                'with_user_account' => true,
                'nric' => '*********012345',
                'is_primary' => true,
                'live_status' => LiveStatus::NORMAL->value,
                'relation_to_student' => 'Papa',
                'is_direct_dependant' => true,
            ],
            [
                'type' => GuardianType::MOTHER->value,
                'name' => [
                    'en' => 'Mother 1',
                ],
                'phone_number' => '+***********',
                'email' => '<EMAIL>',
                'with_user_account' => false,
                'nric' => null,
                'is_primary' => false,
                'live_status' => LiveStatus::SICK->value,
                'relation_to_student' => 'Mama',
                'is_direct_dependant' => true,
            ],
            [
                'id' => $guardian->id,
                'type' => GuardianType::GUARDIAN->value,
                'name' => [
                    'en' => 'Guardian 1',
                ],
                'phone_number' => '+***********',
                'email' => '<EMAIL>',
                'with_user_account' => false,
                'nric' => null,
                'education_id' => $education->id,
                'married_status' => MarriedStatus::SINGLE->value,
                'occupation' => 'Test',
                'occupation_description' => 'Test',
                'is_primary' => false,
                'live_status' => LiveStatus::NORMAL->value,
                'relation_to_student' => 'Grandpa',
                'is_direct_dependant' => true,
            ],
        ],
        'dietary_restriction' => DietaryRestriction::VEGETARIAN->value,
        'health_concern_id' => $other_health_concern->id,
        'primary_school_id' => $primary_school->id,
        'admission_type' => StudentAdmissionType::TRANSFERRED->value,
    ];

    $response = $this->putJson(route($this->routeNamePrefix . 'update', $student->id), $payload);

    $response->assertStatus(422);

    // Expect nothing is created and return validation error
    expect(Student::count())->toBe(1)
        ->and($response->json()['error'])->toBe([
            'guardians.0.nric' => [
                'The guardian nric field must not be greater than 12 characters.'
            ],
        ]);
});

test('update failed - is hostel unassign and student got bed', function () {
    $student_user = User::factory()->create();
    $student = Student::factory()->create([
        'user_id' => $student_user->id,
        'is_hostel' => 1
    ]);

    HostelBedAssignment::factory()->create([
        'assignable_type' => Student::class,
        'assignable_id' => $student->id,
    ]);

    $education = Education::factory()->create();

    $guardian_user = User::factory()->create();
    $guardian = Guardian::factory()->create([
        'user_id' => $guardian_user->id,
        'education_id' => null,
        'married_status' => MarriedStatus::WIDOWED->value,
        'occupation' => 'IT',
        'occupation_description' => 'Fix Computer'
    ]);

    GuardianStudent::factory()->create([
        'guardian_id' => $guardian->id,
        'studenable_id' => $student->id,
        'studenable_type' => Student::class,
    ]);

    $other_health_concern = HealthConcern::factory()->create();
    $primary_school = School::factory()->create([
        'level' => SchoolLevel::PRIMARY,
    ]);

    $payload = [
        'name' => [
            'en' => 'Student 1',
            'zh' => '学生 1',
        ],
        'phone_number' => '+60123301112',
        'phone_number_2' => '+60123301113',
        'nric' => '990101011111',
        'passport_number' => '001',
        'admission_grade_id' => $this->first_grade->id,
        'admission_year' => '2022',
        'join_date' => '2022-01-01',
        'student_number' => '001',
        'birth_cert_number' => '001',
        'gender' => 'MALE',
        'date_of_birth' => '2001-01-01',
        'birthplace' => $this->malaysia->name,
        'nationality_id' => $this->malaysia->id,
        'race_id' => $this->first_race->id,
        'religion_id' => $this->first_religion->id,
        'address' => '123 Main Street',
        'address_2' => '12345 Main Street 2',
        'leave_date' => '2024-01-01',
        'postal_code' => '12345',
        'city' => 'Petaling Jaya',
        'state_id' => $this->first_state->id,
        'country_id' => $this->malaysia->id,
        'is_hostel' => false,
        'remarks' => 'This is a student remark for student 1',
        'photo' => UploadedFile::fake()->create('first_file.png', 500),
        'custom_field' => json_encode([
            'key' => 'custom_key',
            'value' => 'custom_value'
        ]),
        'guardians' => [
            [
                'type' => GuardianType::FATHER->value,
                'name' => [
                    'en' => 'Father 1',
                ],
                'phone_number' => '+***********',
                'email' => '<EMAIL>',
                'with_user_account' => true,
                'nric' => '1234',
                'is_primary' => true,
                'live_status' => LiveStatus::NORMAL->value,
                'relation_to_student' => 'Papa',
                'is_direct_dependant' => true,
            ],
            [
                'type' => GuardianType::MOTHER->value,
                'name' => [
                    'en' => 'Mother 1',
                ],
                'phone_number' => '+***********',
                'email' => '<EMAIL>',
                'with_user_account' => false,
                'nric' => null,
                'is_primary' => false,
                'live_status' => LiveStatus::SICK->value,
                'relation_to_student' => 'Mama',
                'is_direct_dependant' => true,
            ],
            [
                'id' => $guardian->id,
                'type' => GuardianType::GUARDIAN->value,
                'name' => [
                    'en' => 'Guardian 1',
                ],
                'phone_number' => '+***********',
                'email' => '<EMAIL>',
                'with_user_account' => false,
                'nric' => null,
                'education_id' => $education->id,
                'married_status' => MarriedStatus::SINGLE->value,
                'occupation' => 'Test',
                'occupation_description' => 'Test',
                'is_primary' => false,
                'live_status' => LiveStatus::NORMAL->value,
                'relation_to_student' => 'Grandpa',
                'is_direct_dependant' => true,
            ],
        ],
        'dietary_restriction' => DietaryRestriction::VEGETARIAN->value,
        'health_concern_id' => $other_health_concern->id,
        'primary_school_id' => $primary_school->id,
        'admission_type' => StudentAdmissionType::NEW->value,
    ];

    $response = $this->putJson(route($this->routeNamePrefix . 'update', $student->id), $payload);

    $response->assertStatus(422);

    // Expect nothing is created and return validation error
    expect(Student::count())->toBe(1)
        ->and($response->json()['error'])->toBe([
            'is_hostel' => [
                'Unable to change is hostel status to false because student has existing bed assignment.'
            ],
        ]);
});

test('update validation error', function () {
    $student = Student::factory()->create(['nric' => '*********012', 'passport_number' => '**********']);
    $secondary_school = School::factory()->create([
        'level' => SchoolLevel::SECONDARY,
    ]);

    $input = [
        'primary_school_id' => $secondary_school->id,
    ];

    expect(Student::count())->toBe(1);

    $response = $this->putJson(route($this->routeNamePrefix . 'update', $student->id), $input);

    $response->assertStatus(422);

    // Expect nothing is created and return validation error
    expect(Student::count())->toBe(1)
        ->and($response->json()['error'])->toBe([
            'name' => [
                'The name field is required.'
            ],
            'admission_year' => [
                'The admission year field is required.'
            ],
            'admission_grade_id' => [
                'The admission grade id field is required.'
            ],
            'join_date' => [
                'The join date field is required.'
            ],
            'nationality_id' => [
                'The nationality id field is required.'
            ],
            'date_of_birth' => [
                'The date of birth field is required.'
            ],
            'gender' => [
                'The gender field is required.'
            ],
            'birth_cert_number' => [
                'The birth cert number field is required.'
            ],
            'nric' => [
                'NRIC is needed if the Passport Number field is blank.',
            ],
            'passport_number' => [
                'Passport Number is needed if the NRIC field is blank.',
            ],
            'race_id' => [
                'The race id field is required.'
            ],
            'religion_id' => [
                'The religion id field is required.'
            ],
            'address' => [
                'The address field is required.'
            ],
            'postal_code' => [
                'The postal code field is required.'
            ],
            'city' => [
                'The city field is required.'
            ],
            'state_id' => [
                'The state id field is required.'
            ],
            'country_id' => [
                'The country id field is required.'
            ],
            'is_hostel' => [
                'The is hostel field is required.'
            ],
            'dietary_restriction' => [
                'The dietary restriction field is required.'
            ],
            'health_concern_id' => [
                'The health concern id field is required.'
            ],
            'primary_school_id' => [
                'The selected primary school id is invalid.'
            ],
            'admission_type' => [
                'The admission type field is required.'
            ],
        ]);

    // without nric and passport number
    $response = $this->putJson(route($this->routeNamePrefix . 'update', $student->id), []);
    expect(Student::count())->toBe(1)
        ->and($response->json())->toHaveFailedGeneralResponse()
        ->and($response->json()['error'])->toMatchArray([
            'nric' => [
                'NRIC is needed if the Passport Number field is blank.'
            ],
            'passport_number' => [
                'Passport Number is needed if the NRIC field is blank.'
            ],
        ]);

    // only nric
    $response = $this->putJson(route($this->routeNamePrefix . 'update', $student->id), ['nric' => '12345']);
    // nric field and passport number field shouldn't throw error
    expect(Student::count())->toBe(1)
        ->and($response->json())->toHaveFailedGeneralResponse()
        ->and($response->json()['error'])->not->toMatchArray([
            'nric' => [
                'NRIC is needed if the Passport Number field is blank.'
            ],
            'passport_number' => [
                'Passport Number is needed if the NRIC field is blank.'
            ],
        ]);

    // only passport number
    $response = $this->putJson(route($this->routeNamePrefix . 'update', $student->id), ['passport_number' => '12345']);
    // nric field and passport number field shouldn't throw error
    expect(Student::count())->toBe(1)
        ->and($response->json())->toHaveFailedGeneralResponse()
        ->and($response->json()['error'])->not->toMatchArray([
            'nric' => [
                'NRIC is needed if the Passport Number field is blank.'
            ],
            'passport_number' => [
                'Passport Number is needed if the NRIC field is blank.'
            ],
        ]);

    $student2 = Student::factory()->create();
    $response = $this->putJson(route($this->routeNamePrefix . 'update', $student2->id), ['nric' => '*********012', 'passport_number' => '**********']);
    // nric and passport number must be unique
    expect(Student::count())->toBe(2) // used factory to create 2 students
    ->and($response->json())->toHaveFailedGeneralResponse()
        ->and($response->json()['error'])->toMatchArray([
            'nric' => [
                'The nric has already been taken.'
            ],
            'passport_number' => [
                'The passport number has already been taken.'
            ],
        ]);

    $this->assertDatabaseHas($this->table, [
        'id' => $student->id,
        'admission_grade_id' => $student->admission_grade_id,
        'admission_year' => $student->admission_year,
        'join_date' => $student->join_date,
        'student_number' => $student->student_number,
        'birth_cert_number' => $student->birth_cert_number,
        'nric' => $student->nric,
        'passport_number' => $student->passport_number,
        'gender' => $student->gender,
        'date_of_birth' => $student->date_of_birth,
        'birthplace' => $student->birthplace,
        'nationality_id' => $student->nationality_id,
        'race_id' => $student->race_id,
        'religion_id' => $student->religion_id,
        'address' => $student->address,
        'postal_code' => $student->postal_code,
        'city' => $student->city,
        'state_id' => $student->state_id,
        'country_id' => $student->country_id,
        'remarks' => $student->remarks,
    ]);
});

test('update hostel student', function () {
    $nationality = Country::factory()->create();
    $religion = Religion::factory()->create();
    $race = Race::factory()->create();

    $student = Student::factory()->create([
        'is_hostel' => true
    ]);

    $guardian_student1 = GuardianStudent::factory()->create([
        'studenable_id' => $student->id,
        'studenable_type' => Student::class,
        'type' => GuardianType::FATHER->value,
        'is_direct_dependant' => true,
    ]);

    $guardian_student2 = GuardianStudent::factory()->create([
        'studenable_id' => $student->id,
        'studenable_type' => Student::class,
        'type' => GuardianType::MOTHER->value,
        'is_direct_dependant' => true,
    ]);

    $guardian_student3 = GuardianStudent::factory()->create([
        'studenable_id' => $student->id,
        'studenable_type' => Student::class,
        'type' => GuardianType::GUARDIAN->value,
        'is_direct_dependant' => false,
    ]);

    $payload = [
        "guardians" => [
            [
                "id" => null,
                "type" => "GUARDIAN",
                "name" => [
                    "en" => "Guardian 1",
                    "zh" => "Guardian 1 zh"
                ],
                "phone_number" => "+***********",
                "nric" => "*********4",
                "passport_number" => "*********",
                "email" => "<EMAIL>",
                "nationality_id" => $nationality->id,
                "religion_id" => $religion->id,
                "race_id" => $race->id,
                "with_user_account" => false,
                "married_status" => "SINGLE",
                "occupation" => "DOCTOR",
                "occupation_description" => "Save People",
                "relation_to_student" => "Grandpa", // nullable, free text
                "live_status" => "NORMAL", // nullable, accepts NORMAL, SICK, PASSED_AWAY, UNKNOWN
            ]
        ]
    ];

    $this->assertDatabaseCount('guardian_student', 3);
    $this->assertDatabaseHas('guardian_student', [
        'studenable_type' => Student::class,
        'studenable_id' => $student->id,
        'guardian_id' => $guardian_student3->guardian_id,
        'type' => GuardianType::GUARDIAN->value,
    ]);

    $response = $this->putJson(route('admin.hostels.student-update', $student->id), $payload);

    $this->assertDatabaseCount('guardian_student', 3);

    $this->assertDatabaseMissing('guardian_student', [
        'studenable_type' => Student::class,
        'studenable_id' => $student->id,
        'guardian_id' => $guardian_student3->guardian_id,
        'type' => GuardianType::GUARDIAN->value,
    ]);

    $this->assertDatabaseHas('guardian_student', [
        'studenable_type' => Student::class,
        'studenable_id' => $student->id,
        'type' => GuardianType::GUARDIAN->value,
        'is_direct_dependant' => false,
        'is_primary' => false,
    ]);

    // Scenario 2: Updating is_direct_dependant = true guardian -> This should throw error
    $payload = [
        "guardians" => [
            [
                "id" => $guardian_student1->guardian_id,
                "type" => "FATHER",
                "name" => [
                    "en" => "Father 1",
                    "zh" => "Father 1 zh"
                ],
                "phone_number" => "+***********",
                "nric" => "*********9",
                "passport_number" => "*********",
                "email" => "<EMAIL>",
                "nationality_id" => $nationality->id,
                "religion_id" => $religion->id,
                "race_id" => $race->id,
                "with_user_account" => false,
                "married_status" => "SINGLE",
                "occupation" => "DOCTOR",
                "occupation_description" => "Save People",
                "relation_to_student" => "Grandpa", // nullable, free text
                "live_status" => "NORMAL", // nullable, accepts NORMAL, SICK, PASSED_AWAY, UNKNOWN
            ]
        ]
    ];

    $response = $this->putJson(route('admin.hostels.student-update', $student->id), $payload);
    expect($response->json())->toHaveFailedGeneralResponse()
        ->toMatchArray([
            'code' => '7005',
            'error' => "Guardian Father 1 - Father 1 zh already existed."
        ]);
});

test('show single record success', function () {
    $first_student_user = User::factory()->create();
    $second_student_user = User::factory()->create();

    $first_student = Student::factory()->create([
        'user_id' => $first_student_user->id,
        'is_active' => true,
    ]);

    // First Student Have Guardian
    $guardian = Guardian::factory()->create();
    GuardianStudent::factory()->create([
        'studenable_id' => $first_student->id,
        'studenable_type' => Student::class,
        'guardian_id' => $guardian->id,
    ]);

    Carbon::setTestNow(Carbon::parse('2024-03-01'));

    $second_semester_setting = SemesterSetting::factory()->create([
        'from' => '2024-01-01',
        'to' => '2024-12-31',
        'is_current_semester' => false
    ]);

    $semester_setting = SemesterSetting::factory()->create([
        'from' => '2024-01-01',
        'to' => '2024-12-31',
        'is_current_semester' => true
    ]);

    $first_student_class = StudentClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'student_id' => $first_student->id,
        'class_type' => ClassType::PRIMARY
    ]);

    StudentClass::factory()->create([
        'semester_setting_id' => $second_semester_setting->id,
        'student_id' => $first_student->id,
        'class_type' => ClassType::PRIMARY
    ]);

    $second_student = Student::factory()->create([
        'user_id' => $second_student_user->id,
        'is_active' => false,
    ]);

    $first_student_class->load([
        'semesterSetting',
        'semesterClass.classModel'
    ]);

    StudentReportCard::factory(4)->state(new Sequence(
        [
            'student_id' => $first_student->id,
            'is_active' => true
        ],
        [
            'student_id' => $first_student->id,
            'is_active' => true
        ],
        [
            'student_id' => $first_student->id,
            'is_active' => false
        ],
        [
            'student_id' => $second_student->id,
            'is_active' => true
        ]
    ))->create();

    $response = $this->getJson(route($this->routeNamePrefix . 'show', [
        'student' => $first_student->id,
        'includes' => [
            "user",
            "admissionGrade",
            "reportCards",
            "nationality",
            "race",
            "religion",
            "state",
            "country",
            "guardians",
            "leadershipPositionRecord.leadershipPosition",
            "currentSemesterPrimaryClass.semesterSetting",
            "currentSemesterPrimaryClass.semesterClass.classModel",
            "healthConcern",
            "primarySchool"
        ]
    ]));

    $response->assertStatus(200);

    expect($response->json())->toHaveSuccessGeneralResponse()
        ->and($response->json()['data'])
        ->toEqual([
            'id' => $first_student->id,
            'name' => $first_student->name,
            'phone_number' => $first_student->phone_number,
            'phone_number_2' => $first_student->phone_number_2,
            'email' => $first_student->email,
            'user' => resourceToArray(new UserResource($first_student_user)),
            'admission_year' => $first_student->admission_year,
            'admission_grade' => resourceToArray(new GradeResource($first_student->admissionGrade)),
            'report_cards' => resourceToArray(StudentReportCardResource::collection($first_student->reportCards)),
            'join_date' => $first_student->join_date,
            'leave_date' => $first_student->leave_date,
            'leave_status' => $first_student->leave_status,
            'student_number' => $first_student->student_number,
            'birth_cert_number' => $first_student->birth_cert_number,
            'nric' => $first_student->nric,
            'passport_number' => $first_student->passport_number,
            'gender' => $first_student->gender->value,
            'date_of_birth' => $first_student->date_of_birth,
            'birthplace' => $first_student->birthplace,
            'nationality' => resourceToArray(new CountryResource($first_student->nationality)),
            'race' => resourceToArray(new RaceResource($first_student->race)),
            'religion' => resourceToArray(new RaceResource($first_student->religion)),
            'address' => $first_student->address,
            'address_2' => $first_student->address_2,
            'postal_code' => $first_student->postal_code,
            'city' => $first_student->city,
            'state' => resourceToArray(new StateResource($first_student->state)),
            'country' => resourceToArray(new CountryResource($first_student->country)),
            'remarks' => $first_student->remarks,
            'is_hostel' => $first_student->is_hostel,
            'custom_field' => $first_student->custom_field,
            'translations' => $first_student->translations,
            'photo' => $first_student->photo,
            'guardians' => resourceToArray(GuardianResource::collection($first_student->guardians)),
            'leadership_position' => null,
            'current_primary_class' => resourceToArray(new StudentClassResource($first_student_class)),
            'dietary_restriction' => $first_student->dietary_restriction,
            'health_concern' => resourceToArray(new HealthConcernResource($first_student->healthConcern)),
            'primary_school' => null,
            'is_active' => $first_student->is_active,
            'admission_type' => $first_student->admission_type->value,
        ])->and($response->json()['data']['guardians'])->toHaveCount(1);

    $response = $this->getJson(route($this->routeNamePrefix . 'show', [
        'student' => $second_student->id,
        'includes' => [
            "user",
            "admissionGrade",
            "nationality",
            "reportCards",
            "race",
            "religion",
            "state",
            "country",
            "guardians",
            "leadershipPositionRecord.leadershipPosition",
            "currentSemesterPrimaryClass.semesterSetting",
            "currentSemesterPrimaryClass.semesterClass.classModel",
            "healthConcern",
            "primarySchool"
        ]
    ]));

    $response->assertStatus(200);

    expect($response->json())->toHaveSuccessGeneralResponse()
        ->and($response->json()['data'])
        ->toEqual([
            'id' => $second_student->id,
            'user' => resourceToArray(new UserResource($second_student_user)),
            'name' => $second_student->name,
            'phone_number' => $second_student->phone_number,
            'phone_number_2' => $second_student->phone_number_2,
            'email' => $second_student->email,
            'admission_year' => $second_student->admission_year,
            'admission_grade' => resourceToArray(new GradeResource($second_student->admissionGrade)),
            'report_cards' => resourceToArray(StudentReportCardResource::collection($second_student->reportCards)),
            'join_date' => $second_student->join_date,
            'leave_date' => $second_student->leave_date,
            'leave_status' => $second_student->leave_status,
            'student_number' => $second_student->student_number,
            'birth_cert_number' => $second_student->birth_cert_number,
            'nric' => $second_student->nric,
            'passport_number' => $second_student->passport_number,
            'gender' => $second_student->gender->value,
            'date_of_birth' => $second_student->date_of_birth,
            'birthplace' => $second_student->birthplace,
            'nationality' => resourceToArray(new CountryResource($second_student->nationality)),
            'race' => resourceToArray(new RaceResource($second_student->race)),
            'religion' => resourceToArray(new RaceResource($second_student->religion)),
            'address' => $second_student->address,
            'address_2' => $second_student->address_2,
            'postal_code' => $second_student->postal_code,
            'city' => $second_student->city,
            'state' => resourceToArray(new StateResource($second_student->state)),
            'country' => resourceToArray(new CountryResource($second_student->country)),
            'remarks' => $second_student->remarks,
            'is_hostel' => $second_student->is_hostel,
            'custom_field' => $second_student->custom_field,
            'translations' => $second_student->translations,
            'photo' => $first_student->photo,
            'guardians' => [],
            'leadership_position' => null,
            'current_primary_class' => null,
            'dietary_restriction' => $second_student->dietary_restriction,
            'health_concern' => resourceToArray(new HealthConcernResource($second_student->healthConcern)),
            'primary_school' => null,
            'is_active' => $second_student->is_active,
            'admission_type' => $first_student->admission_type->value,
        ])->and($response->json()['data']['guardians'])->toHaveCount(0);
});

test('show not existing record error', function () {
    expect(Student::count())->toBe(0);

    $response = $this->getJson(route($this->routeNamePrefix . 'show', 1));

    $response->assertStatus(404);

    expect($response->json())->toHaveModelResourceNotFoundResponse();
});

test('delete success', function () {
    $first_student = Student::factory()->create();
    $other_students = Student::factory()->count(3)->create();

    $this->assertDatabaseCount($this->table, 4);

    //id not exist
    $response = $this->deleteJson(route($this->routeNamePrefix . 'destroy', ['student' => 9999]))->json();
    expect($response)->toHaveModelResourceNotFoundResponse();

    $this->assertDatabaseCount($this->table, 4);

    //delete success
    $response = $this->deleteJson(route($this->routeNamePrefix . 'destroy', ['student' => $first_student->id]))->json();
    expect($response)->toHaveSuccessGeneralResponse();

    $this->assertDatabaseCount($this->table, 3);
    $this->assertDatabaseMissing($this->table, ['id' => $first_student->id]);

    foreach ($other_students as $other_student) {
        $this->assertDatabaseHas($this->table, ['id' => $other_student->id]);
    }

    $linked_student = Student::factory()->create();
    GuardianStudent::factory()->create([
        'studenable_id' => $linked_student->id,
        'studenable_type' => Student::class,
    ]);

    $response = $this->deleteJson(route($this->routeNamePrefix . 'destroy', ['student' => $linked_student->id]))->json();

    expect($response)->toHaveFailedGeneralResponse()
        ->and($response['error'])
        ->toBe("Guardian cannot be deleted once is linked.");
});

test('regenerate student number success', function () {
    $student_hostel = Student::factory()->create([
        'admission_year' => 2024,
        'is_hostel' => true,
    ]);

    $student_non_hostel = Student::factory()->create([
        'admission_year' => 2024,
        'is_hostel' => false,
    ]);

    $student_hostel_2025 = Student::factory()->create([
        'admission_year' => 2025,
        'is_hostel' => true,
    ]);

    // Regenerate for 2024 Hostel Student
    $response = $this->postJson(route($this->routeNamePrefix . 'regenerate-student-number', ['student' => $student_hostel->id]));

    $response->assertStatus(200);

    expect($response->json()['data'])->toHaveKey('student_number', 'H2024001');

    // Regenerate for 2024 Non Hostel Student
    $response = $this->postJson(route($this->routeNamePrefix . 'regenerate-student-number', ['student' => $student_non_hostel->id]));

    $response->assertStatus(200);

    expect($response->json()['data'])->toHaveKey('student_number', '2024002');

    // Regenerate for 2025 Hostel Student
    $response = $this->postJson(route($this->routeNamePrefix . 'regenerate-student-number', ['student' => $student_hostel_2025->id]));

    $response->assertStatus(200);

    expect($response->json()['data'])->toHaveKey('student_number', 'H2025001');
});

test('get by student number or card number success', function () {
    /**
     * Student AA has active card
     * Student BB has inactive card
     */
    $studentAA = Student::factory()->create([
        'student_number' => 'AA-11'
    ]);

    Card::factory()->create([
        'card_number' => 'AA1',
        'card_number2' => 'AA2',
        'card_number3' => 'AA3',
        'userable_type' => Student::class,
        'userable_id' => $studentAA->id,
        'status' => CardStatus::ACTIVE->value,
    ]);

    $studentBB = Student::factory()->create([
        'student_number' => 'BB-22'
    ]);

    Card::factory()->create([ // inactive
        'card_number' => 'BB1',
        'card_number2' => 'BB2',
        'card_number3' => 'BB3',
        'userable_type' => Student::class,
        'userable_id' => $studentBB->id,
        'status' => CardStatus::INACTIVE->value,
    ]);

    // ================================================ filter by student_number
    $response = $this->getJson(route($this->routeNamePrefix . 'get-by-student-number-or-card-number', ['number' => 'AA-11']));

    $response->assertStatus(200);

    expect($response->json()['data'])->toHaveKey('id', $studentAA->id);

    // ================================================ filter by card_number
    $response = $this->getJson(route($this->routeNamePrefix . 'get-by-student-number-or-card-number', ['number' => 'AA1']));

    $response->assertStatus(200);

    expect($response->json()['data'])->toHaveKey('id', $studentAA->id);

    // ==========================================================================================
    // ==========================================================================================
    // ==========================================================================================

    // ================================================ filter by student_number - even tho card inactive, using student number is still legit
    $response = $this->getJson(route($this->routeNamePrefix . 'get-by-student-number-or-card-number', ['number' => 'BB-22']));

    $response->assertStatus(200);

    expect($response->json()['data'])->toHaveKey('id', $studentBB->id);

    // ================================================ filter by card_number
    $response = $this->getJson(route($this->routeNamePrefix . 'get-by-student-number-or-card-number', ['number' => 'BB1']));

    $response->assertStatus(404);

    expect($response->json())->toHaveModelResourceNotFoundResponse();
});

test('leave success', function () {

    $student = Student::factory()->create([
        'student_number' => 'AA-11',
        'join_date' => '2024-11-01',
        'leave_date' => null,
        'is_active' => true,
        'user_id' => $this->user->id,
        'leave_status' => null,
    ]);

    $leave_reason = LeaveReason::factory()->create();

    $leave_date = now()->addDay()->toDateString();

    $user = $student->user;
    expect($user->hasRole(Role::STUDENT))->toBeTrue();
    expect($user->hasRole(Role::STUDENT_DEACTIVATED))->toBeFalse();

    $this->mock(StudentService::class, function (MockInterface $mock) use ($leave_date, &$student) {
        $mock->shouldReceive('setStudent')->once()->withArgs(function ($value) use (&$student) {
            return $value->id === $student->id;
        })->andReturnSelf();
        $mock->shouldReceive('setRemarks')->once()->with('test')->andReturnSelf();
        $mock->shouldReceive('setApiRequest')->once()->andReturnSelf();
        $mock->shouldReceive('setLeaveReason')->once()->andReturnSelf();
        $mock->shouldReceive('markStudentLeftAt')->with($leave_date)->once();
    });

    $response = $this->postJson(route($this->routeNamePrefix . 'leave', [
        'student' => $student->id,
        'effective_date' => $leave_date,
        'remarks' => 'test',
        'leave_reason_id' => $leave_reason->id,
    ]));

    $response->assertStatus(200);

});

test('leave error', function () {
    $student = Student::factory()->create([
        'student_number' => 'AA-11',
        'join_date' => '2024-11-01',
        'leave_date' => null,
        'is_active' => true,
        'user_id' => $this->user->id,
        'leave_status' => null,
    ]);

    $response = $this->postJson(route($this->routeNamePrefix . 'leave', [
        'student' => $student->id,
    ]));

    $response->assertStatus(422);

    expect($response->json())->toHaveFailedGeneralResponse()
        ->toMatchArray([
            'error' => [
                'effective_date' => [
                    'The effective date field is required.'
                ],
                'leave_reason_id' => [
                    'The leave reason id field is required.'
                ],
            ],
            'data' => null
        ]);

});

test('return success', function () {

    $semester_setting_year_2024 = SemesterYearSetting::create(['year' => 2024]);

    $semester_setting = SemesterSetting::factory()->create([
        'name' => '2024 Semester 1',
        'semester_year_setting_id' => $semester_setting_year_2024->id,
        'is_current_semester' => true
    ]);

    $semester_class = SemesterClass::factory()->create([
        'semester_setting_id' => $semester_setting->id,
        'is_active' => true
    ]);

    // create inactive left student with student deactivated role
    $user = User::factory()->create();
    $user->assignRole(Role::STUDENT_DEACTIVATED);
    $student = Student::factory()->create([
        'student_number' => 'AA-11',
        'join_date' => '2024-11-01',
        'leave_date' => '2024-11-05',
        'is_active' => false,
        'user_id' => $user->id,
        'leave_status' => StudentLeaveStatus::LEFT,
    ]);

    $return_date = now()->addDay()->toDateString();

    $user = $student->user;

    expect($user->hasRole(Role::STUDENT_DEACTIVATED))->toBeTrue()
        ->and($user->hasRole(Role::STUDENT))->toBeFalse();

    $this->mock(StudentService::class, function (MockInterface $mock) use ($return_date, &$student) {
        $mock->shouldReceive('setStudent')->once()->withArgs(function ($value) use (&$student) {
            return $value->id === $student->id;
        })->andReturnSelf();
        $mock->shouldReceive('setApiRequest')->once()->andReturnSelf();
        $mock->shouldReceive('setSemesterSetting')->once()->andReturnSelf();
        $mock->shouldReceive('setSemesterClass')->once()->andReturnSelf();
        $mock->shouldReceive('markStudentReturnedAt')->with($return_date)->once();
    });

    $response = $this->postJson(route($this->routeNamePrefix . 'return', [
        'student' => $student->id,
        'effective_date' => $return_date,
        'semester_setting_id' => $semester_setting->id,
        'semester_class_id' => $semester_class->id,
    ]));

    $response->assertStatus(200);

});

test('return error', function () {
    $user = User::factory()->create();
    $user->assignRole(Role::STUDENT_DEACTIVATED);
    $student = Student::factory()->create([
        'student_number' => 'AA-11',
        'join_date' => '2024-11-01',
        'leave_date' => '2024-11-05',
        'is_active' => false,
        'user_id' => $user->id,
        'leave_status' => StudentLeaveStatus::LEFT,
    ]);

    $response = $this->postJson(route($this->routeNamePrefix . 'return', [
        'student' => $student->id,
    ]));

    $response->assertStatus(422);

    expect($response->json())->toHaveFailedGeneralResponse()
        ->toMatchArray([
            'error' => [
                'effective_date' => [
                    'The effective date field is required.'
                ],
                'semester_setting_id' => [
                    'The semester setting id field is required.'
                ],
                'semester_class_id' => [
                    'The semester class id field is required.'
                ],
            ],
            'data' => null
        ]);
});
