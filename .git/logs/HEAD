6edd605c0dac9a52fd7a05cc14ca3c560129fef4 3ce06b84752b89e6ded1129e6c4396e61e54eedb Sim <PERSON>hen <PERSON>uan <<EMAIL>> 1742051654 +0800	commit: Review WIP
3ce06b84752b89e6ded1129e6c4396e61e54eedb 09176d9c88961d6d149d0487a6fd52c2b81bc7fc Sim Zhen Quan <<EMAIL>> 1742051738 +0800	checkout: moving from migrate-script-competition to dev
09176d9c88961d6d149d0487a6fd52c2b81bc7fc 42b5fe97fdf535bf0dbd721962c5158c3814414e Sim Zhen Quan <<EMAIL>> 1742051741 +0800	checkout: moving from dev to main
42b5fe97fdf535bf0dbd721962c5158c3814414e 85b575f8bf630138e8bb83cf5ccc70830f30070d Sim Zhen Quan <<EMAIL>> 1742051747 +0800	pull: Fast-forward
85b575f8bf630138e8bb83cf5ccc70830f30070d 5b10200afe509ad749da9a57de93d36d8fe848d0 Sim Zhen Quan <<EMAIL>> 1742053191 +0800	pull: Fast-forward
5b10200afe509ad749da9a57de93d36d8fe848d0 63e88e731221cfcd429347a3540d1b65af81478e Sim Zhen Quan <<EMAIL>> 1742053527 +0800	commit: Deployed to PRD
63e88e731221cfcd429347a3540d1b65af81478e bc0ae2467d98f1057a5bea99efbb0c41cd5e311a Sim Zhen Quan <<EMAIL>> 1742178050 +0800	checkout: moving from main to attendance-online-mode-tap-card-api
bc0ae2467d98f1057a5bea99efbb0c41cd5e311a 7f225b596c56082c763a903f3c160c1fbdb3f6f3 Sim Zhen Quan <<EMAIL>> 1742178057 +0800	merge origin/main: Merge made by the 'ort' strategy.
7f225b596c56082c763a903f3c160c1fbdb3f6f3 d21635c33fdf9e425922d6b18d35da25f02820e7 Sim Zhen Quan <<EMAIL>> 1742181322 +0800	commit: Reviewed
d21635c33fdf9e425922d6b18d35da25f02820e7 63e88e731221cfcd429347a3540d1b65af81478e Sim Zhen Quan <<EMAIL>> 1742181597 +0800	checkout: moving from attendance-online-mode-tap-card-api to main
63e88e731221cfcd429347a3540d1b65af81478e 09176d9c88961d6d149d0487a6fd52c2b81bc7fc Sim Zhen Quan <<EMAIL>> 1742181601 +0800	checkout: moving from main to dev
09176d9c88961d6d149d0487a6fd52c2b81bc7fc 5c0196444265e2519184b8007ca4e1ea686a1cf7 Sim Zhen Quan <<EMAIL>> 1742181645 +0800	merge origin/main: Merge made by the 'ort' strategy.
5c0196444265e2519184b8007ca4e1ea686a1cf7 af4aed619c4a933cb46b942f9377b9d9fa17f4e3 Sim Zhen Quan <<EMAIL>> 1742182056 +0800	commit: Deployed to DEV
af4aed619c4a933cb46b942f9377b9d9fa17f4e3 af4aed619c4a933cb46b942f9377b9d9fa17f4e3 Sim Zhen Quan <<EMAIL>> 1742182090 +0800	reset: moving to HEAD
af4aed619c4a933cb46b942f9377b9d9fa17f4e3 63e88e731221cfcd429347a3540d1b65af81478e Sim Zhen Quan <<EMAIL>> 1742182092 +0800	checkout: moving from dev to main
63e88e731221cfcd429347a3540d1b65af81478e 3ea95bfcf2a3e6447c2f8c68bf5fe695d8d55d81 Sim Zhen Quan <<EMAIL>> 1742182097 +0800	pull: Fast-forward
3ea95bfcf2a3e6447c2f8c68bf5fe695d8d55d81 52e6cb051793c7ca5fd0ffaa2d92f0b70c0581ff Sim Zhen Quan <<EMAIL>> 1742182139 +0800	commit: Move ConfigHelper call to handle() to prevent composer dump issue
52e6cb051793c7ca5fd0ffaa2d92f0b70c0581ff 3ce06b84752b89e6ded1129e6c4396e61e54eedb Sim Zhen Quan <<EMAIL>> 1742193252 +0800	checkout: moving from main to migrate-script-competition
3ce06b84752b89e6ded1129e6c4396e61e54eedb 099a01e756fefb4b09bf7f45417431adb4c2b860 Sim Zhen Quan <<EMAIL>> 1742193261 +0800	merge origin/main: Merge made by the 'ort' strategy.
099a01e756fefb4b09bf7f45417431adb4c2b860 d6940cc2a14713c2e8f4e46d9ae0b9cf06a3b35c Sim Zhen Quan <<EMAIL>> 1742195015 +0800	commit: Reviewed
d6940cc2a14713c2e8f4e46d9ae0b9cf06a3b35c 002b805574e0a909462eab8b0310024862440872 Sim Zhen Quan <<EMAIL>> 1742195289 +0800	checkout: moving from migrate-script-competition to product-resource-eager-loading
002b805574e0a909462eab8b0310024862440872 eae5ca0a31bdb4d15fcadae1007cf15406e5415d Sim Zhen Quan <<EMAIL>> 1742195300 +0800	merge origin/main: Merge made by the 'ort' strategy.
eae5ca0a31bdb4d15fcadae1007cf15406e5415d d3c2f9b5538c9c6f9ac4bba0fd20a5bc6c2a8b90 Sim Zhen Quan <<EMAIL>> 1742197069 +0800	commit: Reviewed
d3c2f9b5538c9c6f9ac4bba0fd20a5bc6c2a8b90 d3c2f9b5538c9c6f9ac4bba0fd20a5bc6c2a8b90 Sim Zhen Quan <<EMAIL>> 1742197629 +0800	checkout: moving from product-resource-eager-loading to staging/2025-03-17
d3c2f9b5538c9c6f9ac4bba0fd20a5bc6c2a8b90 3c7b98b463371b6d58b068ad10fdfbdbc51aa126 Sim Zhen Quan <<EMAIL>> 1742198500 +0800	checkout: moving from staging/2025-03-17 to leave-application-status-enhancements
3c7b98b463371b6d58b068ad10fdfbdbc51aa126 57771ad5fa5836650386ec41e4fb1f5f5a5c8f69 Sim Zhen Quan <<EMAIL>> 1742198507 +0800	merge origin/main: Merge made by the 'ort' strategy.
57771ad5fa5836650386ec41e4fb1f5f5a5c8f69 c30dec98d135815f487625a676a419d2fd3029d2 Sim Zhen Quan <<EMAIL>> 1742201222 +0800	merge origin/staging/2025-03-17: Merge made by the 'ort' strategy.
c30dec98d135815f487625a676a419d2fd3029d2 383e7dc6b8d5fb3825980f4b649307d75b27ff3d Sim Zhen Quan <<EMAIL>> 1742201441 +0800	commit: Reviewed
383e7dc6b8d5fb3825980f4b649307d75b27ff3d af4aed619c4a933cb46b942f9377b9d9fa17f4e3 Sim Zhen Quan <<EMAIL>> 1742201563 +0800	checkout: moving from leave-application-status-enhancements to dev
af4aed619c4a933cb46b942f9377b9d9fa17f4e3 de0f4cadd6ef6753654e7ea55880c8808670fd5e Sim Zhen Quan <<EMAIL>> 1742201582 +0800	merge origin/staging/2025-03-17: Merge made by the 'ort' strategy.
de0f4cadd6ef6753654e7ea55880c8808670fd5e eb9338d9bfbfd02e04ee65802d85b7b6d731293c Sim Zhen Quan <<EMAIL>> 1742202054 +0800	commit: Deployed to DEV
eb9338d9bfbfd02e04ee65802d85b7b6d731293c c7b02cd0a88fd410d58d284856f07fa8cb7a859d Sim Zhen Quan <<EMAIL>> 1742202063 +0800	checkout: moving from dev to fix/library-report
c7b02cd0a88fd410d58d284856f07fa8cb7a859d 5f05c526464c607f8e32125758f95730154afcb9 Sim Zhen Quan <<EMAIL>> 1742202079 +0800	merge origin/main: Merge made by the 'ort' strategy.
5f05c526464c607f8e32125758f95730154afcb9 5f05c526464c607f8e32125758f95730154afcb9 Sim Zhen Quan <<EMAIL>> 1742203264 +0800	checkout: moving from fix/library-report to fix/library-report
5f05c526464c607f8e32125758f95730154afcb9 5f05c526464c607f8e32125758f95730154afcb9 Sim Zhen Quan <<EMAIL>> 1742367530 +0800	reset: moving to HEAD
5f05c526464c607f8e32125758f95730154afcb9 d3c2f9b5538c9c6f9ac4bba0fd20a5bc6c2a8b90 Sim Zhen Quan <<EMAIL>> 1742367536 +0800	checkout: moving from fix/library-report to staging/2025-03-17
d3c2f9b5538c9c6f9ac4bba0fd20a5bc6c2a8b90 4d5dcbe06afbfad3518a31f23bd22a1840cf687c Sim Zhen Quan <<EMAIL>> 1742367543 +0800	pull: Fast-forward
4d5dcbe06afbfad3518a31f23bd22a1840cf687c 4d5dcbe06afbfad3518a31f23bd22a1840cf687c Sim Zhen Quan <<EMAIL>> 1742367552 +0800	checkout: moving from staging/2025-03-17 to tap-card-api-respond-userable
4d5dcbe06afbfad3518a31f23bd22a1840cf687c 6665432e624a22e36424993319453462ebe2c24b Sim Zhen Quan <<EMAIL>> 1742370227 +0800	commit: Attendance tap card APi returns userable info
6665432e624a22e36424993319453462ebe2c24b 52e6cb051793c7ca5fd0ffaa2d92f0b70c0581ff Sim Zhen Quan <<EMAIL>> 1742370275 +0800	checkout: moving from tap-card-api-respond-userable to main
52e6cb051793c7ca5fd0ffaa2d92f0b70c0581ff 061a165481e164679e5e8c0d5fcb9a2e971c5d6d Sim Zhen Quan <<EMAIL>> 1742370280 +0800	pull: Fast-forward
061a165481e164679e5e8c0d5fcb9a2e971c5d6d 903944840d3648668e9b4d311acbc0f61422b8b4 Sim Zhen Quan <<EMAIL>> 1742370317 +0800	commit: Remove bracket for exam marks formula
903944840d3648668e9b4d311acbc0f61422b8b4 eb9338d9bfbfd02e04ee65802d85b7b6d731293c Sim Zhen Quan <<EMAIL>> 1742370326 +0800	checkout: moving from main to dev
eb9338d9bfbfd02e04ee65802d85b7b6d731293c 4d5dcbe06afbfad3518a31f23bd22a1840cf687c Sim Zhen Quan <<EMAIL>> 1742370337 +0800	checkout: moving from dev to staging/2025-03-17
4d5dcbe06afbfad3518a31f23bd22a1840cf687c c76837d7cf9a9ae0090213564cfdab621698e807 Sim Zhen Quan <<EMAIL>> 1742370343 +0800	pull: Fast-forward
c76837d7cf9a9ae0090213564cfdab621698e807 063366ebe21d42b416c5a005808a10d4347a1e51 Sim Zhen Quan <<EMAIL>> 1742370343 +0800	merge origin/main: Merge made by the 'ort' strategy.
063366ebe21d42b416c5a005808a10d4347a1e51 eb9338d9bfbfd02e04ee65802d85b7b6d731293c Sim Zhen Quan <<EMAIL>> 1742370354 +0800	checkout: moving from staging/2025-03-17 to dev
eb9338d9bfbfd02e04ee65802d85b7b6d731293c d5448e8985523ca2168aa3a06752def4b7edcd86 Sim Zhen Quan <<EMAIL>> 1742370361 +0800	merge staging/2025-03-17: Merge made by the 'ort' strategy.
d5448e8985523ca2168aa3a06752def4b7edcd86 063366ebe21d42b416c5a005808a10d4347a1e51 Sim Zhen Quan <<EMAIL>> 1742370600 +0800	checkout: moving from dev to staging/2025-03-17
063366ebe21d42b416c5a005808a10d4347a1e51 3fc10b8db1bc37dcc8b3000695eebd25a8799dc4 Sim Zhen Quan <<EMAIL>> 1742371865 +0800	commit: Use UserableResource instead of SimpleUserableResource
3fc10b8db1bc37dcc8b3000695eebd25a8799dc4 3fc10b8db1bc37dcc8b3000695eebd25a8799dc4 Sim Zhen Quan <<EMAIL>> 1742375856 +0800	checkout: moving from staging/2025-03-17 to DO-NOT-MERGE-qas-temporary-data
3fc10b8db1bc37dcc8b3000695eebd25a8799dc4 d9c7cc288405f1d63af37459f64b45e307b93221 Sim Zhen Quan <<EMAIL>> 1742376442 +0800	commit: PLEASE REPORT TO LUCAS IF YOU SEE THIS COMMITED TO MAIN BRANCH
d9c7cc288405f1d63af37459f64b45e307b93221 4c063d2070b586535e17b6610b279722c792e52b Sim Zhen Quan <<EMAIL>> 1742376464 +0800	checkout: moving from DO-NOT-MERGE-qas-temporary-data to qas
4c063d2070b586535e17b6610b279722c792e52b af3d0da2cf58819a4b83e9d09d16cc4b96d6ebcd Sim Zhen Quan <<EMAIL>> 1742376468 +0800	merge DO-NOT-MERGE-qas-temporary-data: Merge made by the 'ort' strategy.
af3d0da2cf58819a4b83e9d09d16cc4b96d6ebcd b11aefa89a0d2983e1847bdef6544c77517257d4 Sim Zhen Quan <<EMAIL>> 1742376712 +0800	commit: Deployed to qas
b11aefa89a0d2983e1847bdef6544c77517257d4 5b2d273be6028018632180ae6683034a6dab04a9 Sim Zhen Quan <<EMAIL>> 1742376720 +0800	checkout: moving from qas to substitute-teacher-enhancements
5b2d273be6028018632180ae6683034a6dab04a9 7472e3cbee395adbdfa37fbada10f2718f0bae63 Sim Zhen Quan <<EMAIL>> 1742376726 +0800	merge origin/main: Merge made by the 'ort' strategy.
7472e3cbee395adbdfa37fbada10f2718f0bae63 d5448e8985523ca2168aa3a06752def4b7edcd86 Sim Zhen Quan <<EMAIL>> 1742377377 +0800	checkout: moving from substitute-teacher-enhancements to dev
d5448e8985523ca2168aa3a06752def4b7edcd86 5d4dbb156cdf62ad593c7fa0f247f02879b3991c Sim Zhen Quan <<EMAIL>> 1742377459 +0800	commit (merge): Merge origin/main
5d4dbb156cdf62ad593c7fa0f247f02879b3991c c300f3c79ed869c41c06070078cb6b39cbb6aec7 Sim Zhen Quan <<EMAIL>> 1742379726 +0800	merge origin/staging/2025-03-17: Merge made by the 'ort' strategy.
c300f3c79ed869c41c06070078cb6b39cbb6aec7 c300f3c79ed869c41c06070078cb6b39cbb6aec7 Sim Zhen Quan <<EMAIL>> 1742404414 +0800	reset: moving to HEAD
c300f3c79ed869c41c06070078cb6b39cbb6aec7 903944840d3648668e9b4d311acbc0f61422b8b4 Sim Zhen Quan <<EMAIL>> 1742404414 +0800	checkout: moving from dev to main
903944840d3648668e9b4d311acbc0f61422b8b4 81aa281a02aa8f4aa65f6a2527b5b8c7e614d74c Sim Zhen Quan <<EMAIL>> 1742404419 +0800	pull: Fast-forward
81aa281a02aa8f4aa65f6a2527b5b8c7e614d74c 7b0774741487bce02df6bd5811dfb5c06ba20bea Sim Zhen Quan <<EMAIL>> 1742404436 +0800	commit: Fixed substitute_record includes overwrite
7b0774741487bce02df6bd5811dfb5c06ba20bea d3690a3bcbcd5028aa7b61454ccf9f618a48d43a Sim Zhen Quan <<EMAIL>> 1742404447 +0800	checkout: moving from main to feature/report-card-custom-functions
d3690a3bcbcd5028aa7b61454ccf9f618a48d43a f0caf1773a114c7a559337fbdcdb97680acc7c29 Sim Zhen Quan <<EMAIL>> 1742404556 +0800	commit (merge): Merge origin/main
f0caf1773a114c7a559337fbdcdb97680acc7c29 02f34cdaf6f3b4cade83741c7f5261f6f9f49560 Sim Zhen Quan <<EMAIL>> 1742445431 +0800	commit: Removed testing code
02f34cdaf6f3b4cade83741c7f5261f6f9f49560 616bd0a0253f10e734543840ba4d1393cd366e59 Sim Zhen Quan <<EMAIL>> 1742446433 +0800	checkout: moving from feature/report-card-custom-functions to fix/billing-document-enhancement
616bd0a0253f10e734543840ba4d1393cd366e59 1cf01439175a955d8852955112743e303c1158c0 Sim Zhen Quan <<EMAIL>> 1742446438 +0800	pull: Fast-forward
1cf01439175a955d8852955112743e303c1158c0 1cf01439175a955d8852955112743e303c1158c0 Sim Zhen Quan <<EMAIL>> 1742449485 +0800	reset: moving to HEAD
1cf01439175a955d8852955112743e303c1158c0 02f34cdaf6f3b4cade83741c7f5261f6f9f49560 Sim Zhen Quan <<EMAIL>> 1742449506 +0800	checkout: moving from fix/billing-document-enhancement to feature/report-card-custom-functions
02f34cdaf6f3b4cade83741c7f5261f6f9f49560 6470aade5ea5c09945ce50bd28803ffc57a36545 Sim Zhen Quan <<EMAIL>> 1742449929 +0800	checkout: moving from feature/report-card-custom-functions to get-requestor-timeslots-enhancements
6470aade5ea5c09945ce50bd28803ffc57a36545 8327ef636d25d4c0058dc927634b4f155ee4a0b4 Sim Zhen Quan <<EMAIL>> 1742456290 +0800	commit: Added semester_class_id and subject_id filter
8327ef636d25d4c0058dc927634b4f155ee4a0b4 02f34cdaf6f3b4cade83741c7f5261f6f9f49560 Sim Zhen Quan <<EMAIL>> 1742460029 +0800	checkout: moving from get-requestor-timeslots-enhancements to feature/report-card-custom-functions
02f34cdaf6f3b4cade83741c7f5261f6f9f49560 0226d414158a2a51a842f789e43d8a1ce11dc528 Sim Zhen Quan <<EMAIL>> 1742460036 +0800	pull: Fast-forward
0226d414158a2a51a842f789e43d8a1ce11dc528 8327ef636d25d4c0058dc927634b4f155ee4a0b4 Sim Zhen Quan <<EMAIL>> 1742461035 +0800	checkout: moving from feature/report-card-custom-functions to get-requestor-timeslots-enhancements
8327ef636d25d4c0058dc927634b4f155ee4a0b4 c300f3c79ed869c41c06070078cb6b39cbb6aec7 Sim Zhen Quan <<EMAIL>> 1742461255 +0800	checkout: moving from get-requestor-timeslots-enhancements to dev
c300f3c79ed869c41c06070078cb6b39cbb6aec7 d7a77009b52525cd9fc5e2f8ccbe4a7f775ac7f8 Sim Zhen Quan <<EMAIL>> 1742461261 +0800	merge origin/main: Merge made by the 'ort' strategy.
d7a77009b52525cd9fc5e2f8ccbe4a7f775ac7f8 3d915c3e886a0e59f70e02a6b622ebdb0ab1eb10 Sim Zhen Quan <<EMAIL>> 1742461385 +0800	merge origin/staging/2025-03-17: Merge made by the 'ort' strategy.
3d915c3e886a0e59f70e02a6b622ebdb0ab1eb10 7d84fd73a62e2842b6767a0a4fece0a96524c1a3 Sim Zhen Quan <<EMAIL>> 1742461783 +0800	commit: Deployed to DEV
7d84fd73a62e2842b6767a0a4fece0a96524c1a3 0226d414158a2a51a842f789e43d8a1ce11dc528 Sim Zhen Quan <<EMAIL>> 1742461825 +0800	checkout: moving from dev to feature/report-card-custom-functions
0226d414158a2a51a842f789e43d8a1ce11dc528 a981e25f07c2fd8e3fb291eac6b1bb18729affd0 Sim Zhen Quan <<EMAIL>> 1742462239 +0800	merge origin/main: Merge made by the 'ort' strategy.
a981e25f07c2fd8e3fb291eac6b1bb18729affd0 0c6d94ba521e1d85c67dd49170e2ba11b970bd9b Sim Zhen Quan <<EMAIL>> 1742464663 +0800	checkout: moving from feature/report-card-custom-functions to fix/class-permission-enhancement
0c6d94ba521e1d85c67dd49170e2ba11b970bd9b 7fd319be4b5f1a900a13c01317f4eaf4ec8e3583 Sim Zhen Quan <<EMAIL>> 1742464669 +0800	merge origin/main: Merge made by the 'ort' strategy.
7fd319be4b5f1a900a13c01317f4eaf4ec8e3583 68078d3d43d3ebdd0f0342fd1bb8438f9edcc7b8 Sim Zhen Quan <<EMAIL>> 1742464712 +0800	merge origin/staging/2025-03-17: Merge made by the 'ort' strategy.
68078d3d43d3ebdd0f0342fd1bb8438f9edcc7b8 3fc10b8db1bc37dcc8b3000695eebd25a8799dc4 Sim Zhen Quan <<EMAIL>> 1742464863 +0800	checkout: moving from fix/class-permission-enhancement to staging/2025-03-17
3fc10b8db1bc37dcc8b3000695eebd25a8799dc4 157638539063a8fc4d44a4efaec02330a7cc2d55 Sim Zhen Quan <<EMAIL>> 1742464868 +0800	pull: Fast-forward
157638539063a8fc4d44a4efaec02330a7cc2d55 327e688514120980874496cb02d4e4bae4f7b006 Sim Zhen Quan <<EMAIL>> 1742464870 +0800	merge origin/main: Merge made by the 'ort' strategy.
327e688514120980874496cb02d4e4bae4f7b006 68078d3d43d3ebdd0f0342fd1bb8438f9edcc7b8 Sim Zhen Quan <<EMAIL>> 1742464944 +0800	checkout: moving from staging/2025-03-17 to fix/class-permission-enhancement
68078d3d43d3ebdd0f0342fd1bb8438f9edcc7b8 f94f5a581b18266024aa72bfa7b0f7957a8e4489 Sim Zhen Quan <<EMAIL>> 1742464966 +0800	merge origin/staging/2025-03-17: Merge made by the 'ort' strategy.
f94f5a581b18266024aa72bfa7b0f7957a8e4489 be9acc61845ce1a320fd4a05eedb399ef3dfbebf Sim Zhen Quan <<EMAIL>> 1742465689 +0800	commit: Sorted permissions
be9acc61845ce1a320fd4a05eedb399ef3dfbebf 7d84fd73a62e2842b6767a0a4fece0a96524c1a3 Sim Zhen Quan <<EMAIL>> 1742466040 +0800	checkout: moving from fix/class-permission-enhancement to dev
7d84fd73a62e2842b6767a0a4fece0a96524c1a3 9b44657f26b90b4781e2101ddea870e6762feeb5 Sim Zhen Quan <<EMAIL>> 1742466057 +0800	merge origin/staging/2025-03-17: Merge made by the 'ort' strategy.
9b44657f26b90b4781e2101ddea870e6762feeb5 5f05c526464c607f8e32125758f95730154afcb9 Sim Zhen Quan <<EMAIL>> 1742468162 +0800	checkout: moving from dev to fix/library-report
5f05c526464c607f8e32125758f95730154afcb9 bdfd81782e8a3b9f87f8ff13d0a16fb7d0add758 Sim Zhen Quan <<EMAIL>> 1742468167 +0800	merge origin/main: Merge made by the 'ort' strategy.
bdfd81782e8a3b9f87f8ff13d0a16fb7d0add758 bdfd81782e8a3b9f87f8ff13d0a16fb7d0add758 Sim Zhen Quan <<EMAIL>> 1742468277 +0800	checkout: moving from fix/library-report to fix/library-report
bdfd81782e8a3b9f87f8ff13d0a16fb7d0add758 3ef34fca91cc82bf9ab8e742ad5162f54afa8b52 Sim Zhen Quan <<EMAIL>> 1742523549 +0800	commit: Reviewed
3ef34fca91cc82bf9ab8e742ad5162f54afa8b52 0df790943aae84f72d31284d9bcb9eaff0522504 Sim Zhen Quan <<EMAIL>> 1742524697 +0800	checkout: moving from fix/library-report to substitute-record-fix-bug
0df790943aae84f72d31284d9bcb9eaff0522504 9ba692586cc65b3a760bca0960e2e04bde5700ab Sim Zhen Quan <<EMAIL>> 1742525225 +0800	commit: Reviewed
9ba692586cc65b3a760bca0960e2e04bde5700ab 9b44657f26b90b4781e2101ddea870e6762feeb5 Sim Zhen Quan <<EMAIL>> 1742525279 +0800	checkout: moving from substitute-record-fix-bug to dev
9b44657f26b90b4781e2101ddea870e6762feeb5 53b02539771243951344269e1a8e6c92d39e14d6 Sim Zhen Quan <<EMAIL>> 1742525296 +0800	merge origin/main: Merge made by the 'ort' strategy.
53b02539771243951344269e1a8e6c92d39e14d6 5a0939f5908f4d7d65d73e4928302d04ab1c28f9 Sim Zhen Quan <<EMAIL>> 1742525309 +0800	merge origin/staging/2025-03-17: Merge made by the 'ort' strategy.
5a0939f5908f4d7d65d73e4928302d04ab1c28f9 a981e25f07c2fd8e3fb291eac6b1bb18729affd0 Sim Zhen Quan <<EMAIL>> 1742525688 +0800	checkout: moving from dev to feature/report-card-custom-functions
a981e25f07c2fd8e3fb291eac6b1bb18729affd0 78ec47586b2aba316dd8a07bbdc535e437d89040 Sim Zhen Quan <<EMAIL>> 1742525697 +0800	merge origin/main: Merge made by the 'ort' strategy.
78ec47586b2aba316dd8a07bbdc535e437d89040 78ec47586b2aba316dd8a07bbdc535e437d89040 Sim Zhen Quan <<EMAIL>> 1742525778 +0800	checkout: moving from feature/report-card-custom-functions to feature/report-card-custom-functions
78ec47586b2aba316dd8a07bbdc535e437d89040 7b0774741487bce02df6bd5811dfb5c06ba20bea Sim Zhen Quan <<EMAIL>> 1742532014 +0800	checkout: moving from feature/report-card-custom-functions to main
7b0774741487bce02df6bd5811dfb5c06ba20bea 8ea8456c9563005f1f68c09cd3aa50b1c6a21ed3 Sim Zhen Quan <<EMAIL>> 1742532019 +0800	pull: Fast-forward
8ea8456c9563005f1f68c09cd3aa50b1c6a21ed3 291204e3fab53d759922a1d3d0c2e5243b04f28c Sim Zhen Quan <<EMAIL>> 1742534965 +0800	commit: Remove sending of invoice via email
291204e3fab53d759922a1d3d0c2e5243b04f28c 8da27fecbb64f9ed10669251010a21416f132948 Sim Zhen Quan <<EMAIL>> 1742536141 +0800	checkout: moving from main to fix/timetable-teacher
8da27fecbb64f9ed10669251010a21416f132948 7c2a820a535d26f1afcdcf07e3790f493a17a985 Sim Zhen Quan <<EMAIL>> 1742536147 +0800	merge origin/main: Merge made by the 'ort' strategy.
7c2a820a535d26f1afcdcf07e3790f493a17a985 f2d663b17e7d8e079cc15ac34af20a99e63a5c8d Sim Zhen Quan <<EMAIL>> 1742538531 +0800	commit: Reviewed
f2d663b17e7d8e079cc15ac34af20a99e63a5c8d 5a0939f5908f4d7d65d73e4928302d04ab1c28f9 Sim Zhen Quan <<EMAIL>> 1742538913 +0800	checkout: moving from fix/timetable-teacher to dev
5a0939f5908f4d7d65d73e4928302d04ab1c28f9 0b5f64b93ea5c4e283918371397d2fadd9b3929c Sim Zhen Quan <<EMAIL>> 1742538919 +0800	merge origin/main: Merge made by the 'ort' strategy.
0b5f64b93ea5c4e283918371397d2fadd9b3929c 291204e3fab53d759922a1d3d0c2e5243b04f28c Sim Zhen Quan <<EMAIL>> 1742539525 +0800	checkout: moving from dev to main
291204e3fab53d759922a1d3d0c2e5243b04f28c 3dabd411866a1137188a44d028bc8be1eb398471 Sim Zhen Quan <<EMAIL>> 1742539530 +0800	pull: Fast-forward
3dabd411866a1137188a44d028bc8be1eb398471 1c6f001d71e5b6bb3c9ac56af292592dc814cfbb Sim Zhen Quan <<EMAIL>> 1742539540 +0800	commit: Update timetable permission dependency
1c6f001d71e5b6bb3c9ac56af292592dc814cfbb 26f57c15834a33a8a4d027c0f1c12c2326c2eed6 Sim Zhen Quan <<EMAIL>> 1742541197 +0800	checkout: moving from main to leave-application-enhancements
26f57c15834a33a8a4d027c0f1c12c2326c2eed6 fb6dc6aa7203da7cc78b417df57e3c908034761e Sim Zhen Quan <<EMAIL>> 1742541205 +0800	merge origin/main: Merge made by the 'ort' strategy.
fb6dc6aa7203da7cc78b417df57e3c908034761e 327e688514120980874496cb02d4e4bae4f7b006 Sim Zhen Quan <<EMAIL>> 1742541381 +0800	checkout: moving from leave-application-enhancements to staging/2025-03-17
327e688514120980874496cb02d4e4bae4f7b006 02a1f6be0c408c008969f46b4a17f1ff668f6f3d Sim Zhen Quan <<EMAIL>> 1742541386 +0800	pull: Fast-forward
02a1f6be0c408c008969f46b4a17f1ff668f6f3d 379ff82a1a5176f446ea571251a431ef878e0aa3 Sim Zhen Quan <<EMAIL>> 1742541386 +0800	merge origin/main: Merge made by the 'ort' strategy.
379ff82a1a5176f446ea571251a431ef878e0aa3 fb6dc6aa7203da7cc78b417df57e3c908034761e Sim Zhen Quan <<EMAIL>> 1742541563 +0800	checkout: moving from staging/2025-03-17 to leave-application-enhancements
fb6dc6aa7203da7cc78b417df57e3c908034761e 379ff82a1a5176f446ea571251a431ef878e0aa3 Sim Zhen Quan <<EMAIL>> 1742542492 +0800	checkout: moving from leave-application-enhancements to staging/2025-03-17
379ff82a1a5176f446ea571251a431ef878e0aa3 379ff82a1a5176f446ea571251a431ef878e0aa3 Sim Zhen Quan <<EMAIL>> 1742542505 +0800	checkout: moving from staging/2025-03-17 to staging/2025-03-17
379ff82a1a5176f446ea571251a431ef878e0aa3 fb6dc6aa7203da7cc78b417df57e3c908034761e Sim Zhen Quan <<EMAIL>> 1742542552 +0800	checkout: moving from staging/2025-03-17 to leave-application-enhancements
fb6dc6aa7203da7cc78b417df57e3c908034761e ed58cda95addcfe51bb6bc88bcfb8bf98e60b9aa Sim Zhen Quan <<EMAIL>> 1742542553 +0800	merge origin/staging/2025-03-17: Merge made by the 'ort' strategy.
ed58cda95addcfe51bb6bc88bcfb8bf98e60b9aa 1cf01439175a955d8852955112743e303c1158c0 Sim Zhen Quan <<EMAIL>> 1742546829 +0800	checkout: moving from leave-application-enhancements to fix/billing-document-enhancement
1cf01439175a955d8852955112743e303c1158c0 2dd51ccba8daaf64827a8eabbc1b830b8f96d90e Sim Zhen Quan <<EMAIL>> 1742546835 +0800	pull: Fast-forward
2dd51ccba8daaf64827a8eabbc1b830b8f96d90e ed58cda95addcfe51bb6bc88bcfb8bf98e60b9aa Sim Zhen Quan <<EMAIL>> 1742550615 +0800	checkout: moving from fix/billing-document-enhancement to leave-application-enhancements
ed58cda95addcfe51bb6bc88bcfb8bf98e60b9aa 2ca3e07e73244c2d992da4eff958905ca3558a6c Sim Zhen Quan <<EMAIL>> 1742550621 +0800	pull: Fast-forward
2ca3e07e73244c2d992da4eff958905ca3558a6c 9e18aff62d7190217e690f8c9beb3b3898a96243 Sim Zhen Quan <<EMAIL>> 1742738427 +0800	commit: Reviewed. Check comments on slack.
9e18aff62d7190217e690f8c9beb3b3898a96243 1a889c792bc9f7ed59b5312753b701fcaa2cc36a Sim Zhen Quan <<EMAIL>> 1742742256 +0800	commit: Added function in interface
1a889c792bc9f7ed59b5312753b701fcaa2cc36a 1c6f001d71e5b6bb3c9ac56af292592dc814cfbb Sim Zhen Quan <<EMAIL>> 1742742979 +0800	checkout: moving from leave-application-enhancements to main
1c6f001d71e5b6bb3c9ac56af292592dc814cfbb 07a23bfa559338fc93ebea54086d799ce9ece419 Sim Zhen Quan <<EMAIL>> 1742742984 +0800	pull: Fast-forward
07a23bfa559338fc93ebea54086d799ce9ece419 51a92082d8066fa03540dc3bf6ee21ab8f2b99ce Sim Zhen Quan <<EMAIL>> 1742782975 +0800	commit: Deployed to prd
51a92082d8066fa03540dc3bf6ee21ab8f2b99ce 1a889c792bc9f7ed59b5312753b701fcaa2cc36a Sim Zhen Quan <<EMAIL>> 1742783010 +0800	checkout: moving from main to leave-application-enhancements
1a889c792bc9f7ed59b5312753b701fcaa2cc36a 51a92082d8066fa03540dc3bf6ee21ab8f2b99ce Sim Zhen Quan <<EMAIL>> 1742798368 +0800	checkout: moving from leave-application-enhancements to main
51a92082d8066fa03540dc3bf6ee21ab8f2b99ce 0b5f64b93ea5c4e283918371397d2fadd9b3929c Sim Zhen Quan <<EMAIL>> 1742798409 +0800	checkout: moving from main to dev
0b5f64b93ea5c4e283918371397d2fadd9b3929c 51a92082d8066fa03540dc3bf6ee21ab8f2b99ce Sim Zhen Quan <<EMAIL>> 1742798444 +0800	checkout: moving from dev to main
51a92082d8066fa03540dc3bf6ee21ab8f2b99ce c0a666403e8e0e57eddfa9698dcb9281d07dd1b5 Sim Zhen Quan <<EMAIL>> 1742798500 +0800	checkout: moving from main to pos-check-charge-status
c0a666403e8e0e57eddfa9698dcb9281d07dd1b5 1ee2ecbb4f585cbd2c1f102444441415d9c09017 Sim Zhen Quan <<EMAIL>> 1742798767 +0800	commit (merge): Merge origin/main
1ee2ecbb4f585cbd2c1f102444441415d9c09017 51a92082d8066fa03540dc3bf6ee21ab8f2b99ce Sim Zhen Quan <<EMAIL>> 1742799388 +0800	checkout: moving from pos-check-charge-status to main
51a92082d8066fa03540dc3bf6ee21ab8f2b99ce 2054a6adcce6264d9de4e86aafd0e1706ed83834 Sim Zhen Quan <<EMAIL>> 1742799425 +0800	commit: Bug fix
2054a6adcce6264d9de4e86aafd0e1706ed83834 b7f41a58b5282cbf2f1958ba56ee366644340149 Sim Zhen Quan <<EMAIL>> 1742801788 +0800	checkout: moving from main to migrate-script-timetable
b7f41a58b5282cbf2f1958ba56ee366644340149 c333b0a4c006b75aef400b9d8cf1255c7338175e Sim Zhen Quan <<EMAIL>> 1742801796 +0800	merge origin/main: Merge made by the 'ort' strategy.
c333b0a4c006b75aef400b9d8cf1255c7338175e 1ee2ecbb4f585cbd2c1f102444441415d9c09017 Sim Zhen Quan <<EMAIL>> 1742833693 +0800	checkout: moving from migrate-script-timetable to pos-check-charge-status
1ee2ecbb4f585cbd2c1f102444441415d9c09017 4ddab932918ddc9c72edfc5f2a8e02265e9663ad Sim Zhen Quan <<EMAIL>> 1742833719 +0800	merge origin/main: Merge made by the 'ort' strategy.
4ddab932918ddc9c72edfc5f2a8e02265e9663ad 2054a6adcce6264d9de4e86aafd0e1706ed83834 Sim Zhen Quan <<EMAIL>> 1742833761 +0800	checkout: moving from pos-check-charge-status to main
2054a6adcce6264d9de4e86aafd0e1706ed83834 9ebc71b626650133146be5ba5aeeb39c922a87e3 Sim Zhen Quan <<EMAIL>> 1742833766 +0800	pull: Fast-forward
9ebc71b626650133146be5ba5aeeb39c922a87e3 8a5fd5d98d139d7c6e3da6745ae49fca5f5c4691 Sim Zhen Quan <<EMAIL>> 1742834036 +0800	commit: Deployed to prd
8a5fd5d98d139d7c6e3da6745ae49fca5f5c4691 c8e9d9fd65fd7f4cdef8979f52a92aed79949526 Sim Zhen Quan <<EMAIL>> 1742834045 +0800	checkout: moving from main to migrate-script-society-position
c8e9d9fd65fd7f4cdef8979f52a92aed79949526 2583f3eca374ae6dd6fa2520cded4e44bf254368 Sim Zhen Quan <<EMAIL>> 1742834267 +0800	commit (merge): Merge origin/main
2583f3eca374ae6dd6fa2520cded4e44bf254368 f014dce0edd250c29354a26ea40076de92ac888d Sim Zhen Quan <<EMAIL>> 1742838247 +0800	commit: Reviewed
f014dce0edd250c29354a26ea40076de92ac888d ab875585d341330ead57a665c9b8c9923e1657e0 Sim Zhen Quan <<EMAIL>> 1742838316 +0800	checkout: moving from migrate-script-society-position to migrate-script-comprehensive-assessment
ab875585d341330ead57a665c9b8c9923e1657e0 5de802f1384a587e521ad07c8d90a4c59413bd30 Sim Zhen Quan <<EMAIL>> 1742838382 +0800	commit (merge): Merge origin/main
5de802f1384a587e521ad07c8d90a4c59413bd30 f9514475c72faa081bee03e314723fcce442cb57 Sim Zhen Quan <<EMAIL>> 1742838589 +0800	commit: Reviewed
f9514475c72faa081bee03e314723fcce442cb57 78ec47586b2aba316dd8a07bbdc535e437d89040 Sim Zhen Quan <<EMAIL>> 1742838653 +0800	checkout: moving from migrate-script-comprehensive-assessment to feature/report-card-custom-functions
78ec47586b2aba316dd8a07bbdc535e437d89040 0aed1973b503e1426591ec1a68f8e6b90dcedd59 Sim Zhen Quan <<EMAIL>> 1742838657 +0800	pull: Fast-forward
0aed1973b503e1426591ec1a68f8e6b90dcedd59 f56a99196355190613de5ebfc51c5ee7d0859ff0 Sim Zhen Quan <<EMAIL>> 1742838658 +0800	merge origin/main: Merge made by the 'ort' strategy.
f56a99196355190613de5ebfc51c5ee7d0859ff0 0b5f64b93ea5c4e283918371397d2fadd9b3929c Sim Zhen Quan <<EMAIL>> 1742838710 +0800	checkout: moving from feature/report-card-custom-functions to dev
0b5f64b93ea5c4e283918371397d2fadd9b3929c eea49ddbfd71b206acd7b0b76d3bbd48ee55f091 Sim Zhen Quan <<EMAIL>> 1742838715 +0800	merge feature/report-card-custom-functions: Merge made by the 'ort' strategy.
eea49ddbfd71b206acd7b0b76d3bbd48ee55f091 d4d8517b3d88ba23a11d9a6f831e0a68b725d68a Sim Zhen Quan <<EMAIL>> 1742873506 +0800	commit: Deployed to dev
d4d8517b3d88ba23a11d9a6f831e0a68b725d68a 8a5fd5d98d139d7c6e3da6745ae49fca5f5c4691 Sim Zhen Quan <<EMAIL>> 1742874091 +0800	checkout: moving from dev to main
8a5fd5d98d139d7c6e3da6745ae49fca5f5c4691 12311d37bc6d65b2e1ddb8f7fe5495b970ac5f90 Sim Zhen Quan <<EMAIL>> 1742874099 +0800	pull: Fast-forward
12311d37bc6d65b2e1ddb8f7fe5495b970ac5f90 6a41e84b9d4bc2b5eb9f995d41e73c03b15de1f7 Sim Zhen Quan <<EMAIL>> 1742874633 +0800	commit: Class subject added semester_setting_id
6a41e84b9d4bc2b5eb9f995d41e73c03b15de1f7 6a41e84b9d4bc2b5eb9f995d41e73c03b15de1f7 Sim Zhen Quan <<EMAIL>> 1742888407 +0800	reset: moving to HEAD
6a41e84b9d4bc2b5eb9f995d41e73c03b15de1f7 2dd51ccba8daaf64827a8eabbc1b830b8f96d90e Sim Zhen Quan <<EMAIL>> 1742888408 +0800	checkout: moving from main to fix/billing-document-enhancement
2dd51ccba8daaf64827a8eabbc1b830b8f96d90e 60ee8a404d40159728bcb702321b80b08e685c66 Sim Zhen Quan <<EMAIL>> 1742888418 +0800	merge origin/main: Merge made by the 'ort' strategy.
60ee8a404d40159728bcb702321b80b08e685c66 7ddcea58f310ae8c7dcae92050602be96322b64a Sim Zhen Quan <<EMAIL>> 1742892765 +0800	commit: Reviewed
7ddcea58f310ae8c7dcae92050602be96322b64a d4d8517b3d88ba23a11d9a6f831e0a68b725d68a Sim Zhen Quan <<EMAIL>> 1742892856 +0800	checkout: moving from fix/billing-document-enhancement to dev
d4d8517b3d88ba23a11d9a6f831e0a68b725d68a c26c3b0a603c9bde79a928eea8cb1eded149653f Sim Zhen Quan <<EMAIL>> 1742892902 +0800	commit (merge): Merge origin/main
c26c3b0a603c9bde79a928eea8cb1eded149653f d96f45ac51e55e673e004185c590783d195a51a9 Sim Zhen Quan <<EMAIL>> 1742892954 +0800	commit: Fix library import
d96f45ac51e55e673e004185c590783d195a51a9 3b9b54534a31a407cf6483673ea47561c7fe0ba5 Sim Zhen Quan <<EMAIL>> 1742893720 +0800	commit: Deployed to DEV
3b9b54534a31a407cf6483673ea47561c7fe0ba5 f36b21d8a37d18668ecae11c7f7839c8069e8a04 Sim Zhen Quan <<EMAIL>> 1742893740 +0800	checkout: moving from dev to migration-fees
f36b21d8a37d18668ecae11c7f7839c8069e8a04 ca988a8ff6e1fec22c4a39e031502e9cc7f015e3 Sim Zhen Quan <<EMAIL>> 1742893951 +0800	commit (merge): Merge origin/main
ca988a8ff6e1fec22c4a39e031502e9cc7f015e3 174367e2a96616e843c48f9a23b9086e26989f96 Sim Zhen Quan <<EMAIL>> 1742901274 +0800	commit: Reviewed
174367e2a96616e843c48f9a23b9086e26989f96 b11aefa89a0d2983e1847bdef6544c77517257d4 Sim Zhen Quan <<EMAIL>> 1742901304 +0800	checkout: moving from migration-fees to qas
b11aefa89a0d2983e1847bdef6544c77517257d4 15765ed8d07bd7f61fdff11e4137171282c88004 Sim Zhen Quan <<EMAIL>> 1742901312 +0800	merge origin/main: Merge made by the 'ort' strategy.
15765ed8d07bd7f61fdff11e4137171282c88004 0fca5ea266186bd2b4063e18eb1b5d4781894c8d Sim Zhen Quan <<EMAIL>> 1742922504 +0800	commit: Deployed to qas
0fca5ea266186bd2b4063e18eb1b5d4781894c8d 6a41e84b9d4bc2b5eb9f995d41e73c03b15de1f7 Sim Zhen Quan <<EMAIL>> 1742922516 +0800	checkout: moving from qas to main
6a41e84b9d4bc2b5eb9f995d41e73c03b15de1f7 5ab269abc7a5aef7179b69be49d6e7f86d8d63cb Sim Zhen Quan <<EMAIL>> 1742922527 +0800	merge origin/main: Fast-forward
5ab269abc7a5aef7179b69be49d6e7f86d8d63cb 430c99f3301d86ed4033559c6230fa847514b318 Sim Zhen Quan <<EMAIL>> 1742923317 +0800	commit: Fix test cases
430c99f3301d86ed4033559c6230fa847514b318 f56a99196355190613de5ebfc51c5ee7d0859ff0 Sim Zhen Quan <<EMAIL>> 1742955719 +0800	checkout: moving from main to feature/report-card-custom-functions
f56a99196355190613de5ebfc51c5ee7d0859ff0 f46c985ad25cd47346010486bae71379668a5164 Sim Zhen Quan <<EMAIL>> 1742955823 +0800	merge origin/main: Merge made by the 'ort' strategy.
f46c985ad25cd47346010486bae71379668a5164 2f078a4654f988427650fcb06e874f08867c85f2 Sim Zhen Quan <<EMAIL>> 1742957630 +0800	checkout: moving from feature/report-card-custom-functions to feature/qr-code-payment
2f078a4654f988427650fcb06e874f08867c85f2 2f078a4654f988427650fcb06e874f08867c85f2 Sim Zhen Quan <<EMAIL>> 1742957634 +0800	checkout: moving from feature/qr-code-payment to feature/qr-code-payment
2f078a4654f988427650fcb06e874f08867c85f2 c5abf670f1acba6de19c8850953f2884d82e5dc7 Sim Zhen Quan <<EMAIL>> 1742958313 +0800	commit (merge): Merge origin/main
c5abf670f1acba6de19c8850953f2884d82e5dc7 3784c3a3a6bc8e41bb4d8641a89865fb2e3c8efd Sim Zhen Quan <<EMAIL>> 1742958931 +0800	commit: Review WIP
3784c3a3a6bc8e41bb4d8641a89865fb2e3c8efd 8bd52234c0ea8613fecd734a6387f1f8b316044e Sim Zhen Quan <<EMAIL>> 1742959874 +0800	checkout: moving from feature/qr-code-payment to exam-leftovers
8bd52234c0ea8613fecd734a6387f1f8b316044e 88896b84e12120f68047de724ab56b5bac785cc0 Sim Zhen Quan <<EMAIL>> 1742959881 +0800	merge origin/main: Merge made by the 'ort' strategy.
88896b84e12120f68047de724ab56b5bac785cc0 46b32420ebbe8c00143bb769e4efa9838d67c608 Sim Zhen Quan <<EMAIL>> 1742963487 +0800	checkout: moving from exam-leftovers to migration-reward-punishment
46b32420ebbe8c00143bb769e4efa9838d67c608 eff54e0cf27659f21a66915b1902454e23f43616 Sim Zhen Quan <<EMAIL>> 1742963724 +0800	commit (merge): Merge origin/main
eff54e0cf27659f21a66915b1902454e23f43616 de36acf5350beb9cec97fbf5ea4bf13584ab83f6 Sim Zhen Quan <<EMAIL>> 1742972734 +0800	commit: Reviewed
de36acf5350beb9cec97fbf5ea4bf13584ab83f6 88896b84e12120f68047de724ab56b5bac785cc0 Sim Zhen Quan <<EMAIL>> 1742972805 +0800	checkout: moving from migration-reward-punishment to exam-leftovers
88896b84e12120f68047de724ab56b5bac785cc0 dd860ed1de261120125a09c88b8c62fad87bfb77 Sim Zhen Quan <<EMAIL>> 1742972811 +0800	pull: Fast-forward
dd860ed1de261120125a09c88b8c62fad87bfb77 72356de7262cd072f127a2774ba60ac2488dd3c3 Sim Zhen Quan <<EMAIL>> 1742972834 +0800	merge origin/main: Merge made by the 'ort' strategy.
72356de7262cd072f127a2774ba60ac2488dd3c3 ae3947bc0e586f850f80e5768062ed4f54c6b5e7 Sim Zhen Quan <<EMAIL>> 1742976082 +0800	commit: Review WIP
ae3947bc0e586f850f80e5768062ed4f54c6b5e7 3b9b54534a31a407cf6483673ea47561c7fe0ba5 Sim Zhen Quan <<EMAIL>> 1742976128 +0800	checkout: moving from exam-leftovers to dev
3b9b54534a31a407cf6483673ea47561c7fe0ba5 bbd8d29c6da0155a99fb82244b878de4e39c9171 Sim Zhen Quan <<EMAIL>> 1742976133 +0800	merge exam-leftovers: Merge made by the 'ort' strategy.
bbd8d29c6da0155a99fb82244b878de4e39c9171 bbd8d29c6da0155a99fb82244b878de4e39c9171 Sim Zhen Quan <<EMAIL>> 1742976162 +0800	checkout: moving from dev to dev
bbd8d29c6da0155a99fb82244b878de4e39c9171 cc6bd07539a9ccd2123c8a7ada767209c7b235e8 Sim Zhen Quan <<EMAIL>> 1742983518 +0800	commit: Deployed to dev
cc6bd07539a9ccd2123c8a7ada767209c7b235e8 b5defef3c60b3cc43d8dbe2f95cd1413f018f304 Sim Zhen Quan <<EMAIL>> 1742983522 +0800	checkout: moving from dev to fix-get-class-attendance-bug
b5defef3c60b3cc43d8dbe2f95cd1413f018f304 b5defef3c60b3cc43d8dbe2f95cd1413f018f304 Sim Zhen Quan <<EMAIL>> 1742983525 +0800	reset: moving to HEAD
b5defef3c60b3cc43d8dbe2f95cd1413f018f304 888b00033cdc8516eeab07bbaf7da04b3a3a0d71 Sim Zhen Quan <<EMAIL>> 1742983637 +0800	commit: Bug fix
888b00033cdc8516eeab07bbaf7da04b3a3a0d71 e0553b818a3b75f6e9dede8be350d461fe8a2749 Sim Zhen Quan <<EMAIL>> 1742983783 +0800	merge origin/main: Merge made by the 'ort' strategy.
e0553b818a3b75f6e9dede8be350d461fe8a2749 ae3947bc0e586f850f80e5768062ed4f54c6b5e7 Sim Zhen Quan <<EMAIL>> 1742984627 +0800	checkout: moving from fix-get-class-attendance-bug to exam-leftovers
ae3947bc0e586f850f80e5768062ed4f54c6b5e7 a2a28ccce07d71a276d213e0a96987d36d7e2793 Sim Zhen Quan <<EMAIL>> 1742984632 +0800	merge origin/main: Merge made by the 'ort' strategy.
a2a28ccce07d71a276d213e0a96987d36d7e2793 a2a28ccce07d71a276d213e0a96987d36d7e2793 Sim Zhen Quan <<EMAIL>> 1742996371 +0800	reset: moving to HEAD
a2a28ccce07d71a276d213e0a96987d36d7e2793 cc6bd07539a9ccd2123c8a7ada767209c7b235e8 Sim Zhen Quan <<EMAIL>> 1742996373 +0800	checkout: moving from exam-leftovers to dev
cc6bd07539a9ccd2123c8a7ada767209c7b235e8 0c8650f5cf44bfcd681ba525e80445214a33e7ef Sim Zhen Quan <<EMAIL>> 1742996394 +0800	merge origin/main: Merge made by the 'ort' strategy.
0c8650f5cf44bfcd681ba525e80445214a33e7ef 430c99f3301d86ed4033559c6230fa847514b318 Sim Zhen Quan <<EMAIL>> ********** +0800	checkout: moving from dev to main
430c99f3301d86ed4033559c6230fa847514b318 4c5623fb7be96611042c2e9a3020e8f9751b2740 Sim Zhen Quan <<EMAIL>> ********** +0800	pull: Fast-forward
4c5623fb7be96611042c2e9a3020e8f9751b2740 2ac1bd95ce1898c3c11a4597945cbbfee0466eb7 Sim Zhen Quan <<EMAIL>> ********** +0800	checkout: moving from main to fix/accounting-bugs
2ac1bd95ce1898c3c11a4597945cbbfee0466eb7 2781d03486501d087f78a16d5f50051926d263a8 Sim Zhen Quan <<EMAIL>> ********** +0800	checkout: moving from fix/accounting-bugs to migrate-script-attendence
2781d03486501d087f78a16d5f50051926d263a8 2840309e28638c0c2728729e2377f1c7b2ac3e1a Sim Zhen Quan <<EMAIL>> ********** +0800	commit: Added report card queue
2840309e28638c0c2728729e2377f1c7b2ac3e1a bddc8a7a5730cc857618f76576c35b9739b57110 Sim Zhen Quan <<EMAIL>> ********** +0800	commit (merge): Merge origin/main
bddc8a7a5730cc857618f76576c35b9739b57110 f8c25b32031e3bc64cf81365946acbc90c6561a1 Sim Zhen Quan <<EMAIL>> ********** +0800	commit: Remove late status
f8c25b32031e3bc64cf81365946acbc90c6561a1 710a70473dbcf09d411d2d43672adf3a0f16fbf9 Sim Zhen Quan <<EMAIL>> ********** +0800	checkout: moving from migrate-script-attendence to feature/conduct-setting-migration
710a70473dbcf09d411d2d43672adf3a0f16fbf9 f301bc42d0fec7cf077a52d687ec9238a2c1baba Sim Zhen Quan <<EMAIL>> ********** +0800	commit (merge): Merge origin/main
f301bc42d0fec7cf077a52d687ec9238a2c1baba 72e4d06faab17cc054700b19fdbd76b1d1757f59 Sim Zhen Quan <<EMAIL>> ********** +0800	commit: Change conduct code
72e4d06faab17cc054700b19fdbd76b1d1757f59 9b58ea2efe29f79a9d314efc304d6c3f21778073 Sim Zhen Quan <<EMAIL>> 1743055274 +0800	commit: Reviewed
9b58ea2efe29f79a9d314efc304d6c3f21778073 f8c25b32031e3bc64cf81365946acbc90c6561a1 Sim Zhen Quan <<EMAIL>> 1743055316 +0800	checkout: moving from feature/conduct-setting-migration to migrate-script-attendence
f8c25b32031e3bc64cf81365946acbc90c6561a1 7b96bcd8394734cbdaad706266baf77f87be3bf6 Sim Zhen Quan <<EMAIL>> 1743055939 +0800	commit (merge): Merge origin/main
7b96bcd8394734cbdaad706266baf77f87be3bf6 28c8cd969dddeea1b335bc70ca92e12c3dfdb4f6 Sim Zhen Quan <<EMAIL>> 1743062154 +0800	commit: Reviewed
28c8cd969dddeea1b335bc70ca92e12c3dfdb4f6 0fca5ea266186bd2b4063e18eb1b5d4781894c8d Sim Zhen Quan <<EMAIL>> 1743068590 +0800	checkout: moving from migrate-script-attendence to qas
0fca5ea266186bd2b4063e18eb1b5d4781894c8d 06256fafdd1173b7078ee05be8cf7b08eee91002 Sim Zhen Quan <<EMAIL>> 1743068601 +0800	merge origin/main: Merge made by the 'ort' strategy.
06256fafdd1173b7078ee05be8cf7b08eee91002 64df3b6d18727060093551e92d5051fa1097e2d9 Sim Zhen Quan <<EMAIL>> 1743069877 +0800	commit: Deployed to qas
64df3b6d18727060093551e92d5051fa1097e2d9 4c5623fb7be96611042c2e9a3020e8f9751b2740 Sim Zhen Quan <<EMAIL>> 1743069905 +0800	checkout: moving from qas to main
4c5623fb7be96611042c2e9a3020e8f9751b2740 6122501c07bef394607b55ccf356bf0e78a52cc4 Sim Zhen Quan <<EMAIL>> 1743069910 +0800	pull: Fast-forward
6122501c07bef394607b55ccf356bf0e78a52cc4 b207bc9e50ef8b71be7038147d6d843d53acc80d Sim Zhen Quan <<EMAIL>> 1743070030 +0800	commit: Added posting in migration
b207bc9e50ef8b71be7038147d6d843d53acc80d 1a322d04123d307845730dff613f30a1ea9d66d2 Sim Zhen Quan <<EMAIL>> 1743070089 +0800	checkout: moving from main to attendance-enhancement-v2
1a322d04123d307845730dff613f30a1ea9d66d2 6d7cf2cb19aaefe92c0bcf599f5241f71a2ef08e Sim Zhen Quan <<EMAIL>> 1743070141 +0800	commit (merge): Merge origin/main
6d7cf2cb19aaefe92c0bcf599f5241f71a2ef08e 0756ad92e9c723415afc1cfdc652719eac729b8d Sim Zhen Quan <<EMAIL>> 1743073840 +0800	commit: Review WIP
0756ad92e9c723415afc1cfdc652719eac729b8d c333b0a4c006b75aef400b9d8cf1255c7338175e Sim Zhen Quan <<EMAIL>> 1743073863 +0800	checkout: moving from attendance-enhancement-v2 to migrate-script-timetable
c333b0a4c006b75aef400b9d8cf1255c7338175e b46aa10e59402bc90c8e151a7a8db9f40121b3b2 Sim Zhen Quan <<EMAIL>> 1743074085 +0800	commit (merge): Merge origin/main
b46aa10e59402bc90c8e151a7a8db9f40121b3b2 b207bc9e50ef8b71be7038147d6d843d53acc80d Sim Zhen Quan <<EMAIL>> 1743090104 +0800	checkout: moving from migrate-script-timetable to main
b207bc9e50ef8b71be7038147d6d843d53acc80d 7a0a3ee4360a3752b397e03f3b3c38e5c552aee9 Sim Zhen Quan <<EMAIL>> 1743091037 +0800	commit: Temporarily update hostel reward punishment order
7a0a3ee4360a3752b397e03f3b3c38e5c552aee9 a2a28ccce07d71a276d213e0a96987d36d7e2793 Sim Zhen Quan <<EMAIL>> 1743091052 +0800	checkout: moving from main to exam-leftovers
a2a28ccce07d71a276d213e0a96987d36d7e2793 a2a28ccce07d71a276d213e0a96987d36d7e2793 Sim Zhen Quan <<EMAIL>> 1743091075 +0800	checkout: moving from exam-leftovers to grading-framework-update-enhancements
a2a28ccce07d71a276d213e0a96987d36d7e2793 a2a28ccce07d71a276d213e0a96987d36d7e2793 Sim Zhen Quan <<EMAIL>> 1743092340 +0800	reset: moving to HEAD
a2a28ccce07d71a276d213e0a96987d36d7e2793 7a0a3ee4360a3752b397e03f3b3c38e5c552aee9 Sim Zhen Quan <<EMAIL>> 1743092340 +0800	checkout: moving from grading-framework-update-enhancements to main
7a0a3ee4360a3752b397e03f3b3c38e5c552aee9 9f7c5e8b8a4efbda6f9e3780204724df9ab9b997 Sim Zhen Quan <<EMAIL>> 1743093087 +0800	commit: Temporarily update reward punishment order
9f7c5e8b8a4efbda6f9e3780204724df9ab9b997 a2a28ccce07d71a276d213e0a96987d36d7e2793 Sim Zhen Quan <<EMAIL>> 1743093097 +0800	checkout: moving from main to grading-framework-update-enhancements
a2a28ccce07d71a276d213e0a96987d36d7e2793 137c69a9b0a683558749fd024f7ccb086138bd46 Sim Zhen Quan <<EMAIL>> 1743100641 +0800	commit: Added update grading framework behavior
137c69a9b0a683558749fd024f7ccb086138bd46 59fc9005f877359c46cde96367966f4f8787a396 Sim Zhen Quan <<EMAIL>> 1743127567 +0800	commit: Added TODOs
59fc9005f877359c46cde96367966f4f8787a396 b46aa10e59402bc90c8e151a7a8db9f40121b3b2 Sim Zhen Quan <<EMAIL>> 1743131892 +0800	checkout: moving from grading-framework-update-enhancements to migrate-script-timetable
b46aa10e59402bc90c8e151a7a8db9f40121b3b2 b46aa10e59402bc90c8e151a7a8db9f40121b3b2 Sim Zhen Quan <<EMAIL>> 1743131898 +0800	merge origin/main: updating HEAD
b46aa10e59402bc90c8e151a7a8db9f40121b3b2 f288f43992e182b162459cb94ffdd88e88725194 Sim Zhen Quan <<EMAIL>> 1743133587 +0800	merge origin/main: Merge made by the 'ort' strategy.
f288f43992e182b162459cb94ffdd88e88725194 902d375ac276e3c357e4ee6f55a1997d852af31f Sim Zhen Quan <<EMAIL>> 1743148532 +0800	commit: Reviewed
902d375ac276e3c357e4ee6f55a1997d852af31f 0820a7ca2a7b81d043387ade2cb8e17d7323e24b Sim Zhen Quan <<EMAIL>> 1743149475 +0800	checkout: moving from migrate-script-timetable to announcement-index-reduce-response-size
0820a7ca2a7b81d043387ade2cb8e17d7323e24b 4f7102a9d777270f6859662d0fb3c6cd8eced1ad Sim Zhen Quan <<EMAIL>> 1743149480 +0800	merge origin/main: Merge made by the 'ort' strategy.
4f7102a9d777270f6859662d0fb3c6cd8eced1ad 811a2d22661d6dd9a74eebcb169944bffcad73c2 Sim Zhen Quan <<EMAIL>> 1743150802 +0800	commit: Fixed some test case
811a2d22661d6dd9a74eebcb169944bffcad73c2 9f7c5e8b8a4efbda6f9e3780204724df9ab9b997 Sim Zhen Quan <<EMAIL>> 1743488538 +0800	checkout: moving from announcement-index-reduce-response-size to main
9f7c5e8b8a4efbda6f9e3780204724df9ab9b997 a44125b11c443cb226dbc4db2954f4f85c25bdc0 Sim Zhen Quan <<EMAIL>> 1743488546 +0800	pull: Fast-forward
a44125b11c443cb226dbc4db2954f4f85c25bdc0 a44125b11c443cb226dbc4db2954f4f85c25bdc0 Sim Zhen Quan <<EMAIL>> 1743488560 +0800	reset: moving to HEAD
a44125b11c443cb226dbc4db2954f4f85c25bdc0 f1d10c9f0cd030e5e40d8313b5c13e3d6e525964 Sim Zhen Quan <<EMAIL>> 1743488561 +0800	checkout: moving from main to migrate-script-period_attendances
f1d10c9f0cd030e5e40d8313b5c13e3d6e525964 f1d10c9f0cd030e5e40d8313b5c13e3d6e525964 Sim Zhen Quan <<EMAIL>> 1743488576 +0800	reset: moving to HEAD
f1d10c9f0cd030e5e40d8313b5c13e3d6e525964 8ec413c80b3f95ccc2d6118a0f128b7e8107479b Sim Zhen Quan <<EMAIL>> 1743488668 +0800	commit (merge): Merge origin/main
8ec413c80b3f95ccc2d6118a0f128b7e8107479b 0733ad4e7901275a56dbc232924d8ac8f381af02 Sim Zhen Quan <<EMAIL>> 1743488868 +0800	commit: Bug fixes
0733ad4e7901275a56dbc232924d8ac8f381af02 60f5f2fdfff04443e087b3e0db99b8e522a4ac3b Sim Zhen Quan <<EMAIL>> 1743519958 +0800	commit: Reviewed
60f5f2fdfff04443e087b3e0db99b8e522a4ac3b a44125b11c443cb226dbc4db2954f4f85c25bdc0 Sim Zhen Quan <<EMAIL>> 1743520045 +0800	checkout: moving from migrate-script-period_attendances to main
a44125b11c443cb226dbc4db2954f4f85c25bdc0 64bf2da30b0fec631722b5da4aba4ed3ad72989a Sim Zhen Quan <<EMAIL>> 1743520053 +0800	pull: Fast-forward
64bf2da30b0fec631722b5da4aba4ed3ad72989a 48ceb328926fd71a8c406012a2cccb4b4322779f Sim Zhen Quan <<EMAIL>> 1743520814 +0800	commit: Deployed to PRD
48ceb328926fd71a8c406012a2cccb4b4322779f 0756ad92e9c723415afc1cfdc652719eac729b8d Sim Zhen Quan <<EMAIL>> 1743520927 +0800	checkout: moving from main to attendance-enhancement-v2
0756ad92e9c723415afc1cfdc652719eac729b8d e6e145f4a71107bc8bea0b72b31f4f2ca461c269 Sim Zhen Quan <<EMAIL>> 1743520937 +0800	merge origin/main: Merge made by the 'ort' strategy.
e6e145f4a71107bc8bea0b72b31f4f2ca461c269 78dfa216994fbf3edefe02c8cfb644315b3540d3 Sim Zhen Quan <<EMAIL>> 1743529510 +0800	commit: Reviewed
78dfa216994fbf3edefe02c8cfb644315b3540d3 0c8650f5cf44bfcd681ba525e80445214a33e7ef Sim Zhen Quan <<EMAIL>> ********** +0800	checkout: moving from attendance-enhancement-v2 to dev
0c8650f5cf44bfcd681ba525e80445214a33e7ef 5a9fa996baa07ef859326166d87a42c00b6149b1 Sim Zhen Quan <<EMAIL>> ********** +0800	commit (merge): Merge origin/main
5a9fa996baa07ef859326166d87a42c00b6149b1 2ac1bd95ce1898c3c11a4597945cbbfee0466eb7 Sim Zhen Quan <<EMAIL>> ********** +0800	checkout: moving from dev to fix/accounting-bugs
2ac1bd95ce1898c3c11a4597945cbbfee0466eb7 1cd8e32af77dcb69b29b257d293d7086e7f20c41 Sim Zhen Quan <<EMAIL>> ********** +0800	merge origin/main: Merge made by the 'ort' strategy.
1cd8e32af77dcb69b29b257d293d7086e7f20c41 5a9fa996baa07ef859326166d87a42c00b6149b1 Sim Zhen Quan <<EMAIL>> ********** +0800	checkout: moving from fix/accounting-bugs to dev
5a9fa996baa07ef859326166d87a42c00b6149b1 915ffca17506332c96d76482815d3839ca736a06 Sim Zhen Quan <<EMAIL>> ********** +0800	merge origin/main: Merge made by the 'ort' strategy.
915ffca17506332c96d76482815d3839ca736a06 fe411aa37108d427bf91ea6223330264a5287e35 Sim Zhen Quan <<EMAIL>> ********** +0800	commit: Deployed to DEV
fe411aa37108d427bf91ea6223330264a5287e35 ef3d1d7f5124751fb3682aa06d873d04fa223e6f Sim Zhen Quan <<EMAIL>> ********** +0800	checkout: moving from dev to feature/promotion-mark-CRUD
ef3d1d7f5124751fb3682aa06d873d04fa223e6f 63148ac69c3f6985a1ce56cb491c6346c6b116de Sim Zhen Quan <<EMAIL>> ********** +0800	commit (merge): Merge origin/main
63148ac69c3f6985a1ce56cb491c6346c6b116de 63148ac69c3f6985a1ce56cb491c6346c6b116de Sim Zhen Quan <<EMAIL>> ********** +0800	checkout: moving from feature/promotion-mark-CRUD to feature/promotion-mark-CRUD
63148ac69c3f6985a1ce56cb491c6346c6b116de 63148ac69c3f6985a1ce56cb491c6346c6b116de Sim Zhen Quan <<EMAIL>> 1743561005 +0800	checkout: moving from feature/promotion-mark-CRUD to feature/promotion-mark-CRUD
63148ac69c3f6985a1ce56cb491c6346c6b116de faf096bf0be6aebe9e576a04bf2b49647f9a73e2 Sim Zhen Quan <<EMAIL>> 1743565464 +0800	checkout: moving from feature/promotion-mark-CRUD to SCRUM-256-Export-For-AutoCount
faf096bf0be6aebe9e576a04bf2b49647f9a73e2 85d4d73543d96d5b3cd372b58f1203a465ecb0cf Sim Zhen Quan <<EMAIL>> 1743565967 +0800	commit (merge): Merge origin/main
85d4d73543d96d5b3cd372b58f1203a465ecb0cf 85d4d73543d96d5b3cd372b58f1203a465ecb0cf Sim Zhen Quan <<EMAIL>> 1743609941 +0800	reset: moving to HEAD
85d4d73543d96d5b3cd372b58f1203a465ecb0cf 64df3b6d18727060093551e92d5051fa1097e2d9 Sim Zhen Quan <<EMAIL>> 1743609944 +0800	checkout: moving from SCRUM-256-Export-For-AutoCount to qas
64df3b6d18727060093551e92d5051fa1097e2d9 6a87aec64efe2453533311eefc21d39f8ac59cf0 Sim Zhen Quan <<EMAIL>> 1743609949 +0800	merge origin/main: Merge made by the 'ort' strategy.
6a87aec64efe2453533311eefc21d39f8ac59cf0 9c94108ee5a3a9c4cde63c8808009a42f8cea865 Sim Zhen Quan <<EMAIL>> 1743610082 +0800	commit: Deployed to qas
9c94108ee5a3a9c4cde63c8808009a42f8cea865 fe411aa37108d427bf91ea6223330264a5287e35 Sim Zhen Quan <<EMAIL>> 1743643191 +0800	checkout: moving from qas to dev
fe411aa37108d427bf91ea6223330264a5287e35 85d4d73543d96d5b3cd372b58f1203a465ecb0cf Sim Zhen Quan <<EMAIL>> 1743643456 +0800	checkout: moving from dev to SCRUM-256-Export-For-AutoCount
85d4d73543d96d5b3cd372b58f1203a465ecb0cf aa79db3e0977dfc87ad886354efda48f5dd717ae Sim Zhen Quan <<EMAIL>> 1743643635 +0800	merge origin/main: Merge made by the 'ort' strategy.
aa79db3e0977dfc87ad886354efda48f5dd717ae a9e12ce34d7323f3c56a68095c6190907e8e1c1e Sim Zhen Quan <<EMAIL>> 1743645826 +0800	commit: Reviewed
a9e12ce34d7323f3c56a68095c6190907e8e1c1e fe411aa37108d427bf91ea6223330264a5287e35 Sim Zhen Quan <<EMAIL>> 1743646111 +0800	checkout: moving from SCRUM-256-Export-For-AutoCount to dev
fe411aa37108d427bf91ea6223330264a5287e35 f3e21203d046a2943b24363018c44143386f9fae Sim Zhen Quan <<EMAIL>> 1743646116 +0800	merge origin/main: Merge made by the 'ort' strategy.
f3e21203d046a2943b24363018c44143386f9fae 7974add18d0988cf20949852621cf3b1abf3db98 Sim Zhen Quan <<EMAIL>> 1743647091 +0800	commit: Deployed to qas
7974add18d0988cf20949852621cf3b1abf3db98 fbb539e6f0d42a1b6507a672f5d939ed2074eda8 Sim Zhen Quan <<EMAIL>> 1743647102 +0800	checkout: moving from dev to fix/hostel-uat-feedback
fbb539e6f0d42a1b6507a672f5d939ed2074eda8 7a1e9a9634002503ab5aa57815aa691faa66316d Sim Zhen Quan <<EMAIL>> 1743649404 +0800	commit (merge): Merge origin/main
7a1e9a9634002503ab5aa57815aa691faa66316d b63b33bb135b2029609d19cfc1b3f6654fc5eba9 Sim Zhen Quan <<EMAIL>> 1743653742 +0800	commit: Reviewed
b63b33bb135b2029609d19cfc1b3f6654fc5eba9 7974add18d0988cf20949852621cf3b1abf3db98 Sim Zhen Quan <<EMAIL>> 1743653956 +0800	checkout: moving from fix/hostel-uat-feedback to dev
7974add18d0988cf20949852621cf3b1abf3db98 0bb74c2c0532fed403f9feaa148774434afdefdf Sim Zhen Quan <<EMAIL>> 1743660913 +0800	merge origin/main: Merge made by the 'ort' strategy.
0bb74c2c0532fed403f9feaa148774434afdefdf 2fba8b2b3fbfa0d7e7592ba55e3fa00be83525d1 Sim Zhen Quan <<EMAIL>> 1743661975 +0800	commit: Deployed to dev
2fba8b2b3fbfa0d7e7592ba55e3fa00be83525d1 b7ca76256d12f53cf0e6de1ad8751ddc232c0640 Sim Zhen Quan <<EMAIL>> 1743662099 +0800	checkout: moving from dev to migrate-script-leadership
b7ca76256d12f53cf0e6de1ad8751ddc232c0640 a80298275028d2146c1869a6626ef8389f09b937 Sim Zhen Quan <<EMAIL>> 1743662107 +0800	merge origin/main: Merge made by the 'ort' strategy.
a80298275028d2146c1869a6626ef8389f09b937 1a8e1a3554fd8c29b2e6628f6fc284d071042628 Sim Zhen Quan <<EMAIL>> 1743670137 +0800	commit: Reviewed
1a8e1a3554fd8c29b2e6628f6fc284d071042628 2fba8b2b3fbfa0d7e7592ba55e3fa00be83525d1 Sim Zhen Quan <<EMAIL>> 1743670166 +0800	checkout: moving from migrate-script-leadership to dev
2fba8b2b3fbfa0d7e7592ba55e3fa00be83525d1 72421c40ac864b308d0b56ff6b536e0fdbbcdcb2 Sim Zhen Quan <<EMAIL>> 1743670175 +0800	merge origin/main: Merge made by the 'ort' strategy.
72421c40ac864b308d0b56ff6b536e0fdbbcdcb2 11c71f912ed874bb5bb2990bf0ff7e27d81fe719 Sim Zhen Quan <<EMAIL>> 1743670551 +0800	commit: Deployed to dev
11c71f912ed874bb5bb2990bf0ff7e27d81fe719 11c71f912ed874bb5bb2990bf0ff7e27d81fe719 Sim Zhen Quan <<EMAIL>> 1743671374 +0800	reset: moving to HEAD
11c71f912ed874bb5bb2990bf0ff7e27d81fe719 48ceb328926fd71a8c406012a2cccb4b4322779f Sim Zhen Quan <<EMAIL>> 1743671377 +0800	checkout: moving from dev to main
48ceb328926fd71a8c406012a2cccb4b4322779f 9319430047f4d24df8d1783658fc189438c7c2eb Sim Zhen Quan <<EMAIL>> 1743671382 +0800	pull: Fast-forward
9319430047f4d24df8d1783658fc189438c7c2eb 41d6c3d5a9d1ff4611caea78bb136a813ba9e241 Sim Zhen Quan <<EMAIL>> 1743672617 +0800	commit: Added extra validation handling
41d6c3d5a9d1ff4611caea78bb136a813ba9e241 04eb3f1a2b71e6793e52c153a8672569ab1d1270 Sim Zhen Quan <<EMAIL>> 1743672651 +0800	checkout: moving from main to SCRUM-391-fix-unpaid-item-report-locale-for-product-name
04eb3f1a2b71e6793e52c153a8672569ab1d1270 82c5045e83fe727bcdc953752fd27c763008b54a Sim Zhen Quan <<EMAIL>> 1743672784 +0800	commit (merge): Merge origin/main
82c5045e83fe727bcdc953752fd27c763008b54a 41d6c3d5a9d1ff4611caea78bb136a813ba9e241 Sim Zhen Quan <<EMAIL>> 1743688645 +0800	checkout: moving from SCRUM-391-fix-unpaid-item-report-locale-for-product-name to main
41d6c3d5a9d1ff4611caea78bb136a813ba9e241 0ad7e44411d91dbb2ec01d083fec13bf755722a9 Sim Zhen Quan <<EMAIL>> 1743688686 +0800	commit: Fix student classes migration
0ad7e44411d91dbb2ec01d083fec13bf755722a9 82c5045e83fe727bcdc953752fd27c763008b54a Sim Zhen Quan <<EMAIL>> 1743693648 +0800	checkout: moving from main to SCRUM-391-fix-unpaid-item-report-locale-for-product-name
82c5045e83fe727bcdc953752fd27c763008b54a eab8d815774d50f962a7707d68cebd63d23b3e9e Sim Zhen Quan <<EMAIL>> 1743693701 +0800	merge origin/main: Merge made by the 'ort' strategy.
eab8d815774d50f962a7707d68cebd63d23b3e9e 33ac93b29c611355cd2a2cf963048f75ac9badec Sim Zhen Quan <<EMAIL>> 1743694480 +0800	checkout: moving from SCRUM-391-fix-unpaid-item-report-locale-for-product-name to feature/billing-document-report
33ac93b29c611355cd2a2cf963048f75ac9badec 1fdfba980ab816e9a052fdfd8131f920a881021d Sim Zhen Quan <<EMAIL>> 1743694485 +0800	merge origin/main: Merge made by the 'ort' strategy.
1fdfba980ab816e9a052fdfd8131f920a881021d cfdb2b623e25941927664eecf1bc4ae54bc69680 Sim Zhen Quan <<EMAIL>> 1743694902 +0800	commit: Clean up imports
cfdb2b623e25941927664eecf1bc4ae54bc69680 11c71f912ed874bb5bb2990bf0ff7e27d81fe719 Sim Zhen Quan <<EMAIL>> 1743694954 +0800	checkout: moving from feature/billing-document-report to dev
11c71f912ed874bb5bb2990bf0ff7e27d81fe719 ea3c13ff143d423fd8c5043ad70b3628218f99b9 Sim Zhen Quan <<EMAIL>> 1743694963 +0800	merge origin/main: Merge made by the 'ort' strategy.
ea3c13ff143d423fd8c5043ad70b3628218f99b9 218eecbd5e430a5703eee8e6721e2e194523b0ae Sim Zhen Quan <<EMAIL>> 1743695127 +0800	commit: Deployed to dev
218eecbd5e430a5703eee8e6721e2e194523b0ae 5578ecf8249469b5f479887eec53484111fe1337 Sim Zhen Quan <<EMAIL>> 1743695138 +0800	checkout: moving from dev to feature/conduct-mark-enhancement
5578ecf8249469b5f479887eec53484111fe1337 6312b8932760fc5cfc1619117c5862cc940b6987 Sim Zhen Quan <<EMAIL>> 1743695144 +0800	merge origin/main: Merge made by the 'ort' strategy.
6312b8932760fc5cfc1619117c5862cc940b6987 6312b8932760fc5cfc1619117c5862cc940b6987 Sim Zhen Quan <<EMAIL>> 1743695260 +0800	checkout: moving from feature/conduct-mark-enhancement to feature/conduct-mark-enhancement
6312b8932760fc5cfc1619117c5862cc940b6987 ad9d71d189fbdaacdd9ad4dea52b457bb145816c Sim Zhen Quan <<EMAIL>> 1743696901 +0800	commit: Reviewed
ad9d71d189fbdaacdd9ad4dea52b457bb145816c 218eecbd5e430a5703eee8e6721e2e194523b0ae Sim Zhen Quan <<EMAIL>> 1743697126 +0800	checkout: moving from feature/conduct-mark-enhancement to dev
218eecbd5e430a5703eee8e6721e2e194523b0ae 9caae034d10d2ff8673d1c82c7cd1dfe1f3d1196 Sim Zhen Quan <<EMAIL>> 1743697132 +0800	merge origin/main: Merge made by the 'ort' strategy.
9caae034d10d2ff8673d1c82c7cd1dfe1f3d1196 e39c74efea0bd94ea797c9ad9b9859599b37afbb Sim Zhen Quan <<EMAIL>> 1743697474 +0800	checkout: moving from dev to academy-student-analysis-report
e39c74efea0bd94ea797c9ad9b9859599b37afbb 30da17d76fae6ef08726f7882d3374d14470d0de Sim Zhen Quan <<EMAIL>> 1743698305 +0800	commit (merge): Merge origin/main
30da17d76fae6ef08726f7882d3374d14470d0de 1a889c792bc9f7ed59b5312753b701fcaa2cc36a Sim Zhen Quan <<EMAIL>> 1743698315 +0800	checkout: moving from academy-student-analysis-report to leave-application-enhancements
1a889c792bc9f7ed59b5312753b701fcaa2cc36a 8471ecb62421020f55fef07f3cd94d6c000b8246 Sim Zhen Quan <<EMAIL>> 1743698322 +0800	pull: Fast-forward
8471ecb62421020f55fef07f3cd94d6c000b8246 8f4a6e1b14bd4c016a39de9b2098159f40badd31 Sim Zhen Quan <<EMAIL>> 1743698459 +0800	commit (merge): Merge origin/main
8f4a6e1b14bd4c016a39de9b2098159f40badd31 a2a28ccce07d71a276d213e0a96987d36d7e2793 Sim Zhen Quan <<EMAIL>> 1743761689 +0800	checkout: moving from leave-application-enhancements to exam-leftovers
a2a28ccce07d71a276d213e0a96987d36d7e2793 e7b65c028684ee443b61d0ae0a79a487fe4dc345 Sim Zhen Quan <<EMAIL>> 1743862624 +0800	pull: Fast-forward
e7b65c028684ee443b61d0ae0a79a487fe4dc345 0ad7e44411d91dbb2ec01d083fec13bf755722a9 Sim Zhen Quan <<EMAIL>> 1743862643 +0800	checkout: moving from exam-leftovers to main
0ad7e44411d91dbb2ec01d083fec13bf755722a9 ad4d6fae4d985515fbff52e7bfe51052351c087b Sim Zhen Quan <<EMAIL>> 1743862648 +0800	pull: Fast-forward
ad4d6fae4d985515fbff52e7bfe51052351c087b ad4d6fae4d985515fbff52e7bfe51052351c087b Sim Zhen Quan <<EMAIL>> 1743864312 +0800	checkout: moving from main to add-employee-timetables
ad4d6fae4d985515fbff52e7bfe51052351c087b 0629105ffe1a193f66a4e003e0d2aaa4df817706 Sim Zhen Quan <<EMAIL>> 1743872500 +0800	commit: Added employee timetable
0629105ffe1a193f66a4e003e0d2aaa4df817706 b619075c74750c367cb04eae83d06363b7f6b296 Sim Zhen Quan <<EMAIL>> 1743949668 +0800	checkout: moving from add-employee-timetables to fix/migrate-script-timetable
b619075c74750c367cb04eae83d06363b7f6b296 41b98191e0290de2a8bc99a6dac0a25dafc00531 Sim Zhen Quan <<EMAIL>> 1743949675 +0800	merge origin/main: Merge made by the 'ort' strategy.
41b98191e0290de2a8bc99a6dac0a25dafc00531 0de8167b53e2410be7a971e0a0aa24fdb27fd358 Sim Zhen Quan <<EMAIL>> 1743952364 +0800	commit: Reviewed
0de8167b53e2410be7a971e0a0aa24fdb27fd358 6058cc91f9b1cdd40b27e1b8202cabc290b188d5 Sim Zhen Quan <<EMAIL>> 1743952419 +0800	checkout: moving from fix/migrate-script-timetable to fix/migrate-script-hostel
6058cc91f9b1cdd40b27e1b8202cabc290b188d5 0a9a3bbf972a1b6f2751c2e1ffb203ae9d4330e6 Sim Zhen Quan <<EMAIL>> 1743952424 +0800	merge origin/main: Merge made by the 'ort' strategy.
0a9a3bbf972a1b6f2751c2e1ffb203ae9d4330e6 f949b2dea77697438b586be632b9e53fd8e67744 Sim Zhen Quan <<EMAIL>> 1743953645 +0800	commit: Reviewed
f949b2dea77697438b586be632b9e53fd8e67744 454c127c078459e48003659985ab189bf0d6ef76 Sim Zhen Quan <<EMAIL>> 1743954008 +0800	checkout: moving from fix/migrate-script-hostel to fix-migrate-script-class-subject-contractor
454c127c078459e48003659985ab189bf0d6ef76 54b100f00b807cc63a0dc6679153b3c111891756 Sim Zhen Quan <<EMAIL>> 1743954015 +0800	merge origin/main: Merge made by the 'ort' strategy.
54b100f00b807cc63a0dc6679153b3c111891756 132e861a524b4661380644b1e7543b0ac9e214d7 Sim Zhen Quan <<EMAIL>> 1743956457 +0800	commit: Reviewed
132e861a524b4661380644b1e7543b0ac9e214d7 ad4d6fae4d985515fbff52e7bfe51052351c087b Sim Zhen Quan <<EMAIL>> 1743956838 +0800	checkout: moving from fix-migrate-script-class-subject-contractor to main
ad4d6fae4d985515fbff52e7bfe51052351c087b 44d3715712a7d0b6d52b8cc4092959b8ef6ea79b Sim Zhen Quan <<EMAIL>> 1743956843 +0800	pull: Fast-forward
44d3715712a7d0b6d52b8cc4092959b8ef6ea79b 53cc56ed0c3775776460e6a57ed2ecd2ffcae188 Sim Zhen Quan <<EMAIL>> 1743990275 +0800	commit: Change invoice to receipt
53cc56ed0c3775776460e6a57ed2ecd2ffcae188 9caae034d10d2ff8673d1c82c7cd1dfe1f3d1196 Sim Zhen Quan <<EMAIL>> 1743990303 +0800	checkout: moving from main to dev
9caae034d10d2ff8673d1c82c7cd1dfe1f3d1196 12242d2a2732bcad79d40030bcdcd887371a2d1a Sim Zhen Quan <<EMAIL>> 1743990325 +0800	merge origin/main: Merge made by the 'ort' strategy.
12242d2a2732bcad79d40030bcdcd887371a2d1a a1b665b62aaaa56f355768012955581d989618ac Sim Zhen Quan <<EMAIL>> 1743990461 +0800	commit: Deployed to qas
a1b665b62aaaa56f355768012955581d989618ac 9c94108ee5a3a9c4cde63c8808009a42f8cea865 Sim Zhen Quan <<EMAIL>> 1743990480 +0800	checkout: moving from dev to qas
9c94108ee5a3a9c4cde63c8808009a42f8cea865 78ef23d2356b80fc814b000edd556d787f9bb6a5 Sim Zhen Quan <<EMAIL>> 1743990486 +0800	merge origin/main: Merge made by the 'ort' strategy.
78ef23d2356b80fc814b000edd556d787f9bb6a5 d957717e90b43b5026ffbc7d5dd4da3aeebfff47 Sim Zhen Quan <<EMAIL>> 1743995614 +0800	commit: Deployed to qas
d957717e90b43b5026ffbc7d5dd4da3aeebfff47 53cc56ed0c3775776460e6a57ed2ecd2ffcae188 Sim Zhen Quan <<EMAIL>> 1743997000 +0800	checkout: moving from qas to main
53cc56ed0c3775776460e6a57ed2ecd2ffcae188 0970f91cd05cdee71a26416bdd4dc0e7e1cb0554 Sim Zhen Quan <<EMAIL>> 1744000217 +0800	commit: Bug fixes
0970f91cd05cdee71a26416bdd4dc0e7e1cb0554 a1b665b62aaaa56f355768012955581d989618ac Sim Zhen Quan <<EMAIL>> 1744000240 +0800	checkout: moving from main to dev
a1b665b62aaaa56f355768012955581d989618ac de8422459ae83d08d7f26371c3c2310c986ce0f2 Sim Zhen Quan <<EMAIL>> 1744000246 +0800	merge origin/main: Merge made by the 'ort' strategy.
de8422459ae83d08d7f26371c3c2310c986ce0f2 b49cd9560b2e4a3b8e2fa7ead09674cd7435bc34 Sim Zhen Quan <<EMAIL>> 1744008085 +0800	commit: Deployed to dev
b49cd9560b2e4a3b8e2fa7ead09674cd7435bc34 e7b65c028684ee443b61d0ae0a79a487fe4dc345 Sim Zhen Quan <<EMAIL>> 1744009115 +0800	checkout: moving from dev to exam-leftovers
e7b65c028684ee443b61d0ae0a79a487fe4dc345 b49cd9560b2e4a3b8e2fa7ead09674cd7435bc34 Sim Zhen Quan <<EMAIL>> 1744011510 +0800	checkout: moving from exam-leftovers to dev
b49cd9560b2e4a3b8e2fa7ead09674cd7435bc34 e7b65c028684ee443b61d0ae0a79a487fe4dc345 Sim Zhen Quan <<EMAIL>> 1744013471 +0800	checkout: moving from dev to exam-leftovers
e7b65c028684ee443b61d0ae0a79a487fe4dc345 0970f91cd05cdee71a26416bdd4dc0e7e1cb0554 Sim Zhen Quan <<EMAIL>> 1744016422 +0800	checkout: moving from exam-leftovers to main
0970f91cd05cdee71a26416bdd4dc0e7e1cb0554 0970f91cd05cdee71a26416bdd4dc0e7e1cb0554 Sim Zhen Quan <<EMAIL>> 1744016447 +0800	checkout: moving from main to migrate-leave-application
0970f91cd05cdee71a26416bdd4dc0e7e1cb0554 ef8ac846735f625e6342856855e83d4a06062aff Sim Zhen Quan <<EMAIL>> 1744018667 +0800	commit: WIP
ef8ac846735f625e6342856855e83d4a06062aff b49cd9560b2e4a3b8e2fa7ead09674cd7435bc34 Sim Zhen Quan <<EMAIL>> 1744018693 +0800	checkout: moving from migrate-leave-application to dev
b49cd9560b2e4a3b8e2fa7ead09674cd7435bc34 7652012e7420db3fde0aedf5c9636e381fd0dc29 Sim Zhen Quan <<EMAIL>> 1744018703 +0800	merge origin/main: Merge made by the 'ort' strategy.
7652012e7420db3fde0aedf5c9636e381fd0dc29 ef8ac846735f625e6342856855e83d4a06062aff Sim Zhen Quan <<EMAIL>> 1744019128 +0800	checkout: moving from dev to migrate-leave-application
ef8ac846735f625e6342856855e83d4a06062aff 2e16e928abafa9123455cb18d7a2470385424dad Sim Zhen Quan <<EMAIL>> 1744081143 +0800	commit: WIP
2e16e928abafa9123455cb18d7a2470385424dad d957717e90b43b5026ffbc7d5dd4da3aeebfff47 Sim Zhen Quan <<EMAIL>> 1744081148 +0800	checkout: moving from migrate-leave-application to qas
d957717e90b43b5026ffbc7d5dd4da3aeebfff47 0970f91cd05cdee71a26416bdd4dc0e7e1cb0554 Sim Zhen Quan <<EMAIL>> 1744081170 +0800	checkout: moving from qas to main
0970f91cd05cdee71a26416bdd4dc0e7e1cb0554 499d565abe53914cf0beb84d7149947d1690fd27 Sim Zhen Quan <<EMAIL>> 1744081264 +0800	pull: Fast-forward
499d565abe53914cf0beb84d7149947d1690fd27 2e16e928abafa9123455cb18d7a2470385424dad Sim Zhen Quan <<EMAIL>> 1744082520 +0800	checkout: moving from main to migrate-leave-application
2e16e928abafa9123455cb18d7a2470385424dad 328ed68848ff1b00b5c371c1eac0788edf8fa6fc Sim Zhen Quan <<EMAIL>> 1744101405 +0800	commit: Completed Migration
328ed68848ff1b00b5c371c1eac0788edf8fa6fc 321ef321fce1982691bd5901bdcce887280fafd3 Sim Zhen Quan <<EMAIL>> 1744101831 +0800	commit: Added TODOs
321ef321fce1982691bd5901bdcce887280fafd3 7652012e7420db3fde0aedf5c9636e381fd0dc29 Sim Zhen Quan <<EMAIL>> 1744101991 +0800	checkout: moving from migrate-leave-application to dev
7652012e7420db3fde0aedf5c9636e381fd0dc29 9c0fc36b58a9d37c861674e9cf2dab71974923a7 Sim Zhen Quan <<EMAIL>> 1744101997 +0800	merge origin/main: Merge made by the 'ort' strategy.
9c0fc36b58a9d37c861674e9cf2dab71974923a7 a089d8505c33afcd6a184d500ad7be14884f638a Sim Zhen Quan <<EMAIL>> 1744102139 +0800	commit: Deployed to dev
a089d8505c33afcd6a184d500ad7be14884f638a d957717e90b43b5026ffbc7d5dd4da3aeebfff47 Sim Zhen Quan <<EMAIL>> 1744102155 +0800	checkout: moving from dev to qas
d957717e90b43b5026ffbc7d5dd4da3aeebfff47 d4542b821cb1efec082e2e546cbca88f2bc0e768 Sim Zhen Quan <<EMAIL>> 1744102159 +0800	merge origin/main: Merge made by the 'ort' strategy.
d4542b821cb1efec082e2e546cbca88f2bc0e768 319f3abd80fa64453483e44b5c34ebeef4859264 Sim Zhen Quan <<EMAIL>> 1744107037 +0800	commit: Deployed to qas
319f3abd80fa64453483e44b5c34ebeef4859264 5bc58f9a530814b1e8716367c22a563adc144eea Sim Zhen Quan <<EMAIL>> 1744107066 +0800	checkout: moving from qas to calendar
5bc58f9a530814b1e8716367c22a563adc144eea 27a4dc9449008a6295b655324de88612beb1d7ff Sim Zhen Quan <<EMAIL>> 1744107073 +0800	pull: Fast-forward
27a4dc9449008a6295b655324de88612beb1d7ff 726bcc0fdc8f7d643c6dc259a2cad03f5e431187 Sim Zhen Quan <<EMAIL>> 1744107251 +0800	merge origin/main: Merge made by the 'ort' strategy.
726bcc0fdc8f7d643c6dc259a2cad03f5e431187 44e15271abe0cab0ce550929331bddd1f4ac8ad2 Sim Zhen Quan <<EMAIL>> 1744109335 +0800	pull: Fast-forward
44e15271abe0cab0ce550929331bddd1f4ac8ad2 499d565abe53914cf0beb84d7149947d1690fd27 Sim Zhen Quan <<EMAIL>> 1744171392 +0800	checkout: moving from calendar to main
499d565abe53914cf0beb84d7149947d1690fd27 793032f9240efbbdb6691aa07ea5eabb618c034b Sim Zhen Quan <<EMAIL>> 1744171402 +0800	pull: Fast-forward
793032f9240efbbdb6691aa07ea5eabb618c034b 44e15271abe0cab0ce550929331bddd1f4ac8ad2 Sim Zhen Quan <<EMAIL>> 1744171408 +0800	checkout: moving from main to calendar
44e15271abe0cab0ce550929331bddd1f4ac8ad2 793032f9240efbbdb6691aa07ea5eabb618c034b Sim Zhen Quan <<EMAIL>> 1744257803 +0800	checkout: moving from calendar to main
793032f9240efbbdb6691aa07ea5eabb618c034b b0cdfc67dd26d8ce10e3aa60d6b02ea652af4b42 Sim Zhen Quan <<EMAIL>> 1744265079 +0800	checkout: moving from main to feature/english-class-migration
b0cdfc67dd26d8ce10e3aa60d6b02ea652af4b42 cfcf736d3e762f1bb646f16018458b438a902cb4 Sim Zhen Quan <<EMAIL>> 1744267301 +0800	commit: Reviewed
cfcf736d3e762f1bb646f16018458b438a902cb4 a089d8505c33afcd6a184d500ad7be14884f638a Sim Zhen Quan <<EMAIL>> 1744267348 +0800	checkout: moving from feature/english-class-migration to dev
a089d8505c33afcd6a184d500ad7be14884f638a 319f3abd80fa64453483e44b5c34ebeef4859264 Sim Zhen Quan <<EMAIL>> 1744267385 +0800	checkout: moving from dev to qas
319f3abd80fa64453483e44b5c34ebeef4859264 089d4898b0deff4b93aaa969913d3adab81d8838 Sim Zhen Quan <<EMAIL>> 1744267391 +0800	merge origin/main: Merge made by the 'ort' strategy.
089d4898b0deff4b93aaa969913d3adab81d8838 a49509a0a3bd97984e60886f54a33bd304a5ad01 Sim Zhen Quan <<EMAIL>> 1744267546 +0800	commit: Deployed to qas
a49509a0a3bd97984e60886f54a33bd304a5ad01 aaa8dc64560784ce7067a9e481b68bb755824ae1 Sim Zhen Quan <<EMAIL>> 1744268419 +0800	checkout: moving from qas to exam-elective-subjects
aaa8dc64560784ce7067a9e481b68bb755824ae1 aaa8dc64560784ce7067a9e481b68bb755824ae1 Sim Zhen Quan <<EMAIL>> 1744268428 +0800	checkout: moving from exam-elective-subjects to exam-elective-subjects
aaa8dc64560784ce7067a9e481b68bb755824ae1 dab450982fe056a51574fdb31fb8d3f1d1bcc604 Sim Zhen Quan <<EMAIL>> 1744268445 +0800	merge origin/main: Merge made by the 'ort' strategy.
dab450982fe056a51574fdb31fb8d3f1d1bcc604 f8312abacf62c708093e3100423c300477e3addc Sim Zhen Quan <<EMAIL>> 1744272877 +0800	pull: Fast-forward
f8312abacf62c708093e3100423c300477e3addc 17699f0dc3c74fe0fc9f39552719dbd15b424e10 Sim Zhen Quan <<EMAIL>> 1744272880 +0800	commit: Partially reviewed, gonna push to main and test from FE
17699f0dc3c74fe0fc9f39552719dbd15b424e10 63d4a65eb1114b6a0fb5d51c5dda0494b7931c82 Sim Zhen Quan <<EMAIL>> 1744273023 +0800	checkout: moving from exam-elective-subjects to SCRUM-392-exam-posting-dashboard
63d4a65eb1114b6a0fb5d51c5dda0494b7931c82 0054d22d5cb663c0fd7ab531845714bdf0d52bfe Sim Zhen Quan <<EMAIL>> 1744273031 +0800	merge origin/main: Merge made by the 'ort' strategy.
0054d22d5cb663c0fd7ab531845714bdf0d52bfe 0054d22d5cb663c0fd7ab531845714bdf0d52bfe Sim Zhen Quan <<EMAIL>> 1744273091 +0800	checkout: moving from SCRUM-392-exam-posting-dashboard to SCRUM-392-exam-posting-dashboard
0054d22d5cb663c0fd7ab531845714bdf0d52bfe 645b4c69b3de268843ae9aa50c9028f09cc3a0d5 Sim Zhen Quan <<EMAIL>> 1744273305 +0800	checkout: moving from SCRUM-392-exam-posting-dashboard to exam-output-component-output-type
645b4c69b3de268843ae9aa50c9028f09cc3a0d5 4fa81871bc3cd640b2c5d0e4617e81850873a88d Sim Zhen Quan <<EMAIL>> 1744273310 +0800	merge origin/main: Merge made by the 'ort' strategy.
4fa81871bc3cd640b2c5d0e4617e81850873a88d 0054d22d5cb663c0fd7ab531845714bdf0d52bfe Sim Zhen Quan <<EMAIL>> 1744274329 +0800	checkout: moving from exam-output-component-output-type to SCRUM-392-exam-posting-dashboard
0054d22d5cb663c0fd7ab531845714bdf0d52bfe c10523fb4f978dafb72c784eb09bf42247ff1a4c Sim Zhen Quan <<EMAIL>> 1744274334 +0800	merge origin/main: Merge made by the 'ort' strategy.
c10523fb4f978dafb72c784eb09bf42247ff1a4c a089d8505c33afcd6a184d500ad7be14884f638a Sim Zhen Quan <<EMAIL>> 1744275571 +0800	checkout: moving from SCRUM-392-exam-posting-dashboard to dev
a089d8505c33afcd6a184d500ad7be14884f638a 793032f9240efbbdb6691aa07ea5eabb618c034b Sim Zhen Quan <<EMAIL>> 1744275581 +0800	checkout: moving from dev to main
793032f9240efbbdb6691aa07ea5eabb618c034b 09e817ea94ec1e34fa55118053cf604665bc2ae8 Sim Zhen Quan <<EMAIL>> 1744275586 +0800	pull: Fast-forward
09e817ea94ec1e34fa55118053cf604665bc2ae8 a089d8505c33afcd6a184d500ad7be14884f638a Sim Zhen Quan <<EMAIL>> 1744275844 +0800	checkout: moving from main to dev
a089d8505c33afcd6a184d500ad7be14884f638a 5f5c5ce0d85041dd47d865f92d4dee1662215a5a Sim Zhen Quan <<EMAIL>> 1744275857 +0800	merge origin/main: Merge made by the 'ort' strategy.
5f5c5ce0d85041dd47d865f92d4dee1662215a5a d59b8b36ebee927bc3c606a41f2039ce2da070ea Sim Zhen Quan <<EMAIL>> 1744336345 +0800	commit: Deployed to dev
d59b8b36ebee927bc3c606a41f2039ce2da070ea 6a717749e84be47a1bff04c5496be538c7182e0e Sim Zhen Quan <<EMAIL>> 1744336354 +0800	checkout: moving from dev to feature/exam-publish-report-card
6a717749e84be47a1bff04c5496be538c7182e0e e41c3acfe0d0fc42424ff088089b75dd5ab356c7 Sim Zhen Quan <<EMAIL>> 1744336360 +0800	merge origin/main: Merge made by the 'ort' strategy.
e41c3acfe0d0fc42424ff088089b75dd5ab356c7 006019bc2b1987028737938b2cb0075994c6581d Sim Zhen Quan <<EMAIL>> 1744340750 +0800	commit: Review WIP
006019bc2b1987028737938b2cb0075994c6581d 4922df94e19b5d57361bc75d3ffdd3d8450a00aa Sim Zhen Quan <<EMAIL>> 1744346172 +0800	pull: Fast-forward
4922df94e19b5d57361bc75d3ffdd3d8450a00aa 44e15271abe0cab0ce550929331bddd1f4ac8ad2 Sim Zhen Quan <<EMAIL>> 1744346954 +0800	checkout: moving from feature/exam-publish-report-card to calendar
44e15271abe0cab0ce550929331bddd1f4ac8ad2 0c2bd77625c33fb5663bbaae797dd9febf957226 Sim Zhen Quan <<EMAIL>> 1744346961 +0800	pull: Fast-forward
0c2bd77625c33fb5663bbaae797dd9febf957226 d59b8b36ebee927bc3c606a41f2039ce2da070ea Sim Zhen Quan <<EMAIL>> 1744346962 +0800	checkout: moving from calendar to dev
d59b8b36ebee927bc3c606a41f2039ce2da070ea d724724a3321261d84cf6b359e2f0ada6bea70d3 Sim Zhen Quan <<EMAIL>> 1744347021 +0800	commit (merge): Merge calendar branch
d724724a3321261d84cf6b359e2f0ada6bea70d3 4922df94e19b5d57361bc75d3ffdd3d8450a00aa Sim Zhen Quan <<EMAIL>> 1744353760 +0800	checkout: moving from dev to feature/exam-publish-report-card
4922df94e19b5d57361bc75d3ffdd3d8450a00aa 54a8da3552cb96f24f3262eb8b9b10f9e05c788b Sim Zhen Quan <<EMAIL>> 1744353766 +0800	pull: Fast-forward
54a8da3552cb96f24f3262eb8b9b10f9e05c788b 58b56afd79d7f0d847973c5f1d1b0e2c7270ee91 Sim Zhen Quan <<EMAIL>> 1744356115 +0800	commit: Reviewed
58b56afd79d7f0d847973c5f1d1b0e2c7270ee91 d724724a3321261d84cf6b359e2f0ada6bea70d3 Sim Zhen Quan <<EMAIL>> 1744359564 +0800	checkout: moving from feature/exam-publish-report-card to dev
d724724a3321261d84cf6b359e2f0ada6bea70d3 0c2bd77625c33fb5663bbaae797dd9febf957226 Sim Zhen Quan <<EMAIL>> 1744360994 +0800	checkout: moving from dev to calendar
0c2bd77625c33fb5663bbaae797dd9febf957226 e883978af9e518397ad793f94239f63712b0e482 Sim Zhen Quan <<EMAIL>> 1744363687 +0800	commit: Fix migration
e883978af9e518397ad793f94239f63712b0e482 7e6d10130f9c0a19927d932d44fae9dc9816d756 Sim Zhen Quan <<EMAIL>> 1744470103 +0800	pull: Fast-forward
7e6d10130f9c0a19927d932d44fae9dc9816d756 893c34ad1ec8c6080ea940c4d0d9872de875cac4 Sim Zhen Quan <<EMAIL>> 1744547908 +0800	commit: Fix overload of data
893c34ad1ec8c6080ea940c4d0d9872de875cac4 6a45081c6e70582f01b79271730612e9b960fb29 Sim Zhen Quan <<EMAIL>> 1744560224 +0800	commit: 1. Calendar Targets index return StudentResource instead of Simple resource because class is needed
6a45081c6e70582f01b79271730612e9b960fb29 d9f0fc185f09cd5920ab7af564bed7c50b13433a Sim Zhen Quan <<EMAIL>> 1744560293 +0800	commit (merge): Merge origin/main
d9f0fc185f09cd5920ab7af564bed7c50b13433a d724724a3321261d84cf6b359e2f0ada6bea70d3 Sim Zhen Quan <<EMAIL>> 1744561737 +0800	checkout: moving from calendar to dev
d724724a3321261d84cf6b359e2f0ada6bea70d3 3c4b9b96f324c241eea49a82912bda480cce4804 Sim Zhen Quan <<EMAIL>> 1744561745 +0800	merge origin/main: Merge made by the 'ort' strategy.
3c4b9b96f324c241eea49a82912bda480cce4804 6815c789ae223360d7de478bf067867cc11e7aa7 Sim Zhen Quan <<EMAIL>> 1744562172 +0800	checkout: moving from dev to migration-english-timeslots
6815c789ae223360d7de478bf067867cc11e7aa7 054f0f954f21f0e34db885440b15e39140f06546 Sim Zhen Quan <<EMAIL>> 1744566151 +0800	commit: Reviewed
054f0f954f21f0e34db885440b15e39140f06546 57807a0d7f0d6ce0a815db6967fed8dd16c34c10 Sim Zhen Quan <<EMAIL>> 1744566372 +0800	checkout: moving from migration-english-timeslots to migrate-substitute-teachers
57807a0d7f0d6ce0a815db6967fed8dd16c34c10 5462493a0336b67c8eb20c583460aac3a8a2accb Sim Zhen Quan <<EMAIL>> 1744566471 +0800	commit (merge): Merge origin/main
5462493a0336b67c8eb20c583460aac3a8a2accb 0091e253b6ae79bb59f480193f895e4b9bcf968f Sim Zhen Quan <<EMAIL>> 1744568837 +0800	commit: Reviewed
0091e253b6ae79bb59f480193f895e4b9bcf968f 0ee8b6a9b60f0c63e3396453d6ae6692a25f37f8 Sim Zhen Quan <<EMAIL>> 1744597149 +0800	checkout: moving from migrate-substitute-teachers to enhancement/add-year-to-semester-setting
0ee8b6a9b60f0c63e3396453d6ae6692a25f37f8 0b52ea9aceb32cff1943c9a991175377d0390720 Sim Zhen Quan <<EMAIL>> 1744597181 +0800	merge origin/main: Merge made by the 'ort' strategy.
0b52ea9aceb32cff1943c9a991175377d0390720 c8be8a14843c12f4474dc36a09fca62f4354fe84 Sim Zhen Quan <<EMAIL>> 1744597203 +0800	checkout: moving from enhancement/add-year-to-semester-setting to migrate-script-cocu-timetable
c8be8a14843c12f4474dc36a09fca62f4354fe84 3e94add502e4ffc0a8f860190ffbc5b0bfc9b395 Sim Zhen Quan <<EMAIL>> 1744597208 +0800	merge origin/main: Merge made by the 'ort' strategy.
3e94add502e4ffc0a8f860190ffbc5b0bfc9b395 8be1acd8c9cb154b38171f747a1ec665c03a36e0 Sim Zhen Quan <<EMAIL>> 1744598869 +0800	commit: Reviewed
8be1acd8c9cb154b38171f747a1ec665c03a36e0 daba61e8bd751a796174edcadf3e4d93a1069998 Sim Zhen Quan <<EMAIL>> 1744599151 +0800	checkout: moving from migrate-script-cocu-timetable to bugfix/exam-exemption-json-decode-student-name
daba61e8bd751a796174edcadf3e4d93a1069998 1aa292d351dcef72bedbcdfd97d982102ef042cc Sim Zhen Quan <<EMAIL>> 1744599159 +0800	merge origin/main: Merge made by the 'ort' strategy.
1aa292d351dcef72bedbcdfd97d982102ef042cc 09e817ea94ec1e34fa55118053cf604665bc2ae8 Sim Zhen Quan <<EMAIL>> 1744599488 +0800	checkout: moving from bugfix/exam-exemption-json-decode-student-name to main
09e817ea94ec1e34fa55118053cf604665bc2ae8 d1e3f3d13b23b6bfc823f815bc664f57b7eef6a1 Sim Zhen Quan <<EMAIL>> 1744599493 +0800	pull: Fast-forward
d1e3f3d13b23b6bfc823f815bc664f57b7eef6a1 3c4b9b96f324c241eea49a82912bda480cce4804 Sim Zhen Quan <<EMAIL>> 1744599501 +0800	checkout: moving from main to dev
3c4b9b96f324c241eea49a82912bda480cce4804 770711b39b693fe41e035ddcb0feaeed34d990b1 Sim Zhen Quan <<EMAIL>> 1744599510 +0800	merge origin/main: Merge made by the 'ort' strategy.
770711b39b693fe41e035ddcb0feaeed34d990b1 a49509a0a3bd97984e60886f54a33bd304a5ad01 Sim Zhen Quan <<EMAIL>> 1744599841 +0800	checkout: moving from dev to qas
a49509a0a3bd97984e60886f54a33bd304a5ad01 ca7a5597d42c0622e92912e356985de55712e7c0 Sim Zhen Quan <<EMAIL>> 1744599847 +0800	merge origin/main: Merge made by the 'ort' strategy.
ca7a5597d42c0622e92912e356985de55712e7c0 e33067de83501f4c10e047d3fab00f5f12b671f7 Sim Zhen Quan <<EMAIL>> 1744602502 +0800	commit: Deployed to qas
e33067de83501f4c10e047d3fab00f5f12b671f7 770711b39b693fe41e035ddcb0feaeed34d990b1 Sim Zhen Quan <<EMAIL>> 1744602508 +0800	checkout: moving from qas to dev
770711b39b693fe41e035ddcb0feaeed34d990b1 781c388090e5407e9dcb3050464a31a2f065dc67 Sim Zhen Quan <<EMAIL>> 1744602521 +0800	merge origin/calendar: Merge made by the 'ort' strategy.
781c388090e5407e9dcb3050464a31a2f065dc67 0b52ea9aceb32cff1943c9a991175377d0390720 Sim Zhen Quan <<EMAIL>> 1744605334 +0800	checkout: moving from dev to enhancement/add-year-to-semester-setting
0b52ea9aceb32cff1943c9a991175377d0390720 e43745e077001d542a96311dcc4d03b8720fcd20 Sim Zhen Quan <<EMAIL>> 1744605339 +0800	merge origin/main: Merge made by the 'ort' strategy.
e43745e077001d542a96311dcc4d03b8720fcd20 d9f0fc185f09cd5920ab7af564bed7c50b13433a Sim Zhen Quan <<EMAIL>> 1744607914 +0800	checkout: moving from enhancement/add-year-to-semester-setting to calendar
d9f0fc185f09cd5920ab7af564bed7c50b13433a 5eb91f3f4110c05c16806b401acd7f18feac1d16 Sim Zhen Quan <<EMAIL>> 1744617272 +0800	pull: Fast-forward
5eb91f3f4110c05c16806b401acd7f18feac1d16 b5a1eaed7ff0dac4f12d69f69f2ccc9f420a64bb Sim Zhen Quan <<EMAIL>> 1744617428 +0800	pull: Fast-forward
b5a1eaed7ff0dac4f12d69f69f2ccc9f420a64bb 12dce545c605417e6691632461cf7facbab2fdac Sim Zhen Quan <<EMAIL>> 1744622138 +0800	pull: Fast-forward
12dce545c605417e6691632461cf7facbab2fdac b1fcf18398670b2ca3d7f10307b26b343717e324 Sim Zhen Quan <<EMAIL>> 1744622268 +0800	commit: Added function to get all grouped periods
b1fcf18398670b2ca3d7f10307b26b343717e324 5db237e9f36b784e216e6551ab3064ec300b6b8c Sim Zhen Quan <<EMAIL>> 1744622587 +0800	pull: Fast-forward
5db237e9f36b784e216e6551ab3064ec300b6b8c 781c388090e5407e9dcb3050464a31a2f065dc67 Sim Zhen Quan <<EMAIL>> 1744622591 +0800	checkout: moving from calendar to dev
781c388090e5407e9dcb3050464a31a2f065dc67 3ea1ac0394fd1266e8e418176fe9605efab9a679 Sim Zhen Quan <<EMAIL>> 1744622607 +0800	merge origin/calendar: Merge made by the 'ort' strategy.
3ea1ac0394fd1266e8e418176fe9605efab9a679 5b35bda554ed2f31923466e1770207e29ddfa280 Sim Zhen Quan <<EMAIL>> 1744653800 +0800	commit: Deployed to dev
5b35bda554ed2f31923466e1770207e29ddfa280 5b35bda554ed2f31923466e1770207e29ddfa280 Sim Zhen Quan <<EMAIL>> 1744681468 +0800	checkout: moving from dev to dev
5b35bda554ed2f31923466e1770207e29ddfa280 d1e3f3d13b23b6bfc823f815bc664f57b7eef6a1 Sim Zhen Quan <<EMAIL>> 1744685715 +0800	checkout: moving from dev to main
d1e3f3d13b23b6bfc823f815bc664f57b7eef6a1 5db237e9f36b784e216e6551ab3064ec300b6b8c Sim Zhen Quan <<EMAIL>> 1744687714 +0800	checkout: moving from main to calendar
5db237e9f36b784e216e6551ab3064ec300b6b8c e98924a99a7b57e7267279cda779087cf2564303 Sim Zhen Quan <<EMAIL>> 1744687720 +0800	pull: Fast-forward
e98924a99a7b57e7267279cda779087cf2564303 d1e3f3d13b23b6bfc823f815bc664f57b7eef6a1 Sim Zhen Quan <<EMAIL>> 1744687726 +0800	checkout: moving from calendar to main
d1e3f3d13b23b6bfc823f815bc664f57b7eef6a1 5b35bda554ed2f31923466e1770207e29ddfa280 Sim Zhen Quan <<EMAIL>> 1744687733 +0800	checkout: moving from main to dev
5b35bda554ed2f31923466e1770207e29ddfa280 1a52e6f67d282b787ace999a9329554c379c2d72 Sim Zhen Quan <<EMAIL>> 1744687745 +0800	merge calendar: Merge made by the 'ort' strategy.
1a52e6f67d282b787ace999a9329554c379c2d72 e98924a99a7b57e7267279cda779087cf2564303 Sim Zhen Quan <<EMAIL>> 1744687996 +0800	checkout: moving from dev to calendar
e98924a99a7b57e7267279cda779087cf2564303 45926da1f8b355fa2390a19ccce34274f9f667ff Sim Zhen Quan <<EMAIL>> 1744688001 +0800	merge origin/main: Merge made by the 'ort' strategy.
45926da1f8b355fa2390a19ccce34274f9f667ff 258d1368ee5934033c8f7b85619cf0484950d0e6 Sim Zhen Quan <<EMAIL>> 1744707441 +0800	pull: Fast-forward
258d1368ee5934033c8f7b85619cf0484950d0e6 ccc1742102a2cb90cb3e9e743e4d61269b1dcd72 Sim Zhen Quan <<EMAIL>> 1744707497 +0800	commit: Review WIP
ccc1742102a2cb90cb3e9e743e4d61269b1dcd72 1a52e6f67d282b787ace999a9329554c379c2d72 Sim Zhen Quan <<EMAIL>> 1744707506 +0800	checkout: moving from calendar to dev
1a52e6f67d282b787ace999a9329554c379c2d72 f4798e9e7d68b4a430d6404919148a057e09789f Sim Zhen Quan <<EMAIL>> 1744707512 +0800	merge calendar: Merge made by the 'ort' strategy.
f4798e9e7d68b4a430d6404919148a057e09789f ccc1742102a2cb90cb3e9e743e4d61269b1dcd72 Sim Zhen Quan <<EMAIL>> 1744712873 +0800	checkout: moving from dev to calendar
ccc1742102a2cb90cb3e9e743e4d61269b1dcd72 0269b350fba060ae4649d9c474d5d83315b7f824 Sim Zhen Quan <<EMAIL>> 1744732823 +0800	merge origin/calendar: Fast-forward
0269b350fba060ae4649d9c474d5d83315b7f824 cd54b15787fd827b7b00205a4daae27ce709b9d9 Sim Zhen Quan <<EMAIL>> 1744733064 +0800	merge origin/main: Merge made by the 'ort' strategy.
cd54b15787fd827b7b00205a4daae27ce709b9d9 f1ef11919071669ee8b596cdd8e6d8110a2348c3 Sim Zhen Quan <<EMAIL>> 1744733405 +0800	commit: Reviewed 70% of the code, PR too big to go thru every line of code, will test manually on FE to make sure the scenarios are being handled correctly
f1ef11919071669ee8b596cdd8e6d8110a2348c3 c5419dc701185f3c0ac48b495bc1d39ec41a9574 Sim Zhen Quan <<EMAIL>> 1744733475 +0800	checkout: moving from calendar to feature/daily-attendance-report
c5419dc701185f3c0ac48b495bc1d39ec41a9574 33e4bcbedbd0b76731f5bec4c345f214436e233e Sim Zhen Quan <<EMAIL>> 1744733482 +0800	merge origin/main: Merge made by the 'ort' strategy.
33e4bcbedbd0b76731f5bec4c345f214436e233e 34cdb85868842546d21fb292c37bb55fb7d6ecf8 Sim Zhen Quan <<EMAIL>> 1744733533 +0800	checkout: moving from feature/daily-attendance-report to feature/class-attendance-taking-report
34cdb85868842546d21fb292c37bb55fb7d6ecf8 812a53178348da4d3174ca418e458ab1ecde30aa Sim Zhen Quan <<EMAIL>> 1744733667 +0800	commit (merge): Merge branch
812a53178348da4d3174ca418e458ab1ecde30aa f4798e9e7d68b4a430d6404919148a057e09789f Sim Zhen Quan <<EMAIL>> 1744733881 +0800	checkout: moving from feature/class-attendance-taking-report to dev
f4798e9e7d68b4a430d6404919148a057e09789f 51e61a789d2f0aa9f62442f1edfa0022fa3311f5 Sim Zhen Quan <<EMAIL>> 1744733890 +0800	merge origin/main: Merge made by the 'ort' strategy.
51e61a789d2f0aa9f62442f1edfa0022fa3311f5 109ae34986c593a41936018f14acebc4ac0815c0 Sim Zhen Quan <<EMAIL>> 1744733906 +0800	merge feature/daily-attendance-report: Merge made by the 'ort' strategy.
109ae34986c593a41936018f14acebc4ac0815c0 75bf27a3dadc81ef7bb13926dfd38e190babc21e Sim Zhen Quan <<EMAIL>> 1744765039 +0800	commit: Deployed to dev
75bf27a3dadc81ef7bb13926dfd38e190babc21e d1e3f3d13b23b6bfc823f815bc664f57b7eef6a1 Sim Zhen Quan <<EMAIL>> 1744765145 +0800	checkout: moving from dev to main
d1e3f3d13b23b6bfc823f815bc664f57b7eef6a1 6a6aa93e905ba6d9e89c9d2df6fcc0f1ce22db97 Sim Zhen Quan <<EMAIL>> 1744765150 +0800	pull: Fast-forward
6a6aa93e905ba6d9e89c9d2df6fcc0f1ce22db97 75bf27a3dadc81ef7bb13926dfd38e190babc21e Sim Zhen Quan <<EMAIL>> 1744766080 +0800	checkout: moving from main to dev
75bf27a3dadc81ef7bb13926dfd38e190babc21e 6a6aa93e905ba6d9e89c9d2df6fcc0f1ce22db97 Sim Zhen Quan <<EMAIL>> 1744766102 +0800	checkout: moving from dev to main
6a6aa93e905ba6d9e89c9d2df6fcc0f1ce22db97 6b68588446a958e5b4195206a40e6e613e4a14f1 Sim Zhen Quan <<EMAIL>> 1744783083 +0800	commit: Migration script fixes
6b68588446a958e5b4195206a40e6e613e4a14f1 3fecbcbcf9458138d27db0f3d602c5a73efa9dcb Sim Zhen Quan <<EMAIL>> 1744783878 +0800	commit: Added Calendar Seeder
3fecbcbcf9458138d27db0f3d602c5a73efa9dcb 6d8b2459c9b2df678a11272c94278fe46a5d976d Sim Zhen Quan <<EMAIL>> 1744785126 +0800	checkout: moving from main to SCRUM-371-fix-scholarship-support-multiple-students
6d8b2459c9b2df678a11272c94278fe46a5d976d f1ba167a371877d796869f4d1bd30c418ecea695 Sim Zhen Quan <<EMAIL>> 1744785132 +0800	merge origin/main: Merge made by the 'ort' strategy.
f1ba167a371877d796869f4d1bd30c418ecea695 3fecbcbcf9458138d27db0f3d602c5a73efa9dcb Sim Zhen Quan <<EMAIL>> 1744785916 +0800	checkout: moving from SCRUM-371-fix-scholarship-support-multiple-students to main
3fecbcbcf9458138d27db0f3d602c5a73efa9dcb b55abc76e5f8409a62a43da6038aaf5d971a99c0 Sim Zhen Quan <<EMAIL>> 1744785921 +0800	pull: Fast-forward
b55abc76e5f8409a62a43da6038aaf5d971a99c0 f10b3a7a17a9869ff941d9e431fd0d88d18a7d21 Sim Zhen Quan <<EMAIL>> 1744788113 +0800	checkout: moving from main to SCRUM-413-Phase2-permission-seeder
f10b3a7a17a9869ff941d9e431fd0d88d18a7d21 0ab059f063ef26bba0df114112730dbd94e7262b Sim Zhen Quan <<EMAIL>> 1744788119 +0800	merge origin/main: Merge made by the 'ort' strategy.
0ab059f063ef26bba0df114112730dbd94e7262b 0fde93d4f93f80bc65a54ba70a40df43cc051e1e Sim Zhen Quan <<EMAIL>> 1744790457 +0800	commit: Updated readme
0fde93d4f93f80bc65a54ba70a40df43cc051e1e b55abc76e5f8409a62a43da6038aaf5d971a99c0 Sim Zhen Quan <<EMAIL>> 1744790480 +0800	checkout: moving from SCRUM-413-Phase2-permission-seeder to main
b55abc76e5f8409a62a43da6038aaf5d971a99c0 c3be00488d4a82ac0a68d5866e57107de7e708aa Sim Zhen Quan <<EMAIL>> 1744790486 +0800	pull: Fast-forward
c3be00488d4a82ac0a68d5866e57107de7e708aa db9be4750acbf70d29db214c60c7a808de6a8328 Sim Zhen Quan <<EMAIL>> 1744795863 +0800	checkout: moving from main to fix-attendance-posting-bug
db9be4750acbf70d29db214c60c7a808de6a8328 e33067de83501f4c10e047d3fab00f5f12b671f7 Sim Zhen Quan <<EMAIL>> 1744812208 +0800	checkout: moving from fix-attendance-posting-bug to qas
e33067de83501f4c10e047d3fab00f5f12b671f7 c0d1cf6d6c46b494c925824631fb5d3ac7170ffe Sim Zhen Quan <<EMAIL>> 1744812270 +0800	commit (merge): Merge origin/main
c0d1cf6d6c46b494c925824631fb5d3ac7170ffe ceba35dec9909dd52fdea1810240d0ab4452806b Sim Zhen Quan <<EMAIL>> 1744812635 +0800	commit: Deployed to qas
ceba35dec9909dd52fdea1810240d0ab4452806b a14d9eeef529798c7f592b87ee0573e83b9259d2 Sim Zhen Quan <<EMAIL>> 1744812687 +0800	checkout: moving from qas to SCRUM-418-Migrate-Hostel-PIC
a14d9eeef529798c7f592b87ee0573e83b9259d2 fabf8dc9313bfbbb0e158811d7d59f9408f31c0f Sim Zhen Quan <<EMAIL>> 1744812879 +0800	commit: Add in readme
fabf8dc9313bfbbb0e158811d7d59f9408f31c0f c3be00488d4a82ac0a68d5866e57107de7e708aa Sim Zhen Quan <<EMAIL>> 1744812939 +0800	checkout: moving from SCRUM-418-Migrate-Hostel-PIC to main
c3be00488d4a82ac0a68d5866e57107de7e708aa 9c278e106fffb99558f7161113e007fee3c2f66b Sim Zhen Quan <<EMAIL>> 1744812944 +0800	pull: Fast-forward
9c278e106fffb99558f7161113e007fee3c2f66b db9be4750acbf70d29db214c60c7a808de6a8328 Sim Zhen Quan <<EMAIL>> 1744812950 +0800	checkout: moving from main to fix-attendance-posting-bug
db9be4750acbf70d29db214c60c7a808de6a8328 d95562fc1db46a30a82929a51dac9736628caae4 Sim Zhen Quan <<EMAIL>> 1744812955 +0800	merge origin/main: Merge made by the 'ort' strategy.
d95562fc1db46a30a82929a51dac9736628caae4 2419c7a069a6dd24dd93e7a9b90edb37389be611 Sim Zhen Quan <<EMAIL>> 1744814540 +0800	commit: Fix to only get timeslot from active timetable
2419c7a069a6dd24dd93e7a9b90edb37389be611 75bf27a3dadc81ef7bb13926dfd38e190babc21e Sim Zhen Quan <<EMAIL>> 1744814580 +0800	checkout: moving from fix-attendance-posting-bug to dev
75bf27a3dadc81ef7bb13926dfd38e190babc21e 9c278e106fffb99558f7161113e007fee3c2f66b Sim Zhen Quan <<EMAIL>> 1744814592 +0800	checkout: moving from dev to main
9c278e106fffb99558f7161113e007fee3c2f66b 7161685ae9b8c87340e5da023f20a64b2da94c33 Sim Zhen Quan <<EMAIL>> 1744814597 +0800	pull: Fast-forward
7161685ae9b8c87340e5da023f20a64b2da94c33 0be760e5a7634b219f8e5cdb1bfa36e7422050fe Sim Zhen Quan <<EMAIL>> 1744816141 +0800	commit: Update Kernel
0be760e5a7634b219f8e5cdb1bfa36e7422050fe ceba35dec9909dd52fdea1810240d0ab4452806b Sim Zhen Quan <<EMAIL>> 1744816151 +0800	checkout: moving from main to qas
ceba35dec9909dd52fdea1810240d0ab4452806b 3732fe66327d4fce862584a219baef31867d242c Sim Zhen Quan <<EMAIL>> 1744816159 +0800	merge origin/main: Merge made by the 'ort' strategy.
3732fe66327d4fce862584a219baef31867d242c 62103c6ba5d212a8bc4e0c2504d0153ce9441384 Sim Zhen Quan <<EMAIL>> 1744816358 +0800	commit: Deployed to qas
62103c6ba5d212a8bc4e0c2504d0153ce9441384 0be760e5a7634b219f8e5cdb1bfa36e7422050fe Sim Zhen Quan <<EMAIL>> 1744816437 +0800	checkout: moving from qas to main
0be760e5a7634b219f8e5cdb1bfa36e7422050fe 8d3479855bfaf0d000310c0d5ff76fea7e1a5585 Sim Zhen Quan <<EMAIL>> 1744817925 +0800	commit: Bug fix
8d3479855bfaf0d000310c0d5ff76fea7e1a5585 206b57fec963d4ab7272efd086487c76e2ef3670 Sim Zhen Quan <<EMAIL>> 1744817941 +0800	checkout: moving from main to SCRUM-418-Withdrawal-Reason
206b57fec963d4ab7272efd086487c76e2ef3670 7dc2dd6bb865efe4147567e150fa4c0c72c8c7f8 Sim Zhen Quan <<EMAIL>> 1744817998 +0800	commit (merge): Merge remote-tracking branch 'origin/main' into SCRUM-418-Withdrawal-Reason
7dc2dd6bb865efe4147567e150fa4c0c72c8c7f8 98c3a0d422dbc42a77faf4cd980307f7717497fd Sim Zhen Quan <<EMAIL>> 1744818121 +0800	commit: Add migration step for withdrawal reasons in README
98c3a0d422dbc42a77faf4cd980307f7717497fd 6e0ac54e24a2df6d353cec55d01650d6804d0dd4 Sim Zhen Quan <<EMAIL>> 1744822315 +0800	checkout: moving from SCRUM-418-Withdrawal-Reason to SCRUM-404-Changes-to-autocount-daily-collection-report
6e0ac54e24a2df6d353cec55d01650d6804d0dd4 37f6029a7b36c8e6f62313a8380b07e14c2f929f Sim Zhen Quan <<EMAIL>> 1744822320 +0800	merge origin/main: Merge made by the 'ort' strategy.
37f6029a7b36c8e6f62313a8380b07e14c2f929f 8d3479855bfaf0d000310c0d5ff76fea7e1a5585 Sim Zhen Quan <<EMAIL>> 1744822646 +0800	checkout: moving from SCRUM-404-Changes-to-autocount-daily-collection-report to main
8d3479855bfaf0d000310c0d5ff76fea7e1a5585 6c60f2b5a95ca988d0dec25c80a8b62f9e6f3471 Sim Zhen Quan <<EMAIL>> 1744822651 +0800	pull: Fast-forward
6c60f2b5a95ca988d0dec25c80a8b62f9e6f3471 8bee32651fd50a7c67bcf81f012073380644ca58 Sim Zhen Quan <<EMAIL>> 1744822659 +0800	checkout: moving from main to api/duplicate-semester
8bee32651fd50a7c67bcf81f012073380644ca58 e37337c2af460ff13f232f172faa32d21aa4984b Sim Zhen Quan <<EMAIL>> 1744822669 +0800	merge origin/main: Merge made by the 'ort' strategy.
e37337c2af460ff13f232f172faa32d21aa4984b 4ab7f2c2937c563f5425f230b2033c55e8ba6b79 Sim Zhen Quan <<EMAIL>> 1744867552 +0800	commit: Add migration step for withdrawal reasons in README
4ab7f2c2937c563f5425f230b2033c55e8ba6b79 6c60f2b5a95ca988d0dec25c80a8b62f9e6f3471 Sim Zhen Quan <<EMAIL>> 1744873136 +0800	checkout: moving from api/duplicate-semester to main
6c60f2b5a95ca988d0dec25c80a8b62f9e6f3471 9134576a366118c20b091d05c398cd82f2d18bbf Sim Zhen Quan <<EMAIL>> 1744876855 +0800	commit: Updated discount to add in nullable source type and student id
9134576a366118c20b091d05c398cd82f2d18bbf 4fc250f3cc306c44baf860e8f6f864c7ac68636a Sim Zhen Quan <<EMAIL>> 1744880249 +0800	checkout: moving from main to attendance-bug-fix
4fc250f3cc306c44baf860e8f6f864c7ac68636a 1fbfbc0c2df0cbb2ec8a601323f4a79380855db2 Sim Zhen Quan <<EMAIL>> 1744880255 +0800	pull: Fast-forward
1fbfbc0c2df0cbb2ec8a601323f4a79380855db2 234897e99285a275354fde75df96d496e360b834 Sim Zhen Quan <<EMAIL>> 1744880271 +0800	merge origin/main: Merge made by the 'ort' strategy.
234897e99285a275354fde75df96d496e360b834 7fe0403e2949a022e1b0b869c2d72dceb979f0d2 Sim Zhen Quan <<EMAIL>> 1744881483 +0800	merge origin/main: Fast-forward
7fe0403e2949a022e1b0b869c2d72dceb979f0d2 36208c56bfe4504e5ca9b81071f775c90eaeccb9 Sim Zhen Quan <<EMAIL>> 1744881516 +0800	checkout: moving from attendance-bug-fix to fix/seat-setting-bug
36208c56bfe4504e5ca9b81071f775c90eaeccb9 4abaf12daf5c736273c7a206a0b9ae4b8c88f0dd Sim Zhen Quan <<EMAIL>> 1744881521 +0800	merge origin/main: Merge made by the 'ort' strategy.
4abaf12daf5c736273c7a206a0b9ae4b8c88f0dd 730866d93083b9b66612ed94ad7194025ef610db Sim Zhen Quan <<EMAIL>> 1744881684 +0800	checkout: moving from fix/seat-setting-bug to substitute-record-apis-add-allowance
730866d93083b9b66612ed94ad7194025ef610db 15132103924297d48c902c4c50ee8cc3d456fcf3 Sim Zhen Quan <<EMAIL>> 1744881797 +0800	commit (merge): Merge remote-tracking branch 'origin/main' into substitute-record-apis-add-allowance
15132103924297d48c902c4c50ee8cc3d456fcf3 15132103924297d48c902c4c50ee8cc3d456fcf3 Sim Zhen Quan <<EMAIL>> 1744882295 +0800	checkout: moving from substitute-record-apis-add-allowance to substitute-record-apis-add-allowance
15132103924297d48c902c4c50ee8cc3d456fcf3 75bf27a3dadc81ef7bb13926dfd38e190babc21e Sim Zhen Quan <<EMAIL>> 1744882634 +0800	checkout: moving from substitute-record-apis-add-allowance to dev
75bf27a3dadc81ef7bb13926dfd38e190babc21e 9134576a366118c20b091d05c398cd82f2d18bbf Sim Zhen Quan <<EMAIL>> 1744882689 +0800	checkout: moving from dev to main
9134576a366118c20b091d05c398cd82f2d18bbf bbdb50115935c27329bbc8848c23093c3d9eae5f Sim Zhen Quan <<EMAIL>> 1744882694 +0800	pull: Fast-forward
bbdb50115935c27329bbc8848c23093c3d9eae5f 75bf27a3dadc81ef7bb13926dfd38e190babc21e Sim Zhen Quan <<EMAIL>> 1744882696 +0800	checkout: moving from main to dev
75bf27a3dadc81ef7bb13926dfd38e190babc21e d7bcb8a92579fce4fdf04569f48b1cbdf8aea034 Sim Zhen Quan <<EMAIL>> 1744882699 +0800	merge origin/main: Merge made by the 'ort' strategy.
d7bcb8a92579fce4fdf04569f48b1cbdf8aea034 49c9cbb50817402ae72b137340d9aee4efc076a1 Sim Zhen Quan <<EMAIL>> 1744885843 +0800	commit: Deployed to dev
49c9cbb50817402ae72b137340d9aee4efc076a1 bbdb50115935c27329bbc8848c23093c3d9eae5f Sim Zhen Quan <<EMAIL>> 1744887727 +0800	checkout: moving from dev to main
bbdb50115935c27329bbc8848c23093c3d9eae5f c052125d5ee560b2cabcd3dae700470f42b33981 Sim Zhen Quan <<EMAIL>> 1744887765 +0800	commit: Add support for pending payment status in billing resources
c052125d5ee560b2cabcd3dae700470f42b33981 18d11bba7014f6c61cf36b63ddca124e3bb0e9fc Sim Zhen Quan <<EMAIL>> 1744887849 +0800	checkout: moving from main to fix-migration-set-student-class-inactive-for-left-school
18d11bba7014f6c61cf36b63ddca124e3bb0e9fc 709b9c5710e7fe62593e22b7ab4c11cd0671f86b Sim Zhen Quan <<EMAIL>> 1744887854 +0800	merge origin/main: Merge made by the 'ort' strategy.
709b9c5710e7fe62593e22b7ab4c11cd0671f86b 709b9c5710e7fe62593e22b7ab4c11cd0671f86b Sim Zhen Quan <<EMAIL>> 1744903016 +0800	reset: moving to HEAD
709b9c5710e7fe62593e22b7ab4c11cd0671f86b 302922014e1c7eacf331ad151bbe4c417cd10099 Sim Zhen Quan <<EMAIL>> 1744903026 +0800	checkout: moving from fix-migration-set-student-class-inactive-for-left-school to mockrun-23-add-primary-class-to-api-response
302922014e1c7eacf331ad151bbe4c417cd10099 b0f4fe4cab3a6d33f4b1bfc38464a110a497845a Sim Zhen Quan <<EMAIL>> 1744907634 +0800	commit: - Fix english class migration
b0f4fe4cab3a6d33f4b1bfc38464a110a497845a a1c1c7288b970880405695798bff8e42bea0e9f5 Sim Zhen Quan <<EMAIL>> 1744907751 +0800	checkout: moving from mockrun-23-add-primary-class-to-api-response to add-show-fe-billing-document-endpoint
a1c1c7288b970880405695798bff8e42bea0e9f5 ff1bcac7ca3a6e3e20efe352c773ca781056e185 Sim Zhen Quan <<EMAIL>> 1744907756 +0800	merge origin/main: Merge made by the 'ort' strategy.
ff1bcac7ca3a6e3e20efe352c773ca781056e185 6cad78fc497e7d86cb916557de0b7b6e962c4706 Sim Zhen Quan <<EMAIL>> 1744908775 +0800	commit: Add unique index for student_timetable
6cad78fc497e7d86cb916557de0b7b6e962c4706 c052125d5ee560b2cabcd3dae700470f42b33981 Sim Zhen Quan <<EMAIL>> 1744908811 +0800	checkout: moving from add-show-fe-billing-document-endpoint to main
c052125d5ee560b2cabcd3dae700470f42b33981 03f1c1209a1746b28478c97627a803d451428f74 Sim Zhen Quan <<EMAIL>> 1744908817 +0800	pull: Fast-forward
03f1c1209a1746b28478c97627a803d451428f74 03f1c1209a1746b28478c97627a803d451428f74 Sim Zhen Quan <<EMAIL>> 1744908839 +0800	checkout: moving from main to change-refresh-view-table-to-job
03f1c1209a1746b28478c97627a803d451428f74 79f5a8288467b08c9345c03a92bb07779ef4be2e Sim Zhen Quan <<EMAIL>> 1744910163 +0800	commit: Refresh materialized view using job instead of sync
79f5a8288467b08c9345c03a92bb07779ef4be2e 03f1c1209a1746b28478c97627a803d451428f74 Sim Zhen Quan <<EMAIL>> 1744910330 +0800	checkout: moving from change-refresh-view-table-to-job to main
03f1c1209a1746b28478c97627a803d451428f74 3c41baaf6548a8d2fa4c81521e06a5190e97c44c Sim Zhen Quan <<EMAIL>> 1744910337 +0800	pull: Fast-forward
3c41baaf6548a8d2fa4c81521e06a5190e97c44c 49c9cbb50817402ae72b137340d9aee4efc076a1 Sim Zhen Quan <<EMAIL>> 1744910373 +0800	checkout: moving from main to dev
49c9cbb50817402ae72b137340d9aee4efc076a1 6c62425c9ef948efbcec48ec44ec562a273cf4e8 Sim Zhen Quan <<EMAIL>> 1744910384 +0800	merge origin/main: Merge made by the 'ort' strategy.
6c62425c9ef948efbcec48ec44ec562a273cf4e8 a29f668526e1f486792ad6b0af4f741df528e838 Sim Zhen Quan <<EMAIL>> 1744940209 +0800	commit: Deployed to dev
a29f668526e1f486792ad6b0af4f741df528e838 3c41baaf6548a8d2fa4c81521e06a5190e97c44c Sim Zhen Quan <<EMAIL>> 1744940218 +0800	checkout: moving from dev to main
3c41baaf6548a8d2fa4c81521e06a5190e97c44c 2cd3853b8402b7d26ca55fe009d607d127293aa7 Sim Zhen Quan <<EMAIL>> 1744942129 +0800	commit: Update materialized view queue config
2cd3853b8402b7d26ca55fe009d607d127293aa7 162abff90e53b343dc54f560ce44df8e073fac2e Sim Zhen Quan <<EMAIL>> 1744942163 +0800	checkout: moving from main to update-attendance-index-param
162abff90e53b343dc54f560ce44df8e073fac2e ff096dfffa368a12f6195a995b7858895d2e5e8f Sim Zhen Quan <<EMAIL>> 1744942173 +0800	merge origin/main: Merge made by the 'ort' strategy.
ff096dfffa368a12f6195a995b7858895d2e5e8f 2cd3853b8402b7d26ca55fe009d607d127293aa7 Sim Zhen Quan <<EMAIL>> 1744943938 +0800	checkout: moving from update-attendance-index-param to main
2cd3853b8402b7d26ca55fe009d607d127293aa7 312a6d9834663f7e57a023fe88bcedac4333b2c4 Sim Zhen Quan <<EMAIL>> 1744943950 +0800	commit: Change attendance posting to 7.31am
312a6d9834663f7e57a023fe88bcedac4333b2c4 080a8cbb6cd6f64dda7980812858f1866a12690b Sim Zhen Quan <<EMAIL>> 1744948165 +0800	commit: Add hostel bed assignment data to employee resource
080a8cbb6cd6f64dda7980812858f1866a12690b c6b4b6188765abfbaf9133a5af70ef31cb1ae091 Sim Zhen Quan <<EMAIL>> 1744948186 +0800	checkout: moving from main to bugfix/semester-class-society-sort
c6b4b6188765abfbaf9133a5af70ef31cb1ae091 660a0c73439d19eaba09b52008085004703bc075 Sim Zhen Quan <<EMAIL>> 1744948190 +0800	merge origin/main: Merge made by the 'ort' strategy.
660a0c73439d19eaba09b52008085004703bc075 080a8cbb6cd6f64dda7980812858f1866a12690b Sim Zhen Quan <<EMAIL>> 1744948469 +0800	checkout: moving from bugfix/semester-class-society-sort to main
080a8cbb6cd6f64dda7980812858f1866a12690b e39a6f32914904ce1042f8fef0cd1e6175050276 Sim Zhen Quan <<EMAIL>> 1744948474 +0800	pull: Fast-forward
e39a6f32914904ce1042f8fef0cd1e6175050276 616e62624a1c2071025cbdeb0f123f86781ef4a6 Sim Zhen Quan <<EMAIL>> 1744948495 +0800	commit: Fix test cases
616e62624a1c2071025cbdeb0f123f86781ef4a6 a29f668526e1f486792ad6b0af4f741df528e838 Sim Zhen Quan <<EMAIL>> 1744948533 +0800	checkout: moving from main to dev
a29f668526e1f486792ad6b0af4f741df528e838 e2b86e8d3438b8d5c362443e4193518ac5a65ec6 Sim Zhen Quan <<EMAIL>> 1744948535 +0800	merge main: Merge made by the 'ort' strategy.
e2b86e8d3438b8d5c362443e4193518ac5a65ec6 6092faf475f1ac267b67580c311b2eb8dfbbd983 Sim Zhen Quan <<EMAIL>> 1744953895 +0800	commit: Deployed to dev
6092faf475f1ac267b67580c311b2eb8dfbbd983 4abe8e5afc2dad95a188b04a1d93223a79101f46 Sim Zhen Quan <<EMAIL>> 1744953934 +0800	checkout: moving from dev to bug/trainer-detail-report
4abe8e5afc2dad95a188b04a1d93223a79101f46 19ef8944d6a63414895799ac7abe08c062956b42 Sim Zhen Quan <<EMAIL>> 1744953939 +0800	merge origin/main: Merge made by the 'ort' strategy.
19ef8944d6a63414895799ac7abe08c062956b42 ff096dfffa368a12f6195a995b7858895d2e5e8f Sim Zhen Quan <<EMAIL>> 1744954340 +0800	checkout: moving from bug/trainer-detail-report to update-attendance-index-param
ff096dfffa368a12f6195a995b7858895d2e5e8f 1cab1476ce316b3d4b199652433c0f0b16ac572b Sim Zhen Quan <<EMAIL>> 1744954346 +0800	pull: Fast-forward
1cab1476ce316b3d4b199652433c0f0b16ac572b 68632fa2ec7a2340aa80b96da465834084bebc4f Sim Zhen Quan <<EMAIL>> 1744954346 +0800	merge origin/main: Merge made by the 'ort' strategy.
68632fa2ec7a2340aa80b96da465834084bebc4f 616e62624a1c2071025cbdeb0f123f86781ef4a6 Sim Zhen Quan <<EMAIL>> 1744955205 +0800	checkout: moving from update-attendance-index-param to main
616e62624a1c2071025cbdeb0f123f86781ef4a6 2efe9e640e9f253df4f7a41dd8c7944644a4fb8c Sim Zhen Quan <<EMAIL>> 1744955213 +0800	pull: Fast-forward
2efe9e640e9f253df4f7a41dd8c7944644a4fb8c 6092faf475f1ac267b67580c311b2eb8dfbbd983 Sim Zhen Quan <<EMAIL>> 1744955213 +0800	checkout: moving from main to dev
6092faf475f1ac267b67580c311b2eb8dfbbd983 acc24ecf57c87714d53c9b5f5a75a953a3816fb9 Sim Zhen Quan <<EMAIL>> 1744955217 +0800	merge origin/main: Merge made by the 'ort' strategy.
acc24ecf57c87714d53c9b5f5a75a953a3816fb9 711f344e9e63614d3dd1011fd429a46ffeb8976c Sim Zhen Quan <<EMAIL>> 1744955471 +0800	commit: Deployed to dev
711f344e9e63614d3dd1011fd429a46ffeb8976c 6a2ca3d30a11f11e791a8255ec76c0b95948f41c Sim Zhen Quan <<EMAIL>> 1744955505 +0800	checkout: moving from dev to fix/conduct-mark-entry-after-deadline
6a2ca3d30a11f11e791a8255ec76c0b95948f41c 22c80052105e744788824cc43c506960ab63dfcb Sim Zhen Quan <<EMAIL>> 1744955515 +0800	merge origin/main: Merge made by the 'ort' strategy.
22c80052105e744788824cc43c506960ab63dfcb df3e1daf0c3cc7ce9f756295d2bbb5769835ffc1 Sim Zhen Quan <<EMAIL>> 1744957769 +0800	commit: Reviewed
df3e1daf0c3cc7ce9f756295d2bbb5769835ffc1 df3e1daf0c3cc7ce9f756295d2bbb5769835ffc1 Sim Zhen Quan <<EMAIL>> 1744957956 +0800	checkout: moving from fix/conduct-mark-entry-after-deadline to fix/conduct-mark-entry-after-deadline
df3e1daf0c3cc7ce9f756295d2bbb5769835ffc1 dd0de29611fe4d48e8f9236193eb3382b42d2600 Sim Zhen Quan <<EMAIL>> 1744957962 +0800	merge origin/main: Fast-forward
dd0de29611fe4d48e8f9236193eb3382b42d2600 f9347416dc68b4e471bc5c759cda8d971026db5d Sim Zhen Quan <<EMAIL>> 1744958005 +0800	checkout: moving from fix/conduct-mark-entry-after-deadline to fix/create-fee-month-validation
f9347416dc68b4e471bc5c759cda8d971026db5d 2fa333baba219c4ef097c579a4127442729e4864 Sim Zhen Quan <<EMAIL>> 1744958011 +0800	merge origin/main: Merge made by the 'ort' strategy.
2fa333baba219c4ef097c579a4127442729e4864 a9fee8e1411cafefda9d97acc80031c151c332c4 Sim Zhen Quan <<EMAIL>> 1744958624 +0800	commit: Reviewed
a9fee8e1411cafefda9d97acc80031c151c332c4 2efe9e640e9f253df4f7a41dd8c7944644a4fb8c Sim Zhen Quan <<EMAIL>> 1744958686 +0800	checkout: moving from fix/create-fee-month-validation to main
2efe9e640e9f253df4f7a41dd8c7944644a4fb8c 242f255099a17a2074eb79700dfa8b4b31dd00c7 Sim Zhen Quan <<EMAIL>> 1744958692 +0800	pull: Fast-forward
242f255099a17a2074eb79700dfa8b4b31dd00c7 711f344e9e63614d3dd1011fd429a46ffeb8976c Sim Zhen Quan <<EMAIL>> 1744958698 +0800	checkout: moving from main to dev
711f344e9e63614d3dd1011fd429a46ffeb8976c fd092df7a8ed07aeb4248220f499b1b15fe590ef Sim Zhen Quan <<EMAIL>> 1744959034 +0800	merge origin/main: Merge made by the 'ort' strategy.
fd092df7a8ed07aeb4248220f499b1b15fe590ef e1a0f0aeafa36c8adb8b9411e7fb6c820b30a2e1 Sim Zhen Quan <<EMAIL>> 1744959929 +0800	commit: Deployed to dev
e1a0f0aeafa36c8adb8b9411e7fb6c820b30a2e1 e1a0f0aeafa36c8adb8b9411e7fb6c820b30a2e1 Sim Zhen Quan <<EMAIL>> 1744960326 +0800	reset: moving to HEAD
e1a0f0aeafa36c8adb8b9411e7fb6c820b30a2e1 242f255099a17a2074eb79700dfa8b4b31dd00c7 Sim Zhen Quan <<EMAIL>> 1744960328 +0800	checkout: moving from dev to main
242f255099a17a2074eb79700dfa8b4b31dd00c7 ee25d4b9229913f152347093bc304179e6dd83db Sim Zhen Quan <<EMAIL>> 1744964200 +0800	pull: Fast-forward
ee25d4b9229913f152347093bc304179e6dd83db 31c87146e23539e7aae60611b43616d1300d676a Sim Zhen Quan <<EMAIL>> 1744964235 +0800	commit: Fix discount typo
31c87146e23539e7aae60611b43616d1300d676a 31c87146e23539e7aae60611b43616d1300d676a Sim Zhen Quan <<EMAIL>> 1744964247 +0800	checkout: moving from main to permission-changes
31c87146e23539e7aae60611b43616d1300d676a 31c87146e23539e7aae60611b43616d1300d676a Sim Zhen Quan <<EMAIL>> 1744972766 +0800	reset: moving to HEAD
31c87146e23539e7aae60611b43616d1300d676a 3d28b2377d28514ecf9ea2296dc753057e58d01c Sim Zhen Quan <<EMAIL>> 1744973201 +0800	commit: Added permissions
3d28b2377d28514ecf9ea2296dc753057e58d01c 31c87146e23539e7aae60611b43616d1300d676a Sim Zhen Quan <<EMAIL>> 1744973293 +0800	checkout: moving from permission-changes to main
31c87146e23539e7aae60611b43616d1300d676a 3d28b2377d28514ecf9ea2296dc753057e58d01c Sim Zhen Quan <<EMAIL>> 1744973312 +0800	checkout: moving from main to permission-changes
3d28b2377d28514ecf9ea2296dc753057e58d01c 31c87146e23539e7aae60611b43616d1300d676a Sim Zhen Quan <<EMAIL>> 1744973325 +0800	checkout: moving from permission-changes to main
31c87146e23539e7aae60611b43616d1300d676a 3d28b2377d28514ecf9ea2296dc753057e58d01c Sim Zhen Quan <<EMAIL>> 1744973325 +0800	merge permission-changes: Fast-forward
3d28b2377d28514ecf9ea2296dc753057e58d01c e1a0f0aeafa36c8adb8b9411e7fb6c820b30a2e1 Sim Zhen Quan <<EMAIL>> 1744973361 +0800	checkout: moving from main to dev
e1a0f0aeafa36c8adb8b9411e7fb6c820b30a2e1 7fefd42f994e70336706ba4930c9bcf2d4caf9a1 Sim Zhen Quan <<EMAIL>> 1744973523 +0800	commit: Deployed to dev
7fefd42f994e70336706ba4930c9bcf2d4caf9a1 3d28b2377d28514ecf9ea2296dc753057e58d01c Sim Zhen Quan <<EMAIL>> 1744983130 +0800	checkout: moving from dev to main
3d28b2377d28514ecf9ea2296dc753057e58d01c 0d9401098843914569a56226dbbacf0edd683d5f Sim Zhen Quan <<EMAIL>> 1744983307 +0800	cherry-pick: Add migration step for withdrawal reasons in README
0d9401098843914569a56226dbbacf0edd683d5f 362cdeac8db6ce95f7daccc8b6251ae222da1617 Sim Zhen Quan <<EMAIL>> 1744984069 +0800	commit: Updated README.md
362cdeac8db6ce95f7daccc8b6251ae222da1617 c89d1e4dac7d52e6374ce88c030e7a57147f40f7 Sim Zhen Quan <<EMAIL>> 1745031251 +0800	commit: Minor fixes
c89d1e4dac7d52e6374ce88c030e7a57147f40f7 1de8cf744fce8c76fdfae4a7560618937b449edb Sim Zhen Quan <<EMAIL>> 1745031267 +0800	merge origin/main: Merge made by the 'ort' strategy.
1de8cf744fce8c76fdfae4a7560618937b449edb f04d1ba6674bfb8ff9353e4583bd5c87d2e8f25a Sim Zhen Quan <<EMAIL>> 1745031283 +0800	checkout: moving from main to library-report-by-school-rate-of-borrow-bug-fix
f04d1ba6674bfb8ff9353e4583bd5c87d2e8f25a 201483e214c305799b6f7912853df5bc74ae0a15 Sim Zhen Quan <<EMAIL>> 1745031289 +0800	merge origin/main: Merge made by the 'ort' strategy.
201483e214c305799b6f7912853df5bc74ae0a15 6f905748f094e589f2f75c3e8af33cd48b8dfdcd Sim Zhen Quan <<EMAIL>> 1745031621 +0800	commit: Remove unused imports
6f905748f094e589f2f75c3e8af33cd48b8dfdcd 9ece82160e297eed0c2e7fc705d0ffa720ef73b6 Sim Zhen Quan <<EMAIL>> 1745031716 +0800	checkout: moving from library-report-by-school-rate-of-borrow-bug-fix to bug/comprehensive-assessment-question
9ece82160e297eed0c2e7fc705d0ffa720ef73b6 9206b3913e2bc1ee322b964c53c33175b6fc33cb Sim Zhen Quan <<EMAIL>> 1745031729 +0800	merge origin/main: Merge made by the 'ort' strategy.
9206b3913e2bc1ee322b964c53c33175b6fc33cb 98ad096ba5e242f583fa4a8b930f5d8583528f37 Sim Zhen Quan <<EMAIL>> 1745032060 +0800	commit: Reviewed
98ad096ba5e242f583fa4a8b930f5d8583528f37 1d10afda9972850987210fdc8b9b425705ae40e9 Sim Zhen Quan <<EMAIL>> 1745032090 +0800	merge origin/main: Merge made by the 'ort' strategy.
1d10afda9972850987210fdc8b9b425705ae40e9 d8e9af1546a91b84f34c7e45745e94224e1eaf25 Sim Zhen Quan <<EMAIL>> 1745032131 +0800	checkout: moving from bug/comprehensive-assessment-question to bug/class-subject
d8e9af1546a91b84f34c7e45745e94224e1eaf25 39f0b36fba1c332d65361c25fc09d6b5fa87ed63 Sim Zhen Quan <<EMAIL>> 1745032138 +0800	merge origin/main: Merge made by the 'ort' strategy.
39f0b36fba1c332d65361c25fc09d6b5fa87ed63 cd3f6655a928910784f49c4b7f5989245f74b325 Sim Zhen Quan <<EMAIL>> 1745032355 +0800	commit: Reviewed
cd3f6655a928910784f49c4b7f5989245f74b325 39b0e7d220858416bf404810ffea3db93c1b50a5 Sim Zhen Quan <<EMAIL>> 1745032398 +0800	checkout: moving from bug/class-subject to bugfix/substitute-management-and-period-sorting
39b0e7d220858416bf404810ffea3db93c1b50a5 06afb10c4e54d4f288cb7f012f9cdad1066e2336 Sim Zhen Quan <<EMAIL>> 1745032409 +0800	merge origin/main: Merge made by the 'ort' strategy.
06afb10c4e54d4f288cb7f012f9cdad1066e2336 dbff68fec903b6fa79c2b38f1820c97bac88702f Sim Zhen Quan <<EMAIL>> 1745033815 +0800	commit: Reviewed Timetable Period
dbff68fec903b6fa79c2b38f1820c97bac88702f 0a9794a758412d019673392224983c491b92befe Sim Zhen Quan <<EMAIL>> 1745033861 +0800	checkout: moving from bugfix/substitute-management-and-period-sorting to update-book-sub-classification-resource
0a9794a758412d019673392224983c491b92befe 9d06d907dec94d3bda45b75ba4c3a373ccf976fa Sim Zhen Quan <<EMAIL>> 1745033867 +0800	merge origin/main: Merge made by the 'ort' strategy.
9d06d907dec94d3bda45b75ba4c3a373ccf976fa dadb4aa61aa39f3e5104dcdbe78a472b3dca4adf Sim Zhen Quan <<EMAIL>> 1745034129 +0800	commit: Reviewed
dadb4aa61aa39f3e5104dcdbe78a472b3dca4adf 1de8cf744fce8c76fdfae4a7560618937b449edb Sim Zhen Quan <<EMAIL>> 1745034275 +0800	checkout: moving from update-book-sub-classification-resource to main
1de8cf744fce8c76fdfae4a7560618937b449edb b4abf7b783fbf0f5f8d7036aa9ca99e93cd0d429 Sim Zhen Quan <<EMAIL>> 1745034282 +0800	pull: Fast-forward
b4abf7b783fbf0f5f8d7036aa9ca99e93cd0d429 7fefd42f994e70336706ba4930c9bcf2d4caf9a1 Sim Zhen Quan <<EMAIL>> 1745034283 +0800	checkout: moving from main to dev
7fefd42f994e70336706ba4930c9bcf2d4caf9a1 9e3d97f4cd466e649bd6eaa098fca9b2341e089e Sim Zhen Quan <<EMAIL>> 1745034293 +0800	merge origin/main: Merge made by the 'ort' strategy.
9e3d97f4cd466e649bd6eaa098fca9b2341e089e 4c51c41f4c3ee04fe9a939ca1df820fa685af51f Sim Zhen Quan <<EMAIL>> 1745034587 +0800	commit: Deployed to dev
4c51c41f4c3ee04fe9a939ca1df820fa685af51f 104de8ae951e4b3ce8057d9d6087e1a1c2ec311c Sim Zhen Quan <<EMAIL>> 1745034699 +0800	checkout: moving from dev to bug/substitute-record
104de8ae951e4b3ce8057d9d6087e1a1c2ec311c d55c5c6f02cc64adb8fbf44338923ee7f1de6393 Sim Zhen Quan <<EMAIL>> 1745034707 +0800	merge origin/main: Merge made by the 'ort' strategy.
d55c5c6f02cc64adb8fbf44338923ee7f1de6393 0738542dd798e2aab8d0bbdcf052c81c34739429 Sim Zhen Quan <<EMAIL>> 1745035362 +0800	commit: Reviewed
0738542dd798e2aab8d0bbdcf052c81c34739429 dbff68fec903b6fa79c2b38f1820c97bac88702f Sim Zhen Quan <<EMAIL>> 1745035491 +0800	checkout: moving from bug/substitute-record to bugfix/substitute-management-and-period-sorting
dbff68fec903b6fa79c2b38f1820c97bac88702f 31535d00ef16c1d1f2cc4460f9fb3bc1f644f607 Sim Zhen Quan <<EMAIL>> 1745035500 +0800	pull: Fast-forward
31535d00ef16c1d1f2cc4460f9fb3bc1f644f607 27412297c8ec38ed9320982c4654b037195f8630 Sim Zhen Quan <<EMAIL>> 1745035577 +0800	commit (merge): Merge remote-tracking branch 'origin/main' into bugfix/substitute-management-and-period-sorting
27412297c8ec38ed9320982c4654b037195f8630 cee31b61e2c8d90f33d1b07730feb8624ee5cb48 Sim Zhen Quan <<EMAIL>> 1745035840 +0800	merge origin/main: Fast-forward
cee31b61e2c8d90f33d1b07730feb8624ee5cb48 b4abf7b783fbf0f5f8d7036aa9ca99e93cd0d429 Sim Zhen Quan <<EMAIL>> 1745035883 +0800	checkout: moving from bugfix/substitute-management-and-period-sorting to main
b4abf7b783fbf0f5f8d7036aa9ca99e93cd0d429 cee31b61e2c8d90f33d1b07730feb8624ee5cb48 Sim Zhen Quan <<EMAIL>> 1745035887 +0800	pull: Fast-forward
cee31b61e2c8d90f33d1b07730feb8624ee5cb48 e536d2c7d350b594202c0612f56d48803a263a0e Sim Zhen Quan <<EMAIL>> 1745035899 +0800	checkout: moving from main to class-attendance-taking-display-period-label-name
e536d2c7d350b594202c0612f56d48803a263a0e cee31b61e2c8d90f33d1b07730feb8624ee5cb48 Sim Zhen Quan <<EMAIL>> 1745036743 +0800	checkout: moving from class-attendance-taking-display-period-label-name to main
cee31b61e2c8d90f33d1b07730feb8624ee5cb48 f8ab0638253a82d9fe23a52889a0df4b9d7fe42a Sim Zhen Quan <<EMAIL>> 1745036748 +0800	pull: Fast-forward
f8ab0638253a82d9fe23a52889a0df4b9d7fe42a 4c51c41f4c3ee04fe9a939ca1df820fa685af51f Sim Zhen Quan <<EMAIL>> 1745036749 +0800	checkout: moving from main to dev
4c51c41f4c3ee04fe9a939ca1df820fa685af51f fe65c30244051064701abcd411022091c46a3995 Sim Zhen Quan <<EMAIL>> 1745036755 +0800	merge origin/main: Merge made by the 'ort' strategy.
fe65c30244051064701abcd411022091c46a3995 f601b6ba67521136a0bb15eaf4451900c995925e Sim Zhen Quan <<EMAIL>> 1745041581 +0800	commit: Deployed to dev
f601b6ba67521136a0bb15eaf4451900c995925e dee179be8780fcff4019e50257aedd457550a8fa Sim Zhen Quan <<EMAIL>> 1745041588 +0800	checkout: moving from dev to fix-batch-delete
dee179be8780fcff4019e50257aedd457550a8fa 2007f2dd1f6aef85238228341ab83bfc3af658cc Sim Zhen Quan <<EMAIL>> 1745041962 +0800	commit: Added validation for timeslot override batch id delete
2007f2dd1f6aef85238228341ab83bfc3af658cc df40fcbaccdb2b7ab0388c186fc8cad22a50ead9 Sim Zhen Quan <<EMAIL>> 1745041985 +0800	merge origin/fix-batch-delete: Merge made by the 'ort' strategy.
df40fcbaccdb2b7ab0388c186fc8cad22a50ead9 27a81db5288bdea14ee3d8e1ca83cff161373e95 Sim Zhen Quan <<EMAIL>> 1745043199 +0800	pull: Fast-forward
27a81db5288bdea14ee3d8e1ca83cff161373e95 c8260c126fd6e8d0966ca8332ec347f6f071b7c3 Sim Zhen Quan <<EMAIL>> 1745043432 +0800	commit: Added validation for user inbox batch id delete
c8260c126fd6e8d0966ca8332ec347f6f071b7c3 6acbfdd82be4e29c6a2d12d269937f43db4fc6cb Sim Zhen Quan <<EMAIL>> 1745043444 +0800	merge origin/fix-batch-delete: Merge made by the 'ort' strategy.
6acbfdd82be4e29c6a2d12d269937f43db4fc6cb f8ab0638253a82d9fe23a52889a0df4b9d7fe42a Sim Zhen Quan <<EMAIL>> 1745043726 +0800	checkout: moving from fix-batch-delete to main
f8ab0638253a82d9fe23a52889a0df4b9d7fe42a 1750e710732442cc41e8574cb50ad01d7e9889e2 Sim Zhen Quan <<EMAIL>> 1745043732 +0800	pull: Fast-forward
1750e710732442cc41e8574cb50ad01d7e9889e2 f601b6ba67521136a0bb15eaf4451900c995925e Sim Zhen Quan <<EMAIL>> 1745043732 +0800	checkout: moving from main to dev
f601b6ba67521136a0bb15eaf4451900c995925e 615bf0022cbe7d251f39eb8bca77673f22fc7b65 Sim Zhen Quan <<EMAIL>> 1745043739 +0800	merge origin/main: Merge made by the 'ort' strategy.
615bf0022cbe7d251f39eb8bca77673f22fc7b65 1750e710732442cc41e8574cb50ad01d7e9889e2 Sim Zhen Quan <<EMAIL>> 1745046301 +0800	checkout: moving from dev to main
1750e710732442cc41e8574cb50ad01d7e9889e2 33e4bcbedbd0b76731f5bec4c345f214436e233e Sim Zhen Quan <<EMAIL>> 1745117051 +0800	checkout: moving from main to feature/daily-attendance-report
33e4bcbedbd0b76731f5bec4c345f214436e233e c9ecf79aecf9653463a625c0e3a36b0d77b7aec7 Sim Zhen Quan <<EMAIL>> 1745117061 +0800	merge origin/main: Merge made by the 'ort' strategy.
c9ecf79aecf9653463a625c0e3a36b0d77b7aec7 1750e710732442cc41e8574cb50ad01d7e9889e2 Sim Zhen Quan <<EMAIL>> 1745121593 +0800	checkout: moving from feature/daily-attendance-report to main
1750e710732442cc41e8574cb50ad01d7e9889e2 1750e710732442cc41e8574cb50ad01d7e9889e2 Sim Zhen Quan <<EMAIL>> 1745121670 +0800	checkout: moving from main to fix-attendance-posting
1750e710732442cc41e8574cb50ad01d7e9889e2 3f75e5dce55307444f1756fb55b88e646b63c66d Sim Zhen Quan <<EMAIL>> 1745121718 +0800	commit: Enhance service for attendance posting
3f75e5dce55307444f1756fb55b88e646b63c66d 1750e710732442cc41e8574cb50ad01d7e9889e2 Sim Zhen Quan <<EMAIL>> 1745123623 +0800	checkout: moving from fix-attendance-posting to main
1750e710732442cc41e8574cb50ad01d7e9889e2 1750e710732442cc41e8574cb50ad01d7e9889e2 Sim Zhen Quan <<EMAIL>> 1745123632 +0800	checkout: moving from main to staging/2025-04-20
1750e710732442cc41e8574cb50ad01d7e9889e2 3f75e5dce55307444f1756fb55b88e646b63c66d Sim Zhen Quan <<EMAIL>> 1745123646 +0800	merge fix-attendance-posting: Fast-forward
3f75e5dce55307444f1756fb55b88e646b63c66d c9ecf79aecf9653463a625c0e3a36b0d77b7aec7 Sim Zhen Quan <<EMAIL>> 1745123992 +0800	checkout: moving from staging/2025-04-20 to feature/daily-attendance-report
c9ecf79aecf9653463a625c0e3a36b0d77b7aec7 3378f3a1d86b024fc9067a93418dbe537a601834 Sim Zhen Quan <<EMAIL>> 1745124004 +0800	merge origin/staging/2025-04-20: Merge made by the 'ort' strategy.
3378f3a1d86b024fc9067a93418dbe537a601834 615bf0022cbe7d251f39eb8bca77673f22fc7b65 Sim Zhen Quan <<EMAIL>> 1745125074 +0800	checkout: moving from feature/daily-attendance-report to dev
615bf0022cbe7d251f39eb8bca77673f22fc7b65 34b49a7c584632b9864ed5436c23f9a04c968f2f Sim Zhen Quan <<EMAIL>> 1745125085 +0800	merge origin/staging/2025-04-20: Merge made by the 'ort' strategy.
34b49a7c584632b9864ed5436c23f9a04c968f2f f491b6a11ed14b39908d6c9dceb74350b8e64985 Sim Zhen Quan <<EMAIL>> 1745129484 +0800	commit: Deploy to dev
f491b6a11ed14b39908d6c9dceb74350b8e64985 812a53178348da4d3174ca418e458ab1ecde30aa Sim Zhen Quan <<EMAIL>> 1745129502 +0800	checkout: moving from dev to feature/class-attendance-taking-report
812a53178348da4d3174ca418e458ab1ecde30aa c6b1e520f1a9b04882346b46bc3e885c033265f4 Sim Zhen Quan <<EMAIL>> 1745129514 +0800	merge origin/staging/2025-04-20: Merge made by the 'ort' strategy.
c6b1e520f1a9b04882346b46bc3e885c033265f4 72dc0eb09fca223dbddfef6664e8810d12dc4664 Sim Zhen Quan <<EMAIL>> 1745131354 +0800	commit: Need to implement for timeslot override
72dc0eb09fca223dbddfef6664e8810d12dc4664 f491b6a11ed14b39908d6c9dceb74350b8e64985 Sim Zhen Quan <<EMAIL>> 1745131425 +0800	checkout: moving from feature/class-attendance-taking-report to dev
f491b6a11ed14b39908d6c9dceb74350b8e64985 18516b8d64ca2b232fa9f55688ec7d72b2d56a41 Sim Zhen Quan <<EMAIL>> 1745131437 +0800	merge origin/staging/2025-04-20: Merge made by the 'ort' strategy.
18516b8d64ca2b232fa9f55688ec7d72b2d56a41 c9ae5a5a8f06c7890004e1fb4d269214537e31a4 Sim Zhen Quan <<EMAIL>> 1745131504 +0800	checkout: moving from dev to feature/attendance-mark-deduction-report
c9ae5a5a8f06c7890004e1fb4d269214537e31a4 18516b8d64ca2b232fa9f55688ec7d72b2d56a41 Sim Zhen Quan <<EMAIL>> 1745131514 +0800	checkout: moving from feature/attendance-mark-deduction-report to dev
18516b8d64ca2b232fa9f55688ec7d72b2d56a41 ddaaf168a25c2270ad2b028c4006ec017525c71b Sim Zhen Quan <<EMAIL>> 1745131812 +0800	commit: Deployed to dev
ddaaf168a25c2270ad2b028c4006ec017525c71b ddaaf168a25c2270ad2b028c4006ec017525c71b Sim Zhen Quan <<EMAIL>> 1745131965 +0800	reset: moving to HEAD
ddaaf168a25c2270ad2b028c4006ec017525c71b c9ae5a5a8f06c7890004e1fb4d269214537e31a4 Sim Zhen Quan <<EMAIL>> 1745131966 +0800	checkout: moving from dev to feature/attendance-mark-deduction-report
c9ae5a5a8f06c7890004e1fb4d269214537e31a4 df666e4804ff8675ee545245974d6d06c4b8f7e5 Sim Zhen Quan <<EMAIL>> 1745131970 +0800	merge origin/staging/2025-04-20: Merge made by the 'ort' strategy.
df666e4804ff8675ee545245974d6d06c4b8f7e5 3cb7bec7e653b60a26bbc67d75374a4a584bd1de Sim Zhen Quan <<EMAIL>> 1745136247 +0800	commit: Review WIP
3cb7bec7e653b60a26bbc67d75374a4a584bd1de 3f75e5dce55307444f1756fb55b88e646b63c66d Sim Zhen Quan <<EMAIL>> 1745136277 +0800	checkout: moving from feature/attendance-mark-deduction-report to staging/2025-04-20
3f75e5dce55307444f1756fb55b88e646b63c66d 5e37d45a25313bec7345c02b405a5c02488c6a0b Sim Zhen Quan <<EMAIL>> 1745136301 +0800	commit: Added relationship include for new flag
5e37d45a25313bec7345c02b405a5c02488c6a0b 2123dfed7dfb4d9e58249cb0bb7134f729100bbc Sim Zhen Quan <<EMAIL>> 1745136403 +0800	checkout: moving from staging/2025-04-20 to report/attendance-report
2123dfed7dfb4d9e58249cb0bb7134f729100bbc ebd177ab08593a33f2108591c59a09f6a8c3e8bc Sim Zhen Quan <<EMAIL>> 1745136738 +0800	commit (merge): Merge remote-tracking branch 'origin/staging/2025-04-20' into report/attendance-report
ebd177ab08593a33f2108591c59a09f6a8c3e8bc ebd177ab08593a33f2108591c59a09f6a8c3e8bc Sim Zhen Quan <<EMAIL>> 1745136790 +0800	checkout: moving from report/attendance-report to report/attendance-report
ebd177ab08593a33f2108591c59a09f6a8c3e8bc b54fb741c760ccc3892c6748d8af300a316fb332 Sim Zhen Quan <<EMAIL>> 1745139867 +0800	commit: Reviewed
b54fb741c760ccc3892c6748d8af300a316fb332 1750e710732442cc41e8574cb50ad01d7e9889e2 Sim Zhen Quan <<EMAIL>> 1745139933 +0800	checkout: moving from report/attendance-report to main
1750e710732442cc41e8574cb50ad01d7e9889e2 ddaaf168a25c2270ad2b028c4006ec017525c71b Sim Zhen Quan <<EMAIL>> 1745139940 +0800	checkout: moving from main to dev
ddaaf168a25c2270ad2b028c4006ec017525c71b de5a454d5662962c30b26f21d39c3d8e3ba070b8 Sim Zhen Quan <<EMAIL>> 1745139951 +0800	merge origin/staging/2025-04-20: Merge made by the 'ort' strategy.
de5a454d5662962c30b26f21d39c3d8e3ba070b8 370d9a5661e6cdac120218da6bbade3c4eacd050 Sim Zhen Quan <<EMAIL>> 1745140108 +0800	commit: Deployed to DEV
370d9a5661e6cdac120218da6bbade3c4eacd050 30da17d76fae6ef08726f7882d3374d14470d0de Sim Zhen Quan <<EMAIL>> 1745140631 +0800	checkout: moving from dev to academy-student-analysis-report
30da17d76fae6ef08726f7882d3374d14470d0de 47944782ad2c7ead7cf1076b1120b5458e0ea7c6 Sim Zhen Quan <<EMAIL>> 1745140813 +0800	commit (merge): Merge remote-tracking branch 'origin/staging/2025-04-20' into academy-student-analysis-report
47944782ad2c7ead7cf1076b1120b5458e0ea7c6 47944782ad2c7ead7cf1076b1120b5458e0ea7c6 Sim Zhen Quan <<EMAIL>> 1745140950 +0800	checkout: moving from academy-student-analysis-report to academy-student-analysis-report
47944782ad2c7ead7cf1076b1120b5458e0ea7c6 47944782ad2c7ead7cf1076b1120b5458e0ea7c6 Sim Zhen Quan <<EMAIL>> 1745142076 +0800	checkout: moving from academy-student-analysis-report to academy-student-analysis-report
47944782ad2c7ead7cf1076b1120b5458e0ea7c6 907312674a34edd23b29554ccdb6b283c9037377 Sim Zhen Quan <<EMAIL>> 1745142768 +0800	commit: Reviewed
907312674a34edd23b29554ccdb6b283c9037377 1750e710732442cc41e8574cb50ad01d7e9889e2 Sim Zhen Quan <<EMAIL>> 1745142789 +0800	checkout: moving from academy-student-analysis-report to main
1750e710732442cc41e8574cb50ad01d7e9889e2 370d9a5661e6cdac120218da6bbade3c4eacd050 Sim Zhen Quan <<EMAIL>> 1745142794 +0800	checkout: moving from main to dev
370d9a5661e6cdac120218da6bbade3c4eacd050 2d8e5dcf65d5fdc117ce6f4a8a4ec8c0729142f0 Sim Zhen Quan <<EMAIL>> 1745142861 +0800	merge origin/staging/2025-04-20: Merge made by the 'ort' strategy.
2d8e5dcf65d5fdc117ce6f4a8a4ec8c0729142f0 8570bbc112f9fb7be74d4e5b2b3591ec8d46d03d Sim Zhen Quan <<EMAIL>> 1745143054 +0800	commit: Deployed to dev
8570bbc112f9fb7be74d4e5b2b3591ec8d46d03d 3a0d7fc253da027d7f03351a6e923745121bf683 Sim Zhen Quan <<EMAIL>> 1745143059 +0800	checkout: moving from dev to SCRUM-178-training-attendance-report
3a0d7fc253da027d7f03351a6e923745121bf683 45fdcba362e2bff9ded5d4fd0d81aa2ac2e4ae94 Sim Zhen Quan <<EMAIL>> 1745143290 +0800	commit (merge): Merge remote-tracking branch 'origin/staging/2025-04-20' into SCRUM-178-training-attendance-report
45fdcba362e2bff9ded5d4fd0d81aa2ac2e4ae94 45fdcba362e2bff9ded5d4fd0d81aa2ac2e4ae94 Sim Zhen Quan <<EMAIL>> 1745148150 +0800	reset: moving to HEAD
45fdcba362e2bff9ded5d4fd0d81aa2ac2e4ae94 1750e710732442cc41e8574cb50ad01d7e9889e2 Sim Zhen Quan <<EMAIL>> 1745148152 +0800	checkout: moving from SCRUM-178-training-attendance-report to main
1750e710732442cc41e8574cb50ad01d7e9889e2 1750e710732442cc41e8574cb50ad01d7e9889e2 Sim Zhen Quan <<EMAIL>> 1745148161 +0800	checkout: moving from main to migration-fixes
1750e710732442cc41e8574cb50ad01d7e9889e2 0deb82910fcfac54bbea47b0218b597329864d92 Sim Zhen Quan <<EMAIL>> 1745148197 +0800	commit: Added migratePeriodAttendancePlaceholderOnly
0deb82910fcfac54bbea47b0218b597329864d92 0deb82910fcfac54bbea47b0218b597329864d92 Sim Zhen Quan <<EMAIL>> 1745150417 +0800	reset: moving to HEAD
0deb82910fcfac54bbea47b0218b597329864d92 5e37d45a25313bec7345c02b405a5c02488c6a0b Sim Zhen Quan <<EMAIL>> 1745150418 +0800	checkout: moving from migration-fixes to staging/2025-04-20
5e37d45a25313bec7345c02b405a5c02488c6a0b 5e37d45a25313bec7345c02b405a5c02488c6a0b Sim Zhen Quan <<EMAIL>> 1745150478 +0800	checkout: moving from staging/2025-04-20 to staging/2025-04-20
5e37d45a25313bec7345c02b405a5c02488c6a0b 5e37d45a25313bec7345c02b405a5c02488c6a0b Sim Zhen Quan <<EMAIL>> 1745150514 +0800	checkout: moving from staging/2025-04-20 to staging/2025-04-20
5e37d45a25313bec7345c02b405a5c02488c6a0b 1750e710732442cc41e8574cb50ad01d7e9889e2 Sim Zhen Quan <<EMAIL>> 1745150687 +0800	checkout: moving from staging/2025-04-20 to main
1750e710732442cc41e8574cb50ad01d7e9889e2 5e37d45a25313bec7345c02b405a5c02488c6a0b Sim Zhen Quan <<EMAIL>> 1745150695 +0800	checkout: moving from main to staging/2025-04-20
5e37d45a25313bec7345c02b405a5c02488c6a0b 5e37d45a25313bec7345c02b405a5c02488c6a0b Sim Zhen Quan <<EMAIL>> 1745150722 +0800	reset: moving to HEAD
5e37d45a25313bec7345c02b405a5c02488c6a0b c8b2d2872a4e22d180530043264761bb28e046d1 Sim Zhen Quan <<EMAIL>> 1745150886 +0800	pull: Merge made by the 'ort' strategy.
c8b2d2872a4e22d180530043264761bb28e046d1 1530f28359caf1c251a2a5179b781c4ce45bd192 Sim Zhen Quan <<EMAIL>> 1745151994 +0800	checkout: moving from staging/2025-04-20 to path-leave-application-individual-override
1530f28359caf1c251a2a5179b781c4ce45bd192 98af6e2060522aec6c7b9606c1caee5357b248e3 Sim Zhen Quan <<EMAIL>> 1745152012 +0800	merge origin/staging/2025-04-20: Merge made by the 'ort' strategy.
98af6e2060522aec6c7b9606c1caee5357b248e3 8570bbc112f9fb7be74d4e5b2b3591ec8d46d03d Sim Zhen Quan <<EMAIL>> 1745161914 +0800	checkout: moving from path-leave-application-individual-override to dev
8570bbc112f9fb7be74d4e5b2b3591ec8d46d03d 402a1a25364b8703c6d3c618147b780972db6f1d Sim Zhen Quan <<EMAIL>> 1745161935 +0800	merge migration-fixes: Merge made by the 'ort' strategy.
402a1a25364b8703c6d3c618147b780972db6f1d ec6465f0fa959f65f69d84254564eb704b115043 Sim Zhen Quan <<EMAIL>> 1745162167 +0800	merge origin/migration-fixes: Merge made by the 'ort' strategy.
ec6465f0fa959f65f69d84254564eb704b115043 ffec0219f1dbfc5487691af800de32428c8faa91 Sim Zhen Quan <<EMAIL>> 1745165092 +0800	commit: Deployed to DEV
ffec0219f1dbfc5487691af800de32428c8faa91 98af6e2060522aec6c7b9606c1caee5357b248e3 Sim Zhen Quan <<EMAIL>> 1745165337 +0800	checkout: moving from dev to path-leave-application-individual-override
98af6e2060522aec6c7b9606c1caee5357b248e3 c8b2d2872a4e22d180530043264761bb28e046d1 Sim Zhen Quan <<EMAIL>> 1745166052 +0800	checkout: moving from path-leave-application-individual-override to staging/2025-04-20
c8b2d2872a4e22d180530043264761bb28e046d1 d1ddd1693ec52399af00a51ef91056351344b702 Sim Zhen Quan <<EMAIL>> 1745166059 +0800	merge origin/main: Merge made by the 'ort' strategy.
d1ddd1693ec52399af00a51ef91056351344b702 1750e710732442cc41e8574cb50ad01d7e9889e2 Sim Zhen Quan <<EMAIL>> 1745167631 +0800	checkout: moving from staging/2025-04-20 to main
1750e710732442cc41e8574cb50ad01d7e9889e2 92db3a8929dbf699e904d58aac529e8dd8602f3d Sim Zhen Quan <<EMAIL>> 1745167637 +0800	pull: Fast-forward
92db3a8929dbf699e904d58aac529e8dd8602f3d bd27fadffd2273ee5897a23741b6cda3a0ba3692 Sim Zhen Quan <<EMAIL>> 1745201063 +0800	pull: Fast-forward
bd27fadffd2273ee5897a23741b6cda3a0ba3692 d8df21279f1f0bd502546519f2a4eb3b28086aab Sim Zhen Quan <<EMAIL>> 1745201814 +0800	commit: Allow filter by late attendance also
d8df21279f1f0bd502546519f2a4eb3b28086aab ffec0219f1dbfc5487691af800de32428c8faa91 Sim Zhen Quan <<EMAIL>> 1745201824 +0800	checkout: moving from main to dev
ffec0219f1dbfc5487691af800de32428c8faa91 c98f77f26232894d0719910f90060fe1631b6b4c Sim Zhen Quan <<EMAIL>> 1745201838 +0800	merge origin/main: Merge made by the 'ort' strategy.
c98f77f26232894d0719910f90060fe1631b6b4c 2e53166ce929c38f5490acadcaf01e28b8888205 Sim Zhen Quan <<EMAIL>> 1745204423 +0800	commit: Deployed to dev
2e53166ce929c38f5490acadcaf01e28b8888205 d8df21279f1f0bd502546519f2a4eb3b28086aab Sim Zhen Quan <<EMAIL>> 1745204451 +0800	checkout: moving from dev to main
d8df21279f1f0bd502546519f2a4eb3b28086aab 7d1807feb8e037d564a1f9169b2184db18167cb7 Sim Zhen Quan <<EMAIL>> 1745204456 +0800	pull: Fast-forward
7d1807feb8e037d564a1f9169b2184db18167cb7 edb1e9a85bdbebf5df03babb3d2dbeef4d827623 Sim Zhen Quan <<EMAIL>> 1745228186 +0800	pull: Fast-forward
edb1e9a85bdbebf5df03babb3d2dbeef4d827623 dad60aa80fef74f84ff2c7cf750edbfcd649272f Sim Zhen Quan <<EMAIL>> 1745228189 +0800	commit: Deployed to prd
dad60aa80fef74f84ff2c7cf750edbfcd649272f dad60aa80fef74f84ff2c7cf750edbfcd649272f Sim Zhen Quan <<EMAIL>> 1745228212 +0800	checkout: moving from main to fix-migration-english-class
dad60aa80fef74f84ff2c7cf750edbfcd649272f 2649f9e1b6ea553a7225e12d1b21d9f0f3bac8ca Sim Zhen Quan <<EMAIL>> 1745231168 +0800	commit: Added patch for english class and primary class placeholder
2649f9e1b6ea553a7225e12d1b21d9f0f3bac8ca 2649f9e1b6ea553a7225e12d1b21d9f0f3bac8ca Sim Zhen Quan <<EMAIL>> 1745238724 +0800	checkout: moving from fix-migration-english-class to staging/2025-04-21
2649f9e1b6ea553a7225e12d1b21d9f0f3bac8ca 429b34422c35b2be30dafbe7756043e127ff7d63 Sim Zhen Quan <<EMAIL>> 1745238771 +0800	checkout: moving from staging/2025-04-21 to class-attendance-taking-is-editable-check-school-attendance-status
429b34422c35b2be30dafbe7756043e127ff7d63 a2c3f5b75ed2db8006e8d5b775627c157c516af5 Sim Zhen Quan <<EMAIL>> 1745238783 +0800	merge origin/main: Merge made by the 'ort' strategy.
a2c3f5b75ed2db8006e8d5b775627c157c516af5 f451a18c90c71936e3ea882f1d3eaeb31aaf6422 Sim Zhen Quan <<EMAIL>> 1745239376 +0800	checkout: moving from class-attendance-taking-is-editable-check-school-attendance-status to substitute-teacher-timeslot-display-period-label
f451a18c90c71936e3ea882f1d3eaeb31aaf6422 2649f9e1b6ea553a7225e12d1b21d9f0f3bac8ca Sim Zhen Quan <<EMAIL>> 1745239386 +0800	checkout: moving from substitute-teacher-timeslot-display-period-label to staging/2025-04-21
2649f9e1b6ea553a7225e12d1b21d9f0f3bac8ca 853c77ca7f2658924cbe984060bbae3935ed8dfd Sim Zhen Quan <<EMAIL>> 1745239391 +0800	pull: Fast-forward
853c77ca7f2658924cbe984060bbae3935ed8dfd f451a18c90c71936e3ea882f1d3eaeb31aaf6422 Sim Zhen Quan <<EMAIL>> 1745239397 +0800	checkout: moving from staging/2025-04-21 to substitute-teacher-timeslot-display-period-label
f451a18c90c71936e3ea882f1d3eaeb31aaf6422 853c77ca7f2658924cbe984060bbae3935ed8dfd Sim Zhen Quan <<EMAIL>> 1745239542 +0800	checkout: moving from substitute-teacher-timeslot-display-period-label to staging/2025-04-21
853c77ca7f2658924cbe984060bbae3935ed8dfd f451a18c90c71936e3ea882f1d3eaeb31aaf6422 Sim Zhen Quan <<EMAIL>> 1745239551 +0800	checkout: moving from staging/2025-04-21 to substitute-teacher-timeslot-display-period-label
f451a18c90c71936e3ea882f1d3eaeb31aaf6422 5355bf9ddd07f4769ac8b8b59151ec9e3967f012 Sim Zhen Quan <<EMAIL>> 1745239569 +0800	merge origin/staging/2025-04-21: Merge made by the 'ort' strategy.
5355bf9ddd07f4769ac8b8b59151ec9e3967f012 046fb2133ff9e821364b471792815ca3af275e4a Sim Zhen Quan <<EMAIL>> 1745244888 +0800	commit: Reviewed
046fb2133ff9e821364b471792815ca3af275e4a 853c77ca7f2658924cbe984060bbae3935ed8dfd Sim Zhen Quan <<EMAIL>> 1745244991 +0800	checkout: moving from substitute-teacher-timeslot-display-period-label to staging/2025-04-21
853c77ca7f2658924cbe984060bbae3935ed8dfd a941e967b7366dcc0282360ae6ea0fa38192ab7d Sim Zhen Quan <<EMAIL>> 1745244996 +0800	pull: Fast-forward
a941e967b7366dcc0282360ae6ea0fa38192ab7d 095bc07fe294ba92bb9759a8308739a007a4d05f Sim Zhen Quan <<EMAIL>> 1745244997 +0800	merge origin/main: Merge made by the 'ort' strategy.
095bc07fe294ba92bb9759a8308739a007a4d05f 2e53166ce929c38f5490acadcaf01e28b8888205 Sim Zhen Quan <<EMAIL>> 1745245073 +0800	checkout: moving from staging/2025-04-21 to dev
2e53166ce929c38f5490acadcaf01e28b8888205 d987dc10b90d16fa9b5ac8338a9dd98d312c5423 Sim Zhen Quan <<EMAIL>> 1745245089 +0800	merge staging/2025-04-21: Merge made by the 'ort' strategy.
d987dc10b90d16fa9b5ac8338a9dd98d312c5423 f27433f58c1eb53b8ffb1c39c8a59a33e449a9fd Sim Zhen Quan <<EMAIL>> 1745245623 +0800	commit: Deployed to dev
f27433f58c1eb53b8ffb1c39c8a59a33e449a9fd 65e84b42c8dac823e70c1c83416f5fcffb8db432 Sim Zhen Quan <<EMAIL>> 1745246206 +0800	merge origin/main: Merge made by the 'ort' strategy.
65e84b42c8dac823e70c1c83416f5fcffb8db432 095bc07fe294ba92bb9759a8308739a007a4d05f Sim Zhen Quan <<EMAIL>> 1745248532 +0800	checkout: moving from dev to staging/2025-04-21
095bc07fe294ba92bb9759a8308739a007a4d05f bdad9f65d99324a6b2a425167fcb16b8a28bd35d Sim Zhen Quan <<EMAIL>> 1745248538 +0800	merge origin/main: Merge made by the 'ort' strategy.
bdad9f65d99324a6b2a425167fcb16b8a28bd35d 7475f6310adb76ace109c59a70b8bbe07e6a8037 Sim Zhen Quan <<EMAIL>> 1745249717 +0800	commit: Revert eager loading
7475f6310adb76ace109c59a70b8bbe07e6a8037 dad60aa80fef74f84ff2c7cf750edbfcd649272f Sim Zhen Quan <<EMAIL>> 1745249740 +0800	checkout: moving from staging/2025-04-21 to main
dad60aa80fef74f84ff2c7cf750edbfcd649272f 3a552cd0f666ccc554d1c6174e4c131dba90a27e Sim Zhen Quan <<EMAIL>> 1745249748 +0800	pull: Fast-forward
3a552cd0f666ccc554d1c6174e4c131dba90a27e a420605c2b6183706543c11dcdb3c20156e80498 Sim Zhen Quan <<EMAIL>> 1745249959 +0800	commit: Deployed to PRD
a420605c2b6183706543c11dcdb3c20156e80498 b1f91ae8256e73d7fd9992157b486bae7c2f9e67 Sim Zhen Quan <<EMAIL>> 1745289216 +0800	pull: Fast-forward
b1f91ae8256e73d7fd9992157b486bae7c2f9e67 991072c655211e4651bda0577cf7957591038bb2 Sim Zhen Quan <<EMAIL>> 1745289220 +0800	checkout: moving from main to exam-gross-average
991072c655211e4651bda0577cf7957591038bb2 fa52e9b003c24908f5bc870f9a352a5c51b44819 Sim Zhen Quan <<EMAIL>> 1745289229 +0800	merge origin/main: Merge made by the 'ort' strategy.
fa52e9b003c24908f5bc870f9a352a5c51b44819 fa52e9b003c24908f5bc870f9a352a5c51b44819 Sim Zhen Quan <<EMAIL>> 1745289380 +0800	checkout: moving from exam-gross-average to exam-module-changes
fa52e9b003c24908f5bc870f9a352a5c51b44819 3044f5b5dc070cf2f0edd5fdc2c43a39357ea9b9 Sim Zhen Quan <<EMAIL>> 1745289507 +0800	checkout: moving from exam-module-changes to exam-expire-student-framework
3044f5b5dc070cf2f0edd5fdc2c43a39357ea9b9 eb5ef264e51faafbf53ef0c8c2be55f0a8e9ff5e Sim Zhen Quan <<EMAIL>> 1745289515 +0800	merge exam-module-changes: Merge made by the 'ort' strategy.
eb5ef264e51faafbf53ef0c8c2be55f0a8e9ff5e 385f173a542fb05b8a66ee1917253c4737de00ca Sim Zhen Quan <<EMAIL>> 1745292843 +0800	commit: Reviewed
385f173a542fb05b8a66ee1917253c4737de00ca b1f91ae8256e73d7fd9992157b486bae7c2f9e67 Sim Zhen Quan <<EMAIL>> 1745298890 +0800	checkout: moving from exam-expire-student-framework to main
b1f91ae8256e73d7fd9992157b486bae7c2f9e67 b1f91ae8256e73d7fd9992157b486bae7c2f9e67 Sim Zhen Quan <<EMAIL>> 1745300055 +0800	checkout: moving from main to main
b1f91ae8256e73d7fd9992157b486bae7c2f9e67 b1f91ae8256e73d7fd9992157b486bae7c2f9e67 Sim Zhen Quan <<EMAIL>> 1745300069 +0800	checkout: moving from main to staging/2025-04-22
b1f91ae8256e73d7fd9992157b486bae7c2f9e67 a4f94999e656203b7b592a8fbb11c547313dc485 Sim Zhen Quan <<EMAIL>> 1745300163 +0800	checkout: moving from staging/2025-04-22 to daily-collection-report-total-and-net-amount
a4f94999e656203b7b592a8fbb11c547313dc485 8e7379d9fea58d7b23632a6df372e57ad8481e22 Sim Zhen Quan <<EMAIL>> 1745300571 +0800	commit: Reviewed
8e7379d9fea58d7b23632a6df372e57ad8481e22 3d2b1b219762f4376edd406637a55996b4625025 Sim Zhen Quan <<EMAIL>> 1745306659 +0800	checkout: moving from daily-collection-report-total-and-net-amount to student-absent-report-enhancement
3d2b1b219762f4376edd406637a55996b4625025 45fdcba362e2bff9ded5d4fd0d81aa2ac2e4ae94 Sim Zhen Quan <<EMAIL>> 1745307317 +0800	checkout: moving from student-absent-report-enhancement to SCRUM-178-training-attendance-report
45fdcba362e2bff9ded5d4fd0d81aa2ac2e4ae94 8c4ee3133e3d79ca4fa5ce67cd1c40c0ae607d0a Sim Zhen Quan <<EMAIL>> 1745307386 +0800	commit (merge): Merge branch 'staging/2025-04-22' into SCRUM-178-training-attendance-report
8c4ee3133e3d79ca4fa5ce67cd1c40c0ae607d0a 3eae46667bcf88f256351a3e26531b2c15fe0c3c Sim Zhen Quan <<EMAIL>> 1745307410 +0800	merge origin/staging/2025-04-22: Merge made by the 'ort' strategy.
3eae46667bcf88f256351a3e26531b2c15fe0c3c 2e13f1d59f403b83e6f5de1d51ab5a4c1671c61b Sim Zhen Quan <<EMAIL>> 1745307458 +0800	checkout: moving from SCRUM-178-training-attendance-report to bugfix/library-book-loan-report-employee-class-relation
2e13f1d59f403b83e6f5de1d51ab5a4c1671c61b 3d8efb33aabf1e91600830691028954488208cfb Sim Zhen Quan <<EMAIL>> 1745307480 +0800	merge origin/staging/2025-04-22: Merge made by the 'ort' strategy.
3d8efb33aabf1e91600830691028954488208cfb 754cc351c16360487b5e5cfc74483adba3fa7c87 Sim Zhen Quan <<EMAIL>> 1745308651 +0800	commit: Fix query
754cc351c16360487b5e5cfc74483adba3fa7c87 c969d3c6a10297a8fd28d194d2a93e0bed39c764 Sim Zhen Quan <<EMAIL>> 1745309078 +0800	checkout: moving from bugfix/library-book-loan-report-employee-class-relation to issuelog-40-prevent-void-paid-invoices
c969d3c6a10297a8fd28d194d2a93e0bed39c764 b1f91ae8256e73d7fd9992157b486bae7c2f9e67 Sim Zhen Quan <<EMAIL>> 1745310455 +0800	checkout: moving from issuelog-40-prevent-void-paid-invoices to staging/2025-04-22
b1f91ae8256e73d7fd9992157b486bae7c2f9e67 aff45193ef22474a6ccebe4e1994effa40cd201f Sim Zhen Quan <<EMAIL>> 1745310462 +0800	pull: Fast-forward
aff45193ef22474a6ccebe4e1994effa40cd201f 687cc351d2a63b3afcbd40f7be09a9c406cba247 Sim Zhen Quan <<EMAIL>> 1745310526 +0800	commit: Add student_number_wildcard to form request
687cc351d2a63b3afcbd40f7be09a9c406cba247 3eae46667bcf88f256351a3e26531b2c15fe0c3c Sim Zhen Quan <<EMAIL>> 1745310696 +0800	checkout: moving from staging/2025-04-22 to SCRUM-178-training-attendance-report
3eae46667bcf88f256351a3e26531b2c15fe0c3c 8f746462bafc1dcea7240b76582a0b0b834a8b30 Sim Zhen Quan <<EMAIL>> 1745310705 +0800	merge origin/staging/2025-04-22: Merge made by the 'ort' strategy.
8f746462bafc1dcea7240b76582a0b0b834a8b30 6ec487d3972bc31b1988835154d429d2f5303af1 Sim Zhen Quan <<EMAIL>> 1745312926 +0800	commit: Reviewed
6ec487d3972bc31b1988835154d429d2f5303af1 65e84b42c8dac823e70c1c83416f5fcffb8db432 Sim Zhen Quan <<EMAIL>> 1745313082 +0800	checkout: moving from SCRUM-178-training-attendance-report to dev
65e84b42c8dac823e70c1c83416f5fcffb8db432 687cc351d2a63b3afcbd40f7be09a9c406cba247 Sim Zhen Quan <<EMAIL>> 1745313092 +0800	checkout: moving from dev to staging/2025-04-22
687cc351d2a63b3afcbd40f7be09a9c406cba247 b31800e6eb9bffa4f6b25db6eb6f7a8eafcea310 Sim Zhen Quan <<EMAIL>> 1745313098 +0800	pull: Fast-forward
b31800e6eb9bffa4f6b25db6eb6f7a8eafcea310 65e84b42c8dac823e70c1c83416f5fcffb8db432 Sim Zhen Quan <<EMAIL>> 1745313107 +0800	checkout: moving from staging/2025-04-22 to dev
65e84b42c8dac823e70c1c83416f5fcffb8db432 b31800e6eb9bffa4f6b25db6eb6f7a8eafcea310 Sim Zhen Quan <<EMAIL>> 1745313120 +0800	checkout: moving from dev to staging/2025-04-22
b31800e6eb9bffa4f6b25db6eb6f7a8eafcea310 65e84b42c8dac823e70c1c83416f5fcffb8db432 Sim Zhen Quan <<EMAIL>> 1745313127 +0800	checkout: moving from staging/2025-04-22 to dev
65e84b42c8dac823e70c1c83416f5fcffb8db432 7035b3e11b37d08f57322c8c935b99873592b2e4 Sim Zhen Quan <<EMAIL>> 1745313138 +0800	merge origin/staging/2025-04-22: Merge made by the 'ort' strategy.
7035b3e11b37d08f57322c8c935b99873592b2e4 52eb30d8571996330da5d5c33a26d357f26539af Sim Zhen Quan <<EMAIL>> 1745317081 +0800	commit: Deployed to dev
52eb30d8571996330da5d5c33a26d357f26539af 0b1eccd1255b4e4ddc8a1010c8f0fbf9a3e19eff Sim Zhen Quan <<EMAIL>> 1745317268 +0800	checkout: moving from dev to discount-userable-add-more-details
0b1eccd1255b4e4ddc8a1010c8f0fbf9a3e19eff 164774b881ddaffb932950749049b815650b38ee Sim Zhen Quan <<EMAIL>> 1745317282 +0800	merge origin/staging/2025-04-22: Merge made by the 'ort' strategy.
164774b881ddaffb932950749049b815650b38ee c7ae49aa14cca4d8df97f1d6aff38d082ae9c63e Sim Zhen Quan <<EMAIL>> 1745331046 +0800	commit: Reviewed
c7ae49aa14cca4d8df97f1d6aff38d082ae9c63e b1f91ae8256e73d7fd9992157b486bae7c2f9e67 Sim Zhen Quan <<EMAIL>> 1745335794 +0800	checkout: moving from discount-userable-add-more-details to main
b1f91ae8256e73d7fd9992157b486bae7c2f9e67 b0e418323088db9153b22b408d4834221452118c Sim Zhen Quan <<EMAIL>> 1745335806 +0800	pull: Fast-forward
b0e418323088db9153b22b408d4834221452118c 889610d2cbcea65247e5cf9d30f79bfb8ec99834 Sim Zhen Quan <<EMAIL>> 1745335924 +0800	commit: Added patch for leave early period correction
889610d2cbcea65247e5cf9d30f79bfb8ec99834 e90ce7169e07685db944c78bc6632c38c73813f7 Sim Zhen Quan <<EMAIL>> 1745336118 +0800	commit: Added patch period 15 incorrect attendance status
e90ce7169e07685db944c78bc6632c38c73813f7 4e4000feda7663f097bf766a7c9e56d2a3cf7c9c Sim Zhen Quan <<EMAIL>> 1745336134 +0800	checkout: moving from main to attendance-posting-auto-create-leave-application
4e4000feda7663f097bf766a7c9e56d2a3cf7c9c d1ddd1693ec52399af00a51ef91056351344b702 Sim Zhen Quan <<EMAIL>> 1745336148 +0800	checkout: moving from attendance-posting-auto-create-leave-application to staging/2025-04-20
d1ddd1693ec52399af00a51ef91056351344b702 e90ce7169e07685db944c78bc6632c38c73813f7 Sim Zhen Quan <<EMAIL>> 1745336162 +0800	merge origin/main: Fast-forward
e90ce7169e07685db944c78bc6632c38c73813f7 4e4000feda7663f097bf766a7c9e56d2a3cf7c9c Sim Zhen Quan <<EMAIL>> 1745336172 +0800	checkout: moving from staging/2025-04-20 to attendance-posting-auto-create-leave-application
4e4000feda7663f097bf766a7c9e56d2a3cf7c9c ecd65e631007eff3be75d9705e288f98de8a2df9 Sim Zhen Quan <<EMAIL>> 1745336187 +0800	merge origin/staging/2025-04-22: Merge made by the 'ort' strategy.
ecd65e631007eff3be75d9705e288f98de8a2df9 46ad5b291d5bc2e320a61af615fc19713b1932e9 Sim Zhen Quan <<EMAIL>> 1745341445 +0800	commit: Reviewed
46ad5b291d5bc2e320a61af615fc19713b1932e9 e90ce7169e07685db944c78bc6632c38c73813f7 Sim Zhen Quan <<EMAIL>> 1745341814 +0800	checkout: moving from attendance-posting-auto-create-leave-application to main
e90ce7169e07685db944c78bc6632c38c73813f7 e843c2078c1cf7147bc4b1fbccfb3384be06444c Sim Zhen Quan <<EMAIL>> 1745341820 +0800	pull: Fast-forward
e843c2078c1cf7147bc4b1fbccfb3384be06444c 071181cc25fbce84151d14a6c95c2ea8acbf57c8 Sim Zhen Quan <<EMAIL>> 1745343230 +0800	commit: Deployed to PRD
071181cc25fbce84151d14a6c95c2ea8acbf57c8 3cb7bec7e653b60a26bbc67d75374a4a584bd1de Sim Zhen Quan <<EMAIL>> 1745393081 +0800	checkout: moving from main to feature/attendance-mark-deduction-report
3cb7bec7e653b60a26bbc67d75374a4a584bd1de 6c0a36d7eb35fe4458df7c2a9eb13e1fd54012d2 Sim Zhen Quan <<EMAIL>> 1745393090 +0800	pull: Fast-forward
6c0a36d7eb35fe4458df7c2a9eb13e1fd54012d2 6c0a36d7eb35fe4458df7c2a9eb13e1fd54012d2 Sim Zhen Quan <<EMAIL>> 1745395098 +0800	reset: moving to HEAD
6c0a36d7eb35fe4458df7c2a9eb13e1fd54012d2 31fd5f63530721dced288c228c419af1e7b89154 Sim Zhen Quan <<EMAIL>> 1745401262 +0800	commit: Added view table
31fd5f63530721dced288c228c419af1e7b89154 ad9a8ec4b83f3ba0472b39a209bbf1081c8ce099 Sim Zhen Quan <<EMAIL>> 1745402076 +0800	checkout: moving from feature/attendance-mark-deduction-report to fix/billing-doc-payment-ref-no-filter
ad9a8ec4b83f3ba0472b39a209bbf1081c8ce099 071181cc25fbce84151d14a6c95c2ea8acbf57c8 Sim Zhen Quan <<EMAIL>> 1745402087 +0800	checkout: moving from fix/billing-doc-payment-ref-no-filter to main
071181cc25fbce84151d14a6c95c2ea8acbf57c8 071181cc25fbce84151d14a6c95c2ea8acbf57c8 Sim Zhen Quan <<EMAIL>> 1745402103 +0800	checkout: moving from main to staging/2025-04-23
071181cc25fbce84151d14a6c95c2ea8acbf57c8 ad9a8ec4b83f3ba0472b39a209bbf1081c8ce099 Sim Zhen Quan <<EMAIL>> 1745402117 +0800	checkout: moving from staging/2025-04-23 to fix/billing-doc-payment-ref-no-filter
ad9a8ec4b83f3ba0472b39a209bbf1081c8ce099 31fd5f63530721dced288c228c419af1e7b89154 Sim Zhen Quan <<EMAIL>> 1745407259 +0800	checkout: moving from fix/billing-doc-payment-ref-no-filter to feature/attendance-mark-deduction-report
31fd5f63530721dced288c228c419af1e7b89154 57e9c88ee17fe913e197a2986ec4399a4695a548 Sim Zhen Quan <<EMAIL>> 1745419487 +0800	pull: Fast-forward
57e9c88ee17fe913e197a2986ec4399a4695a548 bbdb9c5dbca3b195d0c1eb3e6c498d16e695c0b6 Sim Zhen Quan <<EMAIL>> 1745421915 +0800	commit: Reviewed
bbdb9c5dbca3b195d0c1eb3e6c498d16e695c0b6 52eb30d8571996330da5d5c33a26d357f26539af Sim Zhen Quan <<EMAIL>> 1745422086 +0800	checkout: moving from feature/attendance-mark-deduction-report to dev
52eb30d8571996330da5d5c33a26d357f26539af 071181cc25fbce84151d14a6c95c2ea8acbf57c8 Sim Zhen Quan <<EMAIL>> 1745422146 +0800	checkout: moving from dev to staging/2025-04-23
071181cc25fbce84151d14a6c95c2ea8acbf57c8 748b75b177af53897c02b95f08d2216fa0619db4 Sim Zhen Quan <<EMAIL>> 1745422154 +0800	pull: Fast-forward
748b75b177af53897c02b95f08d2216fa0619db4 52eb30d8571996330da5d5c33a26d357f26539af Sim Zhen Quan <<EMAIL>> 1745422173 +0800	checkout: moving from staging/2025-04-23 to dev
52eb30d8571996330da5d5c33a26d357f26539af 34cb12d30995821f9d675823c415c9febbb52c73 Sim Zhen Quan <<EMAIL>> 1745422177 +0800	merge staging/2025-04-23: Merge made by the 'ort' strategy.
34cb12d30995821f9d675823c415c9febbb52c73 5f7ee6ace3ba62e82fc94ce24f6266fc17a586a4 Sim Zhen Quan <<EMAIL>> 1745422891 +0800	commit: Deployed to dev
5f7ee6ace3ba62e82fc94ce24f6266fc17a586a4 3d2b1b219762f4376edd406637a55996b4625025 Sim Zhen Quan <<EMAIL>> 1745422924 +0800	checkout: moving from dev to student-absent-report-enhancement
3d2b1b219762f4376edd406637a55996b4625025 e60a81ef6a71701de390ee3679a4665539d68c7d Sim Zhen Quan <<EMAIL>> 1745422929 +0800	pull: Fast-forward
e60a81ef6a71701de390ee3679a4665539d68c7d bd05212173c3d1c60f8bf2368a8aba5ebd2cb674 Sim Zhen Quan <<EMAIL>> 1745423009 +0800	commit (merge): Merge remote-tracking branch 'origin/main' into student-absent-report-enhancement
bd05212173c3d1c60f8bf2368a8aba5ebd2cb674 95a90de239d77148f27c822b8edf689252998681 Sim Zhen Quan <<EMAIL>> 1745424341 +0800	commit: Reviewed
95a90de239d77148f27c822b8edf689252998681 748b75b177af53897c02b95f08d2216fa0619db4 Sim Zhen Quan <<EMAIL>> 1745424360 +0800	checkout: moving from student-absent-report-enhancement to staging/2025-04-23
748b75b177af53897c02b95f08d2216fa0619db4 988f3ef3db7742061eea5751b67885aca4035e32 Sim Zhen Quan <<EMAIL>> 1745424524 +0800	commit (merge): Merge branch 'student-absent-report-enhancement' into staging/2025-04-23
988f3ef3db7742061eea5751b67885aca4035e32 5f7ee6ace3ba62e82fc94ce24f6266fc17a586a4 Sim Zhen Quan <<EMAIL>> 1745424570 +0800	checkout: moving from staging/2025-04-23 to dev
5f7ee6ace3ba62e82fc94ce24f6266fc17a586a4 cb54783092d12a4512bff421352fcd037764a2d5 Sim Zhen Quan <<EMAIL>> 1745424579 +0800	merge origin/staging/2025-04-23: Merge made by the 'ort' strategy.
cb54783092d12a4512bff421352fcd037764a2d5 1694df83f96224936f6bb7c8c1b55f6826bd4493 Sim Zhen Quan <<EMAIL>> 1745424977 +0800	commit: Deployed to dev
1694df83f96224936f6bb7c8c1b55f6826bd4493 44292e6439cf8301cc1cb8796be57969b3648941 Sim Zhen Quan <<EMAIL>> 1745424984 +0800	checkout: moving from dev to tap-card-trigger-attendance-posting
44292e6439cf8301cc1cb8796be57969b3648941 5a9b5edba7111685bd4566663f48ea53da1d16f0 Sim Zhen Quan <<EMAIL>> 1745424997 +0800	merge origin/staging/2025-04-23: Merge made by the 'ort' strategy.
5a9b5edba7111685bd4566663f48ea53da1d16f0 762896bf1f9fe9e63850d59c2fcd27f89bb59086 Sim Zhen Quan <<EMAIL>> 1745428022 +0800	commit: Reviewed
762896bf1f9fe9e63850d59c2fcd27f89bb59086 1694df83f96224936f6bb7c8c1b55f6826bd4493 Sim Zhen Quan <<EMAIL>> 1745428218 +0800	checkout: moving from tap-card-trigger-attendance-posting to dev
1694df83f96224936f6bb7c8c1b55f6826bd4493 823bbc3907d1f1271e3a2be624feb8b37443f51e Sim Zhen Quan <<EMAIL>> 1745428231 +0800	merge origin/staging/2025-04-23: Merge made by the 'ort' strategy.
823bbc3907d1f1271e3a2be624feb8b37443f51e 9a4cab24e24e99ddf578cb8c7f4cba337a17c3cd Sim Zhen Quan <<EMAIL>> 1745429685 +0800	commit: Deployed to dev
9a4cab24e24e99ddf578cb8c7f4cba337a17c3cd 071181cc25fbce84151d14a6c95c2ea8acbf57c8 Sim Zhen Quan <<EMAIL>> 1745429823 +0800	checkout: moving from dev to main
071181cc25fbce84151d14a6c95c2ea8acbf57c8 a04fbf5406e7bbd01735edd7e00cd4083a6941b8 Sim Zhen Quan <<EMAIL>> 1745429829 +0800	pull: Fast-forward
a04fbf5406e7bbd01735edd7e00cd4083a6941b8 173d0a084e0186f413078de50eafaa7246430390 Sim Zhen Quan <<EMAIL>> 1745460720 +0800	commit: Deployed to prd
173d0a084e0186f413078de50eafaa7246430390 d1e5efb5e2cd091c13bd3e63e8808a5c2ed50824 Sim Zhen Quan <<EMAIL>> 1745461026 +0800	checkout: moving from main to fix/billing-document-report-for-product
d1e5efb5e2cd091c13bd3e63e8808a5c2ed50824 698138a3a71845c7f75e729d1e869e431563b2bd Sim Zhen Quan <<EMAIL>> 1745461032 +0800	merge origin/main: Merge made by the 'ort' strategy.
698138a3a71845c7f75e729d1e869e431563b2bd 1392e6567ef60464a2539ab9a4e716e97ea82958 Sim Zhen Quan <<EMAIL>> 1745463926 +0800	checkout: moving from fix/billing-document-report-for-product to fix/attendance-mark-deduction-report
1392e6567ef60464a2539ab9a4e716e97ea82958 175dddb259244892ea2be8072a62e141c5cd61e8 Sim Zhen Quan <<EMAIL>> 1745483535 +0800	checkout: moving from fix/attendance-mark-deduction-report to issuelog-25-hostel-savings-see-transaction-balance
175dddb259244892ea2be8072a62e141c5cd61e8 749a7523adf4fad36bb5a988cdd1af7ab862bbfb Sim Zhen Quan <<EMAIL>> 1745483547 +0800	merge origin/main: Merge made by the 'ort' strategy.
749a7523adf4fad36bb5a988cdd1af7ab862bbfb 173d0a084e0186f413078de50eafaa7246430390 Sim Zhen Quan <<EMAIL>> 1745483562 +0800	checkout: moving from issuelog-25-hostel-savings-see-transaction-balance to main
173d0a084e0186f413078de50eafaa7246430390 72b23a17fc9dae7ec7cf713667cd83b546d8f123 Sim Zhen Quan <<EMAIL>> 1745483568 +0800	pull: Fast-forward
72b23a17fc9dae7ec7cf713667cd83b546d8f123 72b23a17fc9dae7ec7cf713667cd83b546d8f123 Sim Zhen Quan <<EMAIL>> 1745483583 +0800	checkout: moving from main to staging/2025-04-24
72b23a17fc9dae7ec7cf713667cd83b546d8f123 749a7523adf4fad36bb5a988cdd1af7ab862bbfb Sim Zhen Quan <<EMAIL>> 1745483597 +0800	checkout: moving from staging/2025-04-24 to issuelog-25-hostel-savings-see-transaction-balance
749a7523adf4fad36bb5a988cdd1af7ab862bbfb 698138a3a71845c7f75e729d1e869e431563b2bd Sim Zhen Quan <<EMAIL>> 1745484200 +0800	checkout: moving from issuelog-25-hostel-savings-see-transaction-balance to fix/billing-document-report-for-product
698138a3a71845c7f75e729d1e869e431563b2bd 922ebd290b6653b48abb07ba9d6a668e92023083 Sim Zhen Quan <<EMAIL>> 1745484215 +0800	merge origin/main: Merge made by the 'ort' strategy.
922ebd290b6653b48abb07ba9d6a668e92023083 0c452f5ff76989c5f4019ff71111627f0bdc4ff7 Sim Zhen Quan <<EMAIL>> 1745485868 +0800	commit: Reviewed
0c452f5ff76989c5f4019ff71111627f0bdc4ff7 7518f410774c1e45fa58a457aff51373a6ef34a8 Sim Zhen Quan <<EMAIL>> 1745486205 +0800	checkout: moving from fix/billing-document-report-for-product to fix/load-merit-demerit-relationship
7518f410774c1e45fa58a457aff51373a6ef34a8 2d84297d6f2bfcfdb3b88e2f6ade55ad21b01c84 Sim Zhen Quan <<EMAIL>> 1745486217 +0800	merge origin/staging/2025-04-24: Merge made by the 'ort' strategy.
2d84297d6f2bfcfdb3b88e2f6ade55ad21b01c84 b6ae480b43db5d3b8d54f8507e93ff62d7972151 Sim Zhen Quan <<EMAIL>> 1745508038 +0800	commit: WIP
b6ae480b43db5d3b8d54f8507e93ff62d7972151 f11efac3340f7e281040695c4c097cfb15de79dc Sim Zhen Quan <<EMAIL>> 1745509160 +0800	commit: Reviewed
f11efac3340f7e281040695c4c097cfb15de79dc 70795960c20425989b2deb4da3f8f4712235057f Sim Zhen Quan <<EMAIL>> 1745509433 +0800	commit: Reviewed
70795960c20425989b2deb4da3f8f4712235057f 957e956d9634b4877c397ec38eb54a20d066208b Sim Zhen Quan <<EMAIL>> 1745509924 +0800	commit: Fix test case
957e956d9634b4877c397ec38eb54a20d066208b 72b23a17fc9dae7ec7cf713667cd83b546d8f123 Sim Zhen Quan <<EMAIL>> 1745509992 +0800	checkout: moving from fix/load-merit-demerit-relationship to staging/2025-04-24
72b23a17fc9dae7ec7cf713667cd83b546d8f123 32b1999066e93ed9f0ccae7fbddac66566b09aed Sim Zhen Quan <<EMAIL>> 1745509997 +0800	pull: Fast-forward
32b1999066e93ed9f0ccae7fbddac66566b09aed 1fd23a442951953fcd6814bd1f0e92749b50945d Sim Zhen Quan <<EMAIL>> 1745510065 +0800	merge origin/main: Merge made by the 'ort' strategy.
1fd23a442951953fcd6814bd1f0e92749b50945d 92d31de92e4c81c6f8b0bb58dc2e6c40906ede84 Sim Zhen Quan <<EMAIL>> 1745510331 +0800	commit: Delete unwanted files
92d31de92e4c81c6f8b0bb58dc2e6c40906ede84 de74ea31391a6e52359977d1d1c4910458335872 Sim Zhen Quan <<EMAIL>> 1745510348 +0800	checkout: moving from staging/2025-04-24 to migration/patch-student-guardian
de74ea31391a6e52359977d1d1c4910458335872 bde19f8651e6f4d13a5d120a4f717b187a1a6e35 Sim Zhen Quan <<EMAIL>> 1745510356 +0800	merge origin/staging/2025-04-24: Merge made by the 'ort' strategy.
bde19f8651e6f4d13a5d120a4f717b187a1a6e35 1dd49861a591251abe9bd6a5ecff62ea85c814af Sim Zhen Quan <<EMAIL>> 1745512718 +0800	commit: Reviewed
1dd49861a591251abe9bd6a5ecff62ea85c814af ccb289281e40b5840f40931d589c03c81e8bf843 Sim Zhen Quan <<EMAIL>> 1745512832 +0800	checkout: moving from migration/patch-student-guardian to issuelog40-void-invoice-validation-updates
ccb289281e40b5840f40931d589c03c81e8bf843 72b23a17fc9dae7ec7cf713667cd83b546d8f123 Sim Zhen Quan <<EMAIL>> 1745513477 +0800	checkout: moving from issuelog40-void-invoice-validation-updates to main
72b23a17fc9dae7ec7cf713667cd83b546d8f123 bec9fb2412c4cd22c8377a322e3beb76ea4bb2f6 Sim Zhen Quan <<EMAIL>> 1745513483 +0800	pull: Fast-forward
bec9fb2412c4cd22c8377a322e3beb76ea4bb2f6 c4b674b8d67df017ff476f0698671e831fd20d1f Sim Zhen Quan <<EMAIL>> 1745513843 +0800	merge origin/staging/2025-04-24: Fast-forward
c4b674b8d67df017ff476f0698671e831fd20d1f a078170d63d012ae73cccd8d74b5c6a1278b949e Sim Zhen Quan <<EMAIL>> 1745514100 +0800	commit: Remove comments
a078170d63d012ae73cccd8d74b5c6a1278b949e 6cbc3dd7880b8505008adc868ff38012343292a4 Sim Zhen Quan <<EMAIL>> 1745514849 +0800	commit: Deployed to prd
6cbc3dd7880b8505008adc868ff38012343292a4 6cbc3dd7880b8505008adc868ff38012343292a4 Sim Zhen Quan <<EMAIL>> 1745546384 +0800	checkout: moving from main to staging/2025-04-25
6cbc3dd7880b8505008adc868ff38012343292a4 6c1123819e8edba0a9d0e0df4b6aaadfb1d170a5 Sim Zhen Quan <<EMAIL>> 1745547499 +0800	checkout: moving from staging/2025-04-25 to discount-add-description-column
6c1123819e8edba0a9d0e0df4b6aaadfb1d170a5 96b900a45f83c40c3c06481e37691e6931e3bf9c Sim Zhen Quan <<EMAIL>> 1745547508 +0800	merge origin/main: Merge made by the 'ort' strategy.
96b900a45f83c40c3c06481e37691e6931e3bf9c 7dcfc99f8a8248d56e27cc4a0ef24dcad379f1fb Sim Zhen Quan <<EMAIL>> 1745548049 +0800	commit: Reviewed
7dcfc99f8a8248d56e27cc4a0ef24dcad379f1fb 9a4cab24e24e99ddf578cb8c7f4cba337a17c3cd Sim Zhen Quan <<EMAIL>> 1745548187 +0800	checkout: moving from discount-add-description-column to dev
9a4cab24e24e99ddf578cb8c7f4cba337a17c3cd b1bf4e2003431f813adaa3dbd22f86ab18ca8570 Sim Zhen Quan <<EMAIL>> 1745548195 +0800	merge discount-add-description-column: Merge made by the 'ort' strategy.
b1bf4e2003431f813adaa3dbd22f86ab18ca8570 09acb715b02c3aab7231791534924cfb476b3a4a Sim Zhen Quan <<EMAIL>> 1745548445 +0800	commit: Deployed to dev
09acb715b02c3aab7231791534924cfb476b3a4a 3111b95d45b645ae729d21c4286f3ae204e286ff Sim Zhen Quan <<EMAIL>> 1745548630 +0800	checkout: moving from dev to fix/billing-doc-creation-validation
3111b95d45b645ae729d21c4286f3ae204e286ff c98f66c21f2930cec3e002ad1a313f11ff5f7fe0 Sim Zhen Quan <<EMAIL>> 1745554340 +0800	checkout: moving from fix/billing-doc-creation-validation to issuelog-80-return-scholarship-details-in-discount-api
c98f66c21f2930cec3e002ad1a313f11ff5f7fe0 5f40e2b8388b0de0f28d7235cd20b3cc3811bbbf Sim Zhen Quan <<EMAIL>> 1745554347 +0800	merge origin/main: Merge made by the 'ort' strategy.
5f40e2b8388b0de0f28d7235cd20b3cc3811bbbf 3111b95d45b645ae729d21c4286f3ae204e286ff Sim Zhen Quan <<EMAIL>> 1745558924 +0800	checkout: moving from issuelog-80-return-scholarship-details-in-discount-api to fix/billing-doc-creation-validation
3111b95d45b645ae729d21c4286f3ae204e286ff 7753d3bbb0473c73785edf22127da5587de11456 Sim Zhen Quan <<EMAIL>> 1745558929 +0800	pull: Fast-forward
7753d3bbb0473c73785edf22127da5587de11456 a94be2308575c2aa0599183afeb63a2b926c13fe Sim Zhen Quan <<EMAIL>> 1745562120 +0800	commit: Reviewed
a94be2308575c2aa0599183afeb63a2b926c13fe 6cbc3dd7880b8505008adc868ff38012343292a4 Sim Zhen Quan <<EMAIL>> 1745563399 +0800	checkout: moving from fix/billing-doc-creation-validation to main
6cbc3dd7880b8505008adc868ff38012343292a4 d48fa039cbaa914c1b2aac327c63eb4a7c7c19c8 Sim Zhen Quan <<EMAIL>> 1745563840 +0800	commit: Fix test case
d48fa039cbaa914c1b2aac327c63eb4a7c7c19c8 09acb715b02c3aab7231791534924cfb476b3a4a Sim Zhen Quan <<EMAIL>> 1745563864 +0800	checkout: moving from main to dev
09acb715b02c3aab7231791534924cfb476b3a4a b0915c7856d64be231682dd1ad197dadb90c1e5b Sim Zhen Quan <<EMAIL>> 1745563869 +0800	merge fix/billing-doc-creation-validation: Merge made by the 'ort' strategy.
b0915c7856d64be231682dd1ad197dadb90c1e5b 1d55ad122bfe8175f6dc1cd75fd75ef0070229ac Sim Zhen Quan <<EMAIL>> 1745563912 +0800	merge origin/main: Merge made by the 'ort' strategy.
1d55ad122bfe8175f6dc1cd75fd75ef0070229ac 6b10423db10ad656a49ec4d54c1667e617144e85 Sim Zhen Quan <<EMAIL>> 1745564210 +0800	commit: Deployed to DEV
6b10423db10ad656a49ec4d54c1667e617144e85 ccb289281e40b5840f40931d589c03c81e8bf843 Sim Zhen Quan <<EMAIL>> 1745564215 +0800	checkout: moving from dev to issuelog40-void-invoice-validation-updates
ccb289281e40b5840f40931d589c03c81e8bf843 7899d466d80f2097ceaff0db8d0d154c4554087a Sim Zhen Quan <<EMAIL>> 1745564221 +0800	merge origin/main: Merge made by the 'ort' strategy.
7899d466d80f2097ceaff0db8d0d154c4554087a 6b10423db10ad656a49ec4d54c1667e617144e85 Sim Zhen Quan <<EMAIL>> 1745564740 +0800	checkout: moving from issuelog40-void-invoice-validation-updates to dev
6b10423db10ad656a49ec4d54c1667e617144e85 ad07ac10237d15aef5ed5a428b82d9c79de520ea Sim Zhen Quan <<EMAIL>> 1745564750 +0800	merge issuelog40-void-invoice-validation-updates: Merge made by the 'ort' strategy.
ad07ac10237d15aef5ed5a428b82d9c79de520ea ed64b080fa22d9ff20d70af124cf06e119a959ca Sim Zhen Quan <<EMAIL>> 1745565157 +0800	commit: Deployed to DEV
ed64b080fa22d9ff20d70af124cf06e119a959ca 5f40e2b8388b0de0f28d7235cd20b3cc3811bbbf Sim Zhen Quan <<EMAIL>> 1745565162 +0800	checkout: moving from dev to issuelog-80-return-scholarship-details-in-discount-api
5f40e2b8388b0de0f28d7235cd20b3cc3811bbbf d48fa039cbaa914c1b2aac327c63eb4a7c7c19c8 Sim Zhen Quan <<EMAIL>> 1745565167 +0800	checkout: moving from issuelog-80-return-scholarship-details-in-discount-api to main
d48fa039cbaa914c1b2aac327c63eb4a7c7c19c8 5f40e2b8388b0de0f28d7235cd20b3cc3811bbbf Sim Zhen Quan <<EMAIL>> 1745565262 +0800	checkout: moving from main to issuelog-80-return-scholarship-details-in-discount-api
5f40e2b8388b0de0f28d7235cd20b3cc3811bbbf f21b963180a27d613bc7c0b7a436ff5a053cf228 Sim Zhen Quan <<EMAIL>> 1745565268 +0800	merge origin/main: Merge made by the 'ort' strategy.
f21b963180a27d613bc7c0b7a436ff5a053cf228 ed64b080fa22d9ff20d70af124cf06e119a959ca Sim Zhen Quan <<EMAIL>> 1745565916 +0800	checkout: moving from issuelog-80-return-scholarship-details-in-discount-api to dev
ed64b080fa22d9ff20d70af124cf06e119a959ca 20d43c84e1574d54cf1af854f7a8fde2a1c971f0 Sim Zhen Quan <<EMAIL>> 1745565924 +0800	merge issuelog-80-return-scholarship-details-in-discount-api: Merge made by the 'ort' strategy.
20d43c84e1574d54cf1af854f7a8fde2a1c971f0 f982e0bd08dd0e272d664e879c8f5937c6fb1091 Sim Zhen Quan <<EMAIL>> 1745566442 +0800	commit: Deployed to DEV
f982e0bd08dd0e272d664e879c8f5937c6fb1091 f563478c88744cd885e71836949e2852cebc576b Sim Zhen Quan <<EMAIL>> 1745566450 +0800	checkout: moving from dev to api/book-recovery
f563478c88744cd885e71836949e2852cebc576b 51edf881237bc0bb4631973260f1cd9bfc630448 Sim Zhen Quan <<EMAIL>> 1745566501 +0800	merge origin/main: Merge made by the 'ort' strategy.
51edf881237bc0bb4631973260f1cd9bfc630448 e9a8acdee0c5151c27250af973477ef83371bf1f Sim Zhen Quan <<EMAIL>> 1745567375 +0800	commit: Reviewed
e9a8acdee0c5151c27250af973477ef83371bf1f f982e0bd08dd0e272d664e879c8f5937c6fb1091 Sim Zhen Quan <<EMAIL>> 1745567548 +0800	checkout: moving from api/book-recovery to dev
f982e0bd08dd0e272d664e879c8f5937c6fb1091 cd1a1e62a5d9122d325be81b6b903314316fbb93 Sim Zhen Quan <<EMAIL>> 1745567555 +0800	merge api/book-recovery: Merge made by the 'ort' strategy.
cd1a1e62a5d9122d325be81b6b903314316fbb93 f6833694686913c4a2d67b947673e117b2ea18f4 Sim Zhen Quan <<EMAIL>> 1745568352 +0800	commit: Deployed to dev
f6833694686913c4a2d67b947673e117b2ea18f4 d48fa039cbaa914c1b2aac327c63eb4a7c7c19c8 Sim Zhen Quan <<EMAIL>> 1745768295 +0800	checkout: moving from dev to main
d48fa039cbaa914c1b2aac327c63eb4a7c7c19c8 3ffc52d4692fd8453b111d1dda104daa74d5b6f9 Sim Zhen Quan <<EMAIL>> 1745768300 +0800	pull: Fast-forward
3ffc52d4692fd8453b111d1dda104daa74d5b6f9 51fac5149bc89ba2d4091db64aeb6baa185c5052 Sim Zhen Quan <<EMAIL>> 1745768591 +0800	checkout: moving from main to issue_86_employee_view_permission_dependency
51fac5149bc89ba2d4091db64aeb6baa185c5052 3ffc52d4692fd8453b111d1dda104daa74d5b6f9 Sim Zhen Quan <<EMAIL>> 1745768602 +0800	checkout: moving from issue_86_employee_view_permission_dependency to main
3ffc52d4692fd8453b111d1dda104daa74d5b6f9 28320bba79056630c5a09dc205dccc8140d57700 Sim Zhen Quan <<EMAIL>> 1745768605 +0800	commit: Deployed to PRD
28320bba79056630c5a09dc205dccc8140d57700 51fac5149bc89ba2d4091db64aeb6baa185c5052 Sim Zhen Quan <<EMAIL>> 1745768666 +0800	checkout: moving from main to issue_86_employee_view_permission_dependency
51fac5149bc89ba2d4091db64aeb6baa185c5052 cf03d6db27b06cb6d378c82d918b0a67214b653e Sim Zhen Quan <<EMAIL>> 1745768671 +0800	merge origin/main: Merge made by the 'ort' strategy.
cf03d6db27b06cb6d378c82d918b0a67214b653e b97a88edb577baf318aa04909857614a150cc654 Sim Zhen Quan <<EMAIL>> 1745855954 +0800	commit: Review WIP
b97a88edb577baf318aa04909857614a150cc654 9dd559121f69cd6a84b6be36616b15ca074b2056 Sim Zhen Quan <<EMAIL>> 1745855992 +0800	checkout: moving from issue_86_employee_view_permission_dependency to report/teacher-attendance-report
9dd559121f69cd6a84b6be36616b15ca074b2056 89bec0acd99d67a84b03017f1d81f037aefcffb6 Sim Zhen Quan <<EMAIL>> 1745856340 +0800	checkout: moving from report/teacher-attendance-report to bug/book_no_wildcard
89bec0acd99d67a84b03017f1d81f037aefcffb6 28320bba79056630c5a09dc205dccc8140d57700 Sim Zhen Quan <<EMAIL>> 1745856425 +0800	checkout: moving from bug/book_no_wildcard to main
28320bba79056630c5a09dc205dccc8140d57700 28320bba79056630c5a09dc205dccc8140d57700 Sim Zhen Quan <<EMAIL>> 1745856439 +0800	checkout: moving from main to staging/2025-04-29
28320bba79056630c5a09dc205dccc8140d57700 8ef039b68f184c8bce6a9a6a17e5e027a7a0861c Sim Zhen Quan <<EMAIL>> 1745856622 +0800	checkout: moving from staging/2025-04-29 to bug/library-member-add-class
8ef039b68f184c8bce6a9a6a17e5e027a7a0861c 938300a81125921932c54c91fd088dd4a49222b1 Sim Zhen Quan <<EMAIL>> 1745858186 +0800	commit: Reviewed
938300a81125921932c54c91fd088dd4a49222b1 7dcfc99f8a8248d56e27cc4a0ef24dcad379f1fb Sim Zhen Quan <<EMAIL>> 1745895164 +0800	checkout: moving from bug/library-member-add-class to discount-add-description-column
7dcfc99f8a8248d56e27cc4a0ef24dcad379f1fb 02b53a00fa25b83983f5c4d49d18c53662f13953 Sim Zhen Quan <<EMAIL>> 1745895168 +0800	pull: Fast-forward
02b53a00fa25b83983f5c4d49d18c53662f13953 28320bba79056630c5a09dc205dccc8140d57700 Sim Zhen Quan <<EMAIL>> 1745895645 +0800	checkout: moving from discount-add-description-column to main
28320bba79056630c5a09dc205dccc8140d57700 6d18473cc47c4bcf274e0c3ed7a3b082513a5409 Sim Zhen Quan <<EMAIL>> 1745895657 +0800	merge origin/staging/2025-04-29: Fast-forward
6d18473cc47c4bcf274e0c3ed7a3b082513a5409 f6833694686913c4a2d67b947673e117b2ea18f4 Sim Zhen Quan <<EMAIL>> 1745895679 +0800	checkout: moving from main to dev
f6833694686913c4a2d67b947673e117b2ea18f4 875465344dc01e3cf0ee107e6c6a3ec6e3ba99b4 Sim Zhen Quan <<EMAIL>> 1745895687 +0800	merge origin/staging/2025-04-29: Merge made by the 'ort' strategy.
875465344dc01e3cf0ee107e6c6a3ec6e3ba99b4 d69a3c7b827df4097a6f76846103415784947146 Sim Zhen Quan <<EMAIL>> 1745896765 +0800	commit: Deployed to dev
d69a3c7b827df4097a6f76846103415784947146 9ff50a01d03ab13dd8ae5e3d37bec240be6f3875 Sim Zhen Quan <<EMAIL>> 1745896798 +0800	checkout: moving from dev to issue-105-daily-collection-report-formula
9ff50a01d03ab13dd8ae5e3d37bec240be6f3875 61668f05594f534b993a1aec47a51b2263fed6dc Sim Zhen Quan <<EMAIL>> 1745896805 +0800	merge origin/main: Merge made by the 'ort' strategy.
61668f05594f534b993a1aec47a51b2263fed6dc d69a3c7b827df4097a6f76846103415784947146 Sim Zhen Quan <<EMAIL>> 1745897833 +0800	checkout: moving from issue-105-daily-collection-report-formula to dev
d69a3c7b827df4097a6f76846103415784947146 89c57d48138f326b39a580a4161268e97d63693a Sim Zhen Quan <<EMAIL>> 1745897836 +0800	merge issue-105-daily-collection-report-formula: Merge made by the 'ort' strategy.
89c57d48138f326b39a580a4161268e97d63693a 8e0cf6accadf0ea00003bde7b5d9ba42d687a43f Sim Zhen Quan <<EMAIL>> 1745899021 +0800	commit: Deployed to dev
8e0cf6accadf0ea00003bde7b5d9ba42d687a43f eb759c444c4987c589e32f25f296899275d7e11b Sim Zhen Quan <<EMAIL>> 1745899030 +0800	checkout: moving from dev to fix/departure-optional-guardian
eb759c444c4987c589e32f25f296899275d7e11b fd8b07e09825081f6c6a5cfed0a673f80f8ce5fc Sim Zhen Quan <<EMAIL>> 1745899140 +0800	merge origin/main: Merge made by the 'ort' strategy.
fd8b07e09825081f6c6a5cfed0a673f80f8ce5fc 8e0cf6accadf0ea00003bde7b5d9ba42d687a43f Sim Zhen Quan <<EMAIL>> 1745899328 +0800	checkout: moving from fix/departure-optional-guardian to dev
8e0cf6accadf0ea00003bde7b5d9ba42d687a43f e8948fae63aae8151e6a037ef601c326f8ca92df Sim Zhen Quan <<EMAIL>> 1745899334 +0800	merge origin/fix/departure-optional-guardian: Merge made by the 'ort' strategy.
e8948fae63aae8151e6a037ef601c326f8ca92df a979be1d3e737c1b01424ffac5d1b0d9ffb9430c Sim Zhen Quan <<EMAIL>> 1745899611 +0800	commit: Deployed to dev
a979be1d3e737c1b01424ffac5d1b0d9ffb9430c ecd89224effc0b8a5caad928408a2418136b3dfc Sim Zhen Quan <<EMAIL>> 1745899615 +0800	checkout: moving from dev to issue-124-employee-resigned-with-hostel-bed
ecd89224effc0b8a5caad928408a2418136b3dfc 93bf3035915633ce13deeb3b76133ef3f2c746aa Sim Zhen Quan <<EMAIL>> 1745899620 +0800	merge origin/main: Merge made by the 'ort' strategy.
93bf3035915633ce13deeb3b76133ef3f2c746aa 6a4d139fff82852848a8a777f2497b53e2b7d25c Sim Zhen Quan <<EMAIL>> 1745917521 +0800	checkout: moving from issue-124-employee-resigned-with-hostel-bed to class-attendance-taking-page-add-student-photo-employee-details
6a4d139fff82852848a8a777f2497b53e2b7d25c 9fa65916dead4cd8b1a5f80207c96cafb4629a3a Sim Zhen Quan <<EMAIL>> 1745917527 +0800	merge origin/main: Merge made by the 'ort' strategy.
9fa65916dead4cd8b1a5f80207c96cafb4629a3a 003581e500e1a3c7d3f61c3d09b2d8fccbea744e Sim Zhen Quan <<EMAIL>> 1745918222 +0800	commit: Reviewed
003581e500e1a3c7d3f61c3d09b2d8fccbea744e a979be1d3e737c1b01424ffac5d1b0d9ffb9430c Sim Zhen Quan <<EMAIL>> 1745918492 +0800	checkout: moving from class-attendance-taking-page-add-student-photo-employee-details to dev
a979be1d3e737c1b01424ffac5d1b0d9ffb9430c eb53dd5ed62dfe977e392443d13b8800ccbca30c Sim Zhen Quan <<EMAIL>> 1745918501 +0800	merge class-attendance-taking-page-add-student-photo-employee-details: Merge made by the 'ort' strategy.
eb53dd5ed62dfe977e392443d13b8800ccbca30c eb7c4eef6f0423bed8b1370b1aabb87db4f52e63 Sim Zhen Quan <<EMAIL>> 1745925519 +0800	commit: Deployed to Dev
eb7c4eef6f0423bed8b1370b1aabb87db4f52e63 d5e3b337c994d16bfb0a86d550f787b88e9d170e Sim Zhen Quan <<EMAIL>> 1745925536 +0800	commit: Add default sort for id
d5e3b337c994d16bfb0a86d550f787b88e9d170e 61668f05594f534b993a1aec47a51b2263fed6dc Sim Zhen Quan <<EMAIL>> 1745925585 +0800	checkout: moving from dev to issue-105-daily-collection-report-formula
61668f05594f534b993a1aec47a51b2263fed6dc 3a1203998f53c87efff7758ebc89006319baf384 Sim Zhen Quan <<EMAIL>> 1745925606 +0800	cherry-pick: Add default sort for id
3a1203998f53c87efff7758ebc89006319baf384 a5b4f47a320405838fc46202e708d20abccbcdc8 Sim Zhen Quan <<EMAIL>> 1745925670 +0800	checkout: moving from issue-105-daily-collection-report-formula to report/teacher-attendance
a5b4f47a320405838fc46202e708d20abccbcdc8 d5e3b337c994d16bfb0a86d550f787b88e9d170e Sim Zhen Quan <<EMAIL>> 1745925681 +0800	checkout: moving from report/teacher-attendance to dev
d5e3b337c994d16bfb0a86d550f787b88e9d170e 055fb20c052bdc84f7a46542a210a27ca7168dae Sim Zhen Quan <<EMAIL>> 1745925686 +0800	merge report/teacher-attendance: Merge made by the 'ort' strategy.
055fb20c052bdc84f7a46542a210a27ca7168dae a5b4f47a320405838fc46202e708d20abccbcdc8 Sim Zhen Quan <<EMAIL>> 1745926130 +0800	checkout: moving from dev to report/teacher-attendance
a5b4f47a320405838fc46202e708d20abccbcdc8 055fb20c052bdc84f7a46542a210a27ca7168dae Sim Zhen Quan <<EMAIL>> 1745926178 +0800	checkout: moving from report/teacher-attendance to dev
055fb20c052bdc84f7a46542a210a27ca7168dae f2433953e1fa8fe3032e3a765061708357064bfd Sim Zhen Quan <<EMAIL>> 1745926179 +0800	checkout: moving from dev to issue-50-cocu-student-absent-report
f2433953e1fa8fe3032e3a765061708357064bfd 055fb20c052bdc84f7a46542a210a27ca7168dae Sim Zhen Quan <<EMAIL>> 1745926188 +0800	checkout: moving from issue-50-cocu-student-absent-report to dev
055fb20c052bdc84f7a46542a210a27ca7168dae 6d18473cc47c4bcf274e0c3ed7a3b082513a5409 Sim Zhen Quan <<EMAIL>> 1745927001 +0800	checkout: moving from dev to main
6d18473cc47c4bcf274e0c3ed7a3b082513a5409 055fb20c052bdc84f7a46542a210a27ca7168dae Sim Zhen Quan <<EMAIL>> 1745927063 +0800	checkout: moving from main to dev
055fb20c052bdc84f7a46542a210a27ca7168dae 5c5e3192aaeabac874c7ec7310fe222ace096179 Sim Zhen Quan <<EMAIL>> 1745927164 +0800	commit (merge): Merge to dev
5c5e3192aaeabac874c7ec7310fe222ace096179 5f0025c08a143363b8fe158d2373a111f9906214 Sim Zhen Quan <<EMAIL>> 1745940952 +0800	commit: Deployed to dev
5f0025c08a143363b8fe158d2373a111f9906214 6d18473cc47c4bcf274e0c3ed7a3b082513a5409 Sim Zhen Quan <<EMAIL>> 1745941387 +0800	checkout: moving from dev to main
6d18473cc47c4bcf274e0c3ed7a3b082513a5409 63bd88fbe09bdfe9776f2b42235296a414631d12 Sim Zhen Quan <<EMAIL>> 1745941393 +0800	pull: Fast-forward
63bd88fbe09bdfe9776f2b42235296a414631d12 a5b4f47a320405838fc46202e708d20abccbcdc8 Sim Zhen Quan <<EMAIL>> 1745942337 +0800	checkout: moving from main to report/teacher-attendance
a5b4f47a320405838fc46202e708d20abccbcdc8 63bd88fbe09bdfe9776f2b42235296a414631d12 Sim Zhen Quan <<EMAIL>> 1745942346 +0800	checkout: moving from report/teacher-attendance to main
63bd88fbe09bdfe9776f2b42235296a414631d12 38b120e9982b5022885bdc8d8958a2a445dc24ce Sim Zhen Quan <<EMAIL>> 1745942354 +0800	commit: Deployed to PRD
38b120e9982b5022885bdc8d8958a2a445dc24ce a5b4f47a320405838fc46202e708d20abccbcdc8 Sim Zhen Quan <<EMAIL>> 1745942363 +0800	checkout: moving from main to report/teacher-attendance
a5b4f47a320405838fc46202e708d20abccbcdc8 ecb7073ed0883dac04469a80e0bab5714d90d023 Sim Zhen Quan <<EMAIL>> 1745942371 +0800	merge origin/main: Merge made by the 'ort' strategy.
ecb7073ed0883dac04469a80e0bab5714d90d023 38b120e9982b5022885bdc8d8958a2a445dc24ce Sim Zhen Quan <<EMAIL>> 1745943778 +0800	checkout: moving from report/teacher-attendance to main
38b120e9982b5022885bdc8d8958a2a445dc24ce 38b120e9982b5022885bdc8d8958a2a445dc24ce Sim Zhen Quan <<EMAIL>> 1745943785 +0800	checkout: moving from main to pagination-fix
38b120e9982b5022885bdc8d8958a2a445dc24ce c4a4fc242088fe0647ff819828a4298a2eddc7ed Sim Zhen Quan <<EMAIL>> 1745943813 +0800	commit: Added flag to control default order by status
c4a4fc242088fe0647ff819828a4298a2eddc7ed ecb7073ed0883dac04469a80e0bab5714d90d023 Sim Zhen Quan <<EMAIL>> 1745943828 +0800	checkout: moving from pagination-fix to report/teacher-attendance
ecb7073ed0883dac04469a80e0bab5714d90d023 38b120e9982b5022885bdc8d8958a2a445dc24ce Sim Zhen Quan <<EMAIL>> 1745944033 +0800	checkout: moving from report/teacher-attendance to main
38b120e9982b5022885bdc8d8958a2a445dc24ce 397cd210416fc3aa207241c1d586208f09c0907a Sim Zhen Quan <<EMAIL>> 1745944046 +0800	commit: Temporary disable code
397cd210416fc3aa207241c1d586208f09c0907a ecb7073ed0883dac04469a80e0bab5714d90d023 Sim Zhen Quan <<EMAIL>> 1745978283 +0800	checkout: moving from main to report/teacher-attendance
ecb7073ed0883dac04469a80e0bab5714d90d023 1ae867bae57fab26a466a179b02457c9fdb7e019 Sim Zhen Quan <<EMAIL>> 1745978289 +0800	merge origin/main: Merge made by the 'ort' strategy.
1ae867bae57fab26a466a179b02457c9fdb7e019 f2433953e1fa8fe3032e3a765061708357064bfd Sim Zhen Quan <<EMAIL>> 1745980090 +0800	checkout: moving from report/teacher-attendance to issue-50-cocu-student-absent-report
f2433953e1fa8fe3032e3a765061708357064bfd 6cb82509c967d0da8f905d6277bda7f10212b94d Sim Zhen Quan <<EMAIL>> 1745980096 +0800	pull: Fast-forward
6cb82509c967d0da8f905d6277bda7f10212b94d 2e96c006ec995c89a7dfe15e731be019d0a66741 Sim Zhen Quan <<EMAIL>> 1745980110 +0800	merge origin/main: Merge made by the 'ort' strategy.
2e96c006ec995c89a7dfe15e731be019d0a66741 b97a88edb577baf318aa04909857614a150cc654 Sim Zhen Quan <<EMAIL>> 1745984195 +0800	checkout: moving from issue-50-cocu-student-absent-report to issue_86_employee_view_permission_dependency
b97a88edb577baf318aa04909857614a150cc654 0e7560821c7803beeff9a992901fb0e212883012 Sim Zhen Quan <<EMAIL>> 1745984251 +0800	merge origin/main: Merge made by the 'ort' strategy.
0e7560821c7803beeff9a992901fb0e212883012 0e7560821c7803beeff9a992901fb0e212883012 Sim Zhen Quan <<EMAIL>> 1745985516 +0800	reset: moving to HEAD
0e7560821c7803beeff9a992901fb0e212883012 397cd210416fc3aa207241c1d586208f09c0907a Sim Zhen Quan <<EMAIL>> 1745985518 +0800	checkout: moving from issue_86_employee_view_permission_dependency to main
397cd210416fc3aa207241c1d586208f09c0907a 0e7560821c7803beeff9a992901fb0e212883012 Sim Zhen Quan <<EMAIL>> 1745985557 +0800	checkout: moving from main to issue_86_employee_view_permission_dependency
0e7560821c7803beeff9a992901fb0e212883012 9bbc9487f47f3d9e08b3756738e050d21acbe4ff Sim Zhen Quan <<EMAIL>> 1745986271 +0800	commit: Reviewed
9bbc9487f47f3d9e08b3756738e050d21acbe4ff 397cd210416fc3aa207241c1d586208f09c0907a Sim Zhen Quan <<EMAIL>> 1745986297 +0800	checkout: moving from issue_86_employee_view_permission_dependency to main
397cd210416fc3aa207241c1d586208f09c0907a 397cd210416fc3aa207241c1d586208f09c0907a Sim Zhen Quan <<EMAIL>> 1745986307 +0800	checkout: moving from main to staging/2025-04-30
397cd210416fc3aa207241c1d586208f09c0907a 63f03d221d26503e9bfa4e36e56a3a5b5c46e96b Sim Zhen Quan <<EMAIL>> 1745986433 +0800	checkout: moving from staging/2025-04-30 to attendance-module-enhancements
63f03d221d26503e9bfa4e36e56a3a5b5c46e96b 978127b852ac1d37e422b99c79c39a58b6637e89 Sim Zhen Quan <<EMAIL>> 1745986442 +0800	merge origin/main: Merge made by the 'ort' strategy.
978127b852ac1d37e422b99c79c39a58b6637e89 5f0025c08a143363b8fe158d2373a111f9906214 Sim Zhen Quan <<EMAIL>> 1745988222 +0800	checkout: moving from attendance-module-enhancements to dev
5f0025c08a143363b8fe158d2373a111f9906214 7fa5c3c05f823ebc98963beb09ea8f03b3f36b74 Sim Zhen Quan <<EMAIL>> 1745988528 +0800	commit (merge): Merge remote-tracking branch 'origin/staging/2025-04-30' into dev
7fa5c3c05f823ebc98963beb09ea8f03b3f36b74 0d5be9fd83d2f9c32ae706bae43efa5dda593756 Sim Zhen Quan <<EMAIL>> 1745988557 +0800	commit (merge): Merge remote-tracking branch 'origin/pagination-fix' into dev
0d5be9fd83d2f9c32ae706bae43efa5dda593756 0f2825224a2fc81ae2365bb9145f2ceba56cdef5 Sim Zhen Quan <<EMAIL>> 1745988575 +0800	merge attendance-module-enhancements: Merge made by the 'ort' strategy.
0f2825224a2fc81ae2365bb9145f2ceba56cdef5 b4910c79a1f00fb55069f508b576ae14d61c0824 Sim Zhen Quan <<EMAIL>> 1745995068 +0800	commit: Deployed to DEV
b4910c79a1f00fb55069f508b576ae14d61c0824 7fa9038f2b35b108ba96a1480e3db9ef39b3f642 Sim Zhen Quan <<EMAIL>> 1745995080 +0800	checkout: moving from dev to fix/attendance-mark-deduction-styling
7fa9038f2b35b108ba96a1480e3db9ef39b3f642 e1bfaf7ea366c68690806ef7f3e9e2b441f78d9e Sim Zhen Quan <<EMAIL>> 1745996481 +0800	commit: Updated report with translations
e1bfaf7ea366c68690806ef7f3e9e2b441f78d9e b4910c79a1f00fb55069f508b576ae14d61c0824 Sim Zhen Quan <<EMAIL>> 1745996693 +0800	checkout: moving from fix/attendance-mark-deduction-styling to dev
b4910c79a1f00fb55069f508b576ae14d61c0824 fac98cd40e9830798a7d3df3287e5d2ad87fd5f1 Sim Zhen Quan <<EMAIL>> 1745996706 +0800	merge origin/fix/attendance-mark-deduction-styling: Merge made by the 'ort' strategy.
fac98cd40e9830798a7d3df3287e5d2ad87fd5f1 397cd210416fc3aa207241c1d586208f09c0907a Sim Zhen Quan <<EMAIL>> 1746006300 +0800	checkout: moving from dev to main
397cd210416fc3aa207241c1d586208f09c0907a 2e96c006ec995c89a7dfe15e731be019d0a66741 Sim Zhen Quan <<EMAIL>> 1746108865 +0800	checkout: moving from main to issue-50-cocu-student-absent-report
2e96c006ec995c89a7dfe15e731be019d0a66741 1bd1927347a6313ce168ca1c1e786603dabbace8 Sim Zhen Quan <<EMAIL>> 1746108870 +0800	pull: Fast-forward
1bd1927347a6313ce168ca1c1e786603dabbace8 77ed2aeba83558b71c4416d08a60b8615eed4567 Sim Zhen Quan <<EMAIL>> 1746110943 +0800	commit: Reviewed
77ed2aeba83558b71c4416d08a60b8615eed4567 c4a4fc242088fe0647ff819828a4298a2eddc7ed Sim Zhen Quan <<EMAIL>> 1746111375 +0800	checkout: moving from issue-50-cocu-student-absent-report to pagination-fix
c4a4fc242088fe0647ff819828a4298a2eddc7ed c4a4fc242088fe0647ff819828a4298a2eddc7ed Sim Zhen Quan <<EMAIL>> 1746111460 +0800	reset: moving to HEAD
c4a4fc242088fe0647ff819828a4298a2eddc7ed d80da291cee185fdafed89f24b3427e4e314ae54 Sim Zhen Quan <<EMAIL>> 1746111491 +0800	commit (merge): Merge remote-tracking branch 'origin/main' into pagination-fix
d80da291cee185fdafed89f24b3427e4e314ae54 d1bb6d212819ae867b82a7599471e1d122b2339f Sim Zhen Quan <<EMAIL>> 1746114306 +0800	commit: Only fixed affected placed for now
d1bb6d212819ae867b82a7599471e1d122b2339f c00b315343747031888c6a7008f8e199c4a4c787 Sim Zhen Quan <<EMAIL>> 1746114605 +0800	checkout: moving from pagination-fix to fix/billing-document-daily-report-collection-column-changes
c00b315343747031888c6a7008f8e199c4a4c787 c00b315343747031888c6a7008f8e199c4a4c787 Sim Zhen Quan <<EMAIL>> 1746114924 +0800	checkout: moving from fix/billing-document-daily-report-collection-column-changes to fix/billing-document-daily-report-collection-column-changes
c00b315343747031888c6a7008f8e199c4a4c787 c00b315343747031888c6a7008f8e199c4a4c787 Sim Zhen Quan <<EMAIL>> 1746114943 +0800	checkout: moving from fix/billing-document-daily-report-collection-column-changes to issue-116-fix-student-search-by-class
c00b315343747031888c6a7008f8e199c4a4c787 77ed2aeba83558b71c4416d08a60b8615eed4567 Sim Zhen Quan <<EMAIL>> 1746115084 +0800	checkout: moving from issue-116-fix-student-search-by-class to issue-50-cocu-student-absent-report
77ed2aeba83558b71c4416d08a60b8615eed4567 77ed2aeba83558b71c4416d08a60b8615eed4567 Sim Zhen Quan <<EMAIL>> 1746115106 +0800	checkout: moving from issue-50-cocu-student-absent-report to issue-116-fix-student-search-by-class
77ed2aeba83558b71c4416d08a60b8615eed4567 d4f04721c1fa688f436cf5bbbdd471dc601a45c8 Sim Zhen Quan <<EMAIL>> 1746115523 +0800	commit: Fix student class search
d4f04721c1fa688f436cf5bbbdd471dc601a45c8 ef10ef194e6cc807601c41088983a57a8c52e992 Sim Zhen Quan <<EMAIL>> 1746115779 +0800	commit: Fix student class search
ef10ef194e6cc807601c41088983a57a8c52e992 fac98cd40e9830798a7d3df3287e5d2ad87fd5f1 Sim Zhen Quan <<EMAIL>> 1746115816 +0800	checkout: moving from issue-116-fix-student-search-by-class to dev
fac98cd40e9830798a7d3df3287e5d2ad87fd5f1 dd7fa25da0eeac0ef3b8d314fecdbe5e32fb3643 Sim Zhen Quan <<EMAIL>> 1746115855 +0800	merge origin/issue-116-fix-student-search-by-class: Merge made by the 'ort' strategy.
dd7fa25da0eeac0ef3b8d314fecdbe5e32fb3643 0f94a77adf6701dd7e7d6c1f0b0112575859d367 Sim Zhen Quan <<EMAIL>> 1746115884 +0800	merge origin/fix/billing-document-daily-report-collection-column-changes: Merge made by the 'ort' strategy.
0f94a77adf6701dd7e7d6c1f0b0112575859d367 77c4953918b83cee09b2ea36397743ce7bb493c6 Sim Zhen Quan <<EMAIL>> 1746115917 +0800	commit (merge): Merge remote-tracking branch 'origin/pagination-fix' into dev
77c4953918b83cee09b2ea36397743ce7bb493c6 dc5a77ebfebcce2e5c44475784fb02c7eb0d833a Sim Zhen Quan <<EMAIL>> 1746150531 +0800	commit: Deployed to dev
dc5a77ebfebcce2e5c44475784fb02c7eb0d833a 9c68ef33cbe79be2392de186f62e726ec24ecb3e Sim Zhen Quan <<EMAIL>> 1746150539 +0800	checkout: moving from dev to fix-get-attendance-period-for-student-bug
9c68ef33cbe79be2392de186f62e726ec24ecb3e ce8d3200ac00c264d7872cfac7796664f5becb4a Sim Zhen Quan <<EMAIL>> 1746151151 +0800	commit: Reviewed
ce8d3200ac00c264d7872cfac7796664f5becb4a dc5a77ebfebcce2e5c44475784fb02c7eb0d833a Sim Zhen Quan <<EMAIL>> 1746151185 +0800	checkout: moving from fix-get-attendance-period-for-student-bug to dev
dc5a77ebfebcce2e5c44475784fb02c7eb0d833a 3210b6f12157d81d07c25d5740902e360d43d485 Sim Zhen Quan <<EMAIL>> 1746151193 +0800	merge origin/fix-get-attendance-period-for-student-bug: Merge made by the 'ort' strategy.
3210b6f12157d81d07c25d5740902e360d43d485 1ae867bae57fab26a466a179b02457c9fdb7e019 Sim Zhen Quan <<EMAIL>> 1746151383 +0800	checkout: moving from dev to report/teacher-attendance
1ae867bae57fab26a466a179b02457c9fdb7e019 4c0d73b4ba4d457813c1c5c29e5a213a50800a52 Sim Zhen Quan <<EMAIL>> 1746151388 +0800	pull: Fast-forward
4c0d73b4ba4d457813c1c5c29e5a213a50800a52 54e6fe1cb545e395778ae96a8404839e36410082 Sim Zhen Quan <<EMAIL>> 1746153206 +0800	commit: Review WIP
54e6fe1cb545e395778ae96a8404839e36410082 3210b6f12157d81d07c25d5740902e360d43d485 Sim Zhen Quan <<EMAIL>> 1746153745 +0800	checkout: moving from report/teacher-attendance to dev
3210b6f12157d81d07c25d5740902e360d43d485 11e1b6a68fd9dc415f9973445fc1f4fe77fcd455 Sim Zhen Quan <<EMAIL>> 1746153861 +0800	commit (merge): Merge branch 'report/teacher-attendance' into dev
11e1b6a68fd9dc415f9973445fc1f4fe77fcd455 ba03526edf927f0e18986f2b3fd25379ae4de94f Sim Zhen Quan <<EMAIL>> 1746154865 +0800	commit: Deployed to dev
ba03526edf927f0e18986f2b3fd25379ae4de94f 54e6fe1cb545e395778ae96a8404839e36410082 Sim Zhen Quan <<EMAIL>> 1746154882 +0800	checkout: moving from dev to report/teacher-attendance
54e6fe1cb545e395778ae96a8404839e36410082 bd29c837e318880960946986bd5820b70eb43103 Sim Zhen Quan <<EMAIL>> 1746154892 +0800	commit: Bug fix
bd29c837e318880960946986bd5820b70eb43103 ba03526edf927f0e18986f2b3fd25379ae4de94f Sim Zhen Quan <<EMAIL>> 1746154998 +0800	checkout: moving from report/teacher-attendance to dev
ba03526edf927f0e18986f2b3fd25379ae4de94f bd29c837e318880960946986bd5820b70eb43103 Sim Zhen Quan <<EMAIL>> 1746155414 +0800	checkout: moving from dev to report/teacher-attendance
bd29c837e318880960946986bd5820b70eb43103 2b16984401ed62ce8b1b44ea4906a3e8606becd4 Sim Zhen Quan <<EMAIL>> 1746155452 +0800	commit: Bug fix
2b16984401ed62ce8b1b44ea4906a3e8606becd4 ba03526edf927f0e18986f2b3fd25379ae4de94f Sim Zhen Quan <<EMAIL>> 1746155924 +0800	checkout: moving from report/teacher-attendance to dev
ba03526edf927f0e18986f2b3fd25379ae4de94f 8f0d3bdffd38ca9ea3f7965e3524078a87c94440 Sim Zhen Quan <<EMAIL>> 1746155924 +0800	merge report/teacher-attendance: Merge made by the 'ort' strategy.
8f0d3bdffd38ca9ea3f7965e3524078a87c94440 5f2817407fa569320b20a530a37a1d25979bcf1f Sim Zhen Quan <<EMAIL>> 1746156086 +0800	checkout: moving from dev to tap-card-throw-error-if-inactive-card
5f2817407fa569320b20a530a37a1d25979bcf1f 8f0d3bdffd38ca9ea3f7965e3524078a87c94440 Sim Zhen Quan <<EMAIL>> 1746156422 +0800	checkout: moving from tap-card-throw-error-if-inactive-card to dev
8f0d3bdffd38ca9ea3f7965e3524078a87c94440 5f2817407fa569320b20a530a37a1d25979bcf1f Sim Zhen Quan <<EMAIL>> 1746156536 +0800	checkout: moving from dev to tap-card-throw-error-if-inactive-card
5f2817407fa569320b20a530a37a1d25979bcf1f e895a9ab6eec235cafa9cd7059f09ab94f8f9c06 Sim Zhen Quan <<EMAIL>> 1746156608 +0800	commit: Fix error code
e895a9ab6eec235cafa9cd7059f09ab94f8f9c06 8f0d3bdffd38ca9ea3f7965e3524078a87c94440 Sim Zhen Quan <<EMAIL>> 1746156623 +0800	checkout: moving from tap-card-throw-error-if-inactive-card to dev
8f0d3bdffd38ca9ea3f7965e3524078a87c94440 fb36971b8291dfbd842a4ee2d20c4f5cc85ceea6 Sim Zhen Quan <<EMAIL>> 1746156650 +0800	commit (merge): Merge remote-tracking branch 'origin/tap-card-throw-error-if-inactive-card' into dev
fb36971b8291dfbd842a4ee2d20c4f5cc85ceea6 42f04cdba2a60f7721170b8d469b2068267aa7e0 Sim Zhen Quan <<EMAIL>> 1746156696 +0800	checkout: moving from dev to substitute-records-index-show-period-label-name
42f04cdba2a60f7721170b8d469b2068267aa7e0 229e1730e999e3ddb0c3f364879d7e8a68ebeaa7 Sim Zhen Quan <<EMAIL>> 1746157729 +0800	commit: Added new column instead of reusing old one
229e1730e999e3ddb0c3f364879d7e8a68ebeaa7 fb36971b8291dfbd842a4ee2d20c4f5cc85ceea6 Sim Zhen Quan <<EMAIL>> 1746157837 +0800	checkout: moving from substitute-records-index-show-period-label-name to dev
fb36971b8291dfbd842a4ee2d20c4f5cc85ceea6 b5ccdbbdb02f4693a266419593798a14ee396e00 Sim Zhen Quan <<EMAIL>> 1746157840 +0800	merge origin/substitute-records-index-show-period-label-name: Merge made by the 'ort' strategy.
b5ccdbbdb02f4693a266419593798a14ee396e00 c1383973fd2ec07cfbf340d9e04c1c1157ccd8b8 Sim Zhen Quan <<EMAIL>> 1746371072 +0800	commit: Deployed to DEV
c1383973fd2ec07cfbf340d9e04c1c1157ccd8b8 397cd210416fc3aa207241c1d586208f09c0907a Sim Zhen Quan <<EMAIL>> 1746371077 +0800	checkout: moving from dev to staging/2025-04-30
397cd210416fc3aa207241c1d586208f09c0907a b3db8e0780d3f2aebb8535e8d4b433516e5a7b38 Sim Zhen Quan <<EMAIL>> 1746371087 +0800	pull: Fast-forward
b3db8e0780d3f2aebb8535e8d4b433516e5a7b38 397cd210416fc3aa207241c1d586208f09c0907a Sim Zhen Quan <<EMAIL>> 1746371778 +0800	checkout: moving from staging/2025-04-30 to main
397cd210416fc3aa207241c1d586208f09c0907a 485681a98d509da56c42a70062e3f9d9f9cb8801 Sim Zhen Quan <<EMAIL>> 1746371784 +0800	pull: Fast-forward
485681a98d509da56c42a70062e3f9d9f9cb8801 a136c9367666564da506b17abe51c02b6b724182 Sim Zhen Quan <<EMAIL>> 1746375217 +0800	commit: Test case fixes
a136c9367666564da506b17abe51c02b6b724182 a136c9367666564da506b17abe51c02b6b724182 Sim Zhen Quan <<EMAIL>> 1746583386 +0800	checkout: moving from main to staging/2025-05-07
a136c9367666564da506b17abe51c02b6b724182 59924c6323da866b867c09cd1782b4c3d21c73a1 Sim Zhen Quan <<EMAIL>> 1746583490 +0800	checkout: moving from staging/2025-05-07 to attendance-posting-direct-update-instead-of-delete-create
59924c6323da866b867c09cd1782b4c3d21c73a1 31ef9153ca148078f1586038d82a28b5d59da628 Sim Zhen Quan <<EMAIL>> 1746583498 +0800	commit: Deployed to PRD
31ef9153ca148078f1586038d82a28b5d59da628 003581e500e1a3c7d3f61c3d09b2d8fccbea744e Sim Zhen Quan <<EMAIL>> 1746585419 +0800	checkout: moving from attendance-posting-direct-update-instead-of-delete-create to class-attendance-taking-page-add-student-photo-employee-details
003581e500e1a3c7d3f61c3d09b2d8fccbea744e 04fbf0ff1f7befa7b37936a2a930cae4ed6c4c6c Sim Zhen Quan <<EMAIL>> 1746585627 +0800	commit (merge): Merge remote-tracking branch 'origin/staging/2025-05-07' into class-attendance-taking-page-add-student-photo-employee-details
04fbf0ff1f7befa7b37936a2a930cae4ed6c4c6c e1bfaf7ea366c68690806ef7f3e9e2b441f78d9e Sim Zhen Quan <<EMAIL>> 1746586238 +0800	checkout: moving from class-attendance-taking-page-add-student-photo-employee-details to fix/attendance-mark-deduction-styling
e1bfaf7ea366c68690806ef7f3e9e2b441f78d9e 6e9d38e52b8e31dbc5cd43f55da6dff47271cb6e Sim Zhen Quan <<EMAIL>> 1746586251 +0800	merge origin/main: Merge made by the 'ort' strategy.
6e9d38e52b8e31dbc5cd43f55da6dff47271cb6e 1716f2cfea5b1f9ba0eea4e1a7a3ea416f04b4c7 Sim Zhen Quan <<EMAIL>> 1746586442 +0800	commit: Fix import bug
1716f2cfea5b1f9ba0eea4e1a7a3ea416f04b4c7 c1383973fd2ec07cfbf340d9e04c1c1157ccd8b8 Sim Zhen Quan <<EMAIL>> 1746586467 +0800	checkout: moving from fix/attendance-mark-deduction-styling to dev
c1383973fd2ec07cfbf340d9e04c1c1157ccd8b8 d09a66a45e447671975e82b27c9d0800d95a4cca Sim Zhen Quan <<EMAIL>> 1746586470 +0800	pull: Fast-forward
d09a66a45e447671975e82b27c9d0800d95a4cca 2f82c478e4598f8f5d9575c519d43f914624ad37 Sim Zhen Quan <<EMAIL>> 1746586470 +0800	merge origin/fix/attendance-mark-deduction-styling: Merge made by the 'ort' strategy.
2f82c478e4598f8f5d9575c519d43f914624ad37 978127b852ac1d37e422b99c79c39a58b6637e89 Sim Zhen Quan <<EMAIL>> 1746589421 +0800	checkout: moving from dev to attendance-module-enhancements
978127b852ac1d37e422b99c79c39a58b6637e89 6015a49e492376531fa99f66914b54a3b74bf7db Sim Zhen Quan <<EMAIL>> 1746589426 +0800	pull: Fast-forward
6015a49e492376531fa99f66914b54a3b74bf7db 48501637c4a0da8a03f164439000550cdecc035f Sim Zhen Quan <<EMAIL>> 1746591026 +0800	pull: Fast-forward
48501637c4a0da8a03f164439000550cdecc035f 907d08ad87bba51f05577cca4bbe4548e59da1e4 Sim Zhen Quan <<EMAIL>> 1746591213 +0800	checkout: moving from attendance-module-enhancements to attendance-posting-update-first-period-class-attendance-if-late
907d08ad87bba51f05577cca4bbe4548e59da1e4 48501637c4a0da8a03f164439000550cdecc035f Sim Zhen Quan <<EMAIL>> 1746594351 +0800	checkout: moving from attendance-posting-update-first-period-class-attendance-if-late to attendance-module-enhancements
48501637c4a0da8a03f164439000550cdecc035f 4f5bb0dde583b8e5a29923a15173d9a710624852 Sim Zhen Quan <<EMAIL>> 1746594357 +0800	pull: Fast-forward
4f5bb0dde583b8e5a29923a15173d9a710624852 bf99f48ea2286efe2befce62464a4b21845465d3 Sim Zhen Quan <<EMAIL>> 1746594744 +0800	commit: Test case fix
bf99f48ea2286efe2befce62464a4b21845465d3 2f82c478e4598f8f5d9575c519d43f914624ad37 Sim Zhen Quan <<EMAIL>> 1746594764 +0800	checkout: moving from attendance-module-enhancements to dev
2f82c478e4598f8f5d9575c519d43f914624ad37 432034724ee3a58517b1f46ac0d060aa14aff6f6 Sim Zhen Quan <<EMAIL>> 1746594910 +0800	commit (merge): Merge branch 'attendance-module-enhancements' into dev
432034724ee3a58517b1f46ac0d060aa14aff6f6 f0891fa227cf7ae99faaba8eee4059c3c2a19470 Sim Zhen Quan <<EMAIL>> 1746607491 +0800	commit: Deployed to DEV
f0891fa227cf7ae99faaba8eee4059c3c2a19470 2a0df02698ace2c00bb5911727c1e4b658a1e1a6 Sim Zhen Quan <<EMAIL>> 1746607510 +0800	checkout: moving from dev to bug/library-loan-book-overdue-amount
2a0df02698ace2c00bb5911727c1e4b658a1e1a6 dc09faeddf1ba46c414e4660d4d030a05b11aef9 Sim Zhen Quan <<EMAIL>> 1746608980 +0800	commit: Reviewed
dc09faeddf1ba46c414e4660d4d030a05b11aef9 a136c9367666564da506b17abe51c02b6b724182 Sim Zhen Quan <<EMAIL>> 1746612241 +0800	checkout: moving from bug/library-loan-book-overdue-amount to main
a136c9367666564da506b17abe51c02b6b724182 e87aab33032a85d4d2c0bcf4cda9e147a4ea5e04 Sim Zhen Quan <<EMAIL>> 1746612302 +0800	commit: Deployed to PRD at 4 May 2025
e87aab33032a85d4d2c0bcf4cda9e147a4ea5e04 a136c9367666564da506b17abe51c02b6b724182 Sim Zhen Quan <<EMAIL>> 1746612978 +0800	checkout: moving from main to staging/2025-05-07
a136c9367666564da506b17abe51c02b6b724182 60d58226a7e53167af17fa176261d62344b91def Sim Zhen Quan <<EMAIL>> 1746612985 +0800	pull: Fast-forward
60d58226a7e53167af17fa176261d62344b91def dc52f344c93875e4e35f212120251cd63b988a59 Sim Zhen Quan <<EMAIL>> 1746613170 +0800	checkout: moving from staging/2025-05-07 to bug/call_no_wildcard
dc52f344c93875e4e35f212120251cd63b988a59 767b445dd8563ce094315d096223b3488ab55c4c Sim Zhen Quan <<EMAIL>> 1746613178 +0800	merge origin/main: Merge made by the 'ort' strategy.
767b445dd8563ce094315d096223b3488ab55c4c 2b16984401ed62ce8b1b44ea4906a3e8606becd4 Sim Zhen Quan <<EMAIL>> 1746619790 +0800	checkout: moving from bug/call_no_wildcard to report/teacher-attendance
2b16984401ed62ce8b1b44ea4906a3e8606becd4 9064f236ad2cb1960ce10effdf418c51c783a94d Sim Zhen Quan <<EMAIL>> 1746619796 +0800	pull: Fast-forward
9064f236ad2cb1960ce10effdf418c51c783a94d ff63f34ea6de22b5972365083e2c65b0814d10ad Sim Zhen Quan <<EMAIL>> 1746620265 +0800	commit (merge): Merge remote-tracking branch 'origin/main' into report/teacher-attendance
ff63f34ea6de22b5972365083e2c65b0814d10ad 558d6f0a1aa62a1f018565ff459b18416e0af20e Sim Zhen Quan <<EMAIL>> 1746623813 +0800	commit: Remove unnecessary changes.
558d6f0a1aa62a1f018565ff459b18416e0af20e bd1c69b712f93f3c048dfd6791c8ab9fda004788 Sim Zhen Quan <<EMAIL>> 1746624354 +0800	commit: Fix test case
bd1c69b712f93f3c048dfd6791c8ab9fda004788 f0891fa227cf7ae99faaba8eee4059c3c2a19470 Sim Zhen Quan <<EMAIL>> 1746624388 +0800	checkout: moving from report/teacher-attendance to dev
f0891fa227cf7ae99faaba8eee4059c3c2a19470 2b09d6740555f56166b54ad00d215c71e032eb36 Sim Zhen Quan <<EMAIL>> 1746624436 +0800	commit (merge): Merge branch 'report/teacher-attendance' into dev
2b09d6740555f56166b54ad00d215c71e032eb36 9df33b591f3f06f2c797576867f5c521c44ebaa8 Sim Zhen Quan <<EMAIL>> 1746625257 +0800	commit: Deployed to DEV
9df33b591f3f06f2c797576867f5c521c44ebaa8 e87aab33032a85d4d2c0bcf4cda9e147a4ea5e04 Sim Zhen Quan <<EMAIL>> 1746625265 +0800	checkout: moving from dev to main
e87aab33032a85d4d2c0bcf4cda9e147a4ea5e04 be74f807e7710c62e1a48e22b07f45cd74fd83b7 Sim Zhen Quan <<EMAIL>> 1746625269 +0800	pull: Fast-forward
be74f807e7710c62e1a48e22b07f45cd74fd83b7 9df33b591f3f06f2c797576867f5c521c44ebaa8 Sim Zhen Quan <<EMAIL>> 1746625319 +0800	checkout: moving from main to dev
9df33b591f3f06f2c797576867f5c521c44ebaa8 bd1c69b712f93f3c048dfd6791c8ab9fda004788 Sim Zhen Quan <<EMAIL>> 1746625501 +0800	checkout: moving from dev to report/teacher-attendance
bd1c69b712f93f3c048dfd6791c8ab9fda004788 babd8b48f4d8a49a521d5468bc2bcfbad7e788da Sim Zhen Quan <<EMAIL>> 1746625798 +0800	commit: Remove unused test case
babd8b48f4d8a49a521d5468bc2bcfbad7e788da be74f807e7710c62e1a48e22b07f45cd74fd83b7 Sim Zhen Quan <<EMAIL>> 1746628663 +0800	checkout: moving from report/teacher-attendance to main
be74f807e7710c62e1a48e22b07f45cd74fd83b7 546e08449ac6fd710135dd220067691e671eecd5 Sim Zhen Quan <<EMAIL>> 1746688381 +0800	commit: Deployed to prd
546e08449ac6fd710135dd220067691e671eecd5 15e2d16e802e9e2690986d55c2c2445ca5750878 Sim Zhen Quan <<EMAIL>> 1746688400 +0800	merge origin/main: Merge made by the 'ort' strategy.
15e2d16e802e9e2690986d55c2c2445ca5750878 15e2d16e802e9e2690986d55c2c2445ca5750878 Sim Zhen Quan <<EMAIL>> 1746688459 +0800	checkout: moving from main to issue-83-auto-post-reward-punishment-records
15e2d16e802e9e2690986d55c2c2445ca5750878 a880327d04f36982f287e3dc99b2d242d7440b5c Sim Zhen Quan <<EMAIL>> 1746688865 +0800	commit: Update reward punishment record status to POSTED and adjust test cases
a880327d04f36982f287e3dc99b2d242d7440b5c 15e2d16e802e9e2690986d55c2c2445ca5750878 Sim Zhen Quan <<EMAIL>> 1746688883 +0800	checkout: moving from issue-83-auto-post-reward-punishment-records to main
15e2d16e802e9e2690986d55c2c2445ca5750878 15e2d16e802e9e2690986d55c2c2445ca5750878 Sim Zhen Quan <<EMAIL>> 1746688891 +0800	checkout: moving from main to staging/2025-05-08
15e2d16e802e9e2690986d55c2c2445ca5750878 9df33b591f3f06f2c797576867f5c521c44ebaa8 Sim Zhen Quan <<EMAIL>> 1746688945 +0800	checkout: moving from staging/2025-05-08 to dev
9df33b591f3f06f2c797576867f5c521c44ebaa8 5b5e0d688855355e182df301c128e3c1627bf6c9 Sim Zhen Quan <<EMAIL>> 1746688953 +0800	merge issue-83-auto-post-reward-punishment-records: Merge made by the 'ort' strategy.
5b5e0d688855355e182df301c128e3c1627bf6c9 0d82371920dd2e21c61405e54df44869ca96a675 Sim Zhen Quan <<EMAIL>> 1746689020 +0800	checkout: moving from dev to bug/product-index-enhancement
0d82371920dd2e21c61405e54df44869ca96a675 07349e5b07993602c5a44eef374bf2e037a5cb61 Sim Zhen Quan <<EMAIL>> 1746689026 +0800	merge origin/main: Merge made by the 'ort' strategy.
07349e5b07993602c5a44eef374bf2e037a5cb61 3f8c1f2c67a48283d9fa8faf580575618a0e5aa9 Sim Zhen Quan <<EMAIL>> 1746689185 +0800	commit: Reviewed
3f8c1f2c67a48283d9fa8faf580575618a0e5aa9 5b5e0d688855355e182df301c128e3c1627bf6c9 Sim Zhen Quan <<EMAIL>> 1746689206 +0800	checkout: moving from bug/product-index-enhancement to dev
5b5e0d688855355e182df301c128e3c1627bf6c9 e6bddf114391a56b32d3d74bdb04b4ccda5fdadc Sim Zhen Quan <<EMAIL>> 1746689215 +0800	merge origin/bug/product-index-enhancement: Merge made by the 'ort' strategy.
e6bddf114391a56b32d3d74bdb04b4ccda5fdadc 99d92c3bb00c6554dfacb8198b8c10a4b44c80b0 Sim Zhen Quan <<EMAIL>> 1746689554 +0800	commit: Deployed to DEV
99d92c3bb00c6554dfacb8198b8c10a4b44c80b0 6442802399f7955ff3537b8c1b1af9e3942e0d5f Sim Zhen Quan <<EMAIL>> 1746689562 +0800	checkout: moving from dev to issue-149-wallet-top-up-max-limit
6442802399f7955ff3537b8c1b1af9e3942e0d5f ddd135bd0a1479d803602f163fd6c33c99b7aec8 Sim Zhen Quan <<EMAIL>> 1746689567 +0800	merge origin/main: Merge made by the 'ort' strategy.
ddd135bd0a1479d803602f163fd6c33c99b7aec8 1eeeab6d3010ab868e4544c2d0c771bf7f18f1b0 Sim Zhen Quan <<EMAIL>> 1746692047 +0800	commit: Reviewed
1eeeab6d3010ab868e4544c2d0c771bf7f18f1b0 99d92c3bb00c6554dfacb8198b8c10a4b44c80b0 Sim Zhen Quan <<EMAIL>> 1746692094 +0800	checkout: moving from issue-149-wallet-top-up-max-limit to dev
99d92c3bb00c6554dfacb8198b8c10a4b44c80b0 bef2e0c304caa42d315667e91779cfd04276a75c Sim Zhen Quan <<EMAIL>> 1746692100 +0800	merge origin/issue-149-wallet-top-up-max-limit: Merge made by the 'ort' strategy.
bef2e0c304caa42d315667e91779cfd04276a75c 767b445dd8563ce094315d096223b3488ab55c4c Sim Zhen Quan <<EMAIL>> 1746692221 +0800	checkout: moving from dev to bug/call_no_wildcard
767b445dd8563ce094315d096223b3488ab55c4c 0aeb2e99462cf7713c6d6d1838f05851461ea11f Sim Zhen Quan <<EMAIL>> 1746692227 +0800	merge origin/main: Merge made by the 'ort' strategy.
0aeb2e99462cf7713c6d6d1838f05851461ea11f bef2e0c304caa42d315667e91779cfd04276a75c Sim Zhen Quan <<EMAIL>> 1746692318 +0800	checkout: moving from bug/call_no_wildcard to dev
bef2e0c304caa42d315667e91779cfd04276a75c fcf697c1b08d822957a75e3dbfffd7adc8edb042 Sim Zhen Quan <<EMAIL>> 1746692323 +0800	merge bug/call_no_wildcard: Merge made by the 'ort' strategy.
fcf697c1b08d822957a75e3dbfffd7adc8edb042 20783b1488c426437a92f16f779aa270fe67dc64 Sim Zhen Quan <<EMAIL>> 1746692522 +0800	commit: Deployed to DEV
20783b1488c426437a92f16f779aa270fe67dc64 362cfe59b031923e4f495820729641a049a89d34 Sim Zhen Quan <<EMAIL>> 1746692529 +0800	checkout: moving from dev to fix-substitute-record-query-bug
362cfe59b031923e4f495820729641a049a89d34 a526ca72edfc6787f28cbf6470dddcc7f2ff0d3a Sim Zhen Quan <<EMAIL>> 1746692536 +0800	merge origin/main: Merge made by the 'ort' strategy.
a526ca72edfc6787f28cbf6470dddcc7f2ff0d3a 563a1cdcae45305c83db779a828d8fcd4a869690 Sim Zhen Quan <<EMAIL>> 1746692815 +0800	checkout: moving from fix-substitute-record-query-bug to student-statistic-report-added-class-code-column
563a1cdcae45305c83db779a828d8fcd4a869690 7d8b09e638c5550500bda4660c8188269a633c02 Sim Zhen Quan <<EMAIL>> 1746692820 +0800	merge origin/main: Merge made by the 'ort' strategy.
7d8b09e638c5550500bda4660c8188269a633c02 20783b1488c426437a92f16f779aa270fe67dc64 Sim Zhen Quan <<EMAIL>> 1746694936 +0800	checkout: moving from student-statistic-report-added-class-code-column to dev
20783b1488c426437a92f16f779aa270fe67dc64 e9a6efc740153bb50d199725c9ced5b00a1d6af8 Sim Zhen Quan <<EMAIL>> 1746694942 +0800	merge origin/student-statistic-report-added-class-code-column: Merge made by the 'ort' strategy.
e9a6efc740153bb50d199725c9ced5b00a1d6af8 256f70facf8209441441ea4869fbff461ff393a9 Sim Zhen Quan <<EMAIL>> 1746698562 +0800	checkout: moving from dev to fix/attendance-mark-deduction-type-filters
256f70facf8209441441ea4869fbff461ff393a9 281a59844b2d2f65895a881d04baa76149229efc Sim Zhen Quan <<EMAIL>> 1746698568 +0800	merge origin/main: Merge made by the 'ort' strategy.
281a59844b2d2f65895a881d04baa76149229efc e9a6efc740153bb50d199725c9ced5b00a1d6af8 Sim Zhen Quan <<EMAIL>> 1746700001 +0800	checkout: moving from fix/attendance-mark-deduction-type-filters to dev
e9a6efc740153bb50d199725c9ced5b00a1d6af8 c8d264bce7770084e3ec39ffc9ad3f5336ba70c1 Sim Zhen Quan <<EMAIL>> 1746700045 +0800	commit (merge): Merge branch 'fix/attendance-mark-deduction-type-filters' into dev
c8d264bce7770084e3ec39ffc9ad3f5336ba70c1 f0c93ad7a2607f3f17df2dd99e758f69ceebaae5 Sim Zhen Quan <<EMAIL>> 1746713338 +0800	commit: Deployed to DEV
f0c93ad7a2607f3f17df2dd99e758f69ceebaae5 15e2d16e802e9e2690986d55c2c2445ca5750878 Sim Zhen Quan <<EMAIL>> 1746713913 +0800	checkout: moving from dev to main
15e2d16e802e9e2690986d55c2c2445ca5750878 534a9e177e126eab7fe33c6f9a60613b4806ff8f Sim Zhen Quan <<EMAIL>> 1746713918 +0800	pull: Fast-forward
534a9e177e126eab7fe33c6f9a60613b4806ff8f adb122bcaddf54bae0d5f3bec8a6dcd5b11ce3f2 Sim Zhen Quan <<EMAIL>> 1746714101 +0800	pull: Fast-forward
adb122bcaddf54bae0d5f3bec8a6dcd5b11ce3f2 f02f1656f453475c0f5ed9c33c6e32e872dd5338 Sim Zhen Quan <<EMAIL>> 1746756057 +0800	commit: Deployed to PRD
f02f1656f453475c0f5ed9c33c6e32e872dd5338 7ba7281f8e2546f3de8442ac078d4e88dec06df3 Sim Zhen Quan <<EMAIL>> 1746756279 +0800	checkout: moving from main to timeslot-and-period-attendance-add-has-mark-deduction-column
7ba7281f8e2546f3de8442ac078d4e88dec06df3 64c58efd25474ab741c5ffebffda57024eda742f Sim Zhen Quan <<EMAIL>> 1746756443 +0800	commit (merge): Merge remote-tracking branch 'origin/main' into timeslot-and-period-attendance-add-has-mark-deduction-column
64c58efd25474ab741c5ffebffda57024eda742f f0c93ad7a2607f3f17df2dd99e758f69ceebaae5 Sim Zhen Quan <<EMAIL>> 1746757799 +0800	checkout: moving from timeslot-and-period-attendance-add-has-mark-deduction-column to dev
f0c93ad7a2607f3f17df2dd99e758f69ceebaae5 2959d413f162e223335e80ebada0013defb07aef Sim Zhen Quan <<EMAIL>> 1746757806 +0800	merge timeslot-and-period-attendance-add-has-mark-deduction-column: Merge made by the 'ort' strategy.
2959d413f162e223335e80ebada0013defb07aef 50e8c408e4a982de04d223e358509e8c08ac8f70 Sim Zhen Quan <<EMAIL>> 1746758714 +0800	commit: Deployed to DEV
50e8c408e4a982de04d223e358509e8c08ac8f70 729327b11d1b4f7cdcf4955d81f57a6ed7d2c9e3 Sim Zhen Quan <<EMAIL>> 1746758743 +0800	checkout: moving from dev to period-attendance-patch-script
729327b11d1b4f7cdcf4955d81f57a6ed7d2c9e3 9b5f5a10b427cb34da0cc803ce44f27580b2cd57 Sim Zhen Quan <<EMAIL>> 1746758776 +0800	merge timeslot-and-period-attendance-add-has-mark-deduction-column: Merge made by the 'ort' strategy.
9b5f5a10b427cb34da0cc803ce44f27580b2cd57 3e6f3c7503404d030be0a03a03d08145faa68703 Sim Zhen Quan <<EMAIL>> 1746761164 +0800	pull: Fast-forward
3e6f3c7503404d030be0a03a03d08145faa68703 50e8c408e4a982de04d223e358509e8c08ac8f70 Sim Zhen Quan <<EMAIL>> 1746761753 +0800	checkout: moving from period-attendance-patch-script to dev
50e8c408e4a982de04d223e358509e8c08ac8f70 235ae9f1172a50f1b9d7c1c1e713ffbb980893f8 Sim Zhen Quan <<EMAIL>> 1746761758 +0800	merge origin/timeslot-and-period-attendance-add-has-mark-deduction-column: Merge made by the 'ort' strategy.
235ae9f1172a50f1b9d7c1c1e713ffbb980893f8 f02f1656f453475c0f5ed9c33c6e32e872dd5338 Sim Zhen Quan <<EMAIL>> 1746768091 +0800	checkout: moving from dev to main
f02f1656f453475c0f5ed9c33c6e32e872dd5338 bb9eef3886a046805ccb3d3216e4f106c593f53b Sim Zhen Quan <<EMAIL>> 1746768106 +0800	commit: Pagination issue fix
bb9eef3886a046805ccb3d3216e4f106c593f53b 63148ac69c3f6985a1ce56cb491c6346c6b116de Sim Zhen Quan <<EMAIL>> 1746768114 +0800	checkout: moving from main to feature/promotion-mark-CRUD
63148ac69c3f6985a1ce56cb491c6346c6b116de 59d83d2c163b87348e5eaef7c058ae280047d301 Sim Zhen Quan <<EMAIL>> 1746768124 +0800	pull: Fast-forward
59d83d2c163b87348e5eaef7c058ae280047d301 c153dfa3b32db78c479d9ea0288dc6a8fbe41244 Sim Zhen Quan <<EMAIL>> 1746768125 +0800	merge origin/main: Merge made by the 'ort' strategy.
c153dfa3b32db78c479d9ea0288dc6a8fbe41244 235ae9f1172a50f1b9d7c1c1e713ffbb980893f8 Sim Zhen Quan <<EMAIL>> 1746769678 +0800	checkout: moving from feature/promotion-mark-CRUD to dev
235ae9f1172a50f1b9d7c1c1e713ffbb980893f8 c153dfa3b32db78c479d9ea0288dc6a8fbe41244 Sim Zhen Quan <<EMAIL>> 1746769829 +0800	checkout: moving from dev to feature/promotion-mark-CRUD
c153dfa3b32db78c479d9ea0288dc6a8fbe41244 fa52e9b003c24908f5bc870f9a352a5c51b44819 Sim Zhen Quan <<EMAIL>> 1746770001 +0800	checkout: moving from feature/promotion-mark-CRUD to exam-module-changes
fa52e9b003c24908f5bc870f9a352a5c51b44819 b59f95520d9c1d9f426ff4de550dcf96fe96fd4f Sim Zhen Quan <<EMAIL>> 1746770006 +0800	pull: Fast-forward
b59f95520d9c1d9f426ff4de550dcf96fe96fd4f ee7eae97a01d66f8a9bce57ef46d885652900f0a Sim Zhen Quan <<EMAIL>> 1746770006 +0800	merge origin/main: Merge made by the 'ort' strategy.
ee7eae97a01d66f8a9bce57ef46d885652900f0a 53dfe21d547ae62f35479a28cd8c5911a77da9a4 Sim Zhen Quan <<EMAIL>> 1746770034 +0800	checkout: moving from exam-module-changes to exam-validate-subject-by-semester-setting
53dfe21d547ae62f35479a28cd8c5911a77da9a4 52bf53dd30a8788bac7abae5325624a516373d9b Sim Zhen Quan <<EMAIL>> 1746770056 +0800	merge origin/main: Merge made by the 'ort' strategy.
52bf53dd30a8788bac7abae5325624a516373d9b ee7eae97a01d66f8a9bce57ef46d885652900f0a Sim Zhen Quan <<EMAIL>> 1746770113 +0800	checkout: moving from exam-validate-subject-by-semester-setting to exam-module-changes
ee7eae97a01d66f8a9bce57ef46d885652900f0a 52bf53dd30a8788bac7abae5325624a516373d9b Sim Zhen Quan <<EMAIL>> 1746770125 +0800	checkout: moving from exam-module-changes to exam-validate-subject-by-semester-setting
52bf53dd30a8788bac7abae5325624a516373d9b c153dfa3b32db78c479d9ea0288dc6a8fbe41244 Sim Zhen Quan <<EMAIL>> 1746771966 +0800	checkout: moving from exam-validate-subject-by-semester-setting to feature/promotion-mark-CRUD
c153dfa3b32db78c479d9ea0288dc6a8fbe41244 d8a3385d63c500d7268f6a4144e32608fe89f2fb Sim Zhen Quan <<EMAIL>> 1746772576 +0800	checkout: moving from feature/promotion-mark-CRUD to assign-class-to-student-using-new-latest-class-in-semester
d8a3385d63c500d7268f6a4144e32608fe89f2fb 53c565074f4c0a048f4de977e53fc2767993f779 Sim Zhen Quan <<EMAIL>> 1746772584 +0800	merge origin/main: Merge made by the 'ort' strategy.
53c565074f4c0a048f4de977e53fc2767993f779 676f91d95a9ba5e161b255500333626fb75f32e0 Sim Zhen Quan <<EMAIL>> 1746773462 +0800	commit: Reviewed
676f91d95a9ba5e161b255500333626fb75f32e0 235ae9f1172a50f1b9d7c1c1e713ffbb980893f8 Sim Zhen Quan <<EMAIL>> 1746773567 +0800	checkout: moving from assign-class-to-student-using-new-latest-class-in-semester to dev
235ae9f1172a50f1b9d7c1c1e713ffbb980893f8 1ecd7c2d1ceda0a6480e33b5747f4f9df2e45806 Sim Zhen Quan <<EMAIL>> 1746773573 +0800	merge assign-class-to-student-using-new-latest-class-in-semester: Merge made by the 'ort' strategy.
1ecd7c2d1ceda0a6480e33b5747f4f9df2e45806 749a7523adf4fad36bb5a988cdd1af7ab862bbfb Sim Zhen Quan <<EMAIL>> 1746773675 +0800	checkout: moving from dev to issuelog-25-hostel-savings-see-transaction-balance
749a7523adf4fad36bb5a988cdd1af7ab862bbfb c840b15d86e8b3130c47984dc3f48d5f9c8e9ff3 Sim Zhen Quan <<EMAIL>> 1746773681 +0800	pull: Fast-forward
c840b15d86e8b3130c47984dc3f48d5f9c8e9ff3 1ecd7c2d1ceda0a6480e33b5747f4f9df2e45806 Sim Zhen Quan <<EMAIL>> ********** +0800	checkout: moving from issuelog-25-hostel-savings-see-transaction-balance to dev
1ecd7c2d1ceda0a6480e33b5747f4f9df2e45806 0ae4284adc43da2aac16da31f3ebc02ee8ec358f Sim Zhen Quan <<EMAIL>> ********** +0800	merge origin/timeslot-and-period-attendance-add-has-mark-deduction-column: Merge made by the 'ort' strategy.
0ae4284adc43da2aac16da31f3ebc02ee8ec358f 7c75c11992201c58adf5876152477e10f5129247 Sim Zhen Quan <<EMAIL>> ********** +0800	checkout: moving from dev to report/hostel-report-saving-account
7c75c11992201c58adf5876152477e10f5129247 4fada80be3a64d965a991284cfe31ef6aa8897c6 Sim Zhen Quan <<EMAIL>> ********** +0800	commit (merge): Merge remote-tracking branch 'origin/main' into report/hostel-report-saving-account
4fada80be3a64d965a991284cfe31ef6aa8897c6 53fe959d80ddf8a794c53ab4bb1865308855aea5 Sim Zhen Quan <<EMAIL>> ********** +0800	commit: Review WIP
53fe959d80ddf8a794c53ab4bb1865308855aea5 bb9eef3886a046805ccb3d3216e4f106c593f53b Sim Zhen Quan <<EMAIL>> ********** +0800	checkout: moving from report/hostel-report-saving-account to main
bb9eef3886a046805ccb3d3216e4f106c593f53b bb9eef3886a046805ccb3d3216e4f106c593f53b Sim Zhen Quan <<EMAIL>> ********** +0800	checkout: moving from main to lucas/repository-fix
bb9eef3886a046805ccb3d3216e4f106c593f53b 5bee99ef0f40fedc66adfa0f91abafcaf1407df0 Sim Zhen Quan <<EMAIL>> ********** +0800	commit: WIP
5bee99ef0f40fedc66adfa0f91abafcaf1407df0 3e53500f680264aa6a1a02118e5a73df24b6c694 Sim Zhen Quan <<EMAIL>> ********** +0800	commit: Done
3e53500f680264aa6a1a02118e5a73df24b6c694 bb9eef3886a046805ccb3d3216e4f106c593f53b Sim Zhen Quan <<EMAIL>> ********** +0800	checkout: moving from lucas/repository-fix to main
bb9eef3886a046805ccb3d3216e4f106c593f53b bb9eef3886a046805ccb3d3216e4f106c593f53b Sim Zhen Quan <<EMAIL>> ********** +0800	checkout: moving from main to repository-fix
bb9eef3886a046805ccb3d3216e4f106c593f53b 3e0b41aa1f6d9702082cfe676600d5202cd34fb8 Sim Zhen Quan <<EMAIL>> ********** +0800	checkout: moving from repository-fix to noah-fix-repo
3e0b41aa1f6d9702082cfe676600d5202cd34fb8 7062f274992402077fabb3cf7a676339cef4a4c8 Sim Zhen Quan <<EMAIL>> 1747068360 +0800	commit: Reviewed
7062f274992402077fabb3cf7a676339cef4a4c8 eb26fb35298bac85e81e16f51b1b9d388226767a Sim Zhen Quan <<EMAIL>> 1747068404 +0800	checkout: moving from noah-fix-repo to fix-form-request
eb26fb35298bac85e81e16f51b1b9d388226767a 1d585bce2ea286d000cfcb7f2fba6dc1e2094055 Sim Zhen Quan <<EMAIL>> 1747068428 +0800	merge origin/repository-fix: Merge made by the 'ort' strategy.
1d585bce2ea286d000cfcb7f2fba6dc1e2094055 f81b706f15dc7f65ca47998ed28d2a5a2560a28e Sim Zhen Quan <<EMAIL>> 1747069115 +0800	checkout: moving from fix-form-request to alvin-update-repository
f81b706f15dc7f65ca47998ed28d2a5a2560a28e 56412647dddf909f83c32155000c7bca43332086 Sim Zhen Quan <<EMAIL>> 1747069123 +0800	merge origin/repository-fix: Merge made by the 'ort' strategy.
56412647dddf909f83c32155000c7bca43332086 7374507d91c10c777bddd2fc8560a83481a3db46 Sim Zhen Quan <<EMAIL>> 1747069402 +0800	checkout: moving from alvin-update-repository to ryan-repository-fix
7374507d91c10c777bddd2fc8560a83481a3db46 47651212951046eca4a2f9f1b861a5a656f4c9a7 Sim Zhen Quan <<EMAIL>> 1747069461 +0800	commit (merge): Merge remote-tracking branch 'origin/repository-fix' into ryan-repository-fix
47651212951046eca4a2f9f1b861a5a656f4c9a7 41cb4ce0d99f5eb90b99f4571479070b67e78ac1 Sim Zhen Quan <<EMAIL>> 1747100604 +0800	checkout: moving from ryan-repository-fix to fix/repository-to-isset-kimi
41cb4ce0d99f5eb90b99f4571479070b67e78ac1 5d1ee3bbcac05327ef26c393a89b3709236e4749 Sim Zhen Quan <<EMAIL>> 1747100611 +0800	merge origin/repository-fix: Merge made by the 'ort' strategy.
5d1ee3bbcac05327ef26c393a89b3709236e4749 0ae4284adc43da2aac16da31f3ebc02ee8ec358f Sim Zhen Quan <<EMAIL>> 1747101736 +0800	checkout: moving from fix/repository-to-isset-kimi to dev
0ae4284adc43da2aac16da31f3ebc02ee8ec358f 6eb648b216596044023cde0cfe5b06685b80bf82 Sim Zhen Quan <<EMAIL>> 1747101784 +0800	commit (merge): Merge remote-tracking branch 'origin/repository-fix' into dev
6eb648b216596044023cde0cfe5b06685b80bf82 1283f0f7cfceefc027d3c93bb9a6d4fe828cfa4e Sim Zhen Quan <<EMAIL>> 1747102605 +0800	commit: Deployed to dev
1283f0f7cfceefc027d3c93bb9a6d4fe828cfa4e c153dfa3b32db78c479d9ea0288dc6a8fbe41244 Sim Zhen Quan <<EMAIL>> 1747102630 +0800	checkout: moving from dev to feature/promotion-mark-CRUD
c153dfa3b32db78c479d9ea0288dc6a8fbe41244 5c573a486435225f81ab51bd6fc47a5ae194527b Sim Zhen Quan <<EMAIL>> 1747102635 +0800	pull: Fast-forward
5c573a486435225f81ab51bd6fc47a5ae194527b 561b61a90422f80ab2f5993ed68ea9fcc89a4673 Sim Zhen Quan <<EMAIL>> 1747103649 +0800	commit: Review WIP
561b61a90422f80ab2f5993ed68ea9fcc89a4673 af2fc5b5340faf8755b8bb1971a2f543b3b4ce4a Sim Zhen Quan <<EMAIL>> 1747104399 +0800	commit: Reviewed
af2fc5b5340faf8755b8bb1971a2f543b3b4ce4a ee7eae97a01d66f8a9bce57ef46d885652900f0a Sim Zhen Quan <<EMAIL>> 1747104443 +0800	checkout: moving from feature/promotion-mark-CRUD to exam-module-changes
ee7eae97a01d66f8a9bce57ef46d885652900f0a 25e4b0a40ccb90240216c47d2b188fb4d469a8e2 Sim Zhen Quan <<EMAIL>> 1747104449 +0800	pull: Fast-forward
25e4b0a40ccb90240216c47d2b188fb4d469a8e2 60bd431e5dd3f83b1d20c12fafdb725355b793da Sim Zhen Quan <<EMAIL>> 1747104719 +0800	checkout: moving from exam-module-changes to jira-380-exam-uat-feedback
60bd431e5dd3f83b1d20c12fafdb725355b793da 126c6b1b2582cce943d378cb9a42590695c7fba8 Sim Zhen Quan <<EMAIL>> 1747104727 +0800	merge origin/exam-module-changes: Merge made by the 'ort' strategy.
126c6b1b2582cce943d378cb9a42590695c7fba8 bb9eef3886a046805ccb3d3216e4f106c593f53b Sim Zhen Quan <<EMAIL>> 1747106181 +0800	checkout: moving from jira-380-exam-uat-feedback to repository-fix
bb9eef3886a046805ccb3d3216e4f106c593f53b 18772bd26098aaba1dafd350f7ea823c3f736954 Sim Zhen Quan <<EMAIL>> 1747106185 +0800	pull: Fast-forward
18772bd26098aaba1dafd350f7ea823c3f736954 bb9eef3886a046805ccb3d3216e4f106c593f53b Sim Zhen Quan <<EMAIL>> 1747106882 +0800	checkout: moving from repository-fix to main
bb9eef3886a046805ccb3d3216e4f106c593f53b bb9eef3886a046805ccb3d3216e4f106c593f53b Sim Zhen Quan <<EMAIL>> 1747106928 +0800	checkout: moving from main to staging/2025-05-13
bb9eef3886a046805ccb3d3216e4f106c593f53b 18772bd26098aaba1dafd350f7ea823c3f736954 Sim Zhen Quan <<EMAIL>> 1747107115 +0800	checkout: moving from staging/2025-05-13 to repository-fix
18772bd26098aaba1dafd350f7ea823c3f736954 0b60906babcc41ed25a89077bf99ee7a0f070331 Sim Zhen Quan <<EMAIL>> 1747107121 +0800	merge origin/staging/2025-05-13: Merge made by the 'ort' strategy.
0b60906babcc41ed25a89077bf99ee7a0f070331 41f3fe006ae4c0a95122f7191c6cf9b5bdd27afa Sim Zhen Quan <<EMAIL>> 1747109044 +0800	commit: WIP
41f3fe006ae4c0a95122f7191c6cf9b5bdd27afa 8bf5152d5d434dfb0aa929f31ba262665a61e5cf Sim Zhen Quan <<EMAIL>> 1747109907 +0800	commit: Bug fix
8bf5152d5d434dfb0aa929f31ba262665a61e5cf 1283f0f7cfceefc027d3c93bb9a6d4fe828cfa4e Sim Zhen Quan <<EMAIL>> 1747110478 +0800	checkout: moving from repository-fix to dev
1283f0f7cfceefc027d3c93bb9a6d4fe828cfa4e 1bb9d8f94f7861e0673e16408d30d0db6c16c1df Sim Zhen Quan <<EMAIL>> 1747110485 +0800	merge origin/repository-fix: Merge made by the 'ort' strategy.
1bb9d8f94f7861e0673e16408d30d0db6c16c1df 87eaa5344e47680a5dea7dccc8b245af9c6bacb2 Sim Zhen Quan <<EMAIL>> 1747110504 +0800	merge origin/exam-module-changes: Merge made by the 'ort' strategy.
87eaa5344e47680a5dea7dccc8b245af9c6bacb2 87eaa5344e47680a5dea7dccc8b245af9c6bacb2 Sim Zhen Quan <<EMAIL>> 1747119893 +0800	checkout: moving from dev to dev
87eaa5344e47680a5dea7dccc8b245af9c6bacb2 1b4355bfa32f47615ce4bd69d0e42a6139213640 Sim Zhen Quan <<EMAIL>> 1747119901 +0800	commit: Deployed to dev
1b4355bfa32f47615ce4bd69d0e42a6139213640 bfd041b9e094aa5450623c713abdc497b0c210c3 Sim Zhen Quan <<EMAIL>> 1747119979 +0800	commit (merge): Merge remote-tracking branch 'origin/seed-exam-data' into dev
bfd041b9e094aa5450623c713abdc497b0c210c3 126c6b1b2582cce943d378cb9a42590695c7fba8 Sim Zhen Quan <<EMAIL>> 1747195209 +0800	checkout: moving from dev to jira-380-exam-uat-feedback
126c6b1b2582cce943d378cb9a42590695c7fba8 bb9eef3886a046805ccb3d3216e4f106c593f53b Sim Zhen Quan <<EMAIL>> 1747195224 +0800	checkout: moving from jira-380-exam-uat-feedback to main
bb9eef3886a046805ccb3d3216e4f106c593f53b 6c0cdcd307ead6c259d2c32a12252476769b5228 Sim Zhen Quan <<EMAIL>> 1747195254 +0800	pull: Fast-forward
6c0cdcd307ead6c259d2c32a12252476769b5228 30fe292833e03147752506546cec0bea4f8275a0 Sim Zhen Quan <<EMAIL>> 1747195255 +0800	commit: Added report card worker
30fe292833e03147752506546cec0bea4f8275a0 126c6b1b2582cce943d378cb9a42590695c7fba8 Sim Zhen Quan <<EMAIL>> 1747195263 +0800	checkout: moving from main to jira-380-exam-uat-feedback
126c6b1b2582cce943d378cb9a42590695c7fba8 ad29d18d5735be9d944dfbaeaca3b6670fcb2392 Sim Zhen Quan <<EMAIL>> 1747195268 +0800	pull: Fast-forward
ad29d18d5735be9d944dfbaeaca3b6670fcb2392 ad29d18d5735be9d944dfbaeaca3b6670fcb2392 Sim Zhen Quan <<EMAIL>> 1747195288 +0800	checkout: moving from jira-380-exam-uat-feedback to jira-380-exam-uat-feedback
ad29d18d5735be9d944dfbaeaca3b6670fcb2392 6f1b47ad9b96c19036ab01124bd1731385e7c47a Sim Zhen Quan <<EMAIL>> 1747195641 +0800	checkout: moving from jira-380-exam-uat-feedback to JIRA-436-exam-changes
6f1b47ad9b96c19036ab01124bd1731385e7c47a 654c76d1b26396a24ff51143a6dd73f044c75b99 Sim Zhen Quan <<EMAIL>> 1747196827 +0800	pull: Fast-forward
654c76d1b26396a24ff51143a6dd73f044c75b99 bfd041b9e094aa5450623c713abdc497b0c210c3 Sim Zhen Quan <<EMAIL>> 1747196830 +0800	checkout: moving from JIRA-436-exam-changes to dev
bfd041b9e094aa5450623c713abdc497b0c210c3 2e3e41c7cb5bcb6cd34cd27ac3f43f2a887f25d6 Sim Zhen Quan <<EMAIL>> 1747196834 +0800	merge JIRA-436-exam-changes: Merge made by the 'ort' strategy.
2e3e41c7cb5bcb6cd34cd27ac3f43f2a887f25d6 2e3e41c7cb5bcb6cd34cd27ac3f43f2a887f25d6 Sim Zhen Quan <<EMAIL>> 1747198068 +0800	reset: moving to HEAD
2e3e41c7cb5bcb6cd34cd27ac3f43f2a887f25d6 654c76d1b26396a24ff51143a6dd73f044c75b99 Sim Zhen Quan <<EMAIL>> 1747198070 +0800	checkout: moving from dev to JIRA-436-exam-changes
654c76d1b26396a24ff51143a6dd73f044c75b99 cbad4bb56dce0b3ee3950a43cb23904ced376ab5 Sim Zhen Quan <<EMAIL>> 1747198077 +0800	pull: Fast-forward
cbad4bb56dce0b3ee3950a43cb23904ced376ab5 c25bad2f033e93bb6e3b34cace32af8c0e6f3882 Sim Zhen Quan <<EMAIL>> 1747198099 +0800	commit: Undo accidental comment
c25bad2f033e93bb6e3b34cace32af8c0e6f3882 2e3e41c7cb5bcb6cd34cd27ac3f43f2a887f25d6 Sim Zhen Quan <<EMAIL>> 1747198131 +0800	checkout: moving from JIRA-436-exam-changes to dev
2e3e41c7cb5bcb6cd34cd27ac3f43f2a887f25d6 8d787cefa1ecd0cb5e2de6f56788ab56a044a90a Sim Zhen Quan <<EMAIL>> 1747198137 +0800	merge origin/main: Merge made by the 'ort' strategy.
8d787cefa1ecd0cb5e2de6f56788ab56a044a90a f07cd64cdb45f9a279810c55bc82535297f4792e Sim Zhen Quan <<EMAIL>> 1747198508 +0800	merge JIRA-436-exam-changes: Merge made by the 'ort' strategy.
f07cd64cdb45f9a279810c55bc82535297f4792e f07cd64cdb45f9a279810c55bc82535297f4792e Sim Zhen Quan <<EMAIL>> 1747203628 +0800	checkout: moving from dev to lucas-testing
f07cd64cdb45f9a279810c55bc82535297f4792e bf2a3905d23b95a779658ee560f16a4408221cde Sim Zhen Quan <<EMAIL>> 1747203638 +0800	commit: Test
bf2a3905d23b95a779658ee560f16a4408221cde c25bad2f033e93bb6e3b34cace32af8c0e6f3882 Sim Zhen Quan <<EMAIL>> 1747204456 +0800	checkout: moving from lucas-testing to JIRA-436-exam-changes
c25bad2f033e93bb6e3b34cace32af8c0e6f3882 537f2e9ae6bcb95c19327000d5a384200989b8cb Sim Zhen Quan <<EMAIL>> 1747204467 +0800	pull: Fast-forward
537f2e9ae6bcb95c19327000d5a384200989b8cb 0829aefac2c607301acdaf0990aca3f83456a0ce Sim Zhen Quan <<EMAIL>> 1747204493 +0800	commit: Uncomment queue
0829aefac2c607301acdaf0990aca3f83456a0ce 1ecdbbcd780d1b5760190f11262f84409be1069f Sim Zhen Quan <<EMAIL>> 1747204653 +0800	commit: Updated grading framework
1ecdbbcd780d1b5760190f11262f84409be1069f f07cd64cdb45f9a279810c55bc82535297f4792e Sim Zhen Quan <<EMAIL>> 1747204712 +0800	checkout: moving from JIRA-436-exam-changes to dev
f07cd64cdb45f9a279810c55bc82535297f4792e 7be6877a04390b6aaa1788cbee3b58f15dac1aff Sim Zhen Quan <<EMAIL>> 1747204712 +0800	merge JIRA-436-exam-changes: Merge made by the 'ort' strategy.
7be6877a04390b6aaa1788cbee3b58f15dac1aff 1ecdbbcd780d1b5760190f11262f84409be1069f Sim Zhen Quan <<EMAIL>> 1747215053 +0800	checkout: moving from dev to JIRA-436-exam-changes
1ecdbbcd780d1b5760190f11262f84409be1069f e69be91393589fbe1e3822be078383e30ce397ac Sim Zhen Quan <<EMAIL>> 1747215110 +0800	commit: Update report formatting
e69be91393589fbe1e3822be078383e30ce397ac 7be6877a04390b6aaa1788cbee3b58f15dac1aff Sim Zhen Quan <<EMAIL>> 1747215135 +0800	checkout: moving from JIRA-436-exam-changes to dev
7be6877a04390b6aaa1788cbee3b58f15dac1aff 83f69259b4c23c823843909bec0df5595b2c45d7 Sim Zhen Quan <<EMAIL>> 1747215159 +0800	merge JIRA-436-exam-changes: Merge made by the 'ort' strategy.
83f69259b4c23c823843909bec0df5595b2c45d7 e69be91393589fbe1e3822be078383e30ce397ac Sim Zhen Quan <<EMAIL>> 1747242924 +0800	checkout: moving from dev to JIRA-436-exam-changes
e69be91393589fbe1e3822be078383e30ce397ac 0d985738524f1954b0731373ec6b9ab4fd72268a Sim Zhen Quan <<EMAIL>> 1747242942 +0800	pull: Fast-forward
0d985738524f1954b0731373ec6b9ab4fd72268a 1a0fa22d1cbf094baf4d77b4fa8ae3546b697c61 Sim Zhen Quan <<EMAIL>> 1747242957 +0800	commit: Update report formatting
1a0fa22d1cbf094baf4d77b4fa8ae3546b697c61 30fe292833e03147752506546cec0bea4f8275a0 Sim Zhen Quan <<EMAIL>> 1747242978 +0800	checkout: moving from JIRA-436-exam-changes to main
30fe292833e03147752506546cec0bea4f8275a0 bb9eef3886a046805ccb3d3216e4f106c593f53b Sim Zhen Quan <<EMAIL>> 1747243010 +0800	checkout: moving from main to staging/2025-05-13
bb9eef3886a046805ccb3d3216e4f106c593f53b 07fa86eefc1acd5a7cc8c47dda2d342054a92e8a Sim Zhen Quan <<EMAIL>> 1747243015 +0800	pull: Fast-forward
07fa86eefc1acd5a7cc8c47dda2d342054a92e8a 3b06ce1f1b3c6ef8a84c7c468130956fb695bd8a Sim Zhen Quan <<EMAIL>> 1747243054 +0800	commit (merge): Merge remote-tracking branch 'origin/main' into staging/2025-05-13
3b06ce1f1b3c6ef8a84c7c468130956fb695bd8a 30fe292833e03147752506546cec0bea4f8275a0 Sim Zhen Quan <<EMAIL>> 1747243079 +0800	checkout: moving from staging/2025-05-13 to main
30fe292833e03147752506546cec0bea4f8275a0 56e4364b063771a8dfdc26af56781b90d349fbf7 Sim Zhen Quan <<EMAIL>> 1747243084 +0800	pull: Fast-forward
56e4364b063771a8dfdc26af56781b90d349fbf7 39015ec5ef1cdb1d2f0b3e4fac43659538a1c3ef Sim Zhen Quan <<EMAIL>> 1747244346 +0800	commit: Deployed to prd
39015ec5ef1cdb1d2f0b3e4fac43659538a1c3ef 01afb39fae367143b8ff01aededd7d3e3b4467d5 Sim Zhen Quan <<EMAIL>> 1747297670 +0800	checkout: moving from main to exam-posting-prechecks-handle-is-exempted
01afb39fae367143b8ff01aededd7d3e3b4467d5 f16f7c160de8d14c59865988f326c030e0906587 Sim Zhen Quan <<EMAIL>> 1747297683 +0800	merge origin/exam-module-changes: Merge made by the 'ort' strategy.
f16f7c160de8d14c59865988f326c030e0906587 849a4c285bf78e085ca9bb6ac403122bfe565dec Sim Zhen Quan <<EMAIL>> 1747298057 +0800	checkout: moving from exam-posting-prechecks-handle-is-exempted to seed-exam-data
849a4c285bf78e085ca9bb6ac403122bfe565dec 9888834d088462cfcf9c7c4141266274c95f9e8a Sim Zhen Quan <<EMAIL>> 1747298605 +0800	commit (merge): Merge remote-tracking branch 'origin/exam-module-changes' into seed-exam-data
9888834d088462cfcf9c7c4141266274c95f9e8a 25e4b0a40ccb90240216c47d2b188fb4d469a8e2 Sim Zhen Quan <<EMAIL>> 1747299146 +0800	checkout: moving from seed-exam-data to exam-module-changes
25e4b0a40ccb90240216c47d2b188fb4d469a8e2 09f6350ab4a41159556210187528303750604f0e Sim Zhen Quan <<EMAIL>> 1747299152 +0800	pull: Fast-forward
09f6350ab4a41159556210187528303750604f0e 9fecece139c77f54607e0ee5599d530dff75ec7f Sim Zhen Quan <<EMAIL>> 1747299153 +0800	merge origin/main: Merge made by the 'ort' strategy.
9fecece139c77f54607e0ee5599d530dff75ec7f 1a0fa22d1cbf094baf4d77b4fa8ae3546b697c61 Sim Zhen Quan <<EMAIL>> 1747299653 +0800	checkout: moving from exam-module-changes to JIRA-436-exam-changes
1a0fa22d1cbf094baf4d77b4fa8ae3546b697c61 f026caff06b56f89f6cdd0f591a5e3228a0b8bef Sim Zhen Quan <<EMAIL>> 1747299658 +0800	pull: Fast-forward
f026caff06b56f89f6cdd0f591a5e3228a0b8bef c4f5b02c4e2edaf6ddb16fd5c4f392fdf98d61ec Sim Zhen Quan <<EMAIL>> 1747299659 +0800	merge exam-module-changes: Merge made by the 'ort' strategy.
c4f5b02c4e2edaf6ddb16fd5c4f392fdf98d61ec 978dbc014a5f6a1ce3037d64de2764b2fd7811ba Sim Zhen Quan <<EMAIL>> 1747299829 +0800	commit (merge): Merge remote-tracking branch 'origin/exam-module-changes' into JIRA-436-exam-changes
978dbc014a5f6a1ce3037d64de2764b2fd7811ba 83f69259b4c23c823843909bec0df5595b2c45d7 Sim Zhen Quan <<EMAIL>> 1747299943 +0800	checkout: moving from JIRA-436-exam-changes to dev
83f69259b4c23c823843909bec0df5595b2c45d7 6c2c79244c1e1b8540e8cbc12e0bbf4e2e06dcfc Sim Zhen Quan <<EMAIL>> 1747299955 +0800	merge origin/JIRA-436-exam-changes: Merge made by the 'ort' strategy.
6c2c79244c1e1b8540e8cbc12e0bbf4e2e06dcfc 978dbc014a5f6a1ce3037d64de2764b2fd7811ba Sim Zhen Quan <<EMAIL>> 1747391456 +0800	checkout: moving from dev to JIRA-436-exam-changes
978dbc014a5f6a1ce3037d64de2764b2fd7811ba 180f8fd8a2842524ad0a383b10d52ed6afc5154e Sim Zhen Quan <<EMAIL>> 1747391462 +0800	pull: Fast-forward
180f8fd8a2842524ad0a383b10d52ed6afc5154e 39015ec5ef1cdb1d2f0b3e4fac43659538a1c3ef Sim Zhen Quan <<EMAIL>> 1747590091 +0800	checkout: moving from JIRA-436-exam-changes to main
39015ec5ef1cdb1d2f0b3e4fac43659538a1c3ef c6194433e2e1256da72317db5aefd7ed1fb88269 Sim Zhen Quan <<EMAIL>> 1747590119 +0800	commit: Changed report header
c6194433e2e1256da72317db5aefd7ed1fb88269 6c2c79244c1e1b8540e8cbc12e0bbf4e2e06dcfc Sim Zhen Quan <<EMAIL>> 1747625746 +0800	checkout: moving from main to dev
6c2c79244c1e1b8540e8cbc12e0bbf4e2e06dcfc 9d44782f69d9cb5e2753f304e4220962efb5dcf7 Sim Zhen Quan <<EMAIL>> 1747625939 +0800	commit: Fix merge conflicts
9d44782f69d9cb5e2753f304e4220962efb5dcf7 c6194433e2e1256da72317db5aefd7ed1fb88269 Sim Zhen Quan <<EMAIL>> 1747637535 +0800	checkout: moving from dev to main
c6194433e2e1256da72317db5aefd7ed1fb88269 8536e64e3681503deda5c860fc678553f430eede Sim Zhen Quan <<EMAIL>> 1747637704 +0800	commit: Hotfix hostel in out report repository
8536e64e3681503deda5c860fc678553f430eede 8536e64e3681503deda5c860fc678553f430eede Sim Zhen Quan <<EMAIL>> 1747638360 +0800	checkout: moving from main to staging/2025-05-19
8536e64e3681503deda5c860fc678553f430eede 281a59844b2d2f65895a881d04baa76149229efc Sim Zhen Quan <<EMAIL>> 1747638385 +0800	checkout: moving from staging/2025-05-19 to fix/attendance-mark-deduction-type-filters
281a59844b2d2f65895a881d04baa76149229efc eefd2d30dbc77a5e6d762f51a3b78f722abc7303 Sim Zhen Quan <<EMAIL>> 1747638465 +0800	commit (merge): Merge remote-tracking branch 'origin/main' into fix/attendance-mark-deduction-type-filters
eefd2d30dbc77a5e6d762f51a3b78f722abc7303 180f8fd8a2842524ad0a383b10d52ed6afc5154e Sim Zhen Quan <<EMAIL>> 1747638548 +0800	checkout: moving from fix/attendance-mark-deduction-type-filters to JIRA-436-exam-changes
180f8fd8a2842524ad0a383b10d52ed6afc5154e 9fecece139c77f54607e0ee5599d530dff75ec7f Sim Zhen Quan <<EMAIL>> 1747638565 +0800	checkout: moving from JIRA-436-exam-changes to exam-module-changes
9fecece139c77f54607e0ee5599d530dff75ec7f 4c8178d7531d3a183fdafa4568ab8d77bf7a7d9a Sim Zhen Quan <<EMAIL>> 1747638570 +0800	pull: Fast-forward
4c8178d7531d3a183fdafa4568ab8d77bf7a7d9a a8d582bd8d23ad224ebb889e1855a6cd393f1348 Sim Zhen Quan <<EMAIL>> 1747638571 +0800	merge origin/main: Merge made by the 'ort' strategy.
a8d582bd8d23ad224ebb889e1855a6cd393f1348 180f8fd8a2842524ad0a383b10d52ed6afc5154e Sim Zhen Quan <<EMAIL>> 1747638583 +0800	checkout: moving from exam-module-changes to JIRA-436-exam-changes
180f8fd8a2842524ad0a383b10d52ed6afc5154e 17de40081e310c31245c05dc8fda3f96a2209dd2 Sim Zhen Quan <<EMAIL>> 1747638593 +0800	merge origin/exam-module-changes: Merge made by the 'ort' strategy.
17de40081e310c31245c05dc8fda3f96a2209dd2 55003cca80ad2eba1437f543be0558616b9f844d Sim Zhen Quan <<EMAIL>> 1747640281 +0800	commit: Reviewed
55003cca80ad2eba1437f543be0558616b9f844d 9d44782f69d9cb5e2753f304e4220962efb5dcf7 Sim Zhen Quan <<EMAIL>> 1747640695 +0800	checkout: moving from JIRA-436-exam-changes to dev
9d44782f69d9cb5e2753f304e4220962efb5dcf7 7f0a3d140a091241d5e2e1e89c25a4d001da6916 Sim Zhen Quan <<EMAIL>> 1747640708 +0800	merge origin/exam-module-changes: Merge made by the 'ort' strategy.
7f0a3d140a091241d5e2e1e89c25a4d001da6916 68748fa7bd793a544696060b324c9c46f4f6425d Sim Zhen Quan <<EMAIL>> 1747641514 +0800	commit: Deployed to dev
68748fa7bd793a544696060b324c9c46f4f6425d 300720c1f1866ff011b1ef912d441915bb2fd598 Sim Zhen Quan <<EMAIL>> 1747641530 +0800	checkout: moving from dev to feature/enrollment-session-CRUD
300720c1f1866ff011b1ef912d441915bb2fd598 8536e64e3681503deda5c860fc678553f430eede Sim Zhen Quan <<EMAIL>> 1747644209 +0800	checkout: moving from feature/enrollment-session-CRUD to main
8536e64e3681503deda5c860fc678553f430eede 8536e64e3681503deda5c860fc678553f430eede Sim Zhen Quan <<EMAIL>> 1747644225 +0800	checkout: moving from main to add-guardian-resource-to-hostel-in-out-record
8536e64e3681503deda5c860fc678553f430eede 68748fa7bd793a544696060b324c9c46f4f6425d Sim Zhen Quan <<EMAIL>> 1747644385 +0800	checkout: moving from add-guardian-resource-to-hostel-in-out-record to dev
68748fa7bd793a544696060b324c9c46f4f6425d 8536e64e3681503deda5c860fc678553f430eede Sim Zhen Quan <<EMAIL>> 1747644393 +0800	checkout: moving from dev to add-guardian-resource-to-hostel-in-out-record
8536e64e3681503deda5c860fc678553f430eede 166a0055dfa4606d5bd2ceb7d91e37d5dd7c0fd7 Sim Zhen Quan <<EMAIL>> 1747644524 +0800	commit: Added guardian and student includes into hostel in out
166a0055dfa4606d5bd2ceb7d91e37d5dd7c0fd7 68748fa7bd793a544696060b324c9c46f4f6425d Sim Zhen Quan <<EMAIL>> 1747644536 +0800	checkout: moving from add-guardian-resource-to-hostel-in-out-record to dev
68748fa7bd793a544696060b324c9c46f4f6425d 7c76fb71e37c86de371fb1c23c596ca0e6d65527 Sim Zhen Quan <<EMAIL>> 1747644539 +0800	merge add-guardian-resource-to-hostel-in-out-record: Merge made by the 'ort' strategy.
7c76fb71e37c86de371fb1c23c596ca0e6d65527 43406cbb1b40e40908b07c7c34aec8d833ed8793 Sim Zhen Quan <<EMAIL>> 1747646585 +0800	checkout: moving from dev to fix/update-validation-when-create-billing-doc
43406cbb1b40e40908b07c7c34aec8d833ed8793 edffd32b155e5ef606b1bb99bfc6b0a337aafadb Sim Zhen Quan <<EMAIL>> 1747646591 +0800	merge origin/main: Merge made by the 'ort' strategy.
edffd32b155e5ef606b1bb99bfc6b0a337aafadb 8536e64e3681503deda5c860fc678553f430eede Sim Zhen Quan <<EMAIL>> 1747707943 +0800	checkout: moving from fix/update-validation-when-create-billing-doc to main
8536e64e3681503deda5c860fc678553f430eede 7068f7efbcc996503b57996f24b4260a06b10d6a Sim Zhen Quan <<EMAIL>> 1747708359 +0800	commit: Added refresh view table for exam posting pre check mat view every 5 mins
7068f7efbcc996503b57996f24b4260a06b10d6a edffd32b155e5ef606b1bb99bfc6b0a337aafadb Sim Zhen Quan <<EMAIL>> 1747708989 +0800	checkout: moving from main to fix/update-validation-when-create-billing-doc
edffd32b155e5ef606b1bb99bfc6b0a337aafadb 22ef14452f35905e4c388acf0362d52ca9eaef0b Sim Zhen Quan <<EMAIL>> 1747708995 +0800	merge origin/main: Merge made by the 'ort' strategy.
22ef14452f35905e4c388acf0362d52ca9eaef0b a8d582bd8d23ad224ebb889e1855a6cd393f1348 Sim Zhen Quan <<EMAIL>> 1747715179 +0800	checkout: moving from fix/update-validation-when-create-billing-doc to exam-module-changes
a8d582bd8d23ad224ebb889e1855a6cd393f1348 5d3e61cf1fc926c2876a99caf8760932b7fc79db Sim Zhen Quan <<EMAIL>> 1747715185 +0800	pull: Fast-forward
5d3e61cf1fc926c2876a99caf8760932b7fc79db 22ef14452f35905e4c388acf0362d52ca9eaef0b Sim Zhen Quan <<EMAIL>> 1747725770 +0800	checkout: moving from exam-module-changes to fix/update-validation-when-create-billing-doc
22ef14452f35905e4c388acf0362d52ca9eaef0b 5d3e61cf1fc926c2876a99caf8760932b7fc79db Sim Zhen Quan <<EMAIL>> 1747726158 +0800	checkout: moving from fix/update-validation-when-create-billing-doc to exam-module-changes
5d3e61cf1fc926c2876a99caf8760932b7fc79db ac6a3c0c408ee6400b1e2bca34bdff7ad69d31d9 Sim Zhen Quan <<EMAIL>> 1747726161 +0800	merge origin/main: Merge made by the 'ort' strategy.
ac6a3c0c408ee6400b1e2bca34bdff7ad69d31d9 91b9385b03e321169210ce82eaa9a75bf0d2bfd6 Sim Zhen Quan <<EMAIL>> 1747726171 +0800	checkout: moving from exam-module-changes to JIRA-449-exam-module-changes-v2
91b9385b03e321169210ce82eaa9a75bf0d2bfd6 2c61ebe9514d9212b5711b9428197c731cd8489e Sim Zhen Quan <<EMAIL>> 1747726178 +0800	merge exam-module-changes: Merge made by the 'ort' strategy.
2c61ebe9514d9212b5711b9428197c731cd8489e 7c76fb71e37c86de371fb1c23c596ca0e6d65527 Sim Zhen Quan <<EMAIL>> 1747734072 +0800	checkout: moving from JIRA-449-exam-module-changes-v2 to dev
7c76fb71e37c86de371fb1c23c596ca0e6d65527 64c63d1e3f946d9299abe45175cc5bc7d53c23b5 Sim Zhen Quan <<EMAIL>> 1747734075 +0800	merge JIRA-449-exam-module-changes-v2: Merge made by the 'ort' strategy.
64c63d1e3f946d9299abe45175cc5bc7d53c23b5 3f1e67b1966f69be52845926314df0f1bb64c613 Sim Zhen Quan <<EMAIL>> 1747791957 +0800	commit: Deployed to dev
3f1e67b1966f69be52845926314df0f1bb64c613 ad58f6d6f37fc6574a21ce586a1a6a05131f1ade Sim Zhen Quan <<EMAIL>> 1747791966 +0800	commit: Fix kernel issue
ad58f6d6f37fc6574a21ce586a1a6a05131f1ade ac6a3c0c408ee6400b1e2bca34bdff7ad69d31d9 Sim Zhen Quan <<EMAIL>> 1747791985 +0800	checkout: moving from dev to exam-module-changes
ac6a3c0c408ee6400b1e2bca34bdff7ad69d31d9 d1a40b32e3085ed4cd2851b29ec6f3dc4a2dc3ab Sim Zhen Quan <<EMAIL>> 1747791991 +0800	cherry-pick: Fix kernel issue
d1a40b32e3085ed4cd2851b29ec6f3dc4a2dc3ab 300720c1f1866ff011b1ef912d441915bb2fd598 Sim Zhen Quan <<EMAIL>> 1747792016 +0800	checkout: moving from exam-module-changes to feature/enrollment-session-CRUD
300720c1f1866ff011b1ef912d441915bb2fd598 db2093540287ae4fef0480d5a14c461b9bae2322 Sim Zhen Quan <<EMAIL>> 1747792022 +0800	pull: Fast-forward
db2093540287ae4fef0480d5a14c461b9bae2322 1b6f03c76c85c7fc9898704cb306e1f5acfc91f7 Sim Zhen Quan <<EMAIL>> 1747792023 +0800	merge origin/main: Merge made by the 'ort' strategy.
1b6f03c76c85c7fc9898704cb306e1f5acfc91f7 5321b822662e92244c0717f314c963f9a8f02982 Sim Zhen Quan <<EMAIL>> 1747798770 +0800	commit: Reviewed
5321b822662e92244c0717f314c963f9a8f02982 397cd210416fc3aa207241c1d586208f09c0907a Sim Zhen Quan <<EMAIL>> 1747798782 +0800	checkout: moving from feature/enrollment-session-CRUD to enrollment
397cd210416fc3aa207241c1d586208f09c0907a 7068f7efbcc996503b57996f24b4260a06b10d6a Sim Zhen Quan <<EMAIL>> 1747798793 +0800	checkout: moving from enrollment to main
7068f7efbcc996503b57996f24b4260a06b10d6a 7068f7efbcc996503b57996f24b4260a06b10d6a Sim Zhen Quan <<EMAIL>> 1747798800 +0800	checkout: moving from main to enrollment-v2
7068f7efbcc996503b57996f24b4260a06b10d6a 5321b822662e92244c0717f314c963f9a8f02982 Sim Zhen Quan <<EMAIL>> 1747798814 +0800	checkout: moving from enrollment-v2 to feature/enrollment-session-CRUD
5321b822662e92244c0717f314c963f9a8f02982 ac0e2f184cf3cd7ee05d7d3d2474c21194f22c31 Sim Zhen Quan <<EMAIL>> 1747799074 +0800	checkout: moving from feature/enrollment-session-CRUD to JIRA-448-grading-framework-changes
ac0e2f184cf3cd7ee05d7d3d2474c21194f22c31 9e730eda9be1a933cf3abeb30e60c92b0448345c Sim Zhen Quan <<EMAIL>> 1747800000 +0800	pull: Fast-forward
9e730eda9be1a933cf3abeb30e60c92b0448345c a399992aaf361fb77915da4ef6b9aac03f037595 Sim Zhen Quan <<EMAIL>> 1747800801 +0800	commit: Tidy up
a399992aaf361fb77915da4ef6b9aac03f037595 ad58f6d6f37fc6574a21ce586a1a6a05131f1ade Sim Zhen Quan <<EMAIL>> 1747801244 +0800	checkout: moving from JIRA-448-grading-framework-changes to dev
ad58f6d6f37fc6574a21ce586a1a6a05131f1ade 76273652849bf61f787d139d14b50937f62c07f9 Sim Zhen Quan <<EMAIL>> 1747801244 +0800	merge JIRA-448-grading-framework-changes: Merge made by the 'ort' strategy.
76273652849bf61f787d139d14b50937f62c07f9 7c6eb1c3d78e72c052f611ebab19d943e49d8b86 Sim Zhen Quan <<EMAIL>> 1747806727 +0800	commit: Deployed to dev
7c6eb1c3d78e72c052f611ebab19d943e49d8b86 7068f7efbcc996503b57996f24b4260a06b10d6a Sim Zhen Quan <<EMAIL>> 1747807708 +0800	checkout: moving from dev to main
7068f7efbcc996503b57996f24b4260a06b10d6a 98cd6430fbec4914d05d8a4f4505416b22f74daa Sim Zhen Quan <<EMAIL>> 1747807715 +0800	pull: Fast-forward
98cd6430fbec4914d05d8a4f4505416b22f74daa d1a40b32e3085ed4cd2851b29ec6f3dc4a2dc3ab Sim Zhen Quan <<EMAIL>> 1747812911 +0800	checkout: moving from main to exam-module-changes
d1a40b32e3085ed4cd2851b29ec6f3dc4a2dc3ab 7dac28fc46b3ed716652711020d5b22c95554a63 Sim Zhen Quan <<EMAIL>> 1747812920 +0800	commit: Deployed to prd
7dac28fc46b3ed716652711020d5b22c95554a63 cdce7767e2fa72aefb8b334bed5a277cda2ef652 Sim Zhen Quan <<EMAIL>> 1747813596 +0800	checkout: moving from exam-module-changes to feature/enrollment-migration
cdce7767e2fa72aefb8b334bed5a277cda2ef652 68cdc6c0d5aa212ac959475ad4ced081a5273e15 Sim Zhen Quan <<EMAIL>> 1747813605 +0800	merge origin/enrollment-v2: Merge made by the 'ort' strategy.
68cdc6c0d5aa212ac959475ad4ced081a5273e15 7068f7efbcc996503b57996f24b4260a06b10d6a Sim Zhen Quan <<EMAIL>> 1747813615 +0800	checkout: moving from feature/enrollment-migration to enrollment-v2
7068f7efbcc996503b57996f24b4260a06b10d6a beb8f10d4f8fc6f8ef665b1d068997d4779b92ae Sim Zhen Quan <<EMAIL>> 1747813625 +0800	pull: Fast-forward
beb8f10d4f8fc6f8ef665b1d068997d4779b92ae 856b53f9cb69dc528e1b8661aa367b1e83c192d6 Sim Zhen Quan <<EMAIL>> 1747813625 +0800	merge origin/main: Merge made by the 'ort' strategy.
856b53f9cb69dc528e1b8661aa367b1e83c192d6 68cdc6c0d5aa212ac959475ad4ced081a5273e15 Sim Zhen Quan <<EMAIL>> 1747813642 +0800	checkout: moving from enrollment-v2 to feature/enrollment-migration
68cdc6c0d5aa212ac959475ad4ced081a5273e15 92b6bf3a135b0e4ffbb889eb961100cebe6c43c1 Sim Zhen Quan <<EMAIL>> ********** +0800	merge origin/enrollment-v2: Merge made by the 'ort' strategy.
92b6bf3a135b0e4ffbb889eb961100cebe6c43c1 02c0349fff9055dd3f3b8988e6833a644e1571d6 Sim Zhen Quan <<EMAIL>> ********** +0800	commit: Reviewed
02c0349fff9055dd3f3b8988e6833a644e1571d6 53fe959d80ddf8a794c53ab4bb1865308855aea5 Sim Zhen Quan <<EMAIL>> ********** +0800	checkout: moving from feature/enrollment-migration to report/hostel-report-saving-account
53fe959d80ddf8a794c53ab4bb1865308855aea5 c910ccfe063384f24762978184a5987d7853c42c Sim Zhen Quan <<EMAIL>> ********** +0800	merge origin/main: Merge made by the 'ort' strategy.
c910ccfe063384f24762978184a5987d7853c42c 6c29e9e774b831817dccbae5069b439ad583aba7 Sim Zhen Quan <<EMAIL>> ********** +0800	commit: Reviewed
6c29e9e774b831817dccbae5069b439ad583aba7 7c6eb1c3d78e72c052f611ebab19d943e49d8b86 Sim Zhen Quan <<EMAIL>> ********** +0800	checkout: moving from report/hostel-report-saving-account to dev
7c6eb1c3d78e72c052f611ebab19d943e49d8b86 bbc902b3da8200fd7e32297f9bbbdae9809d7cc7 Sim Zhen Quan <<EMAIL>> ********** +0800	merge origin/report/hostel-report-saving-account: Merge made by the 'ort' strategy.
bbc902b3da8200fd7e32297f9bbbdae9809d7cc7 ab25508b437c2462bfe4a2b4d8706eff90f1ea13 Sim Zhen Quan <<EMAIL>> ********** +0800	merge origin/enrollment-v2: Merge made by the 'ort' strategy.
ab25508b437c2462bfe4a2b4d8706eff90f1ea13 a399992aaf361fb77915da4ef6b9aac03f037595 Sim Zhen Quan <<EMAIL>> ********** +0800	checkout: moving from dev to JIRA-448-grading-framework-changes
a399992aaf361fb77915da4ef6b9aac03f037595 762afe4cb37a3abd70c3a945fbeb6a2f5c69ab0e Sim Zhen Quan <<EMAIL>> ********** +0800	pull: Fast-forward
762afe4cb37a3abd70c3a945fbeb6a2f5c69ab0e ab25508b437c2462bfe4a2b4d8706eff90f1ea13 Sim Zhen Quan <<EMAIL>> ********** +0800	checkout: moving from JIRA-448-grading-framework-changes to dev
ab25508b437c2462bfe4a2b4d8706eff90f1ea13 43b2d9eb3f8204fb65e1ab41b60e656ca3ff5215 Sim Zhen Quan <<EMAIL>> ********** +0800	merge origin/JIRA-448-grading-framework-changes: Merge made by the 'ort' strategy.
43b2d9eb3f8204fb65e1ab41b60e656ca3ff5215 43b2d9eb3f8204fb65e1ab41b60e656ca3ff5215 Sim Zhen Quan <<EMAIL>> 1747878516 +0800	reset: moving to HEAD
43b2d9eb3f8204fb65e1ab41b60e656ca3ff5215 98cd6430fbec4914d05d8a4f4505416b22f74daa Sim Zhen Quan <<EMAIL>> 1747878518 +0800	checkout: moving from dev to main
98cd6430fbec4914d05d8a4f4505416b22f74daa 43b2d9eb3f8204fb65e1ab41b60e656ca3ff5215 Sim Zhen Quan <<EMAIL>> 1747882212 +0800	checkout: moving from main to dev
43b2d9eb3f8204fb65e1ab41b60e656ca3ff5215 43b2d9eb3f8204fb65e1ab41b60e656ca3ff5215 Sim Zhen Quan <<EMAIL>> 1747897786 +0800	reset: moving to HEAD
43b2d9eb3f8204fb65e1ab41b60e656ca3ff5215 98cd6430fbec4914d05d8a4f4505416b22f74daa Sim Zhen Quan <<EMAIL>> 1747897788 +0800	checkout: moving from dev to main
98cd6430fbec4914d05d8a4f4505416b22f74daa 7dac28fc46b3ed716652711020d5b22c95554a63 Sim Zhen Quan <<EMAIL>> 1747897886 +0800	checkout: moving from main to exam-module-changes
7dac28fc46b3ed716652711020d5b22c95554a63 b432ed0353f9f28b8969eba6efc59d745e7fc583 Sim Zhen Quan <<EMAIL>> 1747897924 +0800	reset: moving to origin/exam-module-changes
b432ed0353f9f28b8969eba6efc59d745e7fc583 2d66131e3f9e2e5bd1ea8926e825145b71188228 Sim Zhen Quan <<EMAIL>> 1747897962 +0800	commit: Update README.md
2d66131e3f9e2e5bd1ea8926e825145b71188228 98cd6430fbec4914d05d8a4f4505416b22f74daa Sim Zhen Quan <<EMAIL>> 1747897989 +0800	checkout: moving from exam-module-changes to main
98cd6430fbec4914d05d8a4f4505416b22f74daa 7b569b520e54c0071477014207bdcb7143911d2d Sim Zhen Quan <<EMAIL>> 1747897995 +0800	pull: Fast-forward
7b569b520e54c0071477014207bdcb7143911d2d d10e3a40a8381e0c6ab11103b55ab5f6c660e1a4 Sim Zhen Quan <<EMAIL>> 1747898889 +0800	commit: Fix kernel issue
d10e3a40a8381e0c6ab11103b55ab5f6c660e1a4 4e7d050d38c5fa1e4f0f01f2152220ac5710d1e2 Sim Zhen Quan <<EMAIL>> 1747900517 +0800	checkout: moving from main to feature/enrollment-import-template
4e7d050d38c5fa1e4f0f01f2152220ac5710d1e2 856b53f9cb69dc528e1b8661aa367b1e83c192d6 Sim Zhen Quan <<EMAIL>> 1747900529 +0800	checkout: moving from feature/enrollment-import-template to enrollment-v2
856b53f9cb69dc528e1b8661aa367b1e83c192d6 dc71c69291e10cc9a82508eb5d0051aa5a6b2b21 Sim Zhen Quan <<EMAIL>> 1747900534 +0800	pull: Fast-forward
dc71c69291e10cc9a82508eb5d0051aa5a6b2b21 8e3a834e1638dc286a3df46d786c8279ac8ff807 Sim Zhen Quan <<EMAIL>> 1747900534 +0800	merge origin/main: Merge made by the 'ort' strategy.
8e3a834e1638dc286a3df46d786c8279ac8ff807 4e7d050d38c5fa1e4f0f01f2152220ac5710d1e2 Sim Zhen Quan <<EMAIL>> 1747900546 +0800	checkout: moving from enrollment-v2 to feature/enrollment-import-template
4e7d050d38c5fa1e4f0f01f2152220ac5710d1e2 d78398df81f6c2d9ea42c8de2840dfb6d55b0093 Sim Zhen Quan <<EMAIL>> 1747900551 +0800	merge enrollment-v2: Merge made by the 'ort' strategy.
d78398df81f6c2d9ea42c8de2840dfb6d55b0093 3cdddcc251f82d9a252de0fc4686309d900bbd1b Sim Zhen Quan <<EMAIL>> 1747908940 +0800	commit: Reviewed
3cdddcc251f82d9a252de0fc4686309d900bbd1b d10e3a40a8381e0c6ab11103b55ab5f6c660e1a4 Sim Zhen Quan <<EMAIL>> 1747933573 +0800	checkout: moving from feature/enrollment-import-template to main
d10e3a40a8381e0c6ab11103b55ab5f6c660e1a4 11a18edb615809acc364116d77b5c404614b21dc Sim Zhen Quan <<EMAIL>> 1747933846 +0800	commit: Remove duplicated scheduler
11a18edb615809acc364116d77b5c404614b21dc 2d66131e3f9e2e5bd1ea8926e825145b71188228 Sim Zhen Quan <<EMAIL>> 1747966167 +0800	checkout: moving from main to exam-module-changes
2d66131e3f9e2e5bd1ea8926e825145b71188228 762afe4cb37a3abd70c3a945fbeb6a2f5c69ab0e Sim Zhen Quan <<EMAIL>> 1747969899 +0800	checkout: moving from exam-module-changes to JIRA-448-grading-framework-changes
762afe4cb37a3abd70c3a945fbeb6a2f5c69ab0e 11a18edb615809acc364116d77b5c404614b21dc Sim Zhen Quan <<EMAIL>> 1747969902 +0800	checkout: moving from JIRA-448-grading-framework-changes to main
11a18edb615809acc364116d77b5c404614b21dc 9873f23d406d7770d65aec65c428b7a1020aa6d4 Sim Zhen Quan <<EMAIL>> 1747969911 +0800	commit: Deployed to PRD
9873f23d406d7770d65aec65c428b7a1020aa6d4 762afe4cb37a3abd70c3a945fbeb6a2f5c69ab0e Sim Zhen Quan <<EMAIL>> 1747969921 +0800	checkout: moving from main to JIRA-448-grading-framework-changes
762afe4cb37a3abd70c3a945fbeb6a2f5c69ab0e 109e98ed3651978fa64d795670ca3783c0246a6d Sim Zhen Quan <<EMAIL>> 1747969930 +0800	pull: Fast-forward
109e98ed3651978fa64d795670ca3783c0246a6d 9873f23d406d7770d65aec65c428b7a1020aa6d4 Sim Zhen Quan <<EMAIL>> 1747970678 +0800	checkout: moving from JIRA-448-grading-framework-changes to main
9873f23d406d7770d65aec65c428b7a1020aa6d4 109e98ed3651978fa64d795670ca3783c0246a6d Sim Zhen Quan <<EMAIL>> 1747971702 +0800	checkout: moving from main to JIRA-448-grading-framework-changes
109e98ed3651978fa64d795670ca3783c0246a6d 635f6c7c5588db7ec12d4bd408dc974b9cdfff12 Sim Zhen Quan <<EMAIL>> 1747972062 +0800	commit: Reviewed
635f6c7c5588db7ec12d4bd408dc974b9cdfff12 ffcd384f5b2cadbb68564bf59f1471411e403a95 Sim Zhen Quan <<EMAIL>> 1747972086 +0800	commit: Updated readme
ffcd384f5b2cadbb68564bf59f1471411e403a95 43b2d9eb3f8204fb65e1ab41b60e656ca3ff5215 Sim Zhen Quan <<EMAIL>> 1747972115 +0800	checkout: moving from JIRA-448-grading-framework-changes to dev
43b2d9eb3f8204fb65e1ab41b60e656ca3ff5215 43b2d9eb3f8204fb65e1ab41b60e656ca3ff5215 Sim Zhen Quan <<EMAIL>> 1747972125 +0800	checkout: moving from dev to dev
43b2d9eb3f8204fb65e1ab41b60e656ca3ff5215 a97c9465f7413260e12863a2bc549ebe71c80cd2 Sim Zhen Quan <<EMAIL>> 1747972129 +0800	merge JIRA-448-grading-framework-changes: Merge made by the 'ort' strategy.
a97c9465f7413260e12863a2bc549ebe71c80cd2 40b6d4230ccd35b4b1f6a7471f62556461ee12e1 Sim Zhen Quan <<EMAIL>> 1747988767 +0800	commit: Deployed to dev
40b6d4230ccd35b4b1f6a7471f62556461ee12e1 497b9f71101802d93c83b776b907f440e88b2dc4 Sim Zhen Quan <<EMAIL>> 1747988777 +0800	checkout: moving from dev to feature/enrollment-import-and-save-APIs
497b9f71101802d93c83b776b907f440e88b2dc4 20d2115fca2342a079ee866c1484bca8075cf117 Sim Zhen Quan <<EMAIL>> 1747991332 +0800	commit: Reviewed
20d2115fca2342a079ee866c1484bca8075cf117 40b6d4230ccd35b4b1f6a7471f62556461ee12e1 Sim Zhen Quan <<EMAIL>> 1747991353 +0800	checkout: moving from feature/enrollment-import-and-save-APIs to dev
40b6d4230ccd35b4b1f6a7471f62556461ee12e1 d6b4ef3c656da2c9d8b6807e47caad2ed3c058d6 Sim Zhen Quan <<EMAIL>> 1747991359 +0800	merge feature/enrollment-import-and-save-APIs: Merge made by the 'ort' strategy.
d6b4ef3c656da2c9d8b6807e47caad2ed3c058d6 b452964bfedca7f7ee2cc84cb4e64fc1f36533df Sim Zhen Quan <<EMAIL>> 1747991956 +0800	checkout: moving from dev to feature/enrollment-save-imported-excel-API
b452964bfedca7f7ee2cc84cb4e64fc1f36533df 4c4acc7ab548d5dab3c052006e028289847a1062 Sim Zhen Quan <<EMAIL>> 1748109474 +0800	checkout: moving from feature/enrollment-save-imported-excel-API to enrollment-user
4c4acc7ab548d5dab3c052006e028289847a1062 a38524901503f2d985120ada1b7e06ab73ebd682 Sim Zhen Quan <<EMAIL>> 1748109485 +0800	merge origin/enrollment-v2: Merge made by the 'ort' strategy.
a38524901503f2d985120ada1b7e06ab73ebd682 cfe02e2c7c8193b303e8be3497a041674b3a09ee Sim Zhen Quan <<EMAIL>> 1748109896 +0800	commit: Revert changes
cfe02e2c7c8193b303e8be3497a041674b3a09ee 9873f23d406d7770d65aec65c428b7a1020aa6d4 Sim Zhen Quan <<EMAIL>> 1748110082 +0800	checkout: moving from enrollment-user to main
9873f23d406d7770d65aec65c428b7a1020aa6d4 7ec7fef357689ad9216b97a6e88aff2a48de217d Sim Zhen Quan <<EMAIL>> 1748110090 +0800	pull: Fast-forward
7ec7fef357689ad9216b97a6e88aff2a48de217d 35c5b053bb9bba723e533bae27e61ba3fc61862c Sim Zhen Quan <<EMAIL>> 1748183784 +0800	commit: Deployed to prd
35c5b053bb9bba723e533bae27e61ba3fc61862c cfe02e2c7c8193b303e8be3497a041674b3a09ee Sim Zhen Quan <<EMAIL>> 1748183794 +0800	checkout: moving from main to enrollment-user
cfe02e2c7c8193b303e8be3497a041674b3a09ee ba94b521db7ebbc03ec0fd5e8ec849eae1f5b100 Sim Zhen Quan <<EMAIL>> 1748188131 +0800	commit: Reviewed
ba94b521db7ebbc03ec0fd5e8ec849eae1f5b100 b452964bfedca7f7ee2cc84cb4e64fc1f36533df Sim Zhen Quan <<EMAIL>> 1748188235 +0800	checkout: moving from enrollment-user to feature/enrollment-save-imported-excel-API
b452964bfedca7f7ee2cc84cb4e64fc1f36533df 395266aea364f7dfad6919859ac8f51861e17955 Sim Zhen Quan <<EMAIL>> 1748188240 +0800	pull: Fast-forward
395266aea364f7dfad6919859ac8f51861e17955 20d2115fca2342a079ee866c1484bca8075cf117 Sim Zhen Quan <<EMAIL>> 1748188257 +0800	checkout: moving from feature/enrollment-save-imported-excel-API to feature/enrollment-import-and-save-APIs
20d2115fca2342a079ee866c1484bca8075cf117 b58c610ffe55b1ea23f95dc9ea687f992216d260 Sim Zhen Quan <<EMAIL>> 1748188264 +0800	merge origin/enrollment-user: Merge made by the 'ort' strategy.
b58c610ffe55b1ea23f95dc9ea687f992216d260 395266aea364f7dfad6919859ac8f51861e17955 Sim Zhen Quan <<EMAIL>> 1748188278 +0800	checkout: moving from feature/enrollment-import-and-save-APIs to feature/enrollment-save-imported-excel-API
395266aea364f7dfad6919859ac8f51861e17955 711c133b634480c25da7440571cd64c5beb636bd Sim Zhen Quan <<EMAIL>> 1748188298 +0800	merge feature/enrollment-import-and-save-APIs: Merge made by the 'ort' strategy.
711c133b634480c25da7440571cd64c5beb636bd 18798f5709324c48024dccc123016ef6961097c6 Sim Zhen Quan <<EMAIL>> 1748192152 +0800	commit: Review WIP
18798f5709324c48024dccc123016ef6961097c6 d6b4ef3c656da2c9d8b6807e47caad2ed3c058d6 Sim Zhen Quan <<EMAIL>> 1748192168 +0800	checkout: moving from feature/enrollment-save-imported-excel-API to dev
d6b4ef3c656da2c9d8b6807e47caad2ed3c058d6 0e8ca983ff28eb1485adbcefc8c8855815962ac7 Sim Zhen Quan <<EMAIL>> 1748192172 +0800	merge feature/enrollment-save-imported-excel-API: Merge made by the 'ort' strategy.
0e8ca983ff28eb1485adbcefc8c8855815962ac7 48d7376f79053e60baedc78824ea9a0b8f056ba6 Sim Zhen Quan <<EMAIL>> 1748192492 +0800	commit: Deployed to dev
48d7376f79053e60baedc78824ea9a0b8f056ba6 35c5b053bb9bba723e533bae27e61ba3fc61862c Sim Zhen Quan <<EMAIL>> 1748192576 +0800	checkout: moving from dev to main
35c5b053bb9bba723e533bae27e61ba3fc61862c 143e26bd9e386b5fb5bd056d199f57202890c488 Sim Zhen Quan <<EMAIL>> 1748193680 +0800	commit: Added patch for reward punishment record date of sign
143e26bd9e386b5fb5bd056d199f57202890c488 48d7376f79053e60baedc78824ea9a0b8f056ba6 Sim Zhen Quan <<EMAIL>> 1748193692 +0800	checkout: moving from main to dev
48d7376f79053e60baedc78824ea9a0b8f056ba6 d265574c0a6b2d187517830e9899ed79ff0d2cc8 Sim Zhen Quan <<EMAIL>> 1748193742 +0800	commit (merge): Merge remote-tracking branch 'origin/main' into dev
d265574c0a6b2d187517830e9899ed79ff0d2cc8 e0445ac34ff872bcbd9ab73f16c09af22ceb54bd Sim Zhen Quan <<EMAIL>> 1748193960 +0800	checkout: moving from dev to conduct-report
e0445ac34ff872bcbd9ab73f16c09af22ceb54bd da34cb51b2d7bb96717cb7c9e7435dbec81c66d8 Sim Zhen Quan <<EMAIL>> 1748194029 +0800	merge origin/JIRA-448-grading-framework-changes: Merge made by the 'ort' strategy.
da34cb51b2d7bb96717cb7c9e7435dbec81c66d8 da34cb51b2d7bb96717cb7c9e7435dbec81c66d8 Sim Zhen Quan <<EMAIL>> 1748225704 +0800	reset: moving to HEAD
da34cb51b2d7bb96717cb7c9e7435dbec81c66d8 4c4acc7ab548d5dab3c052006e028289847a1062 Sim Zhen Quan <<EMAIL>> 1748225714 +0800	checkout: moving from conduct-report to 4c4acc7
4c4acc7ab548d5dab3c052006e028289847a1062 da34cb51b2d7bb96717cb7c9e7435dbec81c66d8 Sim Zhen Quan <<EMAIL>> 1748226117 +0800	checkout: moving from 4c4acc7ab548d5dab3c052006e028289847a1062 to conduct-report
da34cb51b2d7bb96717cb7c9e7435dbec81c66d8 83753fa213af0a915a870316d41413396e557ed8 Sim Zhen Quan <<EMAIL>> 1748232092 +0800	commit: Review WIP
83753fa213af0a915a870316d41413396e557ed8 ffcd384f5b2cadbb68564bf59f1471411e403a95 Sim Zhen Quan <<EMAIL>> 1748232106 +0800	checkout: moving from conduct-report to JIRA-448-grading-framework-changes
ffcd384f5b2cadbb68564bf59f1471411e403a95 e870ac3849ada8755376e0c53d032a7dbddc1199 Sim Zhen Quan <<EMAIL>> 1748235438 +0800	pull: Fast-forward
e870ac3849ada8755376e0c53d032a7dbddc1199 f326b208d630f302d9fc80da07a441eef9e62402 Sim Zhen Quan <<EMAIL>> 1748235471 +0800	commit: Exam posting check enhancement
f326b208d630f302d9fc80da07a441eef9e62402 5010d39494c4789df8d50ceaf667026cd7987a41 Sim Zhen Quan <<EMAIL>> 1748235498 +0800	merge exam-module-changes: Merge made by the 'ort' strategy.
5010d39494c4789df8d50ceaf667026cd7987a41 5703e9cb3a48aa0f57c839d1ce7fdebb4642464c Sim Zhen Quan <<EMAIL>> 1748236778 +0800	commit: Reviewed
5703e9cb3a48aa0f57c839d1ce7fdebb4642464c d265574c0a6b2d187517830e9899ed79ff0d2cc8 Sim Zhen Quan <<EMAIL>> 1748236961 +0800	checkout: moving from JIRA-448-grading-framework-changes to dev
d265574c0a6b2d187517830e9899ed79ff0d2cc8 a99916e0208b837a6de64b7fea83e7b98ed68448 Sim Zhen Quan <<EMAIL>> 1748236971 +0800	merge JIRA-448-grading-framework-changes: Merge made by the 'ort' strategy.
a99916e0208b837a6de64b7fea83e7b98ed68448 5703e9cb3a48aa0f57c839d1ce7fdebb4642464c Sim Zhen Quan <<EMAIL>> 1748244280 +0800	checkout: moving from dev to JIRA-448-grading-framework-changes
5703e9cb3a48aa0f57c839d1ce7fdebb4642464c b69166b9727b67854f4196c1c193f48bd5e05d8e Sim Zhen Quan <<EMAIL>> 1748244290 +0800	commit: Reviewed
b69166b9727b67854f4196c1c193f48bd5e05d8e 9299376d944ae9634734a7809ed736739ed63e59 Sim Zhen Quan <<EMAIL>> 1748246914 +0800	commit: Bug fix
9299376d944ae9634734a7809ed736739ed63e59 2d66131e3f9e2e5bd1ea8926e825145b71188228 Sim Zhen Quan <<EMAIL>> 1748248023 +0800	checkout: moving from JIRA-448-grading-framework-changes to exam-module-changes
2d66131e3f9e2e5bd1ea8926e825145b71188228 757b1e1cc1a68dc3285a82f0231ed1c1751d6d8f Sim Zhen Quan <<EMAIL>> 1748248030 +0800	pull: Fast-forward
757b1e1cc1a68dc3285a82f0231ed1c1751d6d8f 757b1e1cc1a68dc3285a82f0231ed1c1751d6d8f Sim Zhen Quan <<EMAIL>> 1748248032 +0800	checkout: moving from exam-module-changes to patch-elective-courses
757b1e1cc1a68dc3285a82f0231ed1c1751d6d8f 83753fa213af0a915a870316d41413396e557ed8 Sim Zhen Quan <<EMAIL>> 1748250289 +0800	checkout: moving from patch-elective-courses to conduct-report
83753fa213af0a915a870316d41413396e557ed8 4606f2f5c4bd96356131e7751d611599067fad50 Sim Zhen Quan <<EMAIL>> 1748250300 +0800	merge origin/exam-module-changes: Merge made by the 'ort' strategy.
4606f2f5c4bd96356131e7751d611599067fad50 4606f2f5c4bd96356131e7751d611599067fad50 Sim Zhen Quan <<EMAIL>> 1748250474 +0800	checkout: moving from conduct-report to conduct-report
4606f2f5c4bd96356131e7751d611599067fad50 710b0a5aed67fa9b81b067e02912ab35e453ef8b Sim Zhen Quan <<EMAIL>> 1748254295 +0800	commit: Review WIP
710b0a5aed67fa9b81b067e02912ab35e453ef8b a99916e0208b837a6de64b7fea83e7b98ed68448 Sim Zhen Quan <<EMAIL>> 1748254351 +0800	checkout: moving from conduct-report to dev
a99916e0208b837a6de64b7fea83e7b98ed68448 fe40d42f014e709fd09b77de15fd85eb348e310c Sim Zhen Quan <<EMAIL>> 1748254402 +0800	commit (merge): Merge branch 'conduct-report' into dev
fe40d42f014e709fd09b77de15fd85eb348e310c 6c30098d4b2d78eece31ec6725ef5fd0387f90ce Sim Zhen Quan <<EMAIL>> 1748258592 +0800	commit: Deployed to DEV
6c30098d4b2d78eece31ec6725ef5fd0387f90ce 757b1e1cc1a68dc3285a82f0231ed1c1751d6d8f Sim Zhen Quan <<EMAIL>> 1748258599 +0800	checkout: moving from dev to exam-module-changes
757b1e1cc1a68dc3285a82f0231ed1c1751d6d8f 569d4d903196b4ccfde995446792d9eb087dde63 Sim Zhen Quan <<EMAIL>> 1748269258 +0800	commit: Added patch for elective subjects
569d4d903196b4ccfde995446792d9eb087dde63 4a2d125e5a8326ea922c698da1ab643c2712f405 Sim Zhen Quan <<EMAIL>> 1748269273 +0800	merge origin/exam-module-changes: Merge made by the 'ort' strategy.
4a2d125e5a8326ea922c698da1ab643c2712f405 6c30098d4b2d78eece31ec6725ef5fd0387f90ce Sim Zhen Quan <<EMAIL>> 1748269284 +0800	checkout: moving from exam-module-changes to dev
6c30098d4b2d78eece31ec6725ef5fd0387f90ce 23a639ce366b3da32de0fb6513fba0e306a28dbc Sim Zhen Quan <<EMAIL>> 1748269296 +0800	merge origin/exam-module-changes: Merge made by the 'ort' strategy.
23a639ce366b3da32de0fb6513fba0e306a28dbc 23a639ce366b3da32de0fb6513fba0e306a28dbc Sim Zhen Quan <<EMAIL>> 1748272361 +0800	reset: moving to HEAD
23a639ce366b3da32de0fb6513fba0e306a28dbc 4a2d125e5a8326ea922c698da1ab643c2712f405 Sim Zhen Quan <<EMAIL>> 1748272362 +0800	checkout: moving from dev to exam-module-changes
4a2d125e5a8326ea922c698da1ab643c2712f405 fc45cfb094b051303ec13559c80c4ad6237985e1 Sim Zhen Quan <<EMAIL>> 1748272402 +0800	commit: Update permissions
fc45cfb094b051303ec13559c80c4ad6237985e1 a78234d3c3b3823bf0680650769dd000393b63e6 Sim Zhen Quan <<EMAIL>> 1748274298 +0800	commit: Added numprocs
a78234d3c3b3823bf0680650769dd000393b63e6 710b0a5aed67fa9b81b067e02912ab35e453ef8b Sim Zhen Quan <<EMAIL>> 1748274307 +0800	checkout: moving from exam-module-changes to conduct-report
710b0a5aed67fa9b81b067e02912ab35e453ef8b b9605fc46a393ddd9034f078565d49245c7df5a3 Sim Zhen Quan <<EMAIL>> 1748274474 +0800	commit: Added patch exam semester settings
b9605fc46a393ddd9034f078565d49245c7df5a3 ac13a133fce88880be3170d50147de6415818ba5 Sim Zhen Quan <<EMAIL>> 1748274493 +0800	merge exam-module-changes: Merge made by the 'ort' strategy.
ac13a133fce88880be3170d50147de6415818ba5 15e6324b641b5a13128dde28be393e77b0bde43d Sim Zhen Quan <<EMAIL>> 1748274832 +0800	pull: Fast-forward
15e6324b641b5a13128dde28be393e77b0bde43d c5fd549a32e2710347ab473883d66461c629d7d5 Sim Zhen Quan <<EMAIL>> 1748274854 +0800	commit: Updated README.md
c5fd549a32e2710347ab473883d66461c629d7d5 23a639ce366b3da32de0fb6513fba0e306a28dbc Sim Zhen Quan <<EMAIL>> 1748274876 +0800	checkout: moving from conduct-report to dev
23a639ce366b3da32de0fb6513fba0e306a28dbc 3414571d9b5146cb346b556ed6b96784e2c906db Sim Zhen Quan <<EMAIL>> 1748274881 +0800	merge conduct-report: Merge made by the 'ort' strategy.
3414571d9b5146cb346b556ed6b96784e2c906db a78234d3c3b3823bf0680650769dd000393b63e6 Sim Zhen Quan <<EMAIL>> 1748276171 +0800	checkout: moving from dev to exam-module-changes
a78234d3c3b3823bf0680650769dd000393b63e6 7048764b7701f7dd9578a501bd0be3fcd9b853d9 Sim Zhen Quan <<EMAIL>> 1748276177 +0800	pull: Fast-forward
7048764b7701f7dd9578a501bd0be3fcd9b853d9 f197c309692b190d38b46f95759ed1f5be27342f Sim Zhen Quan <<EMAIL>> 1748276239 +0800	commit (merge): Merge remote-tracking branch 'origin/main' into exam-module-changes
f197c309692b190d38b46f95759ed1f5be27342f 51148f092a51484dc2ff11515a91bab8a7e7917d Sim Zhen Quan <<EMAIL>> 1748276284 +0800	checkout: moving from exam-module-changes to fix-wallet-trx-ref-no-unique
51148f092a51484dc2ff11515a91bab8a7e7917d 2c5c3c0f52109277f21c495114d3d3fb8b630c4b Sim Zhen Quan <<EMAIL>> 1748276291 +0800	merge origin/main: Merge made by the 'ort' strategy.
2c5c3c0f52109277f21c495114d3d3fb8b630c4b 143e26bd9e386b5fb5bd056d199f57202890c488 Sim Zhen Quan <<EMAIL>> 1748276548 +0800	checkout: moving from fix-wallet-trx-ref-no-unique to main
143e26bd9e386b5fb5bd056d199f57202890c488 7cc66fe79d7ff0bcbf330a1680a22efa420c445c Sim Zhen Quan <<EMAIL>> 1748276558 +0800	pull: Fast-forward
7cc66fe79d7ff0bcbf330a1680a22efa420c445c 73c5bea6b2b0fe443c4bd54750c31b8adf5ef1fb Sim Zhen Quan <<EMAIL>> 1748279942 +0800	commit: Deployed to prd
73c5bea6b2b0fe443c4bd54750c31b8adf5ef1fb b58c610ffe55b1ea23f95dc9ea687f992216d260 Sim Zhen Quan <<EMAIL>> 1748310103 +0800	checkout: moving from main to feature/enrollment-import-and-save-APIs
b58c610ffe55b1ea23f95dc9ea687f992216d260 18798f5709324c48024dccc123016ef6961097c6 Sim Zhen Quan <<EMAIL>> 1748310137 +0800	checkout: moving from feature/enrollment-import-and-save-APIs to feature/enrollment-save-imported-excel-API
18798f5709324c48024dccc123016ef6961097c6 874e81726ef1c5968f9973614072aa7c0a7980ff Sim Zhen Quan <<EMAIL>> 1748310148 +0800	pull: Fast-forward
874e81726ef1c5968f9973614072aa7c0a7980ff 874e81726ef1c5968f9973614072aa7c0a7980ff Sim Zhen Quan <<EMAIL>> 1748310150 +0800	checkout: moving from feature/enrollment-save-imported-excel-API to feature/enrollment-save-imported-excel-API
874e81726ef1c5968f9973614072aa7c0a7980ff 31107403881f89ede7aaa28657a0790a6631ac71 Sim Zhen Quan <<EMAIL>> 1748312311 +0800	commit: Reviewed
31107403881f89ede7aaa28657a0790a6631ac71 ba94b521db7ebbc03ec0fd5e8ec849eae1f5b100 Sim Zhen Quan <<EMAIL>> 1748312379 +0800	checkout: moving from feature/enrollment-save-imported-excel-API to enrollment-user
ba94b521db7ebbc03ec0fd5e8ec849eae1f5b100 df75ebc8fcde4e5e8cba98c7cbae1c15231a8516 Sim Zhen Quan <<EMAIL>> 1748312385 +0800	pull: Fast-forward
df75ebc8fcde4e5e8cba98c7cbae1c15231a8516 c4f9ea754ab63ef141b87778044318a535618701 Sim Zhen Quan <<EMAIL>> 1748313019 +0800	checkout: moving from enrollment-user to feature/enrollment-setting-fee
c4f9ea754ab63ef141b87778044318a535618701 b9582e82310cd10765ee1438cc64089e4da4081d Sim Zhen Quan <<EMAIL>> 1748313037 +0800	merge origin/enrollment-v2: Merge made by the 'ort' strategy.
b9582e82310cd10765ee1438cc64089e4da4081d 3630491947bc7dcee006e555c8af6825da2a97af Sim Zhen Quan <<EMAIL>> 1748317035 +0800	commit: Reviewed
3630491947bc7dcee006e555c8af6825da2a97af 3414571d9b5146cb346b556ed6b96784e2c906db Sim Zhen Quan <<EMAIL>> 1748317086 +0800	checkout: moving from feature/enrollment-setting-fee to dev
3414571d9b5146cb346b556ed6b96784e2c906db c42f3f28df9926354cc606e70ebdb872a717bd22 Sim Zhen Quan <<EMAIL>> 1748317093 +0800	merge origin/enrollment-v2: Merge made by the 'ort' strategy.
c42f3f28df9926354cc606e70ebdb872a717bd22 d1fbb7c649716e05ffdf8d8afbe35eec5b3288ae Sim Zhen Quan <<EMAIL>> 1748324439 +0800	commit: Deployed to dev
d1fbb7c649716e05ffdf8d8afbe35eec5b3288ae 73c5bea6b2b0fe443c4bd54750c31b8adf5ef1fb Sim Zhen Quan <<EMAIL>> 1748324465 +0800	checkout: moving from dev to main
73c5bea6b2b0fe443c4bd54750c31b8adf5ef1fb 645ec3379f9b4756f514445330ed938f25b04fa2 Sim Zhen Quan <<EMAIL>> 1748327702 +0800	commit: Bug fix
645ec3379f9b4756f514445330ed938f25b04fa2 ba06a6f4ee9f9a134b1bddbb6fcd8028b7143a03 Sim Zhen Quan <<EMAIL>> 1748327797 +0800	checkout: moving from main to feature/function-to-determine-fees
ba06a6f4ee9f9a134b1bddbb6fcd8028b7143a03 ee70b18226006ca7e945dab9fea1174bffa58512 Sim Zhen Quan <<EMAIL>> 1748327952 +0800	commit (merge): Merge remote-tracking branch 'origin/enrollment-v2' into feature/function-to-determine-fees
ee70b18226006ca7e945dab9fea1174bffa58512 645ec3379f9b4756f514445330ed938f25b04fa2 Sim Zhen Quan <<EMAIL>> 1748330457 +0800	checkout: moving from feature/function-to-determine-fees to main
645ec3379f9b4756f514445330ed938f25b04fa2 8e3a834e1638dc286a3df46d786c8279ac8ff807 Sim Zhen Quan <<EMAIL>> 1748330488 +0800	checkout: moving from main to enrollment-v2
8e3a834e1638dc286a3df46d786c8279ac8ff807 79144436a7dc52ded6e85cf936043147dad955fb Sim Zhen Quan <<EMAIL>> 1748330494 +0800	pull: Fast-forward
79144436a7dc52ded6e85cf936043147dad955fb ab78d2678b1ff30ca8eb08bfb35fbfe2ec277578 Sim Zhen Quan <<EMAIL>> 1748330495 +0800	merge origin/main: Merge made by the 'ort' strategy.
ab78d2678b1ff30ca8eb08bfb35fbfe2ec277578 ee70b18226006ca7e945dab9fea1174bffa58512 Sim Zhen Quan <<EMAIL>> 1748330518 +0800	checkout: moving from enrollment-v2 to feature/function-to-determine-fees
ee70b18226006ca7e945dab9fea1174bffa58512 160c54b94011b2faf886cf6ef5f9c6c08ec6921b Sim Zhen Quan <<EMAIL>> 1748330528 +0800	merge origin/enrollment-v2: Merge made by the 'ort' strategy.
160c54b94011b2faf886cf6ef5f9c6c08ec6921b 03b6b3cc4fe8506c59ee20b1746fdfd5e4fbe205 Sim Zhen Quan <<EMAIL>> 1748332011 +0800	commit: Reviewed
03b6b3cc4fe8506c59ee20b1746fdfd5e4fbe205 d1fbb7c649716e05ffdf8d8afbe35eec5b3288ae Sim Zhen Quan <<EMAIL>> 1748332454 +0800	checkout: moving from feature/function-to-determine-fees to dev
d1fbb7c649716e05ffdf8d8afbe35eec5b3288ae 0640fb965085ab6950978bf3b0ec76ea365b67ab Sim Zhen Quan <<EMAIL>> 1748332633 +0800	checkout: moving from dev to fix/student-mark-deduction-report
0640fb965085ab6950978bf3b0ec76ea365b67ab 0f4732a4b61b8d8ef01b740230597bc17376d44d Sim Zhen Quan <<EMAIL>> 1748332780 +0800	commit (merge): Merge remote-tracking branch 'origin/main' into fix/student-mark-deduction-report
0f4732a4b61b8d8ef01b740230597bc17376d44d e423ba0a3dbd95c421652bd075124b4c49bcb3d3 Sim Zhen Quan <<EMAIL>> ********** +0800	commit: Bug fix
e423ba0a3dbd95c421652bd075124b4c49bcb3d3 d1fbb7c649716e05ffdf8d8afbe35eec5b3288ae Sim Zhen Quan <<EMAIL>> ********** +0800	checkout: moving from fix/student-mark-deduction-report to dev
d1fbb7c649716e05ffdf8d8afbe35eec5b3288ae 6c29e9e774b831817dccbae5069b439ad583aba7 Sim Zhen Quan <<EMAIL>> ********** +0800	checkout: moving from dev to report/hostel-report-saving-account
6c29e9e774b831817dccbae5069b439ad583aba7 b2588d8558b0b48ee0d9435b9da0d0d0dc984e1e Sim Zhen Quan <<EMAIL>> ********** +0800	commit: Bug fix
b2588d8558b0b48ee0d9435b9da0d0d0dc984e1e f5cf00ece1af5e4599205418ee4e1d454213036b Sim Zhen Quan <<EMAIL>> ********** +0800	checkout: moving from report/hostel-report-saving-account to feature/enrollment-make-payment
f5cf00ece1af5e4599205418ee4e1d454213036b f5a470366e46123e9c547e0aefedf065f218d762 Sim Zhen Quan <<EMAIL>> ********** +0800	commit (merge): Merge remote-tracking branch 'origin/enrollment-v2' into feature/enrollment-make-payment
f5a470366e46123e9c547e0aefedf065f218d762 0e83fa3c59b505d2b514ef0625083c41e0f14fa7 Sim Zhen Quan <<EMAIL>> ********** +0800	commit: Review WIP
0e83fa3c59b505d2b514ef0625083c41e0f14fa7 645ec3379f9b4756f514445330ed938f25b04fa2 Sim Zhen Quan <<EMAIL>> ********** +0800	checkout: moving from feature/enrollment-make-payment to main
645ec3379f9b4756f514445330ed938f25b04fa2 281e528dd1026b9906ecdd280cb1b7deb8611671 Sim Zhen Quan <<EMAIL>> ********** +0800	pull: Fast-forward
281e528dd1026b9906ecdd280cb1b7deb8611671 2e077008480ece690a1aa18259a90709d3b9f47d Sim Zhen Quan <<EMAIL>> ********** +0800	commit: Test case fix
2e077008480ece690a1aa18259a90709d3b9f47d dda13ce2f01c1c42db4eb12942a19ee34c55ed47 Sim Zhen Quan <<EMAIL>> *********0 +0800	commit: Deployed to prd
dda13ce2f01c1c42db4eb12942a19ee34c55ed47 0e83fa3c59b505d2b514ef0625083c41e0f14fa7 Sim Zhen Quan <<EMAIL>> 1748399328 +0800	checkout: moving from main to feature/enrollment-make-payment
0e83fa3c59b505d2b514ef0625083c41e0f14fa7 ccab57ccf2dfdd2dc1151f0617affb175355341b Sim Zhen Quan <<EMAIL>> 1748402181 +0800	commit: Review WIP
ccab57ccf2dfdd2dc1151f0617affb175355341b a4741765aa8ef98672e7ce03fca5abc5ca6d8448 Sim Zhen Quan <<EMAIL>> 1748402189 +0800	checkout: moving from feature/enrollment-make-payment to exam-module-changes-v2
a4741765aa8ef98672e7ce03fca5abc5ca6d8448 157c1b5f697b6dcaa04de948bb08e38c980ccdbe Sim Zhen Quan <<EMAIL>> 1748402200 +0800	merge origin/main: Merge made by the 'ort' strategy.
157c1b5f697b6dcaa04de948bb08e38c980ccdbe 157c1b5f697b6dcaa04de948bb08e38c980ccdbe Sim Zhen Quan <<EMAIL>> 1748404226 +0800	checkout: moving from exam-module-changes-v2 to exam-module-changes-v2
157c1b5f697b6dcaa04de948bb08e38c980ccdbe d1fbb7c649716e05ffdf8d8afbe35eec5b3288ae Sim Zhen Quan <<EMAIL>> 1748404248 +0800	checkout: moving from exam-module-changes-v2 to dev
d1fbb7c649716e05ffdf8d8afbe35eec5b3288ae 3a72f500626fd0eee58a90ab5de2f7952ea20c23 Sim Zhen Quan <<EMAIL>> 1748404255 +0800	merge exam-module-changes-v2: Merge made by the 'ort' strategy.
3a72f500626fd0eee58a90ab5de2f7952ea20c23 d2fc2f1f8bf85bca9ceac7287b952027c76d81f9 Sim Zhen Quan <<EMAIL>> 1748423542 +0800	commit: Deployed to dev
d2fc2f1f8bf85bca9ceac7287b952027c76d81f9 ccab57ccf2dfdd2dc1151f0617affb175355341b Sim Zhen Quan <<EMAIL>> 1748423552 +0800	checkout: moving from dev to feature/enrollment-make-payment
ccab57ccf2dfdd2dc1151f0617affb175355341b ac3616781e229cf8966cf290324c78584e912457 Sim Zhen Quan <<EMAIL>> 1748423560 +0800	pull: Fast-forward
ac3616781e229cf8966cf290324c78584e912457 6a79ffa5d3fe82ab57aff5ed25607d32e6698e9f Sim Zhen Quan <<EMAIL>> 1748452298 +0800	commit: Reviewed
6a79ffa5d3fe82ab57aff5ed25607d32e6698e9f ab78d2678b1ff30ca8eb08bfb35fbfe2ec277578 Sim Zhen Quan <<EMAIL>> 1748452305 +0800	checkout: moving from feature/enrollment-make-payment to enrollment-v2
ab78d2678b1ff30ca8eb08bfb35fbfe2ec277578 c37fd465e6da8c6454b6a37aaf810afa70d42db3 Sim Zhen Quan <<EMAIL>> 1748452311 +0800	pull: Fast-forward
c37fd465e6da8c6454b6a37aaf810afa70d42db3 62f461e72d023531472aad0c73ea999df975f154 Sim Zhen Quan <<EMAIL>> 1748452311 +0800	merge origin/main: Merge made by the 'ort' strategy.
62f461e72d023531472aad0c73ea999df975f154 6a79ffa5d3fe82ab57aff5ed25607d32e6698e9f Sim Zhen Quan <<EMAIL>> 1748452325 +0800	checkout: moving from enrollment-v2 to feature/enrollment-make-payment
6a79ffa5d3fe82ab57aff5ed25607d32e6698e9f bc7d0de8c42f708b6e425a65c3cf9cd92561d752 Sim Zhen Quan <<EMAIL>> 1748452393 +0800	merge enrollment-v2: Merge made by the 'ort' strategy.
bc7d0de8c42f708b6e425a65c3cf9cd92561d752 a056772fa047e799a8fc988b5e809209d82ffa64 Sim Zhen Quan <<EMAIL>> 1748484107 +0800	checkout: moving from feature/enrollment-make-payment to feature/enrollment-update-APIs
a056772fa047e799a8fc988b5e809209d82ffa64 877e4289691ebcf453c3dcdd6caa0547ed4d5d56 Sim Zhen Quan <<EMAIL>> 1748484114 +0800	merge enrollment-v2: Merge made by the 'ort' strategy.
877e4289691ebcf453c3dcdd6caa0547ed4d5d56 877e4289691ebcf453c3dcdd6caa0547ed4d5d56 Sim Zhen Quan <<EMAIL>> 1748485306 +0800	reset: moving to HEAD
877e4289691ebcf453c3dcdd6caa0547ed4d5d56 373502496b3a8feef95995b4dd1ab833952e2798 Sim Zhen Quan <<EMAIL>> 1748485308 +0800	merge origin/enrollment-v2: Merge made by the 'ort' strategy.
373502496b3a8feef95995b4dd1ab833952e2798 a5b9cb0913769db9c9f3b164307519c75c155b90 Sim Zhen Quan <<EMAIL>> 1748485350 +0800	commit: Review WIP
a5b9cb0913769db9c9f3b164307519c75c155b90 baf28720f8b2bf2a74ebd514824af5679d1fade4 Sim Zhen Quan <<EMAIL>> 1748486395 +0800	commit: Review WIP
baf28720f8b2bf2a74ebd514824af5679d1fade4 8c39ea8747f816d2cc9df3e480a6cdc4e24cbf80 Sim Zhen Quan <<EMAIL>> 1748491006 +0800	checkout: moving from feature/enrollment-update-APIs to script-remove-elective-subject-from-primary-class
8c39ea8747f816d2cc9df3e480a6cdc4e24cbf80 7a2b73f7acc20ae42b3036d1dae63986441b545e Sim Zhen Quan <<EMAIL>> 1748491014 +0800	merge origin/main: Merge made by the 'ort' strategy.
7a2b73f7acc20ae42b3036d1dae63986441b545e ea060032ed951dd23f1eeec8733dfde75614a54c Sim Zhen Quan <<EMAIL>> 1748491203 +0800	commit: Reviewed
ea060032ed951dd23f1eeec8733dfde75614a54c d2fc2f1f8bf85bca9ceac7287b952027c76d81f9 Sim Zhen Quan <<EMAIL>> 1748491223 +0800	checkout: moving from script-remove-elective-subject-from-primary-class to dev
d2fc2f1f8bf85bca9ceac7287b952027c76d81f9 617d4b2c3576122a591a313be0bf4c09f4c1aaf3 Sim Zhen Quan <<EMAIL>> 1748491233 +0800	merge origin/main: Merge made by the 'ort' strategy.
617d4b2c3576122a591a313be0bf4c09f4c1aaf3 826b1588f7f205a1d794d6223e5f6f662cf51af8 Sim Zhen Quan <<EMAIL>> 1748491257 +0800	merge origin/exam-module-changes-v2: Merge made by the 'ort' strategy.
826b1588f7f205a1d794d6223e5f6f662cf51af8 826b1588f7f205a1d794d6223e5f6f662cf51af8 Sim Zhen Quan <<EMAIL>> 1748492148 +0800	reset: moving to HEAD
826b1588f7f205a1d794d6223e5f6f662cf51af8 dda13ce2f01c1c42db4eb12942a19ee34c55ed47 Sim Zhen Quan <<EMAIL>> 1748492151 +0800	checkout: moving from dev to main
dda13ce2f01c1c42db4eb12942a19ee34c55ed47 43b71abc11a33490f84b755c65323e6550fae4ed Sim Zhen Quan <<EMAIL>> 1748492155 +0800	pull: Fast-forward
43b71abc11a33490f84b755c65323e6550fae4ed 91a7dd5eaad921da64cd24a06941424b480c604f Sim Zhen Quan <<EMAIL>> 1748492168 +0800	commit: Corrected translations
91a7dd5eaad921da64cd24a06941424b480c604f 22ef14452f35905e4c388acf0362d52ca9eaef0b Sim Zhen Quan <<EMAIL>> 1748493144 +0800	checkout: moving from main to fix/update-validation-when-create-billing-doc
22ef14452f35905e4c388acf0362d52ca9eaef0b 80d5ac0cf84f021dd18a9d8b59483f3c711e2cc8 Sim Zhen Quan <<EMAIL>> 1748493151 +0800	merge origin/main: Merge made by the 'ort' strategy.
80d5ac0cf84f021dd18a9d8b59483f3c711e2cc8 baf28720f8b2bf2a74ebd514824af5679d1fade4 Sim Zhen Quan <<EMAIL>> 1748500554 +0800	checkout: moving from fix/update-validation-when-create-billing-doc to feature/enrollment-update-APIs
baf28720f8b2bf2a74ebd514824af5679d1fade4 da61737008ddd7047f1267136d50067610b6b2e8 Sim Zhen Quan <<EMAIL>> 1748500560 +0800	pull: Fast-forward
da61737008ddd7047f1267136d50067610b6b2e8 0d2746d7bde15d12d0cb7b2365bd08339d51b230 Sim Zhen Quan <<EMAIL>> 1748501495 +0800	commit: Reviewed
0d2746d7bde15d12d0cb7b2365bd08339d51b230 ced1099535f15642cf55a201ef1705ec31275fad Sim Zhen Quan <<EMAIL>> 1748501762 +0800	commit: Reviewed
ced1099535f15642cf55a201ef1705ec31275fad 62f461e72d023531472aad0c73ea999df975f154 Sim Zhen Quan <<EMAIL>> 1748501855 +0800	checkout: moving from feature/enrollment-update-APIs to enrollment-v2
62f461e72d023531472aad0c73ea999df975f154 77e91b2f7c435ad057144a70e765f759812527ba Sim Zhen Quan <<EMAIL>> 1748501861 +0800	pull: Fast-forward
77e91b2f7c435ad057144a70e765f759812527ba 4e903f91a461b99ffe644035cebe4844296cacef Sim Zhen Quan <<EMAIL>> 1748501865 +0800	merge origin/main: Merge made by the 'ort' strategy.
4e903f91a461b99ffe644035cebe4844296cacef 826b1588f7f205a1d794d6223e5f6f662cf51af8 Sim Zhen Quan <<EMAIL>> 1748501894 +0800	checkout: moving from enrollment-v2 to dev
826b1588f7f205a1d794d6223e5f6f662cf51af8 724b4b744a4de16f516604da9fcb380035b0b455 Sim Zhen Quan <<EMAIL>> 1748501899 +0800	merge enrollment-v2: Merge made by the 'ort' strategy.
724b4b744a4de16f516604da9fcb380035b0b455 724b4b744a4de16f516604da9fcb380035b0b455 Sim Zhen Quan <<EMAIL>> 1748502811 +0800	reset: moving to HEAD
724b4b744a4de16f516604da9fcb380035b0b455 4e903f91a461b99ffe644035cebe4844296cacef Sim Zhen Quan <<EMAIL>> 1748502815 +0800	checkout: moving from dev to enrollment-v2
4e903f91a461b99ffe644035cebe4844296cacef f28c1f368300854c07ac9a420274b60de19fe364 Sim Zhen Quan <<EMAIL>> 1748502902 +0800	commit: Added master data routes
f28c1f368300854c07ac9a420274b60de19fe364 724b4b744a4de16f516604da9fcb380035b0b455 Sim Zhen Quan <<EMAIL>> 1748502928 +0800	checkout: moving from enrollment-v2 to dev
724b4b744a4de16f516604da9fcb380035b0b455 c78f370d25277add07b366d87cc0cffc61522af6 Sim Zhen Quan <<EMAIL>> 1748502935 +0800	merge enrollment-v2: Merge made by the 'ort' strategy.
c78f370d25277add07b366d87cc0cffc61522af6 7bf4d65c645dd877b048b2966a1ed73dafdff461 Sim Zhen Quan <<EMAIL>> 1748507658 +0800	commit: Deployed to dev
7bf4d65c645dd877b048b2966a1ed73dafdff461 a54d138cfac1f3ff48c8e274180b5fd61fa3d44c Sim Zhen Quan <<EMAIL>> 1748507667 +0800	commit: Updated api route
a54d138cfac1f3ff48c8e274180b5fd61fa3d44c f28c1f368300854c07ac9a420274b60de19fe364 Sim Zhen Quan <<EMAIL>> 1748507676 +0800	checkout: moving from dev to enrollment-v2
f28c1f368300854c07ac9a420274b60de19fe364 bc0c7d24185b5a70058fb423681586324ebd2a9d Sim Zhen Quan <<EMAIL>> 1748507720 +0800	cherry-pick: Updated api route
bc0c7d24185b5a70058fb423681586324ebd2a9d a54d138cfac1f3ff48c8e274180b5fd61fa3d44c Sim Zhen Quan <<EMAIL>> 1748508278 +0800	checkout: moving from enrollment-v2 to dev
a54d138cfac1f3ff48c8e274180b5fd61fa3d44c b0e672e8136503902ba74f44b29868c5c60607c4 Sim Zhen Quan <<EMAIL>> 1748508284 +0800	pull: Fast-forward
b0e672e8136503902ba74f44b29868c5c60607c4 4a3a7921de3d9623ba6b12e673ee1176c1ae2599 Sim Zhen Quan <<EMAIL>> 1748508858 +0800	commit: Deployed to dev
4a3a7921de3d9623ba6b12e673ee1176c1ae2599 bc0c7d24185b5a70058fb423681586324ebd2a9d Sim Zhen Quan <<EMAIL>> 1748570987 +0800	checkout: moving from dev to enrollment-v2
bc0c7d24185b5a70058fb423681586324ebd2a9d bc0c7d24185b5a70058fb423681586324ebd2a9d Sim Zhen Quan <<EMAIL>> 1748570996 +0800	checkout: moving from enrollment-v2 to enrollment-enhancements
bc0c7d24185b5a70058fb423681586324ebd2a9d e849b832797d579a1de1742d60296ddb21204e79 Sim Zhen Quan <<EMAIL>> 1748572624 +0800	commit: Updated enrollment session
e849b832797d579a1de1742d60296ddb21204e79 8252973b39acd8d125bb1b55bd39c327f17c0b24 Sim Zhen Quan <<EMAIL>> 1748581081 +0800	commit: Update code to cater if no need to pay for enrollment fees
8252973b39acd8d125bb1b55bd39c327f17c0b24 da3c1526c648a371985c2a8d55195cded1c06931 Sim Zhen Quan <<EMAIL>> 1748834100 +0800	commit: Files cleanup
da3c1526c648a371985c2a8d55195cded1c06931 63ee1fb2f70393155b02be4049a5e680b9882a52 Sim Zhen Quan <<EMAIL>> 1748835027 +0800	commit: Files cleanup
63ee1fb2f70393155b02be4049a5e680b9882a52 148360da5b2ce54347f7b14fe5569f1f5e6f4571 Sim Zhen Quan <<EMAIL>> 1748836370 +0800	checkout: moving from enrollment-enhancements to feature/post-payment-enrollment-import
148360da5b2ce54347f7b14fe5569f1f5e6f4571 130817759d2de6cac757693fcb6a4a7891a6fd9e Sim Zhen Quan <<EMAIL>> 1748836377 +0800	pull: Fast-forward
130817759d2de6cac757693fcb6a4a7891a6fd9e 8eb13506a457e8e6ce3e04b3ff2b369506b06acb Sim Zhen Quan <<EMAIL>> 1748836435 +0800	commit (merge): Merge remote-tracking branch 'origin/enrollment-v2' into feature/post-payment-enrollment-import
8eb13506a457e8e6ce3e04b3ff2b369506b06acb 841c188290d1fa9e5f669168df056043a2ed0959 Sim Zhen Quan <<EMAIL>> 1748839802 +0800	commit: Updated code to allow excel upload without exam slip number
841c188290d1fa9e5f669168df056043a2ed0959 5c8680f1e32d1b604de8dae1ec61677b194ef59a Sim Zhen Quan <<EMAIL>> 1748841548 +0800	commit: - Added auto create guardian when import
5c8680f1e32d1b604de8dae1ec61677b194ef59a 4a3a7921de3d9623ba6b12e673ee1176c1ae2599 Sim Zhen Quan <<EMAIL>> 1748841564 +0800	checkout: moving from feature/post-payment-enrollment-import to dev
4a3a7921de3d9623ba6b12e673ee1176c1ae2599 4b367509a6541221febb939ec0d2f9854f9abfde Sim Zhen Quan <<EMAIL>> 1748841573 +0800	merge origin/feature/post-payment-enrollment-import: Merge made by the 'ort' strategy.
4b367509a6541221febb939ec0d2f9854f9abfde 5c8680f1e32d1b604de8dae1ec61677b194ef59a Sim Zhen Quan <<EMAIL>> 1748849391 +0800	checkout: moving from dev to feature/post-payment-enrollment-import
5c8680f1e32d1b604de8dae1ec61677b194ef59a e72b72ab62523082abc8a6ca5256577896b6a938 Sim Zhen Quan <<EMAIL>> 1748849564 +0800	commit: Removed student number
e72b72ab62523082abc8a6ca5256577896b6a938 86409d49c6e4274416f8c5a8902c03338c35f805 Sim Zhen Quan <<EMAIL>> 1748852386 +0800	commit: Reviewed
86409d49c6e4274416f8c5a8902c03338c35f805 4b367509a6541221febb939ec0d2f9854f9abfde Sim Zhen Quan <<EMAIL>> 1748852487 +0800	checkout: moving from feature/post-payment-enrollment-import to dev
4b367509a6541221febb939ec0d2f9854f9abfde 358b592db4cd8d6278248ae0a0fb205ce07766a7 Sim Zhen Quan <<EMAIL>> 1748852493 +0800	merge origin/enrollment-v2: Merge made by the 'ort' strategy.
358b592db4cd8d6278248ae0a0fb205ce07766a7 bc0c7d24185b5a70058fb423681586324ebd2a9d Sim Zhen Quan <<EMAIL>> 1748852953 +0800	checkout: moving from dev to enrollment-v2
bc0c7d24185b5a70058fb423681586324ebd2a9d e7deb8bfc29b90f85d1193b99fcf947a91e6a338 Sim Zhen Quan <<EMAIL>> 1748852959 +0800	pull: Fast-forward
e7deb8bfc29b90f85d1193b99fcf947a91e6a338 f8d66016cc8694fed618b657f19312c2ee486ea7 Sim Zhen Quan <<EMAIL>> 1748854375 +0800	commit: Added redirect_url to enrollment
f8d66016cc8694fed618b657f19312c2ee486ea7 358b592db4cd8d6278248ae0a0fb205ce07766a7 Sim Zhen Quan <<EMAIL>> 1748854389 +0800	checkout: moving from enrollment-v2 to dev
358b592db4cd8d6278248ae0a0fb205ce07766a7 80aade318b9526815a0f22bb088055288b30fc74 Sim Zhen Quan <<EMAIL>> 1748854405 +0800	merge origin/enrollment-v2: Merge made by the 'ort' strategy.
80aade318b9526815a0f22bb088055288b30fc74 91a7dd5eaad921da64cd24a06941424b480c604f Sim Zhen Quan <<EMAIL>> 1748857062 +0800	checkout: moving from dev to main
91a7dd5eaad921da64cd24a06941424b480c604f 91a7dd5eaad921da64cd24a06941424b480c604f Sim Zhen Quan <<EMAIL>> 1748857075 +0800	checkout: moving from main to billing-doc-report-enhancement
91a7dd5eaad921da64cd24a06941424b480c604f f8d66016cc8694fed618b657f19312c2ee486ea7 Sim Zhen Quan <<EMAIL>> 1748857104 +0800	checkout: moving from billing-doc-report-enhancement to enrollment-v2
f8d66016cc8694fed618b657f19312c2ee486ea7 81734f64bc6f60c6a716f89ead22af47f6abbf58 Sim Zhen Quan <<EMAIL>> 1748857527 +0800	commit: Minor enhancements
81734f64bc6f60c6a716f89ead22af47f6abbf58 91a7dd5eaad921da64cd24a06941424b480c604f Sim Zhen Quan <<EMAIL>> 1748857535 +0800	checkout: moving from enrollment-v2 to billing-doc-report-enhancement
91a7dd5eaad921da64cd24a06941424b480c604f 81734f64bc6f60c6a716f89ead22af47f6abbf58 Sim Zhen Quan <<EMAIL>> 1748857736 +0800	checkout: moving from billing-doc-report-enhancement to enrollment-v2
81734f64bc6f60c6a716f89ead22af47f6abbf58 2c520c68677edecb3f6d3b502465951a07884b53 Sim Zhen Quan <<EMAIL>> 1748857812 +0800	commit: Minor enhancements
2c520c68677edecb3f6d3b502465951a07884b53 91a7dd5eaad921da64cd24a06941424b480c604f Sim Zhen Quan <<EMAIL>> 1748857817 +0800	checkout: moving from enrollment-v2 to billing-doc-report-enhancement
91a7dd5eaad921da64cd24a06941424b480c604f 482e3e6454b5648d9b0900e9f82160f32576d3f0 Sim Zhen Quan <<EMAIL>> 1748883206 +0800	commit: Excel enhancements
482e3e6454b5648d9b0900e9f82160f32576d3f0 80aade318b9526815a0f22bb088055288b30fc74 Sim Zhen Quan <<EMAIL>> 1748920255 +0800	checkout: moving from billing-doc-report-enhancement to dev
80aade318b9526815a0f22bb088055288b30fc74 91a7dd5eaad921da64cd24a06941424b480c604f Sim Zhen Quan <<EMAIL>> 1748938224 +0800	checkout: moving from dev to main
91a7dd5eaad921da64cd24a06941424b480c604f 91a7dd5eaad921da64cd24a06941424b480c604f Sim Zhen Quan <<EMAIL>> 1748938234 +0800	checkout: moving from main to staging/2025-06-03
91a7dd5eaad921da64cd24a06941424b480c604f 7c65e0426842307d9a696aeb13d680f36e3f0cd7 Sim Zhen Quan <<EMAIL>> 1748939812 +0800	checkout: moving from staging/2025-06-03 to issue-186-mark-deduction-report-exclude-inactive-students
7c65e0426842307d9a696aeb13d680f36e3f0cd7 91a7dd5eaad921da64cd24a06941424b480c604f Sim Zhen Quan <<EMAIL>> 1748940319 +0800	checkout: moving from issue-186-mark-deduction-report-exclude-inactive-students to staging/2025-06-03
91a7dd5eaad921da64cd24a06941424b480c604f 80aade318b9526815a0f22bb088055288b30fc74 Sim Zhen Quan <<EMAIL>> 1748940458 +0800	checkout: moving from staging/2025-06-03 to dev
80aade318b9526815a0f22bb088055288b30fc74 fc8c90328045e4239373de52470bcb266574c5ef Sim Zhen Quan <<EMAIL>> 1748940468 +0800	merge billing-doc-report-enhancement: Merge made by the 'ort' strategy.
fc8c90328045e4239373de52470bcb266574c5ef 8412b1a53b2e7c74b53f0fba5869dcd392cd2ca1 Sim Zhen Quan <<EMAIL>> 1748964244 +0800	commit: Deployed to dev
8412b1a53b2e7c74b53f0fba5869dcd392cd2ca1 91a7dd5eaad921da64cd24a06941424b480c604f Sim Zhen Quan <<EMAIL>> 1748964250 +0800	checkout: moving from dev to main
91a7dd5eaad921da64cd24a06941424b480c604f 29eadf6f28a047f121b21b39bc1e37f070dbceb8 Sim Zhen Quan <<EMAIL>> 1748964257 +0800	pull: Fast-forward
29eadf6f28a047f121b21b39bc1e37f070dbceb8 b256a94df313192188826a85009a7fbcd5b43b97 Sim Zhen Quan <<EMAIL>> 1748965393 +0800	commit: Deployed to prd
b256a94df313192188826a85009a7fbcd5b43b97 bf985836c3ae0d111f91af71d075ccb81913ebb5 Sim Zhen Quan <<EMAIL>> 1748965922 +0800	checkout: moving from main to issue-140-by-student-in-class-coco-and-english
bf985836c3ae0d111f91af71d075ccb81913ebb5 6a3308368438b927003c819fc960093e34f69be6 Sim Zhen Quan <<EMAIL>> 1748965928 +0800	merge origin/main: Merge made by the 'ort' strategy.
6a3308368438b927003c819fc960093e34f69be6 8412b1a53b2e7c74b53f0fba5869dcd392cd2ca1 Sim Zhen Quan <<EMAIL>> 1748966679 +0800	checkout: moving from issue-140-by-student-in-class-coco-and-english to dev
8412b1a53b2e7c74b53f0fba5869dcd392cd2ca1 defb071d90447e6da5ac433c99215dd7dc15fa15 Sim Zhen Quan <<EMAIL>> 1748966688 +0800	merge origin/issue-140-by-student-in-class-coco-and-english: Merge made by the 'ort' strategy.
defb071d90447e6da5ac433c99215dd7dc15fa15 680bd914578b8a1a98d877ac2b3d9ab8bfeaec4b Sim Zhen Quan <<EMAIL>> 1749002776 +0800	commit: Deployed to dev
680bd914578b8a1a98d877ac2b3d9ab8bfeaec4b f0600e777644f58eed098be0a186b1c32a389fd4 Sim Zhen Quan <<EMAIL>> 1749003055 +0800	checkout: moving from dev to feature/import-validation-changes
f0600e777644f58eed098be0a186b1c32a389fd4 6a3308368438b927003c819fc960093e34f69be6 Sim Zhen Quan <<EMAIL>> 1749005515 +0800	checkout: moving from feature/import-validation-changes to issue-140-by-student-in-class-coco-and-english
6a3308368438b927003c819fc960093e34f69be6 b67db56352b1a1770d61778f1662873143524989 Sim Zhen Quan <<EMAIL>> 1749005915 +0800	commit: Added permission
b67db56352b1a1770d61778f1662873143524989 680bd914578b8a1a98d877ac2b3d9ab8bfeaec4b Sim Zhen Quan <<EMAIL>> 1749005932 +0800	checkout: moving from issue-140-by-student-in-class-coco-and-english to dev
680bd914578b8a1a98d877ac2b3d9ab8bfeaec4b 47ee8a33d641abe2a90970860c1bc509f431306c Sim Zhen Quan <<EMAIL>> 1749005937 +0800	merge issue-140-by-student-in-class-coco-and-english: Merge made by the 'ort' strategy.
47ee8a33d641abe2a90970860c1bc509f431306c b67db56352b1a1770d61778f1662873143524989 Sim Zhen Quan <<EMAIL>> 1749006357 +0800	checkout: moving from dev to issue-140-by-student-in-class-coco-and-english
b67db56352b1a1770d61778f1662873143524989 27ebd33fa4440f695aa1e3f37051e8aab1997cdf Sim Zhen Quan <<EMAIL>> 1749006363 +0800	commit: Fix blade issue
27ebd33fa4440f695aa1e3f37051e8aab1997cdf 47ee8a33d641abe2a90970860c1bc509f431306c Sim Zhen Quan <<EMAIL>> 1749006396 +0800	checkout: moving from issue-140-by-student-in-class-coco-and-english to dev
47ee8a33d641abe2a90970860c1bc509f431306c 6b75b220d75484cafc53afc66cf02283d0cf4f45 Sim Zhen Quan <<EMAIL>> 1749006408 +0800	merge issue-140-by-student-in-class-coco-and-english: Merge made by the 'ort' strategy.
6b75b220d75484cafc53afc66cf02283d0cf4f45 f0600e777644f58eed098be0a186b1c32a389fd4 Sim Zhen Quan <<EMAIL>> 1749006529 +0800	checkout: moving from dev to feature/import-validation-changes
f0600e777644f58eed098be0a186b1c32a389fd4 3f935ac82e1d50ada0960e1d491517c5fac49b23 Sim Zhen Quan <<EMAIL>> 1749006533 +0800	pull: Fast-forward
3f935ac82e1d50ada0960e1d491517c5fac49b23 274a99e55108aedf5f4f856f5f94b1c72d38d9fb Sim Zhen Quan <<EMAIL>> 1749006678 +0800	commit: Fix typo
274a99e55108aedf5f4f856f5f94b1c72d38d9fb 313f0cd59ecdb18725d5d207113bee1700d56929 Sim Zhen Quan <<EMAIL>> 1749006752 +0800	checkout: moving from feature/import-validation-changes to enrollment-register-student-report
313f0cd59ecdb18725d5d207113bee1700d56929 25a3c0d43edf3e5676dbc50f3b4c44aa05052f17 Sim Zhen Quan <<EMAIL>> 1749006905 +0800	commit (merge): Merge remote-tracking branch 'origin/enrollment-v2' into enrollment-register-student-report
25a3c0d43edf3e5676dbc50f3b4c44aa05052f17 27ebd33fa4440f695aa1e3f37051e8aab1997cdf Sim Zhen Quan <<EMAIL>> 1749008678 +0800	checkout: moving from enrollment-register-student-report to issue-140-by-student-in-class-coco-and-english
27ebd33fa4440f695aa1e3f37051e8aab1997cdf 50a90045d1e4d06708e400bc12a6db614e56401b Sim Zhen Quan <<EMAIL>> 1749009664 +0800	commit: Fix report format
50a90045d1e4d06708e400bc12a6db614e56401b 25a3c0d43edf3e5676dbc50f3b4c44aa05052f17 Sim Zhen Quan <<EMAIL>> 1749010013 +0800	checkout: moving from issue-140-by-student-in-class-coco-and-english to enrollment-register-student-report
25a3c0d43edf3e5676dbc50f3b4c44aa05052f17 f4c428f6d86b9a782b4dad15fb79f5a6f0830150 Sim Zhen Quan <<EMAIL>> 1749012553 +0800	commit: Reviewed
f4c428f6d86b9a782b4dad15fb79f5a6f0830150 f4c428f6d86b9a782b4dad15fb79f5a6f0830150 Sim Zhen Quan <<EMAIL>> 1749012771 +0800	reset: moving to HEAD
f4c428f6d86b9a782b4dad15fb79f5a6f0830150 2c520c68677edecb3f6d3b502465951a07884b53 Sim Zhen Quan <<EMAIL>> 1749012773 +0800	checkout: moving from enrollment-register-student-report to enrollment-v2
2c520c68677edecb3f6d3b502465951a07884b53 650c83688b1d8854d7e8691374028121ecabf3b3 Sim Zhen Quan <<EMAIL>> 1749012780 +0800	pull: Fast-forward
650c83688b1d8854d7e8691374028121ecabf3b3 a3f3acb231bf1e9fc916321bbe8c1cc5ad4e4ad0 Sim Zhen Quan <<EMAIL>> 1749012808 +0800	commit: Reviewed
a3f3acb231bf1e9fc916321bbe8c1cc5ad4e4ad0 a3f3acb231bf1e9fc916321bbe8c1cc5ad4e4ad0 Sim Zhen Quan <<EMAIL>> 1749012934 +0800	checkout: moving from enrollment-v2 to add-expiry-date-and-register-date
a3f3acb231bf1e9fc916321bbe8c1cc5ad4e4ad0 1fdf5f17b19285d1b72bc4630f918aec07ae6445 Sim Zhen Quan <<EMAIL>> 1749013210 +0800	commit: Added expiry date and register date
1fdf5f17b19285d1b72bc4630f918aec07ae6445 6b75b220d75484cafc53afc66cf02283d0cf4f45 Sim Zhen Quan <<EMAIL>> 1749016677 +0800	checkout: moving from add-expiry-date-and-register-date to dev
6b75b220d75484cafc53afc66cf02283d0cf4f45 1fdf5f17b19285d1b72bc4630f918aec07ae6445 Sim Zhen Quan <<EMAIL>> 1749016830 +0800	checkout: moving from dev to add-expiry-date-and-register-date
1fdf5f17b19285d1b72bc4630f918aec07ae6445 484f5033f3756a8850d79befde70c6cdfe501e15 Sim Zhen Quan <<EMAIL>> 1749016873 +0800	commit: Added register_date and expiry_date into excel template
484f5033f3756a8850d79befde70c6cdfe501e15 687ed9b1084790f85b791ca06ebabb0072e7569e Sim Zhen Quan <<EMAIL>> 1749017266 +0800	commit: Added register_date and expiry_date into excel template
687ed9b1084790f85b791ca06ebabb0072e7569e 6b75b220d75484cafc53afc66cf02283d0cf4f45 Sim Zhen Quan <<EMAIL>> 1749017288 +0800	checkout: moving from add-expiry-date-and-register-date to dev
6b75b220d75484cafc53afc66cf02283d0cf4f45 d9a91372f2c1cb66a67ce417edd439863325547d Sim Zhen Quan <<EMAIL>> 1749017298 +0800	merge origin/enrollment-v2: Merge made by the 'ort' strategy.
d9a91372f2c1cb66a67ce417edd439863325547d c916efd4a66d99258a0e10b0ad081c5e7f9b515c Sim Zhen Quan <<EMAIL>> 1749019105 +0800	commit: Deployed to dev
c916efd4a66d99258a0e10b0ad081c5e7f9b515c c916efd4a66d99258a0e10b0ad081c5e7f9b515c Sim Zhen Quan <<EMAIL>> 1749020244 +0800	reset: moving to HEAD
c916efd4a66d99258a0e10b0ad081c5e7f9b515c a3f3acb231bf1e9fc916321bbe8c1cc5ad4e4ad0 Sim Zhen Quan <<EMAIL>> 1749020244 +0800	checkout: moving from dev to enrollment-v2
a3f3acb231bf1e9fc916321bbe8c1cc5ad4e4ad0 cbdb1e374720b93c88554d590ab7b5f09973dd06 Sim Zhen Quan <<EMAIL>> 1749020251 +0800	pull: Fast-forward
cbdb1e374720b93c88554d590ab7b5f09973dd06 7ac2f04fd6a54891dd34f15638bc94a551a750b6 Sim Zhen Quan <<EMAIL>> 1749020378 +0800	commit: Fixed date issue
7ac2f04fd6a54891dd34f15638bc94a551a750b6 50a90045d1e4d06708e400bc12a6db614e56401b Sim Zhen Quan <<EMAIL>> 1749020385 +0800	checkout: moving from enrollment-v2 to issue-140-by-student-in-class-coco-and-english
50a90045d1e4d06708e400bc12a6db614e56401b e1d8a48d620e0e5664741ec0d8abda7b70e895e1 Sim Zhen Quan <<EMAIL>> 1749022471 +0800	commit: Fix report
e1d8a48d620e0e5664741ec0d8abda7b70e895e1 c916efd4a66d99258a0e10b0ad081c5e7f9b515c Sim Zhen Quan <<EMAIL>> 1749022495 +0800	checkout: moving from issue-140-by-student-in-class-coco-and-english to dev
c916efd4a66d99258a0e10b0ad081c5e7f9b515c 9df3d915091740196d03032635400f8aab605d39 Sim Zhen Quan <<EMAIL>> 1749022499 +0800	merge issue-140-by-student-in-class-coco-and-english: Merge made by the 'ort' strategy.
9df3d915091740196d03032635400f8aab605d39 7ac2f04fd6a54891dd34f15638bc94a551a750b6 Sim Zhen Quan <<EMAIL>> 1749022518 +0800	checkout: moving from dev to enrollment-v2
7ac2f04fd6a54891dd34f15638bc94a551a750b6 d2b8e2a011e8e63bf0d12fe2a47525d2811ff62a Sim Zhen Quan <<EMAIL>> 1749022715 +0800	commit: Fix EnrollmentPrePaymentImport date
d2b8e2a011e8e63bf0d12fe2a47525d2811ff62a 9df3d915091740196d03032635400f8aab605d39 Sim Zhen Quan <<EMAIL>> 1749022724 +0800	checkout: moving from enrollment-v2 to dev
9df3d915091740196d03032635400f8aab605d39 9edcb7cc0671fdecbdac31e5955fca736ba4c1d6 Sim Zhen Quan <<EMAIL>> 1749022724 +0800	merge enrollment-v2: Merge made by the 'ort' strategy.
9edcb7cc0671fdecbdac31e5955fca736ba4c1d6 d2b8e2a011e8e63bf0d12fe2a47525d2811ff62a Sim Zhen Quan <<EMAIL>> 1749023303 +0800	checkout: moving from dev to enrollment-v2
d2b8e2a011e8e63bf0d12fe2a47525d2811ff62a 241bcb01229bc7edbd40d3c8d9706f8189820f28 Sim Zhen Quan <<EMAIL>> 1749023614 +0800	checkout: moving from enrollment-v2 to feature/retry-enrollment-payment-API
241bcb01229bc7edbd40d3c8d9706f8189820f28 0511b950a8c7ab5e138c20a691cb4fa4810ea22a Sim Zhen Quan <<EMAIL>> 1749023958 +0800	commit (merge): Merge remote-tracking branch 'origin/enrollment-v2' into feature/retry-enrollment-payment-API
0511b950a8c7ab5e138c20a691cb4fa4810ea22a d2b8e2a011e8e63bf0d12fe2a47525d2811ff62a Sim Zhen Quan <<EMAIL>> 1749024052 +0800	checkout: moving from feature/retry-enrollment-payment-API to enrollment-v2
d2b8e2a011e8e63bf0d12fe2a47525d2811ff62a d0a4529d3f89fd0b39f1f5b872c60893386c2545 Sim Zhen Quan <<EMAIL>> 1749024057 +0800	merge origin/main: Merge made by the 'ort' strategy.
d0a4529d3f89fd0b39f1f5b872c60893386c2545 0511b950a8c7ab5e138c20a691cb4fa4810ea22a Sim Zhen Quan <<EMAIL>> 1749024092 +0800	checkout: moving from enrollment-v2 to feature/retry-enrollment-payment-API
0511b950a8c7ab5e138c20a691cb4fa4810ea22a 445a95c29c91d16db3131f009c23a34c714364c1 Sim Zhen Quan <<EMAIL>> 1749024231 +0800	commit (merge): Merge remote-tracking branch 'origin/enrollment-v2' into feature/retry-enrollment-payment-API
445a95c29c91d16db3131f009c23a34c714364c1 445a95c29c91d16db3131f009c23a34c714364c1 Sim Zhen Quan <<EMAIL>> 1749027709 +0800	reset: moving to HEAD
445a95c29c91d16db3131f009c23a34c714364c1 9edcb7cc0671fdecbdac31e5955fca736ba4c1d6 Sim Zhen Quan <<EMAIL>> 1749027710 +0800	checkout: moving from feature/retry-enrollment-payment-API to dev
9edcb7cc0671fdecbdac31e5955fca736ba4c1d6 b256a94df313192188826a85009a7fbcd5b43b97 Sim Zhen Quan <<EMAIL>> 1749027721 +0800	checkout: moving from dev to main
b256a94df313192188826a85009a7fbcd5b43b97 445a95c29c91d16db3131f009c23a34c714364c1 Sim Zhen Quan <<EMAIL>> 1749027735 +0800	checkout: moving from main to feature/retry-enrollment-payment-API
445a95c29c91d16db3131f009c23a34c714364c1 a182e5dfacef79b181b6fdb0b6e32f8116a5d8b9 Sim Zhen Quan <<EMAIL>> 1749028233 +0800	commit: Reviewed
a182e5dfacef79b181b6fdb0b6e32f8116a5d8b9 9edcb7cc0671fdecbdac31e5955fca736ba4c1d6 Sim Zhen Quan <<EMAIL>> 1749028241 +0800	checkout: moving from feature/retry-enrollment-payment-API to dev
9edcb7cc0671fdecbdac31e5955fca736ba4c1d6 dfba6b8e8bee69b06832ded1af88867d8a233cb9 Sim Zhen Quan <<EMAIL>> 1749028251 +0800	merge feature/retry-enrollment-payment-API: Merge made by the 'ort' strategy.
dfba6b8e8bee69b06832ded1af88867d8a233cb9 d0e4c8fc8fc4f51136eeddd7611c3fc20adefe9f Sim Zhen Quan <<EMAIL>> 1749031308 +0800	commit: Deployed to dev
d0e4c8fc8fc4f51136eeddd7611c3fc20adefe9f 185e8481d44a4254c0fa78699c36698369c8656a Sim Zhen Quan <<EMAIL>> 1749031347 +0800	commit: Fixes
185e8481d44a4254c0fa78699c36698369c8656a b280a63c991755f13a4665f9f00728ad5fa2f2fc Sim Zhen Quan <<EMAIL>> 1749033265 +0800	checkout: moving from dev to feature/enrollment-autocount-report
b280a63c991755f13a4665f9f00728ad5fa2f2fc 1f1b019b1da953b6238427c03ecdb760b61503c1 Sim Zhen Quan <<EMAIL>> 1749033300 +0800	merge feature/retry-enrollment-payment-API: Merge made by the 'ort' strategy.
1f1b019b1da953b6238427c03ecdb760b61503c1 cdb511b21f3a13eb863045e0ff1bf4f7504230aa Sim Zhen Quan <<EMAIL>> 1749034434 +0800	commit: Added queue for enrollment
cdb511b21f3a13eb863045e0ff1bf4f7504230aa 185e8481d44a4254c0fa78699c36698369c8656a Sim Zhen Quan <<EMAIL>> 1749034446 +0800	checkout: moving from feature/enrollment-autocount-report to dev
185e8481d44a4254c0fa78699c36698369c8656a 197ebe792107fe497e822beba41a7d0e902e2327 Sim Zhen Quan <<EMAIL>> 1749034447 +0800	merge feature/enrollment-autocount-report: Merge made by the 'ort' strategy.
197ebe792107fe497e822beba41a7d0e902e2327 a88993cbbdaa1023998aed5745d00ebaed2babec Sim Zhen Quan <<EMAIL>> 1749092729 +0800	commit: Deployed to dev
a88993cbbdaa1023998aed5745d00ebaed2babec b256a94df313192188826a85009a7fbcd5b43b97 Sim Zhen Quan <<EMAIL>> ********** +0800	checkout: moving from dev to main
b256a94df313192188826a85009a7fbcd5b43b97 b256a94df313192188826a85009a7fbcd5b43b97 Sim Zhen Quan <<EMAIL>> ********** +0800	checkout: moving from main to staging/2025-06-05
b256a94df313192188826a85009a7fbcd5b43b97 b2588d8558b0b48ee0d9435b9da0d0d0dc984e1e Sim Zhen Quan <<EMAIL>> ********** +0800	checkout: moving from staging/2025-06-05 to report/hostel-report-saving-account
b2588d8558b0b48ee0d9435b9da0d0d0dc984e1e 4b36d3ab59234b09328a88b7d52838f9c17a5047 Sim Zhen Quan <<EMAIL>> ********** +0800	commit (merge): Merge remote-tracking branch 'origin/main' into report/hostel-report-saving-account
4b36d3ab59234b09328a88b7d52838f9c17a5047 e652ad79345b0594222b3a5a5522d6d9d4997f13 Sim Zhen Quan <<EMAIL>> ********** +0800	checkout: moving from report/hostel-report-saving-account to allow-take-class-attendance-5-mins-earlier
e652ad79345b0594222b3a5a5522d6d9d4997f13 c250154f5cc1d7b2fca1d0a932b7df7a5ade3d4a Sim Zhen Quan <<EMAIL>> ********** +0800	merge origin/main: Merge made by the 'ort' strategy.
c250154f5cc1d7b2fca1d0a932b7df7a5ade3d4a 43cf8fe3511d07b7f3f45fb7c8ea9715eb43489d Sim Zhen Quan <<EMAIL>> ********** +0800	commit: Reviewed
43cf8fe3511d07b7f3f45fb7c8ea9715eb43489d a88993cbbdaa1023998aed5745d00ebaed2babec Sim Zhen Quan <<EMAIL>> ********** +0800	checkout: moving from allow-take-class-attendance-5-mins-earlier to dev
a88993cbbdaa1023998aed5745d00ebaed2babec 510c1cc4fb1115fe1442059608f0999065f2f363 Sim Zhen Quan <<EMAIL>> ********** +0800	merge allow-take-class-attendance-5-mins-earlier: Merge made by the 'ort' strategy.
510c1cc4fb1115fe1442059608f0999065f2f363 80d5ac0cf84f021dd18a9d8b59483f3c711e2cc8 Sim Zhen Quan <<EMAIL>> ********** +0800	checkout: moving from dev to fix/update-validation-when-create-billing-doc
80d5ac0cf84f021dd18a9d8b59483f3c711e2cc8 cb379a17b4ec9a8261e39123d721226d62b6d0d9 Sim Zhen Quan <<EMAIL>> 1749093540 +0800	merge origin/main: Merge made by the 'ort' strategy.
cb379a17b4ec9a8261e39123d721226d62b6d0d9 d00d80383e1784ea180cc44a2751f8bd280fcb8f Sim Zhen Quan <<EMAIL>> 1749094825 +0800	commit: Reviewed
d00d80383e1784ea180cc44a2751f8bd280fcb8f 510c1cc4fb1115fe1442059608f0999065f2f363 Sim Zhen Quan <<EMAIL>> 1749094934 +0800	checkout: moving from fix/update-validation-when-create-billing-doc to dev
510c1cc4fb1115fe1442059608f0999065f2f363 d262f3885d1649141dfcddace142bcae843b31a1 Sim Zhen Quan <<EMAIL>> 1749094940 +0800	merge fix/update-validation-when-create-billing-doc: Merge made by the 'ort' strategy.
d262f3885d1649141dfcddace142bcae843b31a1 19168b635157ad77386aef2fd950ba2149298802 Sim Zhen Quan <<EMAIL>> 1749095324 +0800	checkout: moving from dev to enhancement/student-attendance-report
19168b635157ad77386aef2fd950ba2149298802 07fed8b495cf493ed09872b30be0a774d57694d0 Sim Zhen Quan <<EMAIL>> 1749095382 +0800	merge origin/main: Merge made by the 'ort' strategy.
07fed8b495cf493ed09872b30be0a774d57694d0 84f043941dafbab95c1eeb6861c71def21e6311b Sim Zhen Quan <<EMAIL>> 1749097975 +0800	commit: Reviewed
84f043941dafbab95c1eeb6861c71def21e6311b d262f3885d1649141dfcddace142bcae843b31a1 Sim Zhen Quan <<EMAIL>> 1749098011 +0800	checkout: moving from enhancement/student-attendance-report to dev
d262f3885d1649141dfcddace142bcae843b31a1 05623284ad8d56cda06315c5ea7d531ab32d3d63 Sim Zhen Quan <<EMAIL>> 1749098016 +0800	merge enhancement/student-attendance-report: Merge made by the 'ort' strategy.
05623284ad8d56cda06315c5ea7d531ab32d3d63 b18bced93dc57256ec45979081ce6fb7d7b7d1a5 Sim Zhen Quan <<EMAIL>> 1749107012 +0800	commit: Deployed to dev
b18bced93dc57256ec45979081ce6fb7d7b7d1a5 157c1b5f697b6dcaa04de948bb08e38c980ccdbe Sim Zhen Quan <<EMAIL>> 1749107018 +0800	checkout: moving from dev to exam-module-changes-v2
157c1b5f697b6dcaa04de948bb08e38c980ccdbe 512967e21a4e460a61a44448e23bec3940f19908 Sim Zhen Quan <<EMAIL>> 1749107024 +0800	pull: Fast-forward
512967e21a4e460a61a44448e23bec3940f19908 c524bbe3d220e2d81e93fa0539b4499efcc0a251 Sim Zhen Quan <<EMAIL>> 1749110989 +0800	pull: Fast-forward
c524bbe3d220e2d81e93fa0539b4499efcc0a251 956f870947f6a90e76df46ae15264eb3d07971b5 Sim Zhen Quan <<EMAIL>> 1749117956 +0800	commit: Reviewed
956f870947f6a90e76df46ae15264eb3d07971b5 b18bced93dc57256ec45979081ce6fb7d7b7d1a5 Sim Zhen Quan <<EMAIL>> 1749118055 +0800	checkout: moving from exam-module-changes-v2 to dev
b18bced93dc57256ec45979081ce6fb7d7b7d1a5 b90e99ce757c36e565913ac7f066a91f0f33f51d Sim Zhen Quan <<EMAIL>> 1749118069 +0800	merge exam-module-changes-v2: Merge made by the 'ort' strategy.
b90e99ce757c36e565913ac7f066a91f0f33f51d 63e02574e3e8f6d7299c4acf94c7c19d365d5486 Sim Zhen Quan <<EMAIL>> 1749142894 +0800	commit: Deployed to dev
63e02574e3e8f6d7299c4acf94c7c19d365d5486 b256a94df313192188826a85009a7fbcd5b43b97 Sim Zhen Quan <<EMAIL>> 1749143016 +0800	checkout: moving from dev to main
b256a94df313192188826a85009a7fbcd5b43b97 184846544bd4350e51b93f87e9677376cff9dab0 Sim Zhen Quan <<EMAIL>> 1749143021 +0800	pull: Fast-forward
184846544bd4350e51b93f87e9677376cff9dab0 3ef477c3b379761e6f5ab9b501b037411a3e55bf Sim Zhen Quan <<EMAIL>> 1749174880 +0800	commit: Deployed to prd
3ef477c3b379761e6f5ab9b501b037411a3e55bf 3891980d6276ab79276844e0ee0b70146379b29e Sim Zhen Quan <<EMAIL>> 1749175211 +0800	commit: Bug fix
3891980d6276ab79276844e0ee0b70146379b29e 50a1b341a214ca5fee1117a4e561d9e05fe147d6 Sim Zhen Quan <<EMAIL>> 1749176098 +0800	checkout: moving from main to feature/enrollment-user-login-via-email
50a1b341a214ca5fee1117a4e561d9e05fe147d6 c7404aa6fc86c8c261e571035b000ca069b2e50c Sim Zhen Quan <<EMAIL>> 1749176195 +0800	commit (merge): Merge remote-tracking branch 'origin/enrollment-v2' into feature/enrollment-user-login-via-email
c7404aa6fc86c8c261e571035b000ca069b2e50c 3d215d40ae58b0a0adb0a2a69e9e3cbe79f455a7 Sim Zhen Quan <<EMAIL>> 1749177000 +0800	commit: Reviewed
3d215d40ae58b0a0adb0a2a69e9e3cbe79f455a7 63e02574e3e8f6d7299c4acf94c7c19d365d5486 Sim Zhen Quan <<EMAIL>> 1749177054 +0800	checkout: moving from feature/enrollment-user-login-via-email to dev
63e02574e3e8f6d7299c4acf94c7c19d365d5486 5c9598ec1aa4a8c7a8d84cae2e73b9943a8248ff Sim Zhen Quan <<EMAIL>> 1749177064 +0800	merge origin/enrollment-v2: Merge made by the 'ort' strategy.
5c9598ec1aa4a8c7a8d84cae2e73b9943a8248ff 432e483ee58571b3f284d8ce006a4476bf533d8c Sim Zhen Quan <<EMAIL>> 1749177304 +0800	commit: Deployed to dev
432e483ee58571b3f284d8ce006a4476bf533d8c 1b567825f655907552b8d5d02075467d782e4e76 Sim Zhen Quan <<EMAIL>> 1749177315 +0800	checkout: moving from dev to exam-module-passing-marks
1b567825f655907552b8d5d02075467d782e4e76 ccf10ebc87bafb5f2b4339cd3d4789aa41f87146 Sim Zhen Quan <<EMAIL>> 1749178722 +0800	commit: Added a patch for student grading framework
ccf10ebc87bafb5f2b4339cd3d4789aa41f87146 432e483ee58571b3f284d8ce006a4476bf533d8c Sim Zhen Quan <<EMAIL>> 1749178791 +0800	checkout: moving from exam-module-passing-marks to dev
432e483ee58571b3f284d8ce006a4476bf533d8c a41a6bd472204366a1b3c0eaa5fb6bcf7f377272 Sim Zhen Quan <<EMAIL>> 1749178798 +0800	merge origin/exam-module-passing-marks: Merge made by the 'ort' strategy.
a41a6bd472204366a1b3c0eaa5fb6bcf7f377272 a41a6bd472204366a1b3c0eaa5fb6bcf7f377272 Sim Zhen Quan <<EMAIL>> 1749184851 +0800	reset: moving to HEAD
a41a6bd472204366a1b3c0eaa5fb6bcf7f377272 956f870947f6a90e76df46ae15264eb3d07971b5 Sim Zhen Quan <<EMAIL>> 1749184853 +0800	checkout: moving from dev to exam-module-changes-v2
956f870947f6a90e76df46ae15264eb3d07971b5 e289a9641641cc427ed62489980a26d8a62b122f Sim Zhen Quan <<EMAIL>> 1749184884 +0800	commit: Updated supervisord.conf
e289a9641641cc427ed62489980a26d8a62b122f 70d17e9fc5c84a87e9db13f6b9d625c421995426 Sim Zhen Quan <<EMAIL>> 1749184912 +0800	commit (merge): Merge remote-tracking branch 'origin/exam-module-changes-v2' into exam-module-changes-v2
70d17e9fc5c84a87e9db13f6b9d625c421995426 70d17e9fc5c84a87e9db13f6b9d625c421995426 Sim Zhen Quan <<EMAIL>> 1749184942 +0800	checkout: moving from exam-module-changes-v2 to exam-module-changes-v2
70d17e9fc5c84a87e9db13f6b9d625c421995426 6e2b983d2e77651efe06fa3d7ff22bde7bd1e914 Sim Zhen Quan <<EMAIL>> 1749184963 +0800	merge origin/main: Merge made by the 'ort' strategy.
6e2b983d2e77651efe06fa3d7ff22bde7bd1e914 d0a4529d3f89fd0b39f1f5b872c60893386c2545 Sim Zhen Quan <<EMAIL>> 1749186074 +0800	checkout: moving from exam-module-changes-v2 to enrollment-v2
d0a4529d3f89fd0b39f1f5b872c60893386c2545 17e96e22e5a6a798917c554a34bcc072bf6e3d1b Sim Zhen Quan <<EMAIL>> 1749186082 +0800	pull: Fast-forward
17e96e22e5a6a798917c554a34bcc072bf6e3d1b 6e2b983d2e77651efe06fa3d7ff22bde7bd1e914 Sim Zhen Quan <<EMAIL>> 1749186258 +0800	checkout: moving from enrollment-v2 to exam-module-changes-v2
6e2b983d2e77651efe06fa3d7ff22bde7bd1e914 d4c68b3f136abd1a954210bb880cd5e593921e27 Sim Zhen Quan <<EMAIL>> 1749191583 +0800	commit: Updated report card queue timeout
d4c68b3f136abd1a954210bb880cd5e593921e27 17e96e22e5a6a798917c554a34bcc072bf6e3d1b Sim Zhen Quan <<EMAIL>> 1749191593 +0800	checkout: moving from exam-module-changes-v2 to enrollment-v2
17e96e22e5a6a798917c554a34bcc072bf6e3d1b 0d4d5f0a808ef1772b28fae32ca158e270965077 Sim Zhen Quan <<EMAIL>> 1749192581 +0800	checkout: moving from enrollment-v2 to jira-180-examination-result-by-semester-class
0d4d5f0a808ef1772b28fae32ca158e270965077 415557cf78c5aa30918aa6640ca6a7ad1c964cf2 Sim Zhen Quan <<EMAIL>> 1749192587 +0800	merge origin/main: Merge made by the 'ort' strategy.
415557cf78c5aa30918aa6640ca6a7ad1c964cf2 9d51fc2b0b6daf6c2f756f1e709abe28e49598a8 Sim Zhen Quan <<EMAIL>> 1749193352 +0800	checkout: moving from jira-180-examination-result-by-semester-class to examination-result-by-exam-report
9d51fc2b0b6daf6c2f756f1e709abe28e49598a8 08176ee16648d710438380c7903ff72f0401221b Sim Zhen Quan <<EMAIL>> 1749193372 +0800	merge origin/main: Merge made by the 'ort' strategy.
08176ee16648d710438380c7903ff72f0401221b e410147af75bb6860bd0ef3a51d8ee3fbc21948c Sim Zhen Quan <<EMAIL>> 1749193676 +0800	merge origin/exam-module-changes-v2: Merge made by the 'ort' strategy.
e410147af75bb6860bd0ef3a51d8ee3fbc21948c 7a3d67fc22c83d6329bdc083a5c0c295d0a65286 Sim Zhen Quan <<EMAIL>> 1749196228 +0800	commit: Reviewed
7a3d67fc22c83d6329bdc083a5c0c295d0a65286 0da92a71a294c65a5154de8f80f6be595dd494cf Sim Zhen Quan <<EMAIL>> 1749196468 +0800	checkout: moving from examination-result-by-exam-report to jira-183-exam-result-by-student
0da92a71a294c65a5154de8f80f6be595dd494cf a41a6bd472204366a1b3c0eaa5fb6bcf7f377272 Sim Zhen Quan <<EMAIL>> 1749196507 +0800	checkout: moving from jira-183-exam-result-by-student to dev
a41a6bd472204366a1b3c0eaa5fb6bcf7f377272 341e73c1178e3d3384cb599534b93cb846fc8805 Sim Zhen Quan <<EMAIL>> 1749196604 +0800	commit (merge): Merge remote-tracking branch 'origin/exam-module-changes-v2' into dev
341e73c1178e3d3384cb599534b93cb846fc8805 c4cf0254166bb7ca3ab2ea4ec28adf531f669fbd Sim Zhen Quan <<EMAIL>> 1749197030 +0800	commit: Deployed to dev
c4cf0254166bb7ca3ab2ea4ec28adf531f669fbd 0da92a71a294c65a5154de8f80f6be595dd494cf Sim Zhen Quan <<EMAIL>> 1749197040 +0800	checkout: moving from dev to jira-183-exam-result-by-student
0da92a71a294c65a5154de8f80f6be595dd494cf 4c2bbe340efc2db64b3ae98e0536ae4a18994db6 Sim Zhen Quan <<EMAIL>> 1749197048 +0800	merge origin/main: Merge made by the 'ort' strategy.
4c2bbe340efc2db64b3ae98e0536ae4a18994db6 b767828495c6063b6ea6f2fec339b7aa0bbf8837 Sim Zhen Quan <<EMAIL>> 1749197696 +0800	commit (merge): Merge remote-tracking branch 'origin/exam-module-changes-v2' into jira-183-exam-result-by-student
b767828495c6063b6ea6f2fec339b7aa0bbf8837 5b7b77747720acb4416442daa3516dd27cff73d5 Sim Zhen Quan <<EMAIL>> 1749199896 +0800	commit: Reviewed
5b7b77747720acb4416442daa3516dd27cff73d5 c4cf0254166bb7ca3ab2ea4ec28adf531f669fbd Sim Zhen Quan <<EMAIL>> 1749199946 +0800	checkout: moving from jira-183-exam-result-by-student to dev
c4cf0254166bb7ca3ab2ea4ec28adf531f669fbd 183d66c8487ab3d73761c817917de201ee73fdae Sim Zhen Quan <<EMAIL>> 1749199994 +0800	merge origin/jira-183-exam-result-by-student: Merge made by the 'ort' strategy.
183d66c8487ab3d73761c817917de201ee73fdae 45926c0881e62433b5733b05d750c1f84243f1db Sim Zhen Quan <<EMAIL>> 1749202378 +0800	commit: Deployed to dev
45926c0881e62433b5733b05d750c1f84243f1db 45926c0881e62433b5733b05d750c1f84243f1db Sim Zhen Quan <<EMAIL>> 1749202408 +0800	reset: moving to HEAD
45926c0881e62433b5733b05d750c1f84243f1db d4c68b3f136abd1a954210bb880cd5e593921e27 Sim Zhen Quan <<EMAIL>> 1749202409 +0800	checkout: moving from dev to exam-module-changes-v2
d4c68b3f136abd1a954210bb880cd5e593921e27 4cba4c5c953acab9bb85363b0c725bd931dc822e Sim Zhen Quan <<EMAIL>> 1749202417 +0800	pull: Fast-forward
4cba4c5c953acab9bb85363b0c725bd931dc822e cc8eabe99f3d50a3d420d72d45e7647fe068aa3e Sim Zhen Quan <<EMAIL>> 1749202552 +0800	commit: Bug fix
cc8eabe99f3d50a3d420d72d45e7647fe068aa3e cc8eabe99f3d50a3d420d72d45e7647fe068aa3e Sim Zhen Quan <<EMAIL>> 1749202597 +0800	reset: moving to HEAD
cc8eabe99f3d50a3d420d72d45e7647fe068aa3e 17e96e22e5a6a798917c554a34bcc072bf6e3d1b Sim Zhen Quan <<EMAIL>> 1749202598 +0800	checkout: moving from exam-module-changes-v2 to enrollment-v2
17e96e22e5a6a798917c554a34bcc072bf6e3d1b a7d462ddeb3c3aa9c079456c1bdcbb285de401b5 Sim Zhen Quan <<EMAIL>> 1749203010 +0800	commit: Fix form request
a7d462ddeb3c3aa9c079456c1bdcbb285de401b5 cc8eabe99f3d50a3d420d72d45e7647fe068aa3e Sim Zhen Quan <<EMAIL>> 1749203019 +0800	checkout: moving from enrollment-v2 to exam-module-changes-v2
cc8eabe99f3d50a3d420d72d45e7647fe068aa3e 124d7e3c09cbcd9402d41db883810bea72ac3e98 Sim Zhen Quan <<EMAIL>> 1749203212 +0800	checkout: moving from exam-module-changes-v2 to feature/enrollment-update-marks-API
124d7e3c09cbcd9402d41db883810bea72ac3e98 d27d32ecd16b65007f3b17b75d2a5fca0b59a12d Sim Zhen Quan <<EMAIL>> 1749203253 +0800	commit (merge): Merge remote-tracking branch 'origin/enrollment-v2' into feature/enrollment-update-marks-API
d27d32ecd16b65007f3b17b75d2a5fca0b59a12d 45926c0881e62433b5733b05d750c1f84243f1db Sim Zhen Quan <<EMAIL>> 1749203502 +0800	checkout: moving from feature/enrollment-update-marks-API to dev
45926c0881e62433b5733b05d750c1f84243f1db 45926c0881e62433b5733b05d750c1f84243f1db Sim Zhen Quan <<EMAIL>> 1749204709 +0800	reset: moving to HEAD
45926c0881e62433b5733b05d750c1f84243f1db cc8eabe99f3d50a3d420d72d45e7647fe068aa3e Sim Zhen Quan <<EMAIL>> 1749204712 +0800	checkout: moving from dev to exam-module-changes-v2
cc8eabe99f3d50a3d420d72d45e7647fe068aa3e e08941ab2d58db6a6223428f77f8f7102d1b2e20 Sim Zhen Quan <<EMAIL>> 1749205200 +0800	commit: Route update
e08941ab2d58db6a6223428f77f8f7102d1b2e20 d27d32ecd16b65007f3b17b75d2a5fca0b59a12d Sim Zhen Quan <<EMAIL>> 1749205362 +0800	checkout: moving from exam-module-changes-v2 to feature/enrollment-update-marks-API
d27d32ecd16b65007f3b17b75d2a5fca0b59a12d 833a9cd3e3dd925a56a36296d9613ae98f5eac84 Sim Zhen Quan <<EMAIL>> 1749206398 +0800	commit: Reviewed
833a9cd3e3dd925a56a36296d9613ae98f5eac84 45926c0881e62433b5733b05d750c1f84243f1db Sim Zhen Quan <<EMAIL>> 1749206435 +0800	checkout: moving from feature/enrollment-update-marks-API to dev
45926c0881e62433b5733b05d750c1f84243f1db bcb61f8762bfafbbb5c3d6932141d2575d770877 Sim Zhen Quan <<EMAIL>> 1749206445 +0800	merge exam-module-changes-v2: Merge made by the 'ort' strategy.
bcb61f8762bfafbbb5c3d6932141d2575d770877 467392527dc655a67f342eda375d11cbfa299e63 Sim Zhen Quan <<EMAIL>> 1749206456 +0800	merge enrollment-v2: Merge made by the 'ort' strategy.
467392527dc655a67f342eda375d11cbfa299e63 04312cf50bf5c30f8d6e4f169027afa4b03a4488 Sim Zhen Quan <<EMAIL>> 1749206492 +0800	merge origin/enrollment-v2: Merge made by the 'ort' strategy.
04312cf50bf5c30f8d6e4f169027afa4b03a4488 1fb717853b9cba1a0f2e44760391de29a9d9ee93 Sim Zhen Quan <<EMAIL>> 1749206916 +0800	commit: Deployed to dev
1fb717853b9cba1a0f2e44760391de29a9d9ee93 8bbd8a51633cfdc768950b47f622f02535f15249 Sim Zhen Quan <<EMAIL>> 1749206944 +0800	checkout: moving from dev to feature/enrollment-delete-API
8bbd8a51633cfdc768950b47f622f02535f15249 ac36558587de2e1715829c7a0428a891b9bd9811 Sim Zhen Quan <<EMAIL>> 1749206950 +0800	merge origin/enrollment-v2: Merge made by the 'ort' strategy.
ac36558587de2e1715829c7a0428a891b9bd9811 aad8d1a859653acf493c4fa74c9eba4a0e5a61cb Sim Zhen Quan <<EMAIL>> 1749208196 +0800	commit: Reviewed
aad8d1a859653acf493c4fa74c9eba4a0e5a61cb 1fb717853b9cba1a0f2e44760391de29a9d9ee93 Sim Zhen Quan <<EMAIL>> 1749208205 +0800	checkout: moving from feature/enrollment-delete-API to dev
1fb717853b9cba1a0f2e44760391de29a9d9ee93 1fb717853b9cba1a0f2e44760391de29a9d9ee93 Sim Zhen Quan <<EMAIL>> 1749208465 +0800	reset: moving to HEAD
1fb717853b9cba1a0f2e44760391de29a9d9ee93 585c373e9abbf4adff0241e434895f2a6dead323 Sim Zhen Quan <<EMAIL>> 1749208559 +0800	merge origin/enrollment-v2: Merge made by the 'ort' strategy.
585c373e9abbf4adff0241e434895f2a6dead323 12a7452f34e6c8e1c9558de8f6e0d02f1930bf61 Sim Zhen Quan <<EMAIL>> 1749208932 +0800	commit: Deployed to dev
12a7452f34e6c8e1c9558de8f6e0d02f1930bf61 e08941ab2d58db6a6223428f77f8f7102d1b2e20 Sim Zhen Quan <<EMAIL>> 1749208968 +0800	checkout: moving from dev to exam-module-changes-v2
e08941ab2d58db6a6223428f77f8f7102d1b2e20 5e6c9d704a3ff56eef1c15fb5e94fd5bc413449d Sim Zhen Quan <<EMAIL>> 1749208982 +0800	commit: Fix excel generate error
5e6c9d704a3ff56eef1c15fb5e94fd5bc413449d 098ab02355cd8a7c356486e2cf0d8beeea4c6511 Sim Zhen Quan <<EMAIL>> 1749231886 +0800	checkout: moving from exam-module-changes-v2 to feature/manual-payment-enrollment
098ab02355cd8a7c356486e2cf0d8beeea4c6511 7733914c9f10f564c5c4b7be7514cf2b96bd4498 Sim Zhen Quan <<EMAIL>> 1749231893 +0800	merge origin/enrollment-v2: Merge made by the 'ort' strategy.
7733914c9f10f564c5c4b7be7514cf2b96bd4498 c160a27028b3faafcafd69a22e591ef84e3c81d7 Sim Zhen Quan <<EMAIL>> 1749279790 +0800	checkout: moving from feature/manual-payment-enrollment to feature/enrollment-feedback
c160a27028b3faafcafd69a22e591ef84e3c81d7 2f20ddb9c310d36a7207894bf7cbf945564afe08 Sim Zhen Quan <<EMAIL>> 1749280171 +0800	commit (merge): Merge remote-tracking branch 'origin/enrollment-v2' into feature/enrollment-feedback
2f20ddb9c310d36a7207894bf7cbf945564afe08 cf03a60ef01b0b435687f34085e0b73466eaef5e Sim Zhen Quan <<EMAIL>> 1749281380 +0800	commit: Reviewed
cf03a60ef01b0b435687f34085e0b73466eaef5e 2c54e08bbba99dfbaa2cd60fc5daafd7cb536734 Sim Zhen Quan <<EMAIL>> 1749281415 +0800	checkout: moving from feature/enrollment-feedback to feature/enrollment-feedback-2
2c54e08bbba99dfbaa2cd60fc5daafd7cb536734 fd8807f5fa3bb8dc4fccac9711bfa50826417163 Sim Zhen Quan <<EMAIL>> 1749281490 +0800	commit (merge): Merge remote-tracking branch 'origin/enrollment-v2' into feature/enrollment-feedback-2
fd8807f5fa3bb8dc4fccac9711bfa50826417163 dc89d72894814c9a87b13144b0fdcd9c723ecb09 Sim Zhen Quan <<EMAIL>> 1749282590 +0800	commit: Reviewed
dc89d72894814c9a87b13144b0fdcd9c723ecb09 53dda110bf3fd307f7fb6f377a54650f922ae6cc Sim Zhen Quan <<EMAIL>> 1749282620 +0800	commit: Reviewed
53dda110bf3fd307f7fb6f377a54650f922ae6cc a7d462ddeb3c3aa9c079456c1bdcbb285de401b5 Sim Zhen Quan <<EMAIL>> 1749282771 +0800	checkout: moving from feature/enrollment-feedback-2 to enrollment-v2
a7d462ddeb3c3aa9c079456c1bdcbb285de401b5 df4163d24aa7c0b321b0623221f64d0743dce1d3 Sim Zhen Quan <<EMAIL>> 1749282776 +0800	pull: Fast-forward
df4163d24aa7c0b321b0623221f64d0743dce1d3 3cd6e4831e6f9b28484c38b6f2c182a9fa42df9e Sim Zhen Quan <<EMAIL>> 1749282781 +0800	merge origin/feature/enrollment-feedback-2: Merge made by the 'ort' strategy.
3cd6e4831e6f9b28484c38b6f2c182a9fa42df9e 12a7452f34e6c8e1c9558de8f6e0d02f1930bf61 Sim Zhen Quan <<EMAIL>> 1749283035 +0800	checkout: moving from enrollment-v2 to dev
12a7452f34e6c8e1c9558de8f6e0d02f1930bf61 c64a74e7b13c9a91e466c3916205e11c9e1d28cb Sim Zhen Quan <<EMAIL>> 1749283048 +0800	merge origin/enrollment-v2: Merge made by the 'ort' strategy.
c64a74e7b13c9a91e466c3916205e11c9e1d28cb 40c0e80ed95e2dbdeb0fa4b1f02c426447f13867 Sim Zhen Quan <<EMAIL>> 1749308650 +0800	commit: Deployed to dev
40c0e80ed95e2dbdeb0fa4b1f02c426447f13867 3cd6e4831e6f9b28484c38b6f2c182a9fa42df9e Sim Zhen Quan <<EMAIL>> 1749308689 +0800	checkout: moving from dev to enrollment-v2
3cd6e4831e6f9b28484c38b6f2c182a9fa42df9e c4f6a96edf31bfadf5360a5a51c026613d05a638 Sim Zhen Quan <<EMAIL>> 1749308701 +0800	commit: Enhancement
c4f6a96edf31bfadf5360a5a51c026613d05a638 40c0e80ed95e2dbdeb0fa4b1f02c426447f13867 Sim Zhen Quan <<EMAIL>> 1749308714 +0800	checkout: moving from enrollment-v2 to dev
40c0e80ed95e2dbdeb0fa4b1f02c426447f13867 bc71f6affc2f2c9756f899482d40ca0795fd2c12 Sim Zhen Quan <<EMAIL>> 1749308720 +0800	merge origin/enrollment-v2: Merge made by the 'ort' strategy.
bc71f6affc2f2c9756f899482d40ca0795fd2c12 c4f6a96edf31bfadf5360a5a51c026613d05a638 Sim Zhen Quan <<EMAIL>> 1749372603 +0800	checkout: moving from dev to enrollment-v2
c4f6a96edf31bfadf5360a5a51c026613d05a638 c8f44ad752ca4130999d85e854a558b09bf683a9 Sim Zhen Quan <<EMAIL>> 1749372674 +0800	commit: Added more filters
c8f44ad752ca4130999d85e854a558b09bf683a9 bc71f6affc2f2c9756f899482d40ca0795fd2c12 Sim Zhen Quan <<EMAIL>> 1749372679 +0800	checkout: moving from enrollment-v2 to dev
bc71f6affc2f2c9756f899482d40ca0795fd2c12 40edf440dcadcd40b36aefd9620689a49d4ac480 Sim Zhen Quan <<EMAIL>> 1749372694 +0800	merge origin/enrollment-v2: Merge made by the 'ort' strategy.
40edf440dcadcd40b36aefd9620689a49d4ac480 c8f44ad752ca4130999d85e854a558b09bf683a9 Sim Zhen Quan <<EMAIL>> 1749434559 +0800	checkout: moving from dev to enrollment-v2
c8f44ad752ca4130999d85e854a558b09bf683a9 a5da7c1e6c25a72c0df212bb5e55e131ee3145a7 Sim Zhen Quan <<EMAIL>> 1749434588 +0800	commit: Validation enhancements
a5da7c1e6c25a72c0df212bb5e55e131ee3145a7 e028af692fd9dbc925c99cad05eb546777a18d41 Sim Zhen Quan <<EMAIL>> 1749435198 +0800	commit: Added sms notification
e028af692fd9dbc925c99cad05eb546777a18d41 40edf440dcadcd40b36aefd9620689a49d4ac480 Sim Zhen Quan <<EMAIL>> 1749435203 +0800	checkout: moving from enrollment-v2 to dev
40edf440dcadcd40b36aefd9620689a49d4ac480 a897961f7b7e4a763b6a9f47cb3afb27b9d58fd4 Sim Zhen Quan <<EMAIL>> 1749435208 +0800	merge origin/enrollment-v2: Merge made by the 'ort' strategy.
a897961f7b7e4a763b6a9f47cb3afb27b9d58fd4 4ab7f2c2937c563f5425f230b2033c55e8ba6b79 Sim Zhen Quan <<EMAIL>> 1749436675 +0800	checkout: moving from dev to api/duplicate-semester
4ab7f2c2937c563f5425f230b2033c55e8ba6b79 276e433b6af41f19567bf0dca6105dc29706c486 Sim Zhen Quan <<EMAIL>> 1749436680 +0800	pull: Fast-forward
276e433b6af41f19567bf0dca6105dc29706c486 cd33dad06336e66545ad211011780a8f33fe71be Sim Zhen Quan <<EMAIL>> 1749436681 +0800	merge origin/main: Merge made by the 'ort' strategy.
cd33dad06336e66545ad211011780a8f33fe71be a897961f7b7e4a763b6a9f47cb3afb27b9d58fd4 Sim Zhen Quan <<EMAIL>> 1749439593 +0800	checkout: moving from api/duplicate-semester to dev
a897961f7b7e4a763b6a9f47cb3afb27b9d58fd4 ce6182c9a78f71ad636114e600979165b6b75a2f Sim Zhen Quan <<EMAIL>> 1749439625 +0800	commit (merge): Merge branch 'api/duplicate-semester' into dev
ce6182c9a78f71ad636114e600979165b6b75a2f cd33dad06336e66545ad211011780a8f33fe71be Sim Zhen Quan <<EMAIL>> 1749447432 +0800	checkout: moving from dev to api/duplicate-semester
cd33dad06336e66545ad211011780a8f33fe71be 889e2b76262f52faf39b2059ef6b9805ef317d87 Sim Zhen Quan <<EMAIL>> 1749447437 +0800	pull: Fast-forward
889e2b76262f52faf39b2059ef6b9805ef317d87 4dcbb3b315391cb216d6d9c734fb7d6d65d7c09b Sim Zhen Quan <<EMAIL>> 1749451155 +0800	commit: Added semester setting to a batch
4dcbb3b315391cb216d6d9c734fb7d6d65d7c09b ab7f18147ea4431132acd5d091803192c96df022 Sim Zhen Quan <<EMAIL>> 1749451168 +0800	commit: Added semester setting to a batch
ab7f18147ea4431132acd5d091803192c96df022 b590a1a1414f9c7abc5d8dedc9b0041df1fddef2 Sim Zhen Quan <<EMAIL>> 1749451243 +0800	commit: Fix resource name
b590a1a1414f9c7abc5d8dedc9b0041df1fddef2 ce6182c9a78f71ad636114e600979165b6b75a2f Sim Zhen Quan <<EMAIL>> 1749451427 +0800	checkout: moving from api/duplicate-semester to dev
ce6182c9a78f71ad636114e600979165b6b75a2f 9f42083eae78fc49c1b7474ec34a04a1c784f7a4 Sim Zhen Quan <<EMAIL>> 1749451488 +0800	commit (merge): Merge branch 'api/duplicate-semester' into dev
9f42083eae78fc49c1b7474ec34a04a1c784f7a4 14fcd08402b780cf4a640fa35d54cee692b359aa Sim Zhen Quan <<EMAIL>> 1749454782 +0800	commit: Deployed to dev
14fcd08402b780cf4a640fa35d54cee692b359aa e028af692fd9dbc925c99cad05eb546777a18d41 Sim Zhen Quan <<EMAIL>> 1749454794 +0800	checkout: moving from dev to enrollment-v2
e028af692fd9dbc925c99cad05eb546777a18d41 82bef38e0ce58813182cd5a019c0ddbe2cc25445 Sim Zhen Quan <<EMAIL>> 1749455855 +0800	commit: Deployed to dev
82bef38e0ce58813182cd5a019c0ddbe2cc25445 14fcd08402b780cf4a640fa35d54cee692b359aa Sim Zhen Quan <<EMAIL>> 1749455864 +0800	checkout: moving from enrollment-v2 to dev
14fcd08402b780cf4a640fa35d54cee692b359aa 82bef38e0ce58813182cd5a019c0ddbe2cc25445 Sim Zhen Quan <<EMAIL>> 1749455896 +0800	checkout: moving from dev to enrollment-v2
82bef38e0ce58813182cd5a019c0ddbe2cc25445 14fcd08402b780cf4a640fa35d54cee692b359aa Sim Zhen Quan <<EMAIL>> 1749455907 +0800	checkout: moving from enrollment-v2 to dev
14fcd08402b780cf4a640fa35d54cee692b359aa 08a253143491acd98d05b76b68c610fbaf51c32e Sim Zhen Quan <<EMAIL>> 1749455910 +0800	merge origin/enrollment-v2: Merge made by the 'ort' strategy.
08a253143491acd98d05b76b68c610fbaf51c32e 095b4c1cb2fe7e49a9ae742727d87d717af7f12a Sim Zhen Quan <<EMAIL>> 1749461896 +0800	commit: Deployed to dev
095b4c1cb2fe7e49a9ae742727d87d717af7f12a 82bef38e0ce58813182cd5a019c0ddbe2cc25445 Sim Zhen Quan <<EMAIL>> 1749461903 +0800	checkout: moving from dev to enrollment-v2
82bef38e0ce58813182cd5a019c0ddbe2cc25445 1da8a7d5f70d963c2f44d3bcb34ccd7718a55428 Sim Zhen Quan <<EMAIL>> 1749462019 +0800	commit: Fix guardian phone number validation
1da8a7d5f70d963c2f44d3bcb34ccd7718a55428 3891980d6276ab79276844e0ee0b70146379b29e Sim Zhen Quan <<EMAIL>> 1749462109 +0800	checkout: moving from enrollment-v2 to main
3891980d6276ab79276844e0ee0b70146379b29e 1a2e72a6b5c743da6749385536a3c82bd6dc8960 Sim Zhen Quan <<EMAIL>> 1749462122 +0800	commit: Fix active flag
1a2e72a6b5c743da6749385536a3c82bd6dc8960 05da7d6b6947e62e17bc4acf558183bbc5187182 Sim Zhen Quan <<EMAIL>> 1749462137 +0800	checkout: moving from main to fix/unique-validation-for-exam-slip
05da7d6b6947e62e17bc4acf558183bbc5187182 78f8562c070eeabbb18e160c96e798fbdd1bb5b9 Sim Zhen Quan <<EMAIL>> 1749462148 +0800	merge origin/enrollment-v2: Merge made by the 'ort' strategy.
78f8562c070eeabbb18e160c96e798fbdd1bb5b9 1da8a7d5f70d963c2f44d3bcb34ccd7718a55428 Sim Zhen Quan <<EMAIL>> 1749462371 +0800	checkout: moving from fix/unique-validation-for-exam-slip to enrollment-v2
1da8a7d5f70d963c2f44d3bcb34ccd7718a55428 79756e607cef033594ad3c57d359433404a3e0c3 Sim Zhen Quan <<EMAIL>> 1749462377 +0800	pull: Fast-forward
79756e607cef033594ad3c57d359433404a3e0c3 095b4c1cb2fe7e49a9ae742727d87d717af7f12a Sim Zhen Quan <<EMAIL>> 1749462377 +0800	checkout: moving from enrollment-v2 to dev
095b4c1cb2fe7e49a9ae742727d87d717af7f12a c15c061e2ec9f185f57c806da44c95cc4184c8c9 Sim Zhen Quan <<EMAIL>> 1749462401 +0800	merge origin/enrollment-v2: Merge made by the 'ort' strategy.
c15c061e2ec9f185f57c806da44c95cc4184c8c9 b590a1a1414f9c7abc5d8dedc9b0041df1fddef2 Sim Zhen Quan <<EMAIL>> 1749462705 +0800	checkout: moving from dev to api/duplicate-semester
b590a1a1414f9c7abc5d8dedc9b0041df1fddef2 be8bcf8f1e693cdab18d78399cdebae32706426f Sim Zhen Quan <<EMAIL>> 1749462711 +0800	pull: Fast-forward
be8bcf8f1e693cdab18d78399cdebae32706426f 447a0a3e522cb5cc90703836cda5b0312a8b73a5 Sim Zhen Quan <<EMAIL>> 1749462788 +0800	pull: Fast-forward
447a0a3e522cb5cc90703836cda5b0312a8b73a5 447a0a3e522cb5cc90703836cda5b0312a8b73a5 Sim Zhen Quan <<EMAIL>> 1749463792 +0800	reset: moving to HEAD
447a0a3e522cb5cc90703836cda5b0312a8b73a5 1a2e72a6b5c743da6749385536a3c82bd6dc8960 Sim Zhen Quan <<EMAIL>> 1749463793 +0800	checkout: moving from api/duplicate-semester to main
1a2e72a6b5c743da6749385536a3c82bd6dc8960 23bab7c6ed31b2ebfd7d3fe85311396eb0036b63 Sim Zhen Quan <<EMAIL>> 1749463799 +0800	pull: Fast-forward
23bab7c6ed31b2ebfd7d3fe85311396eb0036b63 358843091ccb57a9032bac1ef3d29dcf5bd60b46 Sim Zhen Quan <<EMAIL>> 1749463816 +0800	commit: Remove test code
358843091ccb57a9032bac1ef3d29dcf5bd60b46 79756e607cef033594ad3c57d359433404a3e0c3 Sim Zhen Quan <<EMAIL>> 1749463939 +0800	checkout: moving from main to enrollment-v2
79756e607cef033594ad3c57d359433404a3e0c3 538e8ede7fa84b81289a5a8330d919e565b19039 Sim Zhen Quan <<EMAIL>> 1749463974 +0800	commit (merge): Merge remote-tracking branch 'origin/main' into enrollment-v2
538e8ede7fa84b81289a5a8330d919e565b19039 c95cd663ecca602ab5537aa625e10bab30bd259d Sim Zhen Quan <<EMAIL>> 1749464483 +0800	commit: Remove unused migration
c95cd663ecca602ab5537aa625e10bab30bd259d 987971fea1d642bb62a868ec3266ce2534529ec2 Sim Zhen Quan <<EMAIL>> 1749464751 +0800	commit: Added distinct to email and phone_number
987971fea1d642bb62a868ec3266ce2534529ec2 c15c061e2ec9f185f57c806da44c95cc4184c8c9 Sim Zhen Quan <<EMAIL>> 1749464764 +0800	checkout: moving from enrollment-v2 to dev
c15c061e2ec9f185f57c806da44c95cc4184c8c9 46846cfe4207adab919666d3cf2a67f674d52b71 Sim Zhen Quan <<EMAIL>> 1749464770 +0800	merge origin/main: Merge made by the 'ort' strategy.
46846cfe4207adab919666d3cf2a67f674d52b71 25ce66723142fd50be91f6a24450ba4233d1cca5 Sim Zhen Quan <<EMAIL>> 1749464779 +0800	merge origin/enrollment-v2: Merge made by the 'ort' strategy.
25ce66723142fd50be91f6a24450ba4233d1cca5 358843091ccb57a9032bac1ef3d29dcf5bd60b46 Sim Zhen Quan <<EMAIL>> 1749465794 +0800	checkout: moving from dev to main
358843091ccb57a9032bac1ef3d29dcf5bd60b46 0f56c1589ee8a2056430fbbc0657e71e788beca3 Sim Zhen Quan <<EMAIL>> 1749465799 +0800	pull: Fast-forward
0f56c1589ee8a2056430fbbc0657e71e788beca3 7805ca3e1b691b98b44ac66ae71f9b4a84229349 Sim Zhen Quan <<EMAIL>> 1749523760 +0800	commit: Deployed to prd
7805ca3e1b691b98b44ac66ae71f9b4a84229349 a95f0daa16ff8b0acce05c9082f63f13427aed5b Sim Zhen Quan <<EMAIL>> 1749523972 +0800	commit: Move job trigger to the correct place
a95f0daa16ff8b0acce05c9082f63f13427aed5b 415557cf78c5aa30918aa6640ca6a7ad1c964cf2 Sim Zhen Quan <<EMAIL>> 1749543042 +0800	checkout: moving from main to jira-180-examination-result-by-semester-class
415557cf78c5aa30918aa6640ca6a7ad1c964cf2 5e6c9d704a3ff56eef1c15fb5e94fd5bc413449d Sim Zhen Quan <<EMAIL>> 1749543065 +0800	checkout: moving from jira-180-examination-result-by-semester-class to exam-module-changes-v2
5e6c9d704a3ff56eef1c15fb5e94fd5bc413449d 1a13da4be9c56f556d48d51236e5c4fceefced14 Sim Zhen Quan <<EMAIL>> 1749543681 +0800	commit (merge): Merge remote-tracking branch 'origin/main' into exam-module-changes-v2
1a13da4be9c56f556d48d51236e5c4fceefced14 a95f0daa16ff8b0acce05c9082f63f13427aed5b Sim Zhen Quan <<EMAIL>> 1749543797 +0800	checkout: moving from exam-module-changes-v2 to main
a95f0daa16ff8b0acce05c9082f63f13427aed5b a95f0daa16ff8b0acce05c9082f63f13427aed5b Sim Zhen Quan <<EMAIL>> 1749543807 +0800	checkout: moving from main to staging/2025-06-10
a95f0daa16ff8b0acce05c9082f63f13427aed5b e6423294b65ad1444a8005eac9487add63276195 Sim Zhen Quan <<EMAIL>> 1749544899 +0800	checkout: moving from staging/2025-06-10 to enrollment-student-report-new-exam-bands
e6423294b65ad1444a8005eac9487add63276195 2502d13fe3e84c1cf4a84dac9379ba9ee4f81ff3 Sim Zhen Quan <<EMAIL>> 1749544905 +0800	merge origin/main: Merge made by the 'ort' strategy.
2502d13fe3e84c1cf4a84dac9379ba9ee4f81ff3 f4eeee6ad14483143f8a62afb365e78e54eb7f83 Sim Zhen Quan <<EMAIL>> 1749545387 +0800	commit: Fix test case
f4eeee6ad14483143f8a62afb365e78e54eb7f83 4d48ccca9ac383b33b8ba2c5e44cb83bb18054e4 Sim Zhen Quan <<EMAIL>> 1749545656 +0800	checkout: moving from enrollment-student-report-new-exam-bands to exam-report-card-format-changes
4d48ccca9ac383b33b8ba2c5e44cb83bb18054e4 ca99b5257ea435ab5eae12acacc96e220d29620d Sim Zhen Quan <<EMAIL>> 1749545662 +0800	merge exam-module-changes-v2: Merge made by the 'ort' strategy.
ca99b5257ea435ab5eae12acacc96e220d29620d 25ce66723142fd50be91f6a24450ba4233d1cca5 Sim Zhen Quan <<EMAIL>> 1749546701 +0800	checkout: moving from exam-report-card-format-changes to dev
25ce66723142fd50be91f6a24450ba4233d1cca5 8351807d88619ad549669efee7f8c421d212f337 Sim Zhen Quan <<EMAIL>> 1749546707 +0800	merge origin/exam-module-changes-v2: Merge made by the 'ort' strategy.
8351807d88619ad549669efee7f8c421d212f337 64c383e7c7076e9aca307853cf3e0344d17d5213 Sim Zhen Quan <<EMAIL>> 1749547081 +0800	commit: Deployed to dev
64c383e7c7076e9aca307853cf3e0344d17d5213 415557cf78c5aa30918aa6640ca6a7ad1c964cf2 Sim Zhen Quan <<EMAIL>> 1749548684 +0800	checkout: moving from dev to jira-180-examination-result-by-semester-class
415557cf78c5aa30918aa6640ca6a7ad1c964cf2 cf8d0f413d94efb296afd23f1cf96c6e03bbdbdf Sim Zhen Quan <<EMAIL>> 1749549097 +0800	commit (merge): Merge remote-tracking branch 'origin/exam-module-changes-v2' into jira-180-examination-result-by-semester-class
cf8d0f413d94efb296afd23f1cf96c6e03bbdbdf 3c79d019a0535fb3b33b95bb9e5f3cfb1ae0e6ba Sim Zhen Quan <<EMAIL>> 1749550034 +0800	commit: Review WIP
3c79d019a0535fb3b33b95bb9e5f3cfb1ae0e6ba 64c383e7c7076e9aca307853cf3e0344d17d5213 Sim Zhen Quan <<EMAIL>> 1749550041 +0800	checkout: moving from jira-180-examination-result-by-semester-class to dev
64c383e7c7076e9aca307853cf3e0344d17d5213 9c3ecee9e3024b60d3a4480ef9f54e1589deced7 Sim Zhen Quan <<EMAIL>> 1749550095 +0800	commit (merge): Merge remote-tracking branch 'origin/jira-180-examination-result-by-semester-class' into dev
9c3ecee9e3024b60d3a4480ef9f54e1589deced7 1b916f39bac8f3597cb6761667e73baa029f3175 Sim Zhen Quan <<EMAIL>> 1749574635 +0800	commit: Deployed to dev
1b916f39bac8f3597cb6761667e73baa029f3175 a95f0daa16ff8b0acce05c9082f63f13427aed5b Sim Zhen Quan <<EMAIL>> 1749574640 +0800	checkout: moving from dev to main
a95f0daa16ff8b0acce05c9082f63f13427aed5b add5ae6c345bbfc033d19dc7f0833ecee0ec3548 Sim Zhen Quan <<EMAIL>> 1749574651 +0800	pull: Fast-forward
add5ae6c345bbfc033d19dc7f0833ecee0ec3548 49f07d1bc91d428d712fa9ac66206f1215791f72 Sim Zhen Quan <<EMAIL>> 1749619293 +0800	commit: Deployed to prd
49f07d1bc91d428d712fa9ac66206f1215791f72 8de7e3c8495c5d76d874ca5514233c14b0eff338 Sim Zhen Quan <<EMAIL>> 1749627114 +0800	commit: Added enrollment mail
8de7e3c8495c5d76d874ca5514233c14b0eff338 1b916f39bac8f3597cb6761667e73baa029f3175 Sim Zhen Quan <<EMAIL>> 1749627208 +0800	checkout: moving from main to dev
1b916f39bac8f3597cb6761667e73baa029f3175 75e840256e60a1c889974fdba957f8edc628bb35 Sim Zhen Quan <<EMAIL>> 1749627223 +0800	merge origin/main: Merge made by the 'ort' strategy.
75e840256e60a1c889974fdba957f8edc628bb35 3c79d019a0535fb3b33b95bb9e5f3cfb1ae0e6ba Sim Zhen Quan <<EMAIL>> 1749635252 +0800	checkout: moving from dev to jira-180-examination-result-by-semester-class
3c79d019a0535fb3b33b95bb9e5f3cfb1ae0e6ba eb6a3a35315782ff31577f8c28825be1a78f44ca Sim Zhen Quan <<EMAIL>> 1749635261 +0800	pull: Fast-forward
eb6a3a35315782ff31577f8c28825be1a78f44ca 609d68635b76aa851ae6a538891d0198bdf163fb Sim Zhen Quan <<EMAIL>> 1749635262 +0800	merge origin/main: Merge made by the 'ort' strategy.
609d68635b76aa851ae6a538891d0198bdf163fb 75e840256e60a1c889974fdba957f8edc628bb35 Sim Zhen Quan <<EMAIL>> 1749637122 +0800	checkout: moving from jira-180-examination-result-by-semester-class to dev
75e840256e60a1c889974fdba957f8edc628bb35 8972e0e67a9fa5b8066b99929cb7a93d56537e1d Sim Zhen Quan <<EMAIL>> 1749637129 +0800	merge origin/main: Merge made by the 'ort' strategy.
8972e0e67a9fa5b8066b99929cb7a93d56537e1d 178cf05b518b3b62a9daa49fe783cccc98688713 Sim Zhen Quan <<EMAIL>> 1749637142 +0800	merge jira-180-examination-result-by-semester-class: Merge made by the 'ort' strategy.
178cf05b518b3b62a9daa49fe783cccc98688713 609d68635b76aa851ae6a538891d0198bdf163fb Sim Zhen Quan <<EMAIL>> 1749665694 +0800	checkout: moving from dev to jira-180-examination-result-by-semester-class
609d68635b76aa851ae6a538891d0198bdf163fb fc7831aa263ab9cf39cbb1f10be13c5defcde951 Sim Zhen Quan <<EMAIL>> 1749666141 +0800	commit: Fix report formatting
fc7831aa263ab9cf39cbb1f10be13c5defcde951 8de7e3c8495c5d76d874ca5514233c14b0eff338 Sim Zhen Quan <<EMAIL>> 1749666227 +0800	checkout: moving from jira-180-examination-result-by-semester-class to main
8de7e3c8495c5d76d874ca5514233c14b0eff338 e68ead3f9f3ca54a70c021aa141286fcb9d38dcd Sim Zhen Quan <<EMAIL>> 1749666232 +0800	pull: Fast-forward
e68ead3f9f3ca54a70c021aa141286fcb9d38dcd a1ad7b0cb2a2e3df0e19578ac69b558ed2c67c37 Sim Zhen Quan <<EMAIL>> 1749718576 +0800	checkout: moving from main to feature/get-summary-API
a1ad7b0cb2a2e3df0e19578ac69b558ed2c67c37 e68ead3f9f3ca54a70c021aa141286fcb9d38dcd Sim Zhen Quan <<EMAIL>> 1749718581 +0800	checkout: moving from feature/get-summary-API to main
e68ead3f9f3ca54a70c021aa141286fcb9d38dcd 4f2e8b8d54f334bf21d2513339eb0335a68812ff Sim Zhen Quan <<EMAIL>> 1749718591 +0800	commit: Deployed to prd
4f2e8b8d54f334bf21d2513339eb0335a68812ff 5a10516379599ac547002541ee6f17f75f8e0ef0 Sim Zhen Quan <<EMAIL>> 1749718646 +0800	commit: Fix google form link
5a10516379599ac547002541ee6f17f75f8e0ef0 a1ad7b0cb2a2e3df0e19578ac69b558ed2c67c37 Sim Zhen Quan <<EMAIL>> 1749718654 +0800	checkout: moving from main to feature/get-summary-API
a1ad7b0cb2a2e3df0e19578ac69b558ed2c67c37 773306ac8280a371c2ab19ccfd76bc6011669dce Sim Zhen Quan <<EMAIL>> 1749718660 +0800	merge origin/main: Merge made by the 'ort' strategy.
773306ac8280a371c2ab19ccfd76bc6011669dce 19358a21b74e1d1e828b10de4f0b1ff758278066 Sim Zhen Quan <<EMAIL>> 1749721764 +0800	commit: Added summary into show api
19358a21b74e1d1e828b10de4f0b1ff758278066 178cf05b518b3b62a9daa49fe783cccc98688713 Sim Zhen Quan <<EMAIL>> 1749721829 +0800	checkout: moving from feature/get-summary-API to dev
178cf05b518b3b62a9daa49fe783cccc98688713 e5bd3e7ba856af267704a720d8acfdbdd01a1814 Sim Zhen Quan <<EMAIL>> 1749721839 +0800	merge feature/get-summary-API: Merge made by the 'ort' strategy.
e5bd3e7ba856af267704a720d8acfdbdd01a1814 227423700d400fb0c57d03bfd63c2e7fe8695770 Sim Zhen Quan <<EMAIL>> 1749745508 +0800	commit: Deployed to dev
227423700d400fb0c57d03bfd63c2e7fe8695770 19358a21b74e1d1e828b10de4f0b1ff758278066 Sim Zhen Quan <<EMAIL>> 1749745518 +0800	checkout: moving from dev to feature/get-summary-API
19358a21b74e1d1e828b10de4f0b1ff758278066 f10fd1fb23353a6ac2eab9fef65343c1d1096fc9 Sim Zhen Quan <<EMAIL>> 1749746826 +0800	commit: Reviewed
f10fd1fb23353a6ac2eab9fef65343c1d1096fc9 84c419bb1d684aeddd631083a04adf56fe238708 Sim Zhen Quan <<EMAIL>> 1749747014 +0800	checkout: moving from feature/get-summary-API to added-with-position-in-standard-to-examination-result-by-class-report
84c419bb1d684aeddd631083a04adf56fe238708 265aec35a06108fca4cbccee4908c8f009b04fc2 Sim Zhen Quan <<EMAIL>> 1749747022 +0800	merge origin/main: Merge made by the 'ort' strategy.
265aec35a06108fca4cbccee4908c8f009b04fc2 c5e452cf4ca824b339780db8f81da5299ce8d331 Sim Zhen Quan <<EMAIL>> 1749747667 +0800	commit: Reviewed
c5e452cf4ca824b339780db8f81da5299ce8d331 5a10516379599ac547002541ee6f17f75f8e0ef0 Sim Zhen Quan <<EMAIL>> 1749748178 +0800	checkout: moving from added-with-position-in-standard-to-examination-result-by-class-report to main
5a10516379599ac547002541ee6f17f75f8e0ef0 607c9fe00a1f2de8da3e4917dca5c18d52e3b971 Sim Zhen Quan <<EMAIL>> 1749748183 +0800	pull: Fast-forward
607c9fe00a1f2de8da3e4917dca5c18d52e3b971 efc505a00171bb55900d3e63a9f044ad84e7b413 Sim Zhen Quan <<EMAIL>> 1749748453 +0800	commit: Deployed to prd
efc505a00171bb55900d3e63a9f044ad84e7b413 efc505a00171bb55900d3e63a9f044ad84e7b413 Sim Zhen Quan <<EMAIL>> 1749748475 +0800	checkout: moving from main to ai-rnd
efc505a00171bb55900d3e63a9f044ad84e7b413 a6d8d71fa904c115494403b73e69fdea3ad0adab Sim Zhen Quan <<EMAIL>> 1749792272 +0800	commit: Testing
a6d8d71fa904c115494403b73e69fdea3ad0adab efc505a00171bb55900d3e63a9f044ad84e7b413 Sim Zhen Quan <<EMAIL>> 1749792334 +0800	checkout: moving from ai-rnd to main
efc505a00171bb55900d3e63a9f044ad84e7b413 efc505a00171bb55900d3e63a9f044ad84e7b413 Sim Zhen Quan <<EMAIL>> 1749792872 +0800	reset: moving to HEAD
efc505a00171bb55900d3e63a9f044ad84e7b413 35b7acf60aa6a216bb62eee1df5ae209ffc5affe Sim Zhen Quan <<EMAIL>> 1749792873 +0800	checkout: moving from main to feature/enrollment-siblings-update
35b7acf60aa6a216bb62eee1df5ae209ffc5affe fed8daa2308d8daa474ea2ee9db8ae246c8a312c Sim Zhen Quan <<EMAIL>> 1749795995 +0800	commit: Fixed test cases
fed8daa2308d8daa474ea2ee9db8ae246c8a312c 227423700d400fb0c57d03bfd63c2e7fe8695770 Sim Zhen Quan <<EMAIL>> 1749796037 +0800	checkout: moving from feature/enrollment-siblings-update to dev
227423700d400fb0c57d03bfd63c2e7fe8695770 991cfb68bbdee18e1008b324dd0dfd49c79ba87b Sim Zhen Quan <<EMAIL>> 1749796090 +0800	commit (merge): Merge branch 'feature/enrollment-siblings-update' into dev
991cfb68bbdee18e1008b324dd0dfd49c79ba87b 84c4ca4ce3c26bbc2321ff1b203926a7307982ce Sim Zhen Quan <<EMAIL>> 1749796339 +0800	commit: Deployed to dev
84c4ca4ce3c26bbc2321ff1b203926a7307982ce 66707ea09dae6b2933655e1f9cc13300d1b9b342 Sim Zhen Quan <<EMAIL>> 1749796641 +0800	checkout: moving from dev to exam-result-data-entry-return-id-during-save-error
66707ea09dae6b2933655e1f9cc13300d1b9b342 66707ea09dae6b2933655e1f9cc13300d1b9b342 Sim Zhen Quan <<EMAIL>> 1749796721 +0800	checkout: moving from exam-result-data-entry-return-id-during-save-error to exam-result-data-entry-return-id-during-save-error
66707ea09dae6b2933655e1f9cc13300d1b9b342 9f26b753cbac60f91cc160b4587ed409cda243e1 Sim Zhen Quan <<EMAIL>> 1749796725 +0800	merge origin/main: Merge made by the 'ort' strategy.
9f26b753cbac60f91cc160b4587ed409cda243e1 84c4ca4ce3c26bbc2321ff1b203926a7307982ce Sim Zhen Quan <<EMAIL>> 1749796909 +0800	checkout: moving from exam-result-data-entry-return-id-during-save-error to dev
84c4ca4ce3c26bbc2321ff1b203926a7307982ce d237131826e36c067ec3efa3f6a83f2708e02d76 Sim Zhen Quan <<EMAIL>> 1749796915 +0800	merge exam-result-data-entry-return-id-during-save-error: Merge made by the 'ort' strategy.
d237131826e36c067ec3efa3f6a83f2708e02d76 df23ddfcdd68fbc9288a07db4f2aa0268c655328 Sim Zhen Quan <<EMAIL>> 1749797033 +0800	commit: Deployed to dev
df23ddfcdd68fbc9288a07db4f2aa0268c655328 357b7833b8e0de9dbb2825b6f19b98f90ddea5cf Sim Zhen Quan <<EMAIL>> 1749797063 +0800	checkout: moving from dev to examination-result-by-student-add-principal-name-homeroom-teacher-name
357b7833b8e0de9dbb2825b6f19b98f90ddea5cf 018ea88df8a114674a736c0f40f557c91f0ad52e Sim Zhen Quan <<EMAIL>> 1749797118 +0800	commit (merge): Merge remote-tracking branch 'origin/main' into examination-result-by-student-add-principal-name-homeroom-teacher-name
018ea88df8a114674a736c0f40f557c91f0ad52e 018ea88df8a114674a736c0f40f557c91f0ad52e Sim Zhen Quan <<EMAIL>> 1749797139 +0800	checkout: moving from examination-result-by-student-add-principal-name-homeroom-teacher-name to examination-result-by-student-add-principal-name-homeroom-teacher-name
018ea88df8a114674a736c0f40f557c91f0ad52e 6801bfbcc8047262877c97ec3e1c913aa152087f Sim Zhen Quan <<EMAIL>> 1749798107 +0800	commit: Reviewed
6801bfbcc8047262877c97ec3e1c913aa152087f df23ddfcdd68fbc9288a07db4f2aa0268c655328 Sim Zhen Quan <<EMAIL>> 1749798171 +0800	checkout: moving from examination-result-by-student-add-principal-name-homeroom-teacher-name to dev
df23ddfcdd68fbc9288a07db4f2aa0268c655328 265202854e390fce998ab76d39033deae3a65446 Sim Zhen Quan <<EMAIL>> 1749798178 +0800	merge examination-result-by-student-add-principal-name-homeroom-teacher-name: Merge made by the 'ort' strategy.
265202854e390fce998ab76d39033deae3a65446 1868820bf8c37113b5de7725e8ef06238ed9749e Sim Zhen Quan <<EMAIL>> 1749798191 +0800	checkout: moving from dev to fix-examination-result-by-exam-report-header
1868820bf8c37113b5de7725e8ef06238ed9749e 88abfc0785e920aad5d973cf94c7a3297b72d750 Sim Zhen Quan <<EMAIL>> 1749798197 +0800	merge origin/main: Merge made by the 'ort' strategy.
88abfc0785e920aad5d973cf94c7a3297b72d750 88abfc0785e920aad5d973cf94c7a3297b72d750 Sim Zhen Quan <<EMAIL>> 1749799876 +0800	reset: moving to HEAD
88abfc0785e920aad5d973cf94c7a3297b72d750 efc505a00171bb55900d3e63a9f044ad84e7b413 Sim Zhen Quan <<EMAIL>> 1749799879 +0800	checkout: moving from fix-examination-result-by-exam-report-header to main
efc505a00171bb55900d3e63a9f044ad84e7b413 88abfc0785e920aad5d973cf94c7a3297b72d750 Sim Zhen Quan <<EMAIL>> 1749799901 +0800	checkout: moving from main to fix-examination-result-by-exam-report-header
88abfc0785e920aad5d973cf94c7a3297b72d750 88abfc0785e920aad5d973cf94c7a3297b72d750 Sim Zhen Quan <<EMAIL>> 1749800000 +0800	reset: moving to HEAD
88abfc0785e920aad5d973cf94c7a3297b72d750 efc505a00171bb55900d3e63a9f044ad84e7b413 Sim Zhen Quan <<EMAIL>> 1749800000 +0800	checkout: moving from fix-examination-result-by-exam-report-header to main
efc505a00171bb55900d3e63a9f044ad84e7b413 88abfc0785e920aad5d973cf94c7a3297b72d750 Sim Zhen Quan <<EMAIL>> 1749800012 +0800	checkout: moving from main to fix-examination-result-by-exam-report-header
88abfc0785e920aad5d973cf94c7a3297b72d750 0afff5b687bb1e35f5e600c0d1309378a156731e Sim Zhen Quan <<EMAIL>> 1749800138 +0800	commit: Reviewed
0afff5b687bb1e35f5e600c0d1309378a156731e 0afff5b687bb1e35f5e600c0d1309378a156731e Sim Zhen Quan <<EMAIL>> 1749800338 +0800	checkout: moving from fix-examination-result-by-exam-report-header to fix-examination-result-by-exam-report-header
0afff5b687bb1e35f5e600c0d1309378a156731e 265202854e390fce998ab76d39033deae3a65446 Sim Zhen Quan <<EMAIL>> 1749800348 +0800	checkout: moving from fix-examination-result-by-exam-report-header to dev
265202854e390fce998ab76d39033deae3a65446 a64247b4ef11aad0330ddb77613ca0567f01607a Sim Zhen Quan <<EMAIL>> 1749800348 +0800	merge fix-examination-result-by-exam-report-header: Merge made by the 'ort' strategy.
a64247b4ef11aad0330ddb77613ca0567f01607a 70cef8b36dc6d69b21215d05f08fe488ed74dbeb Sim Zhen Quan <<EMAIL>> 1749800632 +0800	commit: Deployed to dev
70cef8b36dc6d69b21215d05f08fe488ed74dbeb fed8daa2308d8daa474ea2ee9db8ae246c8a312c Sim Zhen Quan <<EMAIL>> 1749800997 +0800	checkout: moving from dev to feature/enrollment-siblings-update
fed8daa2308d8daa474ea2ee9db8ae246c8a312c 9b25ab67273771fa411f44875c392e3f77feef88 Sim Zhen Quan <<EMAIL>> 1749801436 +0800	commit: Added locale for enrollment
9b25ab67273771fa411f44875c392e3f77feef88 70cef8b36dc6d69b21215d05f08fe488ed74dbeb Sim Zhen Quan <<EMAIL>> 1749801448 +0800	checkout: moving from feature/enrollment-siblings-update to dev
70cef8b36dc6d69b21215d05f08fe488ed74dbeb b521ba42b3d8570df68ee19fc8ffaca3b95b9592 Sim Zhen Quan <<EMAIL>> 1749801448 +0800	merge feature/enrollment-siblings-update: Merge made by the 'ort' strategy.
b521ba42b3d8570df68ee19fc8ffaca3b95b9592 8499648f5399c635a94745aa76eca1454a77c1b5 Sim Zhen Quan <<EMAIL>> 1749801647 +0800	commit: Fix locale
8499648f5399c635a94745aa76eca1454a77c1b5 efc505a00171bb55900d3e63a9f044ad84e7b413 Sim Zhen Quan <<EMAIL>> 1749801651 +0800	checkout: moving from dev to main
efc505a00171bb55900d3e63a9f044ad84e7b413 efc505a00171bb55900d3e63a9f044ad84e7b413 Sim Zhen Quan <<EMAIL>> 1749801662 +0800	checkout: moving from main to staging/2025-06-13
efc505a00171bb55900d3e63a9f044ad84e7b413 8baf505cd28e59598fa2d30c822ef9e9d5aa9b99 Sim Zhen Quan <<EMAIL>> 1749801916 +0800	checkout: moving from staging/2025-06-13 to issue-180-students-by-primary-class-report-cosmetic-change
8baf505cd28e59598fa2d30c822ef9e9d5aa9b99 98069ba853a3f0fb62e0f73d4ecab6ad924f3dd1 Sim Zhen Quan <<EMAIL>> 1749801921 +0800	merge origin/main: Merge made by the 'ort' strategy.
98069ba853a3f0fb62e0f73d4ecab6ad924f3dd1 d7f9c94777e038e9ef2e4faacca26875d7ba484e Sim Zhen Quan <<EMAIL>> 1749805463 +0800	commit: Added note for reminder
d7f9c94777e038e9ef2e4faacca26875d7ba484e 8499648f5399c635a94745aa76eca1454a77c1b5 Sim Zhen Quan <<EMAIL>> 1749805581 +0800	checkout: moving from issue-180-students-by-primary-class-report-cosmetic-change to dev
8499648f5399c635a94745aa76eca1454a77c1b5 49f1e64295d735fb96efd2cbd808bf89e6f3192a Sim Zhen Quan <<EMAIL>> 1749805632 +0800	commit (merge): Merge remote-tracking branch 'origin/issue-180-students-by-primary-class-report-cosmetic-change' into dev
49f1e64295d735fb96efd2cbd808bf89e6f3192a 46742089512d6a8c2c46b7118339c6a6a486c70a Sim Zhen Quan <<EMAIL>> 1749805958 +0800	checkout: moving from dev to issue-191-non-primary-student-by-semester-class-report-sorting
46742089512d6a8c2c46b7118339c6a6a486c70a 8cee170d62cf585b6b6106202242994bf01ac2e9 Sim Zhen Quan <<EMAIL>> 1749806046 +0800	commit (merge): Merge remote-tracking branch 'origin/main' into issue-191-non-primary-student-by-semester-class-report-sorting
8cee170d62cf585b6b6106202242994bf01ac2e9 0e9b93bfc386b33ca2e93565fce53145fb771b6f Sim Zhen Quan <<EMAIL>> 1749809115 +0800	commit: Reviewed
0e9b93bfc386b33ca2e93565fce53145fb771b6f 49f1e64295d735fb96efd2cbd808bf89e6f3192a Sim Zhen Quan <<EMAIL>> 1749809236 +0800	checkout: moving from issue-191-non-primary-student-by-semester-class-report-sorting to dev
49f1e64295d735fb96efd2cbd808bf89e6f3192a 2d1d573ccdbd4b063c2b9e6a7729dfc75b2c36ca Sim Zhen Quan <<EMAIL>> 1749809243 +0800	merge issue-191-non-primary-student-by-semester-class-report-sorting: Merge made by the 'ort' strategy.
2d1d573ccdbd4b063c2b9e6a7729dfc75b2c36ca 5e65acb4b090eb8acb0c6db19aa950dfdbf99bc2 Sim Zhen Quan <<EMAIL>> 1749809884 +0800	commit: Deployed to dev
5e65acb4b090eb8acb0c6db19aa950dfdbf99bc2 6c571f6bad34407c9be3e0cb868f348e22b4d813 Sim Zhen Quan <<EMAIL>> 1749809904 +0800	checkout: moving from dev to issue-188-cocu-student-statistic-report-exclude-inactive-student-class
6c571f6bad34407c9be3e0cb868f348e22b4d813 45a8fbe5e62437b0609129c2b76e9ccb4f62c79a Sim Zhen Quan <<EMAIL>> 1749809910 +0800	merge origin/main: Merge made by the 'ort' strategy.
45a8fbe5e62437b0609129c2b76e9ccb4f62c79a efc505a00171bb55900d3e63a9f044ad84e7b413 Sim Zhen Quan <<EMAIL>> 1750006795 +0800	checkout: moving from issue-188-cocu-student-statistic-report-exclude-inactive-student-class to main
efc505a00171bb55900d3e63a9f044ad84e7b413 50241daebb92ba47f74e6eb56b24f3ef1ea9f3e8 Sim Zhen Quan <<EMAIL>> 1750006802 +0800	pull: Fast-forward
50241daebb92ba47f74e6eb56b24f3ef1ea9f3e8 bdf1d1e09cc50f7b4d974bb10040087a903305da Sim Zhen Quan <<EMAIL>> 1750007333 +0800	commit: Deployed to prd
bdf1d1e09cc50f7b4d974bb10040087a903305da 97d10259efe08064c13ede761cc57dca42a43e25 Sim Zhen Quan <<EMAIL>> 1750007351 +0800	checkout: moving from main to exam-passing-rate-reports
97d10259efe08064c13ede761cc57dca42a43e25 e3c54f1bcc42c904e9f316e66697090e5d878bf9 Sim Zhen Quan <<EMAIL>> 1750007584 +0800	commit (merge): Merge remote-tracking branch 'origin/main' into exam-passing-rate-reports
e3c54f1bcc42c904e9f316e66697090e5d878bf9 9b25ab67273771fa411f44875c392e3f77feef88 Sim Zhen Quan <<EMAIL>> 1750039483 +0800	checkout: moving from exam-passing-rate-reports to feature/enrollment-siblings-update
9b25ab67273771fa411f44875c392e3f77feef88 8abd4255ad27efa1c375587b517870b3902af0ad Sim Zhen Quan <<EMAIL>> 1750039492 +0800	merge origin/main: Merge made by the 'ort' strategy.
8abd4255ad27efa1c375587b517870b3902af0ad 18f4d42f68bb1ff3b91d86bdbfaca10483d899a8 Sim Zhen Quan <<EMAIL>> 1750039780 +0800	commit: Added permission for summary view
18f4d42f68bb1ff3b91d86bdbfaca10483d899a8 5e65acb4b090eb8acb0c6db19aa950dfdbf99bc2 Sim Zhen Quan <<EMAIL>> 1750039792 +0800	checkout: moving from feature/enrollment-siblings-update to dev
5e65acb4b090eb8acb0c6db19aa950dfdbf99bc2 ab069f8ebc162c2750728f586a5ea5cfa479c5c0 Sim Zhen Quan <<EMAIL>> 1750039796 +0800	merge feature/enrollment-siblings-update: Merge made by the 'ort' strategy.
ab069f8ebc162c2750728f586a5ea5cfa479c5c0 e3c54f1bcc42c904e9f316e66697090e5d878bf9 Sim Zhen Quan <<EMAIL>> 1750040085 +0800	checkout: moving from dev to exam-passing-rate-reports
e3c54f1bcc42c904e9f316e66697090e5d878bf9 71ccc243aba5866eac875e9caaf804dda9573295 Sim Zhen Quan <<EMAIL>> 1750040956 +0800	checkout: moving from exam-passing-rate-reports to enhance-examination-result-by-class-report-to-check-is-exempted
71ccc243aba5866eac875e9caaf804dda9573295 dce68ce9b03f2b093e4d7d7891506c0c1adf17fc Sim Zhen Quan <<EMAIL>> 1750040962 +0800	merge origin/main: Merge made by the 'ort' strategy.
dce68ce9b03f2b093e4d7d7891506c0c1adf17fc dce68ce9b03f2b093e4d7d7891506c0c1adf17fc Sim Zhen Quan <<EMAIL>> 1750041039 +0800	checkout: moving from enhance-examination-result-by-class-report-to-check-is-exempted to enhance-examination-result-by-class-report-to-check-is-exempted
dce68ce9b03f2b093e4d7d7891506c0c1adf17fc dfa63eba6b98b4054db0b2e9ea4b26adb7e7e681 Sim Zhen Quan <<EMAIL>> 1750046007 +0800	commit: Reviewed
dfa63eba6b98b4054db0b2e9ea4b26adb7e7e681 ab069f8ebc162c2750728f586a5ea5cfa479c5c0 Sim Zhen Quan <<EMAIL>> 1750046060 +0800	checkout: moving from enhance-examination-result-by-class-report-to-check-is-exempted to dev
ab069f8ebc162c2750728f586a5ea5cfa479c5c0 dfa63eba6b98b4054db0b2e9ea4b26adb7e7e681 Sim Zhen Quan <<EMAIL>> 1750046085 +0800	checkout: moving from dev to enhance-examination-result-by-class-report-to-check-is-exempted
dfa63eba6b98b4054db0b2e9ea4b26adb7e7e681 ab069f8ebc162c2750728f586a5ea5cfa479c5c0 Sim Zhen Quan <<EMAIL>> 1750046089 +0800	checkout: moving from enhance-examination-result-by-class-report-to-check-is-exempted to dev
ab069f8ebc162c2750728f586a5ea5cfa479c5c0 ab236c0e848d1e3d3c6310a45a00f107f7c43f70 Sim Zhen Quan <<EMAIL>> 1750046136 +0800	commit (merge): Merge branch 'enhance-examination-result-by-class-report-to-check-is-exempted' into dev
ab236c0e848d1e3d3c6310a45a00f107f7c43f70 50898fe8efca308f4a883a2eaa34175ac2896c19 Sim Zhen Quan <<EMAIL>> 1750054629 +0800	commit: Deployed to dev
50898fe8efca308f4a883a2eaa34175ac2896c19 50898fe8efca308f4a883a2eaa34175ac2896c19 Sim Zhen Quan <<EMAIL>> 1750054652 +0800	checkout: moving from dev to lucas/fix-semester-class-saving-issue
50898fe8efca308f4a883a2eaa34175ac2896c19 d02f18bda9b496c5bb519afd9642869a1cec8c5f Sim Zhen Quan <<EMAIL>> 1750054678 +0800	commit: Fix primary class issue
d02f18bda9b496c5bb519afd9642869a1cec8c5f 09c42a0430bb61f8836280a8a69a10fb02485e0c Sim Zhen Quan <<EMAIL>> 1750055079 +0800	commit: Fix test cases
09c42a0430bb61f8836280a8a69a10fb02485e0c bdf1d1e09cc50f7b4d974bb10040087a903305da Sim Zhen Quan <<EMAIL>> 1750055121 +0800	checkout: moving from lucas/fix-semester-class-saving-issue to main
bdf1d1e09cc50f7b4d974bb10040087a903305da bdf1d1e09cc50f7b4d974bb10040087a903305da Sim Zhen Quan <<EMAIL>> 1750055130 +0800	checkout: moving from main to lucas/fix-sem-class-saving-issue
bdf1d1e09cc50f7b4d974bb10040087a903305da 747710f9de311ae9f753c9451311c5bdc6ce5a24 Sim Zhen Quan <<EMAIL>> 1750055143 +0800	cherry-pick: Fix primary class issue
747710f9de311ae9f753c9451311c5bdc6ce5a24 bdf1d1e09cc50f7b4d974bb10040087a903305da Sim Zhen Quan <<EMAIL>> 1750055213 +0800	checkout: moving from lucas/fix-sem-class-saving-issue to main
bdf1d1e09cc50f7b4d974bb10040087a903305da 50898fe8efca308f4a883a2eaa34175ac2896c19 Sim Zhen Quan <<EMAIL>> 1750055223 +0800	checkout: moving from main to dev
50898fe8efca308f4a883a2eaa34175ac2896c19 5e86028f6b9e78504dd750daecbbe90750a340d7 Sim Zhen Quan <<EMAIL>> 1750055230 +0800	merge lucas/fix-sem-class-saving-issue: Merge made by the 'ort' strategy.
5e86028f6b9e78504dd750daecbbe90750a340d7 82e7e4ec36ede5e60d75e39ad16173a9775661ed Sim Zhen Quan <<EMAIL>> 1750055547 +0800	commit: Deployed to dev
82e7e4ec36ede5e60d75e39ad16173a9775661ed bdf1d1e09cc50f7b4d974bb10040087a903305da Sim Zhen Quan <<EMAIL>> 1750055553 +0800	checkout: moving from dev to main
bdf1d1e09cc50f7b4d974bb10040087a903305da c80bcbd859f1ea5f2667209ab2e09b1b46fcef8a Sim Zhen Quan <<EMAIL>> 1750055558 +0800	pull: Fast-forward
c80bcbd859f1ea5f2667209ab2e09b1b46fcef8a e27c3f47d69d0432a0cde11dba9967a7b31a0e1a Sim Zhen Quan <<EMAIL>> 1750056974 +0800	checkout: moving from main to exam-subject-analysis-report
e27c3f47d69d0432a0cde11dba9967a7b31a0e1a dec162a99bd1b8b39e900bdf3d756b1f06246650 Sim Zhen Quan <<EMAIL>> 1750056981 +0800	commit: Deployed to prd
dec162a99bd1b8b39e900bdf3d756b1f06246650 dec162a99bd1b8b39e900bdf3d756b1f06246650 Sim Zhen Quan <<EMAIL>> 1750056993 +0800	checkout: moving from exam-subject-analysis-report to exam-subject-analysis-report
dec162a99bd1b8b39e900bdf3d756b1f06246650 c80bcbd859f1ea5f2667209ab2e09b1b46fcef8a Sim Zhen Quan <<EMAIL>> 1750057061 +0800	checkout: moving from exam-subject-analysis-report to main
c80bcbd859f1ea5f2667209ab2e09b1b46fcef8a e0252f81ba8934dfa886dd18ddadcd135d0c81fd Sim Zhen Quan <<EMAIL>> 1750057070 +0800	cherry-pick: Deployed to prd
e0252f81ba8934dfa886dd18ddadcd135d0c81fd dec162a99bd1b8b39e900bdf3d756b1f06246650 Sim Zhen Quan <<EMAIL>> 1750057103 +0800	checkout: moving from main to exam-subject-analysis-report
dec162a99bd1b8b39e900bdf3d756b1f06246650 748fa36ae09c79eadc6b4872a0dd1061de12ebc6 Sim Zhen Quan <<EMAIL>> 1750057128 +0800	merge origin/main: Merge made by the 'ort' strategy.
748fa36ae09c79eadc6b4872a0dd1061de12ebc6 18101be23c128b27f4a5b75d2c8cf592ce642efd Sim Zhen Quan <<EMAIL>> 1750058494 +0800	commit: Reviewing
18101be23c128b27f4a5b75d2c8cf592ce642efd 82e7e4ec36ede5e60d75e39ad16173a9775661ed Sim Zhen Quan <<EMAIL>> 1750058506 +0800	checkout: moving from exam-subject-analysis-report to dev
82e7e4ec36ede5e60d75e39ad16173a9775661ed ad6b7bf6ca18aa2124231b5351e8f4a8ca46e215 Sim Zhen Quan <<EMAIL>> 1750058515 +0800	merge exam-subject-analysis-report: Merge made by the 'ort' strategy.
ad6b7bf6ca18aa2124231b5351e8f4a8ca46e215 1e7498c8e29a6d27282d3785d429a7e346973d3f Sim Zhen Quan <<EMAIL>> 1750058773 +0800	commit: Deployed to dev
1e7498c8e29a6d27282d3785d429a7e346973d3f 18101be23c128b27f4a5b75d2c8cf592ce642efd Sim Zhen Quan <<EMAIL>> 1750058778 +0800	checkout: moving from dev to exam-subject-analysis-report
18101be23c128b27f4a5b75d2c8cf592ce642efd 3e0db2532d303833718afc850812bc2e4aef98ad Sim Zhen Quan <<EMAIL>> 1750064248 +0800	commit: Update result posting header by grade
3e0db2532d303833718afc850812bc2e4aef98ad 18f4d42f68bb1ff3b91d86bdbfaca10483d899a8 Sim Zhen Quan <<EMAIL>> 1750064282 +0800	checkout: moving from exam-subject-analysis-report to feature/enrollment-siblings-update
18f4d42f68bb1ff3b91d86bdbfaca10483d899a8 9e0884daab7007ce6485c41978b9127ad9162f87 Sim Zhen Quan <<EMAIL>> 1750064318 +0800	commit: Remove summary
9e0884daab7007ce6485c41978b9127ad9162f87 1e7498c8e29a6d27282d3785d429a7e346973d3f Sim Zhen Quan <<EMAIL>> 1750064681 +0800	checkout: moving from feature/enrollment-siblings-update to dev
1e7498c8e29a6d27282d3785d429a7e346973d3f 3e0db2532d303833718afc850812bc2e4aef98ad Sim Zhen Quan <<EMAIL>> 1750065444 +0800	checkout: moving from dev to exam-subject-analysis-report
3e0db2532d303833718afc850812bc2e4aef98ad 86c20ed030d4b6ba65528a77e9080a90a23bde73 Sim Zhen Quan <<EMAIL>> 1750065482 +0800	commit: Added PermissionDependencies.php
86c20ed030d4b6ba65528a77e9080a90a23bde73 9e0884daab7007ce6485c41978b9127ad9162f87 Sim Zhen Quan <<EMAIL>> 1750066675 +0800	checkout: moving from exam-subject-analysis-report to feature/enrollment-siblings-update
9e0884daab7007ce6485c41978b9127ad9162f87 bb74f9a010569339a6f428c7d0996c053dd10826 Sim Zhen Quan <<EMAIL>> 1750067387 +0800	commit: Update permission dependency
bb74f9a010569339a6f428c7d0996c053dd10826 37ac6717bc43600ea75df1d70c2cced508bbd000 Sim Zhen Quan <<EMAIL>> 1750068328 +0800	checkout: moving from feature/enrollment-siblings-update to bugs/auto-assign-class-subject
37ac6717bc43600ea75df1d70c2cced508bbd000 c79e0aa5ae9683aca01fdd489ccdd1743a373a9b Sim Zhen Quan <<EMAIL>> 1750068335 +0800	merge origin/main: Merge made by the 'ort' strategy.
c79e0aa5ae9683aca01fdd489ccdd1743a373a9b 747710f9de311ae9f753c9451311c5bdc6ce5a24 Sim Zhen Quan <<EMAIL>> 1750068533 +0800	checkout: moving from bugs/auto-assign-class-subject to lucas/fix-sem-class-saving-issue
747710f9de311ae9f753c9451311c5bdc6ce5a24 3c67e3873c42e50bdd1a86bb6d5bb29eed963d19 Sim Zhen Quan <<EMAIL>> 1750070477 +0800	commit: Fix class subject attach issue
3c67e3873c42e50bdd1a86bb6d5bb29eed963d19 e0252f81ba8934dfa886dd18ddadcd135d0c81fd Sim Zhen Quan <<EMAIL>> 1750070596 +0800	checkout: moving from lucas/fix-sem-class-saving-issue to main
e0252f81ba8934dfa886dd18ddadcd135d0c81fd e0252f81ba8934dfa886dd18ddadcd135d0c81fd Sim Zhen Quan <<EMAIL>> 1750070619 +0800	checkout: moving from main to staging/2025-06-16
e0252f81ba8934dfa886dd18ddadcd135d0c81fd efc505a00171bb55900d3e63a9f044ad84e7b413 Sim Zhen Quan <<EMAIL>> 1750070777 +0800	checkout: moving from staging/2025-06-16 to staging/2025-06-13
efc505a00171bb55900d3e63a9f044ad84e7b413 a556a6ed00b2f1ee9866970f9828684d6f499a14 Sim Zhen Quan <<EMAIL>> 1750070783 +0800	pull: Fast-forward
a556a6ed00b2f1ee9866970f9828684d6f499a14 81363b5c1677ba105d72d6e213789cd9cbb503bf Sim Zhen Quan <<EMAIL>> 1750070894 +0800	commit (merge): Merge remote-tracking branch 'origin/main' into staging/2025-06-13
81363b5c1677ba105d72d6e213789cd9cbb503bf e0252f81ba8934dfa886dd18ddadcd135d0c81fd Sim Zhen Quan <<EMAIL>> 1750084398 +0800	checkout: moving from staging/2025-06-13 to main
e0252f81ba8934dfa886dd18ddadcd135d0c81fd 41cee203bdde58506689d0bc19a7ea646559cfe7 Sim Zhen Quan <<EMAIL>> 1750084404 +0800	pull: Fast-forward
41cee203bdde58506689d0bc19a7ea646559cfe7 909d252fcd6d54661e7ab7cdc2a82e2e6376708e Sim Zhen Quan <<EMAIL>> 1750086517 +0800	commit: Deployed to prd
909d252fcd6d54661e7ab7cdc2a82e2e6376708e d6ca2154896e9dbc7c99387cb512defb7bc9ffde Sim Zhen Quan <<EMAIL>> 1750213158 +0800	commit: Added class patch
d6ca2154896e9dbc7c99387cb512defb7bc9ffde d6ca2154896e9dbc7c99387cb512defb7bc9ffde Sim Zhen Quan <<EMAIL>> 1750213192 +0800	checkout: moving from main to main
d6ca2154896e9dbc7c99387cb512defb7bc9ffde 1e7498c8e29a6d27282d3785d429a7e346973d3f Sim Zhen Quan <<EMAIL>> 1750213342 +0800	checkout: moving from main to dev
1e7498c8e29a6d27282d3785d429a7e346973d3f d6ca2154896e9dbc7c99387cb512defb7bc9ffde Sim Zhen Quan <<EMAIL>> 1750214280 +0800	checkout: moving from dev to main
d6ca2154896e9dbc7c99387cb512defb7bc9ffde d6ca2154896e9dbc7c99387cb512defb7bc9ffde Sim Zhen Quan <<EMAIL>> 1750214290 +0800	checkout: moving from main to add-fallback-noto-serif-hk
d6ca2154896e9dbc7c99387cb512defb7bc9ffde b1b7d1827df5fb1590ee2ca17eaba8f148da925c Sim Zhen Quan <<EMAIL>> 1750214377 +0800	commit: Added fallback noto serif HK
b1b7d1827df5fb1590ee2ca17eaba8f148da925c 1e7498c8e29a6d27282d3785d429a7e346973d3f Sim Zhen Quan <<EMAIL>> 1750214777 +0800	checkout: moving from add-fallback-noto-serif-hk to dev
1e7498c8e29a6d27282d3785d429a7e346973d3f 99d96723820d8ca689d69ad594dd194bda17c12a Sim Zhen Quan <<EMAIL>> 1750214781 +0800	merge add-fallback-noto-serif-hk: Merge made by the 'ort' strategy.
99d96723820d8ca689d69ad594dd194bda17c12a b1b7d1827df5fb1590ee2ca17eaba8f148da925c Sim Zhen Quan <<EMAIL>> 1750216798 +0800	checkout: moving from dev to add-fallback-noto-serif-hk
b1b7d1827df5fb1590ee2ca17eaba8f148da925c ef8c83da454a2ab3fc887817f11cd0c6e16c8998 Sim Zhen Quan <<EMAIL>> 1750216817 +0800	commit: Remove unused file
ef8c83da454a2ab3fc887817f11cd0c6e16c8998 d6ca2154896e9dbc7c99387cb512defb7bc9ffde Sim Zhen Quan <<EMAIL>> 1750216833 +0800	checkout: moving from add-fallback-noto-serif-hk to main
d6ca2154896e9dbc7c99387cb512defb7bc9ffde 6cfb756329b35a20014cbda769e981fee859e962 Sim Zhen Quan <<EMAIL>> 1750216838 +0800	pull: Fast-forward
6cfb756329b35a20014cbda769e981fee859e962 f22fb40f6eadb73dc57abeeda77d9d82e1460312 Sim Zhen Quan <<EMAIL>> 1750217371 +0800	commit: Deploy to prd
f22fb40f6eadb73dc57abeeda77d9d82e1460312 e93780cf75365d428934a77fdec7dac1babeeba1 Sim Zhen Quan <<EMAIL>> 1750230596 +0800	checkout: moving from main to fix/enhance-enrollment-admission-year
e93780cf75365d428934a77fdec7dac1babeeba1 4ce54c2528b3f23bac5350776a2d2c6531cd9135 Sim Zhen Quan <<EMAIL>> 1750230602 +0800	merge origin/main: Merge made by the 'ort' strategy.
4ce54c2528b3f23bac5350776a2d2c6531cd9135 3e845f9db1f9e98930fa6bdc48a7a21eda97f4da Sim Zhen Quan <<EMAIL>> 1750233961 +0800	commit: Reviewed
3e845f9db1f9e98930fa6bdc48a7a21eda97f4da 99d96723820d8ca689d69ad594dd194bda17c12a Sim Zhen Quan <<EMAIL>> 1750235354 +0800	checkout: moving from fix/enhance-enrollment-admission-year to dev
99d96723820d8ca689d69ad594dd194bda17c12a a13334ea355df7c0e3acaf066fc1fe8c82fa0a7d Sim Zhen Quan <<EMAIL>> 1750235363 +0800	merge fix/enhance-enrollment-admission-year: Merge made by the 'ort' strategy.
a13334ea355df7c0e3acaf066fc1fe8c82fa0a7d 20921507b1d5bd468f8bb24da8cd538f908be65a Sim Zhen Quan <<EMAIL>> 1750235570 +0800	commit: Deployed to dev
20921507b1d5bd468f8bb24da8cd538f908be65a 159e88afdb418a93dce87975660d3d49a563f292 Sim Zhen Quan <<EMAIL>> 1750237537 +0800	checkout: moving from dev to academy-subject-analysis-report-enhancement
159e88afdb418a93dce87975660d3d49a563f292 73313e868c0fafbaaf269ba2e8c4b111af6d797e Sim Zhen Quan <<EMAIL>> 1750238668 +0800	merge origin/main: Merge made by the 'ort' strategy.
73313e868c0fafbaaf269ba2e8c4b111af6d797e 055ab4e8c5017ca67e68fdc1cdaa7cf9e0cf253f Sim Zhen Quan <<EMAIL>> 1750239454 +0800	commit: Reviewed
055ab4e8c5017ca67e68fdc1cdaa7cf9e0cf253f 20921507b1d5bd468f8bb24da8cd538f908be65a Sim Zhen Quan <<EMAIL>> 1750239463 +0800	checkout: moving from academy-subject-analysis-report-enhancement to dev
20921507b1d5bd468f8bb24da8cd538f908be65a 9b3c72b70372ce7c4173d6162b801c27050d23ba Sim Zhen Quan <<EMAIL>> 1750239467 +0800	merge academy-subject-analysis-report-enhancement: Merge made by the 'ort' strategy.
9b3c72b70372ce7c4173d6162b801c27050d23ba 6cf95127c357571a8e4d5515e2d0507a32c82b9c Sim Zhen Quan <<EMAIL>> 1750240896 +0800	commit: Deployed to dev
6cf95127c357571a8e4d5515e2d0507a32c82b9c 86b0f1bfb271c6a7e2cf70ed486e2a3c160523ee Sim Zhen Quan <<EMAIL>> 1750241727 +0800	checkout: moving from dev to exam-update-grading-framework-enhancement
86b0f1bfb271c6a7e2cf70ed486e2a3c160523ee c0bee257a3f0203d01dc11e54f8d4b0a2359653c Sim Zhen Quan <<EMAIL>> 1750241744 +0800	merge origin/main: Merge made by the 'ort' strategy.
c0bee257a3f0203d01dc11e54f8d4b0a2359653c a9cb0a983625bef4531dd7c2bc85101dad4f7680 Sim Zhen Quan <<EMAIL>> 1750242166 +0800	commit: Reviewed
a9cb0a983625bef4531dd7c2bc85101dad4f7680 6cf95127c357571a8e4d5515e2d0507a32c82b9c Sim Zhen Quan <<EMAIL>> 1750242176 +0800	checkout: moving from exam-update-grading-framework-enhancement to dev
6cf95127c357571a8e4d5515e2d0507a32c82b9c 1e57e86d5d843138433cbeb8a1423c708f6675b8 Sim Zhen Quan <<EMAIL>> ********** +0800	merge exam-update-grading-framework-enhancement: Merge made by the 'ort' strategy.
1e57e86d5d843138433cbeb8a1423c708f6675b8 571955d4d36c25f98c5f88c3575f7516e68ad4a2 Sim Zhen Quan <<EMAIL>> ********** +0800	commit: Deployed to dev
571955d4d36c25f98c5f88c3575f7516e68ad4a2 9c81f4a3139d29b6ebacb22106c4317244e8fef0 Sim Zhen Quan <<EMAIL>> ********** +0800	checkout: moving from dev to SKLEARN-474-api-guest-account-name-translation
9c81f4a3139d29b6ebacb22106c4317244e8fef0 571955d4d36c25f98c5f88c3575f7516e68ad4a2 Sim Zhen Quan <<EMAIL>> ********** +0800	checkout: moving from SKLEARN-474-api-guest-account-name-translation to dev
571955d4d36c25f98c5f88c3575f7516e68ad4a2 7e9a44fcb4bae52b8a95ae173bd7c3c721170e4f Sim Zhen Quan <<EMAIL>> ********** +0800	merge SKLEARN-474-api-guest-account-name-translation: Merge made by the 'ort' strategy.
7e9a44fcb4bae52b8a95ae173bd7c3c721170e4f 6877f93ab81030da77d8d74e6b99aba83a4e3dc1 Sim Zhen Quan <<EMAIL>> ********** +0800	commit: Deployed to dev
6877f93ab81030da77d8d74e6b99aba83a4e3dc1 f22fb40f6eadb73dc57abeeda77d9d82e1460312 Sim Zhen Quan <<EMAIL>> ********** +0800	checkout: moving from dev to main
f22fb40f6eadb73dc57abeeda77d9d82e1460312 f22fb40f6eadb73dc57abeeda77d9d82e1460312 Sim Zhen Quan <<EMAIL>> ********** +0800	checkout: moving from main to staging/2025-06-18
f22fb40f6eadb73dc57abeeda77d9d82e1460312 f22fb40f6eadb73dc57abeeda77d9d82e1460312 Sim Zhen Quan <<EMAIL>> ********** +0800	checkout: moving from staging/2025-06-18 to main
f22fb40f6eadb73dc57abeeda77d9d82e1460312 33ac3069fdcdb8f5c7a2624e57fc8ffb934a4b98 Sim Zhen Quan <<EMAIL>> ********** +0800	commit: Added CHANGELOG.md
33ac3069fdcdb8f5c7a2624e57fc8ffb934a4b98 33ac3069fdcdb8f5c7a2624e57fc8ffb934a4b98 Sim Zhen Quan <<EMAIL>> ********** +0800	checkout: moving from main to laravel-11-update
33ac3069fdcdb8f5c7a2624e57fc8ffb934a4b98 d432cbde2e9b01ea8d958b4859e06161b752f569 Sim Zhen Quan <<EMAIL>> 1750305885 +0800	commit: Upgrade to laravel 11
d432cbde2e9b01ea8d958b4859e06161b752f569 33ac3069fdcdb8f5c7a2624e57fc8ffb934a4b98 Sim Zhen Quan <<EMAIL>> 1750305901 +0800	checkout: moving from laravel-11-update to main
33ac3069fdcdb8f5c7a2624e57fc8ffb934a4b98 51fc5d3a980cc3eebcc384abaf546da85ce1bfc8 Sim Zhen Quan <<EMAIL>> 1750305910 +0800	commit: Bug fix
51fc5d3a980cc3eebcc384abaf546da85ce1bfc8 6877f93ab81030da77d8d74e6b99aba83a4e3dc1 Sim Zhen Quan <<EMAIL>> 1750305916 +0800	checkout: moving from main to dev
6877f93ab81030da77d8d74e6b99aba83a4e3dc1 089dd76f2a73ca35590613b326772aa91d1d6c7d Sim Zhen Quan <<EMAIL>> 1750305922 +0800	merge origin/main: Merge made by the 'ort' strategy.
089dd76f2a73ca35590613b326772aa91d1d6c7d afdac8213be9063af3acf9b58a17193b8ebd59c9 Sim Zhen Quan <<EMAIL>> 1750305932 +0800	merge laravel-11-update: Merge made by the 'ort' strategy.
afdac8213be9063af3acf9b58a17193b8ebd59c9 5e49a809aea2ccd11a21b6032ae2989e1727198d Sim Zhen Quan <<EMAIL>> 1750308987 +0800	commit: Deployed to dev
5e49a809aea2ccd11a21b6032ae2989e1727198d a47baff908cc86dc3702f04552e2a8125b144555 Sim Zhen Quan <<EMAIL>> 1750317641 +0800	checkout: moving from dev to fix/enrollment-user-changes
a47baff908cc86dc3702f04552e2a8125b144555 a60b52734c1092d2a738743c02f3f8d32a1aa588 Sim Zhen Quan <<EMAIL>> 1750317648 +0800	merge origin/main: Merge made by the 'ort' strategy.
a60b52734c1092d2a738743c02f3f8d32a1aa588 10fec6d319a1bb17bb14e7f7c87a3029cccd7dd1 Sim Zhen Quan <<EMAIL>> 1750318403 +0800	commit: Reviewed
10fec6d319a1bb17bb14e7f7c87a3029cccd7dd1 10fec6d319a1bb17bb14e7f7c87a3029cccd7dd1 Sim Zhen Quan <<EMAIL>> 1750318650 +0800	checkout: moving from fix/enrollment-user-changes to fix/enrollment-user-changes
10fec6d319a1bb17bb14e7f7c87a3029cccd7dd1 5e49a809aea2ccd11a21b6032ae2989e1727198d Sim Zhen Quan <<EMAIL>> 1750318659 +0800	checkout: moving from fix/enrollment-user-changes to dev
5e49a809aea2ccd11a21b6032ae2989e1727198d 60dea3682a703bc1ca4f4dbce9d11e7da8fe3570 Sim Zhen Quan <<EMAIL>> 1750318659 +0800	merge fix/enrollment-user-changes: Merge made by the 'ort' strategy.
60dea3682a703bc1ca4f4dbce9d11e7da8fe3570 21f019fc1a7b6f2009b35daf7e9205a7f51b1341 Sim Zhen Quan <<EMAIL>> 1750323099 +0800	commit: Deployed to dev
21f019fc1a7b6f2009b35daf7e9205a7f51b1341 a6d8d71fa904c115494403b73e69fdea3ad0adab Sim Zhen Quan <<EMAIL>> 1750323112 +0800	checkout: moving from dev to ai-rnd
a6d8d71fa904c115494403b73e69fdea3ad0adab f569de44a1674135882dc7e0be8e089b7e92a991 Sim Zhen Quan <<EMAIL>> 1750323119 +0800	merge origin/main: Merge made by the 'ort' strategy.
f569de44a1674135882dc7e0be8e089b7e92a991 1f6a43e6a744fe2bc895fd4b7d5f228b632f9d64 Sim Zhen Quan <<EMAIL>> 1750323151 +0800	commit (merge): Merge branch 'laravel-11-update' into ai-rnd
1f6a43e6a744fe2bc895fd4b7d5f228b632f9d64 1f6a43e6a744fe2bc895fd4b7d5f228b632f9d64 Sim Zhen Quan <<EMAIL>> 1750324945 +0800	reset: moving to HEAD
1f6a43e6a744fe2bc895fd4b7d5f228b632f9d64 10fec6d319a1bb17bb14e7f7c87a3029cccd7dd1 Sim Zhen Quan <<EMAIL>> 1750324947 +0800	checkout: moving from ai-rnd to fix/enrollment-user-changes
10fec6d319a1bb17bb14e7f7c87a3029cccd7dd1 cb855ef01e19d2874d1c400e19731a244b592bdf Sim Zhen Quan <<EMAIL>> 1750325130 +0800	commit: Added translations to SimpleEnrollmentResource.php
cb855ef01e19d2874d1c400e19731a244b592bdf 51fc5d3a980cc3eebcc384abaf546da85ce1bfc8 Sim Zhen Quan <<EMAIL>> 1750339508 +0800	checkout: moving from fix/enrollment-user-changes to main
51fc5d3a980cc3eebcc384abaf546da85ce1bfc8 5e06659be8d287ad115f16f57ba0a926c8f13d90 Sim Zhen Quan <<EMAIL>> 1750339560 +0800	pull: Fast-forward
5e06659be8d287ad115f16f57ba0a926c8f13d90 c6565ddb601ff0647d6da6eb988ecb7d14396218 Sim Zhen Quan <<EMAIL>> 1750339595 +0800	pull: Fast-forward
c6565ddb601ff0647d6da6eb988ecb7d14396218 0b235b9d4dd569cef3a6b848ed8d3e9c1e036549 Sim Zhen Quan <<EMAIL>> 1750342173 +0800	commit: Test case fixes
0b235b9d4dd569cef3a6b848ed8d3e9c1e036549 7f8d1989a9a3e3a04426e6387fbdecdb5db8e4ab Sim Zhen Quan <<EMAIL>> 1750346087 +0800	pull: Fast-forward
7f8d1989a9a3e3a04426e6387fbdecdb5db8e4ab 83924675aa2fe09c902ecbe35318d66b8f31034b Sim Zhen Quan <<EMAIL>> 1750346373 +0800	commit: Deployed to prd
83924675aa2fe09c902ecbe35318d66b8f31034b 1f6a43e6a744fe2bc895fd4b7d5f228b632f9d64 Sim Zhen Quan <<EMAIL>> 1750346597 +0800	checkout: moving from main to ai-rnd
1f6a43e6a744fe2bc895fd4b7d5f228b632f9d64 55452623d9100bb18804de39d680593cc4b32280 Sim Zhen Quan <<EMAIL>> 1750346604 +0800	merge origin/main: Merge made by the 'ort' strategy.
55452623d9100bb18804de39d680593cc4b32280 aa579187b1e362124e37067228ef95ecd6b323f0 Sim Zhen Quan <<EMAIL>> 1750408993 +0800	commit: WIP
aa579187b1e362124e37067228ef95ecd6b323f0 baa95fd0d9c518f4c641cace64d8f64c0dbba730 Sim Zhen Quan <<EMAIL>> 1750409121 +0800	commit: WIP
baa95fd0d9c518f4c641cace64d8f64c0dbba730 21f019fc1a7b6f2009b35daf7e9205a7f51b1341 Sim Zhen Quan <<EMAIL>> 1750409130 +0800	checkout: moving from ai-rnd to dev
21f019fc1a7b6f2009b35daf7e9205a7f51b1341 20a1e9467a0ae8e5c57bc45ffb8e7c5047c03369 Sim Zhen Quan <<EMAIL>> 1750409145 +0800	merge ai-rnd: Merge made by the 'ort' strategy.
20a1e9467a0ae8e5c57bc45ffb8e7c5047c03369 35a1583cb9dfa26d520cdda7a0e9cfa2dfa7b428 Sim Zhen Quan <<EMAIL>> 1750650117 +0800	commit: Deployed to dev
35a1583cb9dfa26d520cdda7a0e9cfa2dfa7b428 664749e610fb312ade663e77a55af8ed4775a6db Sim Zhen Quan <<EMAIL>> 1750650548 +0800	checkout: moving from dev to SKLEARN-481-student-report-enrollment-enhancement
664749e610fb312ade663e77a55af8ed4775a6db 6f120f596be2fade55effdb59f29fb68097f353c Sim Zhen Quan <<EMAIL>> 1750651098 +0800	commit: Update gitignore to ignore tmp file
6f120f596be2fade55effdb59f29fb68097f353c 35a1583cb9dfa26d520cdda7a0e9cfa2dfa7b428 Sim Zhen Quan <<EMAIL>> 1750652941 +0800	checkout: moving from SKLEARN-481-student-report-enrollment-enhancement to dev
35a1583cb9dfa26d520cdda7a0e9cfa2dfa7b428 57c2dc2b0b02d94f3b0458f26ceaed711756fd85 Sim Zhen Quan <<EMAIL>> 1750695087 +0800	pull: Fast-forward
57c2dc2b0b02d94f3b0458f26ceaed711756fd85 83924675aa2fe09c902ecbe35318d66b8f31034b Sim Zhen Quan <<EMAIL>> 1750695152 +0800	checkout: moving from dev to main
83924675aa2fe09c902ecbe35318d66b8f31034b 3072495b5b7c38583eca3254afea71bb6bf587f5 Sim Zhen Quan <<EMAIL>> 1750695653 +0800	pull: Fast-forward
3072495b5b7c38583eca3254afea71bb6bf587f5 795988ef2d130c68835c669641020632761d42cc Sim Zhen Quan <<EMAIL>> 1750696303 +0800	commit: Deployed to prd
795988ef2d130c68835c669641020632761d42cc 45a8fbe5e62437b0609129c2b76e9ccb4f62c79a Sim Zhen Quan <<EMAIL>> 1750696419 +0800	checkout: moving from main to issue-188-cocu-student-statistic-report-exclude-inactive-student-class
45a8fbe5e62437b0609129c2b76e9ccb4f62c79a 2c2a153f9a8f69f45c55011483d917f4cb15ff96 Sim Zhen Quan <<EMAIL>> 1750696426 +0800	merge origin/main: Merge made by the 'ort' strategy.
2c2a153f9a8f69f45c55011483d917f4cb15ff96 4cbf60a367f3c02087cae50f9a0fc3483d077443 Sim Zhen Quan <<EMAIL>> 1750696651 +0800	checkout: moving from issue-188-cocu-student-statistic-report-exclude-inactive-student-class to issue-192-fix-library-report-by-school-rate-of-borrow
4cbf60a367f3c02087cae50f9a0fc3483d077443 d12b686719ba41e0cb04da95927dd45c2b602ec0 Sim Zhen Quan <<EMAIL>> 1750696659 +0800	merge origin/main: Merge made by the 'ort' strategy.
d12b686719ba41e0cb04da95927dd45c2b602ec0 9f1781c294a162feea1f9d4bcfdf37d58c025862 Sim Zhen Quan <<EMAIL>> 1750697467 +0800	commit: Remove unused library
9f1781c294a162feea1f9d4bcfdf37d58c025862 57c2dc2b0b02d94f3b0458f26ceaed711756fd85 Sim Zhen Quan <<EMAIL>> 1750697473 +0800	checkout: moving from issue-192-fix-library-report-by-school-rate-of-borrow to dev
57c2dc2b0b02d94f3b0458f26ceaed711756fd85 265822d0995999e04687b5954dfbab7ffb472013 Sim Zhen Quan <<EMAIL>> 1750697481 +0800	merge issue-192-fix-library-report-by-school-rate-of-borrow: Merge made by the 'ort' strategy.
265822d0995999e04687b5954dfbab7ffb472013 bae75c01006d2769feb08a6a20bf79976285608b Sim Zhen Quan <<EMAIL>> 1750697635 +0800	commit: Deployed to dev
bae75c01006d2769feb08a6a20bf79976285608b 795988ef2d130c68835c669641020632761d42cc Sim Zhen Quan <<EMAIL>> 1750737977 +0800	checkout: moving from dev to main
795988ef2d130c68835c669641020632761d42cc 795988ef2d130c68835c669641020632761d42cc Sim Zhen Quan <<EMAIL>> 1750737988 +0800	checkout: moving from main to staging/2025-06-24
795988ef2d130c68835c669641020632761d42cc cb0a584f8b79f78b51518b918d0b7135370d7141 Sim Zhen Quan <<EMAIL>> 1750738055 +0800	pull: Fast-forward
cb0a584f8b79f78b51518b918d0b7135370d7141 4046f07aa66283b97eaac3eb1070e2c6faa05aa1 Sim Zhen Quan <<EMAIL>> 1750738120 +0800	commit: Update CHANGELOG.md
4046f07aa66283b97eaac3eb1070e2c6faa05aa1 2c2a153f9a8f69f45c55011483d917f4cb15ff96 Sim Zhen Quan <<EMAIL>> 1750738387 +0800	checkout: moving from staging/2025-06-24 to issue-188-cocu-student-statistic-report-exclude-inactive-student-class
2c2a153f9a8f69f45c55011483d917f4cb15ff96 74093456bfa5fba163653cb606d8641baaed2930 Sim Zhen Quan <<EMAIL>> 1750738571 +0800	commit: Reviewed
74093456bfa5fba163653cb606d8641baaed2930 bae75c01006d2769feb08a6a20bf79976285608b Sim Zhen Quan <<EMAIL>> 1750738643 +0800	checkout: moving from issue-188-cocu-student-statistic-report-exclude-inactive-student-class to dev
bae75c01006d2769feb08a6a20bf79976285608b b6d06554806357c84a04d40d21b46293da0f689f Sim Zhen Quan <<EMAIL>> 1750738655 +0800	merge issue-188-cocu-student-statistic-report-exclude-inactive-student-class: Merge made by the 'ort' strategy.
b6d06554806357c84a04d40d21b46293da0f689f baa95fd0d9c518f4c641cace64d8f64c0dbba730 Sim Zhen Quan <<EMAIL>> 1750782315 +0800	checkout: moving from dev to ai-rnd
baa95fd0d9c518f4c641cace64d8f64c0dbba730 cdcba29c239680e465671d68d05bf337b4a38f59 Sim Zhen Quan <<EMAIL>> 1750782332 +0800	commit: WIP
cdcba29c239680e465671d68d05bf337b4a38f59 bed648034b11d920bc511436d28e291d03ba56be Sim Zhen Quan <<EMAIL>> 1751252230 +0800	pull: Fast-forward
bed648034b11d920bc511436d28e291d03ba56be ee0223fc12b22dcc69418add38627501968054fd Sim Zhen Quan <<EMAIL>> 1751252232 +0800	checkout: moving from ai-rnd to SKLEARN-502-newly-registered-student-report-enhancements
ee0223fc12b22dcc69418add38627501968054fd 8c5799e474b574b0a7e00305f15622a20c0afe2d Sim Zhen Quan <<EMAIL>> 1751253034 +0800	commit: Reviewed - Fixed Timezone
8c5799e474b574b0a7e00305f15622a20c0afe2d b6d06554806357c84a04d40d21b46293da0f689f Sim Zhen Quan <<EMAIL>> 1751253062 +0800	checkout: moving from SKLEARN-502-newly-registered-student-report-enhancements to dev
b6d06554806357c84a04d40d21b46293da0f689f b6d06554806357c84a04d40d21b46293da0f689f Sim Zhen Quan <<EMAIL>> 1751253097 +0800	checkout: moving from dev to pinhwa-demo-2025-07-05
b6d06554806357c84a04d40d21b46293da0f689f b6d06554806357c84a04d40d21b46293da0f689f Sim Zhen Quan <<EMAIL>> 1751255305 +0800	checkout: moving from pinhwa-demo-2025-07-05 to dev
b6d06554806357c84a04d40d21b46293da0f689f 93c55b63312dca0ae5fa56f502c1101015585313 Sim Zhen Quan <<EMAIL>> 1751255312 +0800	merge SKLEARN-502-newly-registered-student-report-enhancements: Merge made by the 'ort' strategy.
93c55b63312dca0ae5fa56f502c1101015585313 af12ff5ca74a718c008cbcf53852ae8b2e8d3601 Sim Zhen Quan <<EMAIL>> 1751262782 +0800	merge origin/SKLEARN-502-newly-registered-student-report-enhancements: Merge made by the 'ort' strategy.
af12ff5ca74a718c008cbcf53852ae8b2e8d3601 1a97268af3e65ea63c9c67640f03d0b1aee5acdb Sim Zhen Quan <<EMAIL>> 1751279266 +0800	commit: Deployed to dev
1a97268af3e65ea63c9c67640f03d0b1aee5acdb 6b43914c52c55937e02b76ad7a2984aa770ef771 Sim Zhen Quan <<EMAIL>> 1751279272 +0800	checkout: moving from dev to sklearn-498-issue-166-outstanding-balance-report
6b43914c52c55937e02b76ad7a2984aa770ef771 fa631d67f85d0ce7db9931e2caff235366d04d30 Sim Zhen Quan <<EMAIL>> 1751354908 +0800	commit: Reviewed
fa631d67f85d0ce7db9931e2caff235366d04d30 1a97268af3e65ea63c9c67640f03d0b1aee5acdb Sim Zhen Quan <<EMAIL>> 1751355190 +0800	checkout: moving from sklearn-498-issue-166-outstanding-balance-report to dev
1a97268af3e65ea63c9c67640f03d0b1aee5acdb 0bc1f67871d3fa45ad5c9fe0c5160fe42e077335 Sim Zhen Quan <<EMAIL>> 1751355190 +0800	merge sklearn-498-issue-166-outstanding-balance-report: Merge made by the 'ort' strategy.
0bc1f67871d3fa45ad5c9fe0c5160fe42e077335 56b5070b7baaf835d59bde5606ba2c4771e87d0f Sim Zhen Quan <<EMAIL>> 1751355252 +0800	commit: Updated CHANGELOG.dev.md
56b5070b7baaf835d59bde5606ba2c4771e87d0f 2c26a85fb77f85782dbde155a2f3b29a7b6fab10 Sim Zhen Quan <<EMAIL>> 1751355319 +0800	checkout: moving from dev to feature/enrollment-autocount
2c26a85fb77f85782dbde155a2f3b29a7b6fab10 c97f897a5f2a4559f724dbea6ba2d3365bb0797f Sim Zhen Quan <<EMAIL>> 1751355324 +0800	merge origin/main: Merge made by the 'ort' strategy.
c97f897a5f2a4559f724dbea6ba2d3365bb0797f f5c38e7c2d3d728729fd450312393aa1a316a6e6 Sim Zhen Quan <<EMAIL>> 1751363176 +0800	checkout: moving from feature/enrollment-autocount to issue-187-reward-punishment-reports-enhancement
f5c38e7c2d3d728729fd450312393aa1a316a6e6 b3eadec11e809e4a2992ce0dda8f7de86f801e7b Sim Zhen Quan <<EMAIL>> 1751363182 +0800	merge origin/main: Merge made by the 'ort' strategy.
b3eadec11e809e4a2992ce0dda8f7de86f801e7b f7e365da1b4d1e184913dc3600439f166cc12c05 Sim Zhen Quan <<EMAIL>> 1751364970 +0800	commit: Reviewed
f7e365da1b4d1e184913dc3600439f166cc12c05 56b5070b7baaf835d59bde5606ba2c4771e87d0f Sim Zhen Quan <<EMAIL>> 1751365013 +0800	checkout: moving from issue-187-reward-punishment-reports-enhancement to dev
56b5070b7baaf835d59bde5606ba2c4771e87d0f b2c89161504c49fda5215249dc43b31ca64dc370 Sim Zhen Quan <<EMAIL>> 1751365034 +0800	commit (merge): Merge
b2c89161504c49fda5215249dc43b31ca64dc370 fbe41fdf400bb8ca27f12d0c4a18354374baabc0 Sim Zhen Quan <<EMAIL>> 1751365267 +0800	commit: Deployed to dev
fbe41fdf400bb8ca27f12d0c4a18354374baabc0 74093456bfa5fba163653cb606d8641baaed2930 Sim Zhen Quan <<EMAIL>> 1751365279 +0800	checkout: moving from dev to issue-188-cocu-student-statistic-report-exclude-inactive-student-class
74093456bfa5fba163653cb606d8641baaed2930 fbe41fdf400bb8ca27f12d0c4a18354374baabc0 Sim Zhen Quan <<EMAIL>> 1751365294 +0800	checkout: moving from issue-188-cocu-student-statistic-report-exclude-inactive-student-class to dev
fbe41fdf400bb8ca27f12d0c4a18354374baabc0 4fa456aa11e7ad72b3ca859a6b9766f87ced030f Sim Zhen Quan <<EMAIL>> 1751365377 +0800	checkout: moving from dev to attendance-posting-enhance
4fa456aa11e7ad72b3ca859a6b9766f87ced030f c20361ac69ab9fd921e212eafcc28aae9ca7bd8a Sim Zhen Quan <<EMAIL>> 1751365384 +0800	merge origin/main: Merge made by the 'ort' strategy.
c20361ac69ab9fd921e212eafcc28aae9ca7bd8a c6ae06b853743ddbe13f7561114ff234c033763d Sim Zhen Quan <<EMAIL>> 1751367137 +0800	commit: Reviewed
c6ae06b853743ddbe13f7561114ff234c033763d fbe41fdf400bb8ca27f12d0c4a18354374baabc0 Sim Zhen Quan <<EMAIL>> 1751367207 +0800	checkout: moving from attendance-posting-enhance to dev
fbe41fdf400bb8ca27f12d0c4a18354374baabc0 362de1bc419d5c230017da7cac54307708bfe8d0 Sim Zhen Quan <<EMAIL>> 1751367212 +0800	merge attendance-posting-enhance: Merge made by the 'ort' strategy.
362de1bc419d5c230017da7cac54307708bfe8d0 3f959f5abc48d684dd53432f1be364834887bc28 Sim Zhen Quan <<EMAIL>> 1751367330 +0800	commit: Updated CHANGELOG.dev.md
3f959f5abc48d684dd53432f1be364834887bc28 e75d08812e307a84373106d31f2c56208337828a Sim Zhen Quan <<EMAIL>> 1751368109 +0800	commit: Bug fix
e75d08812e307a84373106d31f2c56208337828a bfbf2863e69cfe59f23f9a0b64f968e699e1457e Sim Zhen Quan <<EMAIL>> 1751368216 +0800	commit: Deployed to dev
bfbf2863e69cfe59f23f9a0b64f968e699e1457e f7e365da1b4d1e184913dc3600439f166cc12c05 Sim Zhen Quan <<EMAIL>> 1751368283 +0800	checkout: moving from dev to issue-187-reward-punishment-reports-enhancement
f7e365da1b4d1e184913dc3600439f166cc12c05 dec95388ded27ffb35724a9381d3b4c5c389b48f Sim Zhen Quan <<EMAIL>> 1751368301 +0800	cherry-pick: Bug fix
dec95388ded27ffb35724a9381d3b4c5c389b48f 795988ef2d130c68835c669641020632761d42cc Sim Zhen Quan <<EMAIL>> 1751385426 +0800	checkout: moving from issue-187-reward-punishment-reports-enhancement to main
795988ef2d130c68835c669641020632761d42cc 795988ef2d130c68835c669641020632761d42cc Sim Zhen Quan <<EMAIL>> 1751385658 +0800	reset: moving to HEAD
795988ef2d130c68835c669641020632761d42cc 4b724cbafed4ce7eb7e63c820536c3f2566c0b07 Sim Zhen Quan <<EMAIL>> 1751385663 +0800	pull: Fast-forward
4b724cbafed4ce7eb7e63c820536c3f2566c0b07 c4347f83081f19bfb2d651bf5c5c8d7cddb04c6c Sim Zhen Quan <<EMAIL>> 1751386668 +0800	commit: Deployed to prd
c4347f83081f19bfb2d651bf5c5c8d7cddb04c6c c97f897a5f2a4559f724dbea6ba2d3365bb0797f Sim Zhen Quan <<EMAIL>> 1751386679 +0800	checkout: moving from main to feature/enrollment-autocount
c97f897a5f2a4559f724dbea6ba2d3365bb0797f 823a80cca7d0e1278eda9fe88683bb46e5a1d07c Sim Zhen Quan <<EMAIL>> 1751386686 +0800	pull: Fast-forward
823a80cca7d0e1278eda9fe88683bb46e5a1d07c 930d48d1336444646e87fc95fa9c961e0b34c012 Sim Zhen Quan <<EMAIL>> 1751386693 +0800	merge origin/main: Merge made by the 'ort' strategy.
930d48d1336444646e87fc95fa9c961e0b34c012 c4347f83081f19bfb2d651bf5c5c8d7cddb04c6c Sim Zhen Quan <<EMAIL>> 1751423305 +0800	checkout: moving from feature/enrollment-autocount to main
c4347f83081f19bfb2d651bf5c5c8d7cddb04c6c c4347f83081f19bfb2d651bf5c5c8d7cddb04c6c Sim Zhen Quan <<EMAIL>> 1751423341 +0800	checkout: moving from main to SKLEARN-515-enrollment-reply-to-address
c4347f83081f19bfb2d651bf5c5c8d7cddb04c6c e0cf6bcb3c3c2b6ad75b2b49ae6724511cc14880 Sim Zhen Quan <<EMAIL>> 1751428783 +0800	commit: Dones
e0cf6bcb3c3c2b6ad75b2b49ae6724511cc14880 bfbf2863e69cfe59f23f9a0b64f968e699e1457e Sim Zhen Quan <<EMAIL>> 1751429295 +0800	checkout: moving from SKLEARN-515-enrollment-reply-to-address to dev
bfbf2863e69cfe59f23f9a0b64f968e699e1457e f8c2559bb694df239153afe7612dec88e04e998d Sim Zhen Quan <<EMAIL>> 1751429306 +0800	merge SKLEARN-515-enrollment-reply-to-address: Merge made by the 'ort' strategy.
f8c2559bb694df239153afe7612dec88e04e998d 0efc99bc528f438985f9065443c5e1f2a1dba3dc Sim Zhen Quan <<EMAIL>> 1751429406 +0800	commit: Updated CHANGELOG.dev.md
0efc99bc528f438985f9065443c5e1f2a1dba3dc 930d48d1336444646e87fc95fa9c961e0b34c012 Sim Zhen Quan <<EMAIL>> 1751429443 +0800	checkout: moving from dev to feature/enrollment-autocount
930d48d1336444646e87fc95fa9c961e0b34c012 e4c90bc9eb15a04a5ca111fa2385b1db331fc094 Sim Zhen Quan <<EMAIL>> 1751431774 +0800	commit: Cleanups
e4c90bc9eb15a04a5ca111fa2385b1db331fc094 0efc99bc528f438985f9065443c5e1f2a1dba3dc Sim Zhen Quan <<EMAIL>> 1751432034 +0800	checkout: moving from feature/enrollment-autocount to dev
0efc99bc528f438985f9065443c5e1f2a1dba3dc e625e23cac4480f8bfc053136f94efa43fc24d6e Sim Zhen Quan <<EMAIL>> 1751432042 +0800	merge origin/feature/enrollment-autocount: Merge made by the 'ort' strategy.
e625e23cac4480f8bfc053136f94efa43fc24d6e ca9a4ba27f9cbbb2e195431c7be6688428026453 Sim Zhen Quan <<EMAIL>> 1751441764 +0800	commit: Deployed to dev
ca9a4ba27f9cbbb2e195431c7be6688428026453 07afe8c737b55bcd221961083059cd34c8b134ae Sim Zhen Quan <<EMAIL>> 1751441790 +0800	checkout: moving from dev to SKLEARN-508-randomizer-script-5-for-demo
07afe8c737b55bcd221961083059cd34c8b134ae 37ad8acf3e1c18d55edb36abe34bef3b3bda8270 Sim Zhen Quan <<EMAIL>> 1751441796 +0800	merge origin/main: Merge made by the 'ort' strategy.
37ad8acf3e1c18d55edb36abe34bef3b3bda8270 b847b5cb560f4fdb7b56edb5210b9ac742d98b21 Sim Zhen Quan <<EMAIL>> 1751509917 +0800	commit: Added some comments
b847b5cb560f4fdb7b56edb5210b9ac742d98b21 b6d06554806357c84a04d40d21b46293da0f689f Sim Zhen Quan <<EMAIL>> 1751509932 +0800	checkout: moving from SKLEARN-508-randomizer-script-5-for-demo to pinhwa-demo-2025-07-05
b6d06554806357c84a04d40d21b46293da0f689f 23fa7efdf0b259ceafe82789d62af49f462c28a3 Sim Zhen Quan <<EMAIL>> 1751509958 +0800	commit (merge): Merge remote-tracking branch 'origin/main' into pinhwa-demo-2025-07-05
23fa7efdf0b259ceafe82789d62af49f462c28a3 23fa7efdf0b259ceafe82789d62af49f462c28a3 Sim Zhen Quan <<EMAIL>> 1751509987 +0800	checkout: moving from pinhwa-demo-2025-07-05 to staging/03-07-2025
23fa7efdf0b259ceafe82789d62af49f462c28a3 c6ae06b853743ddbe13f7561114ff234c033763d Sim Zhen Quan <<EMAIL>> 1751510118 +0800	checkout: moving from staging/03-07-2025 to attendance-posting-enhance
c6ae06b853743ddbe13f7561114ff234c033763d c4347f83081f19bfb2d651bf5c5c8d7cddb04c6c Sim Zhen Quan <<EMAIL>> 1751510197 +0800	checkout: moving from attendance-posting-enhance to main
c4347f83081f19bfb2d651bf5c5c8d7cddb04c6c c4347f83081f19bfb2d651bf5c5c8d7cddb04c6c Sim Zhen Quan <<EMAIL>> 1751510234 +0800	checkout: moving from main to staging/03-07-2025
c4347f83081f19bfb2d651bf5c5c8d7cddb04c6c a88056a98907804c2c1a8120d258b3fb7debb615 Sim Zhen Quan <<EMAIL>> 1751510266 +0800	merge attendance-posting-enhance: Merge made by the 'ort' strategy.
a88056a98907804c2c1a8120d258b3fb7debb615 170ab7af0b96cfb63ced5561a896b43689af8006 Sim Zhen Quan <<EMAIL>> 1751510979 +0800	checkout: moving from staging/03-07-2025 to fix/daily-collection-report-column
170ab7af0b96cfb63ced5561a896b43689af8006 6c7dea3e321e45d07233958e3ccd9447600c81d0 Sim Zhen Quan <<EMAIL>> 1751510985 +0800	merge origin/main: Merge made by the 'ort' strategy.
6c7dea3e321e45d07233958e3ccd9447600c81d0 c2b227cc1f524f6f6eba723bcf06520ea59158ee Sim Zhen Quan <<EMAIL>> 1751517773 +0800	commit: Reviewed
c2b227cc1f524f6f6eba723bcf06520ea59158ee ca9a4ba27f9cbbb2e195431c7be6688428026453 Sim Zhen Quan <<EMAIL>> 1751517808 +0800	checkout: moving from fix/daily-collection-report-column to dev
ca9a4ba27f9cbbb2e195431c7be6688428026453 d0e911b4dbadaed97f1efc1cd7f5bda9dde29f05 Sim Zhen Quan <<EMAIL>> 1751517841 +0800	commit (merge): Merge remote-tracking branch 'origin/fix/daily-collection-report-column' into dev
d0e911b4dbadaed97f1efc1cd7f5bda9dde29f05 22da9988b5d69ab7b3eaea41f7f735922848c964 Sim Zhen Quan <<EMAIL>> 1751527610 +0800	commit: Deployed to dev
22da9988b5d69ab7b3eaea41f7f735922848c964 5d9fbe4b740aecbfc43ef56c1def006e01c07b11 Sim Zhen Quan <<EMAIL>> 1751529723 +0800	checkout: moving from dev to feature/print-enrollment-details
5d9fbe4b740aecbfc43ef56c1def006e01c07b11 51858e99fea19de4ca9b565a0f3a075b35d0c2f2 Sim Zhen Quan <<EMAIL>> 1751529731 +0800	merge origin/main: Merge made by the 'ort' strategy.
51858e99fea19de4ca9b565a0f3a075b35d0c2f2 edd0075be8dd90d7697d8a4134f1e205757324eb Sim Zhen Quan <<EMAIL>> 1751595539 +0800	commit: Reviewed
edd0075be8dd90d7697d8a4134f1e205757324eb 22da9988b5d69ab7b3eaea41f7f735922848c964 Sim Zhen Quan <<EMAIL>> 1751595605 +0800	checkout: moving from feature/print-enrollment-details to dev
22da9988b5d69ab7b3eaea41f7f735922848c964 061065453357789fc16a7bf261a3a613913791ca Sim Zhen Quan <<EMAIL>> 1751595804 +0800	commit (merge): Merge branch 'feature/print-enrollment-details' into dev
061065453357789fc16a7bf261a3a613913791ca 6b36c5d3732678875ff45509648d208f91c755fb Sim Zhen Quan <<EMAIL>> 1751597287 +0800	commit: Deployed to dev
6b36c5d3732678875ff45509648d208f91c755fb 9fd0b61458ef38186c3ed2475f3d51baee94af93 Sim Zhen Quan <<EMAIL>> 1751599653 +0800	checkout: moving from dev to SKLEARN-495-exam-mark-entry-enhancements
9fd0b61458ef38186c3ed2475f3d51baee94af93 28ab4600b8d3d6330762c0847f18c16fda9bdf0e Sim Zhen Quan <<EMAIL>> 1751599660 +0800	merge origin/main: Merge made by the 'ort' strategy.
28ab4600b8d3d6330762c0847f18c16fda9bdf0e b847b5cb560f4fdb7b56edb5210b9ac742d98b21 Sim Zhen Quan <<EMAIL>> 1751614311 +0800	checkout: moving from SKLEARN-495-exam-mark-entry-enhancements to SKLEARN-508-randomizer-script-5-for-demo
b847b5cb560f4fdb7b56edb5210b9ac742d98b21 ff8c58b283fce8f9c10e659dac005b46d6de0ab3 Sim Zhen Quan <<EMAIL>> 1751614317 +0800	pull: Fast-forward
ff8c58b283fce8f9c10e659dac005b46d6de0ab3 798878ba44a94ca075c44c6d3c0baa2166fd35a9 Sim Zhen Quan <<EMAIL>> 1751942265 +0800	checkout: moving from SKLEARN-508-randomizer-script-5-for-demo to issue-199-ecommerce-side-nav-canteen-and-bookstore-permissions
798878ba44a94ca075c44c6d3c0baa2166fd35a9 c4347f83081f19bfb2d651bf5c5c8d7cddb04c6c Sim Zhen Quan <<EMAIL>> 1751942275 +0800	checkout: moving from issue-199-ecommerce-side-nav-canteen-and-bookstore-permissions to main
c4347f83081f19bfb2d651bf5c5c8d7cddb04c6c c4347f83081f19bfb2d651bf5c5c8d7cddb04c6c Sim Zhen Quan <<EMAIL>> 1751942292 +0800	checkout: moving from main to lucas/fix-library-report-userable
c4347f83081f19bfb2d651bf5c5c8d7cddb04c6c 1e3743e789c98708379114cd0bcfb0962d7f47ad Sim Zhen Quan <<EMAIL>> 1751942835 +0800	commit: Fix library report bug
1e3743e789c98708379114cd0bcfb0962d7f47ad d9b5c13a4026582c0df094fe8cb302cf67731fde Sim Zhen Quan <<EMAIL>> 1751942980 +0800	checkout: moving from lucas/fix-library-report-userable to issue-185-student-daily-arrival-report-enhancements
d9b5c13a4026582c0df094fe8cb302cf67731fde 51756c3c9fe7e7f7e67cad92b236e539ef8e00e7 Sim Zhen Quan <<EMAIL>> 1751942986 +0800	merge origin/main: Merge made by the 'ort' strategy.
51756c3c9fe7e7f7e67cad92b236e539ef8e00e7 b925ca9f825a90742f8369c5a351b7c5aa014609 Sim Zhen Quan <<EMAIL>> 1751943571 +0800	commit: Reviewed
b925ca9f825a90742f8369c5a351b7c5aa014609 6b36c5d3732678875ff45509648d208f91c755fb Sim Zhen Quan <<EMAIL>> 1751943589 +0800	checkout: moving from issue-185-student-daily-arrival-report-enhancements to dev
6b36c5d3732678875ff45509648d208f91c755fb eb0d1de97e3c428790ba9038ffe461d9279dfe10 Sim Zhen Quan <<EMAIL>> 1751943594 +0800	merge issue-185-student-daily-arrival-report-enhancements: Merge made by the 'ort' strategy.
eb0d1de97e3c428790ba9038ffe461d9279dfe10 dde8b47d96ad0a480b38674ba2fe2125ad38dbe8 Sim Zhen Quan <<EMAIL>> 1751943687 +0800	commit: Update CHANGELOG.dev.md
dde8b47d96ad0a480b38674ba2fe2125ad38dbe8 798878ba44a94ca075c44c6d3c0baa2166fd35a9 Sim Zhen Quan <<EMAIL>> 1751943699 +0800	checkout: moving from dev to issue-199-ecommerce-side-nav-canteen-and-bookstore-permissions
798878ba44a94ca075c44c6d3c0baa2166fd35a9 678f3caf349c552d47738c3d5977e386deda7aee Sim Zhen Quan <<EMAIL>> 1751943704 +0800	merge origin/main: Merge made by the 'ort' strategy.
678f3caf349c552d47738c3d5977e386deda7aee 65dab3656689a4a26befff94f17ee073a50bbcb5 Sim Zhen Quan <<EMAIL>> 1751944096 +0800	commit: Reviewed
65dab3656689a4a26befff94f17ee073a50bbcb5 dde8b47d96ad0a480b38674ba2fe2125ad38dbe8 Sim Zhen Quan <<EMAIL>> 1751944109 +0800	checkout: moving from issue-199-ecommerce-side-nav-canteen-and-bookstore-permissions to dev
dde8b47d96ad0a480b38674ba2fe2125ad38dbe8 b6b04e288feb717b95d60a0c5eb7cf667006ae59 Sim Zhen Quan <<EMAIL>> 1751944115 +0800	merge issue-199-ecommerce-side-nav-canteen-and-bookstore-permissions: Merge made by the 'ort' strategy.
b6b04e288feb717b95d60a0c5eb7cf667006ae59 a2cb57ff5b6f913657f10b6a0070ad31215ae5c1 Sim Zhen Quan <<EMAIL>> 1751944215 +0800	commit: Updated CHANGELOG.dev.md
a2cb57ff5b6f913657f10b6a0070ad31215ae5c1 f953ba1b0902324f8a1ceef929508d4059a7f212 Sim Zhen Quan <<EMAIL>> 1751944243 +0800	checkout: moving from dev to fix/autocount-report
f953ba1b0902324f8a1ceef929508d4059a7f212 815613cac2a41aa55d033736fa4898579d232605 Sim Zhen Quan <<EMAIL>> 1751944248 +0800	merge origin/main: Merge made by the 'ort' strategy.
815613cac2a41aa55d033736fa4898579d232605 a2cb57ff5b6f913657f10b6a0070ad31215ae5c1 Sim Zhen Quan <<EMAIL>> 1751945111 +0800	checkout: moving from fix/autocount-report to dev
a2cb57ff5b6f913657f10b6a0070ad31215ae5c1 a754d4e315dbaeed6781d47c5c3cceeb019494c9 Sim Zhen Quan <<EMAIL>> 1751947028 +0800	commit: Updated CHANGELOG.dev.md
a754d4e315dbaeed6781d47c5c3cceeb019494c9 e4c90bc9eb15a04a5ca111fa2385b1db331fc094 Sim Zhen Quan <<EMAIL>> 1751947048 +0800	checkout: moving from dev to feature/enrollment-autocount
e4c90bc9eb15a04a5ca111fa2385b1db331fc094 a9e1276ff82205b349b2f837041101cbf29389f1 Sim Zhen Quan <<EMAIL>> 1751947054 +0800	pull: Fast-forward
a9e1276ff82205b349b2f837041101cbf29389f1 a754d4e315dbaeed6781d47c5c3cceeb019494c9 Sim Zhen Quan <<EMAIL>> 1751947129 +0800	checkout: moving from feature/enrollment-autocount to dev
a754d4e315dbaeed6781d47c5c3cceeb019494c9 1a7e61fc1f800fac1fae6c31b99b47c8fa6abd2c Sim Zhen Quan <<EMAIL>> 1751947136 +0800	merge feature/enrollment-autocount: Merge made by the 'ort' strategy.
1a7e61fc1f800fac1fae6c31b99b47c8fa6abd2c ea6a5e984c8b303c734519d767978efa1154ba25 Sim Zhen Quan <<EMAIL>> 1751947340 +0800	commit: Deployed to dev
ea6a5e984c8b303c734519d767978efa1154ba25 8bfd50d2bb7bed686043c0bb3f2a22ee3825de67 Sim Zhen Quan <<EMAIL>> 1751948071 +0800	merge fix/autocount-report: Merge made by the 'ort' strategy.
8bfd50d2bb7bed686043c0bb3f2a22ee3825de67 926abdcfe38745d98e6cca9de07069ddbb164a5d Sim Zhen Quan <<EMAIL>> 1751960385 +0800	checkout: moving from dev to enhancement/add-primary-class-to-absence-report
926abdcfe38745d98e6cca9de07069ddbb164a5d ca7782e68d29296e3386fbe550715faa07da3908 Sim Zhen Quan <<EMAIL>> 1751960544 +0800	commit (merge): Merge remote-tracking branch 'origin/main' into enhancement/add-primary-class-to-absence-report
ca7782e68d29296e3386fbe550715faa07da3908 c770b64e6397e4e08f184e6e8a664a08093c6b97 Sim Zhen Quan <<EMAIL>> 1751962024 +0800	commit: Reviewed
c770b64e6397e4e08f184e6e8a664a08093c6b97 8bfd50d2bb7bed686043c0bb3f2a22ee3825de67 Sim Zhen Quan <<EMAIL>> 1751962108 +0800	checkout: moving from enhancement/add-primary-class-to-absence-report to dev
8bfd50d2bb7bed686043c0bb3f2a22ee3825de67 589f92f218e2a150732648c3e1f62ce8d7b93a3b Sim Zhen Quan <<EMAIL>> 1751962114 +0800	merge enhancement/add-primary-class-to-absence-report: Merge made by the 'ort' strategy.
589f92f218e2a150732648c3e1f62ce8d7b93a3b 9bb0f3bd95affb2c47d240a476dcea902ceed56c Sim Zhen Quan <<EMAIL>> 1751962182 +0800	commit: Updated CHANGELOG.dev.md
9bb0f3bd95affb2c47d240a476dcea902ceed56c 84f043941dafbab95c1eeb6861c71def21e6311b Sim Zhen Quan <<EMAIL>> 1751962261 +0800	checkout: moving from dev to enhancement/student-attendance-report
84f043941dafbab95c1eeb6861c71def21e6311b 84f043941dafbab95c1eeb6861c71def21e6311b Sim Zhen Quan <<EMAIL>> 1751962269 +0800	checkout: moving from enhancement/student-attendance-report to enhancement/student-attendance-report
84f043941dafbab95c1eeb6861c71def21e6311b 2e71c5896be8f328e11f6368f323dd4d3c0acf12 Sim Zhen Quan <<EMAIL>> 1751962365 +0800	commit (merge): Merge remote-tracking branch 'origin/main' into enhancement/student-attendance-report
2e71c5896be8f328e11f6368f323dd4d3c0acf12 2e71c5896be8f328e11f6368f323dd4d3c0acf12 Sim Zhen Quan <<EMAIL>> 1751963893 +0800	checkout: moving from enhancement/student-attendance-report to enhancement/student-attendance-report
2e71c5896be8f328e11f6368f323dd4d3c0acf12 5b1767f5fa64d002ecd26a2b094bed4051fff447 Sim Zhen Quan <<EMAIL>> 1751964964 +0800	commit: Reviewed
5b1767f5fa64d002ecd26a2b094bed4051fff447 9bb0f3bd95affb2c47d240a476dcea902ceed56c Sim Zhen Quan <<EMAIL>> 1751964985 +0800	checkout: moving from enhancement/student-attendance-report to dev
9bb0f3bd95affb2c47d240a476dcea902ceed56c 1f640c9f17905d8ee83a787658096b46fa6d4741 Sim Zhen Quan <<EMAIL>> 1751964990 +0800	merge enhancement/student-attendance-report: Merge made by the 'ort' strategy.
1f640c9f17905d8ee83a787658096b46fa6d4741 5b1767f5fa64d002ecd26a2b094bed4051fff447 Sim Zhen Quan <<EMAIL>> 1751965217 +0800	checkout: moving from dev to enhancement/student-attendance-report
5b1767f5fa64d002ecd26a2b094bed4051fff447 df4857ec1e4837fe044fed28060240c0ab1db7f3 Sim Zhen Quan <<EMAIL>> 1751966564 +0800	commit: Reviewed
df4857ec1e4837fe044fed28060240c0ab1db7f3 1f640c9f17905d8ee83a787658096b46fa6d4741 Sim Zhen Quan <<EMAIL>> 1751966626 +0800	checkout: moving from enhancement/student-attendance-report to dev
1f640c9f17905d8ee83a787658096b46fa6d4741 0dd589b808f87c0c1c616fc54f1a058028452417 Sim Zhen Quan <<EMAIL>> 1751966632 +0800	merge enhancement/student-attendance-report: Merge made by the 'ort' strategy.
0dd589b808f87c0c1c616fc54f1a058028452417 2307c0893582ebb60cd959c670cca253688a3017 Sim Zhen Quan <<EMAIL>> 1751967409 +0800	commit: Deployed to dev
2307c0893582ebb60cd959c670cca253688a3017 ff5de69922068777b4c7eb0379fc68deb9276880 Sim Zhen Quan <<EMAIL>> 1751967422 +0800	checkout: moving from dev to fix/update-enrollment
ff5de69922068777b4c7eb0379fc68deb9276880 daf095df863684d74bcb7d32a8a7edbbd9b2308d Sim Zhen Quan <<EMAIL>> 1751967429 +0800	merge origin/main: Merge made by the 'ort' strategy.
daf095df863684d74bcb7d32a8a7edbbd9b2308d 2307c0893582ebb60cd959c670cca253688a3017 Sim Zhen Quan <<EMAIL>> 1751992754 +0800	checkout: moving from fix/update-enrollment to dev
2307c0893582ebb60cd959c670cca253688a3017 2307c0893582ebb60cd959c670cca253688a3017 Sim Zhen Quan <<EMAIL>> 1751993092 +0800	checkout: moving from dev to dev
2307c0893582ebb60cd959c670cca253688a3017 c4347f83081f19bfb2d651bf5c5c8d7cddb04c6c Sim Zhen Quan <<EMAIL>> 1751993106 +0800	checkout: moving from dev to main
c4347f83081f19bfb2d651bf5c5c8d7cddb04c6c 19ad947e3188e96aab775af3c9c1d14847291ddf Sim Zhen Quan <<EMAIL>> 1751993113 +0800	pull: Fast-forward
19ad947e3188e96aab775af3c9c1d14847291ddf 900d2c0d9ab9988ec9aaed029338c34c63760e29 Sim Zhen Quan <<EMAIL>> 1751994256 +0800	commit: Deployed to prd
900d2c0d9ab9988ec9aaed029338c34c63760e29 b925ca9f825a90742f8369c5a351b7c5aa014609 Sim Zhen Quan <<EMAIL>> 1751994276 +0800	checkout: moving from main to issue-185-student-daily-arrival-report-enhancements
b925ca9f825a90742f8369c5a351b7c5aa014609 1bf12407aa626383b99ba94c9a242a5a7fa9a393 Sim Zhen Quan <<EMAIL>> 1751994441 +0800	merge origin/main: Merge made by the 'ort' strategy.
1bf12407aa626383b99ba94c9a242a5a7fa9a393 c770b64e6397e4e08f184e6e8a664a08093c6b97 Sim Zhen Quan <<EMAIL>> 1751994738 +0800	checkout: moving from issue-185-student-daily-arrival-report-enhancements to enhancement/add-primary-class-to-absence-report
c770b64e6397e4e08f184e6e8a664a08093c6b97 9a1e78c9a4118c537657942d2a90e2615514df2a Sim Zhen Quan <<EMAIL>> 1751994744 +0800	merge origin/main: Merge made by the 'ort' strategy.
9a1e78c9a4118c537657942d2a90e2615514df2a a4040e1401d8067c52cf85af7d819d59856f641c Sim Zhen Quan <<EMAIL>> 1751995044 +0800	commit: Sort by class name then primary class
a4040e1401d8067c52cf85af7d819d59856f641c 2307c0893582ebb60cd959c670cca253688a3017 Sim Zhen Quan <<EMAIL>> 1751995064 +0800	checkout: moving from enhancement/add-primary-class-to-absence-report to dev
2307c0893582ebb60cd959c670cca253688a3017 e6e62ce3d54f8c49b6724ddaa61ccdc21c7ed751 Sim Zhen Quan <<EMAIL>> 1751995068 +0800	merge enhancement/add-primary-class-to-absence-report: Merge made by the 'ort' strategy.
e6e62ce3d54f8c49b6724ddaa61ccdc21c7ed751 df4857ec1e4837fe044fed28060240c0ab1db7f3 Sim Zhen Quan <<EMAIL>> 1751995083 +0800	checkout: moving from dev to enhancement/student-attendance-report
df4857ec1e4837fe044fed28060240c0ab1db7f3 7d28e73319d870a8ebd5935af4bfac2ced4ff60e Sim Zhen Quan <<EMAIL>> 1751995086 +0800	merge origin/main: Merge made by the 'ort' strategy.
7d28e73319d870a8ebd5935af4bfac2ced4ff60e 12c5405e6063822fa58a117c5231a6e53b278a85 Sim Zhen Quan <<EMAIL>> 1751995341 +0800	commit: Sort by class name then student name
12c5405e6063822fa58a117c5231a6e53b278a85 e6e62ce3d54f8c49b6724ddaa61ccdc21c7ed751 Sim Zhen Quan <<EMAIL>> 1751995356 +0800	checkout: moving from enhancement/student-attendance-report to dev
e6e62ce3d54f8c49b6724ddaa61ccdc21c7ed751 0357e40a529c38c74393c47593f123208c9c74dc Sim Zhen Quan <<EMAIL>> 1751995356 +0800	merge enhancement/student-attendance-report: Merge made by the 'ort' strategy.
0357e40a529c38c74393c47593f123208c9c74dc db64315668c5b845b080b351a3cfc2e855395453 Sim Zhen Quan <<EMAIL>> 1751995795 +0800	commit: Deployed to dev
db64315668c5b845b080b351a3cfc2e855395453 0b2bf67bea9cd65fcad26d519af5665eade49452 Sim Zhen Quan <<EMAIL>> 1752027821 +0800	checkout: moving from dev to fix-billing-doc-ref-no-duplicate
0b2bf67bea9cd65fcad26d519af5665eade49452 991d59bac7d384db3a936c7c130fa4405dca8c6d Sim Zhen Quan <<EMAIL>> 1752027828 +0800	merge origin/main: Merge made by the 'ort' strategy.
991d59bac7d384db3a936c7c130fa4405dca8c6d 33101ef8808b94c9b7bfdd1baa826de62c4977bd Sim Zhen Quan <<EMAIL>> 1752028980 +0800	commit: Reviewed
33101ef8808b94c9b7bfdd1baa826de62c4977bd db64315668c5b845b080b351a3cfc2e855395453 Sim Zhen Quan <<EMAIL>> 1752029043 +0800	checkout: moving from fix-billing-doc-ref-no-duplicate to dev
db64315668c5b845b080b351a3cfc2e855395453 5657e1e06b6b9b207dd15eb19257b87e11b90c78 Sim Zhen Quan <<EMAIL>> 1752029424 +0800	commit (merge): Merge branch 'fix-billing-doc-ref-no-duplicate' into dev
5657e1e06b6b9b207dd15eb19257b87e11b90c78 197763405bba89a68ca07b1006d26da147426f1a Sim Zhen Quan <<EMAIL>> 1752029818 +0800	commit: Deployed to DEV
197763405bba89a68ca07b1006d26da147426f1a a78aadd3dd3c7dcb61332f695057d25a2093e87e Sim Zhen Quan <<EMAIL>> 1752055073 +0800	checkout: moving from dev to fix/guardian-type-required
a78aadd3dd3c7dcb61332f695057d25a2093e87e 9dd01e589b3bdf775c38613162c1cbe831dc9ef4 Sim Zhen Quan <<EMAIL>> 1752055078 +0800	merge origin/main: Merge made by the 'ort' strategy.
9dd01e589b3bdf775c38613162c1cbe831dc9ef4 900d2c0d9ab9988ec9aaed029338c34c63760e29 Sim Zhen Quan <<EMAIL>> 1752077317 +0800	checkout: moving from fix/guardian-type-required to main
900d2c0d9ab9988ec9aaed029338c34c63760e29 900d2c0d9ab9988ec9aaed029338c34c63760e29 Sim Zhen Quan <<EMAIL>> 1752077336 +0800	checkout: moving from main to staging/2025-07-10
900d2c0d9ab9988ec9aaed029338c34c63760e29 900d2c0d9ab9988ec9aaed029338c34c63760e29 Sim Zhen Quan <<EMAIL>> 1752078367 +0800	checkout: moving from staging/2025-07-10 to staging/2025-07-10
900d2c0d9ab9988ec9aaed029338c34c63760e29 ba2cb89e1c1fb8644044be5a5dbba5998ed99fc7 Sim Zhen Quan <<EMAIL>> 1752078373 +0800	pull: Fast-forward
ba2cb89e1c1fb8644044be5a5dbba5998ed99fc7 536f2a17d963adc26faf4670211eb07170e77e3c Sim Zhen Quan <<EMAIL>> 1752078832 +0800	commit: Bug fixes
536f2a17d963adc26faf4670211eb07170e77e3c 900d2c0d9ab9988ec9aaed029338c34c63760e29 Sim Zhen Quan <<EMAIL>> 1752078853 +0800	checkout: moving from staging/2025-07-10 to main
900d2c0d9ab9988ec9aaed029338c34c63760e29 1372f76cc601d069d0298356212fb87ce6eb723d Sim Zhen Quan <<EMAIL>> 1752078859 +0800	pull: Fast-forward
1372f76cc601d069d0298356212fb87ce6eb723d 737128d9700936040f5a1c7300d152b07bdcc1ff Sim Zhen Quan <<EMAIL>> 1752114279 +0800	commit: Deployed to prd
737128d9700936040f5a1c7300d152b07bdcc1ff daf095df863684d74bcb7d32a8a7edbbd9b2308d Sim Zhen Quan <<EMAIL>> 1752116784 +0800	checkout: moving from main to fix/update-enrollment
daf095df863684d74bcb7d32a8a7edbbd9b2308d 967c99787b58ab5dcf1f1ed5613ea4146dedbf92 Sim Zhen Quan <<EMAIL>> 1752116790 +0800	merge origin/main: Merge made by the 'ort' strategy.
967c99787b58ab5dcf1f1ed5613ea4146dedbf92 197763405bba89a68ca07b1006d26da147426f1a Sim Zhen Quan <<EMAIL>> 1752117379 +0800	checkout: moving from fix/update-enrollment to dev
197763405bba89a68ca07b1006d26da147426f1a 98d939587f861828b6d22e0cd0cfb0e5dc5d5bf8 Sim Zhen Quan <<EMAIL>> 1752117387 +0800	merge fix/update-enrollment: Merge made by the 'ort' strategy.
98d939587f861828b6d22e0cd0cfb0e5dc5d5bf8 bc22f812aee5438574bfc363f9bce2a84d5a263e Sim Zhen Quan <<EMAIL>> 1752117454 +0800	commit: Updated CHANGELOG.dev.md
bc22f812aee5438574bfc363f9bce2a84d5a263e 39357e38de25aa988bd70fe734149b1a4fb816b8 Sim Zhen Quan <<EMAIL>> 1752119734 +0800	checkout: moving from dev to fix/first-batch-changes
39357e38de25aa988bd70fe734149b1a4fb816b8 7b7c0e0a9fcbdbdb464f5ca8a709e4cfd1ee0ae4 Sim Zhen Quan <<EMAIL>> 1752119740 +0800	merge origin/main: Merge made by the 'ort' strategy.
7b7c0e0a9fcbdbdb464f5ca8a709e4cfd1ee0ae4 587b91a1e060b54fca83f39ff71f6acaf86d7236 Sim Zhen Quan <<EMAIL>> 1752122051 +0800	commit: Reviewed
587b91a1e060b54fca83f39ff71f6acaf86d7236 31f9665386932896515b1473d2a4dfefc290ce3c Sim Zhen Quan <<EMAIL>> 1752122080 +0800	checkout: moving from fix/first-batch-changes to fix/second-excel-import-changes
31f9665386932896515b1473d2a4dfefc290ce3c be78db1f2615c1d90ffd736840801b652ed6cc9d Sim Zhen Quan <<EMAIL>> 1752122086 +0800	merge origin/main: Merge made by the 'ort' strategy.
be78db1f2615c1d90ffd736840801b652ed6cc9d bc22f812aee5438574bfc363f9bce2a84d5a263e Sim Zhen Quan <<EMAIL>> 1752122175 +0800	checkout: moving from fix/second-excel-import-changes to dev
bc22f812aee5438574bfc363f9bce2a84d5a263e be78db1f2615c1d90ffd736840801b652ed6cc9d Sim Zhen Quan <<EMAIL>> 1752122206 +0800	checkout: moving from dev to fix/second-excel-import-changes
be78db1f2615c1d90ffd736840801b652ed6cc9d 097d3c42fe3996ba6e83b3521ff6d8480bf9eec0 Sim Zhen Quan <<EMAIL>> 1752122211 +0800	pull: Fast-forward
097d3c42fe3996ba6e83b3521ff6d8480bf9eec0 bc22f812aee5438574bfc363f9bce2a84d5a263e Sim Zhen Quan <<EMAIL>> 1752122253 +0800	checkout: moving from fix/second-excel-import-changes to dev
bc22f812aee5438574bfc363f9bce2a84d5a263e 982f4ab47e2b7fb45695f89231c72677c9013283 Sim Zhen Quan <<EMAIL>> 1752122259 +0800	merge fix/second-excel-import-changes: Merge made by the 'ort' strategy.
982f4ab47e2b7fb45695f89231c72677c9013283 57bc1a48bd932aa60c1ab10874caadc6a3b3fef4 Sim Zhen Quan <<EMAIL>> 1752122596 +0800	commit: Deployed to dev
57bc1a48bd932aa60c1ab10874caadc6a3b3fef4 46a3551b10fd939bf7c5f88e14706bfe1c65edd9 Sim Zhen Quan <<EMAIL>> 1752122619 +0800	checkout: moving from dev to enhancement/show-discount-and-scholarship
46a3551b10fd939bf7c5f88e14706bfe1c65edd9 02df5ff9339a03794f2b83cff149b998ddbe6165 Sim Zhen Quan <<EMAIL>> 1752122626 +0800	merge origin/main: Merge made by the 'ort' strategy.
02df5ff9339a03794f2b83cff149b998ddbe6165 02df5ff9339a03794f2b83cff149b998ddbe6165 Sim Zhen Quan <<EMAIL>> 1752137951 +0800	checkout: moving from enhancement/show-discount-and-scholarship to enhancement/show-discount-and-scholarship
02df5ff9339a03794f2b83cff149b998ddbe6165 c73dd127fafa20efc31e824b901a0ab292e5dab3 Sim Zhen Quan <<EMAIL>> 1752139835 +0800	commit: Reviewed
c73dd127fafa20efc31e824b901a0ab292e5dab3 57bc1a48bd932aa60c1ab10874caadc6a3b3fef4 Sim Zhen Quan <<EMAIL>> 1752139862 +0800	checkout: moving from enhancement/show-discount-and-scholarship to dev
57bc1a48bd932aa60c1ab10874caadc6a3b3fef4 248dd19cd4552a11f645404ee601fe49178794bf Sim Zhen Quan <<EMAIL>> 1752139875 +0800	merge enhancement/show-discount-and-scholarship: Merge made by the 'ort' strategy.
248dd19cd4552a11f645404ee601fe49178794bf 03c12597283b4f9619efa1333e1be28c9f6916fa Sim Zhen Quan <<EMAIL>> 1752169154 +0800	commit: Deployed to dev
03c12597283b4f9619efa1333e1be28c9f6916fa 737128d9700936040f5a1c7300d152b07bdcc1ff Sim Zhen Quan <<EMAIL>> 1752169159 +0800	checkout: moving from dev to main
737128d9700936040f5a1c7300d152b07bdcc1ff b93dadc35d3ba50e69e76ead1d518b52fce6f12c Sim Zhen Quan <<EMAIL>> 1752169164 +0800	pull: Fast-forward
b93dadc35d3ba50e69e76ead1d518b52fce6f12c 0adaf51e89578618b39f1eb44766db583c2f11f9 Sim Zhen Quan <<EMAIL>> 1752201246 +0800	commit: Deployed to prd
0adaf51e89578618b39f1eb44766db583c2f11f9 61cf067bda046fdd6acf1667d911d778a8dcca3b Sim Zhen Quan <<EMAIL>> 1752225130 +0800	checkout: moving from main to issue-179-hostel-stay-back-report-include-outing-students
61cf067bda046fdd6acf1667d911d778a8dcca3b defebc0aa1998c33185373932f4cb0cdc1dd2ba7 Sim Zhen Quan <<EMAIL>> 1752225141 +0800	merge origin/main: Merge made by the 'ort' strategy.
defebc0aa1998c33185373932f4cb0cdc1dd2ba7 67df483a677a550ceecb9c89deb10fdf44aada12 Sim Zhen Quan <<EMAIL>> 1752225863 +0800	commit: Reviewed
67df483a677a550ceecb9c89deb10fdf44aada12 03c12597283b4f9619efa1333e1be28c9f6916fa Sim Zhen Quan <<EMAIL>> 1752225888 +0800	checkout: moving from issue-179-hostel-stay-back-report-include-outing-students to dev
03c12597283b4f9619efa1333e1be28c9f6916fa 3085dda18bdc53a0b41e417438c7e84b1bca8808 Sim Zhen Quan <<EMAIL>> 1752225934 +0800	commit (merge): Merge branch 'issue-179-hostel-stay-back-report-include-outing-students' into dev
3085dda18bdc53a0b41e417438c7e84b1bca8808 8923990b97e8cbef955c4397d52a88c24f58d890 Sim Zhen Quan <<EMAIL>> 1752225995 +0800	commit: Update CHANGELOG.dev.md
8923990b97e8cbef955c4397d52a88c24f58d890 b26fcba86b44e20ddaaa3b88b1d900fcea1e71f5 Sim Zhen Quan <<EMAIL>> 1752226032 +0800	merge SKLEARN-495-exam-mark-entry-enhancements: Merge made by the 'ort' strategy.
b26fcba86b44e20ddaaa3b88b1d900fcea1e71f5 6faa89ed56f6c6356c1eda88cdbcb690cfa7338b Sim Zhen Quan <<EMAIL>> 1752226134 +0800	commit: Update CHANGELOG.dev.md
6faa89ed56f6c6356c1eda88cdbcb690cfa7338b ca126c9c72294cba122c48d7cc6a1e5a5c817f5e Sim Zhen Quan <<EMAIL>> 1752226163 +0800	checkout: moving from dev to SKLEARN-496-examination-result-by-class-report-enhancements
ca126c9c72294cba122c48d7cc6a1e5a5c817f5e 9470045562b20a1e759cc0a3b33e81280420fba5 Sim Zhen Quan <<EMAIL>> 1752226169 +0800	merge origin/main: Merge made by the 'ort' strategy.
9470045562b20a1e759cc0a3b33e81280420fba5 6faa89ed56f6c6356c1eda88cdbcb690cfa7338b Sim Zhen Quan <<EMAIL>> 1752226641 +0800	checkout: moving from SKLEARN-496-examination-result-by-class-report-enhancements to dev
6faa89ed56f6c6356c1eda88cdbcb690cfa7338b e37ac652c52824b701434bc4c734a3e208601a38 Sim Zhen Quan <<EMAIL>> 1752226645 +0800	merge SKLEARN-496-examination-result-by-class-report-enhancements: Merge made by the 'ort' strategy.
e37ac652c52824b701434bc4c734a3e208601a38 afcd1b2b510f8eda7f77fe4da30095569a0748c0 Sim Zhen Quan <<EMAIL>> 1752226725 +0800	commit: Update CHANGELOG.dev.md
afcd1b2b510f8eda7f77fe4da30095569a0748c0 424988a97f9690e51ff00638a5dbb66c794b2185 Sim Zhen Quan <<EMAIL>> 1752226731 +0800	checkout: moving from dev to SKLEARN-490-add-translatable-to-title-in-net-average-report
424988a97f9690e51ff00638a5dbb66c794b2185 0ffa03fbe981e57224b6d86561158be9ed4320d9 Sim Zhen Quan <<EMAIL>> 1752226737 +0800	merge origin/main: Merge made by the 'ort' strategy.
0ffa03fbe981e57224b6d86561158be9ed4320d9 285b90d7a56086093d20ecafae42beabccc85fec Sim Zhen Quan <<EMAIL>> 1752226826 +0800	commit: Reviewed
285b90d7a56086093d20ecafae42beabccc85fec afcd1b2b510f8eda7f77fe4da30095569a0748c0 Sim Zhen Quan <<EMAIL>> 1752226835 +0800	checkout: moving from SKLEARN-490-add-translatable-to-title-in-net-average-report to dev
afcd1b2b510f8eda7f77fe4da30095569a0748c0 c3cc110c8d8fe0d8fc20a858e1b15e368a0c6519 Sim Zhen Quan <<EMAIL>> 1752226837 +0800	merge SKLEARN-490-add-translatable-to-title-in-net-average-report: Merge made by the 'ort' strategy.
c3cc110c8d8fe0d8fc20a858e1b15e368a0c6519 62ab42c1f49f045f98781c9987486ca7f32c27a0 Sim Zhen Quan <<EMAIL>> 1752226921 +0800	commit: Deployed to dev
62ab42c1f49f045f98781c9987486ca7f32c27a0 0adaf51e89578618b39f1eb44766db583c2f11f9 Sim Zhen Quan <<EMAIL>> 1752460942 +0800	checkout: moving from dev to main
0adaf51e89578618b39f1eb44766db583c2f11f9 62ab42c1f49f045f98781c9987486ca7f32c27a0 Sim Zhen Quan <<EMAIL>> 1752467701 +0800	checkout: moving from main to dev
62ab42c1f49f045f98781c9987486ca7f32c27a0 62ab42c1f49f045f98781c9987486ca7f32c27a0 Sim Zhen Quan <<EMAIL>> 1752468368 +0800	reset: moving to HEAD
62ab42c1f49f045f98781c9987486ca7f32c27a0 967c99787b58ab5dcf1f1ed5613ea4146dedbf92 Sim Zhen Quan <<EMAIL>> 1752468372 +0800	checkout: moving from dev to fix/update-enrollment
967c99787b58ab5dcf1f1ed5613ea4146dedbf92 56b9d48f158f8acb9d055d553a2e0b5edce6ff1d Sim Zhen Quan <<EMAIL>> 1752480227 +0800	merge origin/main: Merge made by the 'ort' strategy.
56b9d48f158f8acb9d055d553a2e0b5edce6ff1d 60d699a595b6e57a95449dbbddd84f97c42acecf Sim Zhen Quan <<EMAIL>> 1752487348 +0800	commit: Fixes
60d699a595b6e57a95449dbbddd84f97c42acecf 62ab42c1f49f045f98781c9987486ca7f32c27a0 Sim Zhen Quan <<EMAIL>> 1752487382 +0800	checkout: moving from fix/update-enrollment to dev
62ab42c1f49f045f98781c9987486ca7f32c27a0 22772323d20ab1d9919908cc7b827e456eff027b Sim Zhen Quan <<EMAIL>> 1752487477 +0800	commit (merge): Merge branch 'fix/update-enrollment' into dev
22772323d20ab1d9919908cc7b827e456eff027b 587b91a1e060b54fca83f39ff71f6acaf86d7236 Sim Zhen Quan <<EMAIL>> 1752487486 +0800	checkout: moving from dev to fix/first-batch-changes
587b91a1e060b54fca83f39ff71f6acaf86d7236 01e427a17626e7214604d2f7f9dfddb2637e544d Sim Zhen Quan <<EMAIL>> 1752487492 +0800	merge origin/main: Merge made by the 'ort' strategy.
01e427a17626e7214604d2f7f9dfddb2637e544d 60d699a595b6e57a95449dbbddd84f97c42acecf Sim Zhen Quan <<EMAIL>> 1752487656 +0800	checkout: moving from fix/first-batch-changes to fix/update-enrollment
60d699a595b6e57a95449dbbddd84f97c42acecf 3263fd267a94463cf271825d81ea3918a5244d59 Sim Zhen Quan <<EMAIL>> 1752487686 +0800	commit (merge): Merge branch 'fix/first-batch-changes' into fix/update-enrollment
3263fd267a94463cf271825d81ea3918a5244d59 0f901db892ba4b7abbe4a97fb9545db6258b00a6 Sim Zhen Quan <<EMAIL>> 1752491308 +0800	commit: Bug fix
0f901db892ba4b7abbe4a97fb9545db6258b00a6 22772323d20ab1d9919908cc7b827e456eff027b Sim Zhen Quan <<EMAIL>> 1752491322 +0800	checkout: moving from fix/update-enrollment to dev
22772323d20ab1d9919908cc7b827e456eff027b 4ba66195f3bb43b509baf8d946b01e5881d898bf Sim Zhen Quan <<EMAIL>> 1752491373 +0800	commit (merge): Merge branch 'fix/update-enrollment' into dev
4ba66195f3bb43b509baf8d946b01e5881d898bf 80f5ae4288ce0d1dae29c3bcf548fe74bfb7f929 Sim Zhen Quan <<EMAIL>> 1752545352 +0800	commit: Deployed to dev
80f5ae4288ce0d1dae29c3bcf548fe74bfb7f929 0adaf51e89578618b39f1eb44766db583c2f11f9 Sim Zhen Quan <<EMAIL>> 1752545465 +0800	checkout: moving from dev to main
0adaf51e89578618b39f1eb44766db583c2f11f9 0adaf51e89578618b39f1eb44766db583c2f11f9 Sim Zhen Quan <<EMAIL>> 1752545494 +0800	checkout: moving from main to SKLEARN-519-semester-class-sorting
0adaf51e89578618b39f1eb44766db583c2f11f9 1f4187d77910e15a8e47455dc4f1e0664a574c96 Sim Zhen Quan <<EMAIL>> 1752546749 +0800	checkout: moving from SKLEARN-519-semester-class-sorting to fix/autocount-changes
1f4187d77910e15a8e47455dc4f1e0664a574c96 0f48e1ea32692e9cc3554691798ecac63199da3c Sim Zhen Quan <<EMAIL>> 1752546756 +0800	merge origin/main: Merge made by the 'ort' strategy.
0f48e1ea32692e9cc3554691798ecac63199da3c 80f5ae4288ce0d1dae29c3bcf548fe74bfb7f929 Sim Zhen Quan <<EMAIL>> 1752547056 +0800	checkout: moving from fix/autocount-changes to dev
80f5ae4288ce0d1dae29c3bcf548fe74bfb7f929 1cfcb95a3644dd01972645908f5c4ee5f8991463 Sim Zhen Quan <<EMAIL>> 1752547062 +0800	merge fix/autocount-changes: Merge made by the 'ort' strategy.
1cfcb95a3644dd01972645908f5c4ee5f8991463 ba40ecb9e1f320486389d011d260bd93e66d437e Sim Zhen Quan <<EMAIL>> 1752549466 +0800	commit: Deployed to dev
ba40ecb9e1f320486389d011d260bd93e66d437e 0f901db892ba4b7abbe4a97fb9545db6258b00a6 Sim Zhen Quan <<EMAIL>> 1752549469 +0800	checkout: moving from dev to fix/update-enrollment
0f901db892ba4b7abbe4a97fb9545db6258b00a6 45cc03e11aad03958ec3f58d2fbe180e23ddbaa6 Sim Zhen Quan <<EMAIL>> 1752552946 +0800	commit: Only show primary school
45cc03e11aad03958ec3f58d2fbe180e23ddbaa6 0adaf51e89578618b39f1eb44766db583c2f11f9 Sim Zhen Quan <<EMAIL>> 1752552971 +0800	checkout: moving from fix/update-enrollment to main
0adaf51e89578618b39f1eb44766db583c2f11f9 0adaf51e89578618b39f1eb44766db583c2f11f9 Sim Zhen Quan <<EMAIL>> 1752552981 +0800	checkout: moving from main to staging/2025-07-15
0adaf51e89578618b39f1eb44766db583c2f11f9 097d3c42fe3996ba6e83b3521ff6d8480bf9eec0 Sim Zhen Quan <<EMAIL>> 1752553075 +0800	checkout: moving from staging/2025-07-15 to fix/second-excel-import-changes
097d3c42fe3996ba6e83b3521ff6d8480bf9eec0 12082a3faa7ed518026932984a14f170693e3e83 Sim Zhen Quan <<EMAIL>> 1752553082 +0800	merge fix/first-batch-changes: Merge made by the 'ort' strategy.
12082a3faa7ed518026932984a14f170693e3e83 ba40ecb9e1f320486389d011d260bd93e66d437e Sim Zhen Quan <<EMAIL>> 1752553241 +0800	checkout: moving from fix/second-excel-import-changes to dev
ba40ecb9e1f320486389d011d260bd93e66d437e 4c09b940ae6d3aa86c7f2aa0597eba56093dc528 Sim Zhen Quan <<EMAIL>> 1752553246 +0800	merge fix/second-excel-import-changes: Merge made by the 'ort' strategy.
4c09b940ae6d3aa86c7f2aa0597eba56093dc528 7701d49113789eb78d19cdcf486d824e4bc92b98 Sim Zhen Quan <<EMAIL>> 1752563013 +0800	checkout: moving from dev to comprehensive-result-deadline
7701d49113789eb78d19cdcf486d824e4bc92b98 e534157dcfcaa39be1bb272c2e7ed5d4034bfad3 Sim Zhen Quan <<EMAIL>> 1752563416 +0800	checkout: moving from comprehensive-result-deadline to feature/enrollment-email-template
e534157dcfcaa39be1bb272c2e7ed5d4034bfad3 75997e1fe50dc5eef5c7e48f5812ebdb9f5592bc Sim Zhen Quan <<EMAIL>> 1752563435 +0800	merge origin/main: Merge made by the 'ort' strategy.
75997e1fe50dc5eef5c7e48f5812ebdb9f5592bc 4c09b940ae6d3aa86c7f2aa0597eba56093dc528 Sim Zhen Quan <<EMAIL>> 1752597244 +0800	checkout: moving from feature/enrollment-email-template to dev
4c09b940ae6d3aa86c7f2aa0597eba56093dc528 0adaf51e89578618b39f1eb44766db583c2f11f9 Sim Zhen Quan <<EMAIL>> 1752597259 +0800	checkout: moving from dev to main
0adaf51e89578618b39f1eb44766db583c2f11f9 9f95676325e2043efb5f8e7bdd0bb4f87dfbfb23 Sim Zhen Quan <<EMAIL>> 1752597264 +0800	pull: Fast-forward
9f95676325e2043efb5f8e7bdd0bb4f87dfbfb23 addd3c58d162007a61da4bddc3867f0a1b1da708 Sim Zhen Quan <<EMAIL>> 1752632253 +0800	commit: Deployed to prd
addd3c58d162007a61da4bddc3867f0a1b1da708 addd3c58d162007a61da4bddc3867f0a1b1da708 Sim Zhen Quan <<EMAIL>> 1752657013 +0800	checkout: moving from main to main
addd3c58d162007a61da4bddc3867f0a1b1da708 4c09b940ae6d3aa86c7f2aa0597eba56093dc528 Sim Zhen Quan <<EMAIL>> 1752657105 +0800	checkout: moving from main to dev
4c09b940ae6d3aa86c7f2aa0597eba56093dc528 addd3c58d162007a61da4bddc3867f0a1b1da708 Sim Zhen Quan <<EMAIL>> 1752657293 +0800	checkout: moving from dev to main
addd3c58d162007a61da4bddc3867f0a1b1da708 0c0f50aa58c3045f8de9698947db5b21e4e761d3 Sim Zhen Quan <<EMAIL>> 1752657302 +0800	pull: Fast-forward
0c0f50aa58c3045f8de9698947db5b21e4e761d3 0c16d265fe9f0b67ac330bf2fcc35f0e92f29566 Sim Zhen Quan <<EMAIL>> 1752684835 +0800	commit: Deployed to prd
0c16d265fe9f0b67ac330bf2fcc35f0e92f29566 7c07064734a0904f8dcdef5f6bc708a3e1dc3a03 Sim Zhen Quan <<EMAIL>> 1752684892 +0800	checkout: moving from main to fix/add-created-by-to-discount
7c07064734a0904f8dcdef5f6bc708a3e1dc3a03 3d9a6aea47abb0220743c3d661aad2591a0abfd4 Sim Zhen Quan <<EMAIL>> 1752684902 +0800	merge origin/main: Merge made by the 'ort' strategy.
3d9a6aea47abb0220743c3d661aad2591a0abfd4 4c09b940ae6d3aa86c7f2aa0597eba56093dc528 Sim Zhen Quan <<EMAIL>> 1752685107 +0800	checkout: moving from fix/add-created-by-to-discount to dev
4c09b940ae6d3aa86c7f2aa0597eba56093dc528 bf464b3ea83d2f6a018f733fd91330b5982bac02 Sim Zhen Quan <<EMAIL>> 1752685249 +0800	commit (merge): Merge branch 'fix/add-created-by-to-discount' into dev
bf464b3ea83d2f6a018f733fd91330b5982bac02 75997e1fe50dc5eef5c7e48f5812ebdb9f5592bc Sim Zhen Quan <<EMAIL>> 1752685257 +0800	checkout: moving from dev to feature/enrollment-email-template
75997e1fe50dc5eef5c7e48f5812ebdb9f5592bc ebfd7a68d8b0cd5aef2555db9da3aa713b73b75e Sim Zhen Quan <<EMAIL>> 1752685262 +0800	merge origin/main: Merge made by the 'ort' strategy.
ebfd7a68d8b0cd5aef2555db9da3aa713b73b75e bf464b3ea83d2f6a018f733fd91330b5982bac02 Sim Zhen Quan <<EMAIL>> 1752685858 +0800	checkout: moving from feature/enrollment-email-template to dev
bf464b3ea83d2f6a018f733fd91330b5982bac02 84d50e12fd7b8e3fea85549d776d65e99a538ff6 Sim Zhen Quan <<EMAIL>> 1752685865 +0800	merge feature/enrollment-email-template: Merge made by the 'ort' strategy.
84d50e12fd7b8e3fea85549d776d65e99a538ff6 ae10a7e5bbd034c9decac02942e99eb67e0e6096 Sim Zhen Quan <<EMAIL>> 1752685917 +0800	commit: Updated CHANGELOG.dev.md
ae10a7e5bbd034c9decac02942e99eb67e0e6096 5c5a049ec40e5ee8ce6a2f43396f38cd05058771 Sim Zhen Quan <<EMAIL>> 1752685967 +0800	checkout: moving from dev to feature/enrollment-student-number
5c5a049ec40e5ee8ce6a2f43396f38cd05058771 ae10a7e5bbd034c9decac02942e99eb67e0e6096 Sim Zhen Quan <<EMAIL>> 1752719230 +0800	checkout: moving from feature/enrollment-student-number to dev
ae10a7e5bbd034c9decac02942e99eb67e0e6096 42625ff0394d4859b013f2175bf64998ccadd983 Sim Zhen Quan <<EMAIL>> 1752722372 +0800	commit: Deployed to dev
42625ff0394d4859b013f2175bf64998ccadd983 5c5a049ec40e5ee8ce6a2f43396f38cd05058771 Sim Zhen Quan <<EMAIL>> 1752722388 +0800	checkout: moving from dev to feature/enrollment-student-number
5c5a049ec40e5ee8ce6a2f43396f38cd05058771 7f0eae22a6ba8f50ec48ee1b36947b85015ac75a Sim Zhen Quan <<EMAIL>> 1752722393 +0800	pull: Fast-forward
7f0eae22a6ba8f50ec48ee1b36947b85015ac75a 62d3bbf631856189e6a9db841fe411b8edc81545 Sim Zhen Quan <<EMAIL>> 1752741119 +0800	checkout: moving from feature/enrollment-student-number to SKLEARN-497-reopen-posted-exam-result-by-exam-and-grade
62d3bbf631856189e6a9db841fe411b8edc81545 aecba7bc7644716bcf6b9e0c6683293d2e83f347 Sim Zhen Quan <<EMAIL>> 1752741136 +0800	merge origin/main: Merge made by the 'ort' strategy.
aecba7bc7644716bcf6b9e0c6683293d2e83f347 04af66a7f000c76774a4674b8e1adb921fd0160e Sim Zhen Quan <<EMAIL>> 1752743871 +0800	checkout: moving from SKLEARN-497-reopen-posted-exam-result-by-exam-and-grade to report/merit-report
04af66a7f000c76774a4674b8e1adb921fd0160e ce272c095ca19d8cae723e81d3962e752dc92633 Sim Zhen Quan <<EMAIL>> 1752744324 +0800	commit (merge): Merge remote-tracking branch 'origin/main' into report/merit-report
ce272c095ca19d8cae723e81d3962e752dc92633 a9ee0a9d314d8c0b77cefb7575ef3551afe53f0e Sim Zhen Quan <<EMAIL>> 1752746929 +0800	commit: Reviewed
a9ee0a9d314d8c0b77cefb7575ef3551afe53f0e 42625ff0394d4859b013f2175bf64998ccadd983 Sim Zhen Quan <<EMAIL>> 1752746958 +0800	checkout: moving from report/merit-report to dev
42625ff0394d4859b013f2175bf64998ccadd983 b27b160b6bb74f8789e18692427c1348ca5476de Sim Zhen Quan <<EMAIL>> 1752747105 +0800	commit (merge): Merge branch 'report/merit-report' into dev
b27b160b6bb74f8789e18692427c1348ca5476de aecba7bc7644716bcf6b9e0c6683293d2e83f347 Sim Zhen Quan <<EMAIL>> 1752747162 +0800	checkout: moving from dev to SKLEARN-497-reopen-posted-exam-result-by-exam-and-grade
aecba7bc7644716bcf6b9e0c6683293d2e83f347 ada2c815c6533c721ec2ad9aae259469adb74a18 Sim Zhen Quan <<EMAIL>> 1752747167 +0800	pull: Fast-forward
ada2c815c6533c721ec2ad9aae259469adb74a18 51dd3121badc76282daa345c778f53f098e5c809 Sim Zhen Quan <<EMAIL>> 1752747547 +0800	commit: Reviewed
51dd3121badc76282daa345c778f53f098e5c809 b27b160b6bb74f8789e18692427c1348ca5476de Sim Zhen Quan <<EMAIL>> 1752747554 +0800	checkout: moving from SKLEARN-497-reopen-posted-exam-result-by-exam-and-grade to dev
b27b160b6bb74f8789e18692427c1348ca5476de cb487124614ce48406f091c7e97bcb461728ece9 Sim Zhen Quan <<EMAIL>> 1752804212 +0800	commit: Deployed to dev
cb487124614ce48406f091c7e97bcb461728ece9 0cbc138fdae8898d63b22e1a31adf4563a513474 Sim Zhen Quan <<EMAIL>> 1752810525 +0800	checkout: moving from dev to student-performance-report
0cbc138fdae8898d63b22e1a31adf4563a513474 17b78e20419db5063da099e78581ae60a6c6623c Sim Zhen Quan <<EMAIL>> 1752810898 +0800	commit (merge): Merge remote-tracking branch 'origin/main' into student-performance-report
17b78e20419db5063da099e78581ae60a6c6623c 0c16d265fe9f0b67ac330bf2fcc35f0e92f29566 Sim Zhen Quan <<EMAIL>> 1752921378 +0800	checkout: moving from student-performance-report to main
0c16d265fe9f0b67ac330bf2fcc35f0e92f29566 0c16d265fe9f0b67ac330bf2fcc35f0e92f29566 Sim Zhen Quan <<EMAIL>> 1752921397 +0800	checkout: moving from main to hotfix/add-command-to-regenerate-billing-document
0c16d265fe9f0b67ac330bf2fcc35f0e92f29566 d6baf125b41fabdc7f074a639d449f9803fd93cd Sim Zhen Quan <<EMAIL>> 1752923063 +0800	commit: Added command to regenerate billing document receipt_url
d6baf125b41fabdc7f074a639d449f9803fd93cd 0c16d265fe9f0b67ac330bf2fcc35f0e92f29566 Sim Zhen Quan <<EMAIL>> 1752940081 +0800	checkout: moving from hotfix/add-command-to-regenerate-billing-document to main
0c16d265fe9f0b67ac330bf2fcc35f0e92f29566 b658a7be723b73b1a50a83d3eb1d0d2802062e93 Sim Zhen Quan <<EMAIL>> 1752940087 +0800	pull: Fast-forward
b658a7be723b73b1a50a83d3eb1d0d2802062e93 74b4342675df38ed0b17b2b74a1bf0a0c4b5030e Sim Zhen Quan <<EMAIL>> 1753022620 +0800	pull: Fast-forward
74b4342675df38ed0b17b2b74a1bf0a0c4b5030e fa7a839281a84208b57281b8b2ba32f1a2616ee3 Sim Zhen Quan <<EMAIL>> 1753063500 +0800	commit: Deployed to prd
fa7a839281a84208b57281b8b2ba32f1a2616ee3 fa7a839281a84208b57281b8b2ba32f1a2616ee3 Sim Zhen Quan <<EMAIL>> 1753063512 +0800	checkout: moving from main to main
fa7a839281a84208b57281b8b2ba32f1a2616ee3 fa7a839281a84208b57281b8b2ba32f1a2616ee3 Sim Zhen Quan <<EMAIL>> 1753063522 +0800	checkout: moving from main to staging/2025-07-21
fa7a839281a84208b57281b8b2ba32f1a2616ee3 588225b1679929c41a28295e3ef025cff054bc7e Sim Zhen Quan <<EMAIL>> 1753064172 +0800	pull: Fast-forward
588225b1679929c41a28295e3ef025cff054bc7e 7f0eae22a6ba8f50ec48ee1b36947b85015ac75a Sim Zhen Quan <<EMAIL>> 1753064174 +0800	checkout: moving from staging/2025-07-21 to feature/enrollment-student-number
7f0eae22a6ba8f50ec48ee1b36947b85015ac75a b914072a7c643cc9bd4b0101afae3b77bfd9da7f Sim Zhen Quan <<EMAIL>> 1753064179 +0800	pull: Fast-forward
b914072a7c643cc9bd4b0101afae3b77bfd9da7f 5c8452ddc5b9c968e569ef0a8c9b1fb344d7aa7a Sim Zhen Quan <<EMAIL>> 1753064180 +0800	merge origin/main: Merge made by the 'ort' strategy.
5c8452ddc5b9c968e569ef0a8c9b1fb344d7aa7a bf855790df179fe650b95fc21bd71e287d29122a Sim Zhen Quan <<EMAIL>> 1753067441 +0800	commit: Reviewed
bf855790df179fe650b95fc21bd71e287d29122a cb487124614ce48406f091c7e97bcb461728ece9 Sim Zhen Quan <<EMAIL>> 1753067448 +0800	checkout: moving from feature/enrollment-student-number to dev
cb487124614ce48406f091c7e97bcb461728ece9 03c57fa9888a5dc691b6edb9ec3ad1e4662839a3 Sim Zhen Quan <<EMAIL>> 1753067456 +0800	pull: Fast-forward
03c57fa9888a5dc691b6edb9ec3ad1e4662839a3 45812129effdca0affde7886f63a44af40846b2f Sim Zhen Quan <<EMAIL>> 1753067457 +0800	merge feature/enrollment-student-number: Merge made by the 'ort' strategy.
45812129effdca0affde7886f63a44af40846b2f 9d767233b158d68c10803897cc6d9607e27208e4 Sim Zhen Quan <<EMAIL>> 1753067639 +0800	commit: Updated CHANGELOG.dev.md
9d767233b158d68c10803897cc6d9607e27208e4 17b78e20419db5063da099e78581ae60a6c6623c Sim Zhen Quan <<EMAIL>> 1753068714 +0800	checkout: moving from dev to student-performance-report
17b78e20419db5063da099e78581ae60a6c6623c b35bba61ddb808d9dc7db281280bdd01ee3453f2 Sim Zhen Quan <<EMAIL>> 1753068727 +0800	merge origin/main: Merge made by the 'ort' strategy.
b35bba61ddb808d9dc7db281280bdd01ee3453f2 423737aa317b15b058af9b98a7549cb179e51751 Sim Zhen Quan <<EMAIL>> 1753069535 +0800	commit: Reviewed
423737aa317b15b058af9b98a7549cb179e51751 9d767233b158d68c10803897cc6d9607e27208e4 Sim Zhen Quan <<EMAIL>> 1753069783 +0800	checkout: moving from student-performance-report to dev
9d767233b158d68c10803897cc6d9607e27208e4 6c92523b79114b005f361a3fe302d5c930fd01a9 Sim Zhen Quan <<EMAIL>> 1753069982 +0800	commit (merge): Merge branch 'student-performance-report' into dev
6c92523b79114b005f361a3fe302d5c930fd01a9 4659fe3bf7a955d06d2dbefd1150573ec4b886f2 Sim Zhen Quan <<EMAIL>> 1753077530 +0800	commit: Deployed to dev
4659fe3bf7a955d06d2dbefd1150573ec4b886f2 53aba8614b3e56e7d9e33bb3e6543116f6b8016c Sim Zhen Quan <<EMAIL>> 1753077619 +0800	checkout: moving from dev to SKLEARN-193-academy-report-best-grade-by-subject
53aba8614b3e56e7d9e33bb3e6543116f6b8016c 56d42b33c2c17a8233243651fe8f9f69114f2779 Sim Zhen Quan <<EMAIL>> 1753077631 +0800	merge origin/main: Merge made by the 'ort' strategy.
56d42b33c2c17a8233243651fe8f9f69114f2779 4ddba49810a711751e55674b4f983e3ca105bbe0 Sim Zhen Quan <<EMAIL>> 1753077965 +0800	checkout: moving from SKLEARN-193-academy-report-best-grade-by-subject to SKLEARN-426-academy-report-all-passed-report
4ddba49810a711751e55674b4f983e3ca105bbe0 9890c526acaa6269221c1f14f1948e3bab27af88 Sim Zhen Quan <<EMAIL>> 1753077971 +0800	merge origin/main: Merge made by the 'ort' strategy.
9890c526acaa6269221c1f14f1948e3bab27af88 8306cb8be306271a3efdbbdeb24ac6f2c88cdd72 Sim Zhen Quan <<EMAIL>> 1753079537 +0800	commit: Reviewed
8306cb8be306271a3efdbbdeb24ac6f2c88cdd72 4659fe3bf7a955d06d2dbefd1150573ec4b886f2 Sim Zhen Quan <<EMAIL>> 1753079557 +0800	checkout: moving from SKLEARN-426-academy-report-all-passed-report to dev
4659fe3bf7a955d06d2dbefd1150573ec4b886f2 2b7713302170f08ec1d991a0da968c300e2f2e8e Sim Zhen Quan <<EMAIL>> 1753079658 +0800	commit (merge): Merge branch 'SKLEARN-426-academy-report-all-passed-report' into dev
2b7713302170f08ec1d991a0da968c300e2f2e8e 60b1eee31f72969080f8f5be67ee20f31a313910 Sim Zhen Quan <<EMAIL>> 1753079699 +0800	checkout: moving from dev to SKLEARN-427-academy-report-best-grade-by-class-report
60b1eee31f72969080f8f5be67ee20f31a313910 f538a26f58bf40e7bae831b1230b4f1c5a28652c Sim Zhen Quan <<EMAIL>> 1753079826 +0800	commit (merge): Merge branch 'SKLEARN-426-academy-report-all-passed-report' into SKLEARN-427-academy-report-best-grade-by-class-report
f538a26f58bf40e7bae831b1230b4f1c5a28652c 1ef63b4cb0ae9ff2c00139d485ba4ec1d60f73ef Sim Zhen Quan <<EMAIL>> 1753081268 +0800	commit: Reviewed
1ef63b4cb0ae9ff2c00139d485ba4ec1d60f73ef 2b7713302170f08ec1d991a0da968c300e2f2e8e Sim Zhen Quan <<EMAIL>> 1753081293 +0800	checkout: moving from SKLEARN-427-academy-report-best-grade-by-class-report to dev
2b7713302170f08ec1d991a0da968c300e2f2e8e cda61f5addc3aeec5bc089041181275e7684841d Sim Zhen Quan <<EMAIL>> 1753081329 +0800	commit (merge): Merge branch 'SKLEARN-427-academy-report-best-grade-by-class-report' into dev
cda61f5addc3aeec5bc089041181275e7684841d 7138711f38fdb7c995f1940ae4854f00447dd0f6 Sim Zhen Quan <<EMAIL>> 1753081399 +0800	commit: Deployed to dev
7138711f38fdb7c995f1940ae4854f00447dd0f6 56d42b33c2c17a8233243651fe8f9f69114f2779 Sim Zhen Quan <<EMAIL>> 1753084429 +0800	checkout: moving from dev to SKLEARN-193-academy-report-best-grade-by-subject
56d42b33c2c17a8233243651fe8f9f69114f2779 f2e259cf87db9834a08f13a03de7659f8228dcee Sim Zhen Quan <<EMAIL>> 1753084502 +0800	commit (merge): Merge branch 'SKLEARN-427-academy-report-best-grade-by-class-report' into SKLEARN-193-academy-report-best-grade-by-subject
f2e259cf87db9834a08f13a03de7659f8228dcee c1e00b5725dc64eab806444701ee5aff0b2e16ce Sim Zhen Quan <<EMAIL>> 1753089612 +0800	commit: Reviewed
c1e00b5725dc64eab806444701ee5aff0b2e16ce 7138711f38fdb7c995f1940ae4854f00447dd0f6 Sim Zhen Quan <<EMAIL>> 1753089667 +0800	checkout: moving from SKLEARN-193-academy-report-best-grade-by-subject to dev
7138711f38fdb7c995f1940ae4854f00447dd0f6 3ab4416ec1a8092813baf2ea864d9ba90fe06124 Sim Zhen Quan <<EMAIL>> 1753089669 +0800	merge SKLEARN-193-academy-report-best-grade-by-subject: Merge made by the 'ort' strategy.
3ab4416ec1a8092813baf2ea864d9ba90fe06124 3d900f5553073713666a2c72f5f9e772e2d6c688 Sim Zhen Quan <<EMAIL>> 1753089733 +0800	commit: Updated CHANGELOG.dev.md
3d900f5553073713666a2c72f5f9e772e2d6c688 b928d981ade32de8a7cb6e6b54b198c36d6092b7 Sim Zhen Quan <<EMAIL>> 1753089856 +0800	checkout: moving from dev to SKLEARN-428-academy-report-best-grade-by-grade
b928d981ade32de8a7cb6e6b54b198c36d6092b7 c3df769030f558bbcb335bfeeef4916f70c7f4c3 Sim Zhen Quan <<EMAIL>> 1753089965 +0800	commit (merge): Merge branch 'SKLEARN-193-academy-report-best-grade-by-subject' into SKLEARN-428-academy-report-best-grade-by-grade
c3df769030f558bbcb335bfeeef4916f70c7f4c3 2bd99fd299b3985051b9250290a167192b9003e9 Sim Zhen Quan <<EMAIL>> 1753092940 +0800	commit: Reviewed
2bd99fd299b3985051b9250290a167192b9003e9 3d900f5553073713666a2c72f5f9e772e2d6c688 Sim Zhen Quan <<EMAIL>> 1753094347 +0800	checkout: moving from SKLEARN-428-academy-report-best-grade-by-grade to dev
3d900f5553073713666a2c72f5f9e772e2d6c688 56244e331a30d404ef5bee2357d9b3d2935cb38b Sim Zhen Quan <<EMAIL>> 1753094353 +0800	merge SKLEARN-428-academy-report-best-grade-by-grade: Merge made by the 'ort' strategy.
56244e331a30d404ef5bee2357d9b3d2935cb38b 943d1bd63c452117ddbb6fa29be294dab21b3dc7 Sim Zhen Quan <<EMAIL>> 1753094912 +0800	commit: Deployed to dev
943d1bd63c452117ddbb6fa29be294dab21b3dc7 2bf2d41d52e25d70249eddb774cb8c19574b7512 Sim Zhen Quan <<EMAIL>> 1753094922 +0800	checkout: moving from dev to feature/hostel-saving-collection-reports
2bf2d41d52e25d70249eddb774cb8c19574b7512 6b850009553366afc1a01845ced7bea779460c32 Sim Zhen Quan <<EMAIL>> 1753095078 +0800	commit (merge): Merge remote-tracking branch 'origin/main' into feature/hostel-saving-collection-reports
6b850009553366afc1a01845ced7bea779460c32 943d1bd63c452117ddbb6fa29be294dab21b3dc7 Sim Zhen Quan <<EMAIL>> 1753151736 +0800	checkout: moving from feature/hostel-saving-collection-reports to dev
943d1bd63c452117ddbb6fa29be294dab21b3dc7 d19ae4d4c63c1b0f06f671e9ee27814e5ad7bd56 Sim Zhen Quan <<EMAIL>> 1753151748 +0800	merge origin/SKLEARN-495-exam-mark-entry-enhancements: Merge made by the 'ort' strategy.
d19ae4d4c63c1b0f06f671e9ee27814e5ad7bd56 fa7a839281a84208b57281b8b2ba32f1a2616ee3 Sim Zhen Quan <<EMAIL>> 1753175769 +0800	checkout: moving from dev to main
fa7a839281a84208b57281b8b2ba32f1a2616ee3 fa7a839281a84208b57281b8b2ba32f1a2616ee3 Sim Zhen Quan <<EMAIL>> 1753175790 +0800	checkout: moving from main to lucas/add-delivery-date-and-available-date-to-product-index-api
fa7a839281a84208b57281b8b2ba32f1a2616ee3 4ec19a6ca76b588c6315afe51b064a00ef2fdc39 Sim Zhen Quan <<EMAIL>> 1753175860 +0800	commit: Add delivery_date and available_date fields to product index API
4ec19a6ca76b588c6315afe51b064a00ef2fdc39 d19ae4d4c63c1b0f06f671e9ee27814e5ad7bd56 Sim Zhen Quan <<EMAIL>> 1753176113 +0800	checkout: moving from lucas/add-delivery-date-and-available-date-to-product-index-api to dev
d19ae4d4c63c1b0f06f671e9ee27814e5ad7bd56 9ce650088ecc9e1e4828943faab8b0c578afefb1 Sim Zhen Quan <<EMAIL>> 1753176114 +0800	merge lucas/add-delivery-date-and-available-date-to-product-index-api: Merge made by the 'ort' strategy.
9ce650088ecc9e1e4828943faab8b0c578afefb1 6b850009553366afc1a01845ced7bea779460c32 Sim Zhen Quan <<EMAIL>> 1753176129 +0800	checkout: moving from dev to feature/hostel-saving-collection-reports
6b850009553366afc1a01845ced7bea779460c32 7b14308492e81f7ea53e8d8048be0f7401801146 Sim Zhen Quan <<EMAIL>> 1753179474 +0800	commit: Reviewed
7b14308492e81f7ea53e8d8048be0f7401801146 9ce650088ecc9e1e4828943faab8b0c578afefb1 Sim Zhen Quan <<EMAIL>> 1753179609 +0800	checkout: moving from feature/hostel-saving-collection-reports to dev
9ce650088ecc9e1e4828943faab8b0c578afefb1 08b4b5d4f47db595491f6dc2d531ccf32e2e2f83 Sim Zhen Quan <<EMAIL>> 1753179635 +0800	commit (merge): Merge branch 'feature/hostel-saving-collection-reports' into dev
08b4b5d4f47db595491f6dc2d531ccf32e2e2f83 8e0f0ebca7e6136b3273f95807ab984862de3bdc Sim Zhen Quan <<EMAIL>> 1753241386 +0800	commit: Deployed to dev
8e0f0ebca7e6136b3273f95807ab984862de3bdc fa7a839281a84208b57281b8b2ba32f1a2616ee3 Sim Zhen Quan <<EMAIL>> 1753241414 +0800	checkout: moving from dev to main
fa7a839281a84208b57281b8b2ba32f1a2616ee3 fa7a839281a84208b57281b8b2ba32f1a2616ee3 Sim Zhen Quan <<EMAIL>> 1753241535 +0800	checkout: moving from main to SKLEARN-480-class-attendance-report-enhancement
fa7a839281a84208b57281b8b2ba32f1a2616ee3 8af9c72f477571fe8d2569163c363d20bb3bf31b Sim Zhen Quan <<EMAIL>> 1753254538 +0800	commit: Enhance attendance report by adding 'attendance_taken_at' column and updating related logic
8af9c72f477571fe8d2569163c363d20bb3bf31b 8e0f0ebca7e6136b3273f95807ab984862de3bdc Sim Zhen Quan <<EMAIL>> 1753254632 +0800	checkout: moving from SKLEARN-480-class-attendance-report-enhancement to dev
8e0f0ebca7e6136b3273f95807ab984862de3bdc 542c48004cc79d2c397a34642ee09ebbbddf6d2d Sim Zhen Quan <<EMAIL>> 1753254635 +0800	merge SKLEARN-480-class-attendance-report-enhancement: Merge made by the 'ort' strategy.
542c48004cc79d2c397a34642ee09ebbbddf6d2d 3cb59885a21178326b999ef4954fe70f9e80c1cb Sim Zhen Quan <<EMAIL>> 1753257001 +0800	commit: Deployed to dev
3cb59885a21178326b999ef4954fe70f9e80c1cb fa7a839281a84208b57281b8b2ba32f1a2616ee3 Sim Zhen Quan <<EMAIL>> 1753258156 +0800	checkout: moving from dev to main
fa7a839281a84208b57281b8b2ba32f1a2616ee3 fa7a839281a84208b57281b8b2ba32f1a2616ee3 Sim Zhen Quan <<EMAIL>> 1753258165 +0800	checkout: moving from main to staging/2025-07-23
fa7a839281a84208b57281b8b2ba32f1a2616ee3 3cb59885a21178326b999ef4954fe70f9e80c1cb Sim Zhen Quan <<EMAIL>> 1753258204 +0800	checkout: moving from staging/2025-07-23 to dev
3cb59885a21178326b999ef4954fe70f9e80c1cb a9ee0a9d314d8c0b77cefb7575ef3551afe53f0e Sim Zhen Quan <<EMAIL>> 1753258480 +0800	checkout: moving from dev to report/merit-report
a9ee0a9d314d8c0b77cefb7575ef3551afe53f0e e4994372f07e1915af114d57c57529f5c51b8536 Sim Zhen Quan <<EMAIL>> 1753258486 +0800	merge staging/2025-07-23: Merge made by the 'ort' strategy.
e4994372f07e1915af114d57c57529f5c51b8536 68b22c5adfb0960a806f93aa04f63d63d1cd5132 Sim Zhen Quan <<EMAIL>> 1753258559 +0800	commit (merge): Merge remote-tracking branch 'origin/staging/2025-07-23' into report/merit-report
68b22c5adfb0960a806f93aa04f63d63d1cd5132 3cb59885a21178326b999ef4954fe70f9e80c1cb Sim Zhen Quan <<EMAIL>> 1753258611 +0800	checkout: moving from report/merit-report to dev
3cb59885a21178326b999ef4954fe70f9e80c1cb c73dd127fafa20efc31e824b901a0ab292e5dab3 Sim Zhen Quan <<EMAIL>> 1753258791 +0800	checkout: moving from dev to enhancement/show-discount-and-scholarship
c73dd127fafa20efc31e824b901a0ab292e5dab3 9387f2010794b19b50163f135fd8e699bd39bfb8 Sim Zhen Quan <<EMAIL>> 1753258885 +0800	commit (merge): Merge remote-tracking branch 'origin/staging/2025-07-23' into enhancement/show-discount-and-scholarship
9387f2010794b19b50163f135fd8e699bd39bfb8 6c9e96cb0261934a3bbe30fbac70b61df74ff34f Sim Zhen Quan <<EMAIL>> 1753259137 +0800	checkout: moving from enhancement/show-discount-and-scholarship to issue-193-student-management-page-employee-only-can-view-their-students
6c9e96cb0261934a3bbe30fbac70b61df74ff34f f6e74a0f877e851df4e1b3fa4d4fa85d8644a57f Sim Zhen Quan <<EMAIL>> 1753259143 +0800	merge origin/main: Merge made by the 'ort' strategy.
f6e74a0f877e851df4e1b3fa4d4fa85d8644a57f 926d2da597bb8f6e70e1c371b348ea2b263b0887 Sim Zhen Quan <<EMAIL>> 1753260368 +0800	commit: Reviewed
926d2da597bb8f6e70e1c371b348ea2b263b0887 3cb59885a21178326b999ef4954fe70f9e80c1cb Sim Zhen Quan <<EMAIL>> 1753260707 +0800	checkout: moving from issue-193-student-management-page-employee-only-can-view-their-students to dev
3cb59885a21178326b999ef4954fe70f9e80c1cb 6ed10a39d84b02d210ca0b2a03a694151ece165c Sim Zhen Quan <<EMAIL>> 1753260709 +0800	merge issue-193-student-management-page-employee-only-can-view-their-students: Merge made by the 'ort' strategy.
6ed10a39d84b02d210ca0b2a03a694151ece165c 839173f3b067e7cdf9d72ccb5b0a70fb44168617 Sim Zhen Quan <<EMAIL>> 1753261699 +0800	commit: Deployed to dev
839173f3b067e7cdf9d72ccb5b0a70fb44168617 ff3a3587e7a3492bcd4b0115f9d82b1fed762682 Sim Zhen Quan <<EMAIL>> 1753261707 +0800	checkout: moving from dev to SKLEARN-504-green-indicator-for-class-attendance-status-report
ff3a3587e7a3492bcd4b0115f9d82b1fed762682 5f5e556e723b5b9fd540662b00b6fdb10f205412 Sim Zhen Quan <<EMAIL>> 1753261714 +0800	merge origin/main: Merge made by the 'ort' strategy.
5f5e556e723b5b9fd540662b00b6fdb10f205412 6ac35d94d8ee06dc37e941534c3fd6c7fe90f996 Sim Zhen Quan <<EMAIL>> 1753262843 +0800	commit: Reviewed
6ac35d94d8ee06dc37e941534c3fd6c7fe90f996 839173f3b067e7cdf9d72ccb5b0a70fb44168617 Sim Zhen Quan <<EMAIL>> 1753262875 +0800	checkout: moving from SKLEARN-504-green-indicator-for-class-attendance-status-report to dev
839173f3b067e7cdf9d72ccb5b0a70fb44168617 7fa090fef0a98d0463b43c2e9b98d22721d474b8 Sim Zhen Quan <<EMAIL>> 1753262878 +0800	merge SKLEARN-504-green-indicator-for-class-attendance-status-report: Merge made by the 'ort' strategy.
7fa090fef0a98d0463b43c2e9b98d22721d474b8 2efb9cd8b7de070b73fb6beacc949ea63daecae4 Sim Zhen Quan <<EMAIL>> 1753262998 +0800	commit: Deployed to dev
2efb9cd8b7de070b73fb6beacc949ea63daecae4 5a071a7d6554e573041a50cf5497cbbd14dbd998 Sim Zhen Quan <<EMAIL>> 1753263112 +0800	checkout: moving from dev to SKLEARN-559-api-override-changes
5a071a7d6554e573041a50cf5497cbbd14dbd998 ffd40fd516e21ba24b59c782e6bc5d6a913a7a07 Sim Zhen Quan <<EMAIL>> 1753263118 +0800	merge origin/main: Merge made by the 'ort' strategy.
ffd40fd516e21ba24b59c782e6bc5d6a913a7a07 fa7a839281a84208b57281b8b2ba32f1a2616ee3 Sim Zhen Quan <<EMAIL>> 1753285405 +0800	checkout: moving from SKLEARN-559-api-override-changes to main
fa7a839281a84208b57281b8b2ba32f1a2616ee3 93bbe6099e7f400e64a4abc5ac83233b725e3f41 Sim Zhen Quan <<EMAIL>> 1753285478 +0800	pull: Fast-forward
93bbe6099e7f400e64a4abc5ac83233b725e3f41 80ce0b58f3e41f3efab291473d2434df58095498 Sim Zhen Quan <<EMAIL>> 1753287440 +0800	commit: Deployed to PRD
80ce0b58f3e41f3efab291473d2434df58095498 737b32a09ce6492ab34be35ea3aa9a7957953bae Sim Zhen Quan <<EMAIL>> 1753288804 +0800	commit: Bug fix
737b32a09ce6492ab34be35ea3aa9a7957953bae ffd40fd516e21ba24b59c782e6bc5d6a913a7a07 Sim Zhen Quan <<EMAIL>> 1753323771 +0800	checkout: moving from main to SKLEARN-559-api-override-changes
ffd40fd516e21ba24b59c782e6bc5d6a913a7a07 03e62a8900de9f37dca38f2aa7b10fd4f9970425 Sim Zhen Quan <<EMAIL>> 1753323868 +0800	commit (merge): Merge remote-tracking branch 'origin/main' into SKLEARN-559-api-override-changes
03e62a8900de9f37dca38f2aa7b10fd4f9970425 69e7b4346fff3773aa911d231b20abf71e8bb8f0 Sim Zhen Quan <<EMAIL>> 1753337014 +0800	checkout: moving from SKLEARN-559-api-override-changes to fix/employee-update
69e7b4346fff3773aa911d231b20abf71e8bb8f0 2efb9cd8b7de070b73fb6beacc949ea63daecae4 Sim Zhen Quan <<EMAIL>> 1753337156 +0800	checkout: moving from fix/employee-update to dev
2efb9cd8b7de070b73fb6beacc949ea63daecae4 3bc3c15bb2dfbace68a9691ab99f225e3077422c Sim Zhen Quan <<EMAIL>> 1753337248 +0800	commit (merge): Merge branch 'fix/employee-update' into dev
3bc3c15bb2dfbace68a9691ab99f225e3077422c 3bc3c15bb2dfbace68a9691ab99f225e3077422c Sim Zhen Quan <<EMAIL>> 1753408369 +0800	checkout: moving from dev to dev
3bc3c15bb2dfbace68a9691ab99f225e3077422c 0035364e4d09db8b41124bd6201beb68829a94de Sim Zhen Quan <<EMAIL>> 1753408391 +0800	commit: Deployed to dev
0035364e4d09db8b41124bd6201beb68829a94de 0035364e4d09db8b41124bd6201beb68829a94de Sim Zhen Quan <<EMAIL>> 1753408397 +0800	checkout: moving from dev to dev
0035364e4d09db8b41124bd6201beb68829a94de 94d2f1033dfbdcec2f8f955133f3ac1ea0c9e142 Sim Zhen Quan <<EMAIL>> 1753408405 +0800	merge 8f46301: Merge made by the 'ort' strategy.
94d2f1033dfbdcec2f8f955133f3ac1ea0c9e142 03e62a8900de9f37dca38f2aa7b10fd4f9970425 Sim Zhen Quan <<EMAIL>> 1753409357 +0800	checkout: moving from dev to SKLEARN-559-api-override-changes
03e62a8900de9f37dca38f2aa7b10fd4f9970425 e3aad6a3d9739093616092b58105f5cbd07a8628 Sim Zhen Quan <<EMAIL>> 1753410269 +0800	commit: Add in attendance_taken_at field for update
e3aad6a3d9739093616092b58105f5cbd07a8628 94d2f1033dfbdcec2f8f955133f3ac1ea0c9e142 Sim Zhen Quan <<EMAIL>> 1753410470 +0800	checkout: moving from SKLEARN-559-api-override-changes to dev
94d2f1033dfbdcec2f8f955133f3ac1ea0c9e142 082ac7312c526f00d08908d26c3be45f67177ab9 Sim Zhen Quan <<EMAIL>> 1753410489 +0800	merge SKLEARN-559-api-override-changes: Merge made by the 'ort' strategy.
082ac7312c526f00d08908d26c3be45f67177ab9 218207ac7bf125c7bd1e3c7304bafcf86c26205f Sim Zhen Quan <<EMAIL>> 1753410604 +0800	commit: Updated CHANGELOG.dev.md
218207ac7bf125c7bd1e3c7304bafcf86c26205f 67d34032f5b466f0d3a1f6d974c8b18a2a5b2c54 Sim Zhen Quan <<EMAIL>> 1753410787 +0800	commit: Deployed to dev
67d34032f5b466f0d3a1f6d974c8b18a2a5b2c54 737b32a09ce6492ab34be35ea3aa9a7957953bae Sim Zhen Quan <<EMAIL>> 1753411090 +0800	checkout: moving from dev to main
737b32a09ce6492ab34be35ea3aa9a7957953bae 737b32a09ce6492ab34be35ea3aa9a7957953bae Sim Zhen Quan <<EMAIL>> 1753411099 +0800	checkout: moving from main to staging/2025-07-25
737b32a09ce6492ab34be35ea3aa9a7957953bae 67d34032f5b466f0d3a1f6d974c8b18a2a5b2c54 Sim Zhen Quan <<EMAIL>> 1753411169 +0800	checkout: moving from staging/2025-07-25 to dev
67d34032f5b466f0d3a1f6d974c8b18a2a5b2c54 f7f8c853dcf8860b9ee2d33608e03b18c27e3eec Sim Zhen Quan <<EMAIL>> 1753411803 +0800	checkout: moving from dev to SKLEARN-497-reopen-posted-exam-result-by-exam-and-grade
f7f8c853dcf8860b9ee2d33608e03b18c27e3eec d835bc658b6ead40c582db94ca8e739377ad6432 Sim Zhen Quan <<EMAIL>> 1753412006 +0800	commit: Reviewed
d835bc658b6ead40c582db94ca8e739377ad6432 8b0d691a816236de406c572ce8163fea437800bf Sim Zhen Quan <<EMAIL>> 1753412014 +0800	merge origin/main: Merge made by the 'ort' strategy.
8b0d691a816236de406c572ce8163fea437800bf 737b32a09ce6492ab34be35ea3aa9a7957953bae Sim Zhen Quan <<EMAIL>> 1753412069 +0800	checkout: moving from SKLEARN-497-reopen-posted-exam-result-by-exam-and-grade to main
737b32a09ce6492ab34be35ea3aa9a7957953bae 67d34032f5b466f0d3a1f6d974c8b18a2a5b2c54 Sim Zhen Quan <<EMAIL>> 1753412072 +0800	checkout: moving from main to dev
67d34032f5b466f0d3a1f6d974c8b18a2a5b2c54 2f0f3d53987abc84354b19f4107f6c356f8ca5db Sim Zhen Quan <<EMAIL>> 1753412091 +0800	commit (merge): Merge branch 'SKLEARN-497-reopen-posted-exam-result-by-exam-and-grade' into dev
2f0f3d53987abc84354b19f4107f6c356f8ca5db a9cf097dc40959a265f2d7911c956af9dcd93f47 Sim Zhen Quan <<EMAIL>> 1753413909 +0800	merge 1e76d08: Merge made by the 'ort' strategy.
a9cf097dc40959a265f2d7911c956af9dcd93f47 737b32a09ce6492ab34be35ea3aa9a7957953bae Sim Zhen Quan <<EMAIL>> 1753415222 +0800	checkout: moving from dev to main
737b32a09ce6492ab34be35ea3aa9a7957953bae a85405b66c6010d83ce58f07c549f4fe0c8e3137 Sim Zhen Quan <<EMAIL>> 1753415899 +0800	commit: Fix test case
a85405b66c6010d83ce58f07c549f4fe0c8e3137 a9cf097dc40959a265f2d7911c956af9dcd93f47 Sim Zhen Quan <<EMAIL>> 1753431662 +0800	checkout: moving from main to dev
a9cf097dc40959a265f2d7911c956af9dcd93f47 a85405b66c6010d83ce58f07c549f4fe0c8e3137 Sim Zhen Quan <<EMAIL>> 1753634018 +0800	checkout: moving from dev to main
a85405b66c6010d83ce58f07c549f4fe0c8e3137 a85405b66c6010d83ce58f07c549f4fe0c8e3137 Sim Zhen Quan <<EMAIL>> 1753634044 +0800	checkout: moving from main to patch-competition-departments
a85405b66c6010d83ce58f07c549f4fe0c8e3137 d2a08f44b3cdaad43f72ad9169d7af630ee2834c Sim Zhen Quan <<EMAIL>> 1753668118 +0800	checkout: moving from patch-competition-departments to fixed-isbn-parse-time-error
d2a08f44b3cdaad43f72ad9169d7af630ee2834c b0a1e3fdc4fc54b5357403a5726633e875a0e9dd Sim Zhen Quan <<EMAIL>> 1753668130 +0800	merge main: Merge made by the 'ort' strategy.
b0a1e3fdc4fc54b5357403a5726633e875a0e9dd a9cf097dc40959a265f2d7911c956af9dcd93f47 Sim Zhen Quan <<EMAIL>> 1753668718 +0800	checkout: moving from fixed-isbn-parse-time-error to dev
a9cf097dc40959a265f2d7911c956af9dcd93f47 5cd3534704c780a50f8f25a8e9b71965c1b8ecf3 Sim Zhen Quan <<EMAIL>> 1753668750 +0800	commit (merge): Merge branch 'fixed-isbn-parse-time-error' into dev
5cd3534704c780a50f8f25a8e9b71965c1b8ecf3 a85405b66c6010d83ce58f07c549f4fe0c8e3137 Sim Zhen Quan <<EMAIL>> 1753668766 +0800	checkout: moving from dev to patch-competition-departments
a85405b66c6010d83ce58f07c549f4fe0c8e3137 ded4406dad1e1f3c7deb8e87aa25694d62b7c7da Sim Zhen Quan <<EMAIL>> 1753668786 +0800	commit: Added patch
ded4406dad1e1f3c7deb8e87aa25694d62b7c7da 5cd3534704c780a50f8f25a8e9b71965c1b8ecf3 Sim Zhen Quan <<EMAIL>> 1753668820 +0800	checkout: moving from patch-competition-departments to dev
5cd3534704c780a50f8f25a8e9b71965c1b8ecf3 eeb5124824fd5ad2d2ff1cb853eecb0b28bb6f46 Sim Zhen Quan <<EMAIL>> 1753668906 +0800	commit: Updated CHANGELOG.dev.md
eeb5124824fd5ad2d2ff1cb853eecb0b28bb6f46 9aa4f9eb7b135da0d8729897f115d9829d4673f9 Sim Zhen Quan <<EMAIL>> 1753669097 +0800	commit: Updated CHANGELOG.dev.md
9aa4f9eb7b135da0d8729897f115d9829d4673f9 1ae996aca47e2734810e18f8c7aeb1418549cdec Sim Zhen Quan <<EMAIL>> 1753669142 +0800	checkout: moving from dev to bug/subject-n1
1ae996aca47e2734810e18f8c7aeb1418549cdec a7bd8c425a977da78b0586c8fdefb22dd0ee01b8 Sim Zhen Quan <<EMAIL>> 1753669152 +0800	merge main: Merge made by the 'ort' strategy.
a7bd8c425a977da78b0586c8fdefb22dd0ee01b8 9aa4f9eb7b135da0d8729897f115d9829d4673f9 Sim Zhen Quan <<EMAIL>> 1753669542 +0800	checkout: moving from bug/subject-n1 to dev
9aa4f9eb7b135da0d8729897f115d9829d4673f9 f6077d0733f4c33f01ed570f267d43114fd3993f Sim Zhen Quan <<EMAIL>> 1753669547 +0800	merge bug/subject-n1: Merge made by the 'ort' strategy.
f6077d0733f4c33f01ed570f267d43114fd3993f db6436647e6350bf39e2a0562040bf1ea4b80af1 Sim Zhen Quan <<EMAIL>> 1753669649 +0800	commit: Updated CHANGELOG.dev.md
db6436647e6350bf39e2a0562040bf1ea4b80af1 672fe1f39cd208bee9e3b186788d43e77a9baf92 Sim Zhen Quan <<EMAIL>> ********** +0800	checkout: moving from dev to support-role-permissions
672fe1f39cd208bee9e3b186788d43e77a9baf92 4c0e97ebb368f61b5a1531501fde78f694755b73 Sim Zhen Quan <<EMAIL>> ********** +0800	merge main: Merge made by the 'ort' strategy.
4c0e97ebb368f61b5a1531501fde78f694755b73 a7225331a46415e6324a7bf3c7c03448c5339e78 Sim Zhen Quan <<EMAIL>> ********** +0800	commit: Added SupportAccountSeeder.php
a7225331a46415e6324a7bf3c7c03448c5339e78 db6436647e6350bf39e2a0562040bf1ea4b80af1 Sim Zhen Quan <<EMAIL>> ********** +0800	checkout: moving from support-role-permissions to dev
db6436647e6350bf39e2a0562040bf1ea4b80af1 e33f54dc951b2f3b0d4b996ff343e5e7f3eaacfa Sim Zhen Quan <<EMAIL>> ********** +0800	merge support-role-permissions: Merge made by the 'ort' strategy.
e33f54dc951b2f3b0d4b996ff343e5e7f3eaacfa 5f2c7c5bf02ff2f7e0ae11184d9e41d316e682a3 Sim Zhen Quan <<EMAIL>> ********** +0800	commit: Updated CHANGELOG.dev.md
5f2c7c5bf02ff2f7e0ae11184d9e41d316e682a3 1392e6567ef60464a2539ab9a4e716e97ea82958 Sim Zhen Quan <<EMAIL>> ********** +0800	checkout: moving from dev to fix/attendance-mark-deduction-report
1392e6567ef60464a2539ab9a4e716e97ea82958 c5f141096582e56887322325750c8afa09884df6 Sim Zhen Quan <<EMAIL>> ********** +0800	commit (merge): Merge branch 'main' into fix/attendance-mark-deduction-report
c5f141096582e56887322325750c8afa09884df6 9c26f7329d2a1fc41b40e7badfbe843d325bb4be Sim Zhen Quan <<EMAIL>> 1753673414 +0800	commit: Bug fix
9c26f7329d2a1fc41b40e7badfbe843d325bb4be 5f2c7c5bf02ff2f7e0ae11184d9e41d316e682a3 Sim Zhen Quan <<EMAIL>> 1753673419 +0800	checkout: moving from fix/attendance-mark-deduction-report to dev
5f2c7c5bf02ff2f7e0ae11184d9e41d316e682a3 8235a733f6047cef756109928f3d9e167a8b2791 Sim Zhen Quan <<EMAIL>> 1753673427 +0800	merge fix/attendance-mark-deduction-report: Merge made by the 'ort' strategy.
8235a733f6047cef756109928f3d9e167a8b2791 57f9f56b45d2c2b80e866acfd9544aed1c81354c Sim Zhen Quan <<EMAIL>> 1753673729 +0800	commit: Deployed to dev
57f9f56b45d2c2b80e866acfd9544aed1c81354c bed648034b11d920bc511436d28e291d03ba56be Sim Zhen Quan <<EMAIL>> 1753673887 +0800	checkout: moving from dev to ai-rnd
bed648034b11d920bc511436d28e291d03ba56be a85405b66c6010d83ce58f07c549f4fe0c8e3137 Sim Zhen Quan <<EMAIL>> 1753673960 +0800	checkout: moving from ai-rnd to main
a85405b66c6010d83ce58f07c549f4fe0c8e3137 bed648034b11d920bc511436d28e291d03ba56be Sim Zhen Quan <<EMAIL>> 1753675392 +0800	checkout: moving from main to ai-rnd
bed648034b11d920bc511436d28e291d03ba56be 83d6c141fd1121d6e821aace4e358540e0e9275c Sim Zhen Quan <<EMAIL>> 1753675473 +0800	commit (merge): Merge branch 'main' into ai-rnd
83d6c141fd1121d6e821aace4e358540e0e9275c 233e197c977b5bbe12b9efd0beca7e841a8373b1 Sim Zhen Quan <<EMAIL>> 1753683962 +0800	commit: Clean up code
233e197c977b5bbe12b9efd0beca7e841a8373b1 7c1205ff953975ffe0e478ff4b84878d1b52d0c2 Sim Zhen Quan <<EMAIL>> 1753688814 +0800	commit: Updated AI Chat
7c1205ff953975ffe0e478ff4b84878d1b52d0c2 75a4e4b16a831ade4547996a407a1a7ca082956e Sim Zhen Quan <<EMAIL>> 1753689637 +0800	commit: Updated AI Chat
75a4e4b16a831ade4547996a407a1a7ca082956e a3707b0bd6bf7fe75097df6427258e0b1c759735 Sim Zhen Quan <<EMAIL>> 1753689799 +0800	commit: Added flag whether is using default config
a3707b0bd6bf7fe75097df6427258e0b1c759735 a3707b0bd6bf7fe75097df6427258e0b1c759735 Sim Zhen Quan <<EMAIL>> 1753689965 +0800	reset: moving to HEAD
a3707b0bd6bf7fe75097df6427258e0b1c759735 57f9f56b45d2c2b80e866acfd9544aed1c81354c Sim Zhen Quan <<EMAIL>> 1753689967 +0800	checkout: moving from ai-rnd to dev
57f9f56b45d2c2b80e866acfd9544aed1c81354c ca75ff020a8c9dc80d6e8805778ffc5823bb8ba5 Sim Zhen Quan <<EMAIL>> 1753689992 +0800	commit (merge): Merge branch 'ai-rnd' into dev
ca75ff020a8c9dc80d6e8805778ffc5823bb8ba5 c17372e4e7249f5a8aadf0f52af7091c464bec88 Sim Zhen Quan <<EMAIL>> 1753690776 +0800	commit: Deployed to dev
c17372e4e7249f5a8aadf0f52af7091c464bec88 96e4b6bd573346510e97e2209905554a05417e35 Sim Zhen Quan <<EMAIL>> 1753693606 +0800	checkout: moving from dev to SKLEARN-429-academy-report-position-ranking-by-grade-report
96e4b6bd573346510e97e2209905554a05417e35 ebaecf0bbc60972c6c328fb0dc40753d2a1bbc83 Sim Zhen Quan <<EMAIL>> 1753693705 +0800	commit (merge): Merge remote-tracking branch 'origin/SKLEARN-428-academy-report-best-grade-by-grade' into SKLEARN-429-academy-report-position-ranking-by-grade-report
ebaecf0bbc60972c6c328fb0dc40753d2a1bbc83 abdbf270f093f08ada8f8c31de7975a279c780b6 Sim Zhen Quan <<EMAIL>> 1753693742 +0800	commit: Fix translations
abdbf270f093f08ada8f8c31de7975a279c780b6 16529d9bc37c80761b6239e06fb1a1cf62956715 Sim Zhen Quan <<EMAIL>> 1753696765 +0800	commit: Reviewed
16529d9bc37c80761b6239e06fb1a1cf62956715 c17372e4e7249f5a8aadf0f52af7091c464bec88 Sim Zhen Quan <<EMAIL>> 1753696869 +0800	checkout: moving from SKLEARN-429-academy-report-position-ranking-by-grade-report to dev
c17372e4e7249f5a8aadf0f52af7091c464bec88 5d156b609952081f45fbfda9ddf20fad266388e7 Sim Zhen Quan <<EMAIL>> 1753696874 +0800	merge SKLEARN-429-academy-report-position-ranking-by-grade-report: Merge made by the 'ort' strategy.
5d156b609952081f45fbfda9ddf20fad266388e7 d6125a8e03c434d4b747bcbbf161a817942aff31 Sim Zhen Quan <<EMAIL>> 1753697134 +0800	commit: Deployed to dev
d6125a8e03c434d4b747bcbbf161a817942aff31 8306cb8be306271a3efdbbdeb24ac6f2c88cdd72 Sim Zhen Quan <<EMAIL>> 1753697447 +0800	checkout: moving from dev to SKLEARN-426-academy-report-all-passed-report
8306cb8be306271a3efdbbdeb24ac6f2c88cdd72 a8666600e0856b8427e35e1066820183d1d2fd0c Sim Zhen Quan <<EMAIL>> 1753697488 +0800	commit (merge): Merge remote-tracking branch 'origin/main' into SKLEARN-426-academy-report-all-passed-report
a8666600e0856b8427e35e1066820183d1d2fd0c 16fed9432a6c0f0c8664061feba9f665551253bd Sim Zhen Quan <<EMAIL>> 1753697573 +0800	checkout: moving from SKLEARN-426-academy-report-all-passed-report to staging/2025-07-25
16fed9432a6c0f0c8664061feba9f665551253bd 1ef63b4cb0ae9ff2c00139d485ba4ec1d60f73ef Sim Zhen Quan <<EMAIL>> 1753697583 +0800	checkout: moving from staging/2025-07-25 to SKLEARN-427-academy-report-best-grade-by-class-report
1ef63b4cb0ae9ff2c00139d485ba4ec1d60f73ef f3811a9e83ad666617cf04b69a1c34efc55aca48 Sim Zhen Quan <<EMAIL>> 1753697621 +0800	commit (merge): Merge remote-tracking branch 'origin/staging/2025-07-25' into SKLEARN-427-academy-report-best-grade-by-class-report
f3811a9e83ad666617cf04b69a1c34efc55aca48 c1e00b5725dc64eab806444701ee5aff0b2e16ce Sim Zhen Quan <<EMAIL>> 1753697684 +0800	checkout: moving from SKLEARN-427-academy-report-best-grade-by-class-report to SKLEARN-193-academy-report-best-grade-by-subject
c1e00b5725dc64eab806444701ee5aff0b2e16ce 7a5a664668c23ffbe5c30e013a143e503b2c4a1c Sim Zhen Quan <<EMAIL>> 1753697747 +0800	commit (merge): Merge branch 'staging/2025-07-25' into SKLEARN-193-academy-report-best-grade-by-subject
7a5a664668c23ffbe5c30e013a143e503b2c4a1c d6125a8e03c434d4b747bcbbf161a817942aff31 Sim Zhen Quan <<EMAIL>> 1753697845 +0800	checkout: moving from SKLEARN-193-academy-report-best-grade-by-subject to dev
d6125a8e03c434d4b747bcbbf161a817942aff31 285b90d7a56086093d20ecafae42beabccc85fec Sim Zhen Quan <<EMAIL>> 1753698068 +0800	checkout: moving from dev to SKLEARN-490-add-translatable-to-title-in-net-average-report
285b90d7a56086093d20ecafae42beabccc85fec a9be8d7540169250c7ae25f228b9710d594502f2 Sim Zhen Quan <<EMAIL>> 1753698143 +0800	commit (merge): Merge branch 'staging/2025-07-25' into SKLEARN-490-add-translatable-to-title-in-net-average-report
a9be8d7540169250c7ae25f228b9710d594502f2 16fed9432a6c0f0c8664061feba9f665551253bd Sim Zhen Quan <<EMAIL>> 1753698451 +0800	checkout: moving from SKLEARN-490-add-translatable-to-title-in-net-average-report to staging/2025-07-25
16fed9432a6c0f0c8664061feba9f665551253bd 7b90dea2918ea678cb884de8f90c8d5f80c58391 Sim Zhen Quan <<EMAIL>> 1753698457 +0800	pull: Fast-forward
7b90dea2918ea678cb884de8f90c8d5f80c58391 a85405b66c6010d83ce58f07c549f4fe0c8e3137 Sim Zhen Quan <<EMAIL>> 1753716716 +0800	checkout: moving from staging/2025-07-25 to main
a85405b66c6010d83ce58f07c549f4fe0c8e3137 dedf895cef26652eabd94f057ce8ba30cf9e26ee Sim Zhen Quan <<EMAIL>> 1753716722 +0800	pull: Fast-forward
dedf895cef26652eabd94f057ce8ba30cf9e26ee 86f813739d0352d026e54d9582c937e7cfec4d2b Sim Zhen Quan <<EMAIL>> 1753718011 +0800	commit: Deployed to prd
86f813739d0352d026e54d9582c937e7cfec4d2b 8b0d691a816236de406c572ce8163fea437800bf Sim Zhen Quan <<EMAIL>> 1753759696 +0800	checkout: moving from main to SKLEARN-497-reopen-posted-exam-result-by-exam-and-grade
8b0d691a816236de406c572ce8163fea437800bf 9210e3374dc0bafeb7b2ad3ae40882858edbc3bc Sim Zhen Quan <<EMAIL>> 1753759703 +0800	pull: Fast-forward
9210e3374dc0bafeb7b2ad3ae40882858edbc3bc 02b277c17c8414197b779429593ee85228705d9a Sim Zhen Quan <<EMAIL>> 1753759704 +0800	merge origin/main: Merge made by the 'ort' strategy.
02b277c17c8414197b779429593ee85228705d9a d6125a8e03c434d4b747bcbbf161a817942aff31 Sim Zhen Quan <<EMAIL>> 1753759817 +0800	checkout: moving from SKLEARN-497-reopen-posted-exam-result-by-exam-and-grade to dev
d6125a8e03c434d4b747bcbbf161a817942aff31 eb44455a951f5ac68262ce5961553497f10b83a2 Sim Zhen Quan <<EMAIL>> 1753759931 +0800	commit (merge): Merge branch 'SKLEARN-497-reopen-posted-exam-result-by-exam-and-grade' into dev
eb44455a951f5ac68262ce5961553497f10b83a2 eb44455a951f5ac68262ce5961553497f10b83a2 Sim Zhen Quan <<EMAIL>> 1753775205 +0800	reset: moving to HEAD
eb44455a951f5ac68262ce5961553497f10b83a2 02b277c17c8414197b779429593ee85228705d9a Sim Zhen Quan <<EMAIL>> 1753775207 +0800	checkout: moving from dev to SKLEARN-497-reopen-posted-exam-result-by-exam-and-grade
02b277c17c8414197b779429593ee85228705d9a 74ab7c658e369876d04326210678943390eb086d Sim Zhen Quan <<EMAIL>> 1753775261 +0800	commit: Set semester_setting_id to optional
74ab7c658e369876d04326210678943390eb086d 86f813739d0352d026e54d9582c937e7cfec4d2b Sim Zhen Quan <<EMAIL>> 1753775281 +0800	checkout: moving from SKLEARN-497-reopen-posted-exam-result-by-exam-and-grade to main
86f813739d0352d026e54d9582c937e7cfec4d2b 86f813739d0352d026e54d9582c937e7cfec4d2b Sim Zhen Quan <<EMAIL>> 1753775471 +0800	checkout: moving from main to SKLEARN-593-only-display-available-teacher-on-substitute-teacher-request-page
86f813739d0352d026e54d9582c937e7cfec4d2b cfb862ec5c88c41e2d40c465ad4c4b4c8ee18b68 Sim Zhen Quan <<EMAIL>> 1753782262 +0800	commit: Add teaching periods retrieval for employees in EmployeeService
cfb862ec5c88c41e2d40c465ad4c4b4c8ee18b68 eb44455a951f5ac68262ce5961553497f10b83a2 Sim Zhen Quan <<EMAIL>> 1753782338 +0800	checkout: moving from SKLEARN-593-only-display-available-teacher-on-substitute-teacher-request-page to dev
eb44455a951f5ac68262ce5961553497f10b83a2 973c226e6f43621411f5078d2a9b0b3713f61dad Sim Zhen Quan <<EMAIL>> 1753782346 +0800	merge SKLEARN-497-reopen-posted-exam-result-by-exam-and-grade: Merge made by the 'ort' strategy.
973c226e6f43621411f5078d2a9b0b3713f61dad cfb862ec5c88c41e2d40c465ad4c4b4c8ee18b68 Sim Zhen Quan <<EMAIL>> 1753782459 +0800	checkout: moving from dev to SKLEARN-593-only-display-available-teacher-on-substitute-teacher-request-page
cfb862ec5c88c41e2d40c465ad4c4b4c8ee18b68 5b0ec4bb10cd4793bdc936321b85c36439e6dc8e Sim Zhen Quan <<EMAIL>> 1753782498 +0800	commit: Remove extra semicolon
5b0ec4bb10cd4793bdc936321b85c36439e6dc8e 973c226e6f43621411f5078d2a9b0b3713f61dad Sim Zhen Quan <<EMAIL>> 1753782507 +0800	checkout: moving from SKLEARN-593-only-display-available-teacher-on-substitute-teacher-request-page to dev
973c226e6f43621411f5078d2a9b0b3713f61dad e947950d03c1df7a39cdb3c9d16dd4904d8e3e5e Sim Zhen Quan <<EMAIL>> 1753782509 +0800	merge SKLEARN-593-only-display-available-teacher-on-substitute-teacher-request-page: Merge made by the 'ort' strategy.
e947950d03c1df7a39cdb3c9d16dd4904d8e3e5e f2b3d53a80f4ca8cb000042b76737fa48fa78faa Sim Zhen Quan <<EMAIL>> 1753804342 +0800	commit: Deployed to dev
f2b3d53a80f4ca8cb000042b76737fa48fa78faa 86f813739d0352d026e54d9582c937e7cfec4d2b Sim Zhen Quan <<EMAIL>> 1753804348 +0800	checkout: moving from dev to main
86f813739d0352d026e54d9582c937e7cfec4d2b 7fd38a2abae172ef523296f052ca516e450e1a82 Sim Zhen Quan <<EMAIL>> 1753804354 +0800	pull: Fast-forward
7fd38a2abae172ef523296f052ca516e450e1a82 d4332065c1809ded6055356b88e5f3c25f70296f Sim Zhen Quan <<EMAIL>> 1753805700 +0800	commit: Deployed to prd
d4332065c1809ded6055356b88e5f3c25f70296f 63f41317c45404e86d46deabfc2531bff9b399d1 Sim Zhen Quan <<EMAIL>> 1753842182 +0800	checkout: moving from main to feature/report-reward-punishment-by-day
63f41317c45404e86d46deabfc2531bff9b399d1 b9084050296dee4f59be3d3e0b8fb5efc4988470 Sim Zhen Quan <<EMAIL>> 1753842197 +0800	merge main: Merge made by the 'ort' strategy.
b9084050296dee4f59be3d3e0b8fb5efc4988470 f2b3d53a80f4ca8cb000042b76737fa48fa78faa Sim Zhen Quan <<EMAIL>> 1753842622 +0800	checkout: moving from feature/report-reward-punishment-by-day to dev
f2b3d53a80f4ca8cb000042b76737fa48fa78faa 6f16293fcedc064c38ae6b3d750583ab4fa16abb Sim Zhen Quan <<EMAIL>> 1753842678 +0800	merge origin/feature/hostel-saving-collection-reports: Merge made by the 'ort' strategy.
6f16293fcedc064c38ae6b3d750583ab4fa16abb b9084050296dee4f59be3d3e0b8fb5efc4988470 Sim Zhen Quan <<EMAIL>> 1753843127 +0800	checkout: moving from dev to feature/report-reward-punishment-by-day
b9084050296dee4f59be3d3e0b8fb5efc4988470 078ee3e2febeadfdd47c6afc70a2325a4bc42a9f Sim Zhen Quan <<EMAIL>> 1753853040 +0800	commit: Review WIP
078ee3e2febeadfdd47c6afc70a2325a4bc42a9f 60a47ded9e2d9e0487dbc56a67157aed04ad87a2 Sim Zhen Quan <<EMAIL>> 1753853045 +0800	checkout: moving from feature/report-reward-punishment-by-day to SKLEARN-460-academy-report-student-exemption-report
60a47ded9e2d9e0487dbc56a67157aed04ad87a2 16529d9bc37c80761b6239e06fb1a1cf62956715 Sim Zhen Quan <<EMAIL>> 1753853147 +0800	checkout: moving from SKLEARN-460-academy-report-student-exemption-report to SKLEARN-429-academy-report-position-ranking-by-grade-report
16529d9bc37c80761b6239e06fb1a1cf62956715 92d600b0c8c4559a2fca8d62b95ff4a1f3738eee Sim Zhen Quan <<EMAIL>> 1753853151 +0800	merge origin/main: Merge made by the 'ort' strategy.
92d600b0c8c4559a2fca8d62b95ff4a1f3738eee 60a47ded9e2d9e0487dbc56a67157aed04ad87a2 Sim Zhen Quan <<EMAIL>> 1753853172 +0800	checkout: moving from SKLEARN-429-academy-report-position-ranking-by-grade-report to SKLEARN-460-academy-report-student-exemption-report
60a47ded9e2d9e0487dbc56a67157aed04ad87a2 0e898de8a7755b1e4debde1b974a4c020ea4cc5c Sim Zhen Quan <<EMAIL>> 1753853266 +0800	commit (merge): Merge
0e898de8a7755b1e4debde1b974a4c020ea4cc5c 964f573f1fbaa0d45689445bf3eccc2cba07ad05 Sim Zhen Quan <<EMAIL>> 1753854367 +0800	commit: Reviewed
964f573f1fbaa0d45689445bf3eccc2cba07ad05 6f16293fcedc064c38ae6b3d750583ab4fa16abb Sim Zhen Quan <<EMAIL>> 1753854398 +0800	checkout: moving from SKLEARN-460-academy-report-student-exemption-report to dev
6f16293fcedc064c38ae6b3d750583ab4fa16abb f7e570aec0a86357b5cbb69914f1bdd29eb303c0 Sim Zhen Quan <<EMAIL>> 1753854434 +0800	commit (merge): Merge code
f7e570aec0a86357b5cbb69914f1bdd29eb303c0 c83cb50366867ef569f2abdadc26afbd78dc66fe Sim Zhen Quan <<EMAIL>> 1753854481 +0800	commit: Updated CHANGELOG.dev.md
c83cb50366867ef569f2abdadc26afbd78dc66fe 1bd749f0c4287ec5d81ee399913eedbf7854feca Sim Zhen Quan <<EMAIL>> 1753854855 +0800	checkout: moving from dev to SKLEARN-501-student-comprehensive-quality-assessment-report
1bd749f0c4287ec5d81ee399913eedbf7854feca 66a627e43c9dd05b1913716f0804c9a9e75e9703 Sim Zhen Quan <<EMAIL>> 1753854944 +0800	commit (merge): Merge code
66a627e43c9dd05b1913716f0804c9a9e75e9703 446155ab00f5700481ec4de0b45282ac8cfadd2b Sim Zhen Quan <<EMAIL>> 1753857618 +0800	commit: Reviewed
446155ab00f5700481ec4de0b45282ac8cfadd2b c83cb50366867ef569f2abdadc26afbd78dc66fe Sim Zhen Quan <<EMAIL>> 1753857635 +0800	checkout: moving from SKLEARN-501-student-comprehensive-quality-assessment-report to dev
c83cb50366867ef569f2abdadc26afbd78dc66fe e477b2ebc5f131f495d74807180fa070f2a0fe60 Sim Zhen Quan <<EMAIL>> 1753857640 +0800	merge SKLEARN-501-student-comprehensive-quality-assessment-report: Merge made by the 'ort' strategy.
e477b2ebc5f131f495d74807180fa070f2a0fe60 4fab729dc5c298cb1f8af34422fed1681463a75e Sim Zhen Quan <<EMAIL>> 1753857789 +0800	commit: Deployed to dev
4fab729dc5c298cb1f8af34422fed1681463a75e c712018d19b12d9a4d564c68ec2f8340838e400a Sim Zhen Quan <<EMAIL>> 1753858332 +0800	checkout: moving from dev to feature/resend-when-extend-enrollment
c712018d19b12d9a4d564c68ec2f8340838e400a 6b028c0d96e47528f41d5417be879e5aae9aa315 Sim Zhen Quan <<EMAIL>> 1753858338 +0800	merge origin/main: Merge made by the 'ort' strategy.
6b028c0d96e47528f41d5417be879e5aae9aa315 fcf96e66e2b636a7ffcfc7e88c39ae864853471c Sim Zhen Quan <<EMAIL>> 1753862253 +0800	commit: Reviewed
fcf96e66e2b636a7ffcfc7e88c39ae864853471c 4fab729dc5c298cb1f8af34422fed1681463a75e Sim Zhen Quan <<EMAIL>> 1753865831 +0800	checkout: moving from feature/resend-when-extend-enrollment to dev
4fab729dc5c298cb1f8af34422fed1681463a75e c0aac4b3e21eea9b3d71ffd45127c6f82b474a49 Sim Zhen Quan <<EMAIL>> 1753865866 +0800	commit (merge): Merge branch 'feature/resend-when-extend-enrollment' into dev
c0aac4b3e21eea9b3d71ffd45127c6f82b474a49 12920eb4edea6f32540c3a36415108213f4b3b26 Sim Zhen Quan <<EMAIL>> 1753866045 +0800	commit: Updated CHANGELOG.dev.md
12920eb4edea6f32540c3a36415108213f4b3b26 d4332065c1809ded6055356b88e5f3c25f70296f Sim Zhen Quan <<EMAIL>> 1753933420 +0800	checkout: moving from dev to main
d4332065c1809ded6055356b88e5f3c25f70296f d4332065c1809ded6055356b88e5f3c25f70296f Sim Zhen Quan <<EMAIL>> 1753934498 +0800	checkout: moving from main to SKLEARN-597-only-display-record-taken-by-teacher
d4332065c1809ded6055356b88e5f3c25f70296f d4332065c1809ded6055356b88e5f3c25f70296f Sim Zhen Quan <<EMAIL>> 1753936115 +0800	checkout: moving from SKLEARN-597-only-display-record-taken-by-teacher to main
d4332065c1809ded6055356b88e5f3c25f70296f c9c494152fc22b6807176f27a1b7b78160964ac3 Sim Zhen Quan <<EMAIL>> 1753938609 +0800	commit: Updated script
c9c494152fc22b6807176f27a1b7b78160964ac3 8bc703b2ddd6b4398ddbe533a1b9f02946afd51f Sim Zhen Quan <<EMAIL>> 1753948126 +0800	pull: Fast-forward
8bc703b2ddd6b4398ddbe533a1b9f02946afd51f d8f2fbd7119402a0b3cc6dd263923a621e1fa214 Sim Zhen Quan <<EMAIL>> 1753956775 +0800	commit: Updated script
d8f2fbd7119402a0b3cc6dd263923a621e1fa214 d4332065c1809ded6055356b88e5f3c25f70296f Sim Zhen Quan <<EMAIL>> 1753956868 +0800	checkout: moving from main to SKLEARN-597-only-display-record-taken-by-teacher
d4332065c1809ded6055356b88e5f3c25f70296f 9257732e616c9823ea315da3b85fb282f653e12b Sim Zhen Quan <<EMAIL>> 1753958111 +0800	commit: Enhance attendance report to only show records that teacher took attendance of
9257732e616c9823ea315da3b85fb282f653e12b 12920eb4edea6f32540c3a36415108213f4b3b26 Sim Zhen Quan <<EMAIL>> 1753958217 +0800	checkout: moving from SKLEARN-597-only-display-record-taken-by-teacher to dev
12920eb4edea6f32540c3a36415108213f4b3b26 338f9b9dd44387802874cc383abfac179f3b4b07 Sim Zhen Quan <<EMAIL>> 1753958217 +0800	merge SKLEARN-597-only-display-record-taken-by-teacher: Merge made by the 'ort' strategy.
338f9b9dd44387802874cc383abfac179f3b4b07 78f43d734ced9ac51ef9ed6e634b76219794a1d5 Sim Zhen Quan <<EMAIL>> 1753958268 +0800	commit: Updated CHANGELOG.dev.md
78f43d734ced9ac51ef9ed6e634b76219794a1d5 d8f2fbd7119402a0b3cc6dd263923a621e1fa214 Sim Zhen Quan <<EMAIL>> 1753958448 +0800	checkout: moving from dev to main
d8f2fbd7119402a0b3cc6dd263923a621e1fa214 d8f2fbd7119402a0b3cc6dd263923a621e1fa214 Sim Zhen Quan <<EMAIL>> 1753958784 +0800	checkout: moving from main to SKLEARN-583-library-status-fixes
d8f2fbd7119402a0b3cc6dd263923a621e1fa214 e519e5e111ad73bfba7bef38f36bf364958092f5 Sim Zhen Quan <<EMAIL>> 1753960352 +0800	commit: Add automatic status updates based on book condition in BookService
e519e5e111ad73bfba7bef38f36bf364958092f5 27f5416868750f28348a4d788cfa8a90ef466ca3 Sim Zhen Quan <<EMAIL>> 1753960552 +0800	commit: Bug fix
27f5416868750f28348a4d788cfa8a90ef466ca3 e5a5a77c22ed0a781a4e596823c01b15847a8879 Sim Zhen Quan <<EMAIL>> 1753960610 +0800	commit: Bug fix
e5a5a77c22ed0a781a4e596823c01b15847a8879 78f43d734ced9ac51ef9ed6e634b76219794a1d5 Sim Zhen Quan <<EMAIL>> 1753960621 +0800	checkout: moving from SKLEARN-583-library-status-fixes to dev
78f43d734ced9ac51ef9ed6e634b76219794a1d5 635d3e1539318f979ac6f7f52abc90799ae7999a Sim Zhen Quan <<EMAIL>> 1753960621 +0800	merge SKLEARN-583-library-status-fixes: Merge made by the 'ort' strategy.
635d3e1539318f979ac6f7f52abc90799ae7999a a477bf2c8cb57ba421b9271c58250df1a8851b04 Sim Zhen Quan <<EMAIL>> 1753960907 +0800	commit: Deploy to dev
a477bf2c8cb57ba421b9271c58250df1a8851b04 6c70ad52d473ac8f3975491ac834913ba237be45 Sim Zhen Quan <<EMAIL>> 1753960919 +0800	checkout: moving from dev to feature/hostel-saving-collection-reports
6c70ad52d473ac8f3975491ac834913ba237be45 a477bf2c8cb57ba421b9271c58250df1a8851b04 Sim Zhen Quan <<EMAIL>> 1753961609 +0800	checkout: moving from feature/hostel-saving-collection-reports to dev
a477bf2c8cb57ba421b9271c58250df1a8851b04 5b6cca14087e2848e9b451deb2ec95ac94ef1281 Sim Zhen Quan <<EMAIL>> 1753961614 +0800	merge feature/hostel-saving-collection-reports: Merge made by the 'ort' strategy.
5b6cca14087e2848e9b451deb2ec95ac94ef1281 8c76e2de68e862912d4c60c34aaa447d49ea8c7e Sim Zhen Quan <<EMAIL>> 1753973800 +0800	commit: Deploy to dev
8c76e2de68e862912d4c60c34aaa447d49ea8c7e 8c76e2de68e862912d4c60c34aaa447d49ea8c7e Sim Zhen Quan <<EMAIL>> 1753973924 +0800	checkout: moving from dev to staging/2025-07-31
8c76e2de68e862912d4c60c34aaa447d49ea8c7e 28ab4600b8d3d6330762c0847f18c16fda9bdf0e Sim Zhen Quan <<EMAIL>> 1753974196 +0800	checkout: moving from staging/2025-07-31 to SKLEARN-495-exam-mark-entry-enhancements
28ab4600b8d3d6330762c0847f18c16fda9bdf0e 6312e6693b0267072cc0546d31e7328151127168 Sim Zhen Quan <<EMAIL>> 1753974202 +0800	pull: Fast-forward
6312e6693b0267072cc0546d31e7328151127168 8c76e2de68e862912d4c60c34aaa447d49ea8c7e Sim Zhen Quan <<EMAIL>> 1753974203 +0800	merge staging/2025-07-31: Fast-forward
8c76e2de68e862912d4c60c34aaa447d49ea8c7e d8f2fbd7119402a0b3cc6dd263923a621e1fa214 Sim Zhen Quan <<EMAIL>> 1753974989 +0800	checkout: moving from SKLEARN-495-exam-mark-entry-enhancements to main
d8f2fbd7119402a0b3cc6dd263923a621e1fa214 d8f2fbd7119402a0b3cc6dd263923a621e1fa214 Sim Zhen Quan <<EMAIL>> 1753975000 +0800	checkout: moving from main to SKLEARN-495-recover
d8f2fbd7119402a0b3cc6dd263923a621e1fa214 466145db90968b88dcaed1165e6c02178e98efc5 Sim Zhen Quan <<EMAIL>> 1753975019 +0800	cherry-pick: Added employee name to getEligibleStudentAndScores, unit test WIP
466145db90968b88dcaed1165e6c02178e98efc5 57b842755bc0682183243c2ef9da3fea12f79be5 Sim Zhen Quan <<EMAIL>> 1753975025 +0800	cherry-pick: unit test complete
57b842755bc0682183243c2ef9da3fea12f79be5 8f34285de9c335cb76b59f03d45c62528c50384a Sim Zhen Quan <<EMAIL>> 1753975045 +0800	cherry-pick: Added data entry time to response
8f34285de9c335cb76b59f03d45c62528c50384a d8f2fbd7119402a0b3cc6dd263923a621e1fa214 Sim Zhen Quan <<EMAIL>> 1753975415 +0800	checkout: moving from SKLEARN-495-recover to main
d8f2fbd7119402a0b3cc6dd263923a621e1fa214 d8f2fbd7119402a0b3cc6dd263923a621e1fa214 Sim Zhen Quan <<EMAIL>> 1753975422 +0800	checkout: moving from main to staging/2025-07-31
d8f2fbd7119402a0b3cc6dd263923a621e1fa214 d8f2fbd7119402a0b3cc6dd263923a621e1fa214 Sim Zhen Quan <<EMAIL>> 1753975463 +0800	checkout: moving from staging/2025-07-31 to main
d8f2fbd7119402a0b3cc6dd263923a621e1fa214 d8f2fbd7119402a0b3cc6dd263923a621e1fa214 Sim Zhen Quan <<EMAIL>> 1753975592 +0800	reset: moving to HEAD
d8f2fbd7119402a0b3cc6dd263923a621e1fa214 74ab7c658e369876d04326210678943390eb086d Sim Zhen Quan <<EMAIL>> 1753975593 +0800	checkout: moving from main to SKLEARN-497-reopen-posted-exam-result-by-exam-and-grade
74ab7c658e369876d04326210678943390eb086d 47c8eb968a15db279803f379717bc7c2b6a9ec6d Sim Zhen Quan <<EMAIL>> 1753975605 +0800	merge staging/2025-07-31: Merge made by the 'ort' strategy.
47c8eb968a15db279803f379717bc7c2b6a9ec6d c9abd2a66ed3b4d15c74961bc730fed0709e5172 Sim Zhen Quan <<EMAIL>> 1753975637 +0800	commit (merge): Merge remote-tracking branch 'origin/staging/2025-07-31' into SKLEARN-497-reopen-posted-exam-result-by-exam-and-grade
c9abd2a66ed3b4d15c74961bc730fed0709e5172 d8f2fbd7119402a0b3cc6dd263923a621e1fa214 Sim Zhen Quan <<EMAIL>> 1753975660 +0800	checkout: moving from SKLEARN-497-reopen-posted-exam-result-by-exam-and-grade to main
d8f2fbd7119402a0b3cc6dd263923a621e1fa214 25b342c0672f019a2766d152299ae40321453115 Sim Zhen Quan <<EMAIL>> 1753975955 +0800	pull: Fast-forward
25b342c0672f019a2766d152299ae40321453115 7a8c83bb3f823a00cb7851cade99f0eab9528a71 Sim Zhen Quan <<EMAIL>> 1753976951 +0800	commit: Deployed to prd
7a8c83bb3f823a00cb7851cade99f0eab9528a71 e5a5a77c22ed0a781a4e596823c01b15847a8879 Sim Zhen Quan <<EMAIL>> 1753976962 +0800	checkout: moving from main to SKLEARN-583-library-status-fixes
e5a5a77c22ed0a781a4e596823c01b15847a8879 f0eaa503472ee94e27904a4307c382eab50926c9 Sim Zhen Quan <<EMAIL>> 1753977344 +0800	commit: Bug fix
f0eaa503472ee94e27904a4307c382eab50926c9 8c76e2de68e862912d4c60c34aaa447d49ea8c7e Sim Zhen Quan <<EMAIL>> 1753977594 +0800	checkout: moving from SKLEARN-583-library-status-fixes to dev
8c76e2de68e862912d4c60c34aaa447d49ea8c7e 4a5a8636ceeb64ced9b8ee7c6e6c31e74e051bee Sim Zhen Quan <<EMAIL>> 1753977594 +0800	merge SKLEARN-583-library-status-fixes: Merge made by the 'ort' strategy.
4a5a8636ceeb64ced9b8ee7c6e6c31e74e051bee 93bf3035915633ce13deeb3b76133ef3f2c746aa Sim Zhen Quan <<EMAIL>> 1754030531 +0800	checkout: moving from dev to issue-124-employee-resigned-with-hostel-bed
93bf3035915633ce13deeb3b76133ef3f2c746aa ef9c54f56e817080f30d9d62d77cea8d1be6012b Sim Zhen Quan <<EMAIL>> 1754030543 +0800	pull: Fast-forward
ef9c54f56e817080f30d9d62d77cea8d1be6012b 3a64502ffaa993c3cc329297cb88d586e8ff2dca Sim Zhen Quan <<EMAIL>> 1754030662 +0800	commit (merge): Merge remote-tracking branch 'origin/main' into issue-124-employee-resigned-with-hostel-bed
3a64502ffaa993c3cc329297cb88d586e8ff2dca e8cce75111265f8ee16c4ceb105cdd229f4edb85 Sim Zhen Quan <<EMAIL>> 1754031161 +0800	commit: Bug fix
e8cce75111265f8ee16c4ceb105cdd229f4edb85 e8cce75111265f8ee16c4ceb105cdd229f4edb85 Sim Zhen Quan <<EMAIL>> 1754031188 +0800	checkout: moving from issue-124-employee-resigned-with-hostel-bed to issue-124-employee-resigned-with-hostel-bed
e8cce75111265f8ee16c4ceb105cdd229f4edb85 4a5a8636ceeb64ced9b8ee7c6e6c31e74e051bee Sim Zhen Quan <<EMAIL>> 1754031211 +0800	checkout: moving from issue-124-employee-resigned-with-hostel-bed to dev
4a5a8636ceeb64ced9b8ee7c6e6c31e74e051bee eefbdd38c371b5bc4680f0e7d98608e5172e047d Sim Zhen Quan <<EMAIL>> 1754031222 +0800	merge issue-124-employee-resigned-with-hostel-bed: Merge made by the 'ort' strategy.
eefbdd38c371b5bc4680f0e7d98608e5172e047d 995d3624682d6198355eb57f45c49dc46bae2e1e Sim Zhen Quan <<EMAIL>> 1754031303 +0800	commit: Deployed to dev
995d3624682d6198355eb57f45c49dc46bae2e1e f0eaa503472ee94e27904a4307c382eab50926c9 Sim Zhen Quan <<EMAIL>> 1754031607 +0800	checkout: moving from dev to SKLEARN-583-library-status-fixes
f0eaa503472ee94e27904a4307c382eab50926c9 91bb4223d28aba9147f61ec937493f67d5bbf9fe Sim Zhen Quan <<EMAIL>> 1754031869 +0800	commit: Bug fix
91bb4223d28aba9147f61ec937493f67d5bbf9fe 995d3624682d6198355eb57f45c49dc46bae2e1e Sim Zhen Quan <<EMAIL>> 1754031890 +0800	checkout: moving from SKLEARN-583-library-status-fixes to dev
995d3624682d6198355eb57f45c49dc46bae2e1e 4bd368ad3c14f004336ecf46b22e9b0a5a8dd9af Sim Zhen Quan <<EMAIL>> 1754031892 +0800	merge SKLEARN-583-library-status-fixes: Merge made by the 'ort' strategy.
4bd368ad3c14f004336ecf46b22e9b0a5a8dd9af 2677e8cc80a6e603b06d2313e8140263b77c8e82 Sim Zhen Quan <<EMAIL>> 1754032320 +0800	checkout: moving from dev to SKLEARN-555-student-absent-report-for-society
2677e8cc80a6e603b06d2313e8140263b77c8e82 1a00d9b7d3415885bbcfc9e7cb93ab71a68ff1e3 Sim Zhen Quan <<EMAIL>> 1754032393 +0800	commit (merge): Merge remote-tracking branch 'origin/main' into SKLEARN-555-student-absent-report-for-society
1a00d9b7d3415885bbcfc9e7cb93ab71a68ff1e3 499fe375ee081800f0f8f4249f37104ffc85e54a Sim Zhen Quan <<EMAIL>> 1754034164 +0800	commit: Reviewed
499fe375ee081800f0f8f4249f37104ffc85e54a 4bd368ad3c14f004336ecf46b22e9b0a5a8dd9af Sim Zhen Quan <<EMAIL>> 1754034175 +0800	checkout: moving from SKLEARN-555-student-absent-report-for-society to dev
4bd368ad3c14f004336ecf46b22e9b0a5a8dd9af 78588eda313743979a2506bbb98f1faff4ae9007 Sim Zhen Quan <<EMAIL>> 1754034179 +0800	merge SKLEARN-555-student-absent-report-for-society: Merge made by the 'ort' strategy.
78588eda313743979a2506bbb98f1faff4ae9007 78588eda313743979a2506bbb98f1faff4ae9007 Sim Zhen Quan <<EMAIL>> 1754036819 +0800	reset: moving to HEAD
78588eda313743979a2506bbb98f1faff4ae9007 91bb4223d28aba9147f61ec937493f67d5bbf9fe Sim Zhen Quan <<EMAIL>> 1754036833 +0800	checkout: moving from dev to SKLEARN-583-library-status-fixes
